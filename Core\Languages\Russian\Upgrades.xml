﻿<?xml version="1.0" encoding="utf-8"?>
<language>

	<!-- Buildings -->
	<entry name="AdeptusMechanicus/Aircraft" value="&lt;string name='Buildings/AdeptusMechanicus/Aircraft'/&gt;"/>
	<entry name="AdeptusMechanicus/AircraftDescription" value="&lt;string name='Buildings/AdeptusMechanicus/AircraftDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/AircraftFlavor" value="&lt;string name='Buildings/AdeptusMechanicus/AircraftFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/Construction" value="&lt;string name='Buildings/AdeptusMechanicus/Construction'/&gt;"/>
	<entry name="AdeptusMechanicus/ConstructionDescription" value="&lt;string name='Buildings/AdeptusMechanicus/ConstructionDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/ConstructionFlavor" value="&lt;string name='Buildings/AdeptusMechanicus/ConstructionFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/Heroes" value="&lt;string name='Buildings/AdeptusMechanicus/Heroes'/&gt;"/>
	<entry name="AdeptusMechanicus/HeroesDescription" value="&lt;string name='Buildings/AdeptusMechanicus/HeroesDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/HeroesFlavor" value="&lt;string name='Buildings/AdeptusMechanicus/HeroesFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/Housing" value="&lt;string name='Buildings/AdeptusMechanicus/Housing'/&gt;"/>
	<entry name="AdeptusMechanicus/HousingDescription" value="&lt;string name='Buildings/AdeptusMechanicus/HousingDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/HousingFlavor" value="&lt;string name='Buildings/AdeptusMechanicus/HousingFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/Loyalty" value="&lt;string name='Buildings/AdeptusMechanicus/Loyalty'/&gt;"/>
	<entry name="AdeptusMechanicus/LoyaltyDescription" value="&lt;string name='Buildings/AdeptusMechanicus/LoyaltyDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/LoyaltyFlavor" value="&lt;string name='Buildings/AdeptusMechanicus/LoyaltyFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/Vehicles" value="&lt;string name='Buildings/AdeptusMechanicus/Vehicles'/&gt;"/>
	<entry name="AdeptusMechanicus/VehiclesDescription" value="&lt;string name='Buildings/AdeptusMechanicus/VehiclesDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/VehiclesFlavor" value="&lt;string name='Buildings/AdeptusMechanicus/VehiclesFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Aircraft" value="&lt;string name='Buildings/AstraMilitarum/Aircraft'/&gt;"/>
	<entry name="AstraMilitarum/AircraftDescription" value="&lt;string name='Buildings/AstraMilitarum/AircraftDescription'/&gt;"/>
	<entry name="AstraMilitarum/AircraftFlavor" value="&lt;string name='Buildings/AstraMilitarum/AircraftFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Construction" value="&lt;string name='Buildings/AstraMilitarum/Construction'/&gt;"/>
	<entry name="AstraMilitarum/ConstructionDescription" value="&lt;string name='Buildings/AstraMilitarum/ConstructionDescription'/&gt;"/>
	<entry name="AstraMilitarum/ConstructionFlavor" value="&lt;string name='Buildings/AstraMilitarum/ConstructionFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Heroes" value="&lt;string name='Buildings/AstraMilitarum/Heroes'/&gt;"/>
	<entry name="AstraMilitarum/HeroesDescription" value="&lt;string name='Buildings/AstraMilitarum/HeroesDescription'/&gt;"/>
	<entry name="AstraMilitarum/HeroesFlavor" value="&lt;string name='Buildings/AstraMilitarum/HeroesFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Housing" value="&lt;string name='Buildings/AstraMilitarum/Housing'/&gt;"/>
	<entry name="AstraMilitarum/HousingDescription" value="&lt;string name='Buildings/AstraMilitarum/HousingDescription'/&gt;"/>
	<entry name="AstraMilitarum/HousingFlavor" value="&lt;string name='Buildings/AstraMilitarum/HousingFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Loyalty" value="&lt;string name='Buildings/AstraMilitarum/Loyalty'/&gt;"/>
	<entry name="AstraMilitarum/LoyaltyDescription" value="&lt;string name='Buildings/AstraMilitarum/LoyaltyDescription'/&gt;"/>
	<entry name="AstraMilitarum/LoyaltyFlavor" value="&lt;string name='Buildings/AstraMilitarum/LoyaltyFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Psykers" value="&lt;string name='Buildings/AstraMilitarum/Psykers'/&gt;"/>
	<entry name="AstraMilitarum/PsykersDescription" value="&lt;string name='Buildings/AstraMilitarum/PsykersDescription'/&gt;"/>
	<entry name="AstraMilitarum/PsykersFlavor" value="&lt;string name='Buildings/AstraMilitarum/PsykersFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Upgrades" value="&lt;string name='Buildings/AstraMilitarum/Upgrades'/&gt;"/>
	<entry name="AstraMilitarum/UpgradesDescription" value="&lt;string name='Buildings/AstraMilitarum/UpgradesDescription'/&gt;"/>
	<entry name="AstraMilitarum/UpgradesFlavor" value="&lt;string name='Buildings/AstraMilitarum/UpgradesFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Vehicles" value="&lt;string name='Buildings/AstraMilitarum/Vehicles'/&gt;"/>
	<entry name="AstraMilitarum/VehiclesDescription" value="&lt;string name='Buildings/AstraMilitarum/VehiclesDescription'/&gt;"/>
	<entry name="AstraMilitarum/VehiclesFlavor" value="&lt;string name='Buildings/AstraMilitarum/VehiclesFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Aircraft" value="&lt;string name='Buildings/ChaosSpaceMarines/Aircraft'/&gt;"/>
	<entry name="ChaosSpaceMarines/AircraftDescription" value="&lt;string name='Buildings/ChaosSpaceMarines/AircraftDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/AircraftFlavor" value="&lt;string name='Buildings/ChaosSpaceMarines/AircraftFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Construction" value="&lt;string name='Buildings/ChaosSpaceMarines/Construction'/&gt;"/>
	<entry name="ChaosSpaceMarines/ConstructionDescription" value="&lt;string name='Buildings/ChaosSpaceMarines/ConstructionDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/ConstructionFlavor" value="&lt;string name='Buildings/ChaosSpaceMarines/ConstructionFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Heroes" value="&lt;string name='Buildings/ChaosSpaceMarines/Heroes'/&gt;"/>
	<entry name="ChaosSpaceMarines/HeroesDescription" value="&lt;string name='Buildings/ChaosSpaceMarines/HeroesDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/HeroesFlavor" value="&lt;string name='Buildings/ChaosSpaceMarines/HeroesFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Housing" value="&lt;string name='Buildings/ChaosSpaceMarines/Housing'/&gt;"/>
	<entry name="ChaosSpaceMarines/HousingDescription" value="&lt;string name='Buildings/ChaosSpaceMarines/HousingDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/HousingFlavor" value="&lt;string name='Buildings/ChaosSpaceMarines/HousingFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Infantry" value="&lt;string name='Buildings/ChaosSpaceMarines/Infantry'/&gt;"/>
	<entry name="ChaosSpaceMarines/InfantryDescription" value="&lt;string name='Buildings/ChaosSpaceMarines/InfantryDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/InfantryFlavor" value="&lt;string name='Buildings/ChaosSpaceMarines/InfantryFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Loyalty" value="&lt;string name='Buildings/ChaosSpaceMarines/Loyalty'/&gt;"/>
	<entry name="ChaosSpaceMarines/LoyaltyDescription" value="&lt;string name='Buildings/ChaosSpaceMarines/LoyaltyDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/LoyaltyFlavor" value="&lt;string name='Buildings/ChaosSpaceMarines/LoyaltyFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Vehicles" value="&lt;string name='Buildings/ChaosSpaceMarines/Vehicles'/&gt;"/>
	<entry name="ChaosSpaceMarines/VehiclesDescription" value="&lt;string name='Buildings/ChaosSpaceMarines/VehiclesDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/VehiclesFlavor" value="&lt;string name='Buildings/ChaosSpaceMarines/VehiclesFlavor'/&gt;"/>
	<entry name="Drukhari/Aircraft" value="&lt;string name='Buildings/Drukhari/Aircraft'/&gt;"/>
	<entry name="Drukhari/AircraftDescription" value="&lt;string name='Buildings/Drukhari/AircraftDescription'/&gt;"/>
	<entry name="Drukhari/AircraftFlavor" value="&lt;string name='Buildings/Drukhari/AircraftFlavor'/&gt;"/>
	<entry name="Drukhari/Construction" value="&lt;string name='Buildings/Drukhari/Construction'/&gt;"/>
	<entry name="Drukhari/ConstructionDescription" value="&lt;string name='Buildings/Drukhari/ConstructionDescription'/&gt;"/>
	<entry name="Drukhari/ConstructionFlavor" value="&lt;string name='Buildings/Drukhari/ConstructionFlavor'/&gt;"/>
	<entry name="Drukhari/Heroes" value="&lt;string name='Buildings/Drukhari/Heroes'/&gt;"/>
	<entry name="Drukhari/HeroesDescription" value="&lt;string name='Buildings/Drukhari/HeroesDescription'/&gt;"/>
	<entry name="Drukhari/HeroesFlavor" value="&lt;string name='Buildings/Drukhari/HeroesFlavor'/&gt;"/>
	<entry name="Drukhari/Housing" value="&lt;string name='Buildings/Drukhari/Housing'/&gt;"/>
	<entry name="Drukhari/HousingDescription" value="&lt;string name='Buildings/Drukhari/HousingDescription'/&gt;"/>
	<entry name="Drukhari/HousingFlavor" value="&lt;string name='Buildings/Drukhari/HousingFlavor'/&gt;"/>
	<entry name="Drukhari/Loyalty" value="&lt;string name='Buildings/Drukhari/Loyalty'/&gt;"/>
	<entry name="Drukhari/LoyaltyDescription" value="&lt;string name='Buildings/Drukhari/LoyaltyDescription'/&gt;"/>
	<entry name="Drukhari/LoyaltyFlavor" value="&lt;string name='Buildings/Drukhari/LoyaltyFlavor'/&gt;"/>
	<entry name="Drukhari/Vehicles" value="&lt;string name='Buildings/Drukhari/Vehicles'/&gt;"/>
	<entry name="Drukhari/VehiclesDescription" value="&lt;string name='Buildings/Drukhari/VehiclesDescription'/&gt;"/>
	<entry name="Drukhari/VehiclesFlavor" value="&lt;string name='Buildings/Drukhari/VehiclesFlavor'/&gt;"/>
	<entry name="Eldar/Aircraft" value="&lt;string name='Buildings/Eldar/Aircraft'/&gt;"/>
	<entry name="Eldar/AircraftDescription" value="&lt;string name='Buildings/Eldar/AircraftDescription'/&gt;"/>
	<entry name="Eldar/AircraftFlavor" value="&lt;string name='Buildings/Eldar/AircraftFlavor'/&gt;"/>
	<entry name="Eldar/Construction" value="&lt;string name='Buildings/Eldar/Construction'/&gt;"/>
	<entry name="Eldar/ConstructionDescription" value="&lt;string name='Buildings/Eldar/ConstructionDescription'/&gt;"/>
	<entry name="Eldar/ConstructionFlavor" value="&lt;string name='Buildings/Eldar/ConstructionFlavor'/&gt;"/>
	<entry name="Eldar/Heroes" value="&lt;string name='Buildings/Eldar/Heroes'/&gt;"/>
	<entry name="Eldar/HeroesDescription" value="&lt;string name='Buildings/Eldar/HeroesDescription'/&gt;"/>
	<entry name="Eldar/HeroesFlavor" value="&lt;string name='Buildings/Eldar/HeroesFlavor'/&gt;"/>
	<entry name="Eldar/Housing" value="&lt;string name='Buildings/Eldar/Housing'/&gt;"/>
	<entry name="Eldar/HousingDescription" value="&lt;string name='Buildings/Eldar/HousingDescription'/&gt;"/>
	<entry name="Eldar/HousingFlavor" value="&lt;string name='Buildings/Eldar/HousingFlavor'/&gt;"/>
	<entry name="Eldar/Infantry" value="&lt;string name='Buildings/Eldar/Infantry'/&gt;"/>
	<entry name="Eldar/InfantryDescription" value="&lt;string name='Buildings/Eldar/InfantryDescription'/&gt;"/>
	<entry name="Eldar/InfantryFlavor" value="&lt;string name='Buildings/Eldar/InfantryFlavor'/&gt;"/>
	<entry name="Eldar/Loyalty" value="&lt;string name='Buildings/Eldar/Loyalty'/&gt;"/>
	<entry name="Eldar/LoyaltyDescription" value="&lt;string name='Buildings/Eldar/LoyaltyDescription'/&gt;"/>
	<entry name="Eldar/LoyaltyFlavor" value="&lt;string name='Buildings/Eldar/LoyaltyFlavor'/&gt;"/>
	<entry name="Eldar/Vehicles" value="&lt;string name='Buildings/Eldar/Vehicles'/&gt;"/>
	<entry name="Eldar/VehiclesDescription" value="&lt;string name='Buildings/Eldar/VehiclesDescription'/&gt;"/>
	<entry name="Eldar/VehiclesFlavor" value="&lt;string name='Buildings/Eldar/VehiclesFlavor'/&gt;"/>
	<entry name="Necrons/Aircraft" value="&lt;string name='Buildings/Necrons/Aircraft'/&gt;"/>
	<entry name="Necrons/AircraftDescription" value="&lt;string name='Buildings/Necrons/AircraftDescription'/&gt;"/>
	<entry name="Necrons/AircraftFlavor" value="&lt;string name='Buildings/Necrons/AircraftFlavor'/&gt;"/>
	<entry name="Necrons/Construction" value="&lt;string name='Buildings/Necrons/Construction'/&gt;"/>
	<entry name="Necrons/ConstructionDescription" value="&lt;string name='Buildings/Necrons/ConstructionDescription'/&gt;"/>
	<entry name="Necrons/ConstructionFlavor" value="&lt;string name='Buildings/Necrons/ConstructionFlavor'/&gt;"/>
	<entry name="Necrons/Heroes" value="&lt;string name='Buildings/Necrons/Heroes'/&gt;"/>
	<entry name="Necrons/HeroesDescription" value="&lt;string name='Buildings/Necrons/HeroesDescription'/&gt;"/>
	<entry name="Necrons/HeroesFlavor" value="&lt;string name='Buildings/Necrons/HeroesFlavor'/&gt;"/>
	<entry name="Necrons/Housing" value="&lt;string name='Buildings/Necrons/Housing'/&gt;"/>
	<entry name="Necrons/HousingDescription" value="&lt;string name='Buildings/Necrons/HousingDescription'/&gt;"/>
	<entry name="Necrons/HousingFlavor" value="&lt;string name='Buildings/Necrons/HousingFlavor'/&gt;"/>
	<entry name="Necrons/Loyalty" value="&lt;string name='Buildings/Necrons/Loyalty'/&gt;"/>
	<entry name="Necrons/LoyaltyDescription" value="&lt;string name='Buildings/Necrons/LoyaltyDescription'/&gt;"/>
	<entry name="Necrons/LoyaltyFlavor" value="&lt;string name='Buildings/Necrons/LoyaltyFlavor'/&gt;"/>
	<entry name="Necrons/Vehicles" value="&lt;string name='Buildings/Necrons/Vehicles'/&gt;"/>
	<entry name="Necrons/VehiclesDescription" value="&lt;string name='Buildings/Necrons/VehiclesDescription'/&gt;"/>
	<entry name="Necrons/VehiclesFlavor" value="&lt;string name='Buildings/Necrons/VehiclesFlavor'/&gt;"/>
	<entry name="Orks/Beasts" value="&lt;string name='Buildings/Orks/Beasts'/&gt;"/>
	<entry name="Orks/BeastsDescription" value="&lt;string name='Buildings/Orks/BeastsDescription'/&gt;"/>
	<entry name="Orks/BeastsFlavor" value="&lt;string name='Buildings/Orks/BeastsFlavor'/&gt;"/>
	<entry name="Orks/Colonizers" value="&lt;string name='Buildings/Orks/Colonizers'/&gt;"/>
	<entry name="Orks/ColonizersDescription" value="&lt;string name='Buildings/Orks/ColonizersDescription'/&gt;"/>
	<entry name="Orks/ColonizersFlavor" value="&lt;string name='Buildings/Orks/ColonizersFlavor'/&gt;"/>
	<entry name="Orks/Construction" value="&lt;string name='Buildings/Orks/Construction'/&gt;"/>
	<entry name="Orks/ConstructionDescription" value="&lt;string name='Buildings/Orks/ConstructionDescription'/&gt;"/>
	<entry name="Orks/ConstructionFlavor" value="&lt;string name='Buildings/Orks/ConstructionFlavor'/&gt;"/>
	<entry name="Orks/Heroes" value="&lt;string name='Buildings/Orks/Heroes'/&gt;"/>
	<entry name="Orks/HeroesDescription" value="&lt;string name='Buildings/Orks/HeroesDescription'/&gt;"/>
	<entry name="Orks/HeroesFlavor" value="&lt;string name='Buildings/Orks/HeroesFlavor'/&gt;"/>
	<entry name="Orks/Housing" value="&lt;string name='Buildings/Orks/Housing'/&gt;"/>
	<entry name="Orks/HousingDescription" value="&lt;string name='Buildings/Orks/HousingDescription'/&gt;"/>
	<entry name="Orks/HousingFlavor" value="&lt;string name='Buildings/Orks/HousingFlavor'/&gt;"/>
	<entry name="Orks/Loyalty" value="&lt;string name='Buildings/Orks/Loyalty'/&gt;"/>
	<entry name="Orks/LoyaltyDescription" value="&lt;string name='Buildings/Orks/LoyaltyDescription'/&gt;"/>
	<entry name="Orks/LoyaltyFlavor" value="&lt;string name='Buildings/Orks/LoyaltyFlavor'/&gt;"/>
	<entry name="Orks/Vehicles" value="&lt;string name='Buildings/Orks/Vehicles'/&gt;"/>
	<entry name="Orks/VehiclesDescription" value="&lt;string name='Buildings/Orks/VehiclesDescription'/&gt;"/>
	<entry name="Orks/VehiclesFlavor" value="&lt;string name='Buildings/Orks/VehiclesFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Auxiliaries" value="&lt;string name='Buildings/SistersOfBattle/Auxiliaries'/&gt;"/>
	<entry name="SistersOfBattle/AuxiliariesDescription" value="&lt;string name='Buildings/SistersOfBattle/AuxiliariesDescription'/&gt;"/>
	<entry name="SistersOfBattle/AuxiliariesFlavor" value="&lt;string name='Buildings/SistersOfBattle/AuxiliariesFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Construction" value="&lt;string name='Buildings/SistersOfBattle/Construction'/&gt;"/>
	<entry name="SistersOfBattle/ConstructionDescription" value="&lt;string name='Buildings/SistersOfBattle/ConstructionDescription'/&gt;"/>
	<entry name="SistersOfBattle/ConstructionFlavor" value="&lt;string name='Buildings/SistersOfBattle/ConstructionFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Heroes" value="&lt;string name='Buildings/SistersOfBattle/Heroes'/&gt;"/>
	<entry name="SistersOfBattle/HeroesDescription" value="&lt;string name='Buildings/SistersOfBattle/HeroesDescription'/&gt;"/>
	<entry name="SistersOfBattle/HeroesFlavor" value="&lt;string name='Buildings/SistersOfBattle/HeroesFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Housing" value="&lt;string name='Buildings/SistersOfBattle/Housing'/&gt;"/>
	<entry name="SistersOfBattle/HousingDescription" value="&lt;string name='Buildings/SistersOfBattle/HousingDescription'/&gt;"/>
	<entry name="SistersOfBattle/HousingFlavor" value="&lt;string name='Buildings/SistersOfBattle/HousingFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Loyalty" value="&lt;string name='Buildings/SistersOfBattle/Loyalty'/&gt;"/>
	<entry name="SistersOfBattle/LoyaltyDescription" value="&lt;string name='Buildings/SistersOfBattle/LoyaltyDescription'/&gt;"/>
	<entry name="SistersOfBattle/LoyaltyFlavor" value="&lt;string name='Buildings/SistersOfBattle/LoyaltyFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Vehicles" value="&lt;string name='Buildings/SistersOfBattle/Vehicles'/&gt;"/>
	<entry name="SistersOfBattle/VehiclesDescription" value="&lt;string name='Buildings/SistersOfBattle/VehiclesDescription'/&gt;"/>
	<entry name="SistersOfBattle/VehiclesFlavor" value="&lt;string name='Buildings/SistersOfBattle/VehiclesFlavor'/&gt;"/>
	<entry name="SpaceMarines/Aircraft" value="&lt;string name='Buildings/SpaceMarines/Aircraft'/&gt;"/>
	<entry name="SpaceMarines/AircraftDescription" value="&lt;string name='Buildings/SpaceMarines/AircraftDescription'/&gt;"/>
	<entry name="SpaceMarines/AircraftFlavor" value="&lt;string name='Buildings/SpaceMarines/AircraftFlavor'/&gt;"/>
	<entry name="SpaceMarines/Construction" value="&lt;string name='Buildings/SpaceMarines/Construction'/&gt;"/>
	<entry name="SpaceMarines/ConstructionDescription" value="&lt;string name='Buildings/SpaceMarines/ConstructionDescription'/&gt;"/>
	<entry name="SpaceMarines/ConstructionFlavor" value="&lt;string name='Buildings/SpaceMarines/ConstructionFlavor'/&gt;"/>
	<entry name="SpaceMarines/GeneseedBunker" value="&lt;string name='Buildings/SpaceMarines/GeneseedBunker'/&gt;"/>
	<entry name="SpaceMarines/GeneseedBunkerDescription" value="&lt;string name='Buildings/SpaceMarines/GeneseedBunkerDescription'/&gt;"/>
	<entry name="SpaceMarines/GeneseedBunkerFlavor" value="&lt;string name='Buildings/SpaceMarines/GeneseedBunkerFlavor'/&gt;"/>
	<entry name="SpaceMarines/Heroes" value="&lt;string name='Buildings/SpaceMarines/Heroes'/&gt;"/>
	<entry name="SpaceMarines/HeroesDescription" value="&lt;string name='Buildings/SpaceMarines/HeroesDescription'/&gt;"/>
	<entry name="SpaceMarines/HeroesFlavor" value="&lt;string name='Buildings/SpaceMarines/HeroesFlavor'/&gt;"/>
	<entry name="SpaceMarines/Housing" value="&lt;string name='Buildings/SpaceMarines/Housing'/&gt;"/>
	<entry name="SpaceMarines/HousingDescription" value="&lt;string name='Buildings/SpaceMarines/HousingDescription'/&gt;"/>
	<entry name="SpaceMarines/HousingFlavor" value="&lt;string name='Buildings/SpaceMarines/HousingFlavor'/&gt;"/>
	<entry name="SpaceMarines/Loyalty" value="&lt;string name='Buildings/SpaceMarines/Loyalty'/&gt;"/>
	<entry name="SpaceMarines/LoyaltyDescription" value="&lt;string name='Buildings/SpaceMarines/LoyaltyDescription'/&gt;"/>
	<entry name="SpaceMarines/LoyaltyFlavor" value="&lt;string name='Buildings/SpaceMarines/LoyaltyFlavor'/&gt;"/>
	<entry name="SpaceMarines/Vehicles" value="&lt;string name='Buildings/SpaceMarines/Vehicles'/&gt;"/>
	<entry name="SpaceMarines/VehiclesDescription" value="&lt;string name='Buildings/SpaceMarines/VehiclesDescription'/&gt;"/>
	<entry name="SpaceMarines/VehiclesFlavor" value="&lt;string name='Buildings/SpaceMarines/VehiclesFlavor'/&gt;"/>
	<entry name="Tau/Aircraft" value="&lt;string name='Buildings/Tau/Aircraft'/&gt;"/>
	<entry name="Tau/AircraftDescription" value="&lt;string name='Buildings/Tau/AircraftDescription'/&gt;"/>
	<entry name="Tau/AircraftFlavor" value="&lt;string name='Buildings/Tau/AircraftFlavor'/&gt;"/>
	<entry name="Tau/Construction" value="&lt;string name='Buildings/Tau/Construction'/&gt;"/>
	<entry name="Tau/ConstructionDescription" value="&lt;string name='Buildings/Tau/ConstructionDescription'/&gt;"/>
	<entry name="Tau/ConstructionFlavor" value="&lt;string name='Buildings/Tau/ConstructionFlavor'/&gt;"/>
	<entry name="Tau/Heroes" value="&lt;string name='Buildings/Tau/Heroes'/&gt;"/>
	<entry name="Tau/HeroesDescription" value="&lt;string name='Buildings/Tau/HeroesDescription'/&gt;"/>
	<entry name="Tau/HeroesFlavor" value="&lt;string name='Buildings/Tau/HeroesFlavor'/&gt;"/>
	<entry name="Tau/Housing" value="&lt;string name='Buildings/Tau/Housing'/&gt;"/>
	<entry name="Tau/HousingDescription" value="&lt;string name='Buildings/Tau/HousingDescription'/&gt;"/>
	<entry name="Tau/HousingFlavor" value="&lt;string name='Buildings/Tau/HousingFlavor'/&gt;"/>
	<entry name="Tau/Loyalty" value="&lt;string name='Buildings/Tau/Loyalty'/&gt;"/>
	<entry name="Tau/LoyaltyDescription" value="&lt;string name='Buildings/Tau/LoyaltyDescription'/&gt;"/>
	<entry name="Tau/LoyaltyFlavor" value="&lt;string name='Buildings/Tau/LoyaltyFlavor'/&gt;"/>
	<entry name="Tau/MonstrousCreatures" value="&lt;string name='Buildings/Tau/MonstrousCreatures'/&gt;"/>
	<entry name="Tau/MonstrousCreaturesDescription" value="&lt;string name='Buildings/Tau/MonstrousCreaturesDescription'/&gt;"/>
	<entry name="Tau/MonstrousCreaturesFlavor" value="&lt;string name='Buildings/Tau/MonstrousCreaturesFlavor'/&gt;"/>
	<entry name="Tau/Vehicles" value="&lt;string name='Buildings/Tau/Vehicles'/&gt;"/>
	<entry name="Tau/VehiclesDescription" value="&lt;string name='Buildings/Tau/VehiclesDescription'/&gt;"/>
	<entry name="Tau/VehiclesFlavor" value="&lt;string name='Buildings/Tau/VehiclesFlavor'/&gt;"/>
	<entry name="Tyranids/Aircraft" value="&lt;string name='Buildings/Tyranids/Aircraft'/&gt;"/>
	<entry name="Tyranids/AircraftDescription" value="&lt;string name='Buildings/Tyranids/AircraftDescription'/&gt;"/>
	<entry name="Tyranids/AircraftFlavor" value="&lt;string name='Buildings/Tyranids/AircraftFlavor'/&gt;"/>
	<entry name="Tyranids/Construction" value="&lt;string name='Buildings/Tyranids/Construction'/&gt;"/>
	<entry name="Tyranids/ConstructionDescription" value="&lt;string name='Buildings/Tyranids/ConstructionDescription'/&gt;"/>
	<entry name="Tyranids/ConstructionFlavor" value="&lt;string name='Buildings/Tyranids/ConstructionFlavor'/&gt;"/>
	<entry name="Tyranids/Heroes" value="&lt;string name='Buildings/Tyranids/Heroes'/&gt;"/>
	<entry name="Tyranids/HeroesDescription" value="&lt;string name='Buildings/Tyranids/HeroesDescription'/&gt;"/>
	<entry name="Tyranids/HeroesFlavor" value="&lt;string name='Buildings/Tyranids/HeroesFlavor'/&gt;"/>
	<entry name="Tyranids/Housing" value="&lt;string name='Buildings/Tyranids/Housing'/&gt;"/>
	<entry name="Tyranids/HousingDescription" value="&lt;string name='Buildings/Tyranids/HousingDescription'/&gt;"/>
	<entry name="Tyranids/HousingFlavor" value="&lt;string name='Buildings/Tyranids/HousingFlavor'/&gt;"/>
	<entry name="Tyranids/Loyalty" value="&lt;string name='Buildings/Tyranids/Loyalty'/&gt;"/>
	<entry name="Tyranids/LoyaltyDescription" value="&lt;string name='Buildings/Tyranids/LoyaltyDescription'/&gt;"/>
	<entry name="Tyranids/LoyaltyFlavor" value="&lt;string name='Buildings/Tyranids/LoyaltyFlavor'/&gt;"/>
	<entry name="Tyranids/Thropes" value="&lt;string name='Buildings/Tyranids/Thropes'/&gt;"/>
	<entry name="Tyranids/ThropesDescription" value="&lt;string name='Buildings/Tyranids/ThropesDescription'/&gt;"/>
	<entry name="Tyranids/ThropesFlavor" value="&lt;string name='Buildings/Tyranids/ThropesFlavor'/&gt;"/>
	<entry name="Tyranids/Vehicles" value="&lt;string name='Buildings/Tyranids/Vehicles'/&gt;"/>
	<entry name="Tyranids/VehiclesDescription" value="&lt;string name='Buildings/Tyranids/VehiclesDescription'/&gt;"/>
	<entry name="Tyranids/VehiclesFlavor" value="&lt;string name='Buildings/Tyranids/VehiclesFlavor'/&gt;"/>
	
	<!-- Edicts -->
	<entry name="AstraMilitarum/AircraftProductionEdict" value="&lt;string name='Actions/AstraMilitarumAircraftProductionEdict'/&gt;"/>
	<entry name="AstraMilitarum/AircraftProductionEdictDescription" value="&lt;string name='Actions/AstraMilitarumAircraftProductionEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/AircraftProductionEdictFlavor" value="&lt;string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/DefenseEdict" value="&lt;string name='Actions/AstraMilitarumDefenseEdict'/&gt;"/>
	<entry name="AstraMilitarum/DefenseEdictDescription" value="&lt;string name='Actions/AstraMilitarumDefenseEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/DefenseEdictFlavor" value="&lt;string name='Actions/AstraMilitarumDefenseEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/EnergyEdict" value="&lt;string name='Actions/AstraMilitarumEnergyEdict'/&gt;"/>
	<entry name="AstraMilitarum/EnergyEdictDescription" value="&lt;string name='Actions/AstraMilitarumEnergyEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/EnergyEdictFlavor" value="&lt;string name='Actions/AstraMilitarumEnergyEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/FoodEdict" value="&lt;string name='Actions/AstraMilitarumFoodEdict'/&gt;"/>
	<entry name="AstraMilitarum/FoodEdictDescription" value="&lt;string name='Actions/AstraMilitarumFoodEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/FoodEdictFlavor" value="&lt;string name='Actions/AstraMilitarumFoodEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/GrowthEdict" value="&lt;string name='Actions/AstraMilitarumGrowthEdict'/&gt;"/>
	<entry name="AstraMilitarum/GrowthEdictDescription" value="&lt;string name='Actions/AstraMilitarumGrowthEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/GrowthEdictFlavor" value="&lt;string name='Actions/AstraMilitarumGrowthEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/InfantryProductionEdict" value="&lt;string name='Actions/AstraMilitarumInfantryProductionEdict'/&gt;"/>
	<entry name="AstraMilitarum/InfantryProductionEdictDescription" value="&lt;string name='Actions/AstraMilitarumInfantryProductionEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/InfantryProductionEdictFlavor" value="&lt;string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/LoyaltyEdict" value="&lt;string name='Actions/AstraMilitarumLoyaltyEdict'/&gt;"/>
	<entry name="AstraMilitarum/LoyaltyEdictDescription" value="&lt;string name='Actions/AstraMilitarumLoyaltyEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/LoyaltyEdictFlavor" value="&lt;string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/OreEdict" value="&lt;string name='Actions/AstraMilitarumOreEdict'/&gt;"/>
	<entry name="AstraMilitarum/OreEdictDescription" value="&lt;string name='Actions/AstraMilitarumOreEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/OreEdictFlavor" value="&lt;string name='Actions/AstraMilitarumOreEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/PsykerProductionEdict" value="&lt;string name='Actions/AstraMilitarumPsykerProductionEdict'/&gt;"/>
	<entry name="AstraMilitarum/PsykerProductionEdictDescription" value="&lt;string name='Actions/AstraMilitarumPsykerProductionEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/PsykerProductionEdictFlavor" value="&lt;string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/ResearchEdict" value="&lt;string name='Actions/AstraMilitarumResearchEdict'/&gt;"/>
	<entry name="AstraMilitarum/ResearchEdictDescription" value="&lt;string name='Actions/AstraMilitarumResearchEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/ResearchEdictFlavor" value="&lt;string name='Actions/AstraMilitarumResearchEdictFlavor'/&gt;"/>
	<entry name="AstraMilitarum/VehicleProductionEdict" value="&lt;string name='Actions/AstraMilitarumVehicleProductionEdict'/&gt;"/>
	<entry name="AstraMilitarum/VehicleProductionEdictDescription" value="&lt;string name='Actions/AstraMilitarumVehicleProductionEdictDescription'/&gt;"/>
	<entry name="AstraMilitarum/VehicleProductionEdictFlavor" value="&lt;string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/&gt;"/>
		
	<!-- Units -->
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="&lt;string name='Units/AdeptusMechanicus/ArchaeopterStratoraptor'/&gt;"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorDescription" value="&lt;string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorFlavor" value="&lt;string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest" value="&lt;string name='Units/AdeptusMechanicus/FulguriteElectroPriest'/&gt;"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestDescription" value="&lt;string name='Units/AdeptusMechanicus/FulguriteElectroPriestDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestFlavor" value="&lt;string name='Units/AdeptusMechanicus/FulguriteElectroPriestFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/KastelanRobot" value="&lt;string name='Units/Neutral/KastelanRobot'/&gt;"/>
	<entry name="AdeptusMechanicus/KastelanRobotDescription" value="&lt;string name='Units/Neutral/KastelanRobotDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/KastelanRobotFlavor" value="&lt;string name='Units/Neutral/KastelanRobotFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/KataphronBreacher" value="&lt;string name='Units/AdeptusMechanicus/KataphronBreacher'/&gt;"/>
	<entry name="AdeptusMechanicus/KataphronBreacherDescription" value="&lt;string name='Units/AdeptusMechanicus/KataphronBreacherDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/KataphronBreacherFlavor" value="&lt;string name='Units/AdeptusMechanicus/KataphronBreacherFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer" value="&lt;string name='Units/AdeptusMechanicus/KataphronDestroyer'/&gt;"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerDescription" value="&lt;string name='Units/AdeptusMechanicus/KataphronDestroyerDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerFlavor" value="&lt;string name='Units/AdeptusMechanicus/KataphronDestroyerFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/KnightCrusader" value="&lt;string name='Units/AdeptusMechanicus/KnightCrusader'/&gt;"/>
	<entry name="AdeptusMechanicus/KnightCrusaderDescription" value="&lt;string name='Units/AdeptusMechanicus/KnightCrusaderDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/KnightCrusaderFlavor" value="&lt;string name='Units/AdeptusMechanicus/KnightCrusaderFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="&lt;string name='Units/AdeptusMechanicus/OnagerDunecrawler'/&gt;"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerDescription" value="&lt;string name='Units/AdeptusMechanicus/OnagerDunecrawlerDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerFlavor" value="&lt;string name='Units/AdeptusMechanicus/OnagerDunecrawlerFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor" value="&lt;string name='Units/AdeptusMechanicus/PteraxiiSterylizor'/&gt;"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorDescription" value="&lt;string name='Units/AdeptusMechanicus/PteraxiiSterylizorDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorFlavor" value="&lt;string name='Units/AdeptusMechanicus/PteraxiiSterylizorFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="&lt;string name='Units/AdeptusMechanicus/SerberysSulphurhound'/&gt;"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundDescription" value="&lt;string name='Units/AdeptusMechanicus/SerberysSulphurhoundDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundFlavor" value="&lt;string name='Units/AdeptusMechanicus/SerberysSulphurhoundFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator" value="&lt;string name='Units/AdeptusMechanicus/SicarianInfiltrator'/&gt;"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorDescription" value="&lt;string name='Units/AdeptusMechanicus/SicarianInfiltratorDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorFlavor" value="&lt;string name='Units/AdeptusMechanicus/SicarianInfiltratorFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker" value="&lt;string name='Units/AdeptusMechanicus/SicarianRuststalker'/&gt;"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerDescription" value="&lt;string name='Units/AdeptusMechanicus/SicarianRuststalkerDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerFlavor" value="&lt;string name='Units/AdeptusMechanicus/SicarianRuststalkerFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal" value="&lt;string name='Units/AdeptusMechanicus/SkitariiMarshal'/&gt;"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalDescription" value="&lt;string name='Units/AdeptusMechanicus/SkitariiMarshalDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalFlavor" value="&lt;string name='Units/AdeptusMechanicus/SkitariiMarshalFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="&lt;string name='Units/AdeptusMechanicus/SkitariiRanger'/&gt;"/>
	<entry name="AdeptusMechanicus/SkitariiRangerDescription" value="&lt;string name='Units/AdeptusMechanicus/SkitariiRangerDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/SkitariiRangerFlavor" value="&lt;string name='Units/AdeptusMechanicus/SkitariiRangerFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="&lt;string name='Units/AdeptusMechanicus/SkorpiusDisintegrator'/&gt;"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorDescription" value="&lt;string name='Units/AdeptusMechanicus/SkorpiusDisintegratorDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorFlavor" value="&lt;string name='Units/AdeptusMechanicus/SkorpiusDisintegratorFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/SkorpiusDunerider" value="&lt;string name='Units/AdeptusMechanicus/SkorpiusDunerider'/&gt;"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderDescription" value="&lt;string name='Units/AdeptusMechanicus/SkorpiusDuneriderDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderFlavor" value="&lt;string name='Units/AdeptusMechanicus/SkorpiusDuneriderFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/SydonianDragoon" value="&lt;string name='Units/AdeptusMechanicus/SydonianDragoon'/&gt;"/>
	<entry name="AdeptusMechanicus/SydonianDragoonDescription" value="&lt;string name='Units/AdeptusMechanicus/SydonianDragoonDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/SydonianDragoonFlavor" value="&lt;string name='Units/AdeptusMechanicus/SydonianDragoonFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/TechPriestDominus" value="&lt;string name='Units/AdeptusMechanicus/TechPriestDominus'/&gt;"/>
	<entry name="AdeptusMechanicus/TechPriestDominusDescription" value="&lt;string name='Units/AdeptusMechanicus/TechPriestDominusDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/TechPriestDominusFlavor" value="&lt;string name='Units/AdeptusMechanicus/TechPriestDominusFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus" value="&lt;string name='Units/AdeptusMechanicus/TechPriestManipulus'/&gt;"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusDescription" value="&lt;string name='Units/AdeptusMechanicus/TechPriestManipulusDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusFlavor" value="&lt;string name='Units/AdeptusMechanicus/TechPriestManipulusFlavor'/&gt;"/>
	<entry name="AstraMilitarum/AttilanRoughRider" value="&lt;string name='Units/AstraMilitarum/AttilanRoughRider'/&gt;"/>
	<entry name="AstraMilitarum/AttilanRoughRiderDescription" value="&lt;string name='Units/AstraMilitarum/AttilanRoughRiderDescription'/&gt;"/>
	<entry name="AstraMilitarum/AttilanRoughRiderFlavor" value="&lt;string name='Units/AstraMilitarum/AttilanRoughRiderFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Baneblade" value="&lt;string name='Units/AstraMilitarum/Baneblade'/&gt;"/>
	<entry name="AstraMilitarum/BanebladeDescription" value="&lt;string name='Units/AstraMilitarum/BanebladeDescription'/&gt;"/>
	<entry name="AstraMilitarum/BanebladeFlavor" value="&lt;string name='Units/AstraMilitarum/BanebladeFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Basilisk" value="&lt;string name='Units/AstraMilitarum/Basilisk'/&gt;"/>
	<entry name="AstraMilitarum/BasiliskDescription" value="&lt;string name='Units/AstraMilitarum/BasiliskDescription'/&gt;"/>
	<entry name="AstraMilitarum/BasiliskFlavor" value="&lt;string name='Units/AstraMilitarum/BasiliskFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Bullgryn" value="&lt;string name='Units/AstraMilitarum/Bullgryn'/&gt;"/>
	<entry name="AstraMilitarum/BullgrynDescription" value="&lt;string name='Units/AstraMilitarum/BullgrynDescription'/&gt;"/>
	<entry name="AstraMilitarum/BullgrynFlavor" value="&lt;string name='Units/AstraMilitarum/BullgrynFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Chimera" value="&lt;string name='Units/AstraMilitarum/Chimera'/&gt;"/>
	<entry name="AstraMilitarum/ChimeraDescription" value="&lt;string name='Units/AstraMilitarum/ChimeraDescription'/&gt;"/>
	<entry name="AstraMilitarum/ChimeraFlavor" value="&lt;string name='Units/AstraMilitarum/ChimeraFlavor'/&gt;"/>
	<entry name="AstraMilitarum/DevilDog" value="&lt;string name='Units/AstraMilitarum/DevilDog'/&gt;"/>
	<entry name="AstraMilitarum/DevilDogDescription" value="&lt;string name='Units/AstraMilitarum/DevilDogDescription'/&gt;"/>
	<entry name="AstraMilitarum/DevilDogFlavor" value="&lt;string name='Units/AstraMilitarum/DevilDogFlavor'/&gt;"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery" value="&lt;string name='Units/AstraMilitarum/FieldOrdnanceBattery'/&gt;"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryDescription" value="&lt;string name='Units/AstraMilitarum/FieldOrdnanceBatteryDescription'/&gt;"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryFlavor" value="&lt;string name='Units/AstraMilitarum/FieldOrdnanceBatteryFlavor'/&gt;"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad" value="&lt;string name='Units/AstraMilitarum/HeavyWeaponsSquad'/&gt;"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadDescription" value="&lt;string name='Units/AstraMilitarum/HeavyWeaponsSquadDescription'/&gt;"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadFlavor" value="&lt;string name='Units/AstraMilitarum/HeavyWeaponsSquadFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Hydra" value="&lt;string name='Units/AstraMilitarum/Hydra'/&gt;"/>
	<entry name="AstraMilitarum/HydraDescription" value="&lt;string name='Units/AstraMilitarum/HydraDescription'/&gt;"/>
	<entry name="AstraMilitarum/HydraFlavor" value="&lt;string name='Units/AstraMilitarum/HydraFlavor'/&gt;"/>
	<entry name="AstraMilitarum/LemanRussBattleTank" value="&lt;string name='Units/AstraMilitarum/LemanRussBattleTank'/&gt;"/>
	<entry name="AstraMilitarum/LemanRussBattleTankDescription" value="&lt;string name='Units/AstraMilitarum/LemanRussBattleTankDescription'/&gt;"/>
	<entry name="AstraMilitarum/LemanRussBattleTankFlavor" value="&lt;string name='Units/AstraMilitarum/LemanRussBattleTankFlavor'/&gt;"/>
	<entry name="AstraMilitarum/LordCommissar" value="&lt;string name='Units/AstraMilitarum/LordCommissar'/&gt;"/>
	<entry name="AstraMilitarum/LordCommissarDescription" value="&lt;string name='Units/AstraMilitarum/LordCommissarDescription'/&gt;"/>
	<entry name="AstraMilitarum/LordCommissarFlavor" value="&lt;string name='Units/AstraMilitarum/LordCommissarFlavor'/&gt;"/>
	<entry name="AstraMilitarum/MarauderBomber" value="&lt;string name='Units/AstraMilitarum/MarauderBomber'/&gt;"/>
	<entry name="AstraMilitarum/MarauderBomberDescription" value="&lt;string name='Units/AstraMilitarum/MarauderBomberDescription'/&gt;"/>
	<entry name="AstraMilitarum/MarauderBomberFlavor" value="&lt;string name='Units/AstraMilitarum/MarauderBomberFlavor'/&gt;"/>
	<entry name="AstraMilitarum/PrimarisPsyker" value="&lt;string name='Units/AstraMilitarum/PrimarisPsyker'/&gt;"/>
	<entry name="AstraMilitarum/PrimarisPsykerDescription" value="&lt;string name='Units/AstraMilitarum/PrimarisPsykerDescription'/&gt;"/>
	<entry name="AstraMilitarum/PrimarisPsykerFlavor" value="&lt;string name='Units/AstraMilitarum/PrimarisPsykerFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Ratling" value="&lt;string name='Units/AstraMilitarum/Ratling'/&gt;"/>
	<entry name="AstraMilitarum/RatlingDescription" value="&lt;string name='Units/AstraMilitarum/RatlingDescription'/&gt;"/>
	<entry name="AstraMilitarum/RatlingFlavor" value="&lt;string name='Units/AstraMilitarum/RatlingFlavor'/&gt;"/>
	<entry name="AstraMilitarum/RogalDornBattleTank" value="&lt;string name='Units/AstraMilitarum/RogalDornBattleTank'/&gt;"/>
	<entry name="AstraMilitarum/RogalDornBattleTankDescription" value="&lt;string name='Units/AstraMilitarum/RogalDornBattleTankDescription'/&gt;"/>
	<entry name="AstraMilitarum/RogalDornBattleTankFlavor" value="&lt;string name='Units/AstraMilitarum/RogalDornBattleTankFlavor'/&gt;"/>
	<entry name="AstraMilitarum/ScoutSentinel" value="&lt;string name='Units/AstraMilitarum/ScoutSentinel'/&gt;"/>
	<entry name="AstraMilitarum/ScoutSentinelDescription" value="&lt;string name='Units/AstraMilitarum/ScoutSentinelDescription'/&gt;"/>
	<entry name="AstraMilitarum/ScoutSentinelFlavor" value="&lt;string name='Units/AstraMilitarum/ScoutSentinelFlavor'/&gt;"/>
	<entry name="AstraMilitarum/TankCommander" value="&lt;string name='Units/AstraMilitarum/TankCommander'/&gt;"/>
	<entry name="AstraMilitarum/TankCommanderDescription" value="&lt;string name='Units/AstraMilitarum/TankCommanderDescription'/&gt;"/>
	<entry name="AstraMilitarum/TankCommanderFlavor" value="&lt;string name='Units/AstraMilitarum/TankCommanderFlavor'/&gt;"/>
	<entry name="AstraMilitarum/TechpriestEnginseer" value="&lt;string name='Units/AstraMilitarum/TechpriestEnginseer'/&gt;"/>
	<entry name="AstraMilitarum/TechpriestEnginseerDescription" value="&lt;string name='Units/AstraMilitarum/TechpriestEnginseerDescription'/&gt;"/>
	<entry name="AstraMilitarum/TechpriestEnginseerFlavor" value="&lt;string name='Units/AstraMilitarum/TechpriestEnginseerFlavor'/&gt;"/>
	<entry name="AstraMilitarum/TempestusScion" value="&lt;string name='Units/AstraMilitarum/TempestusScion'/&gt;"/>
	<entry name="AstraMilitarum/TempestusScionDescription" value="&lt;string name='Units/AstraMilitarum/TempestusScionDescription'/&gt;"/>
	<entry name="AstraMilitarum/TempestusScionFlavor" value="&lt;string name='Units/AstraMilitarum/TempestusScionFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Thunderbolt" value="&lt;string name='Units/AstraMilitarum/Thunderbolt'/&gt;"/>
	<entry name="AstraMilitarum/ThunderboltDescription" value="&lt;string name='Units/AstraMilitarum/ThunderboltDescription'/&gt;"/>
	<entry name="AstraMilitarum/ThunderboltFlavor" value="&lt;string name='Units/AstraMilitarum/ThunderboltFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Valkyrie" value="&lt;string name='Units/AstraMilitarum/Valkyrie'/&gt;"/>
	<entry name="AstraMilitarum/ValkyrieDescription" value="&lt;string name='Units/AstraMilitarum/ValkyrieDescription'/&gt;"/>
	<entry name="AstraMilitarum/ValkyrieFlavor" value="&lt;string name='Units/AstraMilitarum/ValkyrieFlavor'/&gt;"/>
	<entry name="AstraMilitarum/WyrdvanePsyker" value="&lt;string name='Units/AstraMilitarum/WyrdvanePsyker'/&gt;"/>
	<entry name="AstraMilitarum/WyrdvanePsykerDescription" value="&lt;string name='Units/AstraMilitarum/WyrdvanePsykerDescription'/&gt;"/>
	<entry name="AstraMilitarum/WyrdvanePsykerFlavor" value="&lt;string name='Units/AstraMilitarum/WyrdvanePsykerFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider" value="&lt;string name='Units/ChaosSpaceMarines/ChaosLandRaider'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderDescription" value="&lt;string name='Units/ChaosSpaceMarines/ChaosLandRaiderDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderFlavor" value="&lt;string name='Units/ChaosSpaceMarines/ChaosLandRaiderFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn" value="&lt;string name='Units/ChaosSpaceMarines/ChaosSpawn'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnDescription" value="&lt;string name='Units/ChaosSpaceMarines/ChaosSpawnDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnFlavor" value="&lt;string name='Units/ChaosSpaceMarines/ChaosSpawnFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="&lt;string name='Units/ChaosSpaceMarines/ChaosTerminator'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorDescription" value="&lt;string name='Units/ChaosSpaceMarines/ChaosTerminatorDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorFlavor" value="&lt;string name='Units/ChaosSpaceMarines/ChaosTerminatorFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/DaemonPrince" value="&lt;string name='Units/ChaosSpaceMarines/DaemonPrince'/&gt;"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceDescription" value="&lt;string name='Units/ChaosSpaceMarines/DaemonPrinceDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceFlavor" value="&lt;string name='Units/ChaosSpaceMarines/DaemonPrinceFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/DarkDisciple" value="&lt;string name='Units/ChaosSpaceMarines/DarkDisciple'/&gt;"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleDescription" value="&lt;string name='Units/ChaosSpaceMarines/DarkDiscipleDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleFlavor" value="&lt;string name='Units/ChaosSpaceMarines/DarkDiscipleFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Defiler" value="&lt;string name='Units/ChaosSpaceMarines/Defiler'/&gt;"/>
	<entry name="ChaosSpaceMarines/DefilerDescription" value="&lt;string name='Units/ChaosSpaceMarines/DefilerDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/DefilerFlavor" value="&lt;string name='Units/ChaosSpaceMarines/DefilerFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne" value="&lt;string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorne'/&gt;"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription" value="&lt;string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor" value="&lt;string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Havoc" value="&lt;string name='Units/ChaosSpaceMarines/Havoc'/&gt;"/>
	<entry name="ChaosSpaceMarines/HavocDescription" value="&lt;string name='Units/ChaosSpaceMarines/HavocDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/HavocFlavor" value="&lt;string name='Units/ChaosSpaceMarines/HavocFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Helbrute" value="&lt;string name='Units/ChaosSpaceMarines/Helbrute'/&gt;"/>
	<entry name="ChaosSpaceMarines/HelbruteDescription" value="&lt;string name='Units/ChaosSpaceMarines/HelbruteDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/HelbruteFlavor" value="&lt;string name='Units/ChaosSpaceMarines/HelbruteFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker" value="&lt;string name='Units/ChaosSpaceMarines/KhorneBerzerker'/&gt;"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerDescription" value="&lt;string name='Units/ChaosSpaceMarines/KhorneBerzerkerDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerFlavor" value="&lt;string name='Units/ChaosSpaceMarines/KhorneBerzerkerFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="&lt;string name='Units/ChaosSpaceMarines/Forgefiend'/&gt;"/>
	<entry name="ChaosSpaceMarines/ForgefiendDescription" value="&lt;string name='Units/ChaosSpaceMarines/ForgefiendDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/ForgefiendFlavor" value="&lt;string name='Units/ChaosSpaceMarines/ForgefiendFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession" value="&lt;string name='Units/ChaosSpaceMarines/MasterOfPossession'/&gt;"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionDescription" value="&lt;string name='Units/ChaosSpaceMarines/MasterOfPossessionDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionFlavor" value="&lt;string name='Units/ChaosSpaceMarines/MasterOfPossessionFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Maulerfiend" value="&lt;string name='Units/ChaosSpaceMarines/Maulerfiend'/&gt;"/>
	<entry name="ChaosSpaceMarines/MaulerfiendDescription" value="&lt;string name='Units/ChaosSpaceMarines/MaulerfiendDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/MaulerfiendFlavor" value="&lt;string name='Units/ChaosSpaceMarines/MaulerfiendFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/NoctilithCrown" value="&lt;string name='Units/ChaosSpaceMarines/NoctilithCrown'/&gt;"/>
	<entry name="ChaosSpaceMarines/NoctilithCrownDescription" value="Дает Культистам Хаоса возможность строить укрепления."/>
	<entry name="ChaosSpaceMarines/NoctilithCrownFlavor" value="&lt;string name='Units/ChaosSpaceMarines/NoctilithCrownFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Obliterator" value="&lt;string name='Units/ChaosSpaceMarines/Obliterator'/&gt;"/>
	<entry name="ChaosSpaceMarines/ObliteratorDescription" value="&lt;string name='Units/ChaosSpaceMarines/ObliteratorDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/ObliteratorFlavor" value="&lt;string name='Units/ChaosSpaceMarines/ObliteratorFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/RubricMarine" value="&lt;string name='Units/ChaosSpaceMarines/RubricMarine'/&gt;"/>
	<entry name="ChaosSpaceMarines/RubricMarineDescription" value="&lt;string name='Units/ChaosSpaceMarines/RubricMarineDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/RubricMarineFlavor" value="&lt;string name='Units/ChaosSpaceMarines/RubricMarineFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Venomcrawler" value="&lt;string name='Units/ChaosSpaceMarines/Venomcrawler'/&gt;"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerDescription" value="&lt;string name='Units/ChaosSpaceMarines/VenomcrawlerDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerFlavor" value="&lt;string name='Units/ChaosSpaceMarines/VenomcrawlerFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Warpsmith" value="&lt;string name='Units/ChaosSpaceMarines/Warpsmith'/&gt;"/>
	<entry name="ChaosSpaceMarines/WarpsmithDescription" value="&lt;string name='Units/ChaosSpaceMarines/WarpsmithDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/WarpsmithFlavor" value="&lt;string name='Units/ChaosSpaceMarines/WarpsmithFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/WarpTalon" value="&lt;string name='Units/ChaosSpaceMarines/WarpTalon'/&gt;"/>
	<entry name="ChaosSpaceMarines/WarpTalonDescription" value="&lt;string name='Units/ChaosSpaceMarines/WarpTalonDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/WarpTalonFlavor" value="&lt;string name='Units/ChaosSpaceMarines/WarpTalonFlavor'/&gt;"/>
	<entry name="Drukhari/Cronos" value="&lt;string name='Units/Drukhari/Cronos'/&gt;"/>
	<entry name="Drukhari/CronosDescription" value="&lt;string name='Units/Drukhari/CronosDescription'/&gt;"/>
	<entry name="Drukhari/CronosFlavor" value="&lt;string name='Units/Drukhari/CronosFlavor'/&gt;"/>
	<entry name="Drukhari/Haemonculus" value="&lt;string name='Units/Drukhari/Haemonculus'/&gt;"/>
	<entry name="Drukhari/HaemonculusDescription" value="&lt;string name='Units/Drukhari/HaemonculusDescription'/&gt;"/>
	<entry name="Drukhari/HaemonculusFlavor" value="&lt;string name='Units/Drukhari/HaemonculusFlavor'/&gt;"/>
	<entry name="Drukhari/Hellion" value="&lt;string name='Units/Drukhari/Hellion'/&gt;"/>
	<entry name="Drukhari/HellionDescription" value="&lt;string name='Units/Drukhari/HellionDescription'/&gt;"/>
	<entry name="Drukhari/HellionFlavor" value="&lt;string name='Units/Drukhari/HellionFlavor'/&gt;"/>
	<entry name="Drukhari/Incubi" value="&lt;string name='Units/Drukhari/Incubi'/&gt;"/>
	<entry name="Drukhari/IncubiDescription" value="&lt;string name='Units/Drukhari/IncubiDescription'/&gt;"/>
	<entry name="Drukhari/IncubiFlavor" value="&lt;string name='Units/Drukhari/IncubiFlavor'/&gt;"/>
	<entry name="Drukhari/KabaliteTrueborn" value="&lt;string name='Units/Drukhari/KabaliteTrueborn'/&gt;"/>
	<entry name="Drukhari/KabaliteTruebornDescription" value="&lt;string name='Units/Drukhari/KabaliteTruebornDescription'/&gt;"/>
	<entry name="Drukhari/KabaliteTruebornFlavor" value="&lt;string name='Units/Drukhari/KabaliteTruebornFlavor'/&gt;"/>
	<entry name="Drukhari/Mandrake" value="&lt;string name='Units/Drukhari/Mandrake'/&gt;"/>
	<entry name="Drukhari/MandrakeDescription" value="&lt;string name='Units/Drukhari/MandrakeDescription'/&gt;"/>
	<entry name="Drukhari/MandrakeFlavor" value="&lt;string name='Units/Drukhari/MandrakeFlavor'/&gt;"/>
	<entry name="Drukhari/Raider" value="&lt;string name='Units/Drukhari/Raider'/&gt;"/>
	<entry name="Drukhari/RaiderDescription" value="&lt;string name='Units/Drukhari/RaiderDescription'/&gt;"/>
	<entry name="Drukhari/RaiderFlavor" value="&lt;string name='Units/Drukhari/RaiderFlavor'/&gt;"/>
	<entry name="Drukhari/Ravager" value="&lt;string name='Units/Drukhari/Ravager'/&gt;"/>
	<entry name="Drukhari/RavagerDescription" value="&lt;string name='Units/Drukhari/RavagerDescription'/&gt;"/>
	<entry name="Drukhari/RavagerFlavor" value="&lt;string name='Units/Drukhari/RavagerFlavor'/&gt;"/>
	<entry name="Drukhari/Reaver" value="&lt;string name='Units/Drukhari/Reaver'/&gt;"/>
	<entry name="Drukhari/ReaverDescription" value="&lt;string name='Units/Drukhari/ReaverDescription'/&gt;"/>
	<entry name="Drukhari/ReaverFlavor" value="&lt;string name='Units/Drukhari/ReaverFlavor'/&gt;"/>
	<entry name="Drukhari/Scourge" value="&lt;string name='Units/Drukhari/Scourge'/&gt;"/>
	<entry name="Drukhari/ScourgeDescription" value="&lt;string name='Units/Drukhari/ScourgeDescription'/&gt;"/>
	<entry name="Drukhari/ScourgeFlavor" value="&lt;string name='Units/Drukhari/ScourgeFlavor'/&gt;"/>
	<entry name="Drukhari/Succubus" value="&lt;string name='Units/Drukhari/Succubus'/&gt;"/>
	<entry name="Drukhari/SuccubusDescription" value="&lt;string name='Units/Drukhari/SuccubusDescription'/&gt;"/>
	<entry name="Drukhari/SuccubusFlavor" value="&lt;string name='Units/Drukhari/SuccubusFlavor'/&gt;"/>
	<entry name="Drukhari/Talos" value="&lt;string name='Units/Drukhari/Talos'/&gt;"/>
	<entry name="Drukhari/TalosDescription" value="&lt;string name='Units/Drukhari/TalosDescription'/&gt;"/>
	<entry name="Drukhari/TalosFlavor" value="&lt;string name='Units/Drukhari/TalosFlavor'/&gt;"/>
	<entry name="Drukhari/Tantalus" value="&lt;string name='Units/Drukhari/Tantalus'/&gt;"/>
	<entry name="Drukhari/TantalusDescription" value="&lt;string name='Units/Drukhari/TantalusDescription'/&gt;"/>
	<entry name="Drukhari/TantalusFlavor" value="&lt;string name='Units/Drukhari/TantalusFlavor'/&gt;"/>
	<entry name="Drukhari/VoidravenBomber" value="&lt;string name='Units/Drukhari/VoidravenBomber'/&gt;"/>
	<entry name="Drukhari/VoidravenBomberDescription" value="&lt;string name='Units/Drukhari/VoidravenBomberDescription'/&gt;"/>
	<entry name="Drukhari/VoidravenBomberFlavor" value="&lt;string name='Units/Drukhari/VoidravenBomberFlavor'/&gt;"/>
	<entry name="Drukhari/Wrack" value="&lt;string name='Units/Drukhari/Wrack'/&gt;"/>
	<entry name="Drukhari/WrackDescription" value="&lt;string name='Units/Drukhari/WrackDescription'/&gt;"/>
	<entry name="Drukhari/WrackFlavor" value="&lt;string name='Units/Drukhari/WrackFlavor'/&gt;"/>
	<entry name="Drukhari/Wyche" value="&lt;string name='Units/Drukhari/Wyche'/&gt;"/>
	<entry name="Drukhari/WycheDescription" value="&lt;string name='Units/Drukhari/WycheDescription'/&gt;"/>
	<entry name="Drukhari/WycheFlavor" value="&lt;string name='Units/Drukhari/WycheFlavor'/&gt;"/>
	<entry name="Eldar/AvatarOfKhaine" value="&lt;string name='Units/Eldar/AvatarOfKhaine'/&gt;"/>
	<entry name="Eldar/AvatarOfKhaineDescription" value="&lt;string name='Units/Eldar/AvatarOfKhaineDescription'/&gt;"/>
	<entry name="Eldar/AvatarOfKhaineFlavor" value="&lt;string name='Units/Eldar/AvatarOfKhaineFlavor'/&gt;"/>
	<entry name="Eldar/DarkReaper" value="&lt;string name='Units/Eldar/DarkReaper'/&gt;"/>
	<entry name="Eldar/DarkReaperDescription" value="&lt;string name='Units/Eldar/DarkReaperDescription'/&gt;"/>
	<entry name="Eldar/DarkReaperFlavor" value="&lt;string name='Units/Eldar/DarkReaperFlavor'/&gt;"/>
	<entry name="Eldar/FarseerSkyrunner" value="&lt;string name='Units/Eldar/FarseerSkyrunner'/&gt;"/>
	<entry name="Eldar/FarseerSkyrunnerDescription" value="&lt;string name='Units/Eldar/FarseerSkyrunnerDescription'/&gt;"/>
	<entry name="Eldar/FarseerSkyrunnerFlavor" value="&lt;string name='Units/Eldar/FarseerSkyrunnerFlavor'/&gt;"/>
	<entry name="Eldar/FireDragon" value="&lt;string name='Units/Eldar/FireDragon'/&gt;"/>
	<entry name="Eldar/FireDragonDescription" value="&lt;string name='Units/Eldar/FireDragonDescription'/&gt;"/>
	<entry name="Eldar/FireDragonFlavor" value="&lt;string name='Units/Eldar/FireDragonFlavor'/&gt;"/>
	<entry name="Eldar/FirePrism" value="&lt;string name='Units/Eldar/FirePrism'/&gt;"/>
	<entry name="Eldar/FirePrismDescription" value="&lt;string name='Units/Eldar/FirePrismDescription'/&gt;"/>
	<entry name="Eldar/FirePrismFlavor" value="&lt;string name='Units/Eldar/FirePrismFlavor'/&gt;"/>
	<entry name="Eldar/HemlockWraithfighter" value="&lt;string name='Units/Eldar/HemlockWraithfighter'/&gt;"/>
	<entry name="Eldar/HemlockWraithfighterDescription" value="&lt;string name='Units/Eldar/HemlockWraithfighterDescription'/&gt;"/>
	<entry name="Eldar/HemlockWraithfighterFlavor" value="&lt;string name='Units/Eldar/HemlockWraithfighterFlavor'/&gt;"/>
	<entry name="Eldar/Hornet" value="&lt;string name='Units/Eldar/Hornet'/&gt;"/>
	<entry name="Eldar/HornetDescription" value="&lt;string name='Units/Eldar/HornetDescription'/&gt;"/>
	<entry name="Eldar/HornetFlavor" value="&lt;string name='Units/Eldar/HornetFlavor'/&gt;"/>
	<entry name="Eldar/HowlingBanshee" value="&lt;string name='Units/Eldar/HowlingBanshee'/&gt;"/>
	<entry name="Eldar/HowlingBansheeDescription" value="&lt;string name='Units/Eldar/HowlingBansheeDescription'/&gt;"/>
	<entry name="Eldar/HowlingBansheeFlavor" value="&lt;string name='Units/Eldar/HowlingBansheeFlavor'/&gt;"/>
	<entry name="Eldar/Ranger" value="&lt;string name='Units/Eldar/Ranger'/&gt;"/>
	<entry name="Eldar/RangerDescription" value="&lt;string name='Units/Eldar/RangerDescription'/&gt;"/>
	<entry name="Eldar/RangerFlavor" value="&lt;string name='Units/Eldar/RangerFlavor'/&gt;"/>
	<entry name="Eldar/Scorpion" value="&lt;string name='Units/Eldar/Scorpion'/&gt;"/>
	<entry name="Eldar/ScorpionDescription" value="&lt;string name='Units/Eldar/ScorpionDescription'/&gt;"/>
	<entry name="Eldar/ScorpionFlavor" value="&lt;string name='Units/Eldar/ScorpionFlavor'/&gt;"/>
	<entry name="Eldar/Spiritseer" value="&lt;string name='Units/Eldar/Spiritseer'/&gt;"/>
	<entry name="Eldar/SpiritseerDescription" value="&lt;string name='Units/Eldar/SpiritseerDescription'/&gt;"/>
	<entry name="Eldar/SpiritseerFlavor" value="&lt;string name='Units/Eldar/SpiritseerFlavor'/&gt;"/>
	<entry name="Eldar/VaulsWrathSupportBattery" value="&lt;string name='Units/Eldar/VaulsWrathSupportBattery'/&gt;"/>
	<entry name="Eldar/VaulsWrathSupportBatteryDescription" value="&lt;string name='Units/Eldar/VaulsWrathSupportBatteryDescription'/&gt;"/>
	<entry name="Eldar/VaulsWrathSupportBatteryFlavor" value="&lt;string name='Units/Eldar/VaulsWrathSupportBatteryFlavor'/&gt;"/>
	<entry name="Eldar/Vyper" value="&lt;string name='Units/Eldar/Vyper'/&gt;"/>
	<entry name="Eldar/VyperDescription" value="&lt;string name='Units/Eldar/VyperDescription'/&gt;"/>
	<entry name="Eldar/VyperFlavor" value="&lt;string name='Units/Eldar/VyperFlavor'/&gt;"/>
	<entry name="Eldar/WarWalker" value="&lt;string name='Units/Eldar/WarWalker'/&gt;"/>
	<entry name="Eldar/WarWalkerDescription" value="&lt;string name='Units/Eldar/WarWalkerDescription'/&gt;"/>
	<entry name="Eldar/WarWalkerFlavor" value="&lt;string name='Units/Eldar/WarWalkerFlavor'/&gt;"/>
	<entry name="Eldar/Warlock" value="&lt;string name='Units/Eldar/Warlock'/&gt;"/>
	<entry name="Eldar/WarlockDescription" value="&lt;string name='Units/Eldar/WarlockDescription'/&gt;"/>
	<entry name="Eldar/WarlockFlavor" value="&lt;string name='Units/Eldar/WarlockFlavor'/&gt;"/>
	<entry name="Eldar/WaveSerpent" value="&lt;string name='Units/Eldar/WaveSerpent'/&gt;"/>
	<entry name="Eldar/WaveSerpentDescription" value="&lt;string name='Units/Eldar/WaveSerpentDescription'/&gt;"/>
	<entry name="Eldar/WaveSerpentFlavor" value="&lt;string name='Units/Eldar/WaveSerpentFlavor'/&gt;"/>
	<entry name="Eldar/Wraithblade" value="&lt;string name='Units/Eldar/Wraithblade'/&gt;"/>
	<entry name="Eldar/WraithbladeDescription" value="&lt;string name='Units/Eldar/WraithbladeDescription'/&gt;"/>
	<entry name="Eldar/WraithbladeFlavor" value="&lt;string name='Units/Eldar/WraithbladeFlavor'/&gt;"/>
	<entry name="Eldar/Wraithknight" value="&lt;string name='Units/Eldar/Wraithknight'/&gt;"/>
	<entry name="Eldar/WraithknightDescription" value="&lt;string name='Units/Eldar/WraithknightDescription'/&gt;"/>
	<entry name="Eldar/WraithknightFlavor" value="&lt;string name='Units/Eldar/WraithknightFlavor'/&gt;"/>
	<entry name="Eldar/Wraithlord" value="&lt;string name='Units/Eldar/Wraithlord'/&gt;"/>
	<entry name="Eldar/WraithlordDescription" value="&lt;string name='Units/Eldar/WraithlordDescription'/&gt;"/>
	<entry name="Eldar/WraithlordFlavor" value="&lt;string name='Units/Eldar/WraithlordFlavor'/&gt;"/>
	<entry name="Necrons/AnnihilationBarge" value="&lt;string name='Units/Necrons/AnnihilationBarge'/&gt;"/>
	<entry name="Necrons/AnnihilationBargeDescription" value="&lt;string name='Units/Necrons/AnnihilationBargeDescription'/&gt;"/>
	<entry name="Necrons/AnnihilationBargeFlavor" value="&lt;string name='Units/Necrons/AnnihilationBargeFlavor'/&gt;"/>
	<entry name="Necrons/CanoptekReanimator" value="&lt;string name='Units/Necrons/CanoptekReanimator'/&gt;"/>
	<entry name="Necrons/CanoptekReanimatorDescription" value="&lt;string name='Units/Necrons/CanoptekReanimatorDescription'/&gt;"/>
	<entry name="Necrons/CanoptekReanimatorFlavor" value="&lt;string name='Units/Necrons/CanoptekReanimatorFlavor'/&gt;"/>
	<entry name="Necrons/CanoptekSpyder" value="&lt;string name='Units/Necrons/CanoptekSpyder'/&gt;"/>
	<entry name="Necrons/CanoptekSpyderDescription" value="&lt;string name='Units/Necrons/CanoptekSpyderDescription'/&gt;"/>
	<entry name="Necrons/CanoptekSpyderFlavor" value="&lt;string name='Units/Necrons/CanoptekSpyderFlavor'/&gt;"/>
	<entry name="Necrons/CanoptekWraith" value="&lt;string name='Units/Necrons/CanoptekWraith'/&gt;"/>
	<entry name="Necrons/CanoptekWraithDescription" value="&lt;string name='Units/Necrons/CanoptekWraithDescription'/&gt;"/>
	<entry name="Necrons/CanoptekWraithFlavor" value="&lt;string name='Units/Necrons/CanoptekWraithFlavor'/&gt;"/>
	<entry name="Necrons/Cryptek" value="&lt;string name='Units/Necrons/Cryptek'/&gt;"/>
	<entry name="Necrons/CryptekDescription" value="&lt;string name='Units/Necrons/CryptekDescription'/&gt;"/>
	<entry name="Necrons/CryptekFlavor" value="&lt;string name='Units/Necrons/CryptekFlavor'/&gt;"/>
	<entry name="Necrons/Deathmark" value="&lt;string name='Units/Necrons/Deathmark'/&gt;"/>
	<entry name="Necrons/DeathmarkDescription" value="&lt;string name='Units/Necrons/DeathmarkDescription'/&gt;"/>
	<entry name="Necrons/DeathmarkFlavor" value="&lt;string name='Units/Necrons/DeathmarkFlavor'/&gt;"/>
	<entry name="Necrons/DestroyerLord" value="&lt;string name='Units/Necrons/DestroyerLord'/&gt;"/>
	<entry name="Necrons/DestroyerLordDescription" value="&lt;string name='Units/Necrons/DestroyerLordDescription'/&gt;"/>
	<entry name="Necrons/DestroyerLordFlavor" value="&lt;string name='Units/Necrons/DestroyerLordFlavor'/&gt;"/>
	<entry name="Necrons/DoomScythe" value="&lt;string name='Units/Necrons/DoomScythe'/&gt;"/>
	<entry name="Necrons/DoomScytheDescription" value="&lt;string name='Units/Necrons/DoomScytheDescription'/&gt;"/>
	<entry name="Necrons/DoomScytheFlavor" value="&lt;string name='Units/Necrons/DoomScytheFlavor'/&gt;"/>
	<entry name="Necrons/DoomsdayArk" value="&lt;string name='Units/Necrons/DoomsdayArk'/&gt;"/>
	<entry name="Necrons/DoomsdayArkDescription" value="&lt;string name='Units/Necrons/DoomsdayArkDescription'/&gt;"/>
	<entry name="Necrons/DoomsdayArkFlavor" value="&lt;string name='Units/Necrons/DoomsdayArkFlavor'/&gt;"/>
	<entry name="Necrons/FlayedOne" value="&lt;string name='Units/Necrons/FlayedOne'/&gt;"/>
	<entry name="Necrons/FlayedOneDescription" value="&lt;string name='Units/Necrons/FlayedOneDescription'/&gt;"/>
	<entry name="Necrons/FlayedOneFlavor" value="&lt;string name='Units/Necrons/FlayedOneFlavor'/&gt;"/>
	<entry name="Necrons/GhostArk" value="&lt;string name='Units/Necrons/GhostArk'/&gt;"/>
	<entry name="Necrons/GhostArkDescription" value="&lt;string name='Units/Necrons/GhostArkDescription'/&gt;"/>
	<entry name="Necrons/GhostArkFlavor" value="&lt;string name='Units/Necrons/GhostArkFlavor'/&gt;"/>
	<entry name="Necrons/HeavyDestroyer" value="&lt;string name='Units/Necrons/HeavyDestroyer'/&gt;"/>
	<entry name="Necrons/HeavyDestroyerDescription" value="&lt;string name='Units/Necrons/HeavyDestroyerDescription'/&gt;"/>
	<entry name="Necrons/HeavyDestroyerFlavor" value="&lt;string name='Units/Necrons/HeavyDestroyerFlavor'/&gt;"/>
	<entry name="Necrons/Immortal" value="&lt;string name='Units/Necrons/Immortal'/&gt;"/>
	<entry name="Necrons/ImmortalDescription" value="&lt;string name='Units/Necrons/ImmortalDescription'/&gt;"/>
	<entry name="Necrons/ImmortalFlavor" value="&lt;string name='Units/Necrons/ImmortalFlavor'/&gt;"/>
	<entry name="Necrons/Lord" value="&lt;string name='Units/Necrons/Lord'/&gt;"/>
	<entry name="Necrons/LordDescription" value="&lt;string name='Units/Necrons/LordDescription'/&gt;"/>
	<entry name="Necrons/LordFlavor" value="&lt;string name='Units/Necrons/LordFlavor'/&gt;"/>
	<entry name="Necrons/Monolith" value="&lt;string name='Units/Necrons/Monolith'/&gt;"/>
	<entry name="Necrons/MonolithDescription" value="&lt;string name='Units/Necrons/MonolithDescription'/&gt;"/>
	<entry name="Necrons/MonolithFlavor" value="&lt;string name='Units/Necrons/MonolithFlavor'/&gt;"/>
	<entry name="Necrons/NightScythe" value="&lt;string name='Units/Necrons/NightScythe'/&gt;"/>
	<entry name="Necrons/NightScytheDescription" value="&lt;string name='Units/Necrons/NightScytheDescription'/&gt;"/>
	<entry name="Necrons/NightScytheFlavor" value="&lt;string name='Units/Necrons/NightScytheFlavor'/&gt;"/>
	<entry name="Necrons/Obelisk" value="&lt;string name='Units/Necrons/Obelisk'/&gt;"/>
	<entry name="Necrons/ObeliskDescription" value="&lt;string name='Units/Necrons/ObeliskDescription'/&gt;"/>
	<entry name="Necrons/ObeliskFlavor" value="&lt;string name='Units/Necrons/ObeliskFlavor'/&gt;"/>
	<entry name="Necrons/SkorpekhDestroyer" value="&lt;string name='Units/Necrons/SkorpekhDestroyer'/&gt;"/>
	<entry name="Necrons/SkorpekhDestroyerDescription" value="&lt;string name='Units/Necrons/SkorpekhDestroyerDescription'/&gt;"/>
	<entry name="Necrons/SkorpekhDestroyerFlavor" value="&lt;string name='Units/Necrons/SkorpekhDestroyerFlavor'/&gt;"/>
	<entry name="Necrons/TombBlade" value="&lt;string name='Units/Necrons/TombBlade'/&gt;"/>
	<entry name="Necrons/TombBladeDescription" value="&lt;string name='Units/Necrons/TombBladeDescription'/&gt;"/>
	<entry name="Necrons/TombBladeFlavor" value="&lt;string name='Units/Necrons/TombBladeFlavor'/&gt;"/>
	<entry name="Necrons/TranscendentCtan" value="&lt;string name='Units/Necrons/TranscendentCtan'/&gt;"/>
	<entry name="Necrons/TranscendentCtanDescription" value="&lt;string name='Units/Necrons/TranscendentCtanDescription'/&gt;"/>
	<entry name="Necrons/TranscendentCtanFlavor" value="&lt;string name='Units/Necrons/TranscendentCtanFlavor'/&gt;"/>
	<entry name="Necrons/TriarchPraetorian" value="&lt;string name='Units/Necrons/TriarchPraetorian'/&gt;"/>
	<entry name="Necrons/TriarchPraetorianDescription" value="&lt;string name='Units/Necrons/TriarchPraetorianDescription'/&gt;"/>
	<entry name="Necrons/TriarchPraetorianFlavor" value="&lt;string name='Units/Necrons/TriarchPraetorianFlavor'/&gt;"/>
	<entry name="Necrons/TriarchStalker" value="&lt;string name='Units/Necrons/TriarchStalker'/&gt;"/>
	<entry name="Necrons/TriarchStalkerDescription" value="&lt;string name='Units/Necrons/TriarchStalkerDescription'/&gt;"/>
	<entry name="Necrons/TriarchStalkerFlavor" value="&lt;string name='Units/Necrons/TriarchStalkerFlavor'/&gt;"/>
	<entry name="Orks/Battlewagon" value="&lt;string name='Units/Orks/Battlewagon'/&gt;"/>
	<entry name="Orks/BattlewagonDescription" value="&lt;string name='Units/Orks/BattlewagonDescription'/&gt;"/>
	<entry name="Orks/BattlewagonFlavor" value="&lt;string name='Units/Orks/BattlewagonFlavor'/&gt;"/>
	<entry name="Orks/BigMek" value="&lt;string name='Units/Orks/BigMek'/&gt;"/>
	<entry name="Orks/BigMekDescription" value="&lt;string name='Units/Orks/BigMekDescription'/&gt;"/>
	<entry name="Orks/BigMekFlavor" value="&lt;string name='Units/Orks/BigMekFlavor'/&gt;"/>
	<entry name="Orks/BurnaBommer" value="&lt;string name='Units/Orks/BurnaBommer'/&gt;"/>
	<entry name="Orks/BurnaBommerDescription" value="&lt;string name='Units/Orks/BurnaBommerDescription'/&gt;"/>
	<entry name="Orks/BurnaBommerFlavor" value="&lt;string name='Units/Orks/BurnaBommerFlavor'/&gt;"/>
	<entry name="Orks/BurnaBoy" value="&lt;string name='Units/Orks/BurnaBoy'/&gt;"/>
	<entry name="Orks/BurnaBoyDescription" value="&lt;string name='Units/Orks/BurnaBoyDescription'/&gt;"/>
	<entry name="Orks/BurnaBoyFlavor" value="&lt;string name='Units/Orks/BurnaBoyFlavor'/&gt;"/>
	<entry name="Orks/Dakkajet" value="&lt;string name='Units/Orks/Dakkajet'/&gt;"/>
	<entry name="Orks/DakkajetDescription" value="&lt;string name='Units/Orks/DakkajetDescription'/&gt;"/>
	<entry name="Orks/DakkajetFlavor" value="&lt;string name='Units/Orks/DakkajetFlavor'/&gt;"/>
	<entry name="Orks/DeffDread" value="&lt;string name='Units/Orks/DeffDread'/&gt;"/>
	<entry name="Orks/DeffDreadDescription" value="&lt;string name='Units/Orks/DeffDreadDescription'/&gt;"/>
	<entry name="Orks/DeffDreadFlavor" value="&lt;string name='Units/Orks/DeffDreadFlavor'/&gt;"/>
	<entry name="Orks/Deffkopta" value="&lt;string name='Units/Orks/Deffkopta'/&gt;"/>
	<entry name="Orks/DeffkoptaDescription" value="&lt;string name='Units/Orks/DeffkoptaDescription'/&gt;"/>
	<entry name="Orks/DeffkoptaFlavor" value="&lt;string name='Units/Orks/DeffkoptaFlavor'/&gt;"/>
	<entry name="Orks/FlashGitz" value="&lt;string name='Units/Orks/FlashGitz'/&gt;"/>
	<entry name="Orks/FlashGitzDescription" value="&lt;string name='Units/Orks/FlashGitzDescription'/&gt;"/>
	<entry name="Orks/FlashGitzFlavor" value="&lt;string name='Units/Orks/FlashGitzFlavor'/&gt;"/>
	<entry name="Orks/GargantuanSquiggoth" value="&lt;string name='Units/Orks/GargantuanSquiggoth'/&gt;"/>
	<entry name="Orks/GargantuanSquiggothDescription" value="&lt;string name='Units/Orks/GargantuanSquiggothDescription'/&gt;"/>
	<entry name="Orks/GargantuanSquiggothFlavor" value="&lt;string name='Units/Orks/GargantuanSquiggothFlavor'/&gt;"/>
	<entry name="Orks/Gorkanaut" value="&lt;string name='Units/Orks/Gorkanaut'/&gt;"/>
	<entry name="Orks/GorkanautDescription" value="&lt;string name='Units/Orks/GorkanautDescription'/&gt;"/>
	<entry name="Orks/GorkanautFlavor" value="&lt;string name='Units/Orks/GorkanautFlavor'/&gt;"/>
	<entry name="Orks/KillBursta" value="&lt;string name='Units/Orks/KillBursta'/&gt;"/>
	<entry name="Orks/KillBurstaDescription" value="&lt;string name='Units/Orks/KillBurstaDescription'/&gt;"/>
	<entry name="Orks/KillBurstaFlavor" value="&lt;string name='Units/Orks/KillBurstaFlavor'/&gt;"/>
	<entry name="Orks/KillaKan" value="&lt;string name='Units/Orks/KillaKan'/&gt;"/>
	<entry name="Orks/KillaKanDescription" value="&lt;string name='Units/Orks/KillaKanDescription'/&gt;"/>
	<entry name="Orks/KillaKanFlavor" value="&lt;string name='Units/Orks/KillaKanFlavor'/&gt;"/>
	<entry name="Orks/Meganob" value="&lt;string name='Units/Orks/Meganob'/&gt;"/>
	<entry name="Orks/MeganobDescription" value="&lt;string name='Units/Orks/MeganobDescription'/&gt;"/>
	<entry name="Orks/MeganobFlavor" value="&lt;string name='Units/Orks/MeganobFlavor'/&gt;"/>
	<entry name="Orks/MegatrakkScrapjet" value="&lt;string name='Units/Orks/MegatrakkScrapjet'/&gt;"/>
	<entry name="Orks/MegatrakkScrapjetDescription" value="&lt;string name='Units/Orks/MegatrakkScrapjetDescription'/&gt;"/>
	<entry name="Orks/MegatrakkScrapjetFlavor" value="&lt;string name='Units/Orks/MegatrakkScrapjetFlavor'/&gt;"/>
	<entry name="Orks/Mek" value="&lt;string name='Units/Orks/Mek'/&gt;"/>
	<entry name="Orks/MekDescription" value="&lt;string name='Units/Orks/MekDescription'/&gt;"/>
	<entry name="Orks/MekFlavor" value="&lt;string name='Units/Orks/MekFlavor'/&gt;"/>
	<entry name="Orks/MekGun" value="&lt;string name='Units/Orks/MekGun'/&gt;"/>
	<entry name="Orks/MekGunDescription" value="&lt;string name='Units/Orks/MekGunDescription'/&gt;"/>
	<entry name="Orks/MekGunFlavor" value="&lt;string name='Units/Orks/MekGunFlavor'/&gt;"/>
	<entry name="Orks/Painboy" value="&lt;string name='Units/Orks/Painboy'/&gt;"/>
	<entry name="Orks/PainboyDescription" value="&lt;string name='Units/Orks/PainboyDescription'/&gt;"/>
	<entry name="Orks/PainboyFlavor" value="&lt;string name='Units/Orks/PainboyFlavor'/&gt;"/>
	<entry name="Orks/SquighogBoy" value="&lt;string name='Units/Orks/SquighogBoy'/&gt;"/>
	<entry name="Orks/SquighogBoyDescription" value="&lt;string name='Units/Orks/SquighogBoyDescription'/&gt;"/>
	<entry name="Orks/SquighogBoyFlavor" value="&lt;string name='Units/Orks/SquighogBoyFlavor'/&gt;"/>
	<entry name="Orks/Tankbusta" value="&lt;string name='Units/Orks/Tankbusta'/&gt;"/>
	<entry name="Orks/TankbustaDescription" value="&lt;string name='Units/Orks/TankbustaDescription'/&gt;"/>
	<entry name="Orks/TankbustaFlavor" value="&lt;string name='Units/Orks/TankbustaFlavor'/&gt;"/>
	<entry name="Orks/Warbiker" value="&lt;string name='Units/Orks/Warbiker'/&gt;"/>
	<entry name="Orks/WarbikerDescription" value="&lt;string name='Units/Orks/WarbikerDescription'/&gt;"/>
	<entry name="Orks/WarbikerFlavor" value="&lt;string name='Units/Orks/WarbikerFlavor'/&gt;"/>
	<entry name="Orks/Warboss" value="&lt;string name='Units/Orks/Warboss'/&gt;"/>
	<entry name="Orks/WarbossDescription" value="&lt;string name='Units/Orks/WarbossDescription'/&gt;"/>
	<entry name="Orks/WarbossFlavor" value="&lt;string name='Units/Orks/WarbossFlavor'/&gt;"/>
	<entry name="Orks/Warbuggy" value="&lt;string name='Units/Orks/Warbuggy'/&gt;"/>
	<entry name="Orks/WarbuggyDescription" value="&lt;string name='Units/Orks/WarbuggyDescription'/&gt;"/>
	<entry name="Orks/WarbuggyFlavor" value="&lt;string name='Units/Orks/WarbuggyFlavor'/&gt;"/>
	<entry name="Orks/Weirdboy" value="&lt;string name='Units/Orks/Weirdboy'/&gt;"/>
	<entry name="Orks/WeirdboyDescription" value="&lt;string name='Units/Orks/WeirdboyDescription'/&gt;"/>
	<entry name="Orks/WeirdboyFlavor" value="&lt;string name='Units/Orks/WeirdboyFlavor'/&gt;"/>
	<entry name="SistersOfBattle/ArcoFlagellant" value="&lt;string name='Units/SistersOfBattle/ArcoFlagellant'/&gt;"/>
	<entry name="SistersOfBattle/ArcoFlagellantDescription" value="&lt;string name='Units/SistersOfBattle/ArcoFlagellantDescription'/&gt;"/>
	<entry name="SistersOfBattle/ArcoFlagellantFlavor" value="&lt;string name='Units/SistersOfBattle/ArcoFlagellantFlavor'/&gt;"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter" value="&lt;string name='Units/SistersOfBattle/AvengerStrikeFighter'/&gt;"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterDescription" value="&lt;string name='Units/SistersOfBattle/AvengerStrikeFighterDescription'/&gt;"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterFlavor" value="&lt;string name='Units/SistersOfBattle/AvengerStrikeFighterFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Castigator" value="&lt;string name='Units/SistersOfBattle/Castigator'/&gt;"/>
	<entry name="SistersOfBattle/CastigatorDescription" value="&lt;string name='Units/SistersOfBattle/CastigatorDescription'/&gt;"/>
	<entry name="SistersOfBattle/CastigatorFlavor" value="&lt;string name='Units/SistersOfBattle/CastigatorFlavor'/&gt;"/>
	<entry name="SistersOfBattle/CelestianSacresant" value="&lt;string name='Units/SistersOfBattle/CelestianSacresant'/&gt;"/>
	<entry name="SistersOfBattle/CelestianSacresantDescription" value="&lt;string name='Units/SistersOfBattle/CelestianSacresantDescription'/&gt;"/>
	<entry name="SistersOfBattle/CelestianSacresantFlavor" value="&lt;string name='Units/SistersOfBattle/CelestianSacresantFlavor'/&gt;"/>
	<entry name="SistersOfBattle/CerastusKnightLancer" value="&lt;string name='Units/SistersOfBattle/CerastusKnightLancer'/&gt;"/>
	<entry name="SistersOfBattle/CerastusKnightLancerDescription" value="&lt;string name='Units/SistersOfBattle/CerastusKnightLancerDescription'/&gt;"/>
	<entry name="SistersOfBattle/CerastusKnightLancerFlavor" value="&lt;string name='Units/SistersOfBattle/CerastusKnightLancerFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Dialogus" value="&lt;string name='Units/SistersOfBattle/Dialogus'/&gt;"/>
	<entry name="SistersOfBattle/DialogusDescription" value="&lt;string name='Units/SistersOfBattle/DialogusDescription'/&gt;"/>
	<entry name="SistersOfBattle/DialogusFlavor" value="&lt;string name='Units/SistersOfBattle/DialogusFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Dominion" value="&lt;string name='Units/SistersOfBattle/Dominion'/&gt;"/>
	<entry name="SistersOfBattle/DominionDescription" value="&lt;string name='Units/SistersOfBattle/DominionDescription'/&gt;"/>
	<entry name="SistersOfBattle/DominionFlavor" value="&lt;string name='Units/SistersOfBattle/DominionFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Exorcist" value="&lt;string name='Units/SistersOfBattle/Exorcist'/&gt;"/>
	<entry name="SistersOfBattle/ExorcistDescription" value="&lt;string name='Units/SistersOfBattle/ExorcistDescription'/&gt;"/>
	<entry name="SistersOfBattle/ExorcistFlavor" value="&lt;string name='Units/SistersOfBattle/ExorcistFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Headquarters" value="&lt;string name='Buildings/SistersOfBattle/Headquarters'/&gt;"/>
	<entry name="SistersOfBattle/HeadquartersDescription" value="&lt;string name='Units/SistersOfBattle/HeadquartersDescription'/&gt;"/>
	<entry name="SistersOfBattle/HeadquartersFlavor" value="&lt;string name='Buildings/SistersOfBattle/HeadquartersFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Hospitaller" value="&lt;string name='Units/SistersOfBattle/Hospitaller'/&gt;"/>
	<entry name="SistersOfBattle/HospitallerDescription" value="&lt;string name='Units/SistersOfBattle/HospitallerDescription'/&gt;"/>
	<entry name="SistersOfBattle/HospitallerFlavor" value="&lt;string name='Units/SistersOfBattle/HospitallerFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Imagifier" value="&lt;string name='Units/SistersOfBattle/Imagifier'/&gt;"/>
	<entry name="SistersOfBattle/ImagifierDescription" value="&lt;string name='Units/SistersOfBattle/ImagifierDescription'/&gt;"/>
	<entry name="SistersOfBattle/ImagifierFlavor" value="&lt;string name='Units/SistersOfBattle/ImagifierFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Mortifier" value="&lt;string name='Units/SistersOfBattle/Mortifier'/&gt;"/>
	<entry name="SistersOfBattle/MortifierDescription" value="&lt;string name='Units/SistersOfBattle/MortifierDescription'/&gt;"/>
	<entry name="SistersOfBattle/MortifierFlavor" value="&lt;string name='Units/SistersOfBattle/MortifierFlavor'/&gt;"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="&lt;string name='Units/SistersOfBattle/ParagonWarsuit'/&gt;"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="&lt;string name='Units/SistersOfBattle/ParagonWarsuitDescription'/&gt;"/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="&lt;string name='Units/SistersOfBattle/ParagonWarsuitFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Retributor" value="&lt;string name='Units/SistersOfBattle/Retributor'/&gt;"/>
	<entry name="SistersOfBattle/RetributorDescription" value="&lt;string name='Units/SistersOfBattle/RetributorDescription'/&gt;"/>
	<entry name="SistersOfBattle/RetributorFlavor" value="&lt;string name='Units/SistersOfBattle/RetributorFlavor'/&gt;"/>
	<entry name="SistersOfBattle/SaintCelestine" value="&lt;string name='Units/SistersOfBattle/SaintCelestine'/&gt;"/>
	<entry name="SistersOfBattle/SaintCelestineDescription" value="&lt;string name='Units/SistersOfBattle/SaintCelestineDescription'/&gt;"/>
	<entry name="SistersOfBattle/SaintCelestineFlavor" value="&lt;string name='Units/SistersOfBattle/SaintCelestineFlavor'/&gt;"/>
	<entry name="SistersOfBattle/SisterRepentia" value="&lt;string name='Units/SistersOfBattle/SisterRepentia'/&gt;"/>
	<entry name="SistersOfBattle/SisterRepentiaDescription" value="&lt;string name='Units/SistersOfBattle/SisterRepentiaDescription'/&gt;"/>
	<entry name="SistersOfBattle/SisterRepentiaFlavor" value="&lt;string name='Units/SistersOfBattle/SisterRepentiaFlavor'/&gt;"/>
	<entry name="SistersOfBattle/Zephyrim" value="&lt;string name='Units/SistersOfBattle/Zephyrim'/&gt;"/>
	<entry name="SistersOfBattle/ZephyrimDescription" value="&lt;string name='Units/SistersOfBattle/ZephyrimDescription'/&gt;"/>
	<entry name="SistersOfBattle/ZephyrimFlavor" value="&lt;string name='Units/SistersOfBattle/ZephyrimFlavor'/&gt;"/>
	<entry name="SpaceMarines/Apothecary" value="&lt;string name='Units/SpaceMarines/Apothecary'/&gt;"/>
	<entry name="SpaceMarines/ApothecaryDescription" value="&lt;string name='Units/SpaceMarines/ApothecaryDescription'/&gt;"/>
	<entry name="SpaceMarines/ApothecaryFlavor" value="&lt;string name='Units/SpaceMarines/ApothecaryFlavor'/&gt;"/>
	<entry name="SpaceMarines/AquilaMacroCannon" value="&lt;string name='Units/SpaceMarines/AquilaMacroCannon'/&gt;"/>
	<entry name="SpaceMarines/AquilaMacroCannonDescription" value="&lt;string name='Units/SpaceMarines/AquilaMacroCannonDescription'/&gt;"/>
	<entry name="SpaceMarines/AquilaMacroCannonFlavor" value="&lt;string name='Units/SpaceMarines/AquilaMacroCannonFlavor'/&gt;"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="&lt;string name='Units/SpaceMarines/AssaultSpaceMarine'/&gt;"/>
	<entry name="SpaceMarines/AssaultSpaceMarineDescription" value="&lt;string name='Units/SpaceMarines/AssaultSpaceMarineDescription'/&gt;"/>
	<entry name="SpaceMarines/AssaultSpaceMarineFlavor" value="&lt;string name='Units/SpaceMarines/AssaultSpaceMarineFlavor'/&gt;"/>
	<entry name="SpaceMarines/AssaultTerminator" value="&lt;string name='Units/SpaceMarines/AssaultTerminator'/&gt;"/>
	<entry name="SpaceMarines/AssaultTerminatorDescription" value="&lt;string name='Units/SpaceMarines/AssaultTerminatorDescription'/&gt;"/>
	<entry name="SpaceMarines/AssaultTerminatorFlavor" value="&lt;string name='Units/SpaceMarines/AssaultTerminatorFlavor'/&gt;"/>
	<entry name="SpaceMarines/Captain" value="&lt;string name='Units/SpaceMarines/Captain'/&gt;"/>
	<entry name="SpaceMarines/CaptainDescription" value="&lt;string name='Units/SpaceMarines/CaptainDescription'/&gt;"/>
	<entry name="SpaceMarines/CaptainFlavor" value="&lt;string name='Units/SpaceMarines/CaptainFlavor'/&gt;"/>
	<entry name="SpaceMarines/Chaplain" value="&lt;string name='Units/SpaceMarines/Chaplain'/&gt;"/>
	<entry name="SpaceMarines/ChaplainDescription" value="&lt;string name='Units/SpaceMarines/ChaplainDescription'/&gt;"/>
	<entry name="SpaceMarines/ChaplainFlavor" value="&lt;string name='Units/SpaceMarines/ChaplainFlavor'/&gt;"/>
	<entry name="SpaceMarines/DevastatorCenturion" value="&lt;string name='Units/SpaceMarines/DevastatorCenturion'/&gt;"/>
	<entry name="SpaceMarines/DevastatorCenturionDescription" value="&lt;string name='Units/SpaceMarines/DevastatorCenturionDescription'/&gt;"/>
	<entry name="SpaceMarines/DevastatorCenturionFlavor" value="&lt;string name='Units/SpaceMarines/DevastatorCenturionFlavor'/&gt;"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="&lt;string name='Units/SpaceMarines/DevastatorSpaceMarine'/&gt;"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineDescription" value="&lt;string name='Units/SpaceMarines/DevastatorSpaceMarineDescription'/&gt;"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineFlavor" value="&lt;string name='Units/SpaceMarines/DevastatorSpaceMarineFlavor'/&gt;"/>
	<entry name="SpaceMarines/Dreadnought" value="&lt;string name='Units/SpaceMarines/Dreadnought'/&gt;"/>
	<entry name="SpaceMarines/DreadnoughtDescription" value="&lt;string name='Units/SpaceMarines/DreadnoughtDescription'/&gt;"/>
	<entry name="SpaceMarines/DreadnoughtFlavor" value="&lt;string name='Units/SpaceMarines/DreadnoughtFlavor'/&gt;"/>
	<entry name="SpaceMarines/Hunter" value="&lt;string name='Units/SpaceMarines/Hunter'/&gt;"/>
	<entry name="SpaceMarines/HunterDescription" value="&lt;string name='Units/SpaceMarines/HunterDescription'/&gt;"/>
	<entry name="SpaceMarines/HunterFlavor" value="&lt;string name='Units/SpaceMarines/HunterFlavor'/&gt;"/>
	<entry name="SpaceMarines/LandRaider" value="&lt;string name='Units/SpaceMarines/LandRaider'/&gt;"/>
	<entry name="SpaceMarines/LandRaiderDescription" value="&lt;string name='Units/SpaceMarines/LandRaiderDescription'/&gt;"/>
	<entry name="SpaceMarines/LandRaiderFlavor" value="&lt;string name='Units/SpaceMarines/LandRaiderFlavor'/&gt;"/>
	<entry name="SpaceMarines/LandSpeeder" value="&lt;string name='Units/SpaceMarines/LandSpeeder'/&gt;"/>
	<entry name="SpaceMarines/LandSpeederDescription" value="&lt;string name='Units/SpaceMarines/LandSpeederDescription'/&gt;"/>
	<entry name="SpaceMarines/LandSpeederFlavor" value="&lt;string name='Units/SpaceMarines/LandSpeederFlavor'/&gt;"/>
	<entry name="SpaceMarines/Librarian" value="&lt;string name='Units/SpaceMarines/Librarian'/&gt;"/>
	<entry name="SpaceMarines/LibrarianDescription" value="&lt;string name='Units/SpaceMarines/LibrarianDescription'/&gt;"/>
	<entry name="SpaceMarines/LibrarianFlavor" value="&lt;string name='Units/SpaceMarines/LibrarianFlavor'/&gt;"/>	
	<entry name="SpaceMarines/Predator" value="&lt;string name='Units/SpaceMarines/Predator'/&gt;"/>
	<entry name="SpaceMarines/PredatorDescription" value="&lt;string name='Units/SpaceMarines/PredatorDescription'/&gt;"/>
	<entry name="SpaceMarines/PredatorFlavor" value="&lt;string name='Units/SpaceMarines/PredatorFlavor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisAggressor" value="&lt;string name='Units/SpaceMarines/PrimarisAggressor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisAggressorDescription" value="&lt;string name='Units/SpaceMarines/PrimarisAggressorDescription'/&gt;"/>
	<entry name="SpaceMarines/PrimarisAggressorFlavor" value="&lt;string name='Units/SpaceMarines/PrimarisAggressorFlavor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisHellblaster" value="&lt;string name='Units/SpaceMarines/PrimarisHellblaster'/&gt;"/>
	<entry name="SpaceMarines/PrimarisHellblasterDescription" value="&lt;string name='Units/SpaceMarines/PrimarisHellblasterDescription'/&gt;"/>
	<entry name="SpaceMarines/PrimarisHellblasterFlavor" value="&lt;string name='Units/SpaceMarines/PrimarisHellblasterFlavor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisInceptor" value="&lt;string name='Units/SpaceMarines/PrimarisInceptor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisInceptorDescription" value="&lt;string name='Units/SpaceMarines/PrimarisInceptorDescription'/&gt;"/>
	<entry name="SpaceMarines/PrimarisInceptorFlavor" value="&lt;string name='Units/SpaceMarines/PrimarisInceptorFlavor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisIntercessor" value="&lt;string name='Units/SpaceMarines/PrimarisIntercessor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisIntercessorDescription" value="&lt;string name='Units/SpaceMarines/PrimarisIntercessorDescription'/&gt;"/>
	<entry name="SpaceMarines/PrimarisIntercessorFlavor" value="&lt;string name='Units/SpaceMarines/PrimarisIntercessorFlavor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisInvaderATV" value="&lt;string name='Units/SpaceMarines/PrimarisInvaderATV'/&gt;"/>
	<entry name="SpaceMarines/PrimarisInvaderATVDescription" value="&lt;string name='Units/SpaceMarines/PrimarisInvaderATVDescription'/&gt;"/>
	<entry name="SpaceMarines/PrimarisInvaderATVFlavor" value="&lt;string name='Units/SpaceMarines/PrimarisInvaderATVFlavor'/&gt;"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner" value="&lt;string name='Units/SpaceMarines/PrimarisRepulsorExecutioner'/&gt;"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerDescription" value="&lt;string name='Units/SpaceMarines/PrimarisRepulsorExecutionerDescription'/&gt;"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerFlavor" value="&lt;string name='Units/SpaceMarines/PrimarisRepulsorExecutionerFlavor'/&gt;"/>
	<entry name="SpaceMarines/Razorback" value="&lt;string name='Units/SpaceMarines/Razorback'/&gt;"/>
	<entry name="SpaceMarines/RazorbackDescription" value="&lt;string name='Units/SpaceMarines/RazorbackDescription'/&gt;"/>
	<entry name="SpaceMarines/RazorbackFlavor" value="&lt;string name='Units/SpaceMarines/RazorbackFlavor'/&gt;"/>
	<entry name="SpaceMarines/RedemptorDreadnought" value="&lt;string name='Units/SpaceMarines/RedemptorDreadnought'/&gt;"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtDescription" value="&lt;string name='Units/SpaceMarines/RedemptorDreadnoughtDescription'/&gt;"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtFlavor" value="&lt;string name='Units/SpaceMarines/RedemptorDreadnoughtFlavor'/&gt;"/>
	<entry name="SpaceMarines/Scout" value="&lt;string name='Units/SpaceMarines/Scout'/&gt;"/>
	<entry name="SpaceMarines/ScoutDescription" value="&lt;string name='Units/SpaceMarines/ScoutDescription'/&gt;"/>
	<entry name="SpaceMarines/ScoutFlavor" value="&lt;string name='Units/SpaceMarines/ScoutFlavor'/&gt;"/>
	<entry name="SpaceMarines/ScoutBiker" value="&lt;string name='Units/SpaceMarines/ScoutBiker'/&gt;"/>
	<entry name="SpaceMarines/ScoutBikerDescription" value="&lt;string name='Units/SpaceMarines/ScoutBikerDescription'/&gt;"/>
	<entry name="SpaceMarines/ScoutBikerFlavor" value="&lt;string name='Units/SpaceMarines/ScoutBikerFlavor'/&gt;"/>
	<entry name="SpaceMarines/StormravenGunship" value="&lt;string name='Units/SpaceMarines/StormravenGunship'/&gt;"/>
	<entry name="SpaceMarines/StormravenGunshipDescription" value="&lt;string name='Units/SpaceMarines/StormravenGunshipDescription'/&gt;"/>
	<entry name="SpaceMarines/StormravenGunshipFlavor" value="&lt;string name='Units/SpaceMarines/StormravenGunshipFlavor'/&gt;"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike" value="&lt;string name='Units/SpaceMarines/StormSpeederThunderstrike'/&gt;"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeDescription" value="&lt;string name='Units/SpaceMarines/StormSpeederThunderstrikeDescription'/&gt;"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeFlavor" value="&lt;string name='Units/SpaceMarines/StormSpeederThunderstrikeFlavor'/&gt;"/>
	<entry name="SpaceMarines/StormtalonGunship" value="&lt;string name='Units/SpaceMarines/StormtalonGunship'/&gt;"/>
	<entry name="SpaceMarines/StormtalonGunshipDescription" value="&lt;string name='Units/SpaceMarines/StormtalonGunshipDescription'/&gt;"/>
	<entry name="SpaceMarines/StormtalonGunshipFlavor" value="&lt;string name='Units/SpaceMarines/StormtalonGunshipFlavor'/&gt;"/>
	<entry name="SpaceMarines/Terminator" value="&lt;string name='Units/SpaceMarines/Terminator'/&gt;"/>
	<entry name="SpaceMarines/TerminatorDescription" value="&lt;string name='Units/SpaceMarines/TerminatorDescription'/&gt;"/>
	<entry name="SpaceMarines/TerminatorFlavor" value="&lt;string name='Units/SpaceMarines/TerminatorFlavor'/&gt;"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="&lt;string name='Units/SpaceMarines/ThunderfireCannon'/&gt;"/>
	<entry name="SpaceMarines/ThunderfireCannonDescription" value="&lt;string name='Units/SpaceMarines/ThunderfireCannonDescription'/&gt;"/>
	<entry name="SpaceMarines/ThunderfireCannonFlavor" value="&lt;string name='Units/SpaceMarines/ThunderfireCannonFlavor'/&gt;"/>
	<entry name="SpaceMarines/Vindicator" value="&lt;string name='Units/SpaceMarines/Vindicator'/&gt;"/>
	<entry name="SpaceMarines/VindicatorDescription" value="&lt;string name='Units/SpaceMarines/VindicatorDescription'/&gt;"/>
	<entry name="SpaceMarines/VindicatorFlavor" value="&lt;string name='Units/SpaceMarines/VindicatorFlavor'/&gt;"/>
	<entry name="SpaceMarines/Whirlwind" value="&lt;string name='Units/SpaceMarines/Whirlwind'/&gt;"/>
	<entry name="SpaceMarines/WhirlwindDescription" value="&lt;string name='Units/SpaceMarines/WhirlwindDescription'/&gt;"/>
	<entry name="SpaceMarines/WhirlwindFlavor" value="&lt;string name='Units/SpaceMarines/WhirlwindFlavor'/&gt;"/>
	<entry name="Tau/BroadsideBattlesuit" value="&lt;string name='Units/Tau/BroadsideBattlesuit'/&gt;"/>
	<entry name="Tau/BroadsideBattlesuitDescription" value="&lt;string name='Units/Tau/BroadsideBattlesuitDescription'/&gt;"/>
	<entry name="Tau/BroadsideBattlesuitFlavor" value="&lt;string name='Units/Tau/BroadsideBattlesuitFlavor'/&gt;"/>
	<entry name="Tau/BuilderDrone" value="&lt;string name='Units/Tau/BuilderDrone'/&gt;"/>
	<entry name="Tau/BuilderDroneDescription" value="&lt;string name='Units/Tau/BuilderDroneDescription'/&gt;"/>
	<entry name="Tau/BuilderDroneFlavor" value="&lt;string name='Units/Tau/BuilderDroneFlavor'/&gt;"/>
	<entry name="Tau/Commander" value="&lt;string name='Units/Tau/Commander'/&gt;"/>
	<entry name="Tau/CommanderDescription" value="&lt;string name='Units/Tau/CommanderDescription'/&gt;"/>
	<entry name="Tau/CommanderFlavor" value="&lt;string name='Units/Tau/CommanderFlavor'/&gt;"/>
	<entry name="Tau/CrisisBattlesuit" value="&lt;string name='Units/Tau/CrisisBattlesuit'/&gt;"/>
	<entry name="Tau/CrisisBattlesuitDescription" value="&lt;string name='Units/Tau/CrisisBattlesuitDescription'/&gt;"/>
	<entry name="Tau/CrisisBattlesuitFlavor" value="&lt;string name='Units/Tau/CrisisBattlesuitFlavor'/&gt;"/>
	<entry name="Tau/Devilfish" value="&lt;string name='Units/Tau/Devilfish'/&gt;"/>
	<entry name="Tau/DevilfishDescription" value="&lt;string name='Units/Tau/DevilfishDescription'/&gt;"/>
	<entry name="Tau/DevilfishFlavor" value="&lt;string name='Units/Tau/DevilfishFlavor'/&gt;"/>
	<entry name="Tau/Ethereal" value="&lt;string name='Units/Tau/Ethereal'/&gt;"/>
	<entry name="Tau/EtherealDescription" value="&lt;string name='Units/Tau/EtherealDescription'/&gt;"/>
	<entry name="Tau/EtherealFlavor" value="&lt;string name='Units/Tau/EtherealFlavor'/&gt;"/>
	<entry name="Tau/FireWarriorBreacher" value="&lt;string name='Units/Tau/FireWarriorBreacher'/&gt;"/>
	<entry name="Tau/FireWarriorBreacherDescription" value="&lt;string name='Units/Tau/FireWarriorBreacherDescription'/&gt;"/>
	<entry name="Tau/FireWarriorBreacherFlavor" value="&lt;string name='Units/Tau/FireWarriorBreacherFlavor'/&gt;"/>
	<entry name="Tau/GunDrone" value="&lt;string name='Units/Tau/GunDrone'/&gt;"/>
	<entry name="Tau/GunDroneDescription" value="Предоставляет Воинам Огня, Командам прорыва воинов Огня, БСК XV8 Кризис, БСК XV25 Невидимка, БСК XV88 Залп, Кадровому Огненному клинку, Эфирному и Командующему возможность временно использовать боевые дроны."/>
	<entry name="Tau/GunDroneFlavor" value="&lt;string name='Units/Tau/GunDroneFlavor'/&gt;"/>
	<entry name="Tau/HammerheadGunship" value="&lt;string name='Units/Tau/HammerheadGunship'/&gt;"/>
	<entry name="Tau/HammerheadGunshipDescription" value="&lt;string name='Units/Tau/HammerheadGunshipDescription'/&gt;"/>
	<entry name="Tau/HammerheadGunshipFlavor" value="&lt;string name='Units/Tau/HammerheadGunshipFlavor'/&gt;"/>
	<entry name="Tau/KrootoxRider" value="&lt;string name='Units/Tau/KrootoxRider'/&gt;"/>
	<entry name="Tau/KrootoxRiderDescription" value="&lt;string name='Units/Tau/KrootoxRiderDescription'/&gt;"/>
	<entry name="Tau/KrootoxRiderFlavor" value="&lt;string name='Units/Tau/KrootoxRiderFlavor'/&gt;"/>
	<entry name="Tau/MarkerDrone" value="&lt;string name='Units/Tau/MarkerDrone'/&gt;"/>
	<entry name="Tau/MarkerDroneDescription" value="Предоставляет Воинам Огня, Командам прорыва воинов Огня, БСК XV8 Кризис, БСК XV25 Невидимка, БСК XV88 Залп, Кадровому Огненному клинку, Эфирному и Командующему возможность временно использовать дроны, помечающие врагов для увеличения урона."/>
	<entry name="Tau/MarkerDroneFlavor" value="&lt;string name='Units/Tau/MarkerDroneFlavor'/&gt;"/>
	<entry name="Tau/Pathfinder" value="&lt;string name='Units/Tau/Pathfinder'/&gt;"/>
	<entry name="Tau/PathfinderDescription" value="&lt;string name='Units/Tau/PathfinderDescription'/&gt;"/>
	<entry name="Tau/PathfinderFlavor" value="&lt;string name='Units/Tau/PathfinderFlavor'/&gt;"/>
	<entry name="Tau/RiptideBattlesuit" value="&lt;string name='Units/Tau/RiptideBattlesuit'/&gt;"/>
	<entry name="Tau/RiptideBattlesuitDescription" value="&lt;string name='Units/Tau/RiptideBattlesuitDescription'/&gt;"/>
	<entry name="Tau/RiptideBattlesuitFlavor" value="&lt;string name='Units/Tau/RiptideBattlesuitFlavor'/&gt;"/>
	<entry name="Tau/RVarnaBattlesuit" value="&lt;string name='Units/Tau/RVarnaBattlesuit'/&gt;"/>
	<entry name="Tau/RVarnaBattlesuitDescription" value="&lt;string name='Units/Tau/RVarnaBattlesuitDescription'/&gt;"/>
	<entry name="Tau/RVarnaBattlesuitFlavor" value="&lt;string name='Units/Tau/RVarnaBattlesuitFlavor'/&gt;"/>
	<entry name="Tau/ShieldDrone" value="&lt;string name='Units/Tau/ShieldDrone'/&gt;"/>
	<entry name="Tau/ShieldDroneDescription" value="Предоставляет Воинам Огня, Командам прорыва воинов Огня, БСК XV8 Кризис, БСК XV25 Невидимка, БСК XV88 Залп, Кадровому Огненному клинку, Эфирному и Командующему возможность временно использовать дроны, защищающие союзников."/>
	<entry name="Tau/ShieldDroneFlavor" value="&lt;string name='Units/Tau/ShieldDroneFlavor'/&gt;"/>
	<entry name="Tau/SkyRayGunship" value="&lt;string name='Units/Tau/SkyRayGunship'/&gt;"/>
	<entry name="Tau/SkyRayGunshipDescription" value="&lt;string name='Units/Tau/SkyRayGunshipDescription'/&gt;"/>
	<entry name="Tau/SkyRayGunshipFlavor" value="&lt;string name='Units/Tau/SkyRayGunshipFlavor'/&gt;"/>
	<entry name="Tau/StealthBattlesuit" value="&lt;string name='Units/Tau/StealthBattlesuit'/&gt;"/>
	<entry name="Tau/StealthBattlesuitDescription" value="&lt;string name='Units/Tau/StealthBattlesuitDescription'/&gt;"/>
	<entry name="Tau/StealthBattlesuitFlavor" value="&lt;string name='Units/Tau/StealthBattlesuitFlavor'/&gt;"/>
	<entry name="Tau/Stormsurge" value="&lt;string name='Units/Tau/Stormsurge'/&gt;"/>
	<entry name="Tau/StormsurgeDescription" value="&lt;string name='Units/Tau/StormsurgeDescription'/&gt;"/>
	<entry name="Tau/StormsurgeFlavor" value="&lt;string name='Units/Tau/StormsurgeFlavor'/&gt;"/>
	<entry name="Tau/SunSharkBomber" value="&lt;string name='Units/Tau/SunSharkBomber'/&gt;"/>
	<entry name="Tau/SunSharkBomberDescription" value="&lt;string name='Units/Tau/SunSharkBomberDescription'/&gt;"/>
	<entry name="Tau/SunSharkBomberFlavor" value="&lt;string name='Units/Tau/SunSharkBomberFlavor'/&gt;"/>
	<entry name="Tau/TidewallGunrig" value="&lt;string name='Units/Tau/TidewallGunrig'/&gt;"/>
	<entry name="Tau/TidewallGunrigDescription" value="Предоставляет дронам-строителям возможность построить хорошо вооруженное укрепление, которое можно использовать для перебраски войск."/>
	<entry name="Tau/TidewallGunrigFlavor" value="&lt;string name='Units/Tau/TidewallGunrigFlavor'/&gt;"/>
	<entry name="Tau/TigerShark" value="&lt;string name='Units/Tau/TigerShark'/&gt;"/>
	<entry name="Tau/TigerSharkDescription" value="&lt;string name='Units/Tau/TigerSharkDescription'/&gt;"/>
	<entry name="Tau/TigerSharkFlavor" value="&lt;string name='Units/Tau/TigerSharkFlavor'/&gt;"/>
	<entry name="Tyranids/Biovore" value="&lt;string name='Units/Tyranids/Biovore'/&gt;"/>
	<entry name="Tyranids/BiovoreDescription" value="&lt;string name='Units/Tyranids/BiovoreDescription'/&gt;"/>
	<entry name="Tyranids/BiovoreFlavor" value="&lt;string name='Units/Tyranids/BiovoreFlavor'/&gt;"/>
	<entry name="Tyranids/Carnifex" value="&lt;string name='Units/Tyranids/Carnifex'/&gt;"/>
	<entry name="Tyranids/CarnifexDescription" value="&lt;string name='Units/Tyranids/CarnifexDescription'/&gt;"/>
	<entry name="Tyranids/CarnifexFlavor" value="&lt;string name='Units/Tyranids/CarnifexFlavor'/&gt;"/>
	<entry name="Tyranids/Exocrine" value="&lt;string name='Units/Tyranids/Exocrine'/&gt;"/>
	<entry name="Tyranids/ExocrineDescription" value="&lt;string name='Units/Tyranids/ExocrineDescription'/&gt;"/>
	<entry name="Tyranids/ExocrineFlavor" value="&lt;string name='Units/Tyranids/ExocrineFlavor'/&gt;"/>
	<entry name="Tyranids/Gargoyle" value="&lt;string name='Units/Tyranids/Gargoyle'/&gt;"/>
	<entry name="Tyranids/GargoyleDescription" value="&lt;string name='Units/Tyranids/GargoyleDescription'/&gt;"/>
	<entry name="Tyranids/GargoyleFlavor" value="&lt;string name='Units/Tyranids/GargoyleFlavor'/&gt;"/>
	<entry name="Tyranids/Haruspex" value="&lt;string name='Units/Tyranids/Haruspex'/&gt;"/>
	<entry name="Tyranids/HaruspexDescription" value="&lt;string name='Units/Tyranids/HaruspexDescription'/&gt;"/>
	<entry name="Tyranids/HaruspexFlavor" value="&lt;string name='Units/Tyranids/HaruspexFlavor'/&gt;"/>
	<entry name="Tyranids/HiveTyrant" value="&lt;string name='Units/Tyranids/HiveTyrant'/&gt;"/>
	<entry name="Tyranids/HiveTyrantDescription" value="&lt;string name='Units/Tyranids/HiveTyrantDescription'/&gt;"/>
	<entry name="Tyranids/HiveTyrantFlavor" value="&lt;string name='Units/Tyranids/HiveTyrantFlavor'/&gt;"/>
	<entry name="Tyranids/HiveGuard" value="&lt;string name='Units/Tyranids/HiveGuard'/&gt;"/>
	<entry name="Tyranids/HiveGuardDescription" value="&lt;string name='Units/Tyranids/HiveGuardDescription'/&gt;"/>
	<entry name="Tyranids/HiveGuardFlavor" value="&lt;string name='Units/Tyranids/HiveGuardFlavor'/&gt;"/>
	<entry name="Tyranids/Hormagaunt" value="&lt;string name='Units/Tyranids/Hormagaunt'/&gt;"/>
	<entry name="Tyranids/HormagauntDescription" value="&lt;string name='Units/Tyranids/HormagauntDescription'/&gt;"/>
	<entry name="Tyranids/HormagauntFlavor" value="&lt;string name='Units/Tyranids/HormagauntFlavor'/&gt;"/>
	<entry name="Tyranids/Lictor" value="&lt;string name='Units/Tyranids/Lictor'/&gt;"/>
	<entry name="Tyranids/LictorDescription" value="&lt;string name='Units/Tyranids/LictorDescription'/&gt;"/>
	<entry name="Tyranids/LictorFlavor" value="&lt;string name='Units/Tyranids/LictorFlavor'/&gt;"/>
	<entry name="Tyranids/Maleceptor" value="&lt;string name='Units/Tyranids/Maleceptor'/&gt;"/>
	<entry name="Tyranids/MaleceptorDescription" value="&lt;string name='Units/Tyranids/MaleceptorDescription'/&gt;"/>
	<entry name="Tyranids/MaleceptorFlavor" value="&lt;string name='Units/Tyranids/MaleceptorFlavor'/&gt;"/>
	<entry name="Tyranids/NornEmissary" value="&lt;string name='Units/Tyranids/NornEmissary'/&gt;"/>
	<entry name="Tyranids/NornEmissaryDescription" value="&lt;string name='Units/Tyranids/NornEmissaryDescription'/&gt;"/>
	<entry name="Tyranids/NornEmissaryFlavor" value="&lt;string name='Units/Tyranids/NornEmissaryFlavor'/&gt;"/>
	<entry name="Tyranids/Ravener" value="&lt;string name='Units/Tyranids/Ravener'/&gt;"/>
	<entry name="Tyranids/RavenerDescription" value="&lt;string name='Units/Tyranids/RavenerDescription'/&gt;"/>
	<entry name="Tyranids/RavenerFlavor" value="&lt;string name='Units/Tyranids/RavenerFlavor'/&gt;"/>
	<entry name="Tyranids/ScythedHierodule" value="&lt;string name='Units/Tyranids/ScythedHierodule'/&gt;"/>
	<entry name="Tyranids/ScythedHieroduleDescription" value="&lt;string name='Units/Tyranids/ScythedHieroduleDescription'/&gt;"/>
	<entry name="Tyranids/ScythedHieroduleFlavor" value="&lt;string name='Units/Tyranids/ScythedHieroduleFlavor'/&gt;"/>
	<entry name="Tyranids/Tervigon" value="&lt;string name='Units/Tyranids/Tervigon'/&gt;"/>
	<entry name="Tyranids/TervigonDescription" value="&lt;string name='Units/Tyranids/TervigonDescription'/&gt;"/>
	<entry name="Tyranids/TervigonFlavor" value="&lt;string name='Units/Tyranids/TervigonFlavor'/&gt;"/>
	<entry name="Tyranids/Trygon" value="&lt;string name='Units/Tyranids/Trygon'/&gt;"/>
	<entry name="Tyranids/TrygonDescription" value="&lt;string name='Units/Tyranids/TrygonDescription'/&gt;"/>
	<entry name="Tyranids/TrygonFlavor" value="&lt;string name='Units/Tyranids/TrygonFlavor'/&gt;"/>
	<entry name="Tyranids/Tyrannofex" value="&lt;string name='Units/Tyranids/Tyrannofex'/&gt;"/>
	<entry name="Tyranids/TyrannofexDescription" value="&lt;string name='Units/Tyranids/TyrannofexDescription'/&gt;"/>
	<entry name="Tyranids/TyrannofexFlavor" value="&lt;string name='Units/Tyranids/TyrannofexFlavor'/&gt;"/>
	<entry name="Tyranids/Venomthrope" value="&lt;string name='Units/Tyranids/Venomthrope'/&gt;"/>
	<entry name="Tyranids/VenomthropeDescription" value="&lt;string name='Units/Tyranids/VenomthropeDescription'/&gt;"/>
	<entry name="Tyranids/VenomthropeFlavor" value="&lt;string name='Units/Tyranids/VenomthropeFlavor'/&gt;"/>
	<entry name="Tyranids/Warrior" value="&lt;string name='Units/Tyranids/Warrior'/&gt;"/>
	<entry name="Tyranids/WarriorDescription" value="&lt;string name='Units/Tyranids/WarriorDescription'/&gt;"/>
	<entry name="Tyranids/WarriorFlavor" value="&lt;string name='Units/Tyranids/WarriorFlavor'/&gt;"/>
	<entry name="Tyranids/Zoanthrope" value="&lt;string name='Units/Tyranids/Zoanthrope'/&gt;"/>
	<entry name="Tyranids/ZoanthropeDescription" value="&lt;string name='Units/Tyranids/ZoanthropeDescription'/&gt;"/>
	<entry name="Tyranids/ZoanthropeFlavor" value="&lt;string name='Units/Tyranids/ZoanthropeFlavor'/&gt;"/>

	<!-- Other -->
	<entry name="SmokeLauncher" value="Дымовой гранатомёт"/>
	<entry name="SmokeLauncherDescription" value="Даёт наземной технике возможность ставить дымовую завесу, которая повышает сокращение дистанц. урона."/>
	<entry name="SmokeLauncherFlavor" value="Некоторые автомобили имеют небольшие пусковые установки, смонтированные на них, которые несут заряды дыма. Они используются, чтобы временно скрыть технику за клубящимися облаками дыма, позволяя ей пересекать открытые участки в большей безопасности, хотя огонь из своего оружия тоже становиться слабее."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="&lt;string name='Traits/AdeptusMechanicus/AdjacencyIntegration'/&gt;"/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Увеличивает исследования зданий от каждого здания на соседней плитке."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="&lt;string name='Traits/AdeptusMechanicus/AdjacencyIntegrationFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/AggressorImperative" value="&lt;string name='Actions/AdeptusMechanicus/AggressorImperative'/&gt;"/>
	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="&lt;string name='Actions/AdeptusMechanicus/AggressorImperativeDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="&lt;string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="&lt;string name='Traits/AdeptusMechanicus/AssaultWeaponBonus'/&gt;"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Увеличивает бронебойность штурмового оружия."/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="&lt;string name='Traits/AdeptusMechanicus/AssaultWeaponBonusFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="&lt;string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/&gt;"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="&lt;string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="&lt;string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/BlessedConduits" value="Благословенные каналы"/>
	<entry name="AdeptusMechanicus/BlessedConduitsDescription" value="Уменьшает время восстановления Скачка напряжения."/>
	<entry name="AdeptusMechanicus/BlessedConduitsFlavor" value="“И когда, наконец, он натолкнулся на машину, он почувствовал, что двигатель в ней неисправен, и тут же наложил руну, и это было хорошо. После этого двигатель завёлся и наполнился силой…“&lt;br/&gt;—Властелин двигателей, 16-й том, стих 2001"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="&lt;string name='Traits/AdeptusMechanicus/BroadSpectrumDataTether'/&gt;"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Способность для Железоходов-баллистариев, Сидонских драгунов, Скорпиуса Дюноходца, Дюнного ползуна Онагр и Скорпиуса Дезинтегратора, которая снижает моральный урон соседних отрядов Адептус Механикус."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="&lt;string name='Traits/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/BulwarkImperative" value="&lt;string name='Actions/AdeptusMechanicus/BulwarkImperative'/&gt;"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="&lt;string name='Actions/AdeptusMechanicus/BulwarkImperativeDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="&lt;string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="&lt;string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/&gt;"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="&lt;string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="&lt;string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/CityTier2" value="&lt;string name='Traits/AdeptusMechanicus/CityTier2'/&gt;"/>
	<entry name="AdeptusMechanicus/CityTier2Description" value="&lt;string name='Traits/AdeptusMechanicus/CityTier2Description'/&gt;"/>
	<entry name="AdeptusMechanicus/CityTier2Flavor" value="&lt;string name='Traits/AdeptusMechanicus/CityTier2Flavor'/&gt;"/>
	<entry name="AdeptusMechanicus/CityTier3" value="&lt;string name='Traits/AdeptusMechanicus/CityTier3'/&gt;"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="&lt;string name='Traits/AdeptusMechanicus/CityTier3Description'/&gt;"/>
	<entry name="AdeptusMechanicus/CityTier3Flavor" value="&lt;string name='Traits/AdeptusMechanicus/CityTier3Flavor'/&gt;"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubber" value="&lt;string name='Weapons/CognisHeavyStubber'/&gt;"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberDescription" value="Дает Дюнному ползуну Онагр тяжёлый когнис-пулемёт."/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberFlavor" value="&lt;string name='Weapons/CognisHeavyStubberFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/CommandUplink" value="&lt;string name='Traits/AdeptusMechanicus/CommandUplink'/&gt;"/>
	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Способность для Археоптера Трансвектора и Археоптера Стратохищника, снижающая потерю морали соседних подразделений Адептус Механикус."/>
	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="&lt;string name='Traits/AdeptusMechanicus/CommandUplinkFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/ConquerorImperative" value="&lt;string name='Actions/AdeptusMechanicus/ConquerorImperative'/&gt;"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="&lt;string name='Actions/AdeptusMechanicus/ConquerorImperativeDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="&lt;string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/EnhancedDataTether" value="&lt;string name='Traits/AdeptusMechanicus/EnhancedDataTether'/&gt;"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Повышает мораль Авангарда Скитариев и Скитариев-егерей."/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="&lt;string name='Traits/AdeptusMechanicus/EnhancedDataTetherFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmour" value="Грайянские протоколы"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourDescription" value="Повышает броню пехоты."/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourFlavor" value="Интеграция протоколов Грайи в командные цепи Скитариев еще больше повышает их и без того впечатляющую живучесть. Мир-кузница Грайя известен своим отказом когда-либо отступать, учитывая, что их логика просто неопровержима."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmour" value="Агрипинова абляция"/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourDescription" value="Повышает броню техники."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourFlavor" value="Жизнь у Ока Ужаса после падения Кадии превратила Адептус Механикус на Агрипина в экспертов по защите, особенно когда дело доходит до того, чтобы их машины работали под огнем."/>
	<entry name="AdeptusMechanicus/FidorumVossPrime" value="&lt;string name='Traits/AdeptusMechanicus/FidorumVossPrime'/&gt;"/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Увеличивает влияние здания от каждого здания на соседней плитке."/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="&lt;string name='Traits/AdeptusMechanicus/FidorumVossPrimeFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="AdeptusMechanicus/HammerOfWrathDescription" value="Предоставляет Железоходам-баллистариям, Сидонским драгунам, Электрожрецам-фульгуритам, Дюнным ползунам Онагр, Птераксиям Стерилизорам и Рыцарю-крестоносцу возможность совершать более разрушительные атаки."/>
	<entry name="AdeptusMechanicus/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="&lt;string name='Traits/AdeptusMechanicus/HeavyWeaponBonus'/&gt;"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Увеличивает бронебойность тяжёлого оружия."/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="&lt;string name='Traits/AdeptusMechanicus/HeavyWeaponBonusFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="&lt;string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiah'/&gt;"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Повышает лояльность здания от каждого строения на соседней плитке."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="&lt;string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiahFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="&lt;string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/&gt;"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="&lt;string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="&lt;string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="&lt;string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/&gt;"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="&lt;string name='Actions/AdeptusMechanicus/InvocationOfMachineMightDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="&lt;string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancer" value="&lt;string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancer'/&gt;"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerDescription" value="&lt;string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerFlavor" value="&lt;string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/LucianSpecialisation" value="&lt;string name='Traits/AdeptusMechanicus/LucianSpecialisation'/&gt;"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="&lt;string name='Traits/AdeptusMechanicus/LucianSpecialisationDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="&lt;string name='Traits/AdeptusMechanicus/LucianSpecialisationFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="&lt;string name='Traits/AdeptusMechanicus/MeleeWeaponBonus'/&gt;"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="&lt;string name='Traits/AdeptusMechanicus/MeleeWeaponBonusFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/Omnispex" value="&lt;string name='Traits/AdeptusMechanicus/Omnispex'/&gt;"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="Позволяет Авангарду скитариев и Скитариям-егерям игнорировать сокращение дистанц. урона."/>
	<entry name="AdeptusMechanicus/OmnispexFlavor" value="&lt;string name='Traits/AdeptusMechanicus/OmnispexFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/OptateRestrictions" value="&lt;string name='Traits/AdeptusMechanicus/OptateRestrictions'/&gt;"/>
	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Увеличивает лимит населения от особенности Храм-хаб."/>
	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="&lt;string name='Traits/AdeptusMechanicus/OptateRestrictionsFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/ProtectorImperative" value="&lt;string name='Actions/AdeptusMechanicus/ProtectorImperative'/&gt;"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="&lt;string name='Actions/AdeptusMechanicus/ProtectorImperativeDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="&lt;string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="&lt;string name='Traits/AdeptusMechanicus/ReclaimatorProtocols'/&gt;"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Увеличивает темпы роста городов."/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="&lt;string name='Traits/AdeptusMechanicus/ReclaimatorProtocolsFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/Shroudpsalm" value="&lt;string name='Actions/AdeptusMechanicus/Shroudpsalm'/&gt;"/>
	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="&lt;string name='Actions/AdeptusMechanicus/ShroudpsalmDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="&lt;string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/&gt;"/>
 	<entry name="AdeptusMechanicus/SolarReflectors" value="&lt;string name='Traits/AdeptusMechanicus/SolarReflectors'/&gt;"/>
	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Увеличивает генерацию энергии здания от каждого строения на соседней плитке."/>
	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="&lt;string name='Traits/AdeptusMechanicus/SolarReflectorsFlavor'/&gt;"/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="&lt;string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeams'/&gt;"/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Увеличивает выработку еды в строении от каждого здания на соседней клетке."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="&lt;string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeamsFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/StygianEnlightenment" value="&lt;string name='Traits/AdeptusMechanicus/StygianEnlightenment'/&gt;"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="&lt;string name='Traits/AdeptusMechanicus/StygianEnlightenmentDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="&lt;string name='Traits/AdeptusMechanicus/StygianEnlightenmentFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/TerranGeneralism" value="&lt;string name='Traits/AdeptusMechanicus/TerranGeneralism'/&gt;"/>
	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="&lt;string name='Traits/AdeptusMechanicus/TerranGeneralismDescription'/&gt;"/>
	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="&lt;string name='Traits/AdeptusMechanicus/TerranGeneralismFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiency" value="Эффективный термообмен"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyDescription" value="Увеличивает бонус производства от Скачка напряжения."/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyFlavor" value="Как это ни парадоксально, замедление подачи в Термообменник и подача плазмы через второй набор тепловых муфт и конденсаторов может увеличить время, в течение которого он может работать, сверх номинальных значений—или, по крайней мере, сократить количество жизненных потерь и необходимый ремонт."/>
	<entry name="AdeptusMechanicus/TriplexNecessity" value="&lt;string name='Traits/AdeptusMechanicus/TriplexNecessity'/&gt;"/>
	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Увеличивает выработку строения добывающего руду от каждого здания на соседней плитке."/>
	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="&lt;string name='Traits/AdeptusMechanicus/TriplexNecessityFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannon" value="&lt;string name='Weapons/TwinLinkedIcarusAutocannon'/&gt;"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonDescription" value="Даёт Рыцарю-крестоносцу сдвоенную автопушку «Икар»."/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonFlavor" value="&lt;string name='Weapons/TwinLinkedIcarusAutocannonFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/VoicesInTheCode" value="&lt;string name='Actions/AdeptusMechanicus/VoicesInTheCode'/&gt;"/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Увеличивает потерю морали вражескими подразделениями находящимися рядом с Сикарианскими разведчиками."/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="&lt;string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/&gt;"/>
	<entry name="AdeptusMechanicus/XenariteAcceptance" value="Принятие ксенарита"/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceDescription" value="Увеличивает фиксированное количество исследований, получаемых с руин Ваула."/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceFlavor" value="Похоже, Ксенаритские техножрецы со Стигии VIII добиваются успеха на Гладиус Прайм—устанавливая на каждой заставе наблюдательные подразделения и дроны, чтобы лучше понять ксеносов. Практическая польза с точки зрения исследований сразу становится очевидной…"/>
	<entry name="AstraMilitarum/AdditionalHeavyBolters" value="Дополнительный Тяжелый Болтер"/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersDescription" value="Позволяет установить на Гибельный клинок, боевой танк Леман Русс, боевой танк Рогал Дорн и Валькирию дополнительные Тяжелые болтеры."/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersFlavor" value="&lt;string name='Weapons/HeavyBolterFlavor'/&gt;"/>
	<entry name="AstraMilitarum/AwakenTheMachine" value="&lt;string name='Actions/AwakenTheMachine'/&gt;"/>
	<entry name="AstraMilitarum/AwakenTheMachineDescription" value="Позволяет Техножрецу-Машиновидцу увеличить урон наносимый техникой."/>
	<entry name="AstraMilitarum/AwakenTheMachineFlavor" value="&lt;string name='Actions/AwakenTheMachineFlavor'/&gt;"/>
	<entry name="AstraMilitarum/BanebladeLascannon" value="Лазпушка для Гибельного клинка"/>
	<entry name="AstraMilitarum/BanebladeLascannonDescription" value="Позволяет оснастить дополнительными Лазпушками танк Гибельный клинок."/>
	<entry name="AstraMilitarum/BanebladeLascannonFlavor" value="&lt;string name='Weapons/LascannonFlavor'/&gt;"/>
	<entry name="AstraMilitarum/BlastDamage" value="&lt;string name='Traits/AstraMilitarum/BlastDamage'/&gt;"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Увеличивает бронебойность гранат, ракет и разрывного вооружения."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="&lt;string name='Traits/AstraMilitarum/BlastDamageFlavor'/&gt;"/>
	<entry name="AstraMilitarum/BoltDamage" value="&lt;string name='Traits/AstraMilitarum/BoltDamage'/&gt;"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Повышает бронебойность болтерного оружия."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="&lt;string name='Traits/AstraMilitarum/BoltDamageFlavor'/&gt;"/>
	<entry name="AstraMilitarum/BruteShield" value="&lt;string name='Traits/BruteShield'/&gt;"/>
	<entry name="AstraMilitarum/BruteShieldDescription" value="Повышает урон и сокращение урона быкринам."/>
	<entry name="AstraMilitarum/BruteShieldFlavor" value="&lt;string name='Traits/BruteShieldFlavor'/&gt;"/>
	<entry name="AstraMilitarum/CamoNetting" value="&lt;string name='Traits/CamoNetting'/&gt;"/>
	<entry name="AstraMilitarum/CamoNettingDescription" value="Повышает сокращение дистанц. урона наземной технике."/>
	<entry name="AstraMilitarum/CamoNettingFlavor" value="&lt;string name='Traits/CamoNettingFlavor'/&gt;"/>
	<entry name="AstraMilitarum/ChaffLauncher" value="Запуск обманки"/>
	<entry name="AstraMilitarum/ChaffLauncherDescription" value="Даёт Удару молнии и бомбардировщику Мародер возможность запускать обманки, которые повышают сокращение дистанц. урона."/>
	<entry name="AstraMilitarum/ChaffLauncherFlavor" value="&lt;string name='Actions/DispenseChaffFlavor'/&gt;"/>
	<entry name="AstraMilitarum/CityTier2" value="&lt;string name='Traits/AstraMilitarum/CityTier2'/&gt;"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Повышает радиус присоединения плиток к городам."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="&lt;string name='Traits/AstraMilitarum/CityTier2Flavor'/&gt;"/>
	<entry name="AstraMilitarum/CityTier3" value="&lt;string name='Traits/AstraMilitarum/CityTier3'/&gt;"/>
	<entry name="AstraMilitarum/CityTier3Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="&lt;string name='Traits/AstraMilitarum/CityTier3Flavor'/&gt;"/>
	<entry name="AstraMilitarum/DozerBlade" value="&lt;string name='Traits/DozerBlade'/&gt;"/>
	<entry name="AstraMilitarum/DozerBladeDescription" value="Уменьшает танковым подразделениям штраф за движение по лесам и имперским руинам."/>
	<entry name="AstraMilitarum/DozerBladeFlavor" value="&lt;string name='Traits/DozerBladeFlavor'/&gt;"/>
	<entry name="AstraMilitarum/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="AstraMilitarum/ExtraInfantryArmourDescription" value="Увеличивает броню пехоты."/>
	<entry name="AstraMilitarum/ExtraInfantryArmourFlavor" value="Большинство пехоты в 41-м тысячелетии оснащены флак броней или её эквивалентом. Если командир хочет, чтобы его бойцы имели хоть немного шансов на выживание, он снабдит их чем-нибудь похожем на броню из армапласта Астра Милитарум."/>
	<entry name="AstraMilitarum/ExtraVehicleArmour" value="&lt;string name='Traits/ExtraVehicleArmour'/&gt;"/>
	<entry name="AstraMilitarum/ExtraVehicleArmourDescription" value="Увеличивает броню техники."/>
	<entry name="AstraMilitarum/ExtraVehicleArmourFlavor" value="Добавление дополнительных пластин брони на танки для техножрецов является высшей ересью, но для солдат Астра Милитарум это скорее норма. Поглощающее покрытие или специальная броня для защиты от конкретного оружия не является чем-то неслыханным."/>
	<entry name="AstraMilitarum/FragGrenade" value="&lt;string name='Weapons/FragGrenade'/&gt;"/>
	<entry name="AstraMilitarum/FragGrenadeDescription" value="Даёт Быкринам, Полевой артиллерии, Гвардейцам, Отделению тяжелого оружия, Лордам-комиссарам, Псайкерам-примарисам, Техножрецам машиновидцам и Отпрыскам Темпестус возможность бросать противопехотные гранаты."/>
	<entry name="AstraMilitarum/FragGrenadeFlavor" value="&lt;string name='Weapons/FragGrenadeFlavor'/&gt;"/>
	<entry name="AstraMilitarum/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="AstraMilitarum/HammerOfWrathDescription" value="Даёт Разведывательному Часовому и Быкринам возможность выполнения более разрушительных атак."/>
	<entry name="AstraMilitarum/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="AstraMilitarum/HunterKillerMissile" value="&lt;string name='Weapons/HunterKillerMissile'/&gt;"/>
	<entry name="AstraMilitarum/HunterKillerMissileDescription" value="Даёт наземной технике возможность стрельбы ракетами Охотник-убийца."/>
	<entry name="AstraMilitarum/HunterKillerMissileFlavor" value="&lt;string name='Weapons/HunterKillerMissileFlavor'/&gt;"/>
	<entry name="AstraMilitarum/ImperialSplendour" value="&lt;string name='Traits/ImperialSplendour'/&gt;"/>
	<entry name="AstraMilitarum/ImperialSplendourDescription" value="Увеличивает влияние в городе Астра Милитарум."/>
	<entry name="AstraMilitarum/ImperialSplendourFlavor" value="&lt;string name='Traits/ImperialSplendourFlavor'/&gt;"/>
	<entry name="AstraMilitarum/ImperialStrongpoint" value="&lt;string name='Units/AstraMilitarum/ImperialStrongpoint'/&gt;"/>
	<entry name="AstraMilitarum/ImperialStrongpointDescription" value="Даёт Техножрецам-Машиновидцам возможность построить укрепления с Тяжелыми болтерами."/>
	<entry name="AstraMilitarum/ImperialStrongpointFlavor" value="&lt;string name='Units/AstraMilitarum/ImperialStrongpointFlavor'/&gt;"/>
	<entry name="AstraMilitarum/KrakGrenade" value="&lt;string name='Weapons/KrakGrenade'/&gt;"/>
	<entry name="AstraMilitarum/KrakGrenadeDescription" value="Даёт Полевой артиллерии, Гвардейцам, Отделению тяжелого оружия, Лордам-комиссарам, Техножрецам машиновидцам и Отпрыскам Темпестус возможность бросать противотанковые гранаты."/>
	<entry name="AstraMilitarum/KrakGrenadeFlavor" value="&lt;string name='Weapons/KrakGrenadeFlavor'/&gt;"/>
	<entry name="AstraMilitarum/LasDamage" value="&lt;string name='Traits/AstraMilitarum/LasDamage'/&gt;"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Увеличивает бронебойность лазерного оружия."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="&lt;string name='Traits/AstraMilitarum/LasDamageFlavor'/&gt;"/>
	<entry name="AstraMilitarum/MediPack" value="&lt;string name='Actions/MediPack'/&gt;"/>
	<entry name="AstraMilitarum/MediPackDescription" value="Даёт Гвардейцам и Отпрыскам Темпестус способность исцелять себя во время боя."/>
	<entry name="AstraMilitarum/MediPackFlavor" value="&lt;string name='Actions/MediPackFlavor'/&gt;"/>
	<entry name="AstraMilitarum/Misfortune" value="&lt;string name='Actions/Misfortune'/&gt;"/>
	<entry name="AstraMilitarum/MisfortuneDescription" value="Псайкер-вюрдоплетам предоставляется возможность насылать проклятие на вражеские подразделения, чтобы они несли повышенный урон."/>
	<entry name="AstraMilitarum/MisfortuneFlavor" value="&lt;string name='Actions/MisfortuneFlavor'/&gt;"/>
	<entry name="AstraMilitarum/RecoveryGear" value="&lt;string name='Traits/RecoveryGear'/&gt;"/>
	<entry name="AstraMilitarum/RecoveryGearDescription" value="Увеличивает скорость ремонта для наземной техники."/>
	<entry name="AstraMilitarum/RecoveryGearFlavor" value="&lt;string name='Traits/RecoveryGearFlavor'/&gt;"/>
	<entry name="AstraMilitarum/RelicPlating" value="&lt;string name='Traits/RelicPlating'/&gt;"/>
	<entry name="AstraMilitarum/RelicPlatingDescription" value="Повышает сокращение урона от колдовского огня наземной технике."/>
	<entry name="AstraMilitarum/RelicPlatingFlavor" value="&lt;string name='Traits/RelicPlatingFlavor'/&gt;"/>
	<entry name="AstraMilitarum/SkystrikeMissile" value="&lt;string name='Weapons/SkystrikeMissile'/&gt;"/>
	<entry name="AstraMilitarum/SkystrikeMissileDescription" value="Даёт Удару молнии возможность стрелять ракетами по воздушным целям."/>
	<entry name="AstraMilitarum/SkystrikeMissileFlavor" value="&lt;string name='Weapons/SkystrikeMissileFlavor'/&gt;"/>
	<entry name="AstraMilitarum/SmokeLauncher" value="&lt;string name='Upgrades/SmokeLauncher'/&gt;"/>
	<entry name="AstraMilitarum/SmokeLauncherDescription" value="&lt;string name='Upgrades/SmokeLauncherDescription'/&gt;"/>
	<entry name="AstraMilitarum/SmokeLauncherFlavor" value="&lt;string name='Upgrades/SmokeLauncherFlavor'/&gt;"/>
	<entry name="AstraMilitarum/TrainedSentinelPilots" value="&lt;string name='Traits/TrainedSentinelPilots'/&gt;"/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsDescription" value="Увеличивает урон от разведчиков Часовых."/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsFlavor" value="&lt;string name='Traits/TrainedSentinelPilotsFlavor'/&gt;"/>
	<entry name="AstraMilitarum/VoidShieldGenerator" value="&lt;string name='Units/AstraMilitarum/VoidShieldGenerator'/&gt;"/>
	<entry name="AstraMilitarum/VoidShieldGeneratorDescription" value="Даёт Техножрецам-машиновидцам возможность создавать генераторы щита, которые дают сокращение дистанц. урона подразделениям в радиусе действия."/>
	<entry name="AstraMilitarum/VoidShieldGeneratorFlavor" value="&lt;string name='Units/AstraMilitarum/VoidShieldGeneratorFlavor'/&gt;"/>
	<entry name="AstraMilitarum/VoxCaster" value="&lt;string name='Traits/VoxCaster'/&gt;"/>
	<entry name="AstraMilitarum/VoxCasterDescription" value="Уменьшает потерю морали Гвардейцев и Отпрысков Темпестус."/>
	<entry name="AstraMilitarum/VoxCasterFlavor" value="&lt;string name='Traits/VoxCasterFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="&lt;string name='Traits/ChaosSpaceMarines/ArcaneOcculum'/&gt;"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Предоставляет Чемпионам Хаоса шанс получить постоянное повышение точности после убийства врага (Благословение Хаоса)."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/ArcaneOcculumFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/BlastDamage" value="&lt;string name='Traits/ChaosSpaceMarines/BlastDamage'/&gt;"/>
	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Увеличивает бронебойность гранат, ракет и разрывного вооружения."/>
	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/BlastDamageFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Bloated" value="&lt;string name='Traits/ChaosSpaceMarines/Bloated'/&gt;"/>
	<entry name="ChaosSpaceMarines/BloatedDescription" value="Предоставляет Чемпионам Хаоса шанс восстановить свои Очки здоровья при убийстве врага."/>
	<entry name="ChaosSpaceMarines/BloatedFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/BloatedFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/BoltDamage" value="&lt;string name='Traits/ChaosSpaceMarines/BoltDamage'/&gt;"/>
	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Повышает бронебойность болтерного оружия."/>
	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/BoltDamageFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/ChaosRising" value="Восхождение в Хаос"/>
	<entry name="ChaosSpaceMarines/ChaosRisingDescription" value="Снижает стоимость основания новых городов."/>
	<entry name="ChaosSpaceMarines/ChaosRisingFlavor" value="Имперское население Гладиуса мало что знает о Хаосе, но после ада вторжения Ксеносов их вера в далекого Императора была поколеблена. Вашим культистам и поборникам легко повернуть их разум к вашим Богам и к их погибели."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="&lt;string name='Traits/ChaosSpaceMarines/CityTier2'/&gt;"/>
	<entry name="ChaosSpaceMarines/CityTier2Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="&lt;string name='Traits/ChaosSpaceMarines/CityTier2Flavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/CityTier3" value="&lt;string name='Traits/ChaosSpaceMarines/CityTier3'/&gt;"/>
	<entry name="ChaosSpaceMarines/CityTier3Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="&lt;string name='Traits/ChaosSpaceMarines/CityTier3Flavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/CrystallineBody" value="&lt;string name='Traits/ChaosSpaceMarines/CrystallineBody'/&gt;"/>
	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Предоставляет Чемпионам Хаоса шанс получить постоянное увеличение Очков здоровья при убийстве врага (Благословение Хаоса)."/>
	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/CrystallineBodyFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/DirgeCaster" value="&lt;string name='Actions/ChaosSpaceMarines/DirgeCaster'/&gt;"/>
	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Даёт Носорогам Хаоса, Осквернителям и Лендрейдерам Хаоса способность предотвращать атаки из дозора."/>
	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourDescription" value="Увеличивает броню пехоты."/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourFlavor" value="Большинство пехоты в 41-м тысячелетии оснащены бронежилетами или их эквивалентом. Если командир хочет, чтобы его войска имели более лучший шанс на выживание, он снабдит их чем-то более близким к броневому панцирю, созданному из армпласта Астра Милитарум."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmour" value="&lt;string name='Traits/ExtraVehicleArmour'/&gt;"/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourDescription" value="Увеличивает броню машин."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourFlavor" value="Добавление дополнительных броневых пластин на танки может быть высшей степенью ереси для Техножрецов, но для солдат Астра Милитарум это является нормой. Поглощающее покрытие или специальная броня для защиты от определенного оружия не является чем-то неслыханным."/>
	<entry name="ChaosSpaceMarines/FragGrenade" value="&lt;string name='Weapons/FragGrenade'/&gt;"/>
	<entry name="ChaosSpaceMarines/FragGrenadeDescription" value="Предоставляет Лорду Хаоса, Космодесантникам Хаоса, Берсеркам Кхорна, Разорителям, Магистру Одержимости и Варповым кузнецам возможность бросать противопехотные гранаты."/>
	<entry name="ChaosSpaceMarines/FragGrenadeFlavor" value="&lt;string name='Weapons/FragGrenadeFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/GiftOfMutation" value="&lt;string name='Traits/ChaosSpaceMarines/GiftOfMutation'/&gt;"/>
	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="Предоставляет Лорду Хаоса, Космическим Десантникам Хаоса, Терминаторам Хаоса, Разорителям, Берсеркам Кхорна, Магистру Одержимости, Варповым когтям и Варп-кузнецу одно случайное, изученное Благословение Хаоса."/>
	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/GiftOfMutationFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="ChaosSpaceMarines/HammerOfWrathDescription" value="Дает Князь-Демонам, Осквернителям, Стальным извергам, Большим медным скорпионам, Хельбрутам, Изверг-истязателям, Злобным ползунам и Варповым когтям возможность совершать более разрушительные атаки."/>
	<entry name="ChaosSpaceMarines/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/HavocLauncher" value="&lt;string name='Weapons/HavocLauncher'/&gt;"/>
	<entry name="ChaosSpaceMarines/HavocLauncherDescription" value="Предоставляет Носорогам Хаоса и Лендрейдерам Хаоса оружие средней дальности."/>
	<entry name="ChaosSpaceMarines/HavocLauncherFlavor" value="&lt;string name='Weapons/HavocLauncherFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfDespair" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfDespair'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfDespairDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfExcess" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfExcess'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfExcessDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfFlame" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfFlame'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfFlameDescription" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfFlameDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfFlameFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfFlameFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfVengeance" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfVengeance'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceDescription" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfVengeanceDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfVengeanceFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfWrath" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfWrath'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfWrathDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/KrakGrenade" value="&lt;string name='Weapons/KrakGrenade'/&gt;"/>
	<entry name="ChaosSpaceMarines/KrakGrenadeDescription" value="Предоставляет Лордам Хаоса, Космическим Десантникам Хаоса, Разорителям, Берсеркам Кхорна Магистрам Одержимости и Варп-кузнецам возможность бросать противотанковые гранаты."/>
	<entry name="ChaosSpaceMarines/KrakGrenadeFlavor" value="&lt;string name='Weapons/KrakGrenadeFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/LasDamage" value="&lt;string name='Traits/ChaosSpaceMarines/LasDamage'/&gt;"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Увеличивает бронебойность лазерного и плазменного оружия."/>
	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/LasDamageFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfKhorneDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfNurgleDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfTzeentchDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/Mechanoid" value="&lt;string name='Traits/ChaosSpaceMarines/Mechanoid'/&gt;"/>
	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Предоставляет Чемпионом Хаоса шанс получить постоянное повышение уровня брони при убийстве врага (Благословение Хаоса)."/>
	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/MechanoidFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/MeleeDamage" value="&lt;string name='Traits/ChaosSpaceMarines/MeleeDamage'/&gt;"/>
	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/MeleeDamageFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/MeltaBomb" value="&lt;string name='Weapons/MeltaBomb'/&gt;"/>
	<entry name="ChaosSpaceMarines/MeltaBombDescription" value="Предоставляет Лордам Хаоса, Космическим Десантникам Хаоса, Разорителям и Берсеркам Кхорна возможность применения мелта-бомбы, которые очень эффективны против тяжелой техники и укреплений."/>
	<entry name="ChaosSpaceMarines/MeltaBombFlavor" value="&lt;string name='Weapons/MeltaBombFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGods" value="Наша жизнь для Богов"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsDescription" value="Увеличивает скорость роста населения от жертвоприношения культистов."/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsFlavor" value="Так много людей сейчас лежат мертвыми на лезвиях культистов на Гладиусе, что каждая смерть кажется бессмысленной, пустой, мелкой. Но это увеличивает эффект от их жертвы, поскольку они притягивают этот мир ближе к падшему измерению ада Темных Богов, и темным благословениям становится проще преодолеть барьер с Имматериумом."/>
	<entry name="ChaosSpaceMarines/RiteOfBlood" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfBlood'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfBloodDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfChange" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfChange'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfChangeDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfPestilenceDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureDescription'/&gt;"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="&lt;string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/SmokeLauncher" value="&lt;string name='Upgrades/SmokeLauncher'/&gt;"/>
	<entry name="ChaosSpaceMarines/SmokeLauncherDescription" value="Даёт Носорогам Хаоса, Осквернителям и Лендрейдерам Хаоса возможность ставить дымовую завесу, которая повышает сокращение дистанц. урона."/>
	<entry name="ChaosSpaceMarines/SmokeLauncherFlavor" value="&lt;string name='Upgrades/SmokeLauncherFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/TemporalDistortion" value="&lt;string name='Traits/ChaosSpaceMarines/TemporalDistortion'/&gt;"/>
	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Предоставляет Чемпионам Хаоса шанс получить постоянное увеличение движения при убийстве врага (Благословение Хаоса)."/>
	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/TemporalDistortionFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="&lt;string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWar'/&gt;"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Уменьшает потерю морали и увеличивает точность пехоты в ближнем бою против подразделений фракции Космических Десантников."/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWarFlavor'/&gt;"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoyles" value="Варп-пламенная горгулья"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesDescription" value="Позволяет через раз наносить урон оружием Носорога Хаоса, Осквернителя и Лендрейдера Хаоса."/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesFlavor" value="Жерла орудий на этой машине мерцают неестественным огнем."/>
	<entry name="ChaosSpaceMarines/WarpFrenzy" value="&lt;string name='Traits/ChaosSpaceMarines/WarpFrenzy'/&gt;"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Предоставляет Чемпионам шанс получить постоянное увеличение силы атаки при убийстве врага (Благословение Хаоса)."/>
	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="&lt;string name='Traits/ChaosSpaceMarines/WarpFrenzyFlavor'/&gt;"/>
	<entry name="CompendiumFlavor" value="Каждая фракция расширяет городах по разному. Иногда, Варбосс Орков приказывает Парням поднять его в буквальном смысле слова и перенести его дальше за защитные стены, или, возможно, Лорд Некронов приказывает своим рабам раскапывать их древние гробницы от поверхности планеты еще дальше. Тем не менее, все делают то, что необходимо для того, чтобы освободить место для новых зданий, которые расширяют возможности их фракции."/>
	<entry name="Drukhari/AssaultWeaponBonus" value="&lt;string name='Traits/Drukhari/AssaultWeaponBonus'/&gt;"/>
	<entry name="Drukhari/AssaultWeaponBonusDescription" value="&lt;string name='Upgrades/Eldar/AssaultWeaponBonusDescription'/&gt;"/>
	<entry name="Drukhari/AssaultWeaponBonusFlavor" value="&lt;string name='Traits/Drukhari/AssaultWeaponBonusFlavor'/&gt;"/>
	<entry name="Drukhari/BetrayalCultureUpgrade" value="Целенаправленные убийства"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="Увеличивает лояльность городов друкари."/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="Смерть естественна на улицах города друкари, где за каждым углом можно увидеть горку трупов, ждущих, пока ур-гули не утащат их прочь… но смерти драконтов и дворян кабала редки. Или, по крайней мере, были редки, пока инкубы архонта не раскрыли очередной заговор. Теперь в случае даже малейшего проблеска нелояльности тело очередного дворянина окажется на пике над покоями архонта…"/>
	<entry name="Drukhari/BonusResources" value="&lt;string name='Actions/Drukhari/BonusResources'/&gt;"/>
	<entry name="Drukhari/BonusResourcesDescription" value="&lt;string name='Actions/Drukhari/BonusResourcesDescription'/&gt;"/>
	<entry name="Drukhari/BonusResourcesFlavor" value="&lt;string name='Actions/Drukhari/BonusResourcesFlavor'/&gt;"/>
	<entry name="Drukhari/CityTier2" value="&lt;string name='Traits/Drukhari/CityTier2'/&gt;"/>
	<entry name="Drukhari/CityTier2Description" value="&lt;string name='Traits/Drukhari/CityTier2Description'/&gt;"/>
	<entry name="Drukhari/CityTier2Flavor" value="&lt;string name='Traits/Drukhari/CityTier2Flavor'/&gt;"/>
	<entry name="Drukhari/CityTier3" value="&lt;string name='Traits/Drukhari/CityTier3'/&gt;"/>
	<entry name="Drukhari/CityTier3Description" value="&lt;string name='Traits/Drukhari/CityTier3Description'/&gt;"/>
	<entry name="Drukhari/CityTier3Flavor" value="&lt;string name='Traits/Drukhari/CityTier3Flavor'/&gt;"/>
	<entry name="Drukhari/CombatDrugsUpgrade" value="Холодный торговец культа"/>
	<entry name="Drukhari/CombatDrugsUpgradeDescription" value="Даёт всей пехоте друкари способность применять боевые препараты."/>
	<entry name="Drukhari/CombatDrugsUpgradeFlavor" value="Хотя использование боевых препаратов широко распространено по всему обществу друкари, их с наибольшей охотой применяют в культах ведьм, несмотря на вред для физиологии и продолжительности жизни. Наличие связи с культом ведьм и доступа к их несметным запасам ужасающих веществ — серьёзное преимущество для кабала, занимающегося налётами на реальное пространство."/>
	<entry name="Drukhari/CorsairOutpostsUpgrade" value="Паутинная лихорадка"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="Увеличивает базовый рост населения в городах друкари."/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="Слухи, как чума, овладели Комморрой: о богатствах Гладиус Прайм, о миллиарде страдающих людей, отрезанных от Империума варп-штормом, о странном планетарном ядре из призрачной кости, полном падших эльдарских душ… правдивы эти россказни или нет, но друкари стекаются сюда. Но кто начал плести эти басни?.."/>
	<entry name="Drukhari/EnergyBuildingBonus" value="&lt;string name='Traits/Drukhari/EnergyBuildingBonus'/&gt;"/>
	<entry name="Drukhari/EnergyBuildingBonusDescription" value="Повышает эффективность зданий, производящих энергию."/>
	<entry name="Drukhari/EnergyBuildingBonusFlavor" value="&lt;string name='Traits/Drukhari/EnergyBuildingBonusFlavor'/&gt;"/>
	<entry name="Drukhari/EnhancedAethersails" value="&lt;string name='Actions/Drukhari/EnhancedAethersails'/&gt;"/>
	<entry name="Drukhari/EnhancedAethersailsDescription" value="Даёт «Рейдеру», «Губителю» и «Танталу» способность повышать своё движение."/>
	<entry name="Drukhari/EnhancedAethersailsFlavor" value="&lt;string name='Actions/Drukhari/EnhancedAethersailsFlavor'/&gt;"/>
	<entry name="Drukhari/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="Drukhari/ExtraInfantryArmourDescription" value="Повышает броню пехоты."/>
	<entry name="Drukhari/ExtraInfantryArmourFlavor" value="Снарядить в призрачную броню целую армию было бы слишком разорительно. Такой щедрости архонта достойны лишь некоторые избранные. Созданная из странных смолистых материалов и пронизанная ячейками с газом легче воздуха, призрачная броня обеспечивает впечатляющую защиту и при этом почти невесома."/>
	<entry name="Drukhari/ExtraVehicleArmour" value="&lt;string name='Traits/ExtraVehicleArmour'/&gt;"/>
	<entry name="Drukhari/ExtraVehicleArmourDescription" value="&lt;string name='Traits/ExtraVehicleArmourDescription'/&gt;"/>
	<entry name="Drukhari/ExtraVehicleArmourFlavor" value="Транспортные средства друкари, снабжённые мерцающими полями или щитами ночи, тяжело обнаружить и ещё тяжелее поразить. Наделить наиболее ценные машины бронёй из призрачных пластин недёшево, но это снижает их массу и увеличивает скорость, ещё сильнее повышая их шанс избежать попадания."/>
	<entry name="Drukhari/FieldRepairs" value="&lt;string name='Actions/Drukhari/FieldRepairs'/&gt;"/>
	<entry name="Drukhari/FieldRepairsDescription" value="Наделяет всю технику друкари способностью восстанавливать очки здоровья."/>
	<entry name="Drukhari/FieldRepairsFlavor" value="&lt;string name='Actions/Drukhari/FieldRepairsFlavor'/&gt;"/>
	<entry name="Drukhari/GraveLotus" value="Могильный лотос"/>
	<entry name="Drukhari/GraveLotusDescription" value="Боевые препараты дают дополнительный урон в ближнем бою."/>
	<entry name="Drukhari/GraveLotusFlavor" value="В Саду Дьявола из россыпей трупов вырастают зловонные висячие сады могильного лотоса. Это ярко-пурпурный гриб, который заимствует угасающую сущность недавно умерших, питая свой рост. Культы ведьм, в свою очередь, заимствуют результат, употребляя лотос в жидком виде для увеличения собственной физической силы."/>
	<entry name="Drukhari/GrislyTrophies" value="&lt;string name='Actions/Drukhari/GrislyTrophies'/&gt;"/>
	<entry name="Drukhari/GrislyTrophiesDescription" value="Даёт «Яду», «Рейдеру», «Губителю» и «Танталу» ауру, которая снижает потери морали соседних союзных отрядов друкари."/>
	<entry name="Drukhari/GrislyTrophiesFlavor" value="&lt;string name='Actions/Drukhari/GrislyTrophiesFlavor'/&gt;"/>
	<entry name="Drukhari/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="Drukhari/HammerOfWrathDescription" value="Даёт «Кроносам», гелионам, разбойникам, бичевателям и «Талосу» возможность выполнения более разрушительных атак."/>
	<entry name="Drukhari/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="Drukhari/HaywireGrenade" value="&lt;string name='Weapons/HaywireGrenade'/&gt;"/>
	<entry name="Drukhari/HaywireGrenadeDescription" value="Дает воинам-кабалитам, суккубу и ведьмам возможность метать противотанковые гранаты."/>
	<entry name="Drukhari/HaywireGrenadeFlavor" value="&lt;string name='Weapons/HaywireGrenadeFlavor'/&gt;"/>
	<entry name="Drukhari/HeavyWeaponBonus" value="&lt;string name='Traits/Drukhari/HeavyWeaponBonus'/&gt;"/>
	<entry name="Drukhari/HeavyWeaponBonusDescription" value="Увеличивает бронебойность тяжёлого оружия."/>
	<entry name="Drukhari/HeavyWeaponBonusFlavor" value="&lt;string name='Traits/Drukhari/HeavyWeaponBonusFlavor'/&gt;"/>
	<entry name="Drukhari/Hypex" value="Гипекс"/>
	<entry name="Drukhari/HypexDescription" value="Боевые препараты дают проход сквозь укрытия."/>
	<entry name="Drukhari/HypexFlavor" value="Поимка психнойена — опасная задача, но тот, кому это удалось, может продать это насекомое культам ведьм по весьма достойной цене. Средство «Гипекс», получаемое при переработке мозговых жидкостей этого существа, доводит и без того высокую скорость реакции друкари до невиданных уровней."/>
	<entry name="Drukhari/MeleeWeaponBonus" value="&lt;string name='Traits/Drukhari/MeleeWeaponBonus'/&gt;"/>
	<entry name="Drukhari/MeleeWeaponBonusDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="Drukhari/MeleeWeaponBonusFlavor" value="&lt;string name='Traits/Drukhari/MeleeWeaponBonusFlavor'/&gt;"/>
	<entry name="Drukhari/NightShields" value="&lt;string name='Traits/Drukhari/NightShields'/&gt;"/>
	<entry name="Drukhari/NightShieldsDescription" value="Повышает «Рейдерам», «Губителям», истребителям «Острокрыл», «Танталам» и бомбардировщикам «Пустотный ворон» сокращение дистанц. урона."/>
	<entry name="Drukhari/NightShieldsFlavor" value="&lt;string name='Traits/Drukhari/NightShieldsFlavor'/&gt;"/>
	<entry name="Drukhari/Painbringer" value="Болеприноситель"/>
	<entry name="Drukhari/PainbringerDescription" value="Боевые препараты дают сокращение урона нечувствительностью к боли."/>
	<entry name="Drukhari/PainbringerFlavor" value="Только изгнанный герцог Слискус может претендовать на постоянный запас «Болеприносителя». Это один из редчайших укрепляющих эликсиров, он превращает кожу употребившего его в гибкую оболочку, столь же упругую, как выделанная кожа. Хотя этот процесс чрезвычайно мучителен, его сторонники считают боль незначительной платой."/>
	<entry name="Drukhari/PlasmaGrenade" value="&lt;string name='Weapons/PlasmaGrenade'/&gt;"/>
	<entry name="Drukhari/PlasmaGrenadeDescription" value="Дает архонту, бичевателям, суккубу и ведьмам возможность метать противотанковые гранаты."/>
	<entry name="Drukhari/PlasmaGrenadeFlavor" value="&lt;string name='Weapons/PlasmaGrenadeFlavor'/&gt;"/>
	<entry name="Drukhari/RaiderFortress" value="&lt;string name='Actions/Drukhari/RaiderFortress'/&gt;"/>
	<entry name="Drukhari/RaiderFortressDescription" value="Предоставляет возможность основывать новые города на контролируемых Вратах Паутины."/>
	<entry name="Drukhari/RaiderFortressFlavor" value="&lt;string name='Actions/Drukhari/RaiderFortressFlavor'/&gt;"/>
	<entry name="Drukhari/RaidersTacticsDamage" value="&lt;string name='Traits/Drukhari/RaidersTacticsDamage'/&gt;"/>
	<entry name="Drukhari/RaidersTacticsDamageDescription" value="Увеличивает урон пехоты друкари, когда она высаживается из транспорта."/>
	<entry name="Drukhari/RaidersTacticsDamageFlavor" value="&lt;string name='Traits/Drukhari/RaidersTacticsDamageFlavor'/&gt;"/>
	<entry name="Drukhari/RaidersTacticsDamageReduction" value="&lt;string name='Traits/Drukhari/RaidersTacticsDamageReduction'/&gt;"/>
	<entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Повышает сокращение урона пехоте друкари, когда та высаживается из транспорта."/>
	<entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="&lt;string name='Traits/Drukhari/RaidersTacticsDamageReductionFlavor'/&gt;"/>
	<entry name="Drukhari/RaidersTacticsHealingRate" value="&lt;string name='Traits/Drukhari/RaidersTacticsHealingRate'/&gt;"/>
	<entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Увеличивает скорость восстановления пехоты в транспорте."/>
	<entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="&lt;string name='Traits/Drukhari/RaidersTacticsHealingRateFlavor'/&gt;"/>
	<entry name="Drukhari/SacrificeToKhaine" value="&lt;string name='Traits/Drukhari/SacrificeToKhaine'/&gt;"/>
	<entry name="Drukhari/SacrificeToKhaineDescription" value="&lt;string name='Actions/Drukhari/SacrificeToKhaineDescription'/&gt;"/>
	<entry name="Drukhari/SacrificeToKhaineFlavor" value="&lt;string name='Traits/Drukhari/SacrificeToKhaineFlavor'/&gt;"/>
	<entry name="Drukhari/ShroudGate" value="&lt;string name='Traits/Drukhari/ShroudGate'/&gt;"/>
	<entry name="Drukhari/ShroudGateDescription" value="Повышает сокращение дистанц. урона подразделениям, проходящим через Врата Паутины или портал Паутины."/>
	<entry name="Drukhari/ShroudGateFlavor" value="&lt;string name='Traits/Drukhari/ShroudGateFlavor'/&gt;"/>
	<entry name="Drukhari/SoulHungerCost" value="Средоточие души"/>
	<entry name="Drukhari/SoulHungerCostDescription" value="Снижает стоимость способностей Голода душ."/>
	<entry name="Drukhari/SoulHungerCostFlavor" value="По мере роста интенсивности набегов друкари на реальное пространство множатся и связи между этим миром и Комморрой. Даже в случае разрыва физического соединения кабал всё ещё может черпать силу из этих связей и прокачивать через неё украденную жизнь."/>
	<entry name="Drukhari/SoulHungerLoyalty" value="&lt;string name='Traits/Drukhari/SoulHungerLoyalty'/&gt;"/>
	<entry name="Drukhari/SoulHungerLoyaltyDescription" value="&lt;string name='Actions/Drukhari/SoulHungerLoyaltyDescription'/&gt;"/>
	<entry name="Drukhari/SoulHungerLoyaltyFlavor" value="&lt;string name='Traits/Drukhari/SoulHungerLoyaltyFlavor'/&gt;"/>
	<entry name="Drukhari/SoulHungerOutposts" value="&lt;string name='Traits/Drukhari/SoulHungerOutposts'/&gt;"/>
	<entry name="Drukhari/SoulHungerOutpostsDescription" value="&lt;string name='Actions/Drukhari/SoulHungerOutpostsDescription'/&gt;"/>
	<entry name="Drukhari/SoulHungerOutpostsFlavor" value="&lt;string name='Traits/Drukhari/SoulHungerOutpostsFlavor'/&gt;"/>
	<entry name="Drukhari/SoulHungerUpgrade" value="Садисты душ"/>
	<entry name="Drukhari/SoulHungerUpgradeDescription" value="Увеличивает влияние, получаемое подразделениями друкари при убийстве врага."/>
	<entry name="Drukhari/SoulHungerUpgradeFlavor" value="“Накачайте их веществами. Убейте охранников. Откройте врата. Осторожно разбудите их. Выведите их на свободу. Дайте им надежду на спасение. Начните настоящую погоню. Инсценируйте свою смерть. Покажите им выход. А затем раскройте обман. И верните их в камеру пыток, злобно ухмыляясь. Даже не прикоснувшись к ним, вы наполнили их глубокой духовной болью, которая никогда не уйдёт”. — Гиртинеус Рош, архонт Последнего Клинка"/>
	<entry name="Drukhari/FeastOfTorment" value="&lt;string name='Traits/Drukhari/FeastOfTorment'/&gt;"/>
	<entry name="Drukhari/FeastOfTormentDescription" value="&lt;string name='Actions/Drukhari/FeastOfTormentDescription'/&gt;"/>
	<entry name="Drukhari/FeastOfTormentFlavor" value="&lt;string name='Traits/Drukhari/FeastOfTormentFlavor'/&gt;"/>
	<entry name="Drukhari/SoulShelling" value="&lt;string name='Actions/Drukhari/SoulShelling'/&gt;"/>
	<entry name="Drukhari/SoulShellingDescription" value="&lt;string name='Actions/Drukhari/SoulShellingDescription'/&gt;"/>
	<entry name="Drukhari/SoulShellingFlavor" value="&lt;string name='Actions/Drukhari/SoulShellingFlavor'/&gt;"/>
	<entry name="Drukhari/Splintermind" value="Осколки разума"/>
	<entry name="Drukhari/SplintermindDescription" value="Боевые препараты дают снижение потери морали."/>
	<entry name="Drukhari/SplintermindFlavor" value="Осколки разума — препарат из измельчённых кристаллических останков мёртвого эльдарского Провидца. Хотя он и не дарует настоящего предвидения, этот порошок позволяет тем, кто его принимает, мыслить сразу в нескольких направлениях — бесценное преимущество, ведь неразбериха в бою сказывается даже на самом тщательно спланированном сражении."/>
	<entry name="Drukhari/TormentGrenadeLaunchers" value="&lt;string name='Weapons/TormentGrenadeLaunchers'/&gt;"/>
	<entry name="Drukhari/TormentGrenadeLaunchersDescription" value="Даёт «Рейдерам», «Губителям» и «Танталам» гранатомёты «Пытка»."/>
	<entry name="Drukhari/TormentGrenadeLaunchersFlavor" value="&lt;string name='Weapons/TormentGrenadeLaunchersFlavor'/&gt;"/>
	<entry name="Drukhari/WealthPlunder" value="&lt;string name='Actions/Drukhari/WealthPlunder'/&gt;"/>
	<entry name="Drukhari/WealthPlunderDescription" value="&lt;string name='Actions/Drukhari/WealthPlunderDescription'/&gt;"/>
	<entry name="Drukhari/WealthPlunderFlavor" value="&lt;string name='Actions/Drukhari/WealthPlunderFlavor'/&gt;"/>
	<entry name="Drukhari/WeaponRacks" value="&lt;string name='Traits/Drukhari/WeaponRacks'/&gt;"/>
	<entry name="Drukhari/WeaponRacksDescription" value="Даёт свойство Сдвоенные дистанционному оружию подразделений, покидающих «Рейдер» или «Тантал»."/>
	<entry name="Drukhari/WeaponRacksFlavor" value="&lt;string name='Traits/Drukhari/WeaponRacksFlavor'/&gt;"/>
	<entry name="Drukhari/WebwayTravelAction" value="Миграция в реальное пространство"/>
	<entry name="Drukhari/WebwayTravelActionDescription" value="Обнуляет стоимость действия перемещения по Паутине."/>
	<entry name="Drukhari/WebwayTravelActionFlavor" value="Хотя проход через Врата Паутины сам по себе прост, нескоординированное перемещение большой армии или формирования может сделать их уязвимыми. Друкари решают проблему созданием временных врат вокруг постоянных порталов, что позволяет всей группе проходить быстро и одновременно."/>
	<entry name="Eldar/AircraftBuildingBonus" value="&lt;string name='Traits/Eldar/AircraftBuildingBonus'/&gt;"/>
	<entry name="Eldar/AircraftBuildingBonusDescription" value="Увеличивает производство Спиралей портала."/>
	<entry name="Eldar/AircraftBuildingBonusFlavor" value="&lt;string name='Traits/Eldar/AircraftBuildingBonusFlavor'/&gt;"/>
	<entry name="Eldar/AssaultWeaponBonus" value="&lt;string name='Traits/Eldar/AssaultWeaponBonus'/&gt;"/>
	<entry name="Eldar/AssaultWeaponBonusDescription" value="Увеличивает бронебойность штурмового оружия."/>
	<entry name="Eldar/AssaultWeaponBonusFlavor" value="&lt;string name='Traits/Eldar/AssaultWeaponBonusFlavor'/&gt;"/>
	<entry name="Eldar/AsuryaniArrivalsBonus" value="Призыв Провидца"/>
	<entry name="Eldar/AsuryaniArrivalsBonusDescription" value="Снижает стоимость Прибытия Азуриан."/>
	<entry name="Eldar/AsuryaniArrivalsBonusFlavor" value="“Мое слово—больше, чем просто совет: это проклятие, пропущенное через сознание наших легендарных мертвецов, стремящихся навести хоть на йоту порядок в беспорядочной вселенной. Наша раса понимает это и прислушивается к моему слову не как к диктатору, а просто как внимательный ученик.”&lt;br/&gt; — Провидец Катаймон из Малантаи"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2" value="Обещание миров-кораблей"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Description" value="Уменьшает время перезарядки Призыва Азуриан."/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Flavor" value="“Когда Чернокнижники и Провидцы нескольких миров-кораблей разделяют видение—буквальное видение будущего, разума,—тогда они и их миры-корабли работают как одно целое. Они открывают свои собственные чертоги жизни и духа, чтобы выполнить свое обещание этому будущему.”&lt;br/&gt; — Стенограмма лекций Григомен Делра, Вольного торговца и любителя-ксенолога"/>
	<entry name="Eldar/CityTier2" value="&lt;string name='Traits/Eldar/CityTier2'/&gt;"/>
	<entry name="Eldar/CityTier2Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="Eldar/CityTier2Flavor" value="&lt;string name='Traits/Eldar/CityTier2Flavor'/&gt;"/>
	<entry name="Eldar/CityTier3" value="&lt;string name='Traits/Eldar/CityTier3'/&gt;"/>
	<entry name="Eldar/CityTier3Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="Eldar/CityTier3Flavor" value="&lt;string name='Traits/Eldar/CityTier3Flavor'/&gt;"/>
	<entry name="Eldar/CleansingFlame" value="&lt;string name='Actions/CleansingFlame'/&gt;"/>
	<entry name="Eldar/CleansingFlameDescription" value="Дает Колдунам способность поражать соседних противников раскаленным психическим пламенем."/>
	<entry name="Eldar/CleansingFlameFlavor" value="&lt;string name='Actions/CleansingFlameFlavor'/&gt;"/>
	<entry name="Eldar/ConstructionBuildingBonus" value="&lt;string name='Traits/Eldar/ConstructionBuildingBonus'/&gt;"/>
	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Увеличивает объем производства в Часовнях Костопевцев."/>
	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="&lt;string name='Traits/Eldar/ConstructionBuildingBonusFlavor'/&gt;"/>
	<entry name="Eldar/CrackShot" value="&lt;string name='Traits/Eldar/CrackShot'/&gt;"/>
	<entry name="Eldar/CrackShotDescription" value="Увеличивает точность и бронебойность Огненных драконов."/>
	<entry name="Eldar/CrackShotFlavor" value="&lt;string name='Traits/Eldar/CrackShotFlavor'/&gt;"/>
	<entry name="Eldar/CrystalTargetingMatrix" value="&lt;string name='Traits/Eldar/CrystalTargetingMatrix'/&gt;"/>
	<entry name="Eldar/CrystalTargetingMatrixDescription" value="Дает Волновым змеям, Шершням, Огненным призмам и боевым Шагоходам способность временно повышать их точность."/>
	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="&lt;string name='Traits/Eldar/CrystalTargetingMatrixFlavor'/&gt;"/>
	<entry name="Eldar/Dominate" value="&lt;string name='Actions/Dominate'/&gt;"/>
	<entry name="Eldar/DominateDescription" value="Предоставляет истребителю Болиголов возможность оглушать вражеские подразделения, которые не являются техникой или укреплениями."/>
	<entry name="Eldar/DominateFlavor" value="&lt;string name='Actions/DominateFlavor'/&gt;"/>
	<entry name="Eldar/ExpertHunter" value="&lt;string name='Traits/Eldar/ExpertHunter'/&gt;"/>
	<entry name="Eldar/ExpertHunterDescription" value="Повышает урон Сияющих копий против монструозных созданий, техники и укреплений."/>
	<entry name="Eldar/ExpertHunterFlavor" value="&lt;string name='Traits/Eldar/ExpertHunterFlavor'/&gt;"/>
	<entry name="Eldar/ExtraInfantryArmour" value="Лабиринтианская сетчатая броня"/>
	<entry name="Eldar/ExtraInfantryArmourDescription" value="Повышает броню пехоты."/>
	<entry name="Eldar/ExtraInfantryArmourFlavor" value="Броня из термопластической сетки Эльдар предназначена не только для защиты—она также позволяет носителю перемещаться так же свободно, как если бы ее не было. Однако за счет использования её сложных протоколов рассеивания можно повысить её устойчивость без потери подвижности."/>
	<entry name="Eldar/ExtraVehicleArmour" value="Инфузия Призрачной кости"/>
	<entry name="Eldar/ExtraVehicleArmourDescription" value="Повышает броню техники."/>
	<entry name="Eldar/ExtraVehicleArmourFlavor" value="Все машины и сооружения Эльдар буквально «спеты» для реальности Костопевцами, чье владение своим вокальным и психическим диапазоном является конструктивным эквивалентом боевого клича Воющей Баньши. Тем не менее, благодаря ресурсам Гладиус Прайм и изменению их внутренней гармонии, Костопевцы могут вкладывать большую силу в свои творения, еще больше повышая их устойчивость."/>
	<entry name="Eldar/FoodBuildingBonus" value="&lt;string name='Traits/Eldar/FoodBuildingBonus'/&gt;"/>
	<entry name="Eldar/FoodBuildingBonusDescription" value="Увеличивает производство еды в садах Иши."/>
	<entry name="Eldar/FoodBuildingBonusFlavor" value="&lt;string name='Traits/Eldar/FoodBuildingBonusFlavor'/&gt;"/>
	<entry name="Eldar/GhostwalkMatrix" value="Матрица Призрачного пути"/>
	<entry name="Eldar/GhostwalkMatrixDescription" value="Позволяет Волновым змеям, Шершням, Огненным призмам и боевым шагоходам проходить сквозь укрытия."/>
	<entry name="Eldar/GhostwalkMatrixFlavor" value="Матрица призрачного пути использует знания и мудрость, содержащиеся в камне душ, чтобы направлять технику на правильны путь."/>
	<entry name="Eldar/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="Eldar/HammerOfWrathDescription" value="Даёт Сияющим Копьям, Провидцам Оседлавшим ветер, Аватару Кхейна, Боевому шагоходу, Призрачному Лорду и Призрачным Рыцарям возможность выполнять более разрушительные атаки."/>
	<entry name="Eldar/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="Eldar/HeavyWeaponBonus" value="&lt;string name='Traits/Eldar/HeavyWeaponBonus'/&gt;"/>
	<entry name="Eldar/HeavyWeaponBonusDescription" value="Увеличивает бронебойность тяжёлого оружия."/>
	<entry name="Eldar/HeavyWeaponBonusFlavor" value="&lt;string name='Traits/Eldar/HeavyWeaponBonusFlavor'/&gt;"/>
	<entry name="Eldar/HoloFields" value="&lt;string name='Traits/Eldar/HoloFields'/&gt;"/>
	<entry name="Eldar/HoloFieldsDescription" value="Даёт неснижаемое сокращение урона Волновым змеям, Шершням и Огненным призмам после перемещения."/>
	<entry name="Eldar/HoloFieldsFlavor" value="&lt;string name='Traits/Eldar/HoloFieldsFlavor'/&gt;"/>
	<entry name="Eldar/InfantryBuildingBonus" value="&lt;string name='Traits/Eldar/InfantryBuildingBonus'/&gt;"/>
	<entry name="Eldar/InfantryBuildingBonusDescription" value="Увеличивает производство Горнил Азуриана."/>
	<entry name="Eldar/InfantryBuildingBonusFlavor" value="&lt;string name='Traits/Eldar/InfantryBuildingBonusFlavor'/&gt;"/>
	<entry name="Eldar/InfluenceBuildingBonus" value="&lt;string name='Traits/Eldar/InfluenceBuildingBonus'/&gt;"/>
	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Увеличивает влияние Купола Провидцев."/>
	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="&lt;string name='Traits/Eldar/InfluenceBuildingBonusFlavor'/&gt;"/>
	<entry name="Eldar/MarksmansEye" value="&lt;string name='Traits/Eldar/MarksmansEye'/&gt;"/>
	<entry name="Eldar/MarksmansEyeDescription" value="Повышает точность Багровых охотников."/>
	<entry name="Eldar/MarksmansEyeFlavor" value="&lt;string name='Traits/Eldar/MarksmansEyeFlavor'/&gt;"/>
	<entry name="Eldar/MeleeWeaponBonus" value="&lt;string name='Traits/Eldar/MeleeWeaponBonus'/&gt;"/>
	<entry name="Eldar/MeleeWeaponBonusDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="Eldar/MeleeWeaponBonusFlavor" value="&lt;string name='Traits/Eldar/MeleeWeaponBonusFlavor'/&gt;"/>
	<entry name="Eldar/OreBuildingBonus" value="&lt;string name='Traits/Eldar/OreBuildingBonus'/&gt;"/>
	<entry name="Eldar/OreBuildingBonusDescription" value="Увеличивает добычу руды в Альтмарле Ваула."/>
	<entry name="Eldar/OreBuildingBonusFlavor" value="&lt;string name='Traits/Eldar/OreBuildingBonusFlavor'/&gt;"/>
	<entry name="Eldar/PlasmaGrenade" value="&lt;string name='Weapons/PlasmaGrenade'/&gt;"/>
	<entry name="Eldar/PlasmaGrenadeDescription" value="Предоставляет Стражникам Автархам возможность бросать универсальные гранаты."/>
	<entry name="Eldar/PlasmaGrenadeFlavor" value="&lt;string name='Weapons/PlasmaGrenadeFlavor'/&gt;"/>
	<entry name="Eldar/ResearchBuildingBonus" value="&lt;string name='Traits/Eldar/ResearchBuildingBonus'/&gt;"/>
	<entry name="Eldar/ResearchBuildingBonusDescription" value="Повышает эффективность исследования Пещер душ."/>
	<entry name="Eldar/ResearchBuildingBonusFlavor" value="&lt;string name='Traits/Eldar/ResearchBuildingBonusFlavor'/&gt;"/>
	<entry name="Eldar/SpiritPreservationBonus" value="Оправа-щит"/>
	<entry name="Eldar/SpiritPreservationBonusDescription" value="Увеличивает прирост энергии от смерти отрядов Эльдар."/>
	<entry name="Eldar/SpiritPreservationBonusFlavor" value="С незапамятных времен мы помещали наши камни душ в простые футляры, полагаясь на умение их владельцев сохранить их. Тем не менее, древние структуры Паутины, с которыми мы столкнулись на Гладиус Прайм, демонстрируют, что сложная защита может быть встроена в любую оболочку из призрачной кости, что позволяет нам выхватить большее количество наших мертвых воинов из пасти Той-что-жаждет."/>
	<entry name="Eldar/SpiritStones" value="&lt;string name='Traits/Eldar/SpiritStones'/&gt;"/>
	<entry name="Eldar/SpiritStonesDescription" value="Снижает потерю морали Волновых змей, Шершней, Огненных призм и боевых шагоходов."/>
	<entry name="Eldar/SpiritStonesFlavor" value="&lt;string name='Traits/Eldar/SpiritStonesFlavor'/&gt;"/>
	<entry name="Eldar/StarEngines" value="&lt;string name='Traits/Eldar/StarEngines'/&gt;"/>
	<entry name="Eldar/StarEnginesDescription" value="Увеличивает движение Волновых Змей, Огненных призм, Шершней, Боевых шагоходов и Скорпионов."/>
	<entry name="Eldar/StarEnginesFlavor" value="&lt;string name='Traits/Eldar/StarEnginesFlavor'/&gt;"/>
	<entry name="Eldar/TranscendentBliss" value="&lt;string name='Actions/Eldar/TranscendentBliss'/&gt;"/>
	<entry name="Eldar/TranscendentBlissDescription" value="Предоставляет городам возможность тратить влияние на временное повышение лояльности."/>
	<entry name="Eldar/TranscendentBlissFlavor" value="&lt;string name='Actions/Eldar/TranscendentBlissFlavor'/&gt;"/>
	<entry name="Eldar/TranscendentBlissBonus" value="Гибель Эльдар"/>
	<entry name="Eldar/TranscendentBlissBonusDescription" value="Повышает лояльность от Необыкновенного блаженства."/>
	<entry name="Eldar/TranscendentBlissBonusFlavor" value="“Для свободного разума Эльдар нет ни здравомыслия, ни безумия, а просто волна совершенного существования, воплощенная в их собственном диком импульсе.”&lt;br/&gt; — Раламин Мунг, Ордо Ксенос"/>
	<entry name="Eldar/VectoredEngines" value="&lt;string name='Traits/Eldar/VectoredEngines'/&gt;"/>
	<entry name="Eldar/VectoredEnginesDescription" value="Предоставляет Волновым Змеям, Шершням, Огненным призмам, Боевым шагоходам и Скорпионам возможность временно усиливать свою броню против вражеского дальнобойного оружия."/>
	<entry name="Eldar/VectoredEnginesFlavor" value="&lt;string name='Traits/Eldar/VectoredEnginesFlavor'/&gt;"/>
	<entry name="Eldar/VehicleBuildingBonus" value="&lt;string name='Traits/Eldar/VehicleBuildingBonus'/&gt;"/>
	<entry name="Eldar/VehicleBuildingBonusDescription" value="Увеличивает производство Великих входов."/>
	<entry name="Eldar/VehicleBuildingBonusFlavor" value="&lt;string name='Traits/Eldar/VehicleBuildingBonusFlavor'/&gt;"/>
	<entry name="Eldar/WarShout" value="&lt;string name='Actions/Eldar/WarShout'/&gt;"/>
	<entry name="Eldar/WarShoutDescription" value="Предоставляет Воющим Баньши способность деморализовать соседних врагов."/>
	<entry name="Eldar/WarShoutFlavor" value="&lt;string name='Actions/Eldar/WarShoutFlavor'/&gt;"/>
	<entry name="Eldar/WebwayGateBonus" value="Картография Паутины"/>
	<entry name="Eldar/WebwayGateBonusDescription" value="Снижает до нуля стоимость активации врат Паутины."/>
	<entry name="Eldar/WebwayGateBonusFlavor" value="Только Арлекины и хранители Черной библиотеки действительно знают что-то конкретное о межпространственном расположении Паутины. Но, подарив нам часть этих знаний, относящихся только к этому миру, Библиотекари обеспечили нам возможность с легкостью снова открыть эти древние врата."/>
	<entry name="Eldar/WebwayGateBonus2" value="Сжатие Паутины"/>
	<entry name="Eldar/WebwayGateBonus2Description" value="Обнуляет стоимость действия перемещения по Паутине."/>
	<entry name="Eldar/WebwayGateBonus2Flavor" value="Получив руководство от Черной библиотеки по уникальному расположению Паутины Гладиуса, духовидцы спланировали кратчайшие пути между любыми двумя вратами, сделав путь между ними таким же безпроблемным, как и любой другой шаг, который мы можем сделать."/>
	<entry name="Eldar/WebwayRedoubt" value="Редут Паутины"/>
	<entry name="Eldar/WebwayRedoubtDescription" value="Предоставляет возможность основывать новые города на контролируемых Вратах Паутины."/>
	<entry name="Eldar/WebwayRedoubtFlavor" value="&lt;string name='Actions/Eldar/WebwayRedoubtFlavor'/&gt;"/>
	<entry name="Eldar/WraithknightStarcannon" value="Звездные пушки Призрачного рыцаря"/>
	<entry name="Eldar/WraithknightStarcannonDescription" value="Дает две Звездные пушки Призрачным рыцарям"/>
	<entry name="Eldar/WraithknightStarcannonFlavor" value="&lt;string name='Weapons/StarcannonFlavor'/&gt;"/>
	<entry name="Necrons/AircraftBuildingBonus" value="&lt;string name='Traits/Necrons/AircraftBuildingBonus'/&gt;"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Увеличивает объемы производства Безымянного Пути Причины."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/AircraftBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/AttackCityBonus" value="&lt;string name='Traits/Necrons/AttackCityBonus'/&gt;"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Повышает точность при атаке вражеских подразделений в городах."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="&lt;string name='Traits/Necrons/AttackCityBonusFlavor'/&gt;"/>
	<entry name="Necrons/BlastDamage" value="&lt;string name='Traits/Necrons/BlastDamage'/&gt;"/>
	<entry name="Necrons/BlastDamageDescription" value="Увеличивает бронебойность разрывного и типового оружия."/>
	<entry name="Necrons/BlastDamageFlavor" value="&lt;string name='Traits/Necrons/BlastDamageFlavor'/&gt;"/>
	<entry name="Necrons/CityDefenseBonus" value="Невообразимые препятствия"/>
	<entry name="Necrons/CityDefenseBonusDescription" value="Повышает сокращение урона подразделениям в городах."/>
	<entry name="Necrons/CityDefenseBonusFlavor" value="Атаки на защитников города-гробницы загадочным образом пресекаются—энергетические атаки исчезают, отклоняется гравитонное оружие, и физические снаряды, кажется, разбиваются о Циклопические стены появившиеся прямо из гробниц."/>
	<entry name="Necrons/CityTier2" value="&lt;string name='Traits/Necrons/CityTier2'/&gt;"/>
	<entry name="Necrons/CityTier2Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="Necrons/CityTier2Flavor" value="&lt;string name='Traits/Necrons/CityTier2Flavor'/&gt;"/>
	<entry name="Necrons/CityTier3" value="&lt;string name='Traits/Necrons/CityTier3'/&gt;"/>
	<entry name="Necrons/CityTier3Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="Necrons/CityTier3Flavor" value="&lt;string name='Traits/Necrons/CityTier3Flavor'/&gt;"/>
	<entry name="Necrons/ConstructionBuildingBonus" value="&lt;string name='Traits/Necrons/ConstructionBuildingBonus'/&gt;"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Увеличивает объемы производства Рабов дома Вечности."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/ConstructionBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/DimensionalCorridor" value="&lt;string name='Actions/Necrons/DimensionalCorridor'/&gt;"/>
	<entry name="Necrons/DimensionalCorridorDescription" value="Дает пехоте возможность телепортироваться к городам и Монолиту."/>
	<entry name="Necrons/DimensionalCorridorFlavor" value="&lt;string name='Actions/Necrons/DimensionalCorridorFlavor'/&gt;"/>
	<entry name="Necrons/DimensionalCorridor2" value="Размерная Стабильность"/>
	<entry name="Necrons/DimensionalCorridor2Description" value="Снижает стоимость влияния для использования Размерных коридоров."/>
	<entry name="Necrons/DimensionalCorridor2Flavor" value="Опираясь на мощь брошенной техники Древних, Некроны стабилизировали свою технологию призыва, что значительно снижает стоимость воинов проходящих во Врата Вечности."/>
	<entry name="Necrons/DimensionalCorridor3" value="Размерная Поддержка"/>
	<entry name="Necrons/DimensionalCorridor3Description" value="Обнуляет стоимость движения и действия для Размерных коридоров."/>
	<entry name="Necrons/DimensionalCorridor3Flavor" value="Хитроумность Криптека позволили Некронам отладить свою технологию призыва, в значительной степени повысив точность прохождения через них."/>
	<entry name="Necrons/EnergyBuildingBonus" value="&lt;string name='Traits/Necrons/EnergyBuildingBonus'/&gt;"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Повышает эффективность энергетических ядер."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/EnergyBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="Necrons/ExtraInfantryArmourDescription" value="Увеличивает броню пехоты."/>
	<entry name="Necrons/ExtraInfantryArmourFlavor" value="Даже после тысячелетий войны и сна Криптеки Некронов не утратили стремления к инновациям. Внося незначительные изменения в свой некродермис, они способны повысить выживаемость своих войск в современной войне."/>
	<entry name="Necrons/ExtraVehicleArmour" value="&lt;string name='Traits/ExtraVehicleArmour'/&gt;"/>
	<entry name="Necrons/ExtraVehicleArmourDescription" value="Увеличивает броню машин и Каноптеков."/>
	<entry name="Necrons/ExtraVehicleArmourFlavor" value="Внешне эта техника Некронов идентична своим предшественникам. Но Криптек и, возможно, даже образованный Эльдар, может увидеть, что материал, из которой она сделана, повысил её устойчивость каким-то образом, и не изменил её вес."/>
	<entry name="Necrons/GaussDamage" value="&lt;string name='Traits/GaussDamage'/&gt;"/>
	<entry name="Necrons/GaussDamageDescription" value="Увеличивает бронебойность Гаусс-оружия."/>
	<entry name="Necrons/GaussDamageFlavor" value="&lt;string name='Weapons/GaussFlavor'/&gt;"/>
	<entry name="Necrons/GaussPylon" value="&lt;string name='Units/Necrons/GaussPylon'/&gt;"/>
	<entry name="Necrons/GaussPylonDescription" value="Предоставляет городам возможность поднимать из земли мощные Гауссовые укрепления."/>
	<entry name="Necrons/GaussPylonFlavor" value="&lt;string name='Units/Necrons/GaussPylonFlavor'/&gt;"/>
	<entry name="Necrons/GloomPrism" value="&lt;string name='Actions/Necrons/GloomPrism'/&gt;"/>
	<entry name="Necrons/GloomPrismDescription" value="Повышает сокращение урона от колдовского огня Каноптековым паукам и соседним союзникам."/>
	<entry name="Necrons/GloomPrismFlavor" value="&lt;string name='Actions/Necrons/GloomPrismFlavor'/&gt;"/>
	<entry name="Necrons/GrowthBonus" value="&lt;string name='Traits/Necrons/GrowthBonus'/&gt;"/>
	<entry name="Necrons/GrowthBonusDescription" value="Увеличивает скорость роста населения городов Некронов."/>
	<entry name="Necrons/GrowthBonusFlavor" value="&lt;string name='Traits/Necrons/GrowthBonusFlavor'/&gt;"/>
	<entry name="Necrons/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="Necrons/HammerOfWrathDescription" value="Даёт Лорд-уничтожителю, Могильным клинкам, Каноптековым Паукам, Трансцендентным К'тан, Триархическим Преторианцам и Триархическим охотникам возможность выполнения более разрушительных атак."/>
	<entry name="Necrons/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="Necrons/HousingBuildingBonus" value="&lt;string name='Traits/Necrons/HousingBuildingBonus'/&gt;"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Увеличивает лимит населения убежищ."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/HousingBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/InfantryBuildingBonus" value="&lt;string name='Traits/Necrons/InfantryBuildingBonus'/&gt;"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Увеличивает эффективность Призывного ядра."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/InfantryBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/InfluenceBuildingBonus" value="&lt;string name='Traits/Necrons/InfluenceBuildingBonus'/&gt;"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Увеличивает влияние Стелы."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/InfluenceBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/LastStandReady" value="&lt;string name='Traits/LastStand'/&gt;"/>
	<entry name="Necrons/LivingMetal2" value="Бессмертные Формы"/>
	<entry name="Necrons/LivingMetal2Description" value="Увеличивает очки здоровье от Живого металла."/>
	<entry name="Necrons/LivingMetal2Flavor" value="В некотором заумном смысле, машины Некронов сейчас помнят свою былую славу и бесконечно стремятся обрести её вновь, перераспределяя живой металл так быстро, чтобы ликвидировать любой полученный урон."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="&lt;string name='Traits/Necrons/LoyaltyBuildingBonus'/&gt;"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Увеличивает лояльность от Причудливого храма."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/LoyaltyBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/MeleeDamage" value="&lt;string name='Traits/Necrons/MeleeDamage'/&gt;"/>
	<entry name="Necrons/MeleeDamageDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="Necrons/MeleeDamageFlavor" value="&lt;string name='Traits/Necrons/MeleeDamageFlavor'/&gt;"/>
	<entry name="Necrons/Nebuloscope" value="&lt;string name='Traits/Necrons/Nebuloscope'/&gt;"/>
	<entry name="Necrons/NebuloscopeDescription" value="Позволяет Могильным клинкам игнорировать сокращение дистанц. урона."/>
	<entry name="Necrons/NebuloscopeFlavor" value="&lt;string name='Traits/Necrons/NebuloscopeFlavor'/&gt;"/>
	<entry name="Necrons/NecrodermisRepair2" value="Ускоренное Отрастание"/>
	<entry name="Necrons/NecrodermisRepair2Description" value="Увеличивает восстановление Очков здоровья от Ремонта Некродермисом."/>
	<entry name="Necrons/NecrodermisRepair2Flavor" value="Криптеки снова улучшили наномеханическую структуру тел Некронов из живого металла, позволяя им справляться с почти любыми повреждениями."/>
	<entry name="Necrons/NecrodermisRepair3" value="Ужасный Некродермис"/>
	<entry name="Necrons/NecrodermisRepair3Description" value="Отменяет перезарядку Ремонта Некродермисом."/>
	<entry name="Necrons/NecrodermisRepair3Flavor" value="Живой металл, из которого состоят тела Некронов и их техники сейчас постоянно корчится, перестраивается каждое мгновение."/>
	<entry name="Necrons/OreBuildingBonus" value="&lt;string name='Traits/Necrons/OreBuildingBonus'/&gt;"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Увеличивает добычу руды в Карьерах Аль-Хемик."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/OreBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/QuantumShielding" value="&lt;string name='Actions/Necrons/QuantumShielding'/&gt;"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Даёт Аннигиляционным баржам, Ковчегам духов, Ковчегам Судного дня и Триархическим охотникам неснижаемое сокращение урона, которое уходит на перезарядку в начале следующего хода, если получен урон."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="&lt;string name='Actions/Necrons/QuantumShieldingFlavor'/&gt;"/>
	<entry name="Necrons/RapidRiseBonus" value="Команда Лорда"/>
	<entry name="Necrons/RapidRiseBonusDescription" value="Снижает затраты на Быстрое пробуждение."/>
	<entry name="Necrons/RapidRiseBonusFlavor" value="Лорд укрепляет свои протоколы патрон-клиент, так что его приказы выполняются с большей скоростью и меньше обдумываются—меньше свободы воли."/>
	<entry name="Necrons/ReanimationProtocols2" value="Эффективные Протоколы Реанимации"/>
	<entry name="Necrons/ReanimationProtocols2Description" value="Увеличивает исцеление от Протоколов Реанимации."/>
	<entry name="Necrons/ReanimationProtocols2Flavor" value="С помощью этих систем восстановления и пробуждения, созданных Криптеком, Некроны еще более невосприимчивы к смертельно опасному урону."/>
	<entry name="Necrons/ResearchBuildingBonus" value="&lt;string name='Traits/Necrons/ResearchBuildingBonus'/&gt;"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Увеличивает эффективность исследований в Запретных архивах."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/ResearchBuildingBonusFlavor'/&gt;"/>
	<entry name="Necrons/ScarabHive" value="&lt;string name='Actions/Necrons/ScarabHive'/&gt;"/>
	<entry name="Necrons/ScarabHiveDescription" value="Даёт Каноптековым паукам возможность построить Каноптековых Скарабеев."/>
	<entry name="Necrons/ScarabHiveFlavor" value="&lt;string name='Actions/Necrons/ScarabHiveFlavor'/&gt;"/>
	<entry name="Necrons/SeismicAssault" value="Сейсмический Штурм"/>
	<entry name="Necrons/SeismicAssaultDescription" value="Предоставляет Трансцендентным К'тан и Тессерактовым Склепам возможность выполнить разрушительный удар."/>
	<entry name="Necrons/SeismicAssaultFlavor" value="&lt;string name='Weapons/SeismicAssaultTranscendentFlavor'/&gt;"/>
	<entry name="Necrons/ShieldVane" value="&lt;string name='Traits/Necrons/ShieldVane'/&gt;"/>
	<entry name="Necrons/ShieldVaneDescription" value="Увеличивает броню Могильных клинков."/>
	<entry name="Necrons/ShieldVaneFlavor" value="&lt;string name='Traits/Necrons/ShieldVaneFlavor'/&gt;"/>
	<entry name="Necrons/TeslaDamage" value="&lt;string name='Traits/TeslaDamage'/&gt;"/>
	<entry name="Necrons/TeslaDamageDescription" value="Увеличивает бронебойность Тесла-оружия."/>
	<entry name="Necrons/TeslaDamageFlavor" value="&lt;string name='Weapons/TeslaFlavor'/&gt;"/>
	<entry name="Necrons/TheBoundCoalescent" value="&lt;string name='Actions/Necrons/TheBoundCoalescent'/&gt;"/>
	<entry name="Necrons/TheBoundCoalescentDescription" value="Предоставляет Трансцендентному К'тан возможность слиться с Обелиском для создания Тессерактового Склепа."/>
	<entry name="Necrons/TheBoundCoalescentFlavor" value="&lt;string name='Actions/Necrons/TheBoundCoalescentFlavor'/&gt;"/>
	<entry name="Necrons/VehiclesBuildingBonus" value="&lt;string name='Traits/Necrons/VehiclesBuildingBonus'/&gt;"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Увеличивает объемы производства Гипостильного храма."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="&lt;string name='Traits/Necrons/VehiclesBuildingBonusFlavor'/&gt;"/>
	<entry name="Orks/AmmoRunt" value="&lt;string name='Actions/AmmoRunt'/&gt;"/>
	<entry name="Orks/AmmoRuntDescription" value="Даёт Башим Мекам, Понторезам и Мек-пушкам возможность повысить точность дальнего огня."/>
	<entry name="Orks/AmmoRuntFlavor" value="&lt;string name='Actions/AmmoRuntFlavor'/&gt;"/>
	<entry name="Orks/BattlewagonBigShootas" value="Б'ашая Стриляла на Баивой фуре"/>
	<entry name="Orks/BattlewagonBigShootasDescription" value="Оснащает Баивую фуру Б'ашой Стрилялой."/>
	<entry name="Orks/BattlewagonBigShootasFlavor" value="&lt;string name='Weapons/BigShootaFlavor'/&gt;"/>
	<entry name="Orks/BattlewagonRokkitLaunchas" value="Баивая фура с Ракетомётом"/>
	<entry name="Orks/BattlewagonRokkitLaunchasDescription" value="Оснащение Баиваой фуры Ракетомётом."/>
	<entry name="Orks/BattlewagonRokkitLaunchasFlavor" value="&lt;string name='Weapons/RokkitLaunchaFlavor'/&gt;"/>
	<entry name="Orks/Bigbomm" value="&lt;string name='Weapons/Bigbomm'/&gt;"/>
	<entry name="Orks/BigbommDescription" value="Позволяет Смертолётам использовать противопехотные бомбы."/>
	<entry name="Orks/BigbommFlavor" value="&lt;string name='Weapons/BigbommFlavor'/&gt;"/>
	<entry name="Orks/BlastDamage" value="&lt;string name='Traits/Orks/BlastDamage'/&gt;"/>
	<entry name="Orks/BlastDamageDescription" value="Увеличивает бронебойность гранат, ракет и разрывного вооружения."/>
	<entry name="Orks/BlastDamageFlavor" value="&lt;string name='Traits/Orks/BlastDamageFlavor'/&gt;"/>
	<entry name="Orks/BoltDamage" value="&lt;string name='Traits/Orks/BoltDamage'/&gt;"/>
	<entry name="Orks/BoltDamageDescription" value="&lt;string name='Upgrades/AstraMilitarum/BoltDamageDescription'/&gt;"/>
	<entry name="Orks/BoltDamageFlavor" value="&lt;string name='Traits/Orks/BoltDamageFlavor'/&gt;"/>
	<entry name="Orks/BonusBeastsProduction" value="&lt;string name='Traits/Orks/BonusBeastsProduction'/&gt;"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="&lt;string name='Traits/Orks/BonusBeastsProductionDescription'/&gt;"/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="&lt;string name='Traits/Orks/BonusBeastsProductionFlavor'/&gt;"/>
	<entry name="Orks/BonusColonizersProduction" value="&lt;string name='Traits/Orks/BonusColonizersProduction'/&gt;"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="&lt;string name='Traits/Orks/BonusColonizersProductionDescription'/&gt;"/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="&lt;string name='Traits/Orks/BonusColonizersProductionFlavor'/&gt;"/>
	<entry name="Orks/BonusInfantryProduction" value="&lt;string name='Traits/Orks/BonusInfantryProduction'/&gt;"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="&lt;string name='Traits/Orks/BonusInfantryProductionDescription'/&gt;"/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="&lt;string name='Traits/Orks/BonusInfantryProductionFlavor'/&gt;"/>
	<entry name="Orks/BonusVehiclesProduction" value="&lt;string name='Traits/Orks/BonusVehiclesProduction'/&gt;"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="&lt;string name='Traits/Orks/BonusVehiclesProductionDescription'/&gt;"/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="&lt;string name='Traits/Orks/BonusVehiclesProductionFlavor'/&gt;"/>
	<entry name="Orks/Bosspole" value="Боссошест"/>
	<entry name="Orks/BosspoleDescription" value="Способствует уменьшению потери морали от Правил Мобов для Баших Мекав, Парням, Меганобам, Танкобоям, Баивым матоциклистам и Варбоссам."/>
	<entry name="Orks/BosspoleFlavor" value="Нобы Орков часто носят трофейные посохи, которые показывают, что с ними не стоит связываться. Ноб с Боссшестом часто считает, что он очень полезен, когда он им ломает головы, чтобы восстановить порядок в пылу битвы."/>
	<entry name="Orks/CityEnergy" value="&lt;string name='Traits/Orks/CityEnergy'/&gt;"/>
	<entry name="Orks/CityEnergyDescription" value="Увеличивает выработку энергии городами орков."/>
	<entry name="Orks/CityEnergyFlavor" value="&lt;string name='Traits/Orks/CityEnergyFlavor'/&gt;"/>
	<entry name="Orks/CityGrowth" value="&lt;string name='Traits/Orks/CityGrowth'/&gt;"/>
	<entry name="Orks/CityGrowthDescription" value="&lt;string name='Traits/Orks/CityGrowthDescription'/&gt;"/>
	<entry name="Orks/CityGrowthFlavor" value="&lt;string name='Traits/Orks/CityGrowthFlavor'/&gt;"/>
	<entry name="Orks/CityInfluence" value="&lt;string name='Traits/Orks/CityInfluence'/&gt;"/>
	<entry name="Orks/CityInfluenceDescription" value="Увеличивает влияние в городе Орков."/>
	<entry name="Orks/CityInfluenceFlavor" value="&lt;string name='Traits/Orks/CityInfluenceFlavor'/&gt;"/>
	<entry name="Orks/CityLoyalty" value="&lt;string name='Traits/Orks/CityLoyalty'/&gt;"/>
	<entry name="Orks/CityLoyaltyDescription" value="Увеличивает лояльность в городах Орков."/>
	<entry name="Orks/CityLoyaltyFlavor" value="&lt;string name='Traits/Orks/CityLoyaltyFlavor'/&gt;"/>
	<entry name="Orks/CityPopulationLimit" value="&lt;string name='Traits/Orks/CityPopulationLimit'/&gt;"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Увеличивает лимит населения в городах Орков."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="&lt;string name='Traits/Orks/CityPopulationLimitFlavor'/&gt;"/>
	<entry name="Orks/CityResearch" value="&lt;string name='Traits/Orks/CityResearch'/&gt;"/>
	<entry name="Orks/CityResearchDescription" value="Увеличивает эффективность исследований в городах Орков."/>
	<entry name="Orks/CityResearchFlavor" value="&lt;string name='Traits/Orks/CityResearchFlavor'/&gt;"/>
	<entry name="Orks/CityTier2" value="&lt;string name='Traits/Orks/CityTier2'/&gt;"/>
	<entry name="Orks/CityTier2Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="Orks/CityTier2Flavor" value="&lt;string name='Traits/Orks/CityTier2Flavor'/&gt;"/>
	<entry name="Orks/CityTier3" value="&lt;string name='Traits/Orks/CityTier3'/&gt;"/>
	<entry name="Orks/CityTier3Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="Orks/CityTier3Flavor" value="&lt;string name='Traits/Orks/CityTier3Flavor'/&gt;"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeath" value="Постоянное разложение"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathDescription" value="Подразделение создаёт постоянный оркоидный гриб, когда умирает."/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathFlavor" value="&lt;string name='Traits/Orks/CreateOrkoidFungusOnDeathFlavor'/&gt;"/>
	<entry name="Orks/DakkajetSupaShoota" value="Супир стиляла для Дакка-истребителя"/>
	<entry name="Orks/DakkajetSupaShootaDescription" value="Даёт Дакка-истребителю Супир стрилялу."/>
	<entry name="Orks/DakkajetSupaShootaFlavor" value="&lt;string name='Weapons/TwinLinkedSupaShootaFlavor'/&gt;"/>
	<entry name="Orks/EavyArmour" value="&lt;string name='Traits/EavyArmour'/&gt;"/>
	<entry name="Orks/EavyArmourDescription" value="Увеличивает броню Парней и Варбоссов."/>
	<entry name="Orks/EavyArmourFlavor" value="&lt;string name='Traits/EavyArmourFlavor'/&gt;"/>
	<entry name="Orks/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="Orks/ExtraInfantryArmourDescription" value="Увеличивает броню пехоты."/>
	<entry name="Orks/ExtraInfantryArmourFlavor" value="Для любой другой расы броня орков кажется смешной: огромные куски толстых металлических пластин, скрепленные тросами, заклёпками, с бессмысленными приборами, раскрашенные золотом безумными Меками и Гротами. Но от небольшого количества веры Орков в них, они работают как зачарованные."/>
	<entry name="Orks/ExtraVehicleArmour" value="&lt;string name='Traits/ExtraVehicleArmour'/&gt;"/>
	<entry name="Orks/ExtraVehicleArmourDescription" value="Увеличивает броню техники."/>
	<entry name="Orks/ExtraVehicleArmourFlavor" value="Когда Мек столкнется с Варбоссом, который рассержен тем, что его мотики и Баивые фуры продолжают взрываться от огня противника, он быстро прикажет своим Гротам наковырять немного лома и прибить дополнительную броню в любом удобном для них месте. В конце концов, он знает, что Маняки скорасти в скором времени всё сломают."/>
	<entry name="Orks/Flyboss" value="&lt;string name='Traits/Orks/Flyboss'/&gt;"/>
	<entry name="Orks/FlybossDescription" value="Повышает дистанц. точность Дакка-истребителя против флаеров, Реактивных байков и скиммеров."/>
	<entry name="Orks/FlybossFlavor" value="&lt;string name='Traits/Orks/FlybossFlavor'/&gt;"/>
	<entry name="Orks/GrabbinKlaw" value="&lt;string name='Actions/GrabbinKlaw'/&gt;"/>
	<entry name="Orks/GrabbinKlawDescription" value="Даёт Баивым фурам возможность обездвижить соседнюю наземную технику противника."/>
	<entry name="Orks/GrabbinKlawFlavor" value="&lt;string name='Actions/GrabbinKlawFlavor'/&gt;"/>
	<entry name="Orks/GrotRiggers" value="Грот Такелажник"/>
	<entry name="Orks/GrotRiggersDescription" value="Даёт Баивым багги, Банка-убийцам, Баивым фурам, Смертодредам, Убойным разрывалам и Горканавтам пассивную регенерацию Очков здоровья."/>
	<entry name="Orks/GrotRiggersFlavor" value="&lt;string name='Traits/GrotRiggersFlavor'/&gt;"/>
	<entry name="Orks/HealingRate" value="Зеленый прилив"/>
	<entry name="Orks/HealingRateDescription" value="Увеличивает скорость восстановления подразделений."/>
	<entry name="Orks/HealingRateFlavor" value="Благодаря сотрудничеству команды Сумасшедших Доков и Пагонщиков, ваш военачальник прописал новую диету «баших Орков», чтобы сделать Парней больше, сильнее и быстрее. Независимо от того, что есть, холодное Масло Сквигов на завтрак или грибосквиг на обед, никакого физического эффекта не достигается—но Парни верят в это, и заставляют это работать."/>
	<entry name="Orks/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="Orks/HammerOfWrathDescription" value="Даёт Смертодредам, Смертолётам, Гигантским Сквигготам, Горконавтам, Банка-убийцам и Баивым матоциклистам возможность выполнять более разрушительные атаки."/>
	<entry name="Orks/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="Orks/MeleeDamage" value="&lt;string name='Traits/Orks/MeleeDamage'/&gt;"/>
	<entry name="Orks/MeleeDamageDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="Orks/MeleeDamageFlavor" value="&lt;string name='Traits/Orks/MeleeDamageFlavor'/&gt;"/>
	<entry name="Orks/MightMakesRight2" value="Страшитись, Да Орки!"/>
	<entry name="Orks/MightMakesRight2Description" value="Увеличивает влияние, когда подразделение наносит урон."/>
	<entry name="Orks/MightMakesRight2Flavor" value="Когда Вааагх! достаточно мощный, он имеет что-то вроде мультипликативного эффекта, с каждым ударом происходит дальнейшее повышение психического поля, от какой-то ультра-яростной орочьей обратной связи."/>
	<entry name="Orks/OrkoidFungusBonusHealingRate" value="&lt;string name='Traits/OrkoidFungusBonusHealingRate'/&gt;"/>
	<entry name="Orks/OrkoidFungusBonusHealingRateDescription" value="Увеличивает скорость исцеления от Оркоидного грибка."/>
	<entry name="Orks/OrkoidFungusBonusHealingRateFlavor" value="&lt;string name='Traits/OrkoidFungusBonusHealingRateFlavor'/&gt;"/>
	<entry name="Orks/OrkoidFungusFood" value="&lt;string name='Traits/OrkoidFungusFood'/&gt;"/>
	<entry name="Orks/OrkoidFungusFoodDescription" value="Увеличивает производство пищи на плитке с Оркоидным грибом."/>
	<entry name="Orks/OrkoidFungusFoodFlavor" value="&lt;string name='Traits/OrkoidFungusFoodFlavor'/&gt;"/>
	<entry name="Orks/RedPaintJob" value="Работа Красной Краски"/>
	<entry name="Orks/RedPaintJobDescription" value="Увеличивает урон от Вар-багги, Мегатрака Реактивный лом, Баивой фуры, Дакка-истребителей и жыгало-бомбил."/>
	<entry name="Orks/RedPaintJobFlavor" value="&lt;string name='Traits/RedPaintJobFlavor'/&gt;"/>
	<entry name="Orks/Scavenger2" value="Грот Мусорщик"/>
	<entry name="Orks/Scavenger2Description" value="Увеличивает количество подобранной руды при убийстве вражеских подразделений."/>
	<entry name="Orks/Scavenger2Flavor" value="Харашо арганизованый Пагонщик подготовит своих Гротов для сбора и сортировки металлолома для Орков, а также запретит им трогать более опасное имущество и боеприпасы."/>
	<entry name="Orks/SkorchaMissile" value="&lt;string name='Weapons/SkorchaMissile'/&gt;"/>
	<entry name="Orks/SkorchaMissileDescription" value="Позволяет жыгало-бомбилам запускать противопехотные ракеты, которые игнорируют укрытие."/>
	<entry name="Orks/SkorchaMissileFlavor" value="&lt;string name='Weapons/SkorchaMissileFlavor'/&gt;"/>
	<entry name="Orks/Stikkbomb" value="&lt;string name='Weapons/Stikkbomb'/&gt;"/>
	<entry name="Orks/StikkbombDescription" value="Позволяет пехоте бросать противопехотную гранату."/>
	<entry name="Orks/StikkbombFlavor" value="&lt;string name='Weapons/StikkbombFlavor'/&gt;"/>
	<entry name="Orks/TankbustaBomb" value="&lt;string name='Weapons/TankbustaBomb'/&gt;"/>
	<entry name="Orks/TankbustaBombDescription" value="Позволяет Танкобоям бросаться бронебойными бомбами."/>
	<entry name="Orks/TankbustaBombFlavor" value="&lt;string name='Weapons/TankbustaBombFlavor'/&gt;"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolter" value="Дополнительный тяжелый болтер"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterDescription" value="«Испепелители», «Экзорцисты» и «Каратели» получают дополнительный тяжелый болтер."/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterFlavor" value="&lt;string name='Weapons/HeavyBolterFlavor'/&gt;"/>
	<entry name="SistersOfBattle/AdditionalSacredRite" value="С удвоенной верой"/>
	<entry name="SistersOfBattle/AdditionalSacredRiteDescription" value="В одно время могут быть активны два Священнодействия."/>
	<entry name="SistersOfBattle/AdditionalSacredRiteFlavor" value="“Когда-то наши ритуальные хоралы были простыми — одноголосыми или многоголосыми гармониями, исполняемыми священным тоном. Сегодня мы сплетаем наши гимны воедино, усиливая их двойные темы, чтобы восхвалять Бога-Императора как можно большим количеством способов”.&lt;br/&gt; — безымянный летописец, «Евангелие Паутины»"/>
	<entry name="SistersOfBattle/AircraftsMissiles" value="Ракеты для авиации"/>
	<entry name="SistersOfBattle/AircraftsMissilesDescription" value="Наделяет истребители «Молния» ракетами Небесный удар, а штурмовики «Мститель» ракетами Адский удар."/>
	<entry name="SistersOfBattle/AircraftsMissilesFlavor" value="Эти ракеты увеличивают эффективность летательных аппаратов против различных целей. Ракеты Адский удар отлично справляются с бронетехникой, а ракеты Небесный удар — с вражеской авиацией."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="&lt;string name='Traits/SistersOfBattle/AnchoriteSarcophagus'/&gt;"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Увеличивает броню Умертвителей."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="&lt;string name='Traits/SistersOfBattle/AnchoriteSarcophagusFlavor'/&gt;"/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="&lt;string name='Traits/SistersOfBattle/AssaultWeaponBonus'/&gt;"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Увеличивает бронебойность штурмового оружия."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="&lt;string name='Traits/SistersOfBattle/AssaultWeaponBonusFlavor'/&gt;"/>
	<entry name="SistersOfBattle/AvengeTheMartyrs" value="Месть за мучеников"/>
	<entry name="SistersOfBattle/AvengeTheMartyrsDescription" value="Увеличивает эффект Духа возмездия, который уменьшает потери морали."/>
	<entry name="SistersOfBattle/AvengeTheMartyrsFlavor" value="&lt;string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/&gt;"/>
	<entry name="SistersOfBattle/ChaffLauncher" value="Запуск обманки"/>
	<entry name="SistersOfBattle/ChaffLauncherDescription" value="Даёт истребителям «Молния» и штурмовикам «Мститель» возможность запускать обманки, которые повышают сокращение дистанц. урона."/>
	<entry name="SistersOfBattle/ChaffLauncherFlavor" value="&lt;string name='Actions/DispenseChaffFlavor'/&gt;"/>
	<entry name="SistersOfBattle/ChaseTheProfane" value="&lt;string name='Actions/SistersOfBattle/ChaseTheProfane'/&gt;"/>
	<entry name="SistersOfBattle/ChaseTheProfaneDescription" value="&lt;string name='Actions/SistersOfBattle/ChaseTheProfaneDescription'/&gt;"/>
	<entry name="SistersOfBattle/ChaseTheProfaneFlavor" value="&lt;string name='Actions/SistersOfBattle/ChaseTheProfaneFlavor'/&gt;"/>
	<entry name="SistersOfBattle/CityGrowth" value="&lt;string name='Traits/SistersOfBattle/CityGrowth'/&gt;"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Увеличивает темпы роста городов."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="&lt;string name='Traits/SistersOfBattle/CityGrowthFlavor'/&gt;"/>
	<entry name="SistersOfBattle/CityTier2" value="&lt;string name='Traits/SistersOfBattle/CityTier2'/&gt;"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Повышает радиус присоединения плиток к основному городу."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="&lt;string name='Traits/SistersOfBattle/CityTier2Flavor'/&gt;"/>
	<entry name="SistersOfBattle/CityTier3" value="&lt;string name='Traits/SistersOfBattle/CityTier3'/&gt;"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Повышает радиус присоединения плиток к основному городу."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="&lt;string name='Traits/SistersOfBattle/CityTier3Flavor'/&gt;"/>
	<entry name="SistersOfBattle/ConvictionOfFaith" value="&lt;string name='Actions/SistersOfBattle/ConvictionOfFaith'/&gt;"/>
	<entry name="SistersOfBattle/ConvictionOfFaithDescription" value="&lt;string name='Actions/SistersOfBattle/ConvictionOfFaithDescription'/&gt;"/>
	<entry name="SistersOfBattle/ConvictionOfFaithFlavor" value="&lt;string name='Actions/SistersOfBattle/ConvictionOfFaithFlavor'/&gt;"/>
	<entry name="SistersOfBattle/DozerBlade" value="&lt;string name='Traits/DozerBlade'/&gt;"/>
	<entry name="SistersOfBattle/DozerBladeDescription" value="Уменьшает «Испепелителям», «Экзорцистам» и «Карателям» штраф за движение по лесам и имперским руинам."/>
	<entry name="SistersOfBattle/DozerBladeFlavor" value="&lt;string name='Traits/DozerBladeFlavor'/&gt;"/>
	<entry name="SistersOfBattle/EternalCrusade" value="&lt;string name='Actions/SistersOfBattle/EternalCrusade'/&gt;"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="&lt;string name='Actions/SistersOfBattle/EternalCrusadeDescription'/&gt;"/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="&lt;string name='Actions/SistersOfBattle/EternalCrusadeFlavor'/&gt;"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Воздушные асы"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Повышает точность истребителей «Молния» и штурмовиков «Мститель»."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="&lt;string name='Traits/SistersOfBattle/ExpertFightersFlavor'/&gt;"/>
	<entry name="SistersOfBattle/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="SistersOfBattle/ExtraInfantryArmourDescription" value="Увеличивает броню пехоты."/>
	<entry name="SistersOfBattle/ExtraInfantryArmourFlavor" value="Силовая броня, которую носят Адепта Сороритас, изготовлена из толстых керамитовых пластин и основана на тех же архаичных системах, что и у братьев Адептус Астартес. Она обеспечивает ту же степень бронированности, но не включает более продвинутых систем поддержки и усиления, поскольку Сёстры Битвы неспособны напрямую взаимодействовать с доспехом, как это делают космодесантники."/>
	<entry name="SistersOfBattle/ExtraVehicleArmour" value="&lt;string name='Traits/ExtraVehicleArmour'/&gt;"/>
	<entry name="SistersOfBattle/ExtraVehicleArmourDescription" value="Увеличивает броню машин."/>
	<entry name="SistersOfBattle/ExtraVehicleArmourFlavor" value="Средства передвижения Адепта Сороритас являются демонстрацией веры, поэтому крайне важно поддерживать и защищать несомые ими артефакты, выставленные на обозрение. При должной защите технике будет легче прикрывать продвижение солдат Адепта Сороритас."/>
	<entry name="SistersOfBattle/FragGrenade" value="&lt;string name='Weapons/FragGrenade'/&gt;"/>
	<entry name="SistersOfBattle/FragGrenadeDescription" value="Предоставляет Сёстрам Битвы, канониссам, неприкосновенным целестинкам, сёстрам Диалогус, доминионкам, госпитальеркам, икононосицам, шагоходам «Парагон», воздаятельницам, Святой Целестине, сёстрам-репентиям и зефиримам способность бросать противопехотные гранаты."/>
	<entry name="SistersOfBattle/FragGrenadeFlavor" value="&lt;string name='Weapons/FragGrenadeFlavor'/&gt;"/>
	<entry name="SistersOfBattle/HammerOfWrath" value="&lt;string name='Traits/HammerOfWrath'/&gt;"/>
	<entry name="SistersOfBattle/HammerOfWrathDescription" value="Даёт рыцарям-копейщикам «Церастус», Умертвителям, шагоходам «Парагон», Святой Целестине и зефиримам возможность выполнения более разрушительных атак."/>
	<entry name="SistersOfBattle/HammerOfWrathFlavor" value="&lt;string name='Traits/HammerOfWrathFlavor'/&gt;"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="&lt;string name='Traits/SistersOfBattle/HeavyWeaponBonus'/&gt;"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Увеличивает бронебойность тяжёлого оружия."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="&lt;string name='Traits/SistersOfBattle/HeavyWeaponBonusFlavor'/&gt;"/>
	<entry name="SistersOfBattle/HunterKillerMissile" value="&lt;string name='Weapons/HunterKillerMissile'/&gt;"/>
	<entry name="SistersOfBattle/HunterKillerMissileDescription" value="&lt;string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/&gt;"/>
	<entry name="SistersOfBattle/HunterKillerMissileFlavor" value="&lt;string name='Weapons/HunterKillerMissileFlavor'/&gt;"/>
	<entry name="SistersOfBattle/KrakGrenade" value="&lt;string name='Weapons/KrakGrenade'/&gt;"/>
	<entry name="SistersOfBattle/KrakGrenadeDescription" value="Предоставляет Сёстрам Битвы, канониссам, сёстрам Диалогус, доминионкам, госпитальеркам, икононосицам, воздаятельницам, сёстрам-репентиям и зефиримам способность бросать противотанковые гранаты."/>
	<entry name="SistersOfBattle/KrakGrenadeFlavor" value="&lt;string name='Weapons/KrakGrenadeFlavor'/&gt;"/>
	<entry name="SistersOfBattle/LaudHailer" value="&lt;string name='Traits/SistersOfBattle/LaudHailer'/&gt;"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="Даёт «Карателям», «Экзорцистам» и «Испепелителям» ауру, позволяющую соседним подразделениям Адепта Сороритас применять Акты веры."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="&lt;string name='Traits/SistersOfBattle/LaudHailerFlavor'/&gt;"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="&lt;string name='Traits/SistersOfBattle/MedicusMinistorum'/&gt;"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Даёт сокращение урона нечувствительностью к боли союзным подразделениям пехоты, соседним с госпитальерками."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="&lt;string name='Traits/SistersOfBattle/MedicusMinistorumFlavor'/&gt;"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="&lt;string name='Traits/SistersOfBattle/MeleeWeaponBonus'/&gt;"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="&lt;string name='Traits/SistersOfBattle/MeleeWeaponBonusFlavor'/&gt;"/>
	<entry name="SistersOfBattle/MeltaBomb" value="&lt;string name='Weapons/MeltaBomb'/&gt;"/>
	<entry name="SistersOfBattle/MeltaBombDescription" value="Предоставляет Сёстрам Битвы, канониссам, сёстрам Диалогус, доминионкам, воздаятельницам, сёстрам-репентиям и зефиримам возможность применения мелта-бомбы, очень эффективной против тяжёлой техники и укреплений."/>
	<entry name="SistersOfBattle/MeltaBombFlavor" value="&lt;string name='Weapons/MeltaBombFlavor'/&gt;"/>
	<entry name="SistersOfBattle/MinistorumIndoctrination" value="Индоктринация Министорум"/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationDescription" value="Наделяет истребители «Молния», штурмовики «Мститель» и рыцарей-копейщиков «Церастус» свойством Дух мученика."/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationFlavor" value="“После долгого знакомства с нашими ритуалами и молитвами наши союзники по нелёгкой судьбе — Имперский флот и горстка выживших имперских рыцарей — стали принимать участие в наших обрядах и перенимать наши верования. Похоже, вера заразительна”.&lt;br/&gt; — безымянный летописец, «Евангелие Паутины»"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="&lt;string name='Actions/SistersOfBattle/NonMilitantMobilisation'/&gt;"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="&lt;string name='Actions/SistersOfBattle/NonMilitantMobilisationDescription'/&gt;"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="&lt;string name='Actions/SistersOfBattle/NonMilitantMobilisationFlavor'/&gt;"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="&lt;string name='Actions/SistersOfBattle/PurifyingRecitations'/&gt;"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="&lt;string name='Actions/SistersOfBattle/PurifyingRecitationsDescription'/&gt;"/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="&lt;string name='Actions/SistersOfBattle/PurifyingRecitationsFlavor'/&gt;"/>
	<entry name="SistersOfBattle/RagingFervour" value="&lt;string name='Actions/SistersOfBattle/RagingFervour'/&gt;"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="&lt;string name='Actions/SistersOfBattle/RagingFervourDescription'/&gt;"/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="&lt;string name='Actions/SistersOfBattle/RagingFervourFlavor'/&gt;"/>
	<entry name="SistersOfBattle/RitualizedCeremonies" value="Ритуализированные церемонии"/>
	<entry name="SistersOfBattle/RitualizedCeremoniesDescription" value="Снижает затраты на Священнодействия."/>
	<entry name="SistersOfBattle/RitualizedCeremoniesFlavor" value="“Сначала мы молились на ходу, в моменты отдыха, в развалинах или пещерах. Со временем, когда устоявшийся конфликт перерос в бесконечную, изнуряющую войну, наши ритуалы и молитвы тоже устоялись, обретя режим и рутину”.&lt;br/&gt; — безымянный летописец, «Евангелие Паутины»"/>
	<entry name="SistersOfBattle/SacralVigor" value="&lt;string name='Actions/SistersOfBattle/SacralVigor'/&gt;"/>
	<entry name="SistersOfBattle/SacralVigorDescription" value="&lt;string name='Actions/SistersOfBattle/SacralVigorDescription'/&gt;"/>
	<entry name="SistersOfBattle/SacralVigorFlavor" value="&lt;string name='Actions/SistersOfBattle/SacralVigorFlavor'/&gt;"/>
	<entry name="SistersOfBattle/SanctifiedWorld" value="Миссионерский мир"/>
	<entry name="SistersOfBattle/SanctifiedWorldDescription" value="Повышает бонус лояльности от Конвента веры."/>
	<entry name="SistersOfBattle/SanctifiedWorldFlavor" value="Большинство Орденов несут свет Бога-Императора в миры далеко за пределами своих основных святилищ, создавая миссии и вспомогательные часовни, помогающие расширить влияние Ордена и Экклезиархии."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="&lt;string name='Traits/SistersOfBattle/SimulacrumImperialis'/&gt;"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="&lt;string name='Traits/SistersOfBattle/SimulacrumImperialisDescription'/&gt;"/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="&lt;string name='Traits/SistersOfBattle/SimulacrumImperialisFlavor'/&gt;"/>
	<entry name="SistersOfBattle/SisterSuperior" value="&lt;string name='Traits/SistersOfBattle/SisterSuperior'/&gt;"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="&lt;string name='Traits/SistersOfBattle/SisterSuperiorDescription'/&gt;"/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="&lt;string name='Traits/SistersOfBattle/SisterSuperiorFlavor'/&gt;"/>
	<entry name="SistersOfBattle/SmokeLauncher" value="&lt;string name='Upgrades/SmokeLauncher'/&gt;"/>
	<entry name="SistersOfBattle/SmokeLauncherDescription" value="&lt;string name='Upgrades/SmokeLauncherDescription'/&gt;"/>
	<entry name="SistersOfBattle/SmokeLauncherFlavor" value="&lt;string name='Upgrades/SmokeLauncherFlavor'/&gt;"/>
	<entry name="SistersOfBattle/UniversalCatechism" value="Вселенский катехизис"/>
	<entry name="SistersOfBattle/UniversalCatechismDescription" value="Наделяет истребители «Молния», штурмовики «Мститель» и рыцарей-копейщиков «Церастус» свойством Щит веры."/>
	<entry name="SistersOfBattle/UniversalCatechismFlavor" value="Имперские солдаты, реквизированные для Войн веры, в конце концов зачастую начинают молиться бок о бок с сёстрами Адепта Сороритас перед каждым сражением, ища наставление в своих убеждениях."/>
	<entry name="SistersOfBattle/VengefulSpirit" value="&lt;string name='Actions/SistersOfBattle/VengefulSpirit'/&gt;"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="&lt;string name='Actions/SistersOfBattle/VengefulSpiritDescription'/&gt;"/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="&lt;string name='Actions/SistersOfBattle/VengefulSpiritFlavor'/&gt;"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrder" value="Обет ордена-милитант"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderDescription" value="Подразделения не теряют Щит веры, если сломлены."/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderFlavor" value="&lt;string name='Traits/SistersOfBattle/ShieldOfFaithFlavor'/&gt;"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="&lt;string name='Actions/SistersOfBattle/WarmachinesWrath'/&gt;"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="&lt;string name='Actions/SistersOfBattle/WarmachinesWrathDescription'/&gt;"/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="&lt;string name='Actions/SistersOfBattle/WarmachinesWrathFlavor'/&gt;"/>
	<entry name="SpaceMarines/AssaultDoctrine" value="&lt;string name='Traits/AssaultDoctrine'/&gt;"/>
	<entry name="SpaceMarines/AssaultDoctrineDescription" value="&lt;string name='Actions/AssaultDoctrineDescription'/&gt;"/>
	<entry name="SpaceMarines/AssaultDoctrineFlavor" value="&lt;string name='Traits/AssaultDoctrineFlavor'/&gt;"/>
	<entry name="SpaceMarines/BlastDamage" value="&lt;string name='Traits/SpaceMarines/BlastDamage'/&gt;"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="Увеличивает бронебойность гранат, ракет и разрывного вооружения."/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="&lt;string name='Traits/SpaceMarines/BlastDamageFlavor'/&gt;"/>
	<entry name="SpaceMarines/BolsterDefences" value="&lt;string name='Actions/BolsterDefences'/&gt;"/>
	<entry name="SpaceMarines/BolsterDefencesDescription" value="Даёт пушкам Громобой способность повышать сокращение дистанц. урона на указанной плитке."/>
	<entry name="SpaceMarines/BolsterDefencesFlavor" value="&lt;string name='Actions/BolsterDefencesFlavor'/&gt;"/>
	<entry name="SpaceMarines/BoltDamage" value="&lt;string name='Traits/SpaceMarines/BoltDamage'/&gt;"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="&lt;string name='Upgrades/AstraMilitarum/BoltDamageDescription'/&gt;"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="&lt;string name='Traits/SpaceMarines/BoltDamageFlavor'/&gt;"/>
	<entry name="SpaceMarines/BolterDrill" value="&lt;string name='Traits/BolterDrill'/&gt;"/>
	<entry name="SpaceMarines/BolterDrillDescription" value="&lt;string name='Actions/BolterDrillDescription'/&gt;"/>
	<entry name="SpaceMarines/BolterDrillFlavor" value="&lt;string name='Traits/BolterDrillFlavor'/&gt;"/>
	<entry name="SpaceMarines/CeramitePlating" value="&lt;string name='Traits/CeramitePlating'/&gt;"/>
	<entry name="SpaceMarines/CeramitePlatingDescription" value="Повышает броню Грозовому когтю и Грозовому ворону."/>
	<entry name="SpaceMarines/CeramitePlatingFlavor" value="&lt;string name='Traits/CeramitePlatingFlavor'/&gt;"/>
	<entry name="SpaceMarines/ChapterUnity" value="&lt;string name='Traits/ChapterUnity'/&gt;"/>
	<entry name="SpaceMarines/ChapterUnityDescription" value="Повышает лояльность Великого зала."/>
	<entry name="SpaceMarines/ChapterUnityFlavor" value="&lt;string name='Traits/ChapterUnityFlavor'/&gt;"/>
	<entry name="SpaceMarines/CityTier2" value="&lt;string name='Traits/SpaceMarines/CityTier2'/&gt;"/>
	<entry name="SpaceMarines/CityTier2Description" value="Повышает радиус присоединения плиток к городу."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="&lt;string name='Traits/SpaceMarines/CityTier2Flavor'/&gt;"/>
	<entry name="SpaceMarines/CityTier3" value="&lt;string name='Traits/SpaceMarines/CityTier3'/&gt;"/>
	<entry name="SpaceMarines/CityTier3Description" value="&lt;string name='Traits/SpaceMarines/CityTier2Description'/&gt;"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="&lt;string name='Traits/SpaceMarines/CityTier3Flavor'/&gt;"/>
	<entry name="SpaceMarines/CityTier4" value="&lt;string name='Traits/SpaceMarines/CityTier4'/&gt;"/>
	<entry name="SpaceMarines/CityTier4Description" value="&lt;string name='Traits/SpaceMarines/CityTier2Description'/&gt;"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="&lt;string name='Traits/SpaceMarines/CityTier4Flavor'/&gt;"/>
	<entry name="SpaceMarines/ClusterMines" value="&lt;string name='Weapons/ClusterMines'/&gt;"/>
	<entry name="SpaceMarines/ClusterMinesDescription" value="Даёт Мотоциклетному отделению скаутов возможность устанавливать кластерные мины."/>
	<entry name="SpaceMarines/ClusterMinesFlavor" value="&lt;string name='Weapons/ClusterMinesFlavor'/&gt;"/>
	<entry name="SpaceMarines/CombatShield" value="&lt;string name='Traits/CombatShield'/&gt;"/>
	<entry name="SpaceMarines/CombatShieldDescription" value="Повышает сокращение урона Штурмовикам Космодесанта."/>
	<entry name="SpaceMarines/CombatShieldFlavor" value="&lt;string name='Traits/CombatShieldFlavor'/&gt;"/>
	<entry name="SpaceMarines/DevastatorDoctrine" value="&lt;string name='Traits/DevastatorDoctrine'/&gt;"/>
	<entry name="SpaceMarines/DevastatorDoctrineDescription" value="&lt;string name='Actions/DevastatorDoctrineDescription'/&gt;"/>
	<entry name="SpaceMarines/DevastatorDoctrineFlavor" value="&lt;string name='Traits/DevastatorDoctrineFlavor'/&gt;"/>
	<entry name="SpaceMarines/DozerBlade" value="&lt;string name='Traits/DozerBlade'/&gt;"/>
	<entry name="SpaceMarines/DozerBladeDescription" value="Уменьшает Охотникам, Хищникам, Секачам и Вихрям штраф за движение по лесам и имперским руинам."/>
	<entry name="SpaceMarines/DozerBladeFlavor" value="&lt;string name='Traits/DozerBladeFlavor'/&gt;"/>
	<entry name="SpaceMarines/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="SpaceMarines/ExtraInfantryArmourDescription" value="&lt;string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/&gt;"/>
	<entry name="SpaceMarines/ExtraInfantryArmourFlavor" value="Хотя Печати чистоты вполне могут помочь Адептус Астартес дольше продержаться на поле боя, вера не остановит всех летящих в них болтов. Модернизация до более поздних версий их Силовой Брони позволит пехоте Космического Десанта значительно повысить их живучесть."/>
	<entry name="SpaceMarines/ExtraVehicleArmour" value="&lt;string name='Traits/ExtraVehicleArmour'/&gt;"/>
	<entry name="SpaceMarines/ExtraVehicleArmourDescription" value="&lt;string name='Upgrades/AstraMilitarum/ExtraVehicleArmourDescription'/&gt;"/>
	<entry name="SpaceMarines/ExtraVehicleArmourFlavor" value="После того как истинные противники были разбиты и возданы благодарственные молитвы, Технодесантники вполне могут внести некоторые незначительные изменения в свою технику, конечно в соответствии с Кодексом Астартес, чтобы улучшить её выживаемость."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Щит твердыни"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Даёт Крепости Искупления неснижаемое сокращение урона."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="&lt;string name='Traits/SpaceMarines/FortressOfRedemptionDamageReductionFlavor'/&gt;"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSilo" value="Ракетная башня Крепости Искупления"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloDescription" value="Добавляет Крепости Искупления ракеты Кракшторм."/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloFlavor" value="&lt;string name='Weapons/KrakstormMissileSiloFlavor'/&gt;"/>
	<entry name="SpaceMarines/FragGrenade" value="&lt;string name='Weapons/FragGrenade'/&gt;"/>
	<entry name="SpaceMarines/FragGrenadeDescription" value="Предоставляет Апотекариям, Штурмовикам Космодесанта, Капитанам, Опустошителям Космодесанта, Библиариям, Скаутам, Мото отделениям скаутов, Тактическим отделениям и Пушкам Громобой возможность бросать противопехотные гранаты."/>
	<entry name="SpaceMarines/FragGrenadeFlavor" value="&lt;string name='Weapons/FragGrenadeFlavor'/&gt;"/>
	<entry name="SpaceMarines/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="SpaceMarines/HammerOfWrathDescription" value="Дает Штурмовикам Космодесанта, Дредноутам и Мотоциклетному отделению скаутов возможность выполнения более разрушительных атак."/>
	<entry name="SpaceMarines/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="SpaceMarines/HunterKillerMissile" value="&lt;string name='Weapons/HunterKillerMissile'/&gt;"/>
	<entry name="SpaceMarines/HunterKillerMissileDescription" value="&lt;string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/&gt;"/>
	<entry name="SpaceMarines/HunterKillerMissileFlavor" value="&lt;string name='Weapons/HunterKillerMissileFlavor'/&gt;"/>
	<entry name="SpaceMarines/HurricaneBolter" value="Болтер Ураган"/>
	<entry name="SpaceMarines/HurricaneBolterDescription" value="Добавляет Болтерную установку Ураган Грозовому ворону."/>
	<entry name="SpaceMarines/HurricaneBolterFlavor" value="Впервые использованная Орденом Черных Храмовников, болтерная установка Ураган объединяет карающую огневую мощь нескольких Сдвоенных болтеров, чтобы посылать по-настоящему испепеляющий шквал снарядов."/>
	<entry name="SpaceMarines/KrakGrenade" value="&lt;string name='Weapons/KrakGrenade'/&gt;"/>
	<entry name="SpaceMarines/KrakGrenadeDescription" value="Предоставляет Апотекариям, Штурмовикам Космодесанта, Капитанам, Опустошителям Космодесанта, Библиариям, Скаутам, Мото отделениям скаутов, Тактическим отделениям и Пушкам Громобой возможность бросать противотанковые гранаты."/>
	<entry name="SpaceMarines/KrakGrenadeFlavor" value="&lt;string name='Weapons/KrakGrenadeFlavor'/&gt;"/>
	<entry name="SpaceMarines/LandSpeederMultiMelta" value="&lt;string name='Weapons/MultiMelta'/&gt;"/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaDescription" value="Оснащение Лэндспидеров и Лендрейдеров Мульти-Мелтой."/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaFlavor" value="&lt;string name='Weapons/MultiMeltaFlavor'/&gt;"/>
	<entry name="SpaceMarines/LasDamage" value="&lt;string name='Traits/SpaceMarines/LasDamage'/&gt;"/>
	<entry name="SpaceMarines/LasDamageDescription" value="&lt;string name='Upgrades/AstraMilitarum/LasDamageDescription'/&gt;"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="&lt;string name='Traits/SpaceMarines/LasDamageFlavor'/&gt;"/>
	<entry name="SpaceMarines/LastStand" value="&lt;string name='Traits/LastStand'/&gt;"/>
	<entry name="SpaceMarines/LastStandDescription" value="Повышает мораль для всех подразделений Космического Десанта."/>
	<entry name="SpaceMarines/LastStandFlavor" value="&lt;string name='Traits/LastStandFlavor'/&gt;"/>
	<entry name="SpaceMarines/LastStandReady" value="&lt;string name='Traits/LastStand'/&gt;"/>
	<entry name="SpaceMarines/LocatorBeacon" value="&lt;string name='Traits/LocatorBeacon'/&gt;"/>
	<entry name="SpaceMarines/LocatorBeaconDescription" value="При орбитальном развертывании не используются очки действия при развертывании подразделений рядом с Мото отделениями скаутов или Грозовыми воронами."/>
	<entry name="SpaceMarines/LocatorBeaconFlavor" value="&lt;string name='Traits/LocatorBeaconFlavor'/&gt;"/>
	<entry name="SpaceMarines/MachineEmpathy" value="&lt;string name='Traits/MachineEmpathy'/&gt;"/>
	<entry name="SpaceMarines/MachineEmpathyDescription" value="&lt;string name='Actions/MachineEmpathyDescription'/&gt;"/>
	<entry name="SpaceMarines/MachineEmpathyFlavor" value="&lt;string name='Actions/MachineEmpathyFlavor'/&gt;"/>
	<entry name="SpaceMarines/MeleeDamage" value="&lt;string name='Traits/SpaceMarines/MeleeDamage'/&gt;"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="&lt;string name='Traits/SpaceMarines/MeleeDamageFlavor'/&gt;"/>
	<entry name="SpaceMarines/MeltaBomb" value="&lt;string name='Weapons/MeltaBomb'/&gt;"/>
	<entry name="SpaceMarines/MeltaBombDescription" value="Позволяет Тактическим отделениям, Штурмовикам, Опустошителям, Скаутам и Мото отделениям скаутов Космодесанта возможность устанавливать мелта-бомбу, которая очень эффективна против тяжёлой техники и укреплений."/>
	<entry name="SpaceMarines/MeltaBombFlavor" value="&lt;string name='Weapons/MeltaBombFlavor'/&gt;"/>
	<entry name="SpaceMarines/Omniscope" value="&lt;string name='Actions/SpaceMarines/Omniscope'/&gt;"/>
	<entry name="SpaceMarines/OmniscopeDescription" value="Даёт Центурионам Опустошителей возможность обходить сокращение дистанц. урона."/>
	<entry name="SpaceMarines/OmniscopeFlavor" value="&lt;string name='Actions/SpaceMarines/OmniscopeFlavor'/&gt;"/>
	<entry name="SpaceMarines/OrbitalBombardment" value="&lt;string name='Actions/OrbitalBombardment'/&gt;"/>
	<entry name="SpaceMarines/OrbitalBombardmentDescription" value="&lt;string name='Actions/OrbitalBombardmentDescription'/&gt;"/>
	<entry name="SpaceMarines/OrbitalBombardmentFlavor" value="&lt;string name='Actions/OrbitalBombardmentFlavor'/&gt;"/>
	<entry name="SpaceMarines/OrbitalDeployment" value="&lt;string name='Actions/OrbitalDeployment'/&gt;"/>
	<entry name="SpaceMarines/OrbitalDeploymentDescription" value="Дает подразделениям возможность развертываться через Десантную капсулу в любом месте на поле боя."/>
	<entry name="SpaceMarines/OrbitalDeploymentFlavor" value="&lt;string name='Actions/OrbitalDeploymentFlavor'/&gt;"/>
	<entry name="SpaceMarines/OrbitalScan" value="&lt;string name='Actions/OrbitalScan'/&gt;"/>
	<entry name="SpaceMarines/OrbitalScanDescription" value="&lt;string name='Actions/OrbitalScanDescription'/&gt;"/>
	<entry name="SpaceMarines/OrbitalScanFlavor" value="&lt;string name='Actions/OrbitalScanFlavor'/&gt;"/>
	<entry name="SpaceMarines/PredatorLascannon" value="Дополнительные Тяжелые болтеры"/>
	<entry name="SpaceMarines/PredatorLascannonDescription" value="Даёт Хищникам, Крепостям Искупления и Мегапушкам Аквила дополнительные тяжелые болтеры."/>
	<entry name="SpaceMarines/PredatorLascannonFlavor" value="&lt;string name='Weapons/HeavyBolterFlavor'/&gt;"/>
	<entry name="SpaceMarines/SiegeMasters" value="&lt;string name='Traits/SiegeMasters'/&gt;"/>
	<entry name="SpaceMarines/SiegeMastersDescription" value="&lt;string name='Actions/SiegeMastersDescription'/&gt;"/>
	<entry name="SpaceMarines/SiegeMastersFlavor" value="&lt;string name='Actions/SiegeMastersFlavor'/&gt;"/>
	<entry name="SpaceMarines/SiegeShield" value="&lt;string name='Traits/SiegeShield'/&gt;"/>
	<entry name="SpaceMarines/SiegeShieldDescription" value="Увеличивает броню Поборников и уменьшает штраф за движение по лесам и имперским руинам."/>
	<entry name="SpaceMarines/SiegeShieldFlavor" value="&lt;string name='Traits/SiegeShieldFlavor'/&gt;"/>
	<entry name="SpaceMarines/Signum" value="&lt;string name='Actions/Signum'/&gt;"/>
	<entry name="SpaceMarines/SignumDescription" value="Даёт Опустошителям Космодесанта возможность снимать штрафы за тяжелое, артиллерийское и залповое оружие."/>
	<entry name="SpaceMarines/SignumFlavor" value="&lt;string name='Actions/SignumFlavor'/&gt;"/>
	<entry name="SpaceMarines/SmokeLauncher" value="&lt;string name='Upgrades/SmokeLauncher'/&gt;"/>
	<entry name="SpaceMarines/SmokeLauncherDescription" value="&lt;string name='Upgrades/SmokeLauncherDescription'/&gt;"/>
	<entry name="SpaceMarines/SmokeLauncherFlavor" value="&lt;string name='Upgrades/SmokeLauncherFlavor'/&gt;"/>
	<entry name="SpaceMarines/TacticalDoctrine" value="&lt;string name='Traits/TacticalDoctrine'/&gt;"/>
	<entry name="SpaceMarines/TacticalDoctrineDescription" value="&lt;string name='Actions/TacticalDoctrineDescription'/&gt;"/>
	<entry name="SpaceMarines/TacticalDoctrineFlavor" value="&lt;string name='Actions/TacticalDoctrineFlavor'/&gt;"/>
	<entry name="SpaceMarines/TeleportHomer" value="&lt;string name='Traits/TeleportHomer'/&gt;"/>
	<entry name="SpaceMarines/TeleportHomerDescription" value="При орбитальном развертывании не используются очки действия при развертывании Капеллана, Штурмовых терминаторов и Терминаторов рядом с Тактическим отделением Космического Десанта или Скаутами."/>
	<entry name="SpaceMarines/TeleportHomerFlavor" value="&lt;string name='Traits/TeleportHomerFlavor'/&gt;"/>
	<entry name="SpaceMarines/TheFleshIsWeak" value="&lt;string name='Traits/TheFleshIsWeak'/&gt;"/>
	<entry name="SpaceMarines/TheFleshIsWeakDescription" value="&lt;string name='Actions/TheFleshIsWeakDescription'/&gt;"/>
	<entry name="SpaceMarines/TheFleshIsWeakFlavor" value="&lt;string name='Actions/TheFleshIsWeakFlavor'/&gt;"/>
	<entry name="Tau/AdvancedTargetingSystem" value="&lt;string name='Traits/Tau/AdvancedTargetingSystem'/&gt;"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Система поддержки боевых скафандров и модернизация транспортных средств, повышающая дистанц. точность."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="&lt;string name='Traits/Tau/AdvancedTargetingSystemFlavor'/&gt;"/>
	<entry name="Tau/AutomatedRepairSystem" value="&lt;string name='Traits/Tau/AutomatedRepairSystem'/&gt;"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Позволяет машинам восстанавливать очки здоровья каждый ход."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="&lt;string name='Traits/Tau/AutomatedRepairSystemFlavor'/&gt;"/>
	<entry name="Tau/BlacksunFilter" value="&lt;string name='Traits/Tau/BlacksunFilter'/&gt;"/>
	<entry name="Tau/BlacksunFilterDescription" value="Увеличивает радиус обзора машин, Эфирных, Командующего, Следопытов и бронескафандров."/>
	<entry name="Tau/BlacksunFilterFlavor" value="&lt;string name='Traits/Tau/BlacksunFilterFlavor'/&gt;"/>
	<entry name="Tau/BlastDamage" value="&lt;string name='Traits/Tau/BlastDamage'/&gt;"/>
	<entry name="Tau/BlastDamageDescription" value="Увеличивает бронебойность огнемётного и ракетного оружия."/>
	<entry name="Tau/BlastDamageFlavor" value="&lt;string name='Traits/Tau/BlastDamageFlavor'/&gt;"/>
	<entry name="Tau/BoltDamage" value="&lt;string name='Traits/Tau/BoltDamage'/&gt;"/>
	<entry name="Tau/BoltDamageDescription" value="Увеличивает бронебойность скорострельного и рельсового оружия."/>
	<entry name="Tau/BoltDamageFlavor" value="&lt;string name='Traits/Tau/BoltDamageFlavor'/&gt;"/>
	<entry name="Tau/BondingKnifeRitual" value="&lt;string name='Actions/Tau/BondingKnifeRitual'/&gt;"/>
	<entry name="Tau/BondingKnifeRitualDescription" value="Предоставляет Воинам Огня, Командам прорыва воинов Огня, Следопытам, БСК XV25 Невидимка, БСК XV8 Кризис, БСК XV8 Залп, БСК XV95 Фантом и БСК XV104 Быстрина возможность восстановить свою мораль."/>
	<entry name="Tau/BondingKnifeRitualFlavor" value="&lt;string name='Actions/Tau/BondingKnifeRitualFlavor'/&gt;"/>
	<entry name="Tau/CityTier2" value="&lt;string name='Traits/Tau/CityTier2'/&gt;"/>
	<entry name="Tau/CityTier2Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="Tau/CityTier2Flavor" value="&lt;string name='Traits/Tau/CityTier2Flavor'/&gt;"/>
	<entry name="Tau/CityTier3" value="&lt;string name='Traits/Tau/CityTier3'/&gt;"/>
	<entry name="Tau/CityTier3Description" value="&lt;string name='Upgrades/AstraMilitarum/CityTier2Description'/&gt;"/>
	<entry name="Tau/CityTier3Flavor" value="&lt;string name='Traits/Tau/CityTier3Flavor'/&gt;"/>
	<entry name="Tau/CounterfireDefenceSystem" value="&lt;string name='Traits/Tau/CounterfireDefenceSystem'/&gt;"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Система поддержки боевых скафандров, повышающая точность атак из дозора."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="&lt;string name='Traits/Tau/CounterfireDefenceSystemFlavor'/&gt;"/>
	<entry name="Tau/DisruptionPod" value="&lt;string name='Traits/Tau/DisruptionPod'/&gt;"/>
	<entry name="Tau/DisruptionPodDescription" value="Повышает сокращение дистанц. урона технике."/>
	<entry name="Tau/DisruptionPodFlavor" value="&lt;string name='Traits/Tau/DisruptionPodFlavor'/&gt;"/>
	<entry name="Tau/DroneController" value="&lt;string name='Traits/Tau/DroneController'/&gt;"/>
	<entry name="Tau/DroneControllerDescription" value="Система поддержки боевого скафандра, повышающая точность от соседних дронов."/>
	<entry name="Tau/DroneControllerFlavor" value="&lt;string name='Traits/Tau/DroneControllerFlavor'/&gt;"/>
	<entry name="Tau/EMPGrenade" value="&lt;string name='Weapons/EMPGrenade'/&gt;"/>
	<entry name="Tau/EMPGrenadeDescription" value="Дает Воинам Огня, Командам прорыва воинов Огня и Следопытам возможность метать противотанковые гранаты."/>
	<entry name="Tau/EMPGrenadeFlavor" value="&lt;string name='Weapons/EMPGrenadeFlavor'/&gt;"/>
	<entry name="Tau/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="Tau/ExtraInfantryArmourDescription" value="Увеличивает броню пехоты и Монструозных созданий."/>
	<entry name="Tau/ExtraInfantryArmourFlavor" value="Дальнейшее изучение хитинового панциря наших Траксийских товарищей выявило существенные структурные улучшения, которые мы можем внести в состав брони наших Кадров и боевых скафандров."/>
	<entry name="Tau/ExtraVehicleArmour" value="&lt;string name='Traits/ExtraVehicleArmour'/&gt;"/>
	<entry name="Tau/ExtraVehicleArmourDescription" value="Увеличивает броню машин."/>
	<entry name="Tau/ExtraVehicleArmourFlavor" value="В отличие от застойных Техножрецов Марса, каста Земли бесконечно изобретает и творит. Фио'так-это материал, который является настоящим пиком их творчества, твердый, сверхплотный, нанокристаллический металлический сплав, который они используют экономно в своих лучших творениях."/>
	<entry name="Tau/FlechetteDischarger" value="&lt;string name='Traits/Tau/FlechetteDischarger'/&gt;"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Предоставляет технике возможность наносить урон нападающим в ближнем бою."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="&lt;string name='Traits/Tau/FlechetteDischargerFlavor'/&gt;"/>
	<entry name="Tau/ForTheGreaterGoodBonus" value="Споровые послы Харпактин"/>
	<entry name="Tau/ForTheGreaterGoodBonusDescription" value="Снижает стоимость влияния за Высшее Благо."/>
	<entry name="Tau/ForTheGreaterGoodBonusFlavor" value="Когда наши дипломаты из касты Воды находятся на поле боя, они иногда находят полезным взять с собой наших грибовидных союзников Харпактинов, чьи стробирующие ультрафиолетовые сообщения оказывают успокаивающее, почти гипнотическое воздействие почти на все расы."/>
	<entry name="Tau/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="Tau/HammerOfWrathDescription" value="Предоставляет БСК XV95 Фантом, БСК XV104 Быстрина, БСК XV107 Р'Варна и БСК KV128 Нагон возможность выполнять более разрушительные атаки."/>
	<entry name="Tau/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="Tau/LasDamage" value="&lt;string name='Traits/Tau/LasDamage'/&gt;"/>
	<entry name="Tau/LasDamageDescription" value="Увеличивает бронебойность фузионного, ионного, плазменного и импульсного оружия."/>
	<entry name="Tau/LasDamageFlavor" value="&lt;string name='Traits/Tau/LasDamageFlavor'/&gt;"/>
	<entry name="Tau/PhotonGrenade" value="&lt;string name='Weapons/PhotonGrenade'/&gt;"/>
	<entry name="Tau/PhotonGrenadeDescription" value="Дает Воинам Огня, Командам прорыва воинов Огня, Следопытам и Кадровому Огненному клинку возможность метать ослепляющие гранаты."/>
	<entry name="Tau/PhotonGrenadeFlavor" value="&lt;string name='Weapons/PhotonGrenadeFlavor'/&gt;"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="&lt;string name='Traits/Tau/PointDefenceTargetingRelay'/&gt;"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Машины наносят больше урона атаками из дозора врагам, рядом с которыми есть другие дружественные отряды."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="&lt;string name='Traits/Tau/PointDefenceTargetingRelayFlavor'/&gt;"/>
	<entry name="Tau/PurchaseEnergy" value="&lt;string name='Actions/Tau/PurchaseEnergy'/&gt;"/>
	<entry name="Tau/PurchaseEnergyDescription" value="Дает возможность приобретать энергию за влияние."/>
	<entry name="Tau/PurchaseEnergyFlavor" value="&lt;string name='Actions/Tau/PurchaseEnergyFlavor'/&gt;"/>
	<entry name="Tau/PurchaseFood" value="&lt;string name='Actions/Tau/PurchaseFood'/&gt;"/>
	<entry name="Tau/PurchaseFoodDescription" value="Предоставляет возможность покупать еду за влияние."/>
	<entry name="Tau/PurchaseFoodFlavor" value="&lt;string name='Actions/Tau/PurchaseFoodFlavor'/&gt;"/>
	<entry name="Tau/PurchaseOre" value="&lt;string name='Actions/Tau/PurchaseOre'/&gt;"/>
	<entry name="Tau/PurchaseOreDescription" value="Дает возможность покупать руду за влияние."/>
	<entry name="Tau/PurchaseOreFlavor" value="&lt;string name='Actions/Tau/PurchaseOreFlavor'/&gt;"/>
	<entry name="Tau/PurchasePopulation" value="&lt;string name='Actions/Tau/PurchasePopulation'/&gt;"/>
	<entry name="Tau/PurchasePopulationDescription" value="Предоставляет возможность приобретать население за влияние."/>
	<entry name="Tau/PurchasePopulationFlavor" value="&lt;string name='Actions/Tau/PurchasePopulationFlavor'/&gt;"/>
	<entry name="Tau/PurchaseResearch" value="&lt;string name='Actions/Tau/PurchaseResearch'/&gt;"/>
	<entry name="Tau/PurchaseResearchDescription" value="Предоставляет возможность проводить исследования за влияние."/>
	<entry name="Tau/PurchaseResearchFlavor" value="&lt;string name='Actions/Tau/PurchaseResearchFlavor'/&gt;"/>
	<entry name="Tau/RipykaVa" value="&lt;string name='Traits/Tau/RipykaVa'/&gt;"/>
	<entry name="Tau/RipykaVaDescription" value="Уменьшает время перезарядки метастратегий Командующего."/>
	<entry name="Tau/RipykaVaFlavor" value="&lt;string name='Traits/Tau/RipykaVaFlavor'/&gt;"/>
	<entry name="Tau/SeekerMissile" value="&lt;string name='Weapons/SeekerMissile'/&gt;"/>
	<entry name="Tau/SeekerMissileDescription" value="Предоставляет машинам и БСК XV88 Залп возможность использовать ракетное оружие."/>
	<entry name="Tau/SeekerMissileFlavor" value="&lt;string name='Weapons/SeekerMissileFlavor'/&gt;"/>
	<entry name="Tau/SensorSpines" value="Сенсорные выступы"/>
	<entry name="Tau/SensorSpinesDescription" value="Предоставляет машинам возможность движения через укрытия."/>
	<entry name="Tau/SensorSpinesFlavor" value="Сенсорные шипы используются для получения данных с усовершенствованной наземной системы управления полетом, прокладывающую безопасные маршруты через коварный ландшафт, и позволяющей избежать ловушек и мин, которые могут быть скрыты от глаз."/>
	<entry name="Tau/ShieldGenerator" value="&lt;string name='Traits/Tau/ShieldGenerator'/&gt;"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Система поддержки боескафандра, которая даёт неснижаемое сокращение урона."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="&lt;string name='Traits/Tau/ShieldGeneratorFlavor'/&gt;"/>
	<entry name="Tau/StimulantInjector" value="&lt;string name='Traits/Tau/StimulantInjector'/&gt;"/>
	<entry name="Tau/StimulantInjectorDescription" value="Система поддержки боескафандра, которая повышает сокращение урона нечувствительностью к боли."/>
	<entry name="Tau/StimulantInjectorFlavor" value="&lt;string name='Traits/Tau/StimulantInjectorFlavor'/&gt;"/>
	<entry name="Tau/SubversionBonus" value="Исследование бунтарства"/>
	<entry name="Tau/SubversionBonusDescription" value="Повышает штраф на лояльность от Подрыва веры."/>
	<entry name="Tau/SubversionBonusFlavor" value="Проводя исследования сопротивляющихся, Т'ау нашли фразу в военном учебнике для людей 'думай, как твой враг'. Каста Воды приняла это близко к сердцу и тщательно изучила неудовлетворенные потребности и желания жителей данного поселения—будь то дрожащие рабы Некроны или изголодавшиеся Парни Орков."/>
	<entry name="Tau/TacticalSupportTurret" value="&lt;string name='Weapons/TacticalSupportTurret'/&gt;"/>
	<entry name="Tau/TacticalSupportTurretDescription" value="Дает Воинам Огня дополнительное оружие, когда они неподвижны."/>
	<entry name="Tau/TacticalSupportTurretFlavor" value="&lt;string name='Weapons/TacticalSupportTurretFlavor'/&gt;"/>
	<entry name="Tau/UtopiaBonus" value="&lt;string name='Traits/Tau/UtopiaBonus'/&gt;"/>
	<entry name="Tau/UtopiaBonusDescription" value="&lt;string name='Traits/Tau/UtopiaBonusDescription'/&gt;"/>
	<entry name="Tau/UtopiaBonusFlavor" value="&lt;string name='Traits/Tau/UtopiaBonusFlavor'/&gt;"/>
	<entry name="Tau/VectoredRetroThrusters" value="&lt;string name='Traits/Tau/VectoredRetroThrusters'/&gt;"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Система поддержки боевого скафандра, которая увеличивает движение и позволяет подразделению игнорировать зону контроля противника."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="&lt;string name='Traits/Tau/VectoredRetroThrustersFlavor'/&gt;"/>
	<entry name="Tau/VelocityTracker" value="&lt;string name='Traits/Tau/VelocityTracker'/&gt;"/>
	<entry name="Tau/VelocityTrackerDescription" value="Система поддержки боевого скафандра, повышающая дистанц. точность против летунов."/>
	<entry name="Tau/VelocityTrackerFlavor" value="&lt;string name='Traits/Tau/VelocityTrackerFlavor'/&gt;"/>
	<entry name="Tyranids/AcidBlood" value="Едкая кровь"/>
	<entry name="Tyranids/AcidBloodDescription" value="Даётся монструозным существам тиранидов и Тиранид-Прайму возможность наносить урон всем нападающим в ближнем бою."/>
	<entry name="Tyranids/AcidBloodFlavor" value="&lt;string name='Traits/Tyranids/AcidBloodFlavor'/&gt;"/>
	<entry name="Tyranids/AdrenalGlands" value="Адренальные железы"/>
	<entry name="Tyranids/AdrenalGlandsDescription" value="Повышает подразделению движение и ближний урон."/>
	<entry name="Tyranids/AdrenalGlandsFlavor" value="Адренальные железы насыщают тела своего хозяина химическими веществами, которые повышают метаболизм существа до гиперактивного безумного состояния."/>
	<entry name="Tyranids/BiomorphDamage" value="&lt;string name='Traits/Tyranids/BiomorphDamage'/&gt;"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Увеличивает бронебойность биоморфов."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="&lt;string name='Traits/Tyranids/BiomorphDamageFlavor'/&gt;"/>
	<entry name="Tyranids/BioPlasma" value="Биоплазма Карнифексов"/>
	<entry name="Tyranids/BioPlasmaDescription" value="Даёт Карнифексам дополнительное дистанционное оружие."/>
	<entry name="Tyranids/BioPlasmaFlavor" value="&lt;string name='Weapons/BioPlasmaFlavor'/&gt;"/>
	<entry name="Tyranids/BoneMace" value="Костяная булава Карнифекса"/>
	<entry name="Tyranids/BoneMaceDescription" value="Даёт Карнифексам дополнительное оружие ближнего боя."/>
	<entry name="Tyranids/BoneMaceFlavor" value="&lt;string name='Weapons/BoneMaceFlavor'/&gt;"/>
	<entry name="Tyranids/CityCost" value="Метаморфические Малантропы"/>
	<entry name="Tyranids/CityCostDescription" value="Снижает стоимость основания новых городов."/>
	<entry name="Tyranids/CityCostFlavor" value="Роль Малантропа в создании новых ульев Тиранидов была мало изучена Имперскими Генеторами, так как в большинстве случаев они либо умерают, либо уходят в другие области, когда улей основан. Тем не менее, считается, что они транспортируют споры городов в новые места, когда требуется изменить местоположение первого выводка. Иногда наблюдаются необычные Малантропы со специализированными физическими структурами, которые позволяют им выполнять эту задачу более эффективно."/>
	<entry name="Tyranids/CityDamage" value="&lt;string name='Traits/Tyranids/CityDamage'/&gt;"/>
	<entry name="Tyranids/CityDamageDescription" value="Вражеские отряды в городах Тиранид получают урон каждый ход."/>
	<entry name="Tyranids/CityDamageFlavor" value="&lt;string name='Traits/Tyranids/CityDamageFlavor'/&gt;"/>
	<entry name="Tyranids/CityGrowth" value="&lt;string name='Traits/Tyranids/CityGrowth'/&gt;"/>
	<entry name="Tyranids/CityGrowthDescription" value="Увеличивает скорость роста городов Тиранид."/>
	<entry name="Tyranids/CityGrowthFlavor" value="&lt;string name='Traits/Tyranids/CityGrowthFlavor'/&gt;"/>
	<entry name="Tyranids/CityLoyalty" value="&lt;string name='Traits/Tyranids/CityLoyalty'/&gt;"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Снижает штраф на лояльность за количество городов Тиранидов."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="&lt;string name='Traits/Tyranids/CityLoyaltyFlavor'/&gt;"/>
	<entry name="Tyranids/CityPopulationLimit" value="&lt;string name='Traits/Tyranids/CityPopulationLimit'/&gt;"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Увеличивает лимит населения городов Тиранид."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="&lt;string name='Traits/Tyranids/CityPopulationLimitFlavor'/&gt;"/>
	<entry name="Tyranids/CityProduction" value="&lt;string name='Traits/Tyranids/CityProduction'/&gt;"/>
	<entry name="Tyranids/CityProductionDescription" value="Увеличивает выпуск продукции в городах Тиранид."/>
	<entry name="Tyranids/CityProductionFlavor" value="&lt;string name='Traits/Tyranids/CityProductionFlavor'/&gt;"/>
	<entry name="Tyranids/CityTier2" value="&lt;string name='Traits/Tyranids/CityTier2'/&gt;"/>
	<entry name="Tyranids/CityTier2Description" value="Повышает радиус присоединения плиток к городу."/>
	<entry name="Tyranids/CityTier2Flavor" value="&lt;string name='Traits/Tyranids/CityTier2Flavor'/&gt;"/>
	<entry name="Tyranids/ConsumeTile2" value="Эффективное пищеварение"/>
	<entry name="Tyranids/ConsumeTile2Description" value="Снижает затраты влияния на содержание присоединенной плитки."/>
	<entry name="Tyranids/ConsumeTile2Flavor" value="Хотя челюсти Малантропа или Потрошителя не имеют проблем с тем, чтобы перемалывать плоть, кости или даже пласталь, они работую медленно из-за необходимости потреблять огромное количество земли и камней, но Разуму улья требуется, чтобы они работали масштабнее и быстрее. Предполагается, что специализированные организмы используются для этого—хотя, опять же, никто не выжил после встречи с ними, чтобы сообщить сведения о них."/>
	<entry name="Tyranids/Deathspitter" value="Равенер Смертоплюй"/>
	<entry name="Tyranids/DeathspitterDescription" value="Даёт Равенерам дистанционное оружие."/>
	<entry name="Tyranids/DeathspitterFlavor" value="&lt;string name='Weapons/DeathspitterFlavor'/&gt;"/>
	<entry name="Tyranids/DesiccatorLarvae" value="&lt;string name='Weapons/DesiccatorLarvae'/&gt;"/>
	<entry name="Tyranids/DesiccatorLarvaeDescription" value="Даёт Тиранам улья, Тервигонам и Тиранофексам это типовое оружие."/>
	<entry name="Tyranids/DesiccatorLarvaeFlavor" value="&lt;string name='Weapons/DesiccatorLarvaeFlavor'/&gt;"/>
	<entry name="Tyranids/ExtraInfantryArmour" value="&lt;string name='Traits/ExtraInfantryArmour'/&gt;"/>
	<entry name="Tyranids/ExtraInfantryArmourDescription" value="&lt;string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/&gt;"/>
	<entry name="Tyranids/ExtraInfantryArmourFlavor" value="Разум улья имеет бережливый менталитет. Зачем жертвовать ресурсами на вооружение войск, которые все равно будут возвращены обратно? Только когда он встречает сильное сопротивление, Разум вкладывает ресурсы в толстый хитин и кости для своих существ."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmour" value="&lt;string name='Traits/ExtraMonstrousCreatureArmour'/&gt;"/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourDescription" value="Увеличивает броню монструозных существ."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourFlavor" value="Благодаря возможной комбинации твердейших панцирей, разрушительных полей и омертвленным нервным окончаниям, Разум улья может легко изменить боевые характеристики своих крупных существ."/>
	<entry name="Tyranids/FleshHooks" value="&lt;string name='Weapons/FleshHooks'/&gt;"/>
	<entry name="Tyranids/FleshHooksDescription" value="Даёт Тиранидам-воинам, Тираниду-Прайм и Ликтору дополнительное дистанционное оружие."/>
	<entry name="Tyranids/FleshHooksFlavor" value="&lt;string name='Weapons/FleshHooksFlavor'/&gt;"/>
	<entry name="Tyranids/HammerOfWrath" value="&lt;string name='Actions/HammerOfWrath'/&gt;"/>
	<entry name="Tyranids/HammerOfWrathDescription" value="Даёт Экзокринам, Гаргульям, Гаруспикам, Карге, Тиранам улья, Малецепторам, Серпоносному Иеродулу, Тервигонам, Тригонам, Тираноцитам и Тиранофексам способность совершать более разрушительные атаки."/>
	<entry name="Tyranids/HammerOfWrathFlavor" value="&lt;string name='Actions/HammerOfWrathFlavor'/&gt;"/>
	<entry name="Tyranids/InfantryUpkeep" value="&lt;string name='Traits/Tyranids/InfantryUpkeep'/&gt;"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Уменьшает затраты биомассы на содержание пехоты."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="&lt;string name='Traits/Tyranids/InfantryUpkeepFlavor'/&gt;"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2" value="Дикий напор"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Description" value="Снижает затраты влияния для отмены инстинктивного поведения."/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Flavor" value="Поддержание контроля над своими войсками является основной заботой Разума улья, которую он пытается преодолеть с помощью различных адаптаций, например таких, как Синаптические существа. Более простой способ—уменьшить естественную жестокость своих подразделений, когда они обращаются к инстинктам, и поэтому требуется меньше психических усилий, чтобы вернуть над ними контроль."/>
	<entry name="Tyranids/LongRangedDamage" value="&lt;string name='Traits/Tyranids/LongRangedDamage'/&gt;"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Повышает бронебойность оружия высокой дальности."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="&lt;string name='Traits/Tyranids/LongRangedDamageFlavor'/&gt;"/>
	<entry name="Tyranids/MeleeDamage" value="&lt;string name='Traits/Tyranids/MeleeDamage'/&gt;"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Повышает бронебойность оружия ближнего боя."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="&lt;string name='Traits/Tyranids/MeleeDamageFlavor'/&gt;"/>
	<entry name="Tyranids/Paroxysm" value="&lt;string name='Actions/Tyranids/Paroxysm'/&gt;"/>
	<entry name="Tyranids/ParoxysmDescription" value="Предоставляет Зоантропам способность проклинать вражеские подразделения, чтобы уменьшить их точность."/>
	<entry name="Tyranids/ParoxysmFlavor" value="&lt;string name='Actions/Tyranids/ParoxysmFlavor'/&gt;"/>
	<entry name="Tyranids/PreyAdaptation2" value="Малантропные бичи"/>
	<entry name="Tyranids/PreyAdaptation2Description" value="Увеличивает выход очков исследований, когда враги умирают рядом с Малантропами."/>
	<entry name="Tyranids/PreyAdaptation2Flavor" value="Одна из необычных адаптаций формы Малатропа это как-бы-жгутики—тонкие нитивидные усики, которые окружают Малантропа как мясистое, извивающееся облако. Предполагается, что они позволяют Малантропу собирать и хранить больше генетических данных от недавно умерших врагов."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="&lt;string name='Traits/Tyranids/ProductionBuildingUpkeep'/&gt;"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Снижает затраты влияния на содержания производственных зданий."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="&lt;string name='Traits/Tyranids/ProductionBuildingUpkeepFlavor'/&gt;"/>
	<entry name="Tyranids/Reclamation2" value="&lt;string name='Traits/Tyranids/Reclamation2'/&gt;"/>
	<entry name="Tyranids/Reclamation2Description" value="Снижает затраты влияния на освоение земель."/>
	<entry name="Tyranids/Reclamation2Flavor" value="&lt;string name='Traits/Tyranids/Reclamation2Flavor'/&gt;"/>
	<entry name="Tyranids/Reclamation3" value="&lt;string name='Traits/Tyranids/Reclamation3'/&gt;"/>
	<entry name="Tyranids/Reclamation3Description" value="Обнуляет время перезарядки для освоения земель."/>
	<entry name="Tyranids/Reclamation3Flavor" value="&lt;string name='Traits/Tyranids/Reclamation3Flavor'/&gt;"/>
	<entry name="Tyranids/Regeneration" value="Регенерация"/>
	<entry name="Tyranids/RegenerationDescription" value="Даёт способность монструозным существам тиранидов и Тиранид-Праймам восстанавливать очки здоровья каждый ход."/>
	<entry name="Tyranids/RegenerationFlavor" value="&lt;string name='Traits/RegenerationFlavor'/&gt;"/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="&lt;string name='Traits/Tyranids/ResourceBuildingUpkeep'/&gt;"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Снижает затраты влияния на содержание ресурсных зданий."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="&lt;string name='Traits/Tyranids/ResourceBuildingUpkeepFlavor'/&gt;"/>
	<entry name="Tyranids/ShortRangedDamage" value="&lt;string name='Traits/Tyranids/ShortRangedDamage'/&gt;"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Повышает бронебойность оружия низкой дальности."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="&lt;string name='Traits/Tyranids/ShortRangedDamageFlavor'/&gt;"/>
	<entry name="Tyranids/StingerSalvo" value="&lt;string name='Weapons/StingerSalvo'/&gt;"/>
	<entry name="Tyranids/StingerSalvoDescription" value="Даёт Карге дополнительное дистанционное оружие."/>
	<entry name="Tyranids/StingerSalvoFlavor" value="&lt;string name='Weapons/StingerSalvoFlavor'/&gt;"/>
	<entry name="Tyranids/ThresherScythe" value="&lt;string name='Weapons/ThresherScythe'/&gt;"/>
	<entry name="Tyranids/ThresherScytheDescription" value="Даёт Экзокринам и Гаруспикам дополнительное оружие ближнего боя."/>
	<entry name="Tyranids/ThresherScytheFlavor" value="&lt;string name='Weapons/ThresherScytheFlavor'/&gt;"/>
	<entry name="Tyranids/ToxinSacs" value="&lt;string name='Traits/Tyranids/ToxinSacs'/&gt;"/>
	<entry name="Tyranids/ToxinSacsDescription" value="Повышает урон оружия ближнего боя против пехоты и монструозных существ."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="&lt;string name='Traits/Tyranids/ToxinSacsFlavor'/&gt;"/>
	<entry name="Tyranids/Toxinspike" value="Токсичный шип Тригона"/>
	<entry name="Tyranids/ToxinspikeDescription" value="Даёт Тригонам дополнительное оружие ближнего боя."/>
	<entry name="Tyranids/ToxinspikeFlavor" value="&lt;string name='Weapons/ToxinspikeFlavor'/&gt;"/>
	<entry name="Tyranids/Tunnel2" value="&lt;string name='Traits/Tyranids/Tunnel2'/&gt;"/>
	<entry name="Tyranids/Tunnel2Description" value="Увеличивает Очки здоровья Выводка улья."/>
	<entry name="Tyranids/Tunnel2Flavor" value="&lt;string name='Traits/Tyranids/Tunnel2Flavor'/&gt;"/>
	<entry name="Tyranids/VehiclesUpkeep" value="&lt;string name='Traits/Tyranids/VehiclesUpkeep'/&gt;"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Уменьшает затраты биомассы на содержание монструозных существ."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="&lt;string name='Traits/Tyranids/VehiclesUpkeepFlavor'/&gt;"/>
	
	<entry name="Missing" value="Без вести пропавшие"/>
</language>
