<?xml version="1.0" encoding="utf-8"?>
<menu:selectFactionHUD extends="HUD" layout="Flow" layout.alignment="MiddleCenter" layout.gap="0 0" layout.direction="TopToBottom" preferredSize="1240 720" showEffect="FadeInRight" hideEffect="FadeOutRight">
	<label caption="<style name='ShadowedMenuTitle'/><string name='GUI/Credits'/>"/>
	<image texture="GUI/HeadlineBar" color="GUI/HeadlineBar"/>
	<component preferredSize="FillParent 30"/>
	<scrollableContainer name="creditsContainer" surface.texture="GUI/ShadowedSurface" preferredSize="700 WrapContent" maxSize="0 540" weights="FillAll 1">
		<label name="creditsLabel" preferredSize="FillParent WrapContent" alignment="TopCenter"/>
	</scrollableContainer>
	<component preferredSize="FillParent 30"/>
	<container layout.alignment="TopCenter" layout.gap="40 40" preferredSize="FillParent WrapContent">
		<navigationOKButton/>
	</container>
</menu:selectFactionHUD>
