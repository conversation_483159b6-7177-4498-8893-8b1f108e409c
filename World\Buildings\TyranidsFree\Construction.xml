<?xml version="1.0" encoding="utf-8"?>
<building>
	<modifiers>
		<modifier visible="0">
			<effects>
				<influenceUpkeep add="3"/>
				<biomassCost base="80"/>
				<productionCost base="48"/>
				<populationRequired base="1"/>
				<slotsRequired base="1"/>
			</effects>
		</modifier>
		<modifier>
			<effects>
				<biomass add="4"/>
				<populationLimit add="1"/>
				<production add="6"/>
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseConstructionScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<acquireTile icon="Actions/AcquireTile"
				name="Tyranids/AcquireTile">
			<modifiers>
				<modifier visible="0">
					<effects>
						<influenceCost add="5"/>
						<biomassCost add="5"/>
						<productionCost add="12"/>
					</effects>
				</modifier>
			</modifiers>
		</acquireTile>
		<clearTile icon="Actions/ClearTile"
				name="Tyranids/ClearTile">
			<modifiers>
				<modifier visible="0">
					<effects>
						<productionCost add="12"/>
					</effects>
				</modifier>
			</modifiers>
		</clearTile>
		<reclaimUnit cooldown="1"
				name="Tyranids/ReclaimUnit"
				interfaceSound="Interface/ReclaimUnit">
			<modifiers>
				<modifier visible="0">
					<effects>
						<influenceCost add="10"/>
					</effects>
				</modifier>
				<modifier requiredUpgrade="Tyranids/Reclamation2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
				<modifier requiredUpgrade="Tyranids/Reclamation3">
					<effects>
						<cooldown max="0"/>
					</effects>
				</modifier>
			</modifiers>
		</reclaimUnit>
		<constructBuilding building="TyranidsFree/Biomass"/>
		<constructBuilding building="TyranidsFree/Research"/>
		<constructBuilding building="TyranidsFree/Influence"/>
		<constructBuilding building="TyranidsFree/Loyalty" requiredUpgrade="Tyranids/Loyalty"/>
		<constructBuilding building="TyranidsFree/Housing" requiredUpgrade="Tyranids/Housing"/>
		<constructBuilding building="TyranidsFree/Infantry"/>
		<constructBuilding building="TyranidsFree/Heroes" requiredUpgrade="Tyranids/Heroes"/>
		<constructBuilding building="TyranidsFree/Thropes" requiredUpgrade="Tyranids/Thropes"/>
		<constructBuilding building="TyranidsFree/Vehicles" requiredUpgrade="Tyranids/Vehicles"/>
		<constructBuilding building="TyranidsFree/Aircraft" requiredUpgrade="Tyranids/Aircraft"/>
		<constructBuilding building="TyranidsFree/Construction" requiredUpgrade="Tyranids/Construction"/>
	</actions>
	<traits>
		<trait name="Tyranids/ProductionBuildingUpkeep" requiredUpgrade="Tyranids/ProductionBuildingUpkeep"/>
	</traits>
</building>
