<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="Artefacts/Accuracy" value="<string name='Units/Neutral/Artefacts/Accuracy'/>"/>
	<entry name="Artefacts/AccuracyDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Artefacts/Armor" value="<string name='Units/Neutral/Artefacts/Armor'/>"/>
	<entry name="Artefacts/ArmorDescription" value="Erhöht die Panzerung."/>
	<entry name="Artefacts/ArmorPenetration" value="<string name='Units/Neutral/Artefacts/ArmorPenetration'/>"/>
	<entry name="Artefacts/ArmorPenetrationDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Artefacts/Damage" value="<string name='Units/Neutral/Artefacts/Damage'/>"/>
	<entry name="Artefacts/DamageDescription" value="Erhöht den Schaden."/>
	<entry name="Artefacts/Healing" value="<string name='Units/Neutral/Artefacts/Healing'/>"/>
	<entry name="Artefacts/HealingDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="Artefacts/Hitpoints" value="<string name='Units/Neutral/Artefacts/Hitpoints'/>"/>
	<entry name="Artefacts/HitpointsDescription" value="Erhöht die Trefferpunkte."/>
	<entry name="Artefacts/Loyalty" value="<string name='Units/Neutral/Artefacts/Loyalty'/>"/>
	<entry name="Artefacts/LoyaltyDescription" value="Erhöht die Loyalität."/>
	<entry name="Artefacts/Movement" value="<string name='Units/Neutral/Artefacts/Movement'/>"/>
	<entry name="Artefacts/MovementDescription" value="Erhöht die Bewegung."/>
	<entry name="Artefacts/Sight" value="<string name='Units/Neutral/Artefacts/Sight'/>"/>
	<entry name="Artefacts/SightDescription" value="Erhöht die Sichtweite."/>

	<entry name="Items/AdamantiumWeaveVest" value="<string name='Items/AdamantiumWeaveVest'/>"/>
	<entry name="Items/AdamantiumWeaveVestDescription" value="<string name='Items/AdamantiumWeaveVestDescription'/>"/>
	<entry name="Items/AdamantiumWeaveVestFlavor" value="<string name='Items/AdamantiumWeaveVestFlavor'/>"/>
	<entry name="Items/ArmaplasBracers" value="<string name='Items/ArmaplasBracers'/>"/>
	<entry name="Items/ArmaplasBracersDescription" value="<string name='Items/ArmaplasBracersDescription'/>"/>
	<entry name="Items/ArmaplasBracersFlavor" value="<string name='Items/ArmaplasBracersFlavor'/>"/>
	<entry name="Items/AxeOfBlindFury" value="<string name='Items/AxeOfBlindFury'/>"/>
	<entry name="Items/AxeOfBlindFuryDescription" value="<string name='Items/AxeOfBlindFuryDescription'/>"/>
	<entry name="Items/AxeOfBlindFuryFlavor" value="<string name='Items/AxeOfBlindFuryFlavor'/>"/>
	<entry name="Items/CombatStimulant" value="<string name='Items/CombatStimulant'/>"/>
	<entry name="Items/CombatStimulantDescription" value="<string name='Items/CombatStimulantDescription'/>"/>
	<entry name="Items/CombatStimulantFlavor" value="<string name='Items/CombatStimulantFlavor'/>"/>
	<entry name="Items/ConcealedWeaponSystem" value="<string name='Items/ConcealedWeaponSystem'/>"/>
	<entry name="Items/ConcealedWeaponSystemDescription" value="<string name='Items/ConcealedWeaponSystemDescription'/>"/>
	<entry name="Items/ConcealedWeaponSystemFlavor" value="<string name='Items/ConcealedWeaponSystemFlavor'/>"/>
	<entry name="Items/DuskBlade" value="<string name='Items/DuskBlade'/>"/>
	<entry name="Items/DuskBladeDescription" value="<string name='Items/DuskBladeDescription'/>"/>
	<entry name="Items/DuskBladeFlavor" value="<string name='Items/DuskBladeFlavor'/>"/>
	<entry name="Items/EnduranceImplant" value="<string name='Items/EnduranceImplant'/>"/>
	<entry name="Items/EnduranceImplantDescription" value="<string name='Items/EnduranceImplantDescription'/>"/>
	<entry name="Items/EnduranceImplantFlavor" value="<string name='Items/EnduranceImplantFlavor'/>"/>
	<entry name="Items/EntropicLocum" value="<string name='Items/EntropicLocum'/>"/>
	<entry name="Items/EntropicLocumDescription" value="<string name='Items/EntropicLocumDescription'/>"/>
	<entry name="Items/EntropicLocumFlavor" value="<string name='Items/EntropicLocumFlavor'/>"/>
	<entry name="Items/FaolchusWing" value="<string name='Items/FaolchusWing'/>"/>
	<entry name="Items/FaolchusWingDescription" value="<string name='Items/FaolchusWingDescription'/>"/>
	<entry name="Items/FaolchusWingFlavor" value="<string name='Items/FaolchusWingFlavor'/>"/>
	<entry name="Items/LightningGauntlet" value="<string name='Items/LightningGauntlet'/>"/>
	<entry name="Items/LightningGauntletDescription" value="<string name='Items/LightningGauntletDescription'/>"/>
	<entry name="Items/LightningGauntletFlavor" value="<string name='Items/LightningGauntletFlavor'/>"/>
	<entry name="Items/MourningBladeOfLazaerek" value="<string name='Items/MourningBladeOfLazaerek'/>"/>
	<entry name="Items/MourningBladeOfLazaerekDescription" value="<string name='Items/MourningBladeOfLazaerekDescription'/>"/>
	<entry name="Items/MourningBladeOfLazaerekFlavor" value="<string name='Items/MourningBladeOfLazaerekFlavor'/>"/>
	<entry name="Items/OmniScope" value="<string name='Items/OmniScope'/>"/>
	<entry name="Items/OmniScopeDescription" value="<string name='Items/OmniScopeDescription'/>"/>
	<entry name="Items/OmniScopeFlavor" value="<string name='Items/OmniScopeFlavor'/>"/>
	<entry name="Items/PoweredGauntlet" value="<string name='Items/PoweredGauntlet'/>"/>
	<entry name="Items/PoweredGauntletDescription" value="<string name='Items/PoweredGauntletDescription'/>"/>
	<entry name="Items/PoweredGauntletFlavor" value="<string name='Items/PoweredGauntletFlavor'/>"/>
	<entry name="Items/ScrollsOfMagnus" value="<string name='Items/ScrollsOfMagnus'/>"/>
	<entry name="Items/ScrollsOfMagnusDescription" value="<string name='Items/ScrollsOfMagnusDescription'/>"/>
	<entry name="Items/ScrollsOfMagnusFlavor" value="<string name='Items/ScrollsOfMagnusFlavor'/>"/>
	<entry name="Items/SightlessHelm" value="<string name='Items/SightlessHelm'/>"/>
	<entry name="Items/SightlessHelmDescription" value="<string name='Items/SightlessHelmDescription'/>"/>
	<entry name="Items/SightlessHelmFlavor" value="<string name='Items/SightlessHelmFlavor'/>"/>
	<entry name="Items/TantalisingIcon" value="<string name='Items/TantalisingIcon'/>"/>
	<entry name="Items/TantalisingIconDescription" value="<string name='Items/TantalisingIconDescription'/>"/>
	<entry name="Items/TantalisingIconFlavor" value="<string name='Items/TantalisingIconFlavor'/>"/>
	<entry name="Items/TemporaryShield" value="<string name='Items/TemporaryShield'/>"/>
	<entry name="Items/TemporaryShieldDescription" value="<string name='Items/TemporaryShieldDescription'/>"/>
	<entry name="Items/TemporaryShieldFlavor" value="<string name='Items/TemporaryShieldFlavor'/>"/>
	<entry name="Items/UltraWidebandAuspex" value="<string name='Items/UltraWidebandAuspex'/>"/>
	<entry name="Items/UltraWidebandAuspexDescription" value="<string name='Items/UltraWidebandAuspexDescription'/>"/>
	<entry name="Items/UltraWidebandAuspexFlavor" value="<string name='Items/UltraWidebandAuspexFlavor'/>"/>
	<entry name="Items/VolcanisShroud" value="<string name='Items/VolcanisShroud'/>"/>
	<entry name="Items/VolcanisShroudDescription" value="<string name='Items/VolcanisShroudDescription'/>"/>
	<entry name="Items/VolcanisShroudFlavor" value="<string name='Items/VolcanisShroudFlavor'/>"/>
	<entry name="Items/ZoatHideJerkin" value="<string name='Items/ZoatHideJerkin'/>"/>
	<entry name="Items/ZoatHideJerkinDescription" value="<string name='Items/ZoatHideJerkinDescription'/>"/>
	<entry name="Items/ZoatHideJerkinFlavor" value="<string name='Items/ZoatHideJerkinFlavor'/>"/>

	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="Adjazenz-Integration"/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Erhöht die Generierung von Forschungspunkten mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="Die Wiederentdeckung des Wissens ist ein solch heiliger Ritus, dass in der gesamten Makropolstadt Unterstützungsriten vorgeschrieben sind. Mit zunehmender Bevölkerungsdichte erbringen die Gläubigen unentwegt Trankopfer und beschwören die Maschinengeister der Stadt, auf dass sie ihre Geheimnisse den Techpriestern im Librarium Omnis preisgeben mögen."/>
	<entry name="AdeptusMechanicus/AggressionOverride" value="<string name='Actions/AdeptusMechanicus/AggressionOverride'/>"/>
	<entry name="AdeptusMechanicus/AggressionOverrideDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="AdeptusMechanicus/AggressionOverrideFlavor" value="<string name='Actions/AdeptusMechanicus/AggressionOverrideFlavor'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="Erhöht die Bewegung, aber verringert die Panzerung."/>
	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="Dogma Metalica"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="Die Zeloten von Metalica sind dafür bekannt, dass sie einen ohrenbetäubenden Lärm erzeugen, der an die nie ruhende Industrie ihrer Fabrikwelt erinnern soll – und daran, dass sie bei ihrem unerbittlichen Vormarsch nicht innehalten, sondern selbst beim Vorrücken ihre Feinde erbarmungslos auslöschen."/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="Erhöht die Fernkampfgenauigkeit."/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
	<entry name="AdeptusMechanicus/Bionics" value="Bionics"/>
	<entry name="AdeptusMechanicus/BionicsDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="AdeptusMechanicus/BionicsFlavor" value="Seit den Zeiten von Arkhan Land kommt es selten vor, dass ein Magos des Adeptus Mechanicus völlig unaugmentiert ist. Einige eitle Magi verbergen ihre Augmentationen, wenngleich die meisten bionischen Verbesserungen ganz bewusst klobig und unmenschlich gehalten sind, da sie auch als physische Symbole für den Segen des Omnissiah dienen."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Actions/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Verringert den Moralverlust."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Actions/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="Erhöht die Panzerung, aber verringert die Bewegung."/>
	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiah" value="Lobgesänge des Omnissiah"/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiahDescription" value="Klassifizierung."/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiahFlavor" value="In Zeiten des Krieges führen die Jünger des Omnissiah komplexe Kriegssegnungen durch. Dabei handelt es sich nicht nur um Glaubensbekenntnisse an ihre allwissende Gottheit, sondern auch um Subroutinen zum Zwecke der Optimierung."/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="Erhöht die Nahkampfgenauigkeit."/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier2" value="Mavoraforming"/>
	<entry name="AdeptusMechanicus/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="AdeptusMechanicus/CityTier2Flavor" value="Unter Terraforming versteht man den Prozess, einen Planeten lebenswerter zu gestalten, indem man letztendlich – ironischerweise – unter dem sich stetig ausbreitenden Imperialen Palast alles Leben erstickt. Mavoraforming hingegen kommt ohne hehre Ziele aus und verwandelt einen Planeten schlicht und einfach in eine ausgebeutete, kontaminierte Umgebung mit äußerst produktiven Städten, ganz wie zu Hause…"/>
	<entry name="AdeptusMechanicus/CityTier3" value="Makropolwelt-Anerkennung"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="AdeptusMechanicus/CityTier3Flavor" value="Die endgültige Umwandlung eines Planeten in eine Makropolwelt ist vom Weltraum aus sichtbar. Verschwunden sind die sanften Hügel, die Täler und die Bäume. Verschwunden sind die Seen, die Meere und die atembare Atmosphäre. Stattdessen weht ein beißender Sand über die Staubbecken, in denen einige widerspenstige Imperiale im Schatten der himmelhohen, von Milliarden Seelen bewohnten Makropoltürme Landwirtschaft betreiben und ums Überleben kämpfen."/>
	<entry name="AdeptusMechanicus/Cognis" value="Cognis"/>
	<entry name="AdeptusMechanicus/CognisDescription" value="Sorgt dafür, dass sich der Genauigkeitsverlust in Grenzen hält."/>
	<entry name="AdeptusMechanicus/CognisFlavor" value="<string name='Weapons/CognisFlavor'/>"/>
	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Actions/AdeptusMechanicus/CommandUplink'/>"/>
	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Verringert den Moralverlust."/>
	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Actions/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="Erhöht die Nahkampfgenauigkeit, aber verringert die Fernkampfgenauigkeit."/>
	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ControlEdict" value="<string name='Actions/AdeptusMechanicus/ControlEdict'/>"/>
	<entry name="AdeptusMechanicus/ControlEdictDescription" value="Entfernt den Malus durch Doctrina-Imperative."/>
	<entry name="AdeptusMechanicus/ControlEdictFlavor" value="<string name='Actions/AdeptusMechanicus/ControlEdictFlavor'/>"/>
	<entry name="AdeptusMechanicus/DartingHunters" value="Flinke Jäger"/>
	<entry name="AdeptusMechanicus/DartingHuntersDescription" value="Aktionen brauchen keine Bewegungspunkte auf."/>
	<entry name="AdeptusMechanicus/DartingHuntersFlavor" value="Die Reflexe der Pteraxii werden dadurch geschärft, dass man ihre kognitiven Fähigkeiten in den Bereichen beschneidet, welche ihre Primärfunktion hemmen."/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermon" value="<string name='Actions/AdeptusMechanicus/DataBlessedAutosermon'/>"/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermonDescription" value="Generiert beim Ausschalten einer feindlichen Einheit Forschungspunkte."/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermonFlavor" value="<string name='Actions/AdeptusMechanicus/DataBlessedAutosermonFlavor'/>"/>
	<entry name="AdeptusMechanicus/DoctrinaImperatives" value="Doctrina-Imperative"/>
	<entry name="AdeptusMechanicus/DoctrinaImperativesDescription" value="Klassifizierung."/>
	<entry name="AdeptusMechanicus/DoctrinaImperativesFlavor" value="Die Skitarii sind furchterregende Gegner, die unerbittlich den Willen des Omnissiah manifestieren und mit den fortschrittlichsten Waffen des Imperiums ausgerüstet sind. Im Endeffekt handelt es sich jedoch um willenlose kybernetische Werkzeuge der Techpriester. Im Kampfgetümmel werden die Skitarii mit Data-Imperativen ferngesteuert, die ihre geistige und körperliche Leistungsfähigkeit auf ein übermenschliches Niveau steigern."/>
	<entry name="AdeptusMechanicus/Dunestrider" value="Dünenschreiter"/>
	<entry name="AdeptusMechanicus/DunestriderDescription" value="Erhöht die Bewegung."/>
	<entry name="AdeptusMechanicus/DunestriderFlavor" value="Manche Skitarii sind in der Lage, selbst das unwegsamste Gelände unermüdlich und mit hohem Tempo zu durchqueren, da ihre augmentierten Gliedmaßen weder ermüden noch verschleißen."/>
	<entry name="AdeptusMechanicus/EmanatusForceField" value="Emanatus-Kraftfeld"/>
	<entry name="AdeptusMechanicus/EmanatusForceFieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="AdeptusMechanicus/EmanatusForceFieldFlavor" value="Die sich überlappenden Kraftfelder, die von den Dünenläufern erzeugt werden, sind ein Wunder der Militärforschung. Ähnlich wie die bei den niederen Orden der Priesterschaft des Mars verbreiteten Refraktorfelder zerstreuen sie feindliche Energien in der Atmosphäre. Von jedem herannahenden Geschoss bleibt nicht viel mehr als ein aktinisch-blaues Flackern und ein Ozonhauch übrig."/>
	<entry name="AdeptusMechanicus/EnhancedDataTether" value="Verbesserte Databindung"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Erhöht die Moral."/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="Das Sprachrohr der Techpriester, die ihrerseits die Propheten des Maschinengotts höchstselbst sind. Wem die Ehre zuteilwird, über eine verbesserte Databindung zu verfügen, kann sich des bedingungslosen Gehorsams seiner ehrerbietigen Skitarii-Kameraden gewiss sein."/>
	<entry name="AdeptusMechanicus/EnrichedRounds" value="<string name='Actions/AdeptusMechanicus/EnrichedRounds'/>"/>
	<entry name="AdeptusMechanicus/EnrichedRoundsDescription" value="Erhöht den Schaden."/>
	<entry name="AdeptusMechanicus/EnrichedRoundsFlavor" value="<string name='Actions/AdeptusMechanicus/EnrichedRoundsFlavor'/>"/>
	<entry name="AdeptusMechanicus/EnslavedToThePast" value="Joch der Vergangenheit"/>
	<entry name="AdeptusMechanicus/EnslavedToThePastDescription" value="Erhöht die Forschungskosten."/>
	<entry name="AdeptusMechanicus/EnslavedToThePastFlavor" value="Letztendlich wurden die Zitadellen des Wissens auf einem Fundament aus Lügen errichtet. Die Fähigkeit zu wahrer Innovation ist bereits vor langer Zeit verloren gegangen. An ihre Stelle ist eine Ehrfurcht für die Zeiten getreten, in denen die Menschen die Architekten ihres eigenen Schicksals waren. Der Kult Mechanicus besteht nicht mehr aus Meistern ihrer eigenen Schöpfungen, sondern aus Sklaven der Vergangenheit. Mit Riten, Dogmen und Edikten wird an die Herrlichkeit vergangener Zeiten erinnert, anstatt Urteilsvermögen und Verständnis zu fördern. Selbst einfachen Vorgängen wie dem Aktivieren von Waffen geht das Auftragen von Ritualölen, das Verbrennen heiliger Harze und das Singen langer und komplexer Lobgesänge voraus. Und doch, solange dieses Prozedere funktioniert – oder besser gesagt, solange die Armeen des Kults imstande sind, missliebige Feinde auszulöschen –, können die Techpriester weiterhin selbstzufrieden ihren aalglatten Pfad des Starrsinns und der Ignoranz beschreiten."/>
	<entry name="AdeptusMechanicus/GuldiresOrison" value="Guldires Orison"/>
	<entry name="AdeptusMechanicus/GuldiresOrisonDescription" value="Verringert die Genauigkeit."/>
	<entry name="AdeptusMechanicus/GuldiresOrisonFlavor" value="Guldire war einer der obersten Warpschmiede der Word Bearers unter Erebus. Sein „Orison“ ist ein Gebet aus korrumpiertem Maschinencode, der dämonische Einflüsterungen aus dem Herzen des Warp übermittelt. Das Gebet erwies sich für die imperiale Verteidigung zwar nicht als tödlich, kann aber für große Verwirrung in den feindlichen Reihen sorgen…"/>
	<entry name="AdeptusMechanicus/FidorumVossPrime" value="Fidorum Voss Primus"/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Erhöht die Generierung von Einfluss mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="Die Priesterschaft des Kults Mechanicus verehrt die Fabrikwelt Voss Primus für ihre Loyalität gegenüber dem Imperium, die ihresgleichen sucht. Sogar als der Mars selbst an das Dunkle Mechanicum fiel, wurden auf Voss Primus weiterhin Rüstungsgüter produziert, und der Planet war maßgeblich an der Niederschlagung der Horus-Häresie beteiligt."/>
	<entry name="AdeptusMechanicus/GalvanicField" value="<string name='Actions/AdeptusMechanicus/GalvanicField'/>"/>
	<entry name="AdeptusMechanicus/GalvanicFieldDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="AdeptusMechanicus/GalvanicFieldFlavor" value="<string name='Actions/AdeptusMechanicus/GalvanicFieldFlavor'/>"/>
	<entry name="AdeptusMechanicus/EntropicDestabilisation" value="Entropische Destabilisierung"/>
	<entry name="AdeptusMechanicus/EntropicDestabilisationDescription" value="Gewährt Schadensreduktion."/>
	<entry name="AdeptusMechanicus/EntropicDestabilisationFlavor" value="Sie sind gestärkt durch den Segen des Omnissiah! Nichts und niemand kann sich Ihnen in den Weg stellen! (Ist das ein Traum? Haben Sie den Omnissiah gesehen?!)"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="Scriptorum Ordinatus"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="Ein Ordinatus ist eine einzigartige, titangroße Vorrichtung, die für einen bestimmten Zweck gebaut und streng bewacht wird. Bei Ihren Nachforschungen sind Sie auf alte Überlieferungen von der Erschaffung des Ordinatus Oberon gestoßen, einer auf Schienen montierten Raumschiffkanone, die während der Kriege um Armageddon eingesetzt wurde."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="Ikonen des Omnissiah"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Erhöht die Generierung von Loyalität mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="Beschütze uns, Meister. Befreie uns, Maschinengott. Der Omnissiah kommt. Der Omnissiah kommt. DER OMNISSIAH KOMMT. Geißle das Fleisch."/>
	<entry name="AdeptusMechanicus/IncenseCloud" value="Weihrauchwolke"/>
	<entry name="AdeptusMechanicus/IncenseCloudDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="AdeptusMechanicus/IncenseCloudFlavor" value="Wie Aldebrac Vinghs Perpetuum-Mobile-Maschinen funktionieren, die die alten Eisenschreiter antreiben, bleibt ein Geheimnis, sodass diese Maschinen von den Kultisten des Mars als wahre Wunderwerke verehrt werden. Der Weihrauch, in den sie gehüllt sind, ist sowohl ein Symbol der Ehrerbietung als auch ein wirksamer Schutz gegen feindlichen Beschuss."/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="Verringert den Moralverlust."/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="Erhöht den Nahkampfschaden."/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
	<entry name="AdeptusMechanicus/IonShield" value="Ionenschild"/>
	<entry name="AdeptusMechanicus/IonShieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="AdeptusMechanicus/IonShieldFlavor" value="Ritter sind mit leistungsstarken Feldgeneratoren ausgestattet, die als Ionenschilde bezeichnet werden. Diese Geräte basieren auf einer uralten Technologie, die es dem Anwender ermöglicht, ein Energiefeld über einen engen Bogen zu erzeugen. Indem der Ritter die Position des Schildes so verändert, dass feindliche Angriffe abgefangen werden, kann er selbst den schwersten Beschuss überstehen und gleichzeitig seine eigenen Waffen abfeuern. Die genaue Einstellung und Positionierung des Schildes ist von entscheidender Bedeutung, da der Ionenschild nur dazu dient, Schüsse abzulenken und zu verlangsamen, anstatt sie zu absorbieren, wie es bei den Deflektorschilden imperialer Titanen der Fall ist. Das bedeutet, dass die Wirksamkeit des Schildes von den Fähigkeiten und der Erfahrung seines Anwenders abhängt."/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCult" value="<string name='Actions/AdeptusMechanicus/LordOfTheMachineCult'/>"/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCultDescription" value="Erhöht die Genauigkeit."/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCultFlavor" value="<string name='Actions/AdeptusMechanicus/LordOfTheMachineCultFlavor'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisation" value="Lucianische Spezialisierung"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="Erhöht die Ressourcenproduktionsrate von Nicht-Hauptquartier-Gebäuden desselben Typs auf einem Hexfeld."/>
	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="Im Inneren der hohlen Fabrikwelt Lucius befindet sich eine Sonne, die für das Adeptus Mechanicus jedoch weniger interessant ist als die Eigenheiten des Planeten – ein einzigartiges Metall namens „Luciun“ und Warp-Reisen. Die Magi von Lucius sprechen sich für die tiefgreifende Nutzung dieser Eigenheiten aus."/>
	<entry name="AdeptusMechanicus/MechanicusLocum" value="<string name='Actions/AdeptusMechanicus/MechanicusLocum'/>"/>
	<entry name="AdeptusMechanicus/MechanicusLocumDescription" value="<string name='Actions/AdeptusMechanicus/MechanicusLocumDescription'/>"/>
	<entry name="AdeptusMechanicus/MechanicusLocumFlavor" value="<string name='Actions/AdeptusMechanicus/MechanicusLocumFlavor'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="Ryzanische Wildheit"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="Trotz der Spezialisierung des Planeten auf Plasma- und Schildvorrichtungen kommt es auf der Fabrikwelt Ryza in solch einer Häufigkeit zu Ork-Invasionen, dass die Verteidiger mittlerweile für ihre an die Orks erinnernde Nahkampf-Begeisterung bekannt sind."/>
	<entry name="AdeptusMechanicus/MonolithicBuildings" value="Monolithgebäude"/>
	<entry name="AdeptusMechanicus/MonolithicBuildingsDescription" value="Erhöht die Produktionsrate von Nicht-Hauptquartier-Gebäuden desselben Typs auf einem Hexfeld. Verringert die Produktionsrate von Nicht-Hauptquartier-Gebäuden unterschiedlichen Typs auf einem Hexfeld."/>
	<entry name="AdeptusMechanicus/MonolithicBuildingsFlavor" value="„Einheitlichkeit führt zu Spezialisierung. Spezialisierung führt zu Effizienz. Und Effizienz führt zu Ekstase.“<br/> – Kelphor Zhuko-Dim, Arco-Erzflagellant"/>
	<entry name="AdeptusMechanicus/NeurostaticInterface" value="Neurostatische Schnittstelle"/>
	<entry name="AdeptusMechanicus/NeurostaticInterfaceDescription" value="Erhöht die Schadensreduktion im Falle eines Angriffs durch angrenzende feindliche Einheiten."/>
	<entry name="AdeptusMechanicus/NeurostaticInterfaceFlavor" value="Die audiovisuelle Kakophonie, die mit dem Angriff eines Sicarianischen Infiltrators einhergeht, weist breitbandige elektromagnetische Interferenzen auf, die die gegnerischen Systeme lahmlegen und die Nerven überstrapazieren sollen."/>
	<entry name="AdeptusMechanicus/Omnispex" value="Omnispex"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="Ignoriert teilweise die feindliche Fernkampf-Schadensreduktion."/>
	<entry name="AdeptusMechanicus/OmnispexFlavor" value="Der Omnispex trägt einen Maschinengeist der Raptor-Klasse in sich, der Wärmeemissionen, Datensignaturen und biologische Wellenformen selbst aus enormer Entfernung lesen kann. Wird der Fokus über einen längeren Zeitraum aufrechterhalten, kann er die Schwachstellen der von ihm analysierten Kampfeinheit aufdecken und sie an seinen Meister übermitteln."/>
	<entry name="AdeptusMechanicus/OptateRestrictions" value="Optaten-Beschränkungen"/>
	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Erhöht das Bevölkerungslimit."/>
	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="Die Optaten, die das Bevölkerungswachstum in Makropolstädten kontrollieren, haben sich bereiterklärt, die Beschränkungen für die Fortpflanzung aufzuheben. In der Praxis bedeutet dies, dass keine Anaphrodisiaka mehr den Rationen beigemengt werden. Aber anstatt neue Gebäude errichten zu lassen, wird der bereits vorhandene Lebensraum noch mehr überfüllt…"/>
	<entry name="AdeptusMechanicus/PowerSurge" value="<string name='Actions/AdeptusMechanicus/PowerSurge'/>"/>
	<entry name="AdeptusMechanicus/PowerSurgeDescription" value="Erhöht die Produktionsrate von Nicht-Hauptquartier-Gebäuden."/>
	<entry name="AdeptusMechanicus/PowerSurgeFlavor" value="<string name='Actions/AdeptusMechanicus/PowerSurgeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="Erhöht die Fernkampfgenauigkeit, aber verringert die Nahkampfgenauigkeit."/>
	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/RadPoisoning" value="Strahlungsvergiftung"/>
	<entry name="AdeptusMechanicus/RadPoisoningDescription" value="Erhöht den Schaden gegen Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="AdeptusMechanicus/RadSaturation" value="<string name='Actions/AdeptusMechanicus/RadSaturation'/>"/>
	<entry name="AdeptusMechanicus/RadSaturationDescription" value="Verursacht in jeder Runde Schaden."/>
	<entry name="AdeptusMechanicus/RadSaturationFlavor" value="<string name='Actions/AdeptusMechanicus/RadSaturationFlavor'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="Reklamator-Protokolle"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Erhöht die Wachstumsrate."/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="Nichts darf verschwendet werden – kein Bissen Nahrung, kein Watt Energie, kein verwertbarer Mensch. Exekutionen werden zugunsten einer Umwandlung zu Servitoren auf ein Minimum reduziert, was zu einer Zunahme an verfügbarer Arbeitskraft führt…"/>
	<entry name="AdeptusMechanicus/ServoSkullUplink" value="<string name='Actions/AdeptusMechanicus/ServoSkullUplink'/>"/>
	<entry name="AdeptusMechanicus/ServoSkullUplinkDescription" value="Erhöht den Schaden."/>
	<entry name="AdeptusMechanicus/ServoSkullUplinkFlavor" value="<string name='Actions/AdeptusMechanicus/ServoSkullUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
	<entry name="AdeptusMechanicus/SiphonVigour" value="Lebenssauger"/>
	<entry name="AdeptusMechanicus/SiphonVigourDescription" value="Erhöht durch das Ausschalten einer feindlichen Einheit die Unverwundbar-Schadensreduktion."/>
	<entry name="AdeptusMechanicus/SiphonVigourFlavor" value="<string name='Traits/AdeptusMechanicus/SiphonedVigourFlavor'/>"/>
	<entry name="AdeptusMechanicus/SiphonedVigour" value="Entzogene Lebenskraft"/>
	<entry name="AdeptusMechanicus/SiphonedVigourDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="AdeptusMechanicus/SiphonedVigourFlavor" value="Dass die Elektropriester enorme Mengen an Energie verbrauchen, ist kein Geheimnis, allerdings machen sie auch keinen Hehl daraus. Wenn ein Gegner durch die Angriffe ihrer Elektroabsorber-Stäbe stirbt, wird das schützende Voltageistfeld mit bioelektrischer Energie gestärkt und wehrt selbst die stärksten Angriffe ab."/>
	<entry name="AdeptusMechanicus/SolarReflectors" value="Sonnenreflektoren"/>
	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Erhöht die Energiegewinnung mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="Planung, Planung, Planung. Die Magi des Adeptus Mechanicus entwerfen jede Stadt so, dass sie als ganzheitliches System funktioniert, in dem jeder Teilaspekt die anderen Teilaspekte unterstützt. Sonnenreflektoren auf allen Gebäuden beispielsweise leiten die geringe Menge an Energie, die sie durch die auf dem Planeten Gladius Primus vorherrschenden Sturmsysteme einfangen, zum nächstgelegenen Thermotauscherschrein weiter."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="Soylens-Sammelteams"/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Erhöht die Nahrungsproduktion mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="Die Reklamatoren ziehen einige Bewohner der Unterstadt für eine widerwärtige Arbeit heran. Diese Soylens-Sammelteams – besser bekannt als Leichenräuber – machen auf wenig subtile Art und Weise Jagd auf Tote (oder fast Tote), um sicherzustellen, dass auch die zukünftigen Rationen genügend frische Soylens-Proteine enthalten…"/>
	<entry name="AdeptusMechanicus/StygianEnlightenment" value="Stygische Erleuchtung"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="Verringert die Forschungskosten."/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="Manche Techpriester sind dem Studium der Xenos-Technologien gegenüber aufgeschlossener als andere. Jedoch entgeht nur die Fabrikwelt Stygies VIII konsequent einer Bestrafung für diese Xenariten-Tendenzen – teils wegen der Wichtigkeit ihrer Welt, teils weil man mit diesen Technologien jeden Angreifer außer Gefecht setzen kann. Schlägt man den Weg dieser Techpriester ein, kann man sich schnell Zugang zu verbotenen Technologien verschaffen."/>
	<entry name="AdeptusMechanicus/TerranGeneralism" value="Terranische Generalisierung"/>
	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="Erhöht die Produktionsrate jedes Gebäudes auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="Obwohl sich viele Fabrikwelten auf bestimmte Bedürfnisse spezialisiert haben und als unabhängige Reiche innerhalb der menschlichen Sphäre agieren, sollte man nicht vergessen, dass die meisten Techpriester stets das große Ganze im Auge haben und das Imperium bei seinem Streben nach den Sternen loyal unterstützen – wobei sie die Befehle der Erde ebenso befolgen wie die des Mars."/>
	<entry name="AdeptusMechanicus/Transonic" value="Transsonisch"/>
	<entry name="AdeptusMechanicus/TransonicDescription" value="Erhöht nach dem Angreifen den Schaden sowie vorübergehend den Panzerungsdurchschlag."/>
	<entry name="AdeptusMechanicus/TransonicFlavor" value="<string name='Weapons/TransonicBladeFlavor'/>"/>
	<entry name="AdeptusMechanicus/TransonicEffect" value="Transsonische Resonanz"/>
	<entry name="AdeptusMechanicus/TransonicEffectDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="AdeptusMechanicus/TransonicEffectFlavor" value="<string name='Weapons/TransonicBladeFlavor'/>"/>
	<entry name="AdeptusMechanicus/TriplexNecessity" value="Triplex-Erfordernis"/>
	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Erhöht die Erzgewinnung mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="Gemäß neuen Entwürfen, die auf Triplex Phall entdeckt wurden, wird bei der Errichtung neuer Gebäudekomplexe darauf geachtet, das entsprechende Gebiet unterirdisch zu erforschen und alle potenziellen Erzadern zu kartieren. Wird ein Hämotroper Reaktor errichtet, kann sein Plasma in vorkonstruierte Bahnen geleitet werden, um das Erz effizienter zu fördern."/>
	<entry name="AdeptusMechanicus/VoltagheistField" value="Voltageistfeld"/>
	<entry name="AdeptusMechanicus/VoltagheistFieldDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="AdeptusMechanicus/VoltagheistFieldFlavor" value="Alle Elektropriester sind von einem Nimbus aus reiner Energie umgeben, der über ihrer nackten Haut knistert und sich zu funkensprühenden elektromagnetischen Blasen zusammenzieht. Diese schweben in der Luft, als wären sie Irrlichter über einer ertrunkenen Leiche. Wenn herannahende Geschosse oder Energiestrahlen für einen Elektropriester zu einer Bedrohung werden, greifen diese winzigen voltaischen Geister oftmals ein und zerschmettern oder zerstreuen die Bedrohung in einem Hauch von brennendem Ozon. Wenn der Anwender des Voltageistfelds in Richtung Gegner stürmt, stürzen sich ebenjene Voltageister mit elektrischen Stößen auf die Gegner in der Nähe."/>
	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Erhöht den Moralverlust."/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
	<entry name="AdeptusMechanicus/WrathOfMars" value="<string name='Actions/AdeptusMechanicus/WrathOfMars'/>"/>
	<entry name="AdeptusMechanicus/WrathOfMarsDescription" value="Erhöht den Schaden."/>
	<entry name="AdeptusMechanicus/WrathOfMarsFlavor" value="<string name='Actions/AdeptusMechanicus/WrathOfMarsFlavor'/>"/>
	<entry name="AerialAttack" value="Flugabwehrangriff"/>
	<entry name="AerialAttackDescription" value="Kann nur Flieger anvisieren."/>
	<entry name="Agile" value="<string name='Actions/Agile'/>"/>
	<entry name="AgileDescription" value="<string name='Actions/AgileDescription'/>"/>
	<entry name="AmmoRunt" value="Munigrot"/>
	<entry name="AmmoRuntDescription" value="Erhöht die Fernkampfgenauigkeit."/>
	<entry name="AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Amphibious" value="Amphibisch"/>
	<entry name="AmphibiousDescription" value="Die Einheit kann sich über Wasser bewegen und ignoriert Malusse durch Flüsse."/>
	<entry name="AmphibiousFlavor" value="Nur sehr wenige Standardmilitärfahrzeuge im 41. Jahrtausend sind für amphibische Angriffe konzipiert. Eine Verringerung des Gewichts auf Kosten der Munition und Panzerung würde vielen Entscheidungsträgern zu weit gehen. Vor vielen Jahrtausenden wurde jedoch vom Imperium die Chimäre entwickelt – ein äußerst vielseitig einsetzbarer leichter Panzer, der in der Lage ist, Flüsse zu überwinden."/>
	<entry name="AndTheyShallKnowNoFear" value="Die keine Furcht kennen"/>
	<entry name="AndTheyShallKnowNoFearDescription" value="Verringert den Moralverlust und gewährt Immunität gegen Angst."/>
	<entry name="AndTheyShallKnowNoFearFlavor" value="Manche Kämpfer weigern sich aufzugeben und kämpfen weiter – ganz gleich, wie schlecht die Erfolgsaussichten sind."/>
	<entry name="Animosity" value="Feindseligkeit"/>
	<entry name="AnimosityDescription" value="Verringert die Anzahl der Angriffe."/>
	<entry name="AnimosityFlavor" value="Wenn der Waaagh erst einmal ins Rollen gekommen ist, sind die Orks sichtbar größer und stärker. Gerät er jedoch ins Stocken, schwächt das auch die Orks."/>
	<entry name="AntiGravUpwash" value="Antigravschub"/>
	<entry name="AntiGravUpwashDescription" value="Erhöht die Bewegung, wenn die Trefferpunkte über 66% liegen."/>
	<entry name="ApocalypticBarrage" value="Apokalyptisches Sperrfeuer"/>
	<entry name="ApocalypticBarrageDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticBarrageFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ApocalypticBlast" value="Apokalyptische Explosion"/>
	<entry name="ApocalypticBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ApocalypticMegaBlast" value="Apokalyptische Mega-Explosion"/>
	<entry name="ApocalypticMegaBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticMegaBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ArmoriumCherub" value="<string name='Actions/ArmoriumCherub'/>"/>
	<entry name="ArmoriumCherubDescription" value="Erhöht die Genauigkeit."/>
	<entry name="ArmoriumCherubFlavor" value="<string name='Actions/ArmoriumCherubFlavor'/>"/>
	<entry name="Armourbane" value="Panzerfluch"/>
	<entry name="ArmourbaneDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="ArmourbaneFlavor" value="Diese Waffe wurde mit einem Ziel konzipiert: die Panzerung von gepanzerten Fahrzeugen zu überwinden."/>
	<entry name="Artefact" value="Artefakt"/>
	<entry name="ArtefactDescription" value="Klassifizierung."/>
	<entry name="ArtefactFlavor" value="Im Laufe der Jahrtausende hat etwas in den Konstrukten der Alten, die quer über den Planeten Gladius verstreut sind, viele Rassen angezogen. Sie kamen mit Objekten, denen außergewöhnliche Kräfte innewohnen, und nun findet man überall auf der Planetenoberfläche fremdartige Technologien in allen möglichen Erscheinungsformen und Größen."/>
	<entry name="Assault" value="Sturm"/>
	<entry name="AssaultDescription" value="Klassifizierung."/>
	<entry name="AssaultDoctrine" value="Sturm-Doktrin"/>
	<entry name="AssaultDoctrineDescription" value="Erhöht die Genauigkeit."/>
	<entry name="AssaultDoctrineFlavor" value="<string name='Actions/AssaultDoctrineFlavor'/>"/>
	<entry name="AssaultVehicle" value="Sturmfahrzeug"/>
	<entry name="AssaultVehicleDescription" value="Das Aussteigen verbraucht keine Bewegungspunkte."/>
	<entry name="AssaultVehicleFlavor" value="Dieses Fahrzeug ist dafür konzipiert, Truppen im ärgsten Getümmel auszuladen."/>
	<entry name="AstraMilitarum/BlastDamage" value="Verbesserte Fragmentummantelung"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="Dank fortschrittlicherer Stanzprotokolle für die Ummantelung sind Explosivgeschosse noch tödlicher."/>
	<entry name="AstraMilitarum/BoltDamage" value="Kraken-Penetratorgeschosse"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="Dieses Kit ist eine vom Adeptus Astartes inspirierte Verbesserung des standardmäßigen Bolters und ermöglicht es mit Boltern bewaffneten Einheiten, Kraken-Penetratorgeschosse zu verwenden, die über noch wirkungsvollere Sprengladungen verfügen. Besonders beim Deathwatch-Angriffsteam der Space Marines sind diese Hartspitzgeschosse sehr beliebt."/>
	<entry name="AstraMilitarum/CityTier2" value="Substrukturerweiterung"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="Eines Tages wird dies eine Makropole sein, mit einer Milliarde Menschen in einem einzigen Bauwerk, das sich über eine kontaminierte Welt erhebt. Doch zuvor muss diese Siedlung ein wenig erweitert werden, damit das Fundament für den größten Ballungsraum der Galaxie gelegt werden kann."/>
	<entry name="AstraMilitarum/CityTier3" value="Unterbau für Versorgungstunnels"/>
	<entry name="AstraMilitarum/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="Das Departmento Munitorum mag ineffizient sein, doch es kann planen und die Exploratoren der Techpriester sowie die Servitors können bauen. Mithilfe dieser Bunkertunnels kann der Kommandant des Astra Militarum einen viel größeren Bereich der Stadt kontrollieren und ist dabei trotzdem stets geschützt."/>
	<entry name="AstraMilitarum/ShootSharpAndScarper" value="Schießen und verduften"/>
	<entry name="AstraMilitarum/ShootSharpAndScarperDescription" value="Aktionen brauchen keine Bewegungspunkte auf."/>
	<entry name="AstraMilitarum/ShootSharpAndScarperFlavor" value="Rattlinge weisen militärisch gesehen unzählige Schwächen auf, aber immerhin wissen sie, wann der richtige Zeitpunkt gekommen ist, um das Feuer zu eröffnen, und wann sie besser die Beine in die Hand nehmen sollten."/>
	<entry name="AstraMilitarumAircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarumAircraftProductionEdictDescription" value="Erhöht die Produktionsrate."/>
	<entry name="AstraMilitarumAircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumDefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarumDefenseEdictDescription" value="Erhöht die Panzerung."/>
	<entry name="AstraMilitarumDefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarumEnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarumEnergyEdictDescription" value="Erhöht die Energiegewinnung."/>
	<entry name="AstraMilitarumEnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarumFoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarumFoodEdictDescription" value="Erhöht die Nahrungsproduktion."/>
	<entry name="AstraMilitarumFoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarumGrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarumGrowthEdictDescription" value="Erhöht die Wachstumsrate."/>
	<entry name="AstraMilitarumGrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarumInfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarumInfantryProductionEdictDescription" value="Erhöht die Produktionsrate."/>
	<entry name="AstraMilitarumInfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumInfluenceEdict" value="<string name='Actions/AstraMilitarumInfluenceEdict'/>"/>
	<entry name="AstraMilitarumInfluenceEdictDescription" value="Erhöht die Generierung von Einfluss."/>
	<entry name="AstraMilitarumInfluenceEdictFlavor" value="<string name='Actions/AstraMilitarumInfluenceEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="Hochenergie-Powerpacks"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="Diese überragenden modularen Packs verwandeln das standardmäßige Lasergewehr in ein Hochenergie-Lasergewehr, das über eine erhöhte Reichweite und Schusskraft verfügt. Aufgrund der verringerten Zuverlässigkeit und Powerpack-Kapazität muss das Gewehr jedoch regelmäßig gewartet werden. Selbst Einheiten, die bereits über Hochenergie-Lasergewehre verfügen, profitieren von den Hochenergie-Powerpacks."/>
	<entry name="AstraMilitarumLoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarumLoyaltyEdictDescription" value="Erhöht die Generierung von Loyalität."/>
	<entry name="AstraMilitarumLoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarumOreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarumOreEdictDescription" value="Erhöht die Erzgewinnung."/>
	<entry name="AstraMilitarumOreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarumPsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarumPsykerProductionEdictDescription" value="Erhöht die Produktionsrate."/>
	<entry name="AstraMilitarumPsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarumResearchEdictDescription" value="Erhöht die Generierung von Forschungspunkten."/>
	<entry name="AstraMilitarumResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarumVehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarumVehicleProductionEdictDescription" value="Erhöht die Produktionsrate."/>
	<entry name="AstraMilitarumVehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>
	<entry name="AversionToLight" value="Lichtempfindlichkeit"/>
	<entry name="AversionToLightDescription" value="Erhöht den durch Flammen- und Melterwaffen verursachten Schaden."/>
	<entry name="AversionToLightFlavor" value="Bei Nacht lauern in den dunklen Ecken des Planeten Gladius Primus die Schrecken der Umbra. Richtet man auf sie jedoch einen Lichtstrahl, schrumpfen diese Albträume zusammen und weichen. Und richtet man auf sie den Feuerstrahl eines Flammenwerfers, dann…"/>
	<entry name="Barrage" value="Sperrfeuer"/>
	<entry name="BarrageDescription" value="Erfordert keine Sichtlinie, Abwehrfeuer-Attacken sind jedoch nicht möglich."/>
	<entry name="BarrageFlavor" value="<string name='Weapons/BarrageFlavor'/>"/>
	<entry name="Beam" value="Strahl"/>
	<entry name="BeamDescription" value="Trifft mit erhöhter Genauigkeit mehrere Kämpfer in der Zieleinheit."/>
	<entry name="Bike" value="Bike"/>
	<entry name="BikeDescription" value="Klassifizierung."/>
	<entry name="BikeFlavor" value="Einheiten auf Bikes eignen sich hervorragend für Vorhut-Angriffe. Dank der hohen Geschwindigkeit ihrer Bikes können sie tief im feindlichen Territorium zuschlagen und ihr Einsatzziel erfüllen, um sich dann wieder schnell zurückzuziehen, bevor der Feind reagieren kann. Krieger wie diese werden oftmals als hitzköpfige Draufgänger erachtet, aber ihre Effektivität kann niemand anzweifeln."/>
	<entry name="Bladestorm" value="Shurikensturm"/>
	<entry name="BladestormDescription" value="Erhöht den Schaden und den Panzerungsdurchschlag."/>
	<entry name="Blast" value="Explosion"/>
	<entry name="BlastDescription" value="Trifft mehrere Kämpfer in der Zieleinheit."/>
	<entry name="BlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="Blighted" value="Seuchenbefall"/>
	<entry name="BlightedDescription" value="Verursacht in jeder Runde Schaden."/>
	<entry name="Blind" value="Geblendet"/>
	<entry name="BlindDescription" value="Verringert die Genauigkeit."/>
	<entry name="BlindFlavor" value="Dieser Angriff erzeugt ein grelles Licht, das sich in die Augen des Opfers einbrennt und es auf diese Weise dazu zwingt, für kurze Zeit blind zu kämpfen."/>
	<entry name="Blinding" value="Blendung"/>
	<entry name="BlindingDescription" value="Verringert die Genauigkeit der ausgewählten Infanterieeinheit oder Monströsen Kreatur."/>
	<entry name="BloodBlessing" value="Blutsegen"/>
	<entry name="BloodBlessingDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="BloodBlessingFlavor" value="<string name='Features/SkullAltarFlavor'/>"/>
	<entry name="BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="BolsterDefencesDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="BoltWeapon" value="Bolterwaffe"/>
	<entry name="BoltWeaponFlavor" value="<string name='Weapons/BoltFlavor'/>"/>
	<entry name="BolterDrill" value="Bolter-Drill"/>
	<entry name="BolterDrillDescription" value="Erhöht die Genauigkeit."/>
	<entry name="BolterDrillFlavor" value="<string name='Actions/BolterDrillFlavor'/>"/>
	<entry name="Bomb" value="Bombe"/>
	<entry name="BombDescription" value="Fester Genauigkeitswert."/>
	<entry name="Bosspole" value="Trophä'nstangä"/>
	<entry name="BosspoleDescription" value="Verringert den Moralverlust abhängig von der Anzahl an nicht-feindlichen Einheiten in der Nähe."/>
	<entry name="BosspoleFlavor" value="Ork-Nobz tragen oft eine Trophä'nstangä, die zeigt, dass man sich besser nicht mit ihnen anlegen sollte. Außerdem finden es viele Nobz praktisch, ein Werkzeug dabei zu haben, mit dem man Köpfe einschlagen kann, um in der Hitze des Gefechts wieder ein bisschen Ordnung herzustellen."/>
	<entry name="BringItDown" value="„Zerstört es!“"/>
	<entry name="BringItDownDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Broken" value="Moral gebrochen"/>
	<entry name="BrokenDescription" value="Verringert die Genauigkeit und erhöht den erlittenen Schaden."/>
	<entry name="BruteShield" value="Schlagschild"/>
	<entry name="BruteShieldDescription" value="Erhöht den Schaden und die Schadensreduktion."/>
	<entry name="BruteShieldFlavor" value="Diese Schilde sehen aus wie große, robuste energiegeladene Buckler. Sie werden gerne von Bullgryns getragen und dienen ihnen im Kampf sowohl als Schutz als auch zum Schlagen."/>
	<entry name="Bulky" value="Massig"/>
	<entry name="BulkyDescription" value="Die Einheit benötigt in Transportern ein zusätzliches Transportfeld."/>
	<entry name="BulkyFlavor" value="Diese Kreatur ist so groß, dass sie in jedem Fahrzeug oder Gebäude übermäßig viel Platz einnimmt."/>
	<entry name="CamoNetting" value="Tarnnetze"/>
	<entry name="CamoNettingDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="CamoNettingFlavor" value="Ganz gleich, ob seltene Chamäolinnetze oder einfache Geflechte aus der hiesigen Flora: Tarnnetze verbergen ein Fahrzeug vor neugierigen Blicken."/>
	<entry name="Capturable" value="Kann übernommen werden"/>
	<entry name="CapturableDescription" value="Die Einheit kann von einer angrenzenden Einheit übernommen werden."/>
	<entry name="CeramitePlating" value="Ceramitschild"/>
	<entry name="CeramitePlatingDescription" value="Erhöht die Panzerung."/>
	<entry name="CeramitePlatingFlavor" value="Diese Platten haben den dreifachen Segen der Techmarines des Ordens erhalten und wurden mit den sieben heiligen Thermalschutzsalben behandelt, damit sie für die extremen Bedingungen beim Wiedereintritt in den Orbit gewappnet sind. Sicherheitsmaßnahmen wie diese schützen auch vor den eigenen Waffen, da sie selbst die extremsten Temperaturen und höchste Mikrowellenstrahlung absorbieren und zerstreuen können."/>
	<entry name="Chaff" value="Täuschkörper"/>
	<entry name="ChaffDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="Occulum Arcanum"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Ein Segen des Chaos, der die Genauigkeit erhöht."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="Ein blutunterlaufenes Auge drückt sich durch das Fleisch."/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGlory" value="Aura dunkler Pracht"/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGloryDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGloryFlavor" value="Einige Champions des Chaos stehen so sehr in der Gunst ihrer Schutzgottheit, dass sie auf übernatürliche Weise vor Schaden bewahrt werden. Manch einer will gesehen haben, wie sie von einer mächtigen, knisternden Blase aus psionischer Energie umgeben waren, sodass Geschosse vor dem Einschlagen auf sonderbare Weise abgelenkt wurden. Feststeht jedenfalls, dass die Dunklen Götter über sie wachen."/>
	<entry name="ChaosSpaceMarines/BlastDamage" value="Dämonenknochenüberzüge"/>
	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="Die Warpsmiths mögen zwar nur vergleichsweise harmlose Schrecken in diese explosiven Geschosse gebannt haben, doch wenn diese Schrecken beim Einschlag des Geschosses in Raserei verfallen, so ist dies keinesfalls harmlos. Während sie zerbersten und in den Warp zurückkehren, toben sie vor Kampfeswut und hängen sich an alles und jeden in der Nähe fest."/>
	<entry name="ChaosSpaceMarines/Bloated" value="Aufgedunsen"/>
	<entry name="ChaosSpaceMarines/BloatedDescription" value="Stellt Trefferpunkte wieder her."/>
	<entry name="ChaosSpaceMarines/BloatedFlavor" value="Die verfluchten Krankheiten des Nurgle raffen einen Menschen zwar sowohl körperlich als auch geistig dahin, allerdings wohnt dem verwesenden Fleisch eine schier unmenschliche Widerstandskraft inne. Solch aufgedunsenen Lebensformen bringt das Verbreiten ihrer Pocken nicht selten kleinere Segen des Seuchengottes ein."/>
	<entry name="ChaosSpaceMarines/BloodForTheBloodGod" value="Blut für den Blutgott"/>
	<entry name="ChaosSpaceMarines/BloodForTheBloodGodDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="ChaosSpaceMarines/BloodRage" value="Blutrausch"/>
	<entry name="ChaosSpaceMarines/BloodRageDescription" value="Erhöht die Anzahl der Nahkampfangriffe und Bewegungspunkte."/>
	<entry name="ChaosSpaceMarines/BloodRageFlavor" value="Der Helbrute stürzt sich auf Gegner in der Nähe und ignoriert mit seinem verfluchten Dreadnought-Gefängnis sämtliche alten Sicherheitsbeschränkungen, um ein schier dämonenhaftes Verhalten an den Tag legen zu können."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/BoltDamage" value="Warpgetragene Munition"/>
	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="Warpgetragene Munition schlägt eine Brücke zwischen der mit Promethium befüllten Inferno-Munition des Imperiums und der mit ihrer physikalischen Kraft wirkenden Inferno-Munition der Verräterlegion „Thousand Sons“. Warpgetragene Munition saust durch den Warp, um superheißes chemisches Feuer gegen ihre Ziele zu entfesseln."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="Ruf der Dunklen Götter"/>
	<entry name="ChaosSpaceMarines/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="Die Angehörigen des Imperiums schenken jeder Lüge Gehör und glauben jedes Ammenmärchen, wenn man ihnen sagt, dass sie nicht dem Untergang geweiht sind. Doch genau das öffnet unglücklicherweise dem Chaos Tür und Tor, und wer hindurchgeht, wird nicht wieder zurückkehren."/>
	<entry name="ChaosSpaceMarines/CityTier3" value="Nexus des Untergangs"/>
	<entry name="ChaosSpaceMarines/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="Die Anhänger des Chaos machen keinen Hehl mehr aus ihren Praktiken. Während sie früher Seelen vereinnahmt haben, indem sie sich ihre Schwächen zunutze machten oder sie mit dämonischen Kräften verführten, greifen sie nun auf Peitschen und Ketten zurück. Kriegerscharen und ganze Horden von Sklavenhaltern machen im Einzugsgebiet ihrer Städte Jagd auf Lebewesen, die sie opfern können. Nur die Stärksten können sich ihnen entziehen – die Schwachen hingegen werden das Wasser auf den Mühlen des Chaos."/>
	<entry name="ChaosSpaceMarines/Crazed" value="Kampfrausch"/>
	<entry name="ChaosSpaceMarines/CrazedDescription" value="Wenn die Einheit in der vorherigen Runde Schaden erlitten hat, erhält sie für eine Runde per Zufall eine der folgenden Eigenschaften: Schießwut, Wachsende Wut, Blutrausch."/>
	<entry name="ChaosSpaceMarines/CrazedFlavor" value="Alle Helbrutes sind psychotische, gefährliche Monstrositäten – Veteranen des Langen Krieges, die durch ihre qualvolle Gefangenschaft im Inneren des Läufers wahnsinnig geworden sind. Es mag rational erscheinen, einen Helbrute anzugreifen, aber wenn es dem Angreifer nicht gelingt, den Helbrute zu zerstören, wird er nur noch zorniger und wahnsinniger."/> <!-- Hellbrute. -->
	<entry name="ChaosSpaceMarines/ChampionOfChaos" value="Champion des Chaos"/>
	<entry name="ChaosSpaceMarines/ChampionOfChaosDescription" value="Wenn diese Einheit eine feindliche Einheit tötet, kann sie mit einem freigeschalteten Segen des Chaos belohnt werden oder sich in Chaos Spawn bzw. einen Daemon Prince verwandeln, falls es sich um eine Nicht-Helden-Einheit handelt."/>
	<entry name="ChaosSpaceMarines/ChampionOfChaosFlavor" value="Es ist nicht ungewöhnlich, dass die Chaosgötter jene, die in ihrem Namen töten, mit seltsamen Segen und Mutationen belohnen. Nicht alle dieser Geschenke sind vorteilhaft, denn die Dunklen Götter sind ebenso launisch wie desinteressiert. Selbst ihre inbrünstigsten Diener sind bestenfalls Bauern im ewigen göttlichen Schachspiel."/>
	<entry name="ChaosSpaceMarines/CrystallineBody" value="Kristalliner Körper"/>
	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Ein Segen des Chaos, der die Trefferpunkte erhöht."/>
	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="Das Fleisch des Champions wird diamanten."/>
	<entry name="ChaosSpaceMarines/CultistSacrifice" value="<string name='Actions/ChaosSpaceMarines/CultistSacrifice'/>"/>
	<entry name="ChaosSpaceMarines/CultistSacrificeDescription" value="Erhöht die Wachstumsrate."/>
	<entry name="ChaosSpaceMarines/CultistSacrificeFlavor" value="<string name='Actions/ChaosSpaceMarines/CultistSacrificeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Daemonforge" value="<string name='Actions/ChaosSpaceMarines/Daemonforge'/>"/>
	<entry name="ChaosSpaceMarines/DaemonforgeDescription" value="Erhöht den Schaden und den Panzerungsdurchschlag."/>
	<entry name="ChaosSpaceMarines/DaemonforgeFlavor" value="<string name='Actions/ChaosSpaceMarines/DaemonforgeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DarkGlory" value="<string name='Actions/ChaosSpaceMarines/DarkGlory'/>"/>
	<entry name="ChaosSpaceMarines/DarkGloryDescription" value="<string name='Actions/ChaosSpaceMarines/DarkGloryDescription'/>"/>
	<entry name="ChaosSpaceMarines/DarkGloryFlavor" value="<string name='Actions/ChaosSpaceMarines/DarkGloryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DevourerOfSouls" value="Seelenverschlinger"/>
	<entry name="ChaosSpaceMarines/DevourerOfSoulsDescription" value="Stellt in jeder Runde und jedes Mal, wenn diese Einheit eine feindliche Einheit tötet, Trefferpunkte wieder her"/>
	<entry name="ChaosSpaceMarines/DevourerOfSoulsFlavor" value="Venomcrawlers sind vielleicht die ausgeklügeltste Schöpfung der Warpsmiths. Es handelt sich dabei um spinnenartige Dämonenmaschinen, die nach empyreischer Energie gieren und andere dämonische Wesen – oder sogar lebende Kreaturen – verschlingen, um sie zu bekommen."/> <!-- Venomcrawler -->
	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Hindert die Einheit an Abwehrfeuer-Attacken."/>
	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/FireFrenzy" value="Schießwut"/>
	<entry name="ChaosSpaceMarines/FireFrenzyDescription" value="Verringert die Bewegung und erhöht die Anzahl der Fernkampfangriffe."/>
	<entry name="ChaosSpaceMarines/FireFrenzyFlavor" value="Als Gegenreaktion auf den erbärmlichen Beschuss der Sterblichen eröffnet der Helbrute unerbittlich das Feuer und achtet auf nichts anderes mehr als auf das ohrenbetäubende Grollen seiner dämonischen Munition."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/DeferredAbsolution" value="<string name='Actions/ChaosSpaceMarines/DeferredAbsolution'/>"/>
	<entry name="ChaosSpaceMarines/DeferredAbsolutionDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="ChaosSpaceMarines/DeferredAbsolutionFlavor" value="<string name='Actions/ChaosSpaceMarines/DeferredAbsolutionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutation" value="Geschenk der Mutation"/>
	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="Zu Beginn der nächsten Runde erhält die Einheit per Zufall einen neuen freigeschalteten Segen des Chaos und diese Eigenschaft ist nicht mehr wirksam."/>
	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="Die Dunklen Götter ließen ihrem Champion eine grausige Belohnung zuteilwerden, bei der es sich um ein rasiermesserscharfes Körperglied oder eine unvorteilhaft angewachsene Zunge handeln kann."/>
	<entry name="ChaosSpaceMarines/GraspingPseudopods" value="Greifende Pseudopodien"/>
	<entry name="ChaosSpaceMarines/GraspingPseudopodsDescription" value="Erhöht die Anzahl der Nahkampfangriffe."/>
	<entry name="ChaosSpaceMarines/GraspingPseudopodsFlavor" value="Aus den sich windenden Körpern der Chaos Spawn wachsen fleischige Tentakel, mit denen sie in der Nähe von Gegnern wild herumwirbeln. Kommt jemand den Tentakeln zu nahe, schlängeln sie sich reflexartig um das Opfer und ziehen es an alle Mäuler und Zähne heran, die die Chaos Spawn derzeit haben."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/IchorBlood" value="<string name='Actions/ChaosSpaceMarines/IchorBlood'/>"/>
	<entry name="ChaosSpaceMarines/IchorBloodDescription" value="<string name='Actions/ChaosSpaceMarines/IchorBloodDescription'/>"/>
	<entry name="ChaosSpaceMarines/IchorBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/IchorBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="Erhöht die Nahkampf-Schadensreduktion, wenn die Einheit von angsterfüllten gegnerischen Einheiten angegriffen wird."/>
	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="Erhöht den Schaden."/>
	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/InfernalFusillade" value="<string name='Actions/ChaosSpaceMarines/InfernalFusillade'/>"/>
	<entry name="ChaosSpaceMarines/InfernalFusilladeDescription" value="Erhöht die Anzahl der Angriffe mit Bolterwaffen."/>
	<entry name="ChaosSpaceMarines/InfernalFusilladeFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalFusillade'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustry" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustry'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustryDescription" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustryDescription'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustryFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/InfernalPower" value="<string name='Actions/ChaosSpaceMarines/InfernalPower'/>"/>
	<entry name="ChaosSpaceMarines/InfernalPowerDescription" value="Erhöht die Genauigkeit und den Schaden."/>
	<entry name="ChaosSpaceMarines/InfernalPowerFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalPowerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LasDamage" value="Verbotene Energien"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="Dadurch, dass sich das Dunkle Mechanicum über die technologischen Beschränkungen des Imperiums hinweggesetzt hat, konnte es die Waffen weiterentwickeln, die nun eine noch tödlichere und schrecklichere Wirkung entfalten."/>
	<entry name="ChaosSpaceMarines/LasherTendrils" value="<string name='Actions/ChaosSpaceMarines/LasherTendrils'/>"/>
	<entry name="ChaosSpaceMarines/LasherTendrilsDescription" value="Verringert die Anzahl der Nahkampfangriffe."/>
	<entry name="ChaosSpaceMarines/LasherTendrilsFlavor" value="<string name='Actions/ChaosSpaceMarines/LasherTendrilsFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LoathsomeAura" value="<string name='Actions/ChaosSpaceMarines/LoathsomeAura'/>"/>
	<entry name="ChaosSpaceMarines/LoathsomeAuraDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="ChaosSpaceMarines/LoathsomeAuraFlavor" value="<string name='Actions/ChaosSpaceMarines/LoathsomeAuraFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MalevolentLocus" value="<string name='Actions/ChaosSpaceMarines/MalevolentLocus'/>"/>
	<entry name="ChaosSpaceMarines/MalevolentLocusDescription" value="Verursacht in jeder Runde Schaden."/>
	<entry name="ChaosSpaceMarines/MalevolentLocusFlavor" value="<string name='Actions/ChaosSpaceMarines/MalevolentLocusFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MaliciousVolleys" value="Gnadenlose Salven"/>
	<entry name="ChaosSpaceMarines/MaliciousVolleysDescription" value="Bleibt die Einheit stationär, verfügt sie mit ihren Schnellfeuerwaffen auf allen Reichweiten über eine erhöhte Anzahl von Angriffen."/>
	<entry name="ChaosSpaceMarines/MaliciousVolleysFlavor" value="Für einen Häretiker-Astartes ist der Bolter weit mehr als nur eine Waffe. Er ist ein Instrument seines Zorns und ein Werkzeug des Todes gegen seine Feinde."/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="Erhöht die Anzahl der Trefferpunkte."/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="Erhöht die Anzahl der Bewegungspunkte."/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Mechanoid" value="Mechanoid"/>
	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Ein Segen des Chaos, der die Panzerung erhöht."/>
	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="Das Fleisch des Champions verwächst mit seiner Rüstung."/>
	<entry name="ChaosSpaceMarines/MeleeDamage" value="Vereint mit den Waffen"/>
	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="Für Soldaten ist es normal, eine enge Bindung zu ihrer vertrauten Waffe aufzubauen. Die Marines der Verräterlegionen sind jedoch einen Schritt weitergegangen. Im Laufe der Jahrhunderte sind die Truppen des Adeptus Astartes buchstäblich mit ihren Waffen verschmolzen. Sie wurden eins mit ihren Schuss- und Klingenwaffen, wodurch sich ihre Effektivität exponentiell erhöht hat."/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerror" value="Vielbeiniger Schrecken"/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerrorDescription" value="Erhöht die Anzahl der Stampf-Angriffe."/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerrorFlavor" value="Das titanische Gewicht des Eisens und Messings, aus dem die nicht-empyreischen Teile des Messingskorpions bestehen, ruht auf sechs tödlich scharfen Gliedmaßen. Wenn der Skorpion mit unerwartet hoher Geschwindigkeit nach vorne stürmt, kann er jeden kleineren Feind mit Leichtigkeit zertreten."/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReason" value="Mutiert jenseits aller Vernunft"/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReasonDescription" value="Zu Beginn einer jeden Runde erhält die Einheit für eine Runde per Zufall eine der folgenden Mutationen: Greifende Pseudopodien, Subkutane Panzerung, Giftsekret."/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReasonFlavor" value="Wenn Anhänger des Chaos bei ihrem Gott in Ungnade fallen, so ist dies Fluch und Segen zugleich, denn sie verwandeln sich in verdorbene Chaos Spawn – in eine sich krümmende mutierte Masse, aus der immer wieder ganz nach Laune der Gottheit neue Auswüchse herauswuchern."/>
	<entry name="ChaosSpaceMarines/Possession" value="<string name='Actions/ChaosSpaceMarines/Possession'/>"/>
	<entry name="ChaosSpaceMarines/PossessionDescription" value="<string name='Actions/ChaosSpaceMarines/PossessionDescription'/>"/>
	<entry name="ChaosSpaceMarines/PossessionFlavor" value="<string name='Actions/ChaosSpaceMarines/PossessionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/PrinceOfChaos" value="<string name='Actions/ChaosSpaceMarines/PrinceOfChaos'/>"/>
	<entry name="ChaosSpaceMarines/PrinceOfChaosDescription" value="Erhöht die Genauigkeit."/>
	<entry name="ChaosSpaceMarines/PrinceOfChaosFlavor" value="<string name='Actions/ChaosSpaceMarines/PrinceOfChaosFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergy" value="<string name='Actions/ChaosSpaceMarines/ReservoirOfDaemonicEnergy'/>"/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergyDescription" value="Verringert die Abklingzeit für „Dämoneneinfall“."/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergyFlavor" value="<string name='Actions/ChaosSpaceMarines/ReservoirOfDaemonicEnergyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="Erhöht die Produktionsrate."/>
	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="Erhöht die Produktionsrate und die Generierung von Forschungspunkten."/>
	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="Erhöht die Nahrungsproduktion und die Wachstumsrate."/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="Erhöht die Generierung von Einfluss und Loyalität."/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RisingFury" value="Wachsende Wut"/>
	<entry name="ChaosSpaceMarines/RisingFuryDescription" value="Erhöht die Anzahl der Nahkampfangriffe."/>
	<entry name="ChaosSpaceMarines/RisingFuryFlavor" value="Die Wut des Helbrutes ergreift von ihm Besitz und sorgt dafür, dass er in blinder Raserei auf alles in seiner Nähe einschlägt."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGod" value="Runen des Blutgottes"/>
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGodDescription" value="Wirft den Schaden von Hexenfeuer-Angriffen zurück."/>
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGodFlavor" value="Die Adepten des Dunklen Mechanicums haben in den Messingskorpion schimmernde Runen eingraviert, die sich wie tanzende Flammen auf der Oberfläche winden. Psioniker, die töricht genug sind, einen Angriff zu wagen, werden feststellen, dass ihr Gehirn von Khornes ewigem Zorn versengt wird."/>
	<entry name="ChaosSpaceMarines/ShatterDefences" value="<string name='Actions/ChaosSpaceMarines/ShatterDefences'/>"/>
	<entry name="ChaosSpaceMarines/ShatterDefencesDescription" value="Verringert die Fernkampf-Schadensreduktion."/>
	<entry name="ChaosSpaceMarines/ShatterDefencesFlavor" value="<string name='Actions/ChaosSpaceMarines/ShatterDefencesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/SiegeCrawler" value="Belagerungskletterer"/>
	<entry name="ChaosSpaceMarines/SiegeCrawlerDescription" value="Erhöht den Panzerungsdurchschlag gegen Befestigungsanlagen."/>
	<entry name="ChaosSpaceMarines/SiegeCrawlerFlavor" value="Maulerfiends mangelt es an Fernkampfwaffen jeder Art, was für ein Fahrzeug ihrer Größe ungewöhnlich ist. Dank ihrer guten Manövrierbarkeit und unterschiedlichster Schnittwaffen stellen sie jedoch eine große Gefahr für Verteidigungsanlagen dar. Die Maulerfiends nehmen sie genau in Augenschein, um eine Schwachstelle auszumachen und dies anschließend zu ihrem Vorteil zu nutzen."/> <!-- Maulerfiend. -->
	<entry name="ChaosSpaceMarines/SubcutaneousArmour" value="Subkutane Panzerung"/>
	<entry name="ChaosSpaceMarines/SubcutaneousArmourDescription" value="Erhöht die Panzerung."/>
	<entry name="ChaosSpaceMarines/SubcutaneousArmourFlavor" value="Die Panzerung der Space Marines ist so alt wie die Panzerung der Verräterlegionen und mit dem einzigartigen Carapax eines Adeptus Astartes ausgestattet. Im Laufe der Jahrtausende verschmolzen jedoch viele Chaos Space Marines mit ihrer Panzerung, sodass der sogenannte Schwarze Panzer mit ihrem Fleisch und ihren Knochen verwuchs."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/TemporalDistortion" value="Zeitverzerrung"/>
	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Ein Segen des Chaos, der die Anzahl der Bewegungspunkte erhöht."/>
	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="Der Champion verändert die Zeit um sich herum."/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhage" value="Giftsekret"/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhageDescription" value="Erhöht den Schaden gegen Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhageFlavor" value="Die verfluchten Innereien der Chaos Spawn stoßen abscheuliche Dämpfe und Flüssigkeiten aus, die für viele unglückselige Kreaturen den Tod bedeuten können."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="Veteranen des Langen Krieges"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Verringert den Moralverlust und erhöht die Nahkampfgenauigkeit gegen Einheiten der Space Marines."/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="Viele Fraktionen der Chaos Space Marines sind seit Jahrhunderten oder gar Jahrtausenden in einen ständigen Abnutzungskrieg mit dem Imperium der Menschheit verstrickt. Der brennende Hass, den sie für ihre loyalen Brüder empfinden, hat sich so sehr in diese Krieger eingebrannt, dass er jede andere Emotion überschattet. Dies gilt insbesondere für die neun ursprünglichen Verräterlegionen, die sich vor zehntausend Jahren auf Horus' Seite schlugen und bis zum heutigen Tag gegen ihre loyalen Brüder kämpfen."/>
	<entry name="ChaosSpaceMarines/WarpFrenzy" value="Warpwut"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Ein Segen des Chaos, der die Anzahl der Angriffe erhöht."/>
	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="Der Champion ist voller Zorn."/>
	<entry name="ChaosSpaceMarines/WorthyOffering" value="<string name='Actions/ChaosSpaceMarines/WorthyOffering'/>"/>
	<entry name="ChaosSpaceMarines/WorthyOfferingDescription" value="<string name='Actions/ChaosSpaceMarines/WorthyOfferingDescription'/>"/>
	<entry name="ChaosSpaceMarines/WorthyOfferingFlavor" value="<string name='Actions/ChaosSpaceMarines/WorthyOfferingFlavor'/>"/>
	<entry name="ChapterUnity" value="Einigkeit des Ordens"/>
	<entry name="ChapterUnityDescription" value="Erhöht die Generierung von Loyalität."/>
	<entry name="ChapterUnityFlavor" value="Die Reliquien in der Großen Halle erinnern das Adeptus Astartes an die Helden vergangener Zeiten und daran, dass möglicherweise eines Tages ihre Rüstungen, Waffen und Knochen hier verehrt werden."/>
	<entry name="City" value="Stadt"/>
	<entry name="CityDescription" value="Erhöht bei nicht-feindlichen Einheiten die Schadensreduktion sowie die Heilungsrate und verringert die Bewegungskosten. Erhöht zudem die Fernkampf-Schadensreduktion bei Infanterieeinheiten."/>
	<entry name="ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="ClusterMinesDescription" value="Verursacht Schaden, wenn das Hexfeld betreten wird."/>
	<entry name="ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="CombatShield" value="Parierschild"/>
	<entry name="CombatShieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="CombatShieldFlavor" value="Das Parierschild ist eine leichtere Variante des Sturmschilds und wird am Arm des Trägers befestigt. Dadurch ist seine Hand frei, um eine weitere Waffe zu führen, und gleichzeitig ist er besser vor Attacken geschützt."/>
	<entry name="CompendiumFlavor" value="Wären alle Krieger des 41. Jahrtausends identisch, würden die Schlachten allein durch die Truppenstärke entschieden werden – und dann hätte die Menschheit schon längst die Oberhand gewonnen. Die Realität sieht jedoch anders aus: Jede Spezies, jeder Truppentyp und jeder Soldat verfügt über individuelle Eigenschaften, die in bestimmten Situationen von großem Nutzen sein können – in anderen Situationen jedoch wertlos sind."/>
	<entry name="Concussion" value="Erschütterung"/>
	<entry name="ConcussionDescription" value="Verringert die Genauigkeit."/>
	<entry name="Concussive" value="Erschütternd"/>
	<entry name="ConcussiveDescription" value="Verringert vorübergehend die Genauigkeit der ausgewählten Infanterieeinheit oder Monströsen Kreatur."/>
	<entry name="ConcussiveFlavor" value="Manche Waffen sind so konzipiert, dass sie den Gegner orientierungslos zurücklassen, falls er den Angriff überleben sollte, damit er dann problemlos ausgeschaltet werden kann."/>
	<entry name="ConvergentTargeting" value="Konvergente Zielerfassung"/>
	<entry name="ConvergentTargetingDescription" value="Erhöht die Genauigkeit, wenn diese Einheit an eine nicht-feindliche Thunderfire Cannon angrenzt."/>
	<entry name="CultAmbush" value="Symbionten-Hinterhalt"/>
	<entry name="CultAmbushDescription" value="Erhöht die Genauigkeit bei Abwehrfeuer-Attacken."/>
	<entry name="CultAmbushFlavor" value="Jeder einzelne Angriff der Symbionten-Kulte wird genauestens geplant und die angewandte Strategie an die Gegebenheiten angepasst, um dann unverhofft so effektiv wie möglich zuzuschlagen."/>
	<entry name="CurseOfTheWalkingPox" value="Fluch der Wanderpocken"/>
	<entry name="CurseOfTheWalkingPoxDescription" value="Wandelt den von dieser Einheit erlittenen Schaden in Heilung um."/>
	<entry name="CurseOfTheWalkingPoxFlavor" value="Das fiese Grinsen im Gesicht eines jeden Seuchenwandlers täuscht über die Qualen hinweg, die seine Seele erleidet – gefangen in einem mutierten, leblosen Körper, der für Nurgle kämpft und dabei immer mehr Unschuldige mit den Wanderpocken infiziert."/>
	<entry name="Daemon" value="Dämon"/>
	<entry name="DaemonDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="DaemonFlavor" value="Der Warp hat viele niederträchtige Kreaturen in grenzenloser Vielfalt hervorgebracht, die sich jedoch in mancherlei Hinsicht nicht unterscheiden."/>
	<entry name="Damaged" value="Beschädigt"/>
	<entry name="DamagedDescription" value="Verringert die Genauigkeit."/>
	<entry name="Deathshriek" value="Todesschrei"/>
	<entry name="DeathshriekDescription" value="Fügt bei seinem Tod dem Angreifer Schaden zu."/>
	<entry name="DeathshriekFlavor" value="Wenn Umbra sterben, ereilt jeden Kämpfer in ihrer Nähe eine Vision über Kreaturen aus längst vergangenen Zeiten, die in Stücke gerissen und in den Warp gebannt wurden, während eine schreckliche und zugleich betörende Stimme einen Fluch flüstert… „VERWEILEN…“"/>
	<entry name="DeedsOfGlory" value="Ruhmreiche Taten"/>
	<entry name="DeedsOfGloryDescription" value="<string name='Actions/DeedsOfGloryDescription'/>"/>
	<entry name="DestroyerWeapon" value="Titanenkillerwaffe"/>
	<entry name="DestroyerWeaponDescription" value="Erhöht den Schaden."/>
	<entry name="DevastatorDoctrine" value="Devastor-Doktrin"/>
	<entry name="DevastatorDoctrineDescription" value="Erhöht die Genauigkeit."/>
	<entry name="DevastatorDoctrineFlavor" value="<string name='Actions/DevastatorDoctrineFlavor'/>"/>
	<entry name="Discipline" value="Disziplin"/>
	<entry name="DisciplineDescription" value="Erhöht die Genauigkeit."/>
	<entry name="DisciplineFlavor" value="<string name='Actions/AuraOfDisciplineFlavor'/>"/>
	<entry name="DistortScythe" value="Warpsense"/>
	<entry name="DistortScytheDescription" value="Erhöht den Schaden."/>
	<entry name="DistortScytheFlavor" value="<string name='Weapons/DistortionFlavor'/>"/>
	<entry name="DogmaAstrates" value="Astartes-Dogma"/>
	<entry name="DogmaAstratesDescription" value="<string name='Actions/DogmaAstratesDescription'/>"/>
	<entry name="DogmaAstratesFlavor" value="<string name='Actions/DogmaAstratesFlavor'/>"/>
	<entry name="DozerBlade" value="Bulldozerschaufel"/>
	<entry name="DozerBladeDescription" value="Verringert den Bewegungsmalus in Wäldern und imperialen Ruinen."/>
	<entry name="DozerBladeFlavor" value="Hierbei handelt es sich um schwere Pflüge, Klingenblätter, Rammen oder Schaufeln, die für das Fahrzeug Hindernisse aus dem Weg räumen."/>
	<entry name="Drukhari/AncientEvil" value="<string name='Actions/Drukhari/AncientEvil'/>"/>
	<entry name="Drukhari/AncientEvilDescription" value="Angegriffene Einheiten erleiden einen Moralverlust."/>
	<entry name="Drukhari/AncientEvilFlavor" value="<string name='Actions/Drukhari/AncientEvilFlavor'/>"/>
	<entry name="Drukhari/AssaultWeaponBonus" value="Absonderliche Konstruktion"/>
	<entry name="Drukhari/AssaultWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Drukhari/AssaultWeaponBonusFlavor" value="Die Waffenmeister der Kabalengießereien haben selbst auf Waffenausführungen Zugriff, die als wundersam oder in höchstem Maße unethisch gelten. Die Frage ist lediglich, wie viel ein interessierter Archon bereit ist, dafür zu zahlen."/>
	<entry name="Drukhari/BetrayalCulture" value="Kultivierter Verrat"/>
	<entry name="Drukhari/BetrayalCultureDescription" value="Die Städte der Drukhari weisen standardmäßig eine niedrigere Loyalität auf. Durch die Generierung von Einfluss steigt jedoch die Loyalität."/>
	<entry name="Drukhari/BetrayalCultureFlavor" value="Für die verdorbenen Drukhari scheint es normal zu sein, dass ein Kind seine Eltern verrät und sie langer Folter aussetzt, nur um den kleinsten Vorteil zu erlangen. In einer Stadt der Drukhari gibt es kein Vertrauen, sondern nur ein erbittertes Streben nach Macht. Je mächtiger ein Drukhari ist, desto mehr Artgenossen werden sich auf seine Seite schlagen."/>
	<entry name="Drukhari/BetrayalCultureUpgrade" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgrade'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgradeDescription'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgradeDescription'/>"/>
	<entry name="Drukhari/BladeArtists" value="Klingenkünstler"/>
	<entry name="Drukhari/BladeArtistsDescription" value="Erhöht den Panzerungsdurchschlag im Nahkampf."/>
	<entry name="Drukhari/BladeArtistsFlavor" value="Jeder Einwohner von Commorragh lernt bereits in jungen Jahren, den Wert von Klingen zu schätzen, und sie alle sind geübt im Umgang mit ihnen – ganz gleich, ob sie die Klingen in ihren grausamen Händen schwingen oder ob sie Teil ihrer Rüstungen sind."/>
	<entry name="Drukhari/BladeWhip" value="Peitschende Klingen"/> 
	<entry name="Drukhari/BladeWhipDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Drukhari/BladeWhipFlavor" value="Nur die Lacerai der Hekatarii-Kulte kämpfen regelmäßig mit der als Klingenpeitsche bekannten segmentierten Klinge, und ihre Fähigkeiten im Umgang mit dieser Waffe sind legendär. Angriffe, die ihr Ziel sicher zu verfehlen scheinen, treffen auf scheinbar magische Weise doch noch ihr Ziel, und die Lacerai sind imstande, nach Belieben zwischen Peitschen und Klingen zu wechseln."/>
	<entry name="Drukhari/BloodDancer" value="<string name='Actions/Drukhari/BloodDancer'/>"/>
	<entry name="Drukhari/BloodDancerDescription" value="<string name='Actions/Drukhari/BloodDancerDescription'/>"/>
	<entry name="Drukhari/BloodDancerFlavor" value="<string name='Actions/Drukhari/BloodDancerFlavor'/>"/>
	<entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
	<entry name="Drukhari/BonusResourcesDescription" value="Erhöht die Produktionsrate von Gebäuden."/>
	<entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
	<entry name="Drukhari/BridesOfDeath" value="<string name='Actions/Drukhari/BridesOfDeath'/>"/>
	<entry name="Drukhari/BridesOfDeathDescription" value="Erhöht den Nahkampfschaden."/>
	<entry name="Drukhari/BridesOfDeathFlavor" value="<string name='Actions/Drukhari/BridesOfDeathFlavor'/>"/>
	<entry name="Drukhari/CityTier2" value="Voll von Sünde"/>
	<entry name="Drukhari/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Drukhari/CityTier2Flavor" value="Wenn sich die plündernden Drukhari an einen Planeten gewöhnt haben, expandieren sie mit ihren Basen immer weiter und ziehen dabei die niederträchtigsten Bewohner der Galaxis an. Da sie durchaus in der Lage sind, noch mehr abscheuliche Nachfahren der Aeldari zu versorgen, breiten sich die Slums und Foltergärten der Städte immer weiter nach außen und nach unten aus…"/>
	<entry name="Drukhari/CityTier3" value="Domäne der Verzweiflung"/>
	<entry name="Drukhari/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Drukhari/CityTier3Flavor" value="Die Archonten und Draconten dieses Piratenreichs erlangten einen solchen Wohlstand, dass sie hier ihre eigenen Paläste errichten ließen, wohlwissend, dass sie nicht bleiben können, da „Sie, die dürstet“ sonst ihre Seelen vollends verschlingen würde. Solange sie hier sind, müssen sie ständig mit Gefangenen versorgt werden, um ihre unmenschlichen Gelüste zu stillen und das Verwelken ihrer Seelen aufzuhalten."/>
	<entry name="Drukhari/CombatDrugs" value="Kampfdrogen"/>
	<entry name="Drukhari/CombatDrugsDescription" value="Verbessert diverse Kampffähigkeiten."/>
	<entry name="Drukhari/CombatDrugsFlavor" value="Chemische Stimulanzien werden häufig zur Verbesserung der Fähigkeiten im Kampf eingesetzt, wenngleich sie die Lebenserwartung des Anwenders drastisch verringern."/>
	<entry name="Drukhari/CorsairOutposts" value="Korsarenaußenposten"/>
	<entry name="Drukhari/CorsairOutpostsDescription" value="Die Städte der Drukhari weisen standardmäßig eine niedrigere Wachstumsrate auf. Kontrollierte Außenposten sorgen jedoch für ein erhöhtes Wachstum."/>
	<entry name="Drukhari/CorsairOutpostsFlavor" value="Diese Außenposten sind so groß, dass sie für die Herdenvölker wie imposante Städte anmuten, doch für die Drukhari sind sie nichts weiter als vorübergehende Aufenthaltsorte – es sei denn, sie können in der Umgebung reiche Beute machen. Je erfolgreicher Außenposten sind, desto mehr andere Drukhari, die ihren Anteil am Gemetzel und Leid der Opfer haben wollen, fühlen sich angezogen."/>
	<entry name="Drukhari/CorsairOutpostsUpgrade" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgrade'/>"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgradeDescription'/>"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgradeDescription'/>"/>
	<entry name="Drukhari/Dodge" value="Ausweichen"/>
	<entry name="Drukhari/DodgeDescription" value="Erhöht die Nahkampf-Schadensreduktion."/>
	<entry name="Drukhari/DodgeFlavor" value="Die verschiedenen Krieger in den Reihen der Hekatarii-Kulte verfügen über eine Vielzahl exotischer Werkzeuge zum Verstümmeln, Einfangen, Aufschlitzen und Aufspießen. Allerdings tragen sie nur wenig Rüstung, da sie bei der Verteidigung auf ihre Flinkheit und Gewandtheit setzen, indem sie den ungeschickten Schlägen ihrer Gegner gleichermaßen gekonnt wie arrogant ausweichen."/>
	<entry name="Drukhari/EnergyBuildingBonus" value="Gelockerte Ilmaea-Sicherheitsvorkehrungen"/>
	<entry name="Drukhari/EnergyBuildingBonusDescription" value="Erhöht die Energiegewinnung."/>
	<entry name="Drukhari/EnergyBuildingBonusFlavor" value="Dieses Kraftwerk nutzt lediglich einen winzigen Bruchteil der Energie, die eine Ilmaea freisetzt – eine der gestohlenen schwarzen Sonnen, die Commorragh umkreisen. Lockert man die Sicherheitsvorkehrungen auch nur ein bisschen, steigt die Energieleistung – und damit auch die Wahrscheinlichkeit, dass die Ilmaea den Planeten Gladius Primus einfach verschluckt. Dies ist jedoch ein Risiko, das die praktisch unsterblichen Drukhari gerne bereit sind einzugehen."/>
	<entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
	<entry name="Drukhari/EnhancedAethersailsDescription" value="<string name='Actions/Drukhari/EnhancedAethersailsDescription'/>"/>
	<entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
	<entry name="Drukhari/Flickerfield" value="Flackerfeld"/>
	<entry name="Drukhari/FlickerfieldDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="Drukhari/FlickerfieldFlavor" value="Flackerfelder sind hochentwickelte optische Kraftfelder, die ihr Fahrzeug von einem Augenblick auf den anderen erscheinen und wieder verschwinden lassen."/>
	<entry name="Drukhari/GhostplateArmour" value="Geisterharnisch"/>
	<entry name="Drukhari/GhostplateArmourDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="Drukhari/GhostplateArmourFlavor" value="Jene Drukhari, die gerne gut geschützt sind, ohne dabei in hohem Maße ihre Beweglichkeit einzubüßen, tragen Rüstung aus gehärteten Kunstharzen mit Einschlüssen von Gas, das leichter ist als Luft. Zusätzlich dazu kommen bei Geisterharnischen Kraftfeldtechnologien zum Einsatz, um den Träger noch besser zu schützen."/>
	<entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
	<entry name="Drukhari/GrislyTrophiesDescription" value="Verringert den Moralverlust."/>
	<entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
	<entry name="Drukhari/HeavyWeaponBonus" value="Kalibrierte Geschütze"/>
	<entry name="Drukhari/HeavyWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Drukhari/HeavyWeaponBonusFlavor" value="„Hofft nicht auf ein Morgen, erbärmliche Sterbliche. Ihr seid nun in der Hand der Drukhari, und ihr Griff ist so fest, dass ihr nicht entkommen könnt. Ich versichere euch: Alles, was ihr jetzt erleben werdet, ist ewige Nacht – und ewige Qualen.“ – Gyrthineus Roche, Archon der Letzten Klinge"/>
	<entry name="Drukhari/MasterOfPain" value="<string name='Actions/Drukhari/MasterOfPain'/>"/>
	<entry name="Drukhari/MasterOfPainDescription" value="Erhöht die Schadensreduktion „Verletzungen ignorieren“."/>
	<entry name="Drukhari/MasterOfPainFlavor" value="<string name='Actions/Drukhari/MasterOfPainFlavor'/>"/>
	<entry name="Drukhari/MeleeWeaponBonus" value="Perfektionierte Klingen"/>
	<entry name="Drukhari/MeleeWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Drukhari/MeleeWeaponBonusFlavor" value="„Schmerz ist die einzige allgemeingültige Konstante. Schmerz ist alles. Er ist der Schlüssel zu Schöpfung und Zerstörung. Deshalb wird jener, der den Schmerz meistert, zu einem Gott.“ – Urien Rakarth, Meister der Haemonculi"/>
	<entry name="Drukhari/NightShields" value="Nachtfeld"/>
	<entry name="Drukhari/NightShieldsDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="Drukhari/NightShieldsFlavor" value="Das Fahrzeug ist von einem Breitband-Verzerrungsfeld umgeben, das es in kalte, tiefschwarze Dunkelheit hüllt. Gegnern fällt es schwer, das Fahrzeug durch diesen Mantel aus flimmernden Schatten anzuvisieren."/>
	<entry name="Drukhari/NoEscape" value="<string name='Actions/Drukhari/NoEscape'/>"/>
	<entry name="Drukhari/NoEscapeDescription" value="Verringert die Bewegung."/>
	<entry name="Drukhari/NoEscapeFlavor" value="<string name='Actions/Drukhari/NoEscapeFlavor'/>"/>
	<entry name="Drukhari/Overlord" value="<string name='Actions/Drukhari/Overlord'/>"/>
	<entry name="Drukhari/OverlordDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Drukhari/OverlordFlavor" value="<string name='Actions/Drukhari/OverlordFlavor'/>"/>
	<entry name="Drukhari/PowerFromPain" value="Macht durch Schmerz"/>
	<entry name="Drukhari/PowerFromPainDescription" value="Die Einheit erhält bei Stufenaufstiegen Kampfboni. Erhöht die Schadensreduktion auf Stufe 3 oder höher, erhöht den Nahkampfschaden auf Stufe 6 oder höher und verringert den Moralverlust auf Stufe 10."/>
	<entry name="Drukhari/PowerFromPainFlavor" value="Während sich die Drukhari an den Seelen ihrer leidenden Gegner laben, werden sie von übernatürlicher Macht erfüllt und verwandeln sich dadurch in Tötungsmaschinen."/>
	<entry name="Drukhari/RaidersTactics" value="Plünderertaktiken"/>
	<entry name="Drukhari/RaidersTacticsDescription" value="Gewährt Bonuseigenschaften, wenn diese Einheit eine Transporteinheit verlässt."/>
	<entry name="Drukhari/RaidersTacticsFlavor" value="„Warum schweben wir an Deck dieser eleganten Fahrzeuge durch die Lüfte? Um die Schreie unserer Beute zu hören, während wir sie niederringen, um die Angst in ihren Gesichtern zu sehen, um den verlockenden Geruch ihres Blutes in der Luft zu schmecken – als Vorspeise vor dem Festmahl. Aber vor allem, damit das Gemetzel so schnell wie möglich beginnen kann.“ – Dariaq Klingenzunge, Kabale des Durchbohrten Auges"/>
	<entry name="Drukhari/RaidersTacticsDamage" value="Plünderer – Sturmangriff"/>
	<entry name="Drukhari/RaidersTacticsDamageDescription" value="Erhöht den Schaden."/>
	<entry name="Drukhari/RaidersTacticsDamageFlavor" value="Jahrtausende der Plünderungen in der Dimension der Sterblichen lassen keinen anderen Schluss zu, als dass die Drukhari erbarmungslose, opportunistische Jäger sind, wie sie in der Geschichte ihresgleichen suchen. Dies ist teils auf jahrhundertelange Erfahrung, teils auf die Evolution und teils auf die Anpassungen durch die Haemonculi zurückzuführen. Nach dem Verlassen ihrer Flieger machen sie Jagd auf ihre Gegner wie Adler im Sturzflug."/>
	<entry name="Drukhari/RaidersTacticsDamageReduction" value="Plünderer – Ausweichen"/>
	<entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="Die Drukhari leben dafür, anderen Schmerz zuzufügen, und es gibt entscheidende Momente in ihrer Existenz, die dies verdeutlichen: wenn ein Incubus seine Gleve schwingt, wenn eine Hekatari ihr Opfer in der Arena quält oder wenn die Drukhari-Plünderer in Sekundenschnelle aus ihrem Flieger springen und für eine tödliche Überraschung sorgen…"/>
	<entry name="Drukhari/RaidersTacticsHealingRate" value="Plünderer – Bereitschaft"/>
	<entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Erhöht die Heilungsrate, wenn diese Einheit in ein Fahrzeug eingeladen wurde."/>
	<entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="„Freunde – ich nenne euch so, obwohl ich weiß, dass ein Blick von Vect genügen würde, um mir ein Messer in den Rücken zu rammen – Freunde, haltet euch bereit. Der Augenblick naht, und die Herde weiß noch nicht einmal, dass wir kommen. In wenigen Minuten werden wir über sie herfallen, sie häuten, sie zerfleischen, sie quälen, sie niedermetzeln und ihre Seelen trinken… natürlich mit der gebotenen Eleganz, denn wir sind schließlich keine Wilden.“ – Gyrthineus Roche, Archon der Letzten Klinge"/>
	<entry name="Drukhari/Shadowfield" value="<string name='Actions/Drukhari/Shadowfield'/>"/>
	<entry name="Drukhari/ShadowfieldDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="Drukhari/ShadowfieldFlavor" value="<string name='Actions/Drukhari/ShadowfieldFlavor'/>"/>
	<entry name="Drukhari/SacrificeToKhaine" value="Opfer für Khaine"/>
	<entry name="Drukhari/SacrificeToKhaineDescription" value="Erhöht den Schaden."/>
	<entry name="Drukhari/SacrificeToKhaineFlavor" value="Die Drukhari verehren weder die Alten noch die alten Aeldari-Götter, mit einer Ausnahme: Kaela Mensha Khaine, Gott des Krieges und der Zerstörung. Nur wenige Drukhari halten Khaine die Treue, und die meisten von ihnen sind Incubi, die dunklen Gegenstücke zu den Aeldari-Aspektkriegern. Jedes Mal, wenn sie mit ihren rituellen Gleven töten, tun sie dies zu Ehren von Khaine."/>
	<entry name="Drukhari/ShroudGate" value="Schleiertor"/>
	<entry name="Drukhari/ShroudGateDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="Drukhari/ShroudGateFlavor" value="„Der Hinterhalt am Netz der Tausend Tore war eine clevere Idee, tapferer kleiner Mensch. Nur wusstest du nicht, wann und wo wir auftauchen würden, und das war dein Fehler. Für uns Drukhari sind die Portale eine Annehmlichkeit, aber eigentlich nicht notwendig… dein Leiden hingegen, so fürchte ich, ist eine Notwendigkeit.“ – Gyrthineus Roche, Archon der Letzten Klinge"/>
	<entry name="Drukhari/SoulHunger" value="Seelenhunger"/>
	<entry name="Drukhari/SoulHungerDescription" value="Beim Töten einer gegnerischen Einheit wird Einfluss generiert."/>
	<entry name="Drukhari/SoulHungerFlavor" value="Die einzige Form der Nahrung, die Drukhari stärkt – das Einzige, was ihr unsterbliches Leben überhaupt erträglich macht, während sich Slaanesh an ihren Seelen labt –, ist das Leiden anderer. Und diejenigen Archonten, die für einen regelmäßigen Nachschub an Opfern und Qualen sorgen können, werden dafür mit Loyalität belohnt – zumindest in dem Maße, wie die Drukhari dazu in der Lage sind…"/>
	<entry name="Drukhari/SoulHungerLoyalty" value="Seelenbrot und Spiele"/>
	<entry name="Drukhari/SoulHungerLoyaltyDescription" value="Erhöht die Generierung von Loyalität."/>
	<entry name="Drukhari/SoulHungerLoyaltyFlavor" value="In einer Welt, in der Loyalität ein Fremdwort ist, zahlt es sich aus, die Massen auf seiner Seite zu wissen – wer weiß schon genau, wann es zum Beispiel nötig sein könnte, einen Lynchmob auf seinen Rivalen zu hetzen? Wenn ein Archon einen Hekatarii-Kult für eine Darbietung in der Arena anheuert oder den Bedürftigen in der Unterstadt von Commorragh Nahrung, Waffen oder Gefangene zukommen lässt, weiß jeder Drukhari, dass er dies nicht aus Mitgefühl, sondern aus Berechnung tut."/>
	<entry name="Drukhari/SoulHungerOutposts" value="Seelenzehnt"/>
	<entry name="Drukhari/SoulHungerOutpostsDescription" value="Erhöht die Ressourcenproduktion."/>
	<entry name="Drukhari/SoulHungerOutpostsFlavor" value="Alle Drukhari, die einer Kabale angehören, sind ihrem Archon treu ergeben und würden nichts tun, um dessen Gunst zu verlieren… Nichtsdestotrotz gibt es einen florierenden Schwarzmarkt für gestohlene Ressourcen, und dieser illegale Handel erfreut sich vor allem weiter weg vom Zentrum der Macht großer Beliebtheit. Wenn der Befehl kommt, den Zehnten zu entrichten, ist es daher erstaunlich leicht, die nötigen Abgaben zusammenzubekommen."/>
	<entry name="Drukhari/SoulHarvest" value="<string name='Actions/Drukhari/SoulHarvest'/>"/>
	<entry name="Drukhari/SoulHarvestDescription" value="Erhöht den Schaden und sorgt dafür, dass beim Ausschalten von feindlichen Einheiten Einfluss generiert wird."/>
	<entry name="Drukhari/SoulHarvestFlavor" value="<string name='Actions/Drukhari/SoulHarvestFlavor'/>"/>
	<entry name="Drukhari/FeastOfTorment" value="Fest der Qualen"/>
	<entry name="Drukhari/FeastOfTormentDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="Drukhari/FeastOfTormentFlavor" value="Es ist unnötig, die Drukhari zu sadistischen Handlungen zu bewegen, schließlich entspricht es ihrem Wesen. Da sie jedoch den Zorn ihres Archons fürchten, ist es bisweilen notwendig, ihnen die ausdrückliche Erlaubnis zu erteilen, die unschuldige Bevölkerung von Gladius Primus zu foltern, anstatt sie direkt nach Commorragh zu schicken. Nebenbei bemerkt hat das Blut Unschuldiger eine verjüngende Wirkung."/>
	<entry name="Drukhari/SpiritProbe" value="<string name='Actions/Drukhari/SpiritProbe'/>"/>
	<entry name="Drukhari/SpiritProbeDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Drukhari/SpiritProbeFlavor" value="<string name='Actions/Drukhari/SpiritProbeFlavor'/>"/>
	<entry name="Drukhari/ToweringArrogance" value="<string name='Actions/Drukhari/ToweringArrogance'/>"/>
	<entry name="Drukhari/ToweringArroganceDescription" value="Verringert den Moralverlust."/>
	<entry name="Drukhari/ToweringArroganceFlavor" value="<string name='Actions/Drukhari/ToweringArroganceFlavor'/>"/>
	<entry name="Drukhari/WealthPlunder" value="Studien zur Agonie"/>
	<entry name="Drukhari/WealthPlunderDescription" value="Sorgt dafür, dass beim Ausschalten einer feindlichen Einheit Forschungspunkte generiert werden."/>
	<entry name="Drukhari/WealthPlunderFlavor" value="„Dem Tod sind sie nahe, doch ich halte sie im Diesseits fest. Die Geheimnisse, die sie flüstern… ihr Flehen… Taktiken, Erfindungen, Wissen über die andere Seite und weitere Enthüllungen… Nichts davon behalten sie für sich, auf dass das Leiden aufhören möge. Aber warum sollte es aufhören? Wenn es doch so… produktiv ist.“ – Arkanic, Haemonculus der Kabale der Letzten Klinge"/>
	<entry name="Drukhari/WeaponRacks" value="Waffenvorrat"/>
	<entry name="Drukhari/WeaponRacksDescription" value="Die Waffen von Einheiten, die diese Fahrzeugeinheit verlassen, erhalten für eine Runde die Eigenschaft „Synchronisiert“."/>
	<entry name="Drukhari/WeaponRacksFlavor" value="Einige Fahrzeuge der Drukhari verfügen über einen zusätzlichen Vorrat an Waffen zur Infanterieabwehr an Deck. Dies erlaubt es ihren Passagieren, bedenkenlos ihre Magazine leerzuschießen und den Feind mit einem wahren Feuerregen einzudecken, ehe sie vor dem Verlassen des Fahrzeugs einfach ihre Waffen fallen lassen und voll geladenen Ersatz erhalten."/>
	<entry name="Drukhari/WhirlingDeath" value="<string name='Actions/Drukhari/WhirlingDeath'/>"/>
	<entry name="Drukhari/WhirlingDeathDescription" value="Trifft alle Kämpfer der Zieleinheit."/>
	<entry name="Drukhari/WhirlingDeathFlavor" value="<string name='Actions/Drukhari/WhirlingDeathFlavor'/>"/>
	<entry name="EavyArmour" value="Starke Panzerung"/>
	<entry name="EavyArmourDescription" value="Erhöht die Panzerung."/>
	<entry name="EavyArmourFlavor" value="Die starke Panzerung der Orks wird aus Eisenschrott, Blech und erbeuteten Panzerplatten gefallener Feinde zusammengehämmert. Sie mag nicht immer genau sitzen und gut aussehen, aber sie bietet dem Träger ordentlichen Schutz."/>
	<entry name="Eldar/AerobaticGrace" value="Anmutiger Flug"/>
	<entry name="Eldar/AerobaticGraceDescription" value="Gewährt Fernkampf-Schadensreduktion, falls die Einheit in dieser Runde bewegt wurde."/>
	<entry name="Eldar/AerobaticGraceFlavor" value="Die Harlekine sind wahrlich meisterhafte Tänzer auf dem Schlachtfeld, doch auch die Schimmerspeere wissen mit ihrer Anmut zu beeindrucken, wenn sie mit ihren Jetbikes scheinbar Pirouetten drehen, um dem feindlichen Bolterfeuer auszuweichen und auf den Feind zuzustürmen."/>
	<entry name="Eldar/AircraftBuildingBonus" value="Ruf des Kurnous"/>
	<entry name="Eldar/AircraftBuildingBonusDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Eldar/AircraftBuildingBonusFlavor" value="Die Blutjäger sind Kurnous zur Treue verpflichtet, dem Aeldari-Gott der Jagd. Er ist zwar bereits seit sechzig Millionen Jahren tot, doch wenn er angerufen wird, können die Aeldari bei der Rekrutierung und bei Bauvorhaben ungeahnte Kräfte freisetzen."/>
	<entry name="Eldar/AncientDoom" value="Alte Nemesis"/>
	<entry name="Eldar/AncientDoomDescription" value="Erhöht die Genauigkeit und den erlittenen Schaden gegen Einheiten des Chaos."/>
	<entry name="Eldar/AncientDoomFlavor" value="Die Aeldari verabscheuen und fürchten „Sie, die dürstet“ mehr als alles andere, denn Slaanesh verkörpert für sie ihren leibhaftigen Untergang."/>
	<entry name="Eldar/AssaultWeaponBonus" value="Submolekulare Munition"/>
	<entry name="Eldar/AssaultWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Eldar/AssaultWeaponBonusFlavor" value="Die einzigartige Shurikenmunition der Aeldari besteht aus monomolekularen, rotierenden Klingen, die mit großer Geschwindigkeit abgefeuert werden. Allerdings könnte man Moleküle als unnötig einschränkend erachten, wenn man doch auch kleinere, tödlichere Konstrukte verwenden kann."/>
	<entry name="Eldar/AssuredDestruction" value="Sichere Vernichtung"/>
	<entry name="Eldar/AssuredDestructionDescription" value="Erhöht den Schaden gegen Fahrzeuge."/>
	<entry name="Eldar/AssuredDestructionFlavor" value="„Wenn ein Panzerkommandant töricht genug ist, fünf Aeldari mit Meltern und mehreren Jahrtausenden Kampferfahrung näher als 20 Meter an seinen Panzer herankommen zu lassen, dann verdient er es ehrlich gesagt, als glühender Fleischhaufen im Matsch zu enden.“<br/> – Kommissar Gruber"/>
	<entry name="Eldar/AsuryaniArrivals" value="<string name='Actions/Eldar/AsuryaniArrivals'/>"/>
	<entry name="Eldar/AsuryaniArrivalsDescription" value="<string name='Actions/Eldar/AsuryaniArrivalsDescription'/>"/>
	<entry name="Eldar/AsuryaniArrivalsFlavor" value="<string name='Actions/Eldar/AsuryaniArrivalsFlavor'/>"/>
	<entry name="Eldar/AutarchsAssault" value="<string name='Actions/Eldar/AutarchsAssault'/>"/>
	<entry name="Eldar/AutarchsAssaultDescription" value="Erhöht den Schaden."/>
	<entry name="Eldar/AutarchsAssaultFlavor" value="<string name='Actions/Eldar/AutarchsAssaultFlavor'/>"/>
	<entry name="Eldar/AutarchsAssaultPassive" value="<string name='Actions/Eldar/AutarchsAssault'/>"/>
	<entry name="Eldar/BansheeMask" value="Todesfeen-Kampfmaske"/>
	<entry name="Eldar/BansheeMaskDescription" value="<string name='Traits/InfiltrateDescription'/>"/>
	<entry name="Eldar/BansheeMaskFlavor" value="Masken wie diese verstärken den Kampfschrei der Aeldari und verursachen eine psionische Lähmung."/>
	<entry name="Eldar/BattleFocus" value="Kampftrance"/>
	<entry name="Eldar/BattleFocusDescription" value="Aktionen brauchen keine Bewegungspunkte auf."/>
	<entry name="Eldar/BattleFocusFlavor" value="Wenn die Asuryani ihre Masken des Krieges aufsetzen, verfallen sie in eine Kampftrance und sind so von Inbrunst erfüllt, dass sie wieselflink über das Schlachtfeld huschen und ohne haltzumachen ihre Gegner niedermetzeln."/>
	<entry name="Eldar/CityTier2" value="Exoditenexpansion"/>
	<entry name="Eldar/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Eldar/CityTier2Flavor" value="Trotz Jahrtausenden des Umherziehens im All haben einige Weltenläufer der Weltenschiff-Aeldari Zeit auf den Exoditenwelten und Jungfernwelten verbracht und dort gelernt, sesshaft zu sein, indem sie auf althergebrachte Weise Nahrungsmittel angebaut und Phantomkristallbauwerke errichtet haben."/>
	<entry name="Eldar/CityTier3" value="Exoditeninfrastruktur"/>
	<entry name="Eldar/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Eldar/CityTier3Flavor" value="Hier setzen Kristallsänger des Weltenschiffs altbewährte Konstruktionsentwürfe der Exoditen in die Tat um. Da sie bisher nur auf den alten Weltenschiffen tätig waren, ist für sie der Bau einer Stadt eine völlig neue Herausforderung."/>
	<entry name="Eldar/Command" value="<string name='Actions/Eldar/Command'/>"/>
	<entry name="Eldar/CommandDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Eldar/CommandFlavor" value="<string name='Actions/Eldar/CommandFlavor'/>"/>
	<entry name="Eldar/ConstructionBuildingBonus" value="Kristallsänger-Einberufung"/>
	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="Kristallsänger sind rar gesät, selbst unter den verstreuten Aeldari. Doch nun sind sie auf Geheiß des Runenpropheten in großer Zahl zusammengekommen, um diesen einzigartigen Planeten für ihre vom Untergang bedrohte Spezies zu erobern."/>
	<entry name="Eldar/CrackShot" value="Exzellenter Schütze"/>
	<entry name="Eldar/CrackShotDescription" value="Erhöht die Genauigkeit und den Panzerungsdurchschlag."/>
	<entry name="Eldar/CrackShotFlavor" value="Die Feuerdrachen sind mit Fug und Recht als Panzerjäger gefürchtet, und besonders gilt das für ihren Exarchen, der seinen Verband direkt an der Front anführt und ihn mit seiner eigenen Zerstörungskraft zu noch mehr Tod und Vernichtung inspiriert."/>
	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Actions/Eldar/CrystalTargetingMatrix'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixDescription" value="<string name='Actions/Eldar/CrystalTargetingMatrixDescription'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Actions/Eldar/CrystalTargetingMatrixFlavor'/>"/>
	<entry name="Eldar/Doom" value="<string name='Actions/Eldar/Doom'/>"/>
	<entry name="Eldar/DoomDescription" value="Erhöht den erlittenen Schaden."/>
	<entry name="Eldar/DoomFlavor" value="<string name='Actions/Eldar/DoomFlavor'/>"/>
	<entry name="Eldar/ExpertHunter" value="Meisterlicher Jäger"/>
	<entry name="Eldar/ExpertHunterDescription" value="Erhöht den Schaden gegen Monströse Kreaturen, Fahrzeuge und Festungen."/>
	<entry name="Eldar/ExpertHunterFlavor" value="Die Waffen der Schimmerspeere mögen zwar keine besonders hohe Reichweite haben, aber ihre Sternenlanzen und Impulslanzen entwickeln eine gewaltige Angriffskraft, wenn die Schimmerspeere um größere Ziele herumschwirren und nach ihren Schwachstellen Ausschau halten."/>
	<entry name="Eldar/FoodBuildingBonus" value="Meisterliche Kultivierung"/>
	<entry name="Eldar/FoodBuildingBonusDescription" value="Erhöht die Nahrungsproduktion."/>
	<entry name="Eldar/FoodBuildingBonusFlavor" value="Die Aeldari sind so feinsinnig, dass sie selbst ihre Grundnahrungsmittel als kreative Schöpfungen erachten, kultiviert von regelrechten Kunsthandwerkern, die Jahrtausende damit verbracht haben, ihre Kochkünste zu perfektionieren. Verfügen sie über eine reiche Auswahl an Zutaten und Gewürzen, können sie selbst mit den einfachsten Nahrungsmitteln wahre Wunder wirken."/>
	<entry name="Eldar/Forceshield" value="Energieschild"/>
	<entry name="Eldar/ForceshieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Eldar/ForceshieldFlavor" value="Diese leistungsstarken Schildgeneratoren können beinahe jeden Schlag abwehren."/>
	<entry name="Eldar/HeavyWeaponBonus" value="Maschine des Vaul"/>
	<entry name="Eldar/HeavyWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Eldar/HeavyWeaponBonusFlavor" value="Während die Plasmawaffen der Menschen oftmals auch für ihre Anwender lebensgefährlich sind, besteht diese Gefahr bei den eleganten Plasmawaffen der Aeldari nicht. Dank einer zufälligen Entdeckung auf der Planetenoberfläche von Gladius Primus konnten die Kristallsänger und Exarchen diese Waffen sogar noch verbessern."/>
	<entry name="Eldar/HoloFields" value="Holofelder"/>
	<entry name="Eldar/HoloFieldsDescription" value="Erhöht die Schadensreduktion, falls die Einheit in dieser Runde bewegt wurde."/>
	<entry name="Eldar/HoloFieldsFlavor" value="Indem Holofelder kinetische Energie dazu verwenden, die Silhouette des Fahrzeugs flimmern zu lassen und zu verzerren, machen sie es dem Gegner unmöglich, das Fahrzeug genau anzuvisieren, während es über das Schlachtfeld rast."/>
	<entry name="Eldar/InescapableAccuracy" value="Unentrinnbare Präzision"/>
	<entry name="Eldar/InescapableAccuracyDescription" value="Erhöht die Genauigkeit gegen Bikes, Jetbikes, Flieger und Fahrzeuge, die in dieser Runde bewegt wurden."/>
	<entry name="Eldar/InescapableAccuracyFlavor" value="Die Seelenschnitter-Aspektkrieger der Weltenschiff-Aeldari haben in Äonen von Kämpfen gegen ihre Artgenossen, die Drukhari, gelernt, wie sie sogar die durch Holofelder geschützten wendigen Jetbikes ins Visier nehmen können. Solche Ziele mit ihren tödlichen Seelenschnitter-Raketenwerfern zu treffen, ist für sie ein Leichtes – und alles Teil ihrer Nachahmung von Khaine in seiner Rolle als Zerstörer."/>
	<entry name="Eldar/InfantryBuildingBonus" value="Ruf des Asuryan"/>
	<entry name="Eldar/InfantryBuildingBonusDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Eldar/InfantryBuildingBonusFlavor" value="Asurmen begründete den Pfad des Kriegers und war der erste der Phönixkönige, die die Aspektschreine erschufen. Wer ihn anruft, kann dadurch für mehr Krieger auf Gladius Primus sorgen, um den Planeten für die Aeldari zu erobern."/>
	<entry name="Eldar/InfluenceBuildingBonus" value="Lileaths Orakel"/>
	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Erhöht die Generierung von Einfluss."/>
	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="Nur bei sehr wenigen Sterblichen ist die Gabe der Voraussicht so ausgeprägt wie bei Runenpropheten. Es handelt sich dabei um eine mächtige Fähigkeit, die möglicherweise auf die tote Aeldari-Göttin Lileath zurückgeht. Sie hatte einst den Tod der Götter durch die Hand der sterblichen Aeldari vorausgesagt. Ein Orakel, bei dem Lileath angerufen wird, erregt die Aufmerksamkeit aller Aeldari…"/>
	<entry name="Eldar/Jinx" value="Verwünschen"/>
	<entry name="Eldar/JinxDescription" value="Verringert die Panzerung."/>
	<entry name="Eldar/JinxFlavor" value="<string name='Actions/Eldar/ProtectJinxFlavor'/>"/>
	<entry name="Eldar/KhaineAwakened" value="<string name='Actions/Eldar/KhaineAwakened'/>"/>
	<entry name="Eldar/KhaineAwakenedDescription" value="Erhöht die Anzahl der Nahkampfangriffe und den Nahkampfschaden, verringert den Moralverlust und gewährt Immunität gegen Angst und Niederhalten."/>
	<entry name="Eldar/KhaineAwakenedFlavor" value="<string name='Actions/Eldar/KhaineAwakenedFlavor'/>"/>
	<entry name="Eldar/KhainesMight" value="<string name='Actions/Eldar/KhainesMight'/>"/>
	<entry name="Eldar/KhainesMightDescription" value="Erhöht die Anzahl der Nahkampfangriffe."/>
	<entry name="Eldar/KhainesMightFlavor" value="<string name='Actions/Eldar/KhainesMightFlavor'/>"/>
	<entry name="Eldar/LinkedFire" value="<string name='Actions/Eldar/LinkedFire'/>"/>
	<entry name="Eldar/LinkedFireDescription" value="Erhöht den Schaden und den Panzerungsdurchschlag der Prismenkanone."/>
	<entry name="Eldar/LinkedFireFlavor" value="<string name='Actions/Eldar/LinkedFireFlavor'/>"/>
	<entry name="Eldar/MarksmansEye" value="Blick des Scharfschützen"/>
	<entry name="Eldar/MarksmansEyeDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Eldar/MarksmansEyeFlavor" value="Nur ein Blutjäger erachtet das Steuern des schnellsten und wendigsten Fluggefährts der Aeldari als so geringe Herausforderung, dass er dabei auch noch drei verschiedene Waffen mit unterschiedlicher Feuerrate bedienen kann."/>
	<entry name="Eldar/MeleeWeaponBonus" value="Submolekulare Glättung"/>
	<entry name="Eldar/MeleeWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Eldar/MeleeWeaponBonusFlavor" value="Die Monofilamentwaffen der Aeldari sind ohnehin schon äußerst durchschlagskräftig, aber wenn sie auch nur geringfügig mit Phasentechnologie verbessert werden (wie bei der Harlekinliebkosung), können sie selbst die dickste Panzerung durchdringen."/>
	<entry name="Eldar/MindWar" value="<string name='Actions/Eldar/MindWar'/>"/>
	<entry name="Eldar/MindWarDescription" value="Verringert die Genauigkeit."/>
	<entry name="Eldar/MindWarFlavor" value="<string name='Actions/Eldar/MindWarFlavor'/>"/>
	<entry name="Eldar/MoltenBody" value="Glühender Körper"/>
	<entry name="Eldar/MoltenBodyDescription" value="Immun gegenüber Flammen- und Melterwaffen."/>
	<entry name="Eldar/MoltenBodyFlavor" value="Der Avatar des Khaine ist nicht nur das letzte Fragment eines sterbenden Gottes, sondern für sich gesehen auch ein Konstrukt aus äußerst heißem Eisen mit einem Lavakern, am Leben gehalten mit der psionischen Kraft der Aeldari und dem ewigen Zorn der Gottheit. Wird das Eisen weiter erhitzt, schwillt auch der Zorn weiter an…"/>
	<entry name="Eldar/OreBuildingBonus" value="Fördereffizienz"/>
	<entry name="Eldar/OreBuildingBonusDescription" value="Erhöht die Erzgewinnung."/>
	<entry name="Eldar/OreBuildingBonusFlavor" value="Die Aeldari benötigen weniger Metall als andere Rassen, da nahezu alle ihre Bauwerke und Fahrzeuge aus Phantomkristall – materialisierter psionischer Energie – bestehen. Für die geringen Mengen, die sie dennoch benötigen, werden die Fördertechniken stets verbessert."/>
	<entry name="Eldar/PowerField" value="Kraftfeld"/>
	<entry name="Eldar/PowerFieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Eldar/PowerFieldFlavor" value="Kraftfelder zweigen einen Teil der Energieversorgung eines Fahrzeugs ab, um ein schimmerndes Schutzfeld um das Fahrzeug zu erzeugen."/>
	<entry name="Eldar/Protect" value="Schützen"/>
	<entry name="Eldar/ProtectDescription" value="Erhöht die Panzerung."/>
	<entry name="Eldar/ProtectFlavor" value="<string name='Actions/Eldar/ProtectJinxFlavor'/>"/>
	<entry name="Eldar/ReaperRangefinder" value="Seelenschnitter-Zielerfassung"/>
	<entry name="Eldar/ReaperRangefinderDescription" value="Ignoriert die durch „Ausweichmanöver“ und „Geschicktes Ausweichmanöver“ gewährte Fernkampf-Schadensreduktion."/>
	<entry name="Eldar/ReaperRangefinderFlavor" value="In die Helme der Seelenschnitter wurden hochmoderne Zielerfassungsvorrichtungen eingebaut, die Telemetriedaten in Sekundenschnelle berechnen können."/>
	<entry name="Eldar/RemnantsOfTheFall" value="Überlebende des Falls"/>
	<entry name="Eldar/RemnantsOfTheFallDescription" value="Verringert die Wachstumsrate."/>
	<entry name="Eldar/RemnantsOfTheFallFlavor" value="Seit der Geburt von Slaanesh müssen die letzten verbliebenen Aeldari in einer feindseligen Galaxis ums Überleben kämpfen. Sie haben zwar eine äonenlange Lebenserwartung, doch die Kehrseite der Medaille ist eine geringe Fruchtbarkeit und eine lange Schwangerschaftsphase, wodurch es für sie schwierig ist, Verluste auf dem Schlachtfeld zu ersetzen. Auf vielen Weltenschiffen ist die Sterberate deutlich höher als die Geburtenrate."/>
	<entry name="Eldar/ResearchBuildingBonus" value="Zugang zur Schwarzen Bibliothek"/>
	<entry name="Eldar/ResearchBuildingBonusDescription" value="Erhöht die Generierung von Forschungspunkten."/>
	<entry name="Eldar/ResearchBuildingBonusFlavor" value="Die Runenpropheten der Schwarzen Bibliothek waren der Grund dafür, dass sich die Aeldari zum Planeten Gladius Primus aufgemacht haben, und bieten nun ihre Unterstützung an, indem sie Zugang zu ihren Archiven gewähren. Dadurch sollte es möglich sein, der Vergessenheit anheimgefallene Technologien wiederzuentdecken und mit ihrer Hilfe schneller auf diesem Planeten Fuß zu fassen."/>
	<entry name="Eldar/ReturnOfTheAeldari" value="<string name='Actions/Eldar/ReturnOfTheAeldari'/>"/>
	<entry name="Eldar/ReturnOfTheAeldariDescription" value="<string name='Actions/Eldar/ReturnOfTheAeldariDescription'/>"/>
	<entry name="Eldar/ReturnOfTheAeldariFlavor" value="<string name='Actions/Eldar/ReturnOfTheAeldariFlavor'/>"/>
	<entry name="Eldar/RuneArmour" value="Runenrüstung"/>
	<entry name="Eldar/RuneArmourDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Eldar/RuneArmourFlavor" value="Psioniker der Aeldari fertigen für sich selbst elegante, mit Phantomkristallrunen verzierte Rüstungen an. Es heißt, dass Runenrüstungen zum Takt des Herzschlags des Trägers pulsieren, und sie gewähren ihm Schutz sowohl vor Attacken spiritueller als auch physischer Natur."/>
	<entry name="Eldar/Scattershield" value="Streuschild"/>
	<entry name="Eldar/ScattershieldDescription" value="Erhöht die Schadensreduktion und blendet im Nahkampf angreifende Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="Eldar/ScattershieldFlavor" value="Streuschilde werden eingesetzt, um wertvolle Kriegskonstruktionen der Aeldari zu schützen. Es handelt sich dabei um riesige, lüfterförmige Schildgeneratoren, die die Energie feindlicher Angriffe in blendende Blitzschauer vielfarbigen Lichts umwandeln."/>
	<entry name="Eldar/SerpentShield" value="Wellenschild"/>
	<entry name="Eldar/SerpentShieldDescription" value="Erhöht die Panzerung, wenn sich die Waffe des Wellenschilds nicht im Abklingmodus befindet."/>
	<entry name="Eldar/SerpentShieldFlavor" value="<string name='Weapons/SerpentShieldFlavor'/>"/>
	<entry name="Eldar/Skyhunter" value="Himmelsjäger"/>
	<entry name="Eldar/SkyhunterDescription" value="Erhöht den Panzerungsdurchschlag gegen Flieger."/>
	<entry name="Eldar/SkyhunterFlavor" value="Der Geist einer verstorbenen Exarchin vom Weltenschiff Biel-Tan erfüllt die Blutjäger auf Gladius Primus und offenbart Geheimnisse aus jahrtausendelangen Kämpfen der Aeldari gegen ihre Feinde. Mit diesem wiederentdeckten Wissen können die Blutjäger den Himmel über dem Planeten dominieren wie nie zuvor."/>
	<entry name="Eldar/SpiritMark" value="<string name='Actions/Eldar/SpiritMark'/>"/>
	<entry name="Eldar/SpiritMarkDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Eldar/SpiritMarkFlavor" value="<string name='Actions/Eldar/SpiritMarkFlavor'/>"/>
	<entry name="Eldar/SpiritPreservation" value="Erhaltung des Geistes"/>
	<entry name="Eldar/SpiritPreservationDescription" value="Setzt beim Tod Energie frei."/>
	<entry name="Eldar/SpiritPreservationFlavor" value="Die Aeldari sind nur wenige und „Sie, die dürstet“ lauert im Warp, um ihre Seelen zu verschlingen. Allerdings tragen sie Seelensteine bei sich, die im Augenblick ihres Todes ihre Essenz aufnehmen. Zwar erschaudert jeder Aeldari beim Gedanken daran, doch immerhin können die Seelen auf diese Weise noch eine nützliche Aufgabe erfüllen, indem sie die Kriegsmaschinen der Aeldari steuern."/>
	<entry name="Eldar/SpiritStones" value="Seelensteine"/>
	<entry name="Eldar/SpiritStonesDescription" value="Verringert den Moralverlust."/>
	<entry name="Eldar/SpiritStonesFlavor" value="Einige Fahrzeuge der Aeldari sind mit großen Seelensteinen ausgerüstet, die einen Geist enthalten, der das Fahrzeug steuern kann, sollte es außer Gefecht gesetzt werden."/>
	<entry name="Eldar/StarEngines" value="Sternentriebwerke"/>
	<entry name="Eldar/StarEnginesDescription" value="Erhöht die Bewegung."/>
	<entry name="Eldar/StarEnginesFlavor" value="Zwar sind alle Fahrzeuge der Aeldari schnell und wendig, doch solche mit Sternentriebwerken können sich oft schneller fortbewegen, als man mit dem Auge erfassen kann. Niedere Völker können nur über die phänomenale Geschwindigkeit und Wendigkeit eines so ausgerüsteten Fahrzeugs staunen."/>
	<entry name="Eldar/TitanHoloFields" value="Titanen-Holofelder"/>
	<entry name="Eldar/TitanHoloFieldsDescription" value="<string name='Traits/Eldar/HoloFieldsDescription'/>"/>
	<entry name="Eldar/TitanHoloFieldsFlavor" value="„Ich wurde in der Makropole Zeuge des imposantesten Lichtspektakels, das ich je gesehen habe. Überall funkelte und knallte es wie in einem psychodelisch anmutenden Traum. Dann sah ich mich um und überall lagen tote Krieger, und plötzlich rollte ein Xenospanzer an mir vorbei, als wäre ich Luft.“<br/> – Soldat Grande, einziger Überlebender des 4. Irregulären Regiments der Makropolwelt Necromunda"/>
	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
	<entry name="Eldar/TranscendentBlissDescription" value="<string name='Actions/Eldar/TranscendentBlissDescription'/>"/>
	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
	<entry name="Eldar/VectorDancer" value="<string name='Actions/Eldar/VectorDancer'/>"/>
	<entry name="Eldar/VectorDancerDescription" value="<string name='Actions/Eldar/VectorDancerDescription'/>"/>
	<entry name="Eldar/VectorDancerFlavor" value="<string name='Actions/Eldar/VectorDancerFlavor'/>"/>
	<entry name="Eldar/VectoredEngines" value="<string name='Actions/Eldar/VectoredEngines'/>"/>
	<entry name="Eldar/VectoredEnginesDescription" value="<string name='Actions/Eldar/VectoredEnginesDescription'/>"/>
	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Actions/Eldar/VectoredEnginesFlavor'/>"/>
	<entry name="Eldar/VehicleBuildingBonus" value="Ruf des Vaul"/>
	<entry name="Eldar/VehicleBuildingBonusDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Eldar/VehicleBuildingBonusFlavor" value="Auf Gladius wurde ein Seelenstein gefunden, der vor langer Zeit einem Kristallsänger gehörte und hier vor tausenden von Jahren verloren ging. Er offenbarte tiefe Einblicke in die Struktur und Ressourcen des Planeten, und dieses Wissen kann nun genutzt werden, um die Produktionseffizienz auf Gladius Primus zu steigern."/>
	<entry name="Enslaved" value="Versklavt"/>
	<entry name="EnslavedDescription" value="Töten Sie die Enslavers, um die Einheit zu befreien."/>
	<entry name="EreWeGo" value="Jetz' geht'z los!"/>
	<entry name="EreWeGoDescription" value="Erhöht die Bewegung."/>
	<entry name="EreWeGoFlavor" value="<string name='Actions/EreWeGoFlavor'/>"/>
	<entry name="ExtraInfantryArmour" value="Zusätzliche Infanteriepanzerung"/>
	<entry name="ExtraInfantryArmourDescription" value="Erhöht die Panzerung."/>
	<entry name="ExtraMonstrousCreatureArmour" value="Zusätzliche Monströse Kreaturpanzerung"/>
	<entry name="ExtraMonstrousCreatureArmourDescription" value="Erhöht die Panzerung."/>
	<entry name="ExtraVehicleArmour" value="Zusätzliche Fahrzeugpanzerung"/>
	<entry name="ExtraVehicleArmourDescription" value="Erhöht die Panzerung."/>
	<entry name="Fear" value="Angst"/>
	<entry name="FearDescription" value="Verringert in jeder Runde die Moral."/>
	<entry name="FearFlavor" value="<string name='Actions/AuraOfFearFlavor'/>"/>
	<entry name="Fearless" value="Furchtlos"/>
	<entry name="FearlessDescription" value="Verringert den Moralverlust und gewährt Immunität gegen Angst und Niederhalten."/>
	<entry name="FearlessFlavor" value="Furchtlose Truppen ergeben sich nicht und machen nur selten umfassend von Deckung Gebrauch – wenngleich es manchmal klüger wäre, es zu tun."/>
	<entry name="FeelNoPain" value="Verletzungen ignorieren"/>
	<entry name="FeelNoPainDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="FeelNoPainFlavor" value="Dieser Krieger kann trotz schrecklicher Wunden weiterkämpfen. Es ist unklar, ob dies auf seine Willenskraft, auf bionische Augmentation oder schlicht und einfach auf zwielichtige Hexerei zurückzuführen ist."/>
	<entry name="Flail" value="Flegel"/>
	<entry name="FlailDescription" value="Erhöht die Nahkampf-Schadensreduktion."/>
	<entry name="FlailFlavor" value="Diese Krieger sind mit einer flegelähnlichen Waffe ausgerüstet und behindern Angreifer beim Vorrücken."/>
	<entry name="Flame" value="Flamme"/>
	<entry name="FlameDescription" value="Klassifizierung."/>
	<entry name="Fleet" value="Flink"/>
	<entry name="FleetDescription" value="Erhöht die Bewegung."/>
	<entry name="FleetFlavor" value="Diese Krieger sind außergewöhnlich flink und können sich schneller über das Schlachtfeld bewegen als ihre schwerfälligen Gegner."/>
	<entry name="Fleshbane" value="Lebensfluch"/>
	<entry name="FleshbaneDescription" value="Erhöht den Schaden gegen Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="Flyer" value="Flieger"/>
	<entry name="FlyerDescription" value="Kann über Klippen, Wasser und feindliche Einheiten hinwegfliegen. Kann keine Artefakte oder Außenposten einnehmen. Ignoriert die Kontrollzonen feindlicher Bodeneinheiten. Kann nicht mit Bodennahkampfwaffen angegriffen werden und ist ohne Flugabwehr generell schwer vom Boden aus zu treffen. Kein Malus für schwere Waffen und Geschütze."/>
	<entry name="FlyerFlavor" value="Auch am Himmel über dem Schlachtfeld tobt der Krieg. Jagdflugzeuge und Bomber rasen durch die Lüfte, um für die Bodentruppen Feuerunterstützung zu leisten, wenn sie sich nicht gerade erbitterte Luftkämpfe mit dem Feind liefern."/>
	<entry name="Forest" value="Wald"/>
	<entry name="ForestDescription" value="Erhöht die Fernkampf-Schadensreduktion bei Infanterieeinheiten."/>
	<entry name="ForestFlavor" value="<string name='Features/ForestFlavor'/>"/>
	<entry name="ForestStealth" value="Waldtarnung"/>
	<entry name="ForestStealthDescription" value="Erhöht die Fernkampf-Schadensreduktion in Wäldern."/>
	<entry name="Fortification" value="Befestigungsanlage"/>
	<entry name="FortificationDescription" value="Stationäre, befestigte Einheit, die im bewaffneten Zustand die Kontrolle über angrenzende Außenposten übernimmt."/>
	<entry name="FullThrottle" value="„Gas geben!“"/>
	<entry name="FullThrottleDescription" value="Erhöht die Bewegung."/>
	<entry name="FuelledByRage" value="Macht der Wut"/>
	<entry name="FuelledByRageDescription" value="Erhöht die Anzahl der Angriffe, wenn die Trefferpunkte der Einheit sinken."/>
	<entry name="FuriousCharge" value="Rasender Angriff"/>
	<entry name="FuriousChargeDescription" value="Erhöht den Nahkampfschaden."/>
	<entry name="FuriousChargeFlavor" value="Manche Kämpfer nutzen den Schwung des Angriffs als Brennstoff für den Zorn, der in ihnen lodert."/>
	<entry name="Gargantuan" value="Gigantisch"/>
	<entry name="GargantuanDescription" value="Klassifizierung."/>
	<entry name="GargantuanFlavor" value="Gigantische Kreaturen sind von so immenser Größe, dass sie es mit ganzen Armeen aufnehmen können. Sie türmen sich über dem Schlachtfeld auf und lassen den Boden erbeben, wenn sie sich dem Feind nähern, um dann kleinere Kreaturen einfach unter ihren Füßen zu zermalmen."/>
	<entry name="Gauss" value="Gauss"/>
	<entry name="GaussDescription" value="Der minimale Schaden ist abhängig von den Trefferpunkten der Zieleinheit."/>
	<entry name="GaussFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="GaussDamage" value="Atomarer Desintegrator"/>
	<entry name="GaussDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="GaussDamageFlavor" value="Selbst in einer Galaxie voller grausamer, riesiger und schier verrückter Waffen wird die Gausstechnologie besonders gefürchtet. Das Geschoss eines Bolters explodiert einfach in seinem Opfer, aber eine Gausswaffe zerlegt es Atom für Atom in seine Einzelteile."/>
	<entry name="GetsHot" value="Überhitzen"/>
	<entry name="GetsHotDescription" value="Jedes Mal, wenn eine Fernkampfwaffe abgefeuert wird, verliert die Einheit Trefferpunkte."/>
	<entry name="GetsHotFlavor" value="Manche Waffen werden mit instabilen Energiequellen betrieben und laufen bei jedem Schuss Gefahr zu überhitzen – oft zum Leidwesen desjenigen Kämpfers, der die Waffe einsetzt."/>
	<entry name="Graviton" value="Graviton"/>
	<entry name="GravitonDescription" value="Der Schaden ist abhängig von der Panzerung der Zieleinheit."/>
	<entry name="GravitonFlavor" value="Manche Waffen können feindliche Einheiten in ihren eigenen Rüstungen zermalmen."/>
	<entry name="Grenade" value="Granate"/>
	<entry name="GrenadeDescription" value="Klassifizierung."/>
	<entry name="GrotRiggers" value="Grothälfaz"/>
	<entry name="GrotRiggersDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="GrotRiggersFlavor" value="Ob sie abgefallene Fahrzeugteile mit Nietpistolen wieder anbringen oder einfach nur aussteigen und anschieben: Ein Team aus Grothälfaz kann seinen Beitrag dazu leisten, ein Ork-Fahrzeug im Kampf zu halten – selbst dann noch, wenn es schon längst hätte auseinanderfallen müssen."/>
	<entry name="GroundAttack" value="Bodenangriff"/>
	<entry name="GroundAttackDescription" value="Kann nur Bodenziele ins Visier nehmen."/>
	<entry name="GunnersKillOnSight" value="„Bordschützen, Feuer bei Sichtkontakt!“"/>
	<entry name="GunnersKillOnSightDescription" value="Erhöht die Anzahl der Fernkampfangriffe."/>
	<entry name="HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="HammerOfWrathDescription" value="Erhöht den Schaden von Nicht-Bombenwaffen."/>
	<entry name="HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Hammerhand" value="<string name='Actions/Hammerhand'/>"/>
	<entry name="HammerhandDescription" value="<string name='Actions/HammerhandDescription'/>"/>
	<entry name="HammerhandFlavor" value="<string name='Actions/HammerhandFlavor'/>"/>
	<entry name="Hallucination" value="<string name='Actions/Hallucination'/>"/>
	<entry name="HallucinationDescription" value="Verringert die Anzahl der Angriffe."/>
	<entry name="HallucinationFlavor" value="<string name='Actions/HallucinationFlavor'/>"/>
	<entry name="HarvestResourceFeatures" value="Ressourcenabbau von Ressourcenhexfeldern"/>
	<entry name="HarvestResourceFeaturesDescription" value="Baut Ressourcen von angrenzenden speziellen Ressourcenhexfeldern ab."/>
	<entry name="Haywire" value="Impulswaffe"/>
	<entry name="HaywireDescription" value="Erhöht den Schaden gegen Fahrzeuge und Festungen und ignoriert ihre Panzerung."/>
	<entry name="HaywireFlavor" value="Impulswaffen entfesseln todbringende elektromagnetische Energie."/>
	<entry name="Headquarters" value="Hauptquartier"/>
	<entry name="HeadquartersDescription" value="Wird die Hauptquartier-Einheit zerstört, fällt die gesamte Stadt."/>
	<entry name="HeavyWeapon" value="Schwer"/>
	<entry name="HeavyWeaponDescription" value="Verringert die Genauigkeit nach dem Bewegen."/>
	<entry name="HellstormTemplate" value="Höllensturm-Flammenschablone"/>
	<entry name="HellstormTemplateDescription" value="<string name='Traits/BeamDescription'/>"/>
	<entry name="Hero" value="Held"/>
	<entry name="HeroDescription" value="Erhöht die Schadensreduktion bei Infanterieeinheiten."/>
	<entry name="HeroFlavor" value="Was bedeutet es, ein Held zu sein? Viele sehen in einem Helden einen ungewöhnlich tapferen, klugen oder starken Angehörigen einer Spezies. In einem Universum immerwährender Kriege trifft die Bezeichnung „Held“ leider am ehesten auf jemanden zu, der besonders effizient darin ist, Feinde zu töten und dabei selbst irgendwie zu überleben."/>
	<entry name="HighPower" value="Starke Energie"/>
	<entry name="HighPowerDescription" value="Aktiv vor dem Bewegen."/>
	<entry name="HitAndRun" value="Auf nach vorn"/>
	<entry name="HitAndRunDescription" value="Ignoriert feindliche Kontrollzonen."/>
	<entry name="HitAndRunFlavor" value="Manche Truppen haben einen flexiblen Kampfstil. Sie können den Feind in den Nahkampf verwickeln und sich dann wieder so schnell aus diesem Nahkampf lösen, dass sie kurz darauf mit neuem Elan den nächsten Gegner angreifen können."/>
	<entry name="Homing" value="Zielsucher"/>
	<entry name="HomingDescription" value="Erfordert keine Sichtlinie."/>
	<entry name="IgnoresCover" value="Deckung ignorieren"/>
	<entry name="IgnoresCoverDescription" value="Ignoriert die Fernkampf-Schadensreduktion der Zieleinheit."/>
	<entry name="IgnoresCoverFlavor" value="Mit dieser Waffe kann Munition abgefeuert werden, die die Deckung des Feindes durchdringt."/>
	<entry name="Illuminated" value="Angestrahlt"/>
	<entry name="IlluminatedDescription" value="Verringert die Fernkampf-Schadensreduktion."/>
	<entry name="Immobilized" value="Bewegungsunfähig"/>
	<entry name="ImmobilizedDescription" value="Die Einheit kann sich nicht bewegen."/>
	<entry name="ImperialRuin" value="<string name='Features/ImperialRuin'/>"/>
	<entry name="ImperialRuinDescription" value="Erhöht die Fernkampf-Schadensreduktion bei Infanterieeinheiten."/>
	<entry name="ImperialRuinFlavor" value="<string name='Features/ImperialRuinFlavor'/>"/>
	<entry name="ImperialSplendour" value="Imperialer Glanz"/>
	<entry name="ImperialSplendourDescription" value="Erhöht den Einfluss."/>
	<entry name="ImperialSplendourFlavor" value="Erfolgreiche planetare Gouverneure bringen den neuen Reichtum ihres Herrschaftsgebiets mit Monumenten zu Ehren des Imperiums zum Ausdruck. Es handelt sich dabei um riesige Basiliken aus Plasbeton und Statuen von Space Marines, Primarchen und dem Imperator höchstselbst."/>
	<entry name="Infiltrate" value="Infiltrieren"/>
	<entry name="InfiltrateDescription" value="Verhindert, dass die Einheit das Ziel von Abwehrfeuer-Attacken wird."/>
	<entry name="InfiltrateFlavor" value="Viele Armeen setzen Aufklärungstrupps ein, die sich tagelang versteckt halten und auf den richtigen Moment warten, um zuzuschlagen."/>
	<entry name="InstantDeath" value="Sofort ausschalten"/>
	<entry name="InstantDeathDescription" value="Erhöht signifikant den Schaden gegen Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="InstantDeathFlavor" value="Manchmal reicht ein Treffer, um den Gegner auf der Stelle zu töten, egal wie zäh er auch sein mag."/>
	<entry name="Invulnerable" value="Unverwundbar"/>
	<entry name="InvulnerableDescription" value="Diese Einheit ist unverwundbar."/>
	<entry name="IronHalo" value="Stählerner Stern"/>
	<entry name="IronHaloDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="IronHaloFlavor" value="Der Stählerne Stern ist eine ehrenhafte Auszeichnung für Kommandeure der Space Marines und ein Symbol ihrer außergewöhnlichen Tapferkeit und Weisheit. Er wird für gewöhnlich am Rückenmodul angebracht oder in ein gepanzertes Halsband eingefasst und erzeugt ein Energiefeld, das selbst gegen die schlagkräftigsten feindlichen Waffen schützt."/>
	<entry name="IronWill" value="Eiserner Wille"/>
	<entry name="IronWillDescription" value="<string name='Actions/IronWillDescription'/>"/>
	<entry name="IronWillFlavor" value="<string name='Actions/IronWillFlavor'/>"/>
	<entry name="ItWillNotDie" value="Es stirbt nicht"/>
	<entry name="ItWillNotDieDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="ItWillNotDieFlavor" value="In den dunklen Winkeln der Galaxie existieren Kreaturen, die sich mit erschreckender Geschwindigkeit heilen."/>
	<entry name="JetPack" value="Schwebemodul"/>
	<entry name="JetPackDescription" value="Die Einheit kann sich über Wasser bewegen und ignoriert den Malus durch Flüsse und Stahlkraut."/>
	<entry name="JetPackFlavor" value="Schwebemodule sollen den Kämpfern als stabile Plattform zum Feuern dienen und sind weniger dazu gedacht, in den Nahkampf zu gelangen."/>
	<entry name="Jetbike" value="Jetbike"/>
	<entry name="JetbikeDescription" value="Die Einheit kann sich über Wasser bewegen und ignoriert den Malus durch Flüsse und Stahlkraut."/>
	<entry name="JetbikeFlavor" value="Die Technologie, mithilfe derer zuverlässige, schnelle kleine Antigraveinheiten gebaut werden, ist komplexer, als man auf den ersten Blick meinen mag. Für eine lange Zeit besaßen nur die Aeldari das technische Wissen, doch die Orks haben jüngst ihren eigenen grobschlächtigen Ableger entwickelt: den Deffkopta. Die Jetbikes der Necrons werden von den unberechenbaren Tomb Blades gesteuert."/>
	<entry name="Jink" value="<string name='Actions/Jink'/>"/>
	<entry name="JinkDescription" value="Erhöht die Fernkampf-Schadensreduktion, aber verringert die Genauigkeit."/>
	<entry name="JinkFlavor" value="<string name='Actions/JinkFlavor'/>"/>
	<entry name="Killshot" value="Todesschuss"/>
	<entry name="KillshotDescription" value="Erhöht den Schaden gegen Monströse Kreaturen, Fahrzeuge und Festungen, wenn diese an einen nicht-feindlichen Predator angrenzen."/>
	<entry name="KillshotFlavor" value="Es gibt nicht viel, was man der gebündelten Kraft einer Predator-Scharfrichterschwadron entgegensetzen kann. Es handelt sich dabei um drei Predators der Space Marines, deren Schützen ihr Geschützfeuer so genau aufeinander abstimmen, dass sie selbst gewaltige Xenos-Kreaturen und riesige Kriegsmaschinen mit konzentriertem Sperrfeuer ausschalten können."/>
	<entry name="Lance" value="Lanze"/>
	<entry name="LanceDescription" value="Schwächt die Panzerung der Zieleinheit."/>
	<entry name="LanceFlavor" value="Diese Lanzenwaffe ist der Schrecken eines jeden Panzerkommandanten, da sie einen gebündelten Energiestrahl ausstößt, der sich durch jede Panzerung bohren kann – ganz gleich, wie dick sie ist."/>
	<entry name="LargeBlast" value="Große Explosion"/>
	<entry name="LargeBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="LargeBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="LastStand" value="Letztes Gefecht"/>
	<entry name="LastStandDescription" value="Erhöht die Moral."/>
	<entry name="LastStandFlavor" value="„Hier stehen wir und hier werden wir sterben, nicht gebeugt und nicht gebrochen. Wenn der Tod mit seiner Klaue nach uns greift, werden wir ihm bis zu unserem letzten Atemzug trotzen!“<br/> – Chaplain Armand Titus von den Howling Griffons"/>
	<entry name="LifeDrain" value="Lebensentzug"/>
	<entry name="LifeDrainDescription" value="Erhöht den Schaden gegen Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="LifeSteal" value="Lebensraub"/>
	<entry name="LifeStealDescription" value="Wandelt bei dieser Einheit wie auch bei angrenzenden Infanterieeinheiten und Monströsen Kreaturen Schaden in Heilung um."/>
	<entry name="LinebreakerBombardment" value="Linienbrecher-Bombardement"/>
	<entry name="LinebreakerBombardmentDescription" value="Die Einheit ignoriert die Fernkampf-Schadensreduktion der Zieleinheit, wenn sie an einen nicht-feindlichen Vindicator angrenzt."/>
	<entry name="Luminagen" value="Luminagen"/>
	<entry name="LuminagenDescription" value="Verringert vorübergehend die Fernkampf-Schadensreduktion der Zieleinheit."/>
	<entry name="LocatorBeacon" value="Peilsender"/>
	<entry name="LocatorBeaconDescription" value="Ermöglicht das Absetzen aus dem Orbit ohne Verbrauch von Aktionspunkten, wenn das Absetzen neben dieser Einheit erfolgt."/>
	<entry name="LocatorBeaconFlavor" value="Peilsender werden oft von Scout Bikers getragen oder in Drop Pods montiert und bestehen aus einer Signalbake, einem Breitband-Kommunikator und einem Geo-Positionsbestimmer. Bei Aktivierung übermittelt der Peilsender detaillierte Daten in das taktische Netz und erlaubt den präzisen Einsatz von Reserveverbänden."/>
	<entry name="LowPower" value="Niedrige Energie"/>
	<entry name="LowPowerDescription" value="Aktiv nach dem Bewegen."/>
	<entry name="MachineEmpathy" value="Maschinenempathie"/>
	<entry name="MachineEmpathyDescription" value="Stellt Trefferpunkte wieder her."/>
	<entry name="MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="MannedWeapon" value="Bemannte Waffe"/>
	<entry name="MannedWeaponDescription" value="Benötigt zum Feuern transportierte Einheiten."/>
	<entry name="MassiveBlast" value="Massive Explosion"/>
	<entry name="MassiveBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="MassiveBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="MasterCrafted" value="Meisterhaft"/>
	<entry name="MasterCraftedDescription" value="Erhöht die Genauigkeit."/>
	<entry name="MasterCraftedFlavor" value="Bei manch einer Waffe handelt es sich um ein liebevoll gepflegtes Artefakt, das mit Fähigkeiten gefertigt wurde, über die heutzutage niemand mehr verfügt. Auch wenn die Bauweise einer solchen Waffe variieren mag, so ist sie doch stets ein Zeugnis perfektionierter Waffenschmiedekunst."/>
	<entry name="Melee" value="Nahkampf"/>
	<entry name="MeleeDescription" value="Keine Abwehrfeuer-Attacken möglich."/>
	<entry name="MeleeFlavor" value="<string name='Weapons/MeleeFlavor'/>"/>
	<entry name="Melta" value="Melter"/>
	<entry name="MeltaDescription" value="Erhöht den Panzerungsdurchschlag bei halber Reichweite."/>
	<entry name="MeltaFlavor" value="<string name='Weapons/MeltaFlavor'/>"/>
	<entry name="MindControl" value="Gedankenmanipulation"/>
	<entry name="MindControlDescription" value="Verringert in jeder Runde die Moral."/>
	<entry name="Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="MisfortuneDescription" value="Erhöht den erlittenen Schaden."/>
	<entry name="MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="Missing" value="Fehlt"/>
	<entry name="MobRule" value="Grüner Mob"/>
	<entry name="MobRuleDescription" value="Verringert den Moralverlust abhängig von der Anzahl an nicht-feindlichen Einheiten in der Nähe."/>
	<entry name="MobRuleFlavor" value="Orks sind einfach gestrickte, brutale Kreaturen, die gerne kämpfen und ihr Selbstvertrauen aus der zahlenmäßigen Überlegenheit ziehen."/>
	<entry name="MobileCommand" value="Kommandofahrzeug"/>
	<entry name="MobileCommandDescription" value="Einheiten, die in diesem Fahrzeug transportiert werden, nutzen weiterhin ihre passiven Fähigkeiten."/>
	<entry name="MobileCommandFlavor" value="Transportfahrzeuge schirmen die Truppen im Inneren für gewöhnlich so gut ab, dass sie zwar geschützt, aber nicht mehr handlungsfähig sind, solange sie ihren Zielort nicht erreicht haben. Waffenschlitze können Abhilfe schaffen, verringern allerdings die Panzerung. Bei der Kommandoausführung der imperialen Chimäre besteht dieses Problem nicht, da sie mit Kommunikationsgeräten ausgerüstet ist, die keine Verringerung der Panzerung nach sich ziehen, während Befehlshaber im Inneren der Chimäre weiterhin ihre Truppen koordinieren können."/>
	<entry name="Monofilament" value="Monofilament"/>
	<entry name="MonofilamentDescription" value="Skaliert die Genauigkeit gegenläufig zur maximalen Geschwindigkeit der Zieleinheit. Erhöht den Panzerungsdurchschlag."/>
	<entry name="MonofilamentFlavor" value="<string name='Weapons/MonofilamentFlavor'/>"/>
	<entry name="MonstrousCreature" value="Monströse Kreatur"/>
	<entry name="MonstrousCreatureDescription" value="Klassifizierung."/>
	<entry name="MonstrousCreatureFlavor" value="Hierbei handelt es sich um emporragende Giganten, die in der Lage sind, selbst einen Panzer zu zerquetschen. Als Beispiel sei der Carnifex der Tyraniden genannt, eine durch Biotechnik erzeugte Kreatur, die zu einem lebenden Rammbock weiterentwickelt wurde."/>
	<entry name="MoraleSoak" value="Schützende Moral"/>
	<entry name="MoraleSoakDescription" value="Verringert den Schaden abhängig vom Moralwert der Zieleinheit."/>
	<entry name="MoveThroughCover" value="Durch Deckung bewegen"/>
	<entry name="MoveThroughCoverDescription" value="Hebt den Bewegungsmalus in Wäldern und imperialen Ruinen auf."/>
	<entry name="MoveThroughCoverFlavor" value="Manche Kämpfer sind sehr geschickt darin, sich in unwegsamem Gelände fortzubewegen."/>
	<entry name="Necrons/AircraftBuildingBonus" value="Orphischer Wille"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="Beim Durchqueren dieser Steinkonstruktion scheint das Raumzeitgefüge für die Flugeinheiten der Necrons nicht mehr zu gelten, wenn sie absonderlich gestreckt und mit einem Flimmern bald der Existenz scheinbar entschwinden, bald jedoch wieder im Hier und Jetzt auftauchen."/>
	<entry name="Necrons/AttackCityBonus" value="Statische Zielerfassung"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Erhöht die Genauigkeit gegen Einheiten in Städten."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="Die Technologien der Necrons mögen uralten, staubigen Grüften entsprungen sein, aber nur wenige Rassen konnten den Necrons in den letzten 60 Millionen Jahren bei der Manipulation von Dimensionsvariablen das Wasser reichen. Keine Mauer, kein Bunker und kein Lasergitter kann Angriffe derartiger Waffen abwehren."/>
	<entry name="Necrons/BlastDamage" value="Gestreute Zielerfassung"/>
	<entry name="Necrons/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Necrons/BlastDamageFlavor" value="Die Menschen legen bei ihren Zielerfassungssystemen den Schwerpunkt auf Genauigkeit und verringern die Streuung der Waffen. Die höherentwickelten Necrons hingegen tun überraschenderweise genau das Gegenteil: Bei ihren Zielerfassungssystemen ist die Streuung der Waffen erhöht, und aus irgendwelchen Gründen steigert dies die Trefferwahrscheinlichkeit."/>
	<entry name="Necrons/BloodyCrusade" value="<string name='Actions/Necrons/BloodyCrusade'/>"/>
	<entry name="Necrons/BloodyCrusadeDescription" value="Erhöht den Schaden."/>
	<entry name="Necrons/CityTier2" value="Eingeleitete Grabung"/>
	<entry name="Necrons/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Necrons/CityTier2Flavor" value="Weitere Teile der uralten Stadt wurden von den Sklaven ausgegraben, die nun damit beschäftigt sind, die lange verschütteten Gebäude wieder instand zu setzen."/>
	<entry name="Necrons/CityTier3" value="Ausgegrabene Stadt"/>
	<entry name="Necrons/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Necrons/CityTier3Flavor" value="Die gesamte Nekropole wurde ausgegraben und erstrahlt wieder in ihrem alten schrecklichen Glanz."/>
	<entry name="Necrons/ConstructionBuildingBonus" value="Verstärkte Sklaven"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="Dank geringfügiger Modifizierungen an den Körpern und Gehirnen Ihrer Sklaven brauchen sie weniger Schlaf und können so lange arbeiten, wie es ihre Zähigkeit zulässt."/>
	<entry name="Necrons/CtanNecrodermis" value="C'tan-Necrodermis"/>
	<entry name="Necrons/CtanNecrodermisDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Necrons/CtanNecrodermisBlast" value="C'tan-Necrodermis-Explosion"/>
	<entry name="Necrons/CtanNecrodermisBlastDescription" value="Wenn die Einheit stirbt, erleiden Einheiten in der Nähe Schaden."/>
	<entry name="Necrons/DefensiveProtocols" value="<string name='Actions/Necrons/DefensiveProtocols'/>"/>
	<entry name="Necrons/DefensiveProtocolsDescription" value="Erhöht die Panzerung."/>
	<entry name="Necrons/DestructionProtocols" value="<string name='Actions/Necrons/DestructionProtocols'/>"/>
	<entry name="Necrons/DestructionProtocolsDescription" value="Erhöht den erlittenen Schaden."/>
	<entry name="Necrons/Dynasty" value="<string name='Actions/Necrons/Dynasty'/>"/>
	<entry name="Necrons/DynastyDescription" value="<string name='Actions/Necrons/DynastyDescription'/>"/>
	<entry name="Necrons/EnergyBuildingBonus" value="Himmlische Akkumulatoren"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Erhöht die Energiegewinnung."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="Außen hat sich an diesem grotesken Sarkophag wenig geändert – in seinem Inneren jedoch stabilisieren unheimliche, finstere Crypteks eine stark verbesserte Apparatur."/>
	<entry name="Necrons/EntropicStrike" value="Entropiestoß"/>
	<entry name="Necrons/EntropicStrikeDescription" value="Der minimale Schaden ist abhängig von den Trefferpunkten der Zieleinheit."/>
	<entry name="Necrons/EternityGate" value="Tor der Ewigkeit"/>
	<entry name="Necrons/EternityGateDescription" value="Necron-Einheiten können zu angrenzenden Hexfeldern teleportiert werden."/>
	<entry name="Necrons/EternityGateFlavor" value="Das Tor der Ewigkeit eines Monoliths ist ein Dimensionskorridor zwischen dem Schlachtfeld und einer Gruftwelt, über den ganze Legionen von Necron-Kriegern enorme Distanzen zurücklegen und sich mit einem einzigen Schritt in die Schlacht stürzen können."/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="Erhöht die Hexenfeuer-Schadensreduktion."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GravityPulse" value="<string name='Actions/Necrons/GravityPulse'/>"/>
	<entry name="Necrons/GravityPulseDescription" value="Verringert die Bewegung von Fliegern, Jetbikes und Antigraveinheiten in Reichweite und fügt ihnen Schaden zu."/>
	<entry name="Necrons/GrowthBonus" value="Reanimationsrituale"/>
	<entry name="Necrons/GrowthBonusDescription" value="Erhöht die Wachstumsrate."/>
	<entry name="Necrons/GrowthBonusFlavor" value="Die Entwicklung effizienterer Protokolle zur Bergung der lange begrabenen Necrons aus ihren Grüften verringert die Verluste, die durch ungeschickte Sklavenausgräber entstehen."/>
	<entry name="Necrons/HardwiredForDestruction" value="Verdrahtet für Zerstörung"/>
	<entry name="Necrons/HardwiredForDestructionDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Necrons/HardwiredForDestructionFlavor" value="Die Necrons des Destruktorkults sind hasserfüllte Feinde des Lebens und besessen von der Ausrottung aller empfindungsfähigen Wesen. Sie beauftragen die Kryptomanten damit, ihre Körper zu modifizieren und sie so in optimierte Tötungsmaschinen zu verwandeln. Auf Gladius Primus stellen dies die kürzlich erwachten Skorpekh-Destruktoren eindrucksvoll zur Schau, indem sie mit ihren gigantischen Klingen gnadenlos präzise Treffer landen."/>
	<entry name="Necrons/HousingBuildingBonus" value="Kompression der Unterkünfte"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Erhöht das Bevölkerungslimit."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="Dank einer Anpassung der Dimensionsbeschränkungen für jede Unterkunft können dort mehr Necrons für Reparatur- und Wartungsarbeiten untergebracht werden."/>
	<entry name="Necrons/HuntersFromHyperspace" value="<string name='Actions/Necrons/HuntersFromHyperspace'/>"/>
	<entry name="Necrons/HuntersFromHyperspaceDescription" value="<string name='Actions/Necrons/HuntersFromHyperspaceDescription'/>"/>
	<entry name="Necrons/HuntersFromHyperspaceFlavor" value="<string name='Actions/Necrons/HuntersFromHyperspaceFlavor'/>"/>
	<entry name="Necrons/ImmuneToNaturalLaw" value="Außerhalb der Naturgesetze"/>
	<entry name="Necrons/ImmuneToNaturalLawDescription" value="Die Einheit kann sich über Wasser sowie durch feindliche Einheiten hindurch bewegen und ignoriert den Malus durch Flüsse und Stahlkraut."/>
	<entry name="Necrons/InfantryBuildingBonus" value="Kernveredelung"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="Dank besserer Werkzeuge und weniger Beschränkungen können die Crypteks und Canopteks mehr begrabene Necrons reaktivieren und kommen dabei zudem schneller voran."/>
	<entry name="Necrons/InfluenceBuildingBonus" value="Furchterregende Inschriften"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Erhöht die Generierung von Einfluss."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="Wehe dem Sterblichen, der diese Inschriften in Augenschein nimmt. Ihre scharfen Konturen scheinen in den Augenwinkeln zu flackern und erfüllen das menschliche Herz mit Angst."/>
	<entry name="Necrons/InvasionBeams" value="Invasions-Teleporter"/>
	<entry name="Necrons/InvasionBeamsDescription" value="Das Aussteigen verbraucht keine Bewegungspunkte."/>
	<entry name="Necrons/JetCharge" value="<string name='Actions/Necrons/JetCharge'/>"/>
	<entry name="Necrons/JetChargeDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Necrons/LivingMetal" value="Lebendes Metall"/>
	<entry name="Necrons/LivingMetalDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="Necrons/LivingMetalFlavor" value="Es ist verständlich, warum die Necrons ihre Fahrzeuge aus dem gleichen lebenden Metall bauen, aus dem auch ihre Körper bestehen, wenn man bedenkt, wie robust es ist und wie gut sich Schäden von der Schlacht reparieren lassen."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="Unheimlicher Pakt"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Erhöht die Generierung von Loyalität."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="Mehr von der verborgenen Geschichte des Schreins wird offenbart, wenngleich sie immer noch vor der Rache des Stillen Königs an den unersättlichen Sternengöttern endet, die seinem Volk die Seelen geraubt haben."/>
	<entry name="Necrons/MeleeDamage" value="Necrodermis-Klingen"/>
	<entry name="Necrons/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Necrons/MeleeDamageFlavor" value="Diese Klingen wurden aus unfassbar dünnen Stücken des nützlichen lebenden Metalls der Necrons gefertigt. Sie können erschreckend mühelos durch die Haut, Knochen und Panzerung der meisten Einheiten schneiden."/>
	<entry name="Necrons/NanoscarabReanimationBeam" value="Nanoskarabäen-Reanimationsstrahl"/>
	<entry name="Necrons/NanoscarabReanimationBeamDescription" value="Stellt die Trefferpunkte wieder her."/>
	<entry name="Necrons/NanoscarabReanimationBeamFlavor" value="<string name='Actions/Necrons/NanoscarabReanimationBeamFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="Nebuloskop"/>
	<entry name="Necrons/NebuloscopeDescription" value="Ignoriert teilweise die feindliche Fernkampf-Schadensreduktion."/>
	<entry name="Necrons/NebuloscopeFlavor" value="Dieses arkane Gerät ermöglicht es Piloten von Tomb Blades, ihre Beute in verschiedenen Dimensionen aufzuspüren, wodurch sie keine Gelegenheit hat, sich zu verstecken."/>
	<entry name="Necrons/OreBuildingBonus" value="Zuverlässige Sklaven"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Erhöht die Erzgewinnung."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="Den versklavten jungen Rassen der Alten Vertrauen zu schenken und sie zu verbessern, mag töricht erscheinen, doch es steigert die Produktivität."/>
	<entry name="Necrons/Chronometron" value="<string name='Actions/Necrons/Chronometron'/>"/>
	<entry name="Necrons/ChronometronDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Necrons/ChronometronFlavor" value="<string name='Actions/Necrons/ChronometronFlavor'/>"/>
	<entry name="Necrons/PhaseShiftGenerator" value="<string name='Actions/Necrons/PhaseShiftGenerator'/>"/>
	<entry name="Necrons/PhaseShiftGeneratorDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="Necrons/PhaseShiftGeneratorFlavor" value="<string name='Actions/Necrons/PhaseShiftGeneratorFlavor'/>"/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/ReanimationProtocols" value="Reanimationsprotokolle"/>
	<entry name="Necrons/ReanimationProtocolsDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="Necrons/ReanimationProtocolsFlavor" value="Die Necrons verfügen über ausgeklügelte Selbstreparatursysteme, dank derer selbst schwer verwundete Krieger aufs Schlachtfeld zurückkehren können."/>
	<entry name="Necrons/Reaper" value="<string name='Actions/Necrons/Reaper'/>"/>
	<entry name="Necrons/ReaperFlavor" value="<string name='Actions/Necrons/ReaperFlavor'/>"/>
	<entry name="Necrons/ResearchBuildingBonus" value="Cryptek-Datensysteme"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Erhöht die Generierung von Forschungspunkten."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="Neue Technologien zu ersinnen, ist die Aufgabe der Crypteks, und dank innovativer Datensysteme können sie auf geteilte Informationen zugreifen, ohne den Risiken ausgesetzt zu sein, die bei einer Gedankenübertragung bestehen."/>
	<entry name="Necrons/ShieldVane" value="Schildplatten"/>
	<entry name="Necrons/ShieldVaneDescription" value="Erhöht die Panzerung."/>
	<entry name="Necrons/ShieldVaneFlavor" value="Tomb Blades, die mitten in die Verteidigungsstellungen einer Welt abgesetzt werden, sind häufig mit zusätzlichen Panzerplatten ausgestattet."/>
	<entry name="Necrons/SleepingSentry" value="<string name='Actions/Necrons/SleepingSentry'/>"/>
	<entry name="Necrons/SleepingSentryDescription" value="Erhöht die Schadensreduktion, aber die Einheit kann sich weder bewegen noch Aktionen durchführen."/>
	<entry name="Necrons/TargetRelayed" value="Weitergegebenes Ziel"/>
	<entry name="Necrons/TargetRelayedDescription" value="Erhöht die Fernkampfgenauigkeit der Necrons gegen diese Einheit."/>
	<entry name="Necrons/TargetRelayedFlavor" value="<string name='Traits/Necrons/TargetingRelayFlavor'/>"/>
	<entry name="Necrons/TargetingRelay" value="Zielrelais"/>
	<entry name="Necrons/TargetingRelayDescription" value="Erhöht vorübergehend die Fernkampfgenauigkeit von Einheiten der Necrons gegen die Zieleinheit."/>
	<entry name="Necrons/TargetingRelayFlavor" value="Triarch Stalkers wirken wenig robust, doch dank ihrer einzigartigen Quantenschilde können sie sich gut verteidigen und verstärken mit ihrem Zielrelais die Feuerkraft von Einheiten der Necrons in ihrer Nähe."/>
	<entry name="Necrons/Technomancer" value="Technomant"/>
	<entry name="Necrons/TechnomancerDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Necrons/VehiclesBuildingBonus" value="Canoptek-Arbeiter"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="Dadurch, dass Canoptek-Arbeiter für Wartungs- und Reparaturarbeiten bereitgestellt werden, können verschiedenste Kriegsmaschinen, die lange inaktiv waren, schneller wieder instand gesetzt werden."/>
	<entry name="Necrons/VengeanceOfTheEnchained" value="Rache des Gefesselten"/>
	<entry name="Necrons/VengeanceOfTheEnchainedDescription" value="Todesexplosionen verursachen zusätzlichen Schaden."/>
	<entry name="Necrons/WraithForm" value="Phantomgestalt"/>
	<entry name="Necrons/WraithFormDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="Necrons/WraithFormFlavor" value="Als Canoptek-Vorrichtung wurden Phantome dafür konzipiert, die schlummernden Necrons in ihrem Jahrtausende währenden Schlaf zu warten. Sie konnten sich selbst dimensional destabilisieren, um ausgefallene Innenkomponenten zu reparieren. Nun da ihre Ladungen aktiviert sind, nutzen sie dieselbe Technologie, um über das Schlachtfeld zu huschen, während feindliche Geschosse wirkungslos durch sie hindurchfliegen."/>
	<entry name="Necrons/Wraithflight" value="Phasenflug"/>
	<entry name="Necrons/WraithflightDescription" value="<string name='Traits/Necrons/ImmuneToNaturalLawDescription'/>"/>
	<entry name="Necrons/WraithflightFlavor" value="Aufgrund ihrer dimensionalen Destabilisierungsmatrix sind Phantome nicht nur unglaublich schwer auszuschalten, sondern auch in der Lage, durch jedes Hindernis einfach hindurchzuhuschen. Auf diese Weise können sie Flüsse, Stahlkraut und sogar feindliche Krieger problemlos überwinden."/>
	<entry name="Necrons/WrithingWorldscape" value="<string name='Actions/Necrons/WrithingWorldscape'/>"/>
	<entry name="NoEscape" value="Kein Entkommen"/>
	<entry name="NoEscapeDescription" value="Erhöht die Anzahl der Angriffe gegen offene Einheiten."/>
	<entry name="OpenTopped" value="Offen"/>
	<entry name="OpenToppedDescription" value="Klassifizierung."/>
	<entry name="OpenToppedFlavor" value="Manche Fahrzeuge verfügen lediglich über eine schwache Panzerung, wodurch sie anfälliger für Schaden sind. Allerdings eignen sich Fahrzeuge wie diese hervorragend als Truppentransporter, da die Kämpfer deutlich leichter aus dem Fahrzeug gelangen können."/>
	<entry name="OrationOfRestoration" value="Wiederherstellungspredigt"/>
	<entry name="OrationOfRestorationDescription" value="Stellt Trefferpunkte wieder her."/>
	<entry name="Ordnance" value="Geschütze"/>
	<entry name="OrdnanceDescription" value="Erhöht den Panzerungsdurchschlag gegen Fahrzeuge und Festungen. Kann nicht von bewegter Infanterie eingesetzt werden."/>
	<entry name="OrkoidFungus" value="Orkoide Pilze"/>
	<entry name="OrkoidFungusDescription" value="Stellt bei Bodeneinheiten der Orks in jeder Runde Trefferpunkte wieder her."/>
	<entry name="OrkoidFungusFlavor" value="Zwischen den Orks und den Pilzen, die sich über ihre Planeten ausgebreitet haben, herrscht eine sonderbare Symbiose. Orks sowie ihre Squigs und Grotz haben denselben genetischen Code wie diese Pilze und entstehen aus ihnen, anstatt wie andere Lebensformen eine Geburt zu durchlaufen. Erleiden Einheiten der Orks in der Schlacht hohe Verluste, sollten sie sich zu diesen Pilzkulturen zurückziehen, um mit (im wahrsten Sinne des Wortes) frischen Rekruten verstärkt zu werden."/>
	<entry name="OrkoidFungusBonusHealingRate" value="Pilzwälder"/>
	<entry name="OrkoidFungusBonusHealingRateDescription" value="Stellt bei Bodeneinheiten der Orks in jeder Runde Trefferpunkte wieder her."/>
	<entry name="OrkoidFungusBonusHealingRateFlavor" value="Wenn sich die Pilzsporen der Orks erst einmal über einen Planeten ausgebreitet haben, entstehen Wälder, die jedoch nicht aus Bäumen bestehen, sondern aus riesigen Pilzen, denen wilde Orks, Grotz und Squigs entwachsen."/>
	<entry name="OrkoidFungusFood" value="Fratzlingvermehrung"/>
	<entry name="OrkoidFungusFoodDescription" value="Erhöht die Nahrungsproduktion."/>
	<entry name="OrkoidFungusFoodFlavor" value="Die winzigen Snotlinge entstehen wie alle orkoiden Lebewesen aus ein und derselben genetischen Pilzstruktur. Im Gegensatz zu anderen orkoiden Lebewesen können sich besonders träge Snotlinge wieder in ihre Pilzkultur zurückziehen und verwandeln sich dann zu riesigen Halbpilzkreaturen, die bei ihren größeren Ork-Artgenossen als Delikatesse gelten."/>
	<entry name="Orks/BeastSnagga" value="Viechfänga"/>
	<entry name="Orks/BeastSnaggaDescription" value="Erhöht die Genauigkeit gegen Fahrzeuge und Monströse Kreaturen. Erhöht zudem die Unverwundbar-Schadensreduktion."/>
	<entry name="Orks/BeastSnaggaFlavor" value="Die Viechfänga halten nach den größten oder gefährlichsten Gegnern auf dem Schlachtfeld (abgesehen von den Orks selbst …) Ausschau und machen dann mit größtem Enthusiasmus Jagd auf sie."/>
	<entry name="Orks/BigBoss" value="<string name='Actions/Orks/BigBoss'/>"/>
	<entry name="Orks/BigBossDescription" value="Erhöht die Trefferpunkte."/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="<string name='Traits/AstraMilitarum/BlastDamageDescription'/>"/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="Größerä Geschossä"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Traits/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="Die Meks haben den Lauf dieser Wumme so modifiziert, dass sie mit großkalibrigerer Munition ähnlich den Kraken-Penetratorgeschossen der Space Marines geladen werden kann. Der glückliche Besitzer dieser Wumme kann mit einer Waffe prahlen, die noch größer ist und mit der er noch härter trifft als bisher."/>
	<entry name="Orks/BonusBeastsProduction" value="Größerä Peitsch'n"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="Erhöht die Produktionsrate von Treiba-Anlagen."/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="„Wia brauch'n 'ne größerä Peitschä.“<br/> – Treiba Gark Snotsmeer, kurz bevor ein Squiggofant auf ihn getreten ist"/>
	<entry name="Orks/BonusColonizersProduction" value="Schrotthämmer"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="Erhöht die Produktionsrate von Mek-Krempelplätzen."/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="Wenn die Mekboyz nicht genug Schrott vorrätig haben, um ihre plumpen Erfindungen zu bauen, beschaffen sie sich kurzerhand mehr, indem sie verrückte und gleichwohl wundersame Maschinen entwickeln, mit denen sie die Ruinen und Wracks ihrer eroberten Welten ausschlachten können."/>
	<entry name="Orks/BonusInfantryProduction" value="Mehr Dakka"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="Erhöht die Produktionsrate von Dakkahaufen."/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="„Des is die bestä Wummä, die ich je gebaut hab. Vielä Rohrä, damit des Ding schön rummst. Außa des da, des rummst nich, weil's brennt – und wie! Un' die Kugl'n sin' explosiv. Macht Bumm da drin, wenn man schießt. Un' der Knopf hia… Des is des bestä Teil. Des macht, also, ähm… oh, Zog. Nee, alles duftä, Boss. Nee, musst nich seh'n, was der Knopf macht. Nich drückän!“<br/> – Die letzten Worte des Meks Nazdakka Boomsnik"/>
	<entry name="Orks/BonusVehiclesProduction" value="Zusammengewürfelt"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="Erhöht die Produktionsrate von Heizakulten"/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="Die meisten Technologien der Orks können eigentlich gar nicht funktionieren, da wichtige Komponenten fehlen oder die Erfindung technisch gesehen so nicht umsetzbar ist. Durch das geradlinige Denken der einfach gestrickten Orks entsteht jedoch Waaagh-Energie, die diese technischen Mängel überbrückt. Aus diesem Grund kann auch die Produktion von Fahrzeugen und Waffen angekurbelt werden, ohne Qualitätseinbußen fürchten zu müssen…"/>
	<entry name="Orks/BustHeads" value="<string name='Actions/Orks/BustHeads'/>"/>
	<entry name="Orks/BustHeadsDescription" value="Verringert den Moralverlust."/>
	<entry name="Orks/BustHeadsFlavor" value="<string name='Actions/Orks/BustHeadsFlavor'/>"/>
	<entry name="Orks/ChannelMentalEmissions" value="<string name='Actions/Orks/ChannelMentalEmissions'/>"/>
	<entry name="Orks/ChannelMentalEmissionsDescription" value="Erhöht die Generierung von Forschungspunkten."/>
	<entry name="Orks/CityEnergy" value="Knistanda Kremp'l"/>
	<entry name="Orks/CityEnergyDescription" value="Erhöht die Energiegewinnung."/>
	<entry name="Orks/CityEnergyFlavor" value="Grüne und rote Blitze zucken zwischen den komplexen Resonatoren dieser Energieverstärkungskonstrukte und rösten jede Grünhaut, die sich zu nah heranwagt. Die Blitze bieten zwar einen spektakulären Anblick, allerdings ist die Technologie nicht besonders ausgereift. Dass Anlagen wie diese die Energieleistung erhöhen, liegt nicht an der Technik, wie die Meks behaupten, sondern schlicht und einfach am Glauben der Orks, der bekanntlich Berge versetzen kann."/>
	<entry name="Orks/CityGrowth" value="Bosslautsprächa"/>
	<entry name="Orks/CityGrowthDescription" value="Erhöht die Produktionsrate von Schrotthütten."/>
	<entry name="Orks/CityGrowthFlavor" value="„Neee, ich will keine zoggigen Hochtöna hab'n, sondern Gorkawoofa… welchä, die alles wegballan!“<br/> – Bleeding Ears Merfnik, Waaaghboss der Goffs und Rocka"/>
	<entry name="Orks/CityInfluence" value="Großä Glitzadinga"/>
	<entry name="Orks/CityInfluenceDescription" value="Erhöht die Generierung von Einfluss."/>
	<entry name="Orks/CityInfluenceFlavor" value="„Ich weiß nich, was das is, aba ich will's hab'n.“<br/> – Waaaghboss Flashgrub Nitwiz, als er auf die goldenen Türen der Basilica Imperialis auf Catiline VII starrt"/>
	<entry name="Orks/CityLoyalty" value="Trophä'nstangän"/>
	<entry name="Orks/CityLoyaltyDescription" value="Erhöht die Generierung von Loyalität."/>
	<entry name="Orks/CityLoyaltyFlavor" value="Nichts gibt einem Ork mehr Auftrieb und Kraft als der Anblick eines besiegten Feindes, den man irgendwo aufgehängt hat, damit ihn alle sehen können – auch wenn möglicherweise nicht mehr viel von ihm übrig ist."/>
	<entry name="Orks/CityPopulationLimit" value="Tiefgräba"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Erhöht das Bevölkerungslimit."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="Dank den neuesten Erfindungen der Meks erstrecken sich die Behausungen und Schmieden der Orks nun bis tief unter die Oberfläche der Stadt. Sicher, hin und wieder kommt es zu Einstürzen, die Orks schreiend in die Tiefen reißen, aber was tut man nicht alles für etwas mehr Platz?"/>
	<entry name="Orks/CityResearch" value="Dänkkamman"/>
	<entry name="Orks/CityResearchDescription" value="Erhöht die Generierung von Forschungspunkten."/>
	<entry name="Orks/CityResearchFlavor" value="Da den Orks das Wissen um bestimmte Technologien in die Gene gepflanzt wurde, sperren clevere Waaaghbosse ihre Meks in einen kleinen Raum voller Schrott und befehlen ihnen, sich irgendetwas einfallen zu lassen, damit aus ihrem Kremp'l richtig gutes Dakka wird."/>
	<entry name="Orks/CityTier2" value="Slumstadt"/>
	<entry name="Orks/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Orks/CityTier2Flavor" value="Außerhalb der Festungsmauern hat sich eine Barackenstadt der Gretchin, Squigs, Snotlinge und niederrangigen Orks gebildet, während sich die heimtückischen grünen Pilze überall verbreitet haben und diese Welt gemäß dem genetischen Fingerabdruck der Orks neu formen."/>
	<entry name="Orks/CityTier3" value="Barackensiedlung"/>
	<entry name="Orks/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Orks/CityTier3Flavor" value="Der Ruhm des Waaaghbosses eilt ihm auf dem gesamten Planeten voraus, und wohin er auch zieht, wird er von unsichtbaren Waaagh-Energien begleitet. Wenn neue Orks das Antlitz dieser Welt erblicken, verspüren sie eine unwiderstehliche Anziehungskraft, die von den Städten des Waaaghbosses ausgeht. Endlose Barackensiedlungen sind in ihrer Gegend bereits aus dem Boden geschossen."/>
	<entry name="Orks/CreateOrkoidFungusOnDeath" value="Schneller Verfall"/>
	<entry name="Orks/CreateOrkoidFungusOnDeathDescription" value="Wenn Einheiten sterben, wachsen orkoide Pilze."/>
	<entry name="Orks/CreateOrkoidFungusOnDeathFlavor" value="„Exponat 27: eine einzelne 'Spore' der Xenos-Spezies #0451 'Ork'. Akolythen, beachtet das fungoide Gewebe und die mikrohomunkularen Strukturen. Wir vermuten, dass diese 'Sporen' neue 'Orks' hervorbringen, wenn die Umgebung fruchtbar genug ist. Sobald ein Ork ausgewachsen ist (für gewöhnlich 30 Minuten nach seiner Entstehung), sondert er permanent diese 'Sporen' ab. Stirbt ein Ork, werden sie im Augenblick seines Todes massenhaft freigesetzt. Bitte nicht fallen lassen.“<br/> – Lehrvortragsabschrift, Grigomen „Ork-Liebhaber“ Delr, Freihändler und Hobbyxenologist"/>
	<entry name="Orks/CyborkImplants" value="<string name='Actions/Orks/CyborkImplants'/>"/>
	<entry name="Orks/CyborkImplantsDescription" value="Erhöht den Schaden und die Schadensreduktion."/>
	<entry name="Orks/ExtraBitz" value="<string name='Actions/Orks/ExtraBitz'/>"/>
	<entry name="Orks/ExtraBitzDescription" value="Verringert die laufenden Nahrungs-, Erz- und Energiekosten."/>
	<entry name="Orks/ExperimentalProcedure" value="<string name='Actions/Orks/ExperimentalProcedure'/>"/>
	<entry name="Orks/ExperimentalProcedureDescription" value="Erhöht den Schaden und die Schadensreduktion."/>
	<entry name="Orks/Flyboss" value="Fliegaboss"/>
	<entry name="Orks/FlybossDescription" value="Erhöht die Fernkampfgenauigkeit gegen Flieger, Jetbikes und Antigraveinheiten."/>
	<entry name="Orks/FlybossFlavor" value="Diese Fliegerasse haben schon mehr Kurvenkämpfe überlebt, als sie zählen können (selbst wenn sie ihre Zehen zu Hilfe nehmen)."/>
	<entry name="Orks/Gitfinda" value="Gitsucha"/>
	<entry name="Orks/GitfindaDescription" value="Erhöht die Fernkampfgenauigkeit, wenn die Einheit stationär bleibt."/>
	<entry name="Orks/GitfindaFlavor" value="Hierbei kann es sich um komplizierte Okularimplantate handeln, aber auch um monokulare Aufsätze, übergroße Teleskope oder weiß Mork was alles. Der Zweck des Gitsuchas besteht darin, die Treffsicherheit seines Trägers auf ein beinahe durchschnittliches Niveau zu heben."/>
	<entry name="Orks/GreenTide" value="Grüne Flut"/>
	<entry name="Orks/GreenTideFlavor" value="Es ist verdammt schwer, Orks zu töten. Und wenn man denkt, dass man sie getötet hat, tauchen sie oftmals in der nächsten Schlacht wieder auf, weil ein Mad Dok sie zusammengeflickt hat oder weil sie auf wundersame Weise geheilt wurden. Zu allem Überfluss sind sie dann aufgrund ihrer Kampferfahrung auch noch zäher und stärker."/>
	<entry name="Orks/GreenTideGrowth" value="<string name='Traits/Orks/GreenTide'/>"/>
	<entry name="Orks/GreenTideGrowthDescription" value="Erhöht die Wachstumsrate."/>
	<entry name="Orks/GreenTideGrowthFlavor" value="<string name='Traits/Orks/GreenTideFlavor'/>"/>
	<entry name="Orks/GreenTideHealing" value="<string name='Traits/Orks/GreenTide'/>"/>
	<entry name="Orks/GreenTideHealingDescription" value="Erhöht die Heilungsrate."/>
	<entry name="Orks/GreenTideHealingFlavor" value="<string name='Traits/Orks/GreenTideFlavor'/>"/>
	<entry name="Orks/GrotGunner" value="Grotschütze"/>
	<entry name="Orks/GrotGunnerDescription" value="Erhöht die Genauigkeit von Fetten Wummen und Synchronisierten Fetten Wummen."/>
	<entry name="Orks/KustomForceField" value="<string name='Actions/Orks/KustomForceField'/>"/>
	<entry name="Orks/KustomForceFieldDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="Orks/KustomForceFieldFlavor" value="<string name='Actions/Orks/KustomForceFieldFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="Fettere Spaltaz"/>
	<entry name="Orks/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Orks/MeleeDamageFlavor" value="Wenn Orks Verbesserungen planen, machen sie sich nicht sonderlich viele Gedanken. Megarüstung, Gigawummen… und größere Spaltaz. Wenn man so ein Ding tragen kann, kann man es auch schwingen – und wenn man es schwingen kann, kann man damit auch jemanden treffen."/>
	<entry name="Orks/MightMakesRight" value="Recht des Stärkeren"/>
	<entry name="Orks/MightMakesRightDescription" value="Generiert beim Angreifen Einfluss."/>
	<entry name="Orks/MightMakesRightFlavor" value="Die Orks fühlen sich eigentlich nur lebendig, wenn sie gerade auf einem Waaagh sind, und ein Waaagh kann nur aufrechterhalten werden, wenn sie von Schlacht zu Schlacht ziehen. Solange Orks kämpfen, wird der Waaagh weiterhin befeuert."/>
	<entry name="Orks/ProphetOfTheWaaagh" value="<string name='Actions/Orks/ProphetOfTheWaaagh'/>"/>
	<entry name="Orks/ProphetOfTheWaaaghDescription" value="Generiert beim Angreifen Einfluss abhängig von den Unterhaltskosten."/>
	<entry name="Orks/Scavenger" value="Plünderer"/>
	<entry name="Orks/ScavengerDescription" value="Beim Töten einer gegnerischen Einheit wird Erz geplündert."/>
	<entry name="Orks/ScavengerFlavor" value="Orks sind nicht gerade für ihren Arbeitseifer bekannt. Stattdessen sitzen sie zwischen Schlachten lieber herum, wenn sie nicht gerade die Reste eines Squigs aus ihrem Mund ziehen oder sich gegenseitig bekämpfen. Anstatt Erz abzubauen, plündern sie es lieber, wo immer sie können – oder überlassen diese Arbeit ihren untergebenen Grotz."/>
	<entry name="Orks/WarbikeTurboBoost" value="<string name='Actions/Orks/WarbikeTurboBoost'/>"/>
	<entry name="Orks/WarbikeTurboBoostDescription" value="<string name='Actions/Orks/WarbikeTurboBoostDescription'/>"/>
	<entry name="Orks/WarbikeTurboBoostFlavor" value="<string name='Actions/Orks/WarbikeTurboBoostFlavor'/>"/>
	<entry name="Orks/Warpath" value="<string name='Actions/Orks/Warpath'/>"/>
	<entry name="Orks/WarpathDescription" value="<string name='Actions/Orks/WarpathDescription'/>"/>
	<entry name="Orks/WarpathFlavor" value="<string name='Actions/Orks/WarpathFlavor'/>"/>
	<entry name="Orks/WingMissiles" value="<string name='Weapons/WingMissiles'/>"/>
	<entry name="Orks/WingMissilesDescription" value="Erhöht die Genauigkeit gegen Fahrzeuge."/>
	<entry name="Outpost" value="Außenposten"/>
	<entry name="OutpostDescription" value="Erhöht die Schadensreduktion und die Heilungsrate bei nicht-feindlichen Einheiten. Erhöht die Fernkampf-Schadensreduktion bei Infanterieeinheiten."/>
	<entry name="Outflank" value="Flankenbewegung"/>
	<entry name="OutflankDescription" value="Erhöht die Genauigkeit, wenn diese Einheit an eine nicht-feindliche Einheit angrenzt."/>
	<entry name="OutflankFlavor" value="Am besten kann man den Feind dadurch überraschen, dass man ihn von einer unerwarteten Position aus angreift."/>
	<entry name="Pinned" value="Niedergehalten"/>
	<entry name="PinnedDescription" value="Verringert die Bewegung und die Fernkampfgenauigkeit, erhöht die Fernkampf-Schadensreduktion und hindert die Einheit an Abwehrfeuer-Attacken."/>
	<entry name="PinnedFlavor" value="Selbst die tapfersten Krieger verlieren ihre wilde Entschlossenheit, wenn sie unter Beschuss geraten, ohne zu wissen, aus welcher Richtung dieser erfolgt, oder wenn sie einen Artillerieangriff über sich ergehen lassen müssen. Sie ziehen die Köpfe ein und suchen die nächstbeste Deckung."/>
	<entry name="Pinning" value="Niederhalten"/>
	<entry name="PinningDescription" value="Verringert vorübergehend die Bewegung der ausgewählten angsterfüllten Infanterieeinheit und hindert sie an Abwehrfeuer-Attacken."/>
	<entry name="PinningFlavor" value="<string name='Traits/PinnedFlavor'/>"/>
	<entry name="Poisoned" value="Gift"/>
	<entry name="PoisonedDescription" value="Erhöht den Schaden gegen Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="PoisonedFlavor" value="In der dunklen Zukunft gibt es eine Vielzahl von virulenten, tödlichen Giften und es erklärt sich von selbst, dass derartige Substanzen auch auf dem Schlachtfeld zum Einsatz kommen. Ganz gleich, ob das Gift auf Klingen und Geschosse aufgetragen oder von Xenos-Kreaturen abgesondert wird – tödlich ist es in jedem Fall."/>
	<entry name="PowerOfTheMachineSpirit" value="<string name='Actions/PowerOfTheMachineSpirit'/>"/>
	<entry name="PowerOfTheMachineSpiritDescription" value="<string name='Actions/PowerOfTheMachineSpiritDescription'/>"/>
	<entry name="PowerOfTheMachineSpiritFlavor" value="<string name='Actions/PowerOfTheMachineSpiritFlavor'/>"/>
	<entry name="PrecisionShots" value="Präzise Schüsse"/>
	<entry name="PrecisionShotsDescription" value="Erhöht die Genauigkeit."/>
	<entry name="PreferredEnemy" value="Erzfeind"/>
	<entry name="PreferredEnemyDescription" value="Erhöht die Genauigkeit."/>
	<entry name="PreferredEnemyFlavor" value="Viele Krieger der Galaxie trainieren hart, um gegen einen bestimmten Feind bestehen zu können, wodurch sie über den Kampfstil des Feindes bereits im Vorfeld Bescheid wissen und somit leichter einen Treffer landen können."/>
	<entry name="PrimaryWeapon" value="Makrowaffe"/>
	<entry name="PrimaryWeaponDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="PsychicBlock" value="Psiblock"/>
	<entry name="PsychicBlockDescription" value="Wehrt den Schaden und die Effekte der als Nächstes vom Feind eingesetzten Psikraft ab."/>
	<entry name="PsychicHood" value="Psimatrix"/>
	<entry name="PsychicHoodDescription" value="Erhöht die Hexenfeuer-Schadensreduktion."/>
	<entry name="PsychicLash" value="Psipeitsche"/>
	<entry name="PsychicLashDescription" value="Angriffe durchdringen jede Panzerung."/>
	<entry name="PsychicPower" value="Psikräfte"/>
	<entry name="PsychicPowerDescription" value="Klassifizierung."/>
	<entry name="PsychneueinInfest" value="Psychnen-Befall"/>
	<entry name="PsychneueinInfestDescription" value="Wenn die Zieleinheit getötet wurde, wird eine Psychne hervorgebracht."/>
	<entry name="PsychneueinInfestation" value="Befall durch Psychnen"/>
	<entry name="PsychneueinInfestationDescription" value="Wenn diese Einheit stirbt, wird eine Psychne hervorgebracht."/>
	<entry name="Psyker" value="Psioniker"/>
	<entry name="PsykerDescription" value="Klassifizierung."/>
	<entry name="PsykerFlavor" value="Psioniker sind Mystiker, die sich auf dem Schlachtfeld die Kräfte des Warp zunutze machen."/>
	<entry name="Rage" value="Berserker"/>
	<entry name="RageDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="RageFlavor" value="Die Blutlust ist auf dem Schlachtfeld eine mächtige Waffe, da sie den Krieger dazu anspornt, seine Feinde in einem temporären Zustand geistiger Entrücktheit (der jedoch äußerst befriedigend ist) in Stücke zu hacken."/>
	<entry name="Rampage" value="Amok"/>
	<entry name="RampageDescription" value="Erhöht die Anzahl der Angriffe, wenn sich mehr feindliche als nicht-feindliche Einheiten auf angrenzenden Hexfeldern befinden."/>
	<entry name="RampageFlavor" value="Manche Krieger verzagen nicht, wenn sie zahlenmäßig unterlegen sind, sondern fühlen sich dazu ermuntert, wie Berserker zum Gegenangriff überzugehen."/>
	<entry name="RapidFire" value="Schnellfeuer"/>
	<entry name="RapidFireDescription" value="Verdoppelt die Anzahl der Angriffe bei halber Reichweite."/>
	<entry name="RecoveryGear" value="Bergeausrüstung"/>
	<entry name="RecoveryGearDescription" value="Erhöht die Heilungsrate."/>
	<entry name="RecoveryGearFlavor" value="Viele Fahrzeugbesatzungen haben Werkzeug, Abschleppseile und andere nützliche Ausrüstung dabei, was entscheidend sein kann, wenn es darum geht, ein bewegungsunfähiges Fahrzeug aus einer Gefahrenzone zu schaffen, anstatt es seinem Schicksal zu überlassen."/>
	<entry name="RedPaintJob" value="Rotä Farbä"/>
	<entry name="RedPaintJobDescription" value="Erhöht den Schaden."/>
	<entry name="RedPaintJobFlavor" value="Orks glauben, dass ein mit roter Farbe bemaltes Fahrzeug besser abschneidet als ein Fahrzeug des gleichen Typs ohne rote Farbe. Es mag seltsam klingen, aber sie haben recht."/>
	<entry name="RefractorField" value="Refraktorfeld"/>
	<entry name="RefractorFieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="RefractorFieldFlavor" value="Hochrangige Offiziere und Helden des Imperiums tragen häufig schimmernde Refraktorfelder, die auftreffende Energie um den Träger herum brechen. Dadurch wehren sie Geschosse und Klingen ab, die ansonsten tödlich wären."/>
	<entry name="Regeneration" value="Regeneration"/>
	<entry name="RegenerationDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="RegenerationFlavor" value="Manche Einheiten haben die Fähigkeit, sich von schrecklichen Verletzungen zu erholen, die eigentlich tödlich enden müssten."/>
	<entry name="Relentless" value="Unaufhaltsam"/>
	<entry name="RelentlessDescription" value="Hebt den Malus für schwere Waffen, Geschütze und Salvenwaffen auf."/>
	<entry name="RelentlessFlavor" value="Unaufhaltsame Krieger rücken unbeirrt vor und können durch nichts und niemanden aufgehalten werden."/>
	<entry name="RelicPlating" value="Schützende Reliquien"/>
	<entry name="RelicPlatingDescription" value="Erhöht die Hexenfeuer-Schadensreduktion."/>
	<entry name="RelicPlatingFlavor" value="Gelegentlich kommt es vor, dass die Besatzung eines Kampfpanzers eine empathische Bindung mit seinem Maschinengeist eingeht. Fällt eine solche Besatzung in der Schlacht, werden ihre Überreste manchmal in ihrem Fahrzeug bestattet. Geister verweilen dann schützend dort, um die unheilvollen Energien der Leere zu vertreiben."/>
	<entry name="Rending" value="Rüstungsbrechend"/>
	<entry name="RendingDescription" value="Erhöht den Schaden und den Panzerungsdurchschlag."/>
	<entry name="RendingFlavor" value="Manche Waffen können kritischen Schaden verursachen, vor dem keine Panzerung schützt."/>
	<entry name="RepulsorGrid" value="Repulsionsgitter"/>
	<entry name="RepulsorGridDescription" value="Erhöht die Fernkampf-Schadensreduktion und wirft den Fernkampfschaden von Waffen, bei denen es sich nicht um Explosiv-, Flammenschablonen- oder Hexenfeuerwaffen handelt, auf den Angreifer zurück."/>
	<entry name="RitesOfWar" value="Riten des Krieges"/>
	<entry name="RitesOfWarDescription" value="Erhöht den Schaden."/>
	<entry name="RitesOfWarFlavor" value="<string name='Actions/RitesOfWarFlavor'/>"/>
	<entry name="Rosarius" value="Rosarius"/>
	<entry name="RosariusDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="RosariusFlavor" value="Chaplains der Space Marines tragen einen Rosarius, um sich zu schützen und um ihre Stellung hervorzuheben. Er erzeugt um den Träger ein schützendes Energiefeld, das Schläge und Schüsse abwehrt, die selbst einen Bunker aus Ferrobeton zertrümmern könnten. Es heißt, je inbrünstiger der Glaube des Trägers an die Macht des Imperators ist, desto stärker ist das Kraftfeld des Rosarius."/>
	<entry name="RuinsStealth" value="Ruinendeckung"/>
	<entry name="RuinsStealthDescription" value="Erhöht die Fernkampf-Schadensreduktion, solange sich die Einheit in imperialen Ruinen aufhält."/>
	<entry name="Salvo" value="Salve"/>
	<entry name="SalvoDescription" value="Halbiert die Anzahl der Angriffe und die Reichweite, wenn die Einheit bewegt wurde."/>
	<entry name="SavantInterlocution" value="Sophos-Kommunikation"/>
	<entry name="SavantInterlocutionDescription" value="Erhöht die Fernkampfgenauigkeit gegen Flieger und Antigraveinheiten, wenn die Einheit an einen nicht-feindlichen Hunter angrenzt."/>
	<entry name="SavantLock" value="Sophos-Aufschaltung"/>
	<entry name="SavantLockDescription" value="Erhöht die Genauigkeit gegen Flieger, Jetbikes und Antigraveinheiten."/>
	<entry name="SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="SeekerMissileDescription" value="Verringerte Reichweite gegen Ziele, die nicht mit einem Zielmarkierer anvisiert wurden. Erfordert keine Sichtlinie gegen Ziele, die mit einem Zielmarkierer anvisiert wurden."/>
	<entry name="SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Seeking" value="Zielsuchend"/>
	<entry name="SeekingDescription" value="Erhöht die Fernkampfgenauigkeit gegen Flieger."/>
	<entry name="Shaken" value="Aufgewühlt"/>
	<entry name="ShakenDescription" value="Verringert die Genauigkeit und erhöht den erlittenen Schaden."/>
	<entry name="Shielded" value="Abgeschirmt"/>
	<entry name="ShieldedDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="ShieldedFlavor" value="Manche Krieger werden nicht nur durch physische Panzerung geschützt. Sie können durch Kraftfelder oder eine Hülle mystischer Energien abgeschirmt sein oder über eine solch hohe Konstitution verfügen, dass ihnen Treffer nichts anhaben können, die selbst die Panzerung eines Kampfpanzers durchdringen würden."/>
	<entry name="Shop" value="Shop"/>
	<entry name="ShopDescription" value="Ermöglicht es Heldeneinheiten, Gegenstände zu kaufen und zu verkaufen."/>
	<entry name="Shred" value="Reißend"/>
	<entry name="ShredDescription" value="Erhöht den Schaden."/>
	<entry name="ShredFlavor" value="Manche Krieger nutzen ihre Waffen, um das Fleisch ihrer Gegner mit einer Abfolge schneller, brutaler Angriffe in Stücke zu reißen."/>
	<entry name="Shrouded" value="Schleier"/>
	<entry name="ShroudedDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="ShroudedFlavor" value="Ganz gleich, welche dunklen Kräfte um diese Krieger wirken – es spielt keine Rolle. Nur mit einem Glückstreffer kann der Schleier durchdrungen werden, der die Krieger vor den Blicken ihrer Feinde schützt."/>
	<entry name="SiegeMasters" value="Belagerungsmeister"/>
	<entry name="SiegeMastersDescription" value="Erhöht den Schaden gegen feindliche Einheiten in Städten und Befestigungsanlagen."/>
	<entry name="SiegeShield" value="Belagerungsschild"/>
	<entry name="SiegeShieldDescription" value="Erhöht die Panzerung und verringert den Bewegungsmalus in Wäldern und imperialen Ruinen."/>
	<entry name="SiegeShieldFlavor" value="Viele Vindicators sind mit einer gewaltigen Bulldozerschaufel ausgerüstet, mit der sie gefahrlos Schlachtfeldtrümmer aus dem Weg räumen können."/>
	<entry name="Signum" value="Signum"/>
	<entry name="SignumDescription" value="Erhöht die Fernkampfgenauigkeit."/>
	<entry name="SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SistersOfBattle/ActOfFaith" value="Glaubensakt"/>
	<entry name="SistersOfBattle/ActOfFaithDescription" value="Die Einheit kann Glaubensakte vollziehen, wenn sie nicht aufgewühlt oder gebrochen ist."/>
	<entry name="SistersOfBattle/ActOfFaithFlavor" value="Die Kriegerinnen der Adepta Sororitas können aus der Quelle ihres Glaubens schöpfen und den Imperator um Führung bitten. Ihr unerschütterliches Bekenntnis zum Imperialen Glauben ermöglicht es den Sororitas, auf dem Schlachtfeld scheinbar Unmögliches zu vollbringen. Sie sollten jedoch davor gefeit sein, Wunder als Selbstverständlichkeit anzusehen. Der wichtigste Aspekt des Imperialen Glaubens ist der Glaube daran, dass der göttliche Imperator die Menschen zur Selbsterlösung befähigt hat, wenngleich er in aussichtslosen Situationen durchaus einzugreifen vermag, um seine Gottgetreuen zu retten."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="Eremitin-Sarkophag"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Erhöht die Panzerung."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="Diejenigen Repentia, die nicht nur geflohen sind, sondern ihre Schwestern im Kampf auch verraten haben, erwartet ein schlimmes Schicksal. Nachdem sie mit dem Kern ihres Marterers verbunden wurden, werden sie hinter einer dicken Adamant-Ummantelung isoliert. Dieser Sarkophag schützt ihre geschundenen Leiber vor Beschuss und Klingenhieben, wodurch es ihnen verwehrt bleibt, schnell durch den Tod erlöst zu werden."/>
	<entry name="SistersOfBattle/AngelicVisage" value="Engelsgleich"/>
	<entry name="SistersOfBattle/AngelicVisageDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/AngelicVisageFlavor" value="Mit einem seligen Lächeln und geschmeidiger Anmut lenken die Zephyrim die Treffer ihrer Gegner ab, bevor sie selbst einen tödlichen Treffer landen – für gewöhnlich mit einem gezielten Kopfschuss aus einer Boltpistole oder mit einem Hieb ihres Energieschwerts."/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemed" value="Pein der Unerlösten"/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemedDescription" value="Fügt dem Nahkampfangreifer beim Tod Schaden zu."/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemedFlavor" value="Die Marterer-Pilotinnen brennen mit ihren Flammenwerfern eine Schneise der Verwüstung in die feindlichen Reihen, bevor sie sich auf den Gegner stürzen, getrieben von Schuldgefühlen und Schmerz und ungeachtet jeglicher Gefahr. Selbst wenn sie sterben, schlagen sie auf den Gegner ein, während sie den Augenblick ihrer Erlösung herbeisehnen."/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherine" value="Rüstung der Heiligen Katherine"/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherineDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherineFlavor" value="Seit diese ehrerbietige Rüstung mit dem Märtyrerblut der Heiligen Katherine geweiht wurde, glaubt man, dass ihr heilige Schutzkräfte innewohnen."/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="Wilder Feuersturm"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="„Mit dem richtigen Angriff, wohlplatziert, können selbst die überlegensten Gegner bezwungen werden. Aber was, wenn man nicht weiß, welcher Angriff der richtige ist? Dann wird ein überwältigender, wenn auch unausgegorener Angriff am Ende die richtige Stelle treffen…“<br/> – Principalis Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/AvengingZeal" value="Eifer der Vergeltung"/>
	<entry name="SistersOfBattle/AvengingZealDescription" value="Verringert den Moralverlust."/>
	<entry name="SistersOfBattle/AvengingZealFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/BerserkKillingMachine" value="Berserker-Tötungsmaschine"/>
	<entry name="SistersOfBattle/BerserkKillingMachineDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/BerserkKillingMachineFlavor" value="Die unglückselige Pilotin eines Marterers wird von Neuro-Agonisatoren gepeinigt, die ihren Selbsthass verstärken, und durch eine Abschirmung um ihren Kopf von den Gebeten ihrer Schwestern isoliert. Die dadurch hervorgerufenen geistigen Qualen treiben den Marterer an. Als furchterregende Stoßtruppen stürmen diese Maschinen nach vorne und krachen dann in ihre Gegner."/>
	<entry name="SistersOfBattle/BloodyResolution" value="Blutige Durchführung"/>
	<entry name="SistersOfBattle/BloodyResolutionDescription" value="Verringert den Moralverlust."/>
	<entry name="SistersOfBattle/BloodyResolutionFlavor" value="<string name='Traits/SistersOfBattle/MartyrdomFlavor'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnance" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnance'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnanceDescription" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnanceDescription'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnanceFlavor" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnanceFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="Novizinnenrekrutierung"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Erhöht die Wachstumsrate."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="Die Invasion der Orks und der anschließende Krieg auf Gladius Primus hinterließen auf dem Planeten viele, viele Waisen. Diese können durch die Entsendung von Missionarinnen und Predigerinnen für die Armeen des Imperiums rekrutiert werden, was sich positiv auf das Wachstum der Stadt auswirkt."/>
	<entry name="SistersOfBattle/CityTier2" value="Praeceptorium-Expansion"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="Angesichts der vielen Flüchtlinge, die aus den zerstörten Makropolen und aus den Wüsten von Gladius Primus in das Praeceptorium strömen, hat die Principalis Superioris angeordnet, dass wir neues Land durch Weihung nutzbar machen und alle Einheimischen – ob sie wollen oder nicht – in unsere Herde aufnehmen."/>
	<entry name="SistersOfBattle/CityTier3" value="Glaubensfestung"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="Das Praeceptorium hat sich zu einer Stadt entwickelt – vergleichbar mit den Makropolen, die einst die Oberfläche des Planeten säumten. Eine Unterstadt bildet das Fundament, und weiter oben zeugen Türme und Sakralbauten vom Glanz der Stadt. Nur die Ordensfestungen des Adeptus Astartes reichen an das Praeceptorium heran, was Größe und religiösen Prunk betrifft."/>
	<entry name="SistersOfBattle/DivineDeliverance" value="<string name='Actions/SistersOfBattle/DivineDeliverance'/>"/>
	<entry name="SistersOfBattle/DivineDeliveranceDescription" value="<string name='Actions/SistersOfBattle/DivineDeliveranceDescription'/>"/>
	<entry name="SistersOfBattle/DivineDeliveranceFlavor" value="<string name='Actions/SistersOfBattle/DivineDeliveranceFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="Ewiger Kreuzzug"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="Erhöht die Produktionsrate."/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="„Und wir werden nicht ruhen, denn der Blick des Gottimperators ist auf uns gerichtet. Er sieht jede gerettete Seele, aber weiß auch, dass die Zahl der gefährdeten Seelen weitaus höher ist. Wir dürfen nicht aufhören, wir dürfen nicht nachlassen – unser Kreuzzug wird nie enden.“<br/> – Principalis Vandire, In Memoriam De Virtute"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Meisterhafte Kampfpiloten"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Erhöht die Genauigkeit."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="In den meisten Angriffsstaffeln werden einige Flugzeuge von Veteranen geflogen. Diese Fliegerasse sind wahrlich gefährliche Gegner, da sie die Reaktion ihrer Beute mit unheimlicher Präzision vorhersagen können. Dennoch überleben nur wenige von ihnen mehr als einen oder zwei Einsätze, da sie die gefährlichsten Missionen fliegen. Nur für die erfahrensten Piloten besteht Hoffnung, dass sie unversehrt zurückkehren."/>
	<entry name="SistersOfBattle/FlankSpeed" value="Höchstgeschwindigkeit"/>
	<entry name="SistersOfBattle/FlankSpeedDescription" value="Erhöht die Bewegung, aber macht den Einsatz von Fernkampfwaffen unmöglich."/>
	<entry name="SistersOfBattle/FlankSpeedFlavor" value="„Niemand käme auf die Idee, einem Questor Imperialis seine Ehrwürdigkeit abzusprechen. Doch was den Glauben angeht, so kämpft niemand so inbrünstig wie die Cerastus-Lanzenritter. Lediglich mit einer Lanze bewaffnet preschen sie mit ungeheurer Geschwindigkeit nach vorne und beten, dass sie es bis zu den feindlichen Linien schaffen. Sie sind wahrlich eine Bereicherung für unsere Streitkräfte.“<br/> – Principalis Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="Verbesserte Geschosse"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="„Wir streuen die Erde des heiligen Planeten Terra über die Geschosse des Castigators, erheben unsere Stimmen zum Gebet und verneigen uns vor dem Schrein der Heiligen Katherine. Dann wird die Sprengkraft jedes Geschosses um 25 Prozent erhöht und eine Adamantium-Ummantelung hinzugefügt.“<br/> – Principalis Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/IonGauntletShield" value="Ionenhandschuh-Schild"/>
	<entry name="SistersOfBattle/IonGauntletShieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/IonGauntletShieldFlavor" value="Der Ionenhandschuh-Schildgenerator am rechten Handschuh des Cerastus-Lanzenritters wirkt konzentrierter als der gerichtete Ionenschild des Paladinritters, kann jedoch nicht so taktisch flexibel eingesetzt werden."/>
	<entry name="SistersOfBattle/KeepersOfTheFaith" value="Hüterinnen des Glaubens"/>
	<entry name="SistersOfBattle/KeepersOfTheFaithDescription" value="Verhindert Abwehrfeuer-Attacken, erhöht jedoch die Schadensreduktion."/>
	<entry name="SistersOfBattle/KeepersOfTheFaithFlavor" value="„Der Verstand sagt dir, dass du feuern sollst, wenn du den Gegner siehst. Der Verstand sagt dir, dass du in Deckung gehen sollst, wenn der Gegner feuert. Der Verstand sagt dir, dass du sterben musst, wenn du tödlich getroffen wirst. Der Verstand versteht jedoch nicht unseren Glauben – und auch nicht, wie wir ihn aufrechterhalten.“<br/> – Principalis Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/KnightParry" value="Ritterschild"/>
	<entry name="SistersOfBattle/KnightParryDescription" value="Verringert die Nahkampfgenauigkeit."/>
	<entry name="SistersOfBattle/KnightParryFlavor" value="Der Ionenhandschuh-Schildgenerator am rechten Handschuh des Cerastus-Lanzenritters kann selbst die stärksten Treffer im Nahkampf abwehren."/>
	<entry name="SistersOfBattle/LaudHailer" value="Stimmverstärker"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="Diese Einheit kann selbst dann Glaubensakte vollziehen, wenn sie aufgewühlt ist."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Actions/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/LeadTheRighteous" value="<string name='Actions/SistersOfBattle/LeadTheRighteous'/>"/>
	<entry name="SistersOfBattle/LeadTheRighteousDescription" value="Erhöht die Genauigkeit."/>
	<entry name="SistersOfBattle/LeadTheRighteousFlavor" value="<string name='Actions/SistersOfBattle/LeadTheRighteousFlavor'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperor" value="<string name='Actions/SistersOfBattle/LightOfTheEmperor'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperorDescription" value="<string name='Actions/SistersOfBattle/LightOfTheEmperorDescription'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperorFlavor" value="<string name='Actions/SistersOfBattle/LightOfTheEmperorFlavor'/>"/>
	<entry name="SistersOfBattle/Martyrdom" value="Märtyrertum"/>
	<entry name="SistersOfBattle/MartyrdomDescription" value="Stirbt diese Einheit, erleiden alle nicht-feindlichen Einheiten der Adepta Sororitas einen geringeren Moralverlust."/>
	<entry name="SistersOfBattle/MartyrdomFlavor" value="Die Schwestern der Adepta Sororitas verzagen nicht, wenn ihre Anführerinnen in der Schlacht fallen. Stattdessen stärkt das Blut dieser heldenhaften Märtyrerinnen ihre Entschlossenheit, und ihr Opfer inspiriert sie so zu großen Heldentaten."/>
	<entry name="SistersOfBattle/MartyrSpirit" value="Märtyrergeist"/>
	<entry name="SistersOfBattle/MartyrSpiritDescription" value="Stirbt diese Einheit, ist der Moralverlust angrenzender nicht-feindlicher Einheiten mit „Schild des Glaubens“ verringert."/>
	<entry name="SistersOfBattle/MartyrSpiritFlavor" value="„Der Verlust unserer Schwestern und Hilfstruppen sorgte für Verdruss, trieb uns jedoch auch dazu an, unsere Anstrengungen zu verdoppeln.“<br/> – Unbekannter Memorator, Evangelium des Netzes der Tausend Tore"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Actions/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Actions/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="Gesegnete Klingen"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="Die Waffen der Adepta Sororitas unterscheiden sich kaum von denen des Astra Militarum, werden jedoch mehr gepflegt und sind dank Segnungen und Bittgebeten Teil des religiösen Alltags. Nur Letzteres kann eine Erklärung für die Wunder sein, die sich im Kampf ereignen: Immer wieder finden die gesegneten Klingen selbst die kleinste Schwachstelle des Gegners."/>
	<entry name="SistersOfBattle/MiraculousIntervention" value="<string name='Actions/SistersOfBattle/MiraculousIntervention'/>"/>
	<entry name="SistersOfBattle/MiraculousInterventionDescription" value="<string name='Actions/SistersOfBattle/MiraculousInterventionDescription'/>"/>
	<entry name="SistersOfBattle/MiraculousInterventionFlavor" value="<string name='Actions/SistersOfBattle/MiraculousInterventionFlavor'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="Ministorum-Mobilisierung"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="Erhöht die Produktion von Bedarfsgütern."/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="Die Riten der Ekklesiarchie haben nicht nur einen militärischen Nutzen: Es existieren Gebete und Liturgien für alle Belange des Imperiums. Ganz gleich, ob man einem Ordo Famulus, einem Ordo Pronatus oder der Arbeiterklasse angehört: Wer im Mittelpunkt eines solchen Ritus steht, fühlt sich beschwingt und vermag mehr zu leisten."/>
	<entry name="SistersOfBattle/OathOfFaith" value="Eid des Glaubens"/>
	<entry name="SistersOfBattle/OathOfFaithDescription" value="Verringert die Genauigkeit und verhindert den Einsatz von „Schild des Glaubens“."/>
	<entry name="SistersOfBattle/OathOfFaithFlavor" value="„Wir alle haben den Eid abgelegt, das Imperium zu verteidigen und die Häretiker und die Xenos zu vernichten. Dieser Eid beflügelt uns dabei, Schmerz und Angst zu überwinden. Doch wenn die Entschlossenheit einer Schwester schwindet und sie die Flucht ergreift, wird dieser Eid zu einer Bürde, denn sie weiß: Sollte sie ihre Flucht überleben, wird sie als Repentia zurückkehren…“<br/> – Unbekannter Memorator, Evangelium des Netzes der Tausend Tore"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="Paragon-Kriegsanzug"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="Die uralten Paragon-Kriegsanzüge gehören zum heiligen Arsenal eines jeden Ordo Militaris und gelten als heilige Rüstungen mit einem eigenen, hehren Willen. Nur die würdigsten Celestia Sacresantis sind imstande, den diesen Kriegsanzügen innewohnenden Geist zu beherrschen."/>
	<entry name="SistersOfBattle/Protected" value="Geschützt"/>
	<entry name="SistersOfBattle/ProtectedDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/ProtectedFlavor" value="<string name='Actions/SistersOfBattle/BodyguardFlavor'/>"/>
	<entry name="SistersOfBattle/PsyShock" value="PsiSchock"/>
	<entry name="SistersOfBattle/PsyShockDescription" value="Betäubt Psioniker."/>
	<entry name="SistersOfBattle/PsyShockFlavor" value="<string name='Weapons/CondemnorBoltgunSilverStake'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="Läuternde Predigten"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="Erhöht den Schaden."/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="„Unsere Feinde beschreiben uns als kaltherzig, unmenschlich und gefühllos. Aber wir fühlen, wir trauern, wir wüten. Lasst sie unsere Wärme spüren – mithilfe unserer Flammenwerfer und Melter.“<br/> – Principalis Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/RagingFervour" value="Tobende Inbrunst"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="Erhöht den Schaden."/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="„Die Waffen in unseren Händen sind die Werkzeuge unseres Glaubens. Jedes Boltgeschoss ist ein Gebet, das mitten ins Herz eines Ungläubigen gesandt wird, auf dass es sein Inneres mit heiligen Worten erfüllt.“ – Principalis Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/RighteousJudgement" value="<string name='Actions/SistersOfBattle/RighteousJudgement'/>"/>
	<entry name="SistersOfBattle/RighteousJudgementDescription" value="<string name='Actions/SistersOfBattle/RighteousJudgementDescription'/>"/>
	<entry name="SistersOfBattle/RighteousJudgementFlavor" value="<string name='Actions/SistersOfBattle/RighteousJudgementFlavor'/>"/>
	<entry name="SistersOfBattle/SacresantShield" value="Sacresantis-Schild"/>
	<entry name="SistersOfBattle/SacresantShieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/SacresantShieldFlavor" value="Die streng gläubigen Celestia Sacresantis halten selbst den schlimmsten Schrecken der Galaxis stand. Horden von Mutanten und Häretikern prallen vergeblich gegen ihre Schildwälle, bevor sie mit dem Zorn der Rechtschaffenheit niedergemetzelt werden."/>
	<entry name="SistersOfBattle/SaintlyBlessings" value="Heilige Segen"/>
	<entry name="SistersOfBattle/SaintlyBlessingsDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/SaintlyBlessingsFlavor" value="<string name='Actions/SistersOfBattle/SaintlyBlessingsFlavor'/>"/>
	<entry name="SistersOfBattle/ShieldOfFaith" value="Schild des Glaubens"/>
	<entry name="SistersOfBattle/ShieldOfFaithDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SistersOfBattle/ShieldOfFaithFlavor" value="Die Angehörigen der Adepta Sororitas lernen, dass der Glaube ein Schild ist, der stärker ist als jede Rüstung. Die Überzeugung, dass der Imperator sie beschützen wird, ist so stark, dass die Kriegerinnen selbst die schwersten Wunden überstehen und Hexern standhalten können."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="Simulacrum Imperialis"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="Verringert die Abklingzeit von Glaubensakten."/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="Diese heiligen Symbole der Ekklesiarchie wurden einst von einem der vielen Heiligen des Imperiums getragen oder vielleicht sogar aus seinen Knochen gefertigt. Sie sind ein Quell der Inspiration und des Glaubens, und es gilt als große Ehre, sich mit einer solch unersetzlichen Reliquie auf dem Schlachtfeld zu schmücken."/>
	<entry name="SistersOfBattle/SisterSuperior" value="Prioris"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="Erhöht die Moral von Infanterie- und Paragon-Kriegsanzug-Einheiten."/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="Die Prioris sind der Dreh- und Angelpunkt eines jeden Sororitas-Trupps. Mit ihrer Autorität, die auf jahrelanger Kampferfahrung und ihrem festen Glauben an den Gottimperator fußt, sorgen diese bemerkenswerten Offizierinnen dafür, dass jede Schwester unter ihrem Kommando ihr Potenzial voll ausschöpft, um so die Kampfkraft des gesamten Trupps zu maximieren."/>
	<entry name="SistersOfBattle/SolaceInAnguish" value="<string name='Actions/SistersOfBattle/SolaceInAnguish'/>"/>
	<entry name="SistersOfBattle/SolaceInAnguishDescription" value="<string name='Actions/SistersOfBattle/SolaceInAnguishDescription'/>"/>
	<entry name="SistersOfBattle/SolaceInAnguishFlavor" value="<string name='Actions/SistersOfBattle/SolaceInAnguishFlavor'/>"/>
	<entry name="SistersOfBattle/StirringRhetoric" value="<string name='Actions/SistersOfBattle/StirringRhetoric'/>"/>
	<entry name="SistersOfBattle/StirringRhetoricDescription" value="Erhöht die Panzerung."/>
	<entry name="SistersOfBattle/StirringRhetoricFlavor" value="<string name='Actions/SistersOfBattle/StirringRhetoricFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithful" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithful'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithfulDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithfulDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithfulFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithfulFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoic" value="<string name='Actions/SistersOfBattle/TaleOfTheStoic'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoicDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheStoicDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoicFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheStoicFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarrior" value="<string name='Actions/SistersOfBattle/TaleOfTheWarrior'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarriorDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheWarriorDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarriorFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheWarriorFlavor'/>"/>
	<entry name="SistersOfBattle/ThePassion" value="<string name='Actions/SistersOfBattle/ThePassion'/>"/>
	<entry name="SistersOfBattle/ThePassionDescription" value="Erhöht die Nahkampfgenauigkeit."/>
	<entry name="SistersOfBattle/ThePassionFlavor" value="<string name='Actions/SistersOfBattle/ThePassionFlavor'/>"/>
	<entry name="SistersOfBattle/UsedActOfFaith" value="Glaubensakt eingesetzt"/>
	<entry name="SistersOfBattle/UsedActOfFaithDescription" value="Diese Einheit hat ihre Fähigkeit „Glaubensakt“ in dieser Runde bereits eingesetzt."/>
	<entry name="SistersOfBattle/UsedActOfFaithFlavor" value="<string name='Traits/SistersOfBattle/UsedActOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/UsedSacredRite" value="1 Heilige Stätte genutzt"/>
	<entry name="SistersOfBattle/UsedSacredRite2" value="2 Heilige Stätten genutzt"/>
	<entry name="SistersOfBattle/VengefulSpirit" value="Rachsüchtiger Geist"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="Wirft Schaden auf den Angreifer zurück."/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="„Im Tod liegt auch eine Chance, denn er nimmt uns alle Sorgen. Wenn wir sterben, gibt es nichts, was unsere Wut zurückhält, und es gibt auch keinen Selbsterhaltungsinstinkt mehr. Wir können ein letztes Mal aus unseren Vollen schöpfen und alle Treffer einstecken – für eine letzte Chance auf Gerechtigkeit.“<br/> – Principalis Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/WarHymn" value="<string name='Actions/SistersOfBattle/WarHymn'/>"/>
	<entry name="SistersOfBattle/WarHymnDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="SistersOfBattle/WarHymnFlavor" value="<string name='Actions/SistersOfBattle/WarHymnFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="Mechanisierter Zorn"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="Erhöht die Produktionsrate."/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="„In den letzten Tagen wurde der Eifer stärker. Der Ritus der Bittstellerei wurde vollzogen, um den Maschinengeistern das Äußerste abzuverlangen – schließlich betrieben sie die Manufactorien und Hangars, in denen die uralten Kriegsmaschinen gesalbt wurden. Sie enttäuschten uns nicht.“<br/> – Unbekannter Memorator, Evangelium des Netzes der Tausend Tore"/>
	<entry name="SkilledJink" value="<string name='Actions/SkilledJink'/>"/>
	<entry name="SkilledJinkDescription" value="<string name='Traits/JinkDescription'/>"/>
	<entry name="SkilledJinkFlavor" value="<string name='Actions/SkilledJinkFlavor'/>"/>
	<entry name="Skimmer" value="Antigrav"/>
	<entry name="SkimmerDescription" value="Die Einheit kann sich über Wasser bewegen und ignoriert den Malus durch Flüsse und Stahlkraut. Verringert zudem den Bewegungsmalus in Wäldern und imperialen Ruinen."/>
	<entry name="SkimmerFlavor" value="Manche hochentwickelte Fahrzeuge sind mit Antigravantrieben ausgestattet, dank derer sie schnell über unwegsames Gelände schweben und feindliche Truppen abfangen können, wodurch sie perfekt für überraschende Flankenangriffe geeignet sind."/>
	<entry name="SkullAltar" value="<string name='Features/SkullAltar'/>"/>
	<entry name="SkullAltarDescription" value="Einheiten, die das Hexfeld betreten, erhalten eine Belohnung."/>
	<entry name="SkullAltarFlavor" value="<string name='Features/SkullAltarFlavor'/>"/>
	<entry name="SkullsForTheSkullThrone" value="Schädel für den Schädelthron"/>
	<entry name="SkullsForTheSkullThroneDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="SkullsForTheSkullThroneFlavor" value="Khorne respektiert nur starke und grenzenlos blutrünstige Kämpfer."/>
	<entry name="Skyfire" value="Flugabwehr"/>
	<entry name="SkyfireDescription" value="Erhöht die Genauigkeit gegen Flieger, aber verringert die Genauigkeit gegen Bodeneinheiten, bei denen es sich nicht um Antigrav-, Jetbike- oder Schwebeeinheiten handelt."/>
	<entry name="SkyfireFlavor" value="Flugabwehrwaffen eignen sich hervorragend dazu, feindliche Flugzeuge und Antigraveinheiten vom Himmel zu holen."/>
	<entry name="SlowAndPurposeful" value="Langsam und entschlossen"/>
	<entry name="SlowAndPurposefulDescription" value="Hebt den Malus für schwere Waffen und Geschütze auf."/>
	<entry name="SlowAndPurposefulFlavor" value="Viele Krieger rücken mit einer behäbigen Beharrlichkeit vor, sind deswegen aber nicht weniger todbringend."/>
	<entry name="Slowed" value="Verlangsamt"/>
	<entry name="SlowedDescription" value="Verringert die Bewegung."/>
	<entry name="Smash" value="Wuchtige Hiebe"/>
	<entry name="SmashDescription" value="Erhöht den Panzerungsdurchschlag im Nahkampf."/>
	<entry name="SmashFlavor" value="Den meisten furchterregenden Kreaturen reicht ein einziger Schlag, um die Panzerung eines Fahrzeugs zu knacken oder ein Lebewesen in einen blutigen Brei zu verwandeln."/>
	<entry name="SmokeScreen" value="Nebelwand"/>
	<entry name="SmokeScreenDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="SmokeScreenFlavor" value="<string name='Actions/CreateSmokeScreenFlavor'/>"/>
	<entry name="Sniper" value="Scharfschütze"/>
	<entry name="SniperDescription" value="Erhöht den Schaden und den Panzerungsdurchschlag gegen Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="SniperFlavor" value="Scharfschützenwaffen sind Präzisionswaffen, um das Ziel punktgenau an seinen Schwachstellen anzugreifen."/>
	<entry name="SonicBoom" value="Druckwelle"/>
	<entry name="SonicBoomDescription" value="Erhöht den Schaden gegen Flieger."/>
	<entry name="SoulBlaze" value="Seelenfeuer"/>
	<entry name="SoulBlazeDescription" value="Verursacht in jeder Runde Schaden."/>
	<entry name="SoulBlazed" value="Seelenfeuer"/>
	<entry name="SoulBlazedDescription" value="<string name='Traits/SoulBlazeDescription'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="<string name='Traits/AstraMilitarum/BlastDamageDescription'/>"/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Traits/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDiscipline" value="Bolter-Disziplin"/>
	<entry name="SpaceMarines/BolterDisciplineDescription" value="<string name='Traits/ChaosSpaceMarines/MaliciousVolleysDescription'/>"/>
	<entry name="SpaceMarines/BolterDisciplineFlavor" value="Für einen Space Marine ist der Bolter mehr als nur eine Waffe. Er ist Ausdruck der Göttlichkeit des Menschen und ein Werkzeug des Todes gegen seine Feinde."/>
	<entry name="SpaceMarines/CityTier2" value="Festungserweiterung"/>
	<entry name="SpaceMarines/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="Die zweite Bauphase der Festung wurde eingeleitet. Die Kernanlagen sind bereits errichtet und die Techmarines wurden autorisiert, ihre Operationen auszuweiten und eine zweite Verteidigungslinie aufzubauen."/>
	<entry name="SpaceMarines/CityTier3" value="Hochentwickelte Festung"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="In der dritten Bauphase wird die Festung mit unabhängigen Garnisonen und Stützpfeilern entlang einer zusätzlichen Stadtmauer verstärkt. Im Schatten dieser zyklopischen Barrieren verbringen Servitors und Bedienstete ihr ganzes Leben damit, von einer Aufgabe zur nächsten zu hasten."/>
	<entry name="SpaceMarines/CityTier4" value="Gigantische Festung"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="Mit dieser Erweiterung kann der Artificator den Festungsbau für beendet erklären und die Festung steht in ihrer Größe einer Makropole in nichts nach. Innerhalb ihrer unbezwingbaren Mauern merkt man, dass man sich auf imperialem Boden befindet. Außerhalb davon strömen weitgereiste Stämme herbei, um ihre Tributzahlungen zu leisten, während ihre besten Krieger hoffen, für die tödlichen Prüfungen zugelassen zu werden, um anschließend zu einem Space Marine modifiziert zu werden."/>
	<entry name="SpaceMarines/CloseQuartersFirepower" value="Nahbereichsverheerung"/>
	<entry name="SpaceMarines/CloseQuartersFirepowerDescription" value="Erhöht den Fernkampf-Panzerungsdurchschlag."/>
	<entry name="SpaceMarines/CloseQuartersFirepowerFlavor" value="Auf kürzere Entfernungen entfalten die von Belisarius Cawl für seine Primaris-Marines konzipierten Waffen ihr todbringendes Potenzial – mit dem positiven Nebeneffekt, dass sich die besten Truppen des Imperators ermutigt fühlen, die Distanz zu ihren Gegnern zu verringern."/>
	<entry name="SpaceMarines/DutyEternal" value="Ewige Pflicht"/>
	<entry name="SpaceMarines/DutyEternalDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="SpaceMarines/DutyEternalFlavor" value="Allen Angehörigen des Adeptus Astartes ist ein unbeugsamer Wille gemein. Die wenigen Primaris, denen nach dem Tod die Ehre zuteilwird, in einen Redemptor Dreadnought integriert zu werden, sind sich dieses einzigartigen Privilegs bewusst und wissen um die Verantwortung, die sie tragen. Sie werden weiterkämpfen, wie schwer die Verletzungen oder Schäden auch sein mögen."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Festungsschild"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="Gladius Primus erweist sich schon jetzt als weitaus feindseliger, als die schlimmsten Prognosen des Adeptus Administratum vermuten ließen. Da die physischen Verteidigungsanlagen der Festung fast ständig angegriffen werden, haben die Techmarines die Installation eines Deflektorschilds bewilligt. Einmal errichtet, reicht diese schimmernde Kuppel der Macht aus, um die Feuerkraft ganzer Bataillone (oder selbst eines abtrünnigen Imperialen Ritters) mithilfe von Warpenergie verflüchtigen zu lassen, ehe die Kuppel in sich zusammenfällt."/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Traits/AstraMilitarum/LasDamageDescription'/>"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="Meisterhafter Nahkampf"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="Einige Orden des Adeptus Astartes – darunter die Space Wolves, die Blood Drinkers und die Minotaurs – sind bekannt für ihre meisterhaften Nahkampffähigkeiten. Dass selbst niederrangigste Scout Marines eines solchen Ordens auf dem Schlachtfeld eine Bedrohung darstellen können, haben sie dem Wissensschatz der Generationen vor ihnen und ihrer Ausbildung zu verdanken."/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
	<entry name="SpaceMarines/OmniscopeDescription" value="<string name='Actions/SpaceMarines/OmniscopeDescription'/>"/>
	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/RepulsorField" value="<string name='Actions/SpaceMarines/RepulsorField'/>"/>
	<entry name="SpaceMarines/RepulsorFieldDescription" value="Verringert die Bewegung."/>
	<entry name="SpaceMarines/RepulsorFieldFlavor" value="<string name='Actions/SpaceMarines/RepulsorFieldFlavor'/>"/>
	<entry name="SpaceMarines/SuppressiveBombardment" value="Unterdrückendes Bombardement"/>
	<entry name="SpaceMarines/SuppressiveBombardmentDescription" value="Erhöht den Schaden und belegt bei Angriffen feindliche Infanterieeinheiten mit dem Status „Niedergehalten“, wenn sie an einen weiteren Whirlwind angrenzen."/>
	<entry name="SpaceMarines/SuppressiveBombardmentFlavor" value="Gemäß dem Codex Astartes operieren Whirlwinds am effektivsten in größeren Gruppen. Dabei fällt ihnen die Aufgabe zu, für ein donnerndes Sperrfeuer zu sorgen, das man über sich ergehen lassen muss, da man ihm nicht ausweichen kann. Leicht gepanzerte Truppen und Fahrzeuge überstehen den unerbittlichen Regen aus Gefechtsköpfen selbst dann nicht, wenn sie gut geschützt sind."/>
	<entry name="SpaceMarines/StormShield" value="Sturmschild"/>
	<entry name="SpaceMarines/StormShieldDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="SpaceMarines/StormShieldFlavor" value="Ein Sturmschild ist ein massiver Schild mit einem eingebauten Energiefeldgenerator. Bereits der robuste Schild an sich bietet physischen Schutz, doch besonders beeindruckend ist das Energiefeld, da es so gut wie jeden Angriff ablenken kann. Selbst Angriffe, die normalerweise sogar eine Terminatorrüstung durchschlagen könnten, werden von der schützenden Energie des Sturmschilds mühelos abgewehrt."/>
	<entry name="SpaceMarines/Thunderstrike" value="Thunderstrike"/>
	<entry name="SpaceMarines/ThunderstrikeDescription" value="Erhöht den von Einheiten der Space Marines verursachten Fernkampfschaden gegen Ziele, die von dieser Einheit angegriffen werden."/>
	<entry name="SpaceMarines/ThunderstrikeFlavor" value="Die von Belisarius Cawl entworfenen, schwer bewaffneten Storm Speeder sind für den Kampf gegen bestimmte Ziele konzipiert, wobei der Thunderstrike auf die Neutralisierung von feindlichen Fahrzeugen und Schlüsselzielen spezialisiert ist. Ein Gegner, der mit den Waffen einer solchen Einheit bereits geschwächt wurde, ist gegenüber den Folgeangriffen der Storm-Speeder-Kampfbrüder noch verwundbarer."/>
	<entry name="SpaceMarines/ThunderstrikeTarget" value="<string name='Traits/SpaceMarines/Thunderstrike'/>"/>
	<entry name="SpaceMarines/ThunderstrikeTargetDescription" value="Erhöht den von Einheiten der Space Marines verursachten Fernkampfschaden gegen diese Einheit."/>
	<entry name="SpaceMarines/ThunderstrikeTargetFlavor" value="<string name='Traits/SpaceMarines/ThunderstrikeFlavor'/>"/>
	<entry name="SpaceSlip" value="<string name='Actions/SpaceSlip'/>"/>
	<entry name="SpaceSlipDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Stealth" value="Tarnung"/>
	<entry name="StealthDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="StealthFlavor" value="Manche Kämpfer sind Meister der Tarnung und können sich in den Trümmern des Schlachtfelds verborgen halten, bis sie bereit sind, überraschend zuzuschlagen."/>
	<entry name="StrafingRun" value="Tiefflugangriff"/>
	<entry name="StrafingRunDescription" value="Erhöht die Fernkampfgenauigkeit gegen Bodeneinheiten."/>
	<entry name="StrafingRunFlavor" value="Diese Einheit ist für Bodenangriffe konzipiert. Ihre Waffen sind so ausgerichtet, dass sie unter ihnen ein größtmögliches Blutvergießen anrichten."/>
	<entry name="Strikedown" value="Niederwerfen"/>
	<entry name="StrikedownDescription" value="Verringert vorübergehend die Bewegung der ausgewählten Infanterieeinheit."/>
	<entry name="StrikedownFlavor" value="Ist der Angriff stark genug, kann er selbst den mächtigsten Krieger umhauen."/>
	<entry name="Stubborn" value="Unnachgiebig"/>
	<entry name="StubbornDescription" value="Verringert den Moralverlust."/>
	<entry name="StubbornFlavor" value="Viele Krieger leben und sterben nach dem Motto „Tod vor Unehre“. Nur selten weichen Krieger wie diese einen Schritt zurück, wenn Gefahr droht."/>
	<entry name="Stunned" value="Betäubt"/>
	<entry name="StunnedDescription" value="Verhindert, dass sich die Einheit bewegen oder Aktionen durchführen kann."/>
	<entry name="Suicider" value="Selbstmörder"/>
	<entry name="SuiciderDescription" value="Feindliche Einheiten erhalten keine Erfahrungspunkte, wenn sich diese Einheit selbst tötet."/>
	<entry name="Summon" value="Beschwören"/>
	<entry name="SummonDescription" value="Klassifizierung."/>
	<entry name="SuperHeavy" value="Superschwer"/>
	<entry name="SuperHeavyDescription" value="Klassifizierung."/>
	<entry name="SuperHeavyFlavor" value="Riesige gepanzerte Konstrukte, die über genug Feuerkraft verfügen, um eine ganze Armee in die Luft zu jagen, zu zerschmettern oder zu verbrennen."/>
	<entry name="Supersonic" value="Überschall"/>
	<entry name="SupersonicDescription" value="Erhöht die Bewegung."/>
	<entry name="SupersonicFlavor" value="Überschalleinheiten sind selbst im Vergleich zu standardmäßigen Flugzeugen unwahrscheinlich schnell und deswegen in der Schlacht außerordentlich mobil."/>
	<entry name="Swarms" value="Schwarm"/>
	<entry name="SwarmsDescription" value="Der erlittene Schaden verteilt sich zu gleichen Teilen auf die ganze Einheitengruppe. Allerdings erleidet die Gruppe zusätzliche Treffer durch Explosiv- und Flammenschablonenwaffen."/>
	<entry name="SwarmsFlavor" value="Diese Kreaturen sind so zahlreich vorhanden, dass man sie nicht einzeln, sondern nur als Gruppe bekämpfen kann."/>
	<entry name="Swiftstrike" value="Schneller Hieb"/>
	<entry name="SwiftstrikeDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="TacticalDoctrine" value="Taktische Doktrin"/>
	<entry name="TacticalDoctrineDescription" value="Erhöht die Genauigkeit."/>
	<entry name="TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="Tank" value="Panzer"/>
	<entry name="TankDescription" value="Klassifizierung."/>
	<entry name="TankFlavor" value="Panzer können ihre schiere Masse als Waffe einsetzen, indem sie direkt in und durch konzentrierte feindliche Truppen fahren. Dies führt außerdem häufig dazu, dass die feindliche Kampflinie in Unordnung gerät, da ein näherkommendes Ungetüm aus Metall niemanden kaltlässt."/>
	<entry name="TankHunters" value="Panzerjäger"/>
	<entry name="TankHuntersDescription" value="Erhöht den Panzerungsdurchschlag gegen feindliche Fahrzeuge."/>
	<entry name="TankHuntersFlavor" value="Diese Veteranen des Panzerkriegs können die Schwachstellen feindlicher Fahrzeuge ausmachen und zielen dementsprechend."/>
	<entry name="Tau/AdvancedTargetingSystem" value="Hochentwickeltes Zielsystem"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Erhöht die Fernkampfgenauigkeit."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="Ein hochentwickeltes Zielsystem, das den Fahrzeugschützen unterstützt, indem es wichtige oder gefährliche Ziele identifiziert und Feuerlösungen berechnet, um diese außer Gefecht zu setzen."/>
	<entry name="Tau/AutomatedRepairSystem" value="Automatisches Reparatursystem"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="Winzige Wartungsdrohnen schwirren über beschädigten Systemen umher, um sie mitten in der Schlacht zu reparieren."/>
	<entry name="Tau/BlacksunFilter" value="Filteroptik"/>
	<entry name="Tau/BlacksunFilterDescription" value="Erhöht die Sichtweite."/>
	<entry name="Tau/BlacksunFilterFlavor" value="Dank diesem optischen Filterpaket können Fahrzeugsensoren feindliche Einheiten mit höchster Effizienz und aus größtmöglicher Distanz anvisieren, und das selbst in Nachteinsätzen."/>
	<entry name="Tau/BlastDamage" value="Explosivwaffen-Phasenverzerrung"/>
	<entry name="Tau/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Tau/BlastDamageFlavor" value="Waffen mit flächendeckender Wirkung eignen sich für gewöhnlich besser gegen große Infanterieverbände als gegen einzelne gepanzerte Ziele wie Fahrzeuge. Den Ingenieuren der Erdkaste gelang es jedoch mit einer Phasenverzerrung bei den Energiekomponenten ihrer Explosiv- und Brandwaffen, dass zumindest ein Teil ihres Zerstörungspotentials jegliche Arten von Schilden überwindet."/>
	<entry name="Tau/BoltDamage" value="Brachyura-Produktion"/>
	<entry name="Tau/BoltDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Tau/BoltDamageFlavor" value="Selbst die geschicktesten Techniker der Erdkaste können nicht die Komponenten ihrer Massebeschleunigerwaffen von Hand zusammensetzen. Dadurch, dass sie diese Arbeit der winzigen Rasse der Brachyura überlassen und sie mit kleineren Versionen ihrer Werkzeuge ausrüsten, können sie ihre Produktion verfeinern und so noch genauer gearbeitete Waffen herstellen."/>
	<entry name="Tau/BreakComposure" value="<string name='Actions/Tau/BreakComposure'/>"/>
	<entry name="Tau/BreakComposureDescription" value="Angegriffene Einheiten verlieren Moral."/>
	<entry name="Tau/BreakComposureFlavor" value="<string name='Actions/Tau/BreakComposureFlavor'/>"/>
	<entry name="Tau/CityTier2" value="Ausbreitung"/>
	<entry name="Tau/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Tau/CityTier2Flavor" value="Sobald eine Stadt eine gewisse Größe erreicht hat, beginnt die Wasserkaste eine diplomatische Offensive, um mehr Bürger für den rechten Weg zu gewinnen. Sie greift dabei in großem Umfang auf die vielfältigsten Propagandamittel zurück – von abgeworfenen Pamphleten über Videoübertragungen bis hin zu Lautsprecherdurchsagen."/>
	<entry name="Tau/CityTier3" value="Anthrazodenfundamente"/>
	<entry name="Tau/CityTier3Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Tau/CityTier3Flavor" value="Die dickhäutigen Anthrazoden sind eine nicht besonders intelligente Spezies und werden für gewöhnlich von den T'au bei gefährlichen, körperlich anstrengenden Arbeiten wie dem Asteroidenbergbau eingesetzt. Unter der Aufsicht von den Demiurg und der Erdkaste können sie jedoch mit unübertroffener Leichtigkeit das Fundament von Städten sowie Transporttunnel errichten, um selbst weit voneinander entfernte Sektoren miteinander zu verbinden."/>
	<entry name="Tau/ClusterFire" value="Streufeuer"/>
	<entry name="Tau/ClusterFireDescription" value="Erhöht die Anzahl der Angriffe und den Schaden gegen Bikes, Jetbikes oder Einheiten mit der Eigenschaft „Sehr massig“. Erhöht signifikant die Anzahl der Angriffe und den Schaden gegen Monströse Kreaturen, Fahrzeuge und Festungseinheiten."/>
	<entry name="Tau/ClusterFireFlavor" value="Die einzigartigen Pulsstreukanonen des Kampfanzugs XV107 R'Varna weisen eine enorme Reichweite auf und decken das Zielgebiet mit Mikroplasmasalven ein. Je größer das Ziel ist, desto mehr Plasmawellen schlagen nahezu zeitgleich ein und zerfetzen es problemlos."/>
	<entry name="Tau/CounterfireDefenceSystem" value="Gegenfeuer-Abwehrsystem"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Erhöht die Genauigkeit bei Abwehrfeuer-Attacken."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Actions/Tau/CounterfireDefenceSystemFlavor'/>"/>
	<entry name="Tau/DisruptionPod" value="Störsystem"/>
	<entry name="Tau/DisruptionPodDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="Tau/DisruptionPodFlavor" value="Mithilfe von Störsystemen werden visuell sichtbare wie auch im Magnetspektrum wahrnehmbare Zerrbilder erzeugt, die es dem Feind erschweren, das Fahrzeug aus der Distanz anzuvisieren."/>
	<entry name="Tau/DroneController" value="Drohnensteuerung"/>
	<entry name="Tau/DroneControllerDescription" value="Erhöht die Genauigkeit angrenzender Drohnen."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Actions/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/DroneControllerInRange" value="Drohnensteuerung in Reichweite"/>
	<entry name="Tau/DroneControllerInRangeDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Tau/DroneControllerInRangeFlavor" value="<string name='Actions/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/FieldAmplifierRelay" value="Feldverstärker-Relais"/>
	<entry name="Tau/FieldAmplifierRelayDescription" value="Erhöht die Unverwundbar-Schadensreduktion, wenn die Einheit durch einen Schild geschützt ist."/>
	<entry name="Tau/FieldAmplifierRelayFlavor" value="Das Feldverstärker-Relais in der Größe eines Rucksacks fängt das schützende Kraftfeld der Schilddrohne auf, um es dann wie einen energetischen Schirm über seinen Träger zu spannen und das Signal an andere Relais in Reichweite weiterzuleiten."/>
	<entry name="Tau/FireTeam" value="Koordiniertes Feuer"/>
	<entry name="Tau/FireTeamDescription" value="Erhöht die Fernkampfgenauigkeit, wenn die Einheit an ein nicht-feindliches Fahrzeug oder eine nicht-feindliche Monströse Kreatur angrenzt."/>
	<entry name="Tau/FireTeamFlavor" value="Einige Kampfanzüge und Kampfpanzer-Sensorsysteme können vernetzt werden und sind dann beim Kämpfen in Feuerteams noch effizienter."/>
	<entry name="Tau/FlechetteDischarger" value="Flechettewerfer"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Fügt Nahkampfangreifern Schaden zu."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="An den Rumpf zahlreicher Fahrzeuge der T'au werden reaktive Ladungen angebracht. Sobald sich der Feind nähert, treten todbringende pfeilförmige Projektile aus, die mit rasender Geschwindigkeit auf den Feind niederregnen."/>
	<entry name="Tau/GhostkeelElectrowarfareSuite" value="Phantom-Sensorenangriffspaket"/>
	<entry name="Tau/GhostkeelElectrowarfareSuiteDescription" value="Erhöht die Fernkampf-Schadensreduktion gegen Angriffe aus mindestens 2 Feldern Entfernung."/>
	<entry name="Tau/GhostkeelElectrowarfareSuiteFlavor" value="Das KI-gesteuerte Phantom-Sensorenangriffspaket tastet auf aggressive Weise feindliche Zielspektren ab. Anschließend verschafft es sich Zugang zur feindlichen Sensormatrix, um sie mit Falschinformationen und Datenmüll zu füttern, wodurch es dem Feind nahezu unmöglich ist, aus der Distanz auf den Kampfanzug zu feuern."/>
	<entry name="Tau/HolophotonCountermeasures" value="<string name='Actions/Tau/HolophotonCountermeasures'/>"/>
	<entry name="Tau/HolophotonCountermeasuresDescription" value="Verringert die Fernkampfgenauigkeit."/>
	<entry name="Tau/HolophotonCountermeasuresFlavor" value="<string name='Actions/Tau/HolophotonCountermeasuresFlavor'/>"/>
	<entry name="Tau/IntegratedShieldGenerator" value="Integrierter Schildgenerator"/>
	<entry name="Tau/IntegratedShieldGeneratorDescription" value="Erhöht die Unverwundbar-Schadensreduktion und gewährt eine erhöhte Unverwundbar-Fernkampf-Schadensreduktion."/>
	<entry name="Tau/IntegratedShieldGeneratorFlavor" value="Im Gegensatz zum Kampfanzug XV104 Sturmflut wurde der Kampfanzug XV107 R'Varna für die Feuerunterstützung aus der Ferne und nicht für den mobilen Einsatz konzipiert, sodass er mit stärkerer Panzerung versehen werden konnte. Sein Schildgenerator ist wirkungsvoll gegen Nahkampfangriffe, entfaltet jedoch sein ganzes Potenzial, wenn der Feind aus der Distanz angreift."/>
	<entry name="Tau/Kauyon" value="<string name='Actions/Tau/Kauyon'/>"/>
	<entry name="Tau/KauyonDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="Tau/KauyonFlavor" value="<string name='Actions/Tau/KauyonFlavor'/>"/>
	<entry name="Tau/LasDamage" value="Mor'tonium-Beschleuniger"/>
	<entry name="Tau/LasDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Tau/LasDamageFlavor" value="Immer wieder musste die Menschheit am eigenen Leib erfahren, dass die Nutzung von radioaktivem Material für ihre Waffen stets gefährlich ist. Die T'au sind sich dessen gewiss bewusst, greifen für ihre Militärtechnologien dennoch auf das kürzlich entdeckte Mor'tonium zurück. Wird diese Metalllegierung Sauerstoff ausgesetzt, zerfällt sie innerhalb kürzester Zeit und setzt in einer gewaltigen Explosion Ionenenergie frei, die sich die T'au zunutze machen."/>
	<entry name="Tau/MobileDefencePlatform" value="Mobile Verteidigungsplattform"/>
	<entry name="Tau/MobileDefencePlatformDescription" value="Erhöht die Bewegung beim Transport von Einheiten."/>
	<entry name="Tau/Montka" value="<string name='Actions/Tau/Montka'/>"/>
	<entry name="Tau/MontkaDescription" value="Erhöht den Schaden gegen Einheiten mit weniger als 50% Trefferpunkten."/>
	<entry name="Tau/MontkaFlavor" value="<string name='Actions/Tau/MontkaFlavor'/>"/>
	<entry name="Tau/NetworkedMarkerlight" value="Vernetzter Zielmarkierer"/>
	<entry name="Tau/NetworkedMarkerlightDescription" value="Erhöht die Fernkampfgenauigkeit sowie die ignorierte Fernkampf-Schadensreduktion. Die Angriffe dieser Einheit profitieren nicht von „Ziel erfasst“ und brauchen diese Eigenschaft auch nicht auf."/>
	<entry name="Tau/NetworkedMarkerlightFlavor" value="Diese Zielmarkierer sind direkt mit Waffensystemen verbunden, wodurch diese mit äußerster Präzision ihre Ziele treffen."/>
	<entry name="Tau/NovaBoost" value="<string name='Actions/Tau/NovaBoost'/>"/>
	<entry name="Tau/NovaBoostDescription" value="Erhöht die Bewegung."/>
	<entry name="Tau/NovaBoostFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaElectromagneticShockwave" value="<string name='Actions/Tau/NovaElectromagneticShockwave'/>"/>
	<entry name="Tau/NovaElectromagneticShockwaveDescription" value="Trifft alle Kämpfer der Zieleinheit."/>
	<entry name="Tau/NovaElectromagneticShockwaveFlavor" value="<string name='Actions/Tau/NovaElectromagneticShockwaveFlavor'/>"/>
	<entry name="Tau/NovaFire" value="<string name='Actions/Tau/NovaFire'/>"/>
	<entry name="Tau/NovaFireDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="Tau/NovaFireFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaShield" value="<string name='Actions/Tau/NovaShield'/>"/>
	<entry name="Tau/NovaShieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Tau/NovaShieldFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="Punktverteidigungs-Zielrelais"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Erhöht den Abwehrfeuerschaden gegen feindliche Einheiten, die an nicht-feindliche Einheiten angrenzen."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="Punktverteidigungs-Zielrelais wurden dafür konzipiert, Einheiten der Feuerkaste in der Nähe mit starkem Deckungsfeuer zu unterstützen. Das Punktverteidigungs-Zielrelais visiert automatisch Ziele an und attackiert Gegner, die versuchen anzugreifen."/>
	<entry name="Tau/PulseAccelerator" value="Pulsbeschleuniger"/>
	<entry name="Tau/PulseAcceleratorDescription" value="Erhöht die Reichweite von Pulswaffen."/>
	<entry name="Tau/PulseAcceleratorFlavor" value="<string name='Actions/Tau/PulseAcceleratorFlavor'/>"/>
	<entry name="Tau/PulseBlaster" value="Pulsblaster"/>
	<entry name="Tau/PulseBlasterDescription" value="Erhöht den Schaden und den Panzerungsdurchschlag bei Abwehrfeuer-Attacken."/>
	<entry name="Tau/PulseBlasterFlavor" value="Obwohl die T'au den Nahkampf zu Recht fürchten und meiden, führte die Notwendigkeit von Kämpfen in Space Hulks und labyrinthartigen imperialen Makropolwelten zur Entwicklung des Pulsblasters, umgangssprachlich auch Pulsschrotflinte genannt. Ein einzigartiges Merkmal dieser Waffe ist, dass sie kurz vor dem Feuern negativ geladene Partikel gegen das Ziel aussendet, um die Wirkung der im Anschluss daran abgegebenen Plasmaladung zu verstärken."/>
	<entry name="Tau/Rinyon" value="<string name='Actions/Tau/Rinyon'/>"/>
	<entry name="Tau/RinyonDescription" value="Erhöht die Genauigkeit gegen Einheiten, die an nicht-feindliche Einheiten angrenzen."/>
	<entry name="Tau/RinyonFlavor" value="<string name='Actions/Tau/RinyonFlavor'/>"/>
	<entry name="Tau/RiptideShieldGenerator" value="Sturmflut-Schildgenerator"/>
	<entry name="Tau/RiptideShieldGeneratorDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Tau/RiptideShieldGeneratorFlavor" value="Im ablativen Schild des Sturmflut-Kampfanzugs befindet sich ein kleiner Energiefeldgenerator, dessen Leistung weiter erhöht werden kann, indem man Energie vom Novareaktor des XV104 abzweigt."/>
	<entry name="Tau/Ripyka" value="<string name='Actions/Tau/Ripyka'/>"/>
	<entry name="Tau/RipykaDescription" value="<string name='Actions/Tau/RipykaDescription'/>"/>
	<entry name="Tau/RipykaFlavor" value="<string name='Actions/Tau/RipykaFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="Ripyka'va"/>
	<entry name="Tau/RipykaVaDescription" value="<string name='Actions/Tau/RipykaDescription'/>"/>
	<entry name="Tau/RipykaVaFlavor" value="Commander Coldflame war berühmt für ihre Spitzfindigkeit, da sie nicht nur äußerst vielschichtige Taktiken ersann, sondern es auch verstand, ihre Truppen dazu anzuspornen, ebenso komplex strategisch zu denken. Manch einer erzählt jedoch mit vorgehaltener Hand, dass sie sich für eine Feuerkriegerin zu eingehend mit dem Höheren Wohl befasste…"/>
	<entry name="Tau/SenseOfStone" value="<string name='Actions/Tau/SenseOfStone'/>"/>
	<entry name="Tau/SenseOfStoneDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Tau/SenseOfStoneFlavor" value="<string name='Actions/Tau/SenseOfStoneFlavor'/>"/>
	<entry name="Tau/ShieldGenerator" value="Schildgenerator"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Actions/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StabilisingAnchors" value="Stabilisationsanker"/>
	<entry name="Tau/StabilisingAnchorsDescription" value="Erhöht die Anzahl der Fernkampfangriffe, falls die Einheit in dieser Runde noch nicht bewegt wurde."/>
	<entry name="Tau/StabilisingAnchorsFlavor" value="Die Ballistischen Anzüge KV128 Orkanwoge sind zu groß, um sie wie ihre Kampfanzug-Gegenstücke mit einem Schwebemodul zu versehen. Bork'an, ein Wissenschaftler der Erdkaste, hat sie daher so konzipiert, dass sie sich fest im Boden verankern, sobald sie in Stellung gebracht wurden, wodurch ihre gesamte Reaktorenergie in ihr gewaltiges Waffenarsenal umgeleitet werden kann."/>
	<entry name="Tau/StimulantInjector" value="Stimulanzinjektor"/>
	<entry name="Tau/StimulantInjectorDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Actions/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/StormOfFire" value="<string name='Actions/Tau/StormOfFire'/>"/>
	<entry name="Tau/StormOfFireDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="Tau/StormOfFireFlavor" value="<string name='Actions/Tau/StormOfFireFlavor'/>"/>
	<entry name="Tau/SubvertCity" value="<string name='Actions/Tau/SubvertCity'/>"/>
	<entry name="Tau/SubvertCityDescription" value="Verringert die Loyalität."/>
	<entry name="Tau/SubvertCityFlavor" value="<string name='Actions/Tau/SubvertCityFlavor'/>"/>
	<entry name="Tau/SupportSystems" value="Unterstützungssysteme"/>
	<entry name="Tau/SupportSystemsDescription" value="Ermöglicht die Installation von Unterstützungssystemen."/>
	<entry name="Tau/SupportSystemsFlavor" value="Die Kampfanzüge der T'au wurden modular konzipiert, was bedeutet, dass die Erdkaste sie problemlos an bestimmte Kampfsituationen anpassen kann. Jeder Kampfanzug verfügt jedoch nur über eine bestimmte Anzahl von Anschlüssen, und für eine solche Anpassung die Feuerkraft des Kampfanzugs zu verringern, ist eine Entscheidung, die man nicht gerne trifft."/>
	<entry name="Tau/SupportingFire" value="Unterstützungsfeuer"/>
	<entry name="Tau/SupportingFireDescription" value="Erhöht den Abwehrfeuerschaden gegen feindliche Einheiten, die an nicht-feindliche Einheiten angrenzen."/>
	<entry name="Tau/SupportingFireFlavor" value="Die Doktrin der Feuerkaste, wie sie im Kodex des Feuers verankert ist, sieht vor, dass jeder Krieger seine Kameraden beschützen muss. Auf dem Schlachtfeld unterstützen sich die einzelnen Trupps gegenseitig mithilfe sich überlappender Schussfelder."/>
	<entry name="Tau/TargetAcquired" value="Ziel erfasst"/>
	<entry name="Tau/TargetAcquiredDescription" value="Erhöht die Fernkampfgenauigkeit der T'au gegen die Einheit. Verringert die Fernkampf-Schadensreduktion der Einheit gegen die T'au. Endet, wenn die Einheit von T'au-Einheiten angegriffen wurde."/>
	<entry name="Tau/TargetAcquiredFlavor" value="Bei keiner anderen Rasse arbeiten die Truppen so eng zusammen wie bei den T'au, und Zielmarkierer sind dafür das perfekte Beispiel. Es handelt sich dabei um tragbare Zielerfassungslaser, die mit dem informationstechnischen System der T'au verbunden sind und anderen T'au-Truppen präzise Angriffe ermöglichen."/>
	<entry name="Tau/TidewallShieldline" value="Wellenbrecher-Schildbarriere"/>
	<entry name="Tau/TidewallShieldlineDescription" value="Der Fernkampfschaden des Feindes wird auf den Angreifer zurückgeworfen, sofern er nicht mit Explosivwaffen, Flammenschablonenwaffen oder Hexenfeuer verursacht wurde."/>
	<entry name="Tau/TidewallShieldlineFlavor" value="Die am häufigsten von den Armeen des Sternenreichs der T'au genutzte Befestigung ist die Wellenbrecher-Schildbarriere, eine Mauer aus Energie, hinter der Infanterie Deckung suchen kann. Während die Salven des Feindes aufgrund des refraktiven Feldes der Schildbarriere harmlos zischen und knistern, entfesseln die Feuerkrieger unter ihrem Schutz im Gegenzug einen glühenden Hagel aus Pulsfeuer. Noch schlimmer für jeden Aggressor, der die T'au aus der Deckung treiben will, ist, dass diese Energiemauer die kinetische Energie zurückwerfen kann und Laserschüsse sowie panzerbrechende Geschosse zurück in die feindlichen Reihen abprallen lässt."/>
	<entry name="Tau/TidewallShieldlineCity" value="<string name='Traits/Tau/TidewallShieldline'/>"/>
	<entry name="Tau/TidewallShieldlineCityDescription" value="<string name='Traits/Tau/TidewallShieldlineDescription'/>"/>
	<entry name="Tau/TidewallShieldlineCityFlavor" value="<string name='Traits/Tau/TidewallShieldlineFlavor'/>"/>
	<entry name="Tau/TidewallShieldlineOutpost" value="<string name='Traits/Tau/TidewallShieldline'/>"/>
	<entry name="Tau/TidewallShieldlineOutpostDescription" value="<string name='Traits/Tau/TidewallShieldlineDescription'/>"/>
	<entry name="Tau/TidewallShieldlineOutpostFlavor" value="<string name='Traits/Tau/TidewallShieldlineFlavor'/>"/>
	<entry name="Tau/Unity" value="<string name='Actions/Tau/Unity'/>"/>
	<entry name="Tau/UnityDescription" value="<string name='Actions/Tau/UnityDescription'/>"/>
	<entry name="Tau/UnityFlavor" value="<string name='Actions/Tau/UnityFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="Einheit in Vielfalt"/>
	<entry name="Tau/UtopiaBonusDescription" value="Erhöht den Loyalitätsbonus durch neue Gebäudearten."/>
	<entry name="Tau/UtopiaBonusFlavor" value="Die ideale T'au-Stadt soll allen anderen als schillerndes Beispiel dienen, indem sie das perfekte Gleichgewicht der verschiedensten Kasten innerhalb der T'au repräsentiert, die harmonisch mit ihren abhängigen Rassen und Hilfstruppen zusammenleben."/>
	<entry name="Tau/VectoredRetroThrusters" value="Schubumkehrdüsen"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Erhöht die Bewegung und ignoriert feindliche Kontrollzonen."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Actions/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="Luftzielverfolger"/>
	<entry name="Tau/VelocityTrackerDescription" value="Erhöht die Fernkampfgenauigkeit gegen Flieger."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Actions/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tau/VolleyFire" value="<string name='Actions/Tau/VolleyFire'/>"/>
	<entry name="Tau/VolleyFireDescription" value="Erhöht die Anzahl der Fernkampfangriffe, falls die Einheit nicht bewegt wurde."/>
	<entry name="Tau/VolleyFireFlavor" value="<string name='Actions/Tau/VolleyFireFlavor'/>"/>
	<entry name="Tau/ZephyrsGrace" value="<string name='Actions/Tau/ZephyrsGrace'/>"/>
	<entry name="Tau/ZephyrsGraceDescription" value="Erhöht die Anzahl der Aktionspunkte."/>
	<entry name="Tau/ZephyrsGraceFlavor" value="<string name='Actions/Tau/ZephyrsGraceFlavor'/>"/>
	<entry name="TelekineDome" value="Telekinetischer Schild"/>
	<entry name="TelekineDomeDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="TelekineDomeFlavor" value="<string name='Actions/TelekineDomeFlavor'/>"/>
	<entry name="TeleportHomer" value="Teleport-Peilsender"/>
	<entry name="TeleportHomerDescription" value="Ermöglicht das Absetzen von Chaplains, Sturmterminatoren und Terminators aus dem Orbit ohne Verbrauch von Aktionspunkten, wenn die entsprechenden Einheiten neben dieser Einheit platziert werden."/>
	<entry name="TeleportHomerFlavor" value="Teleport-Peilsender emittieren ein starkes Signal, das von den Teleporterphalangen der Angriffskreuzer im Orbit erfasst werden kann. Durch Verwendung solch exakter Landekoordinaten bleibt bei der Teleportation nur ein minimales Risiko."/>
	<entry name="Template" value="Flammen"/>
	<entry name="TemplateDescription" value="<string name='Traits/BeamDescription'/>"/>
	<entry name="TerminatorArmour" value="Terminatorrüstung"/>
	<entry name="TerminatorArmourDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="TerminatorArmourFlavor" value="Terminatorrüstung bietet den besten Schutz, den ein Space Marine haben kann. Es heißt, eine Terminatorrüstung könne sogar den titanischen Energien im Kern eines Plasmareaktors standhalten, und tatsächlich war dies die ursprüngliche Aufgabe der Rüstungsprototypen."/>
	<entry name="Tesla" value="Tesla"/>
	<entry name="TeslaDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="TeslaFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="TeslaDamage" value="Viridianische Entladung"/>
	<entry name="TeslaDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="TeslaDamageFlavor" value="Der leuchtend grüne Blitz, der von diesen uralten exotischen Waffen der Necrons ausgesendet wird, knistert von Leben erfüllt. Trifft er auf ein Ziel, windet er sich um das Opfer und umgreift es förmlich, als wäre er ein denkendes Lebewesen."/>
	<entry name="TheFleshIsWeak" value="Das Fleisch ist schwach"/>
	<entry name="TheFleshIsWeakDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="TrainedSentinelPilots" value="Ausgebildete Sentinel-Piloten"/>
	<entry name="TrainedSentinelPilotsDescription" value="Erhöht den Schaden."/>
	<entry name="TrainedSentinelPilotsFlavor" value="Sentinels erfüllen im Imperium der Menschheit vielfältige Aufgaben. So werden sie beispielsweise als Hebewerkzeug zum Bewaffnen von Raumschiffen verwendet, aber auch als offene Späher. Manche Sentinels sind mit verbesserter Panzerung und Stabilisatoren für schwerere Waffen ausgestattet. Der Sentinel erfüllt dann nicht mehr die Aufgaben einer Unterstützungs- oder Aufklärungseinheit, sondern dient dem Imperium als schwere Feuerplattform."/>
	<entry name="Traktor" value="Fangstrahla"/>
	<entry name="TraktorDescription" value="Macht feindliche Flieger bewegungsunfähig."/>
	<entry name="Transport" value="Transporter"/>
	<entry name="TransportDescription" value="Ermöglicht den Transport von Infanterieeinheiten und Monströsen Kreaturen."/>
	<entry name="TransportFlavor" value="Mit manchen Transportern kann Infanterie über das Schlachtfeld transportiert werden, wodurch die Infanterie schneller und besser geschützt ist. Wird der Transporter jedoch zerstört, verbrennen die Insassen bei lebendigem Leibe."/>
	<entry name="TurboBoost" value="Turboboost"/>
	<entry name="TurboBoostDescription" value="Erhöht die Bewegung."/>
	<entry name="Tusked" value="Ries'nhaua"/>
	<entry name="TuskedDescription" value="Erhöht die Anzahl der Angriffe mit Nahkampfwaffen."/>
	<entry name="Tyranids/AcidBlood" value="Säureblut"/>
	<entry name="Tyranids/AcidBloodDescription" value="Feindliche Einheiten, die Nahkampfschaden verursachen, erleiden Schaden."/>
	<entry name="Tyranids/AcidBloodFlavor" value="Das fremdartige Blut bestimmter Tyraniden ist so ätzend, dass es sich durch Ceramitpanzerung fressen und Fleisch in wenigen Augenblicken auflösen kann."/>
	<entry name="Tyranids/AdaptiveBiology" value="<string name='Actions/Tyranids/AdaptiveBiology'/>"/>
	<entry name="Tyranids/AdaptiveBiologyDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Tyranids/AdaptiveBiologyFlavor" value="<string name='Actions/Tyranids/AdaptiveBiologyFlavor'/>"/>
	<entry name="Tyranids/AlphaWarrior" value="<string name='Actions/Tyranids/AlphaWarrior'/>"/>
	<entry name="Tyranids/AlphaWarriorDescription" value="Erhöht die Genauigkeit."/>
	<entry name="Tyranids/AlphaWarriorFlavor" value="<string name='Actions/Tyranids/AlphaWarriorFlavor'/>"/>
	<entry name="Tyranids/Biomorph" value="Biomorph"/>
	<entry name="Tyranids/BiomorphDescription" value="Klassifizierung."/>
	<entry name="Tyranids/BiomorphDamage" value="Biomorph-Adaption"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="Tyraniden sind an sich beileibe keine Wesen, die für jede Situation bestens gewappnet sind. Allerdings sind sie aufgrund ihrer DNA auf verschiedenste Weise anpassungsfähig. Um sich diesen Umstand zunutze machen zu können und das Verhalten sowie die physikalischen Fähigkeiten auf dem Schlachtfeld zu optimieren, sind jedoch große Ressourcenmengen erforderlich."/>
	<entry name="Tyranids/BoundingLeap" value="Flink"/>
	<entry name="Tyranids/BoundingLeapDescription" value="Hebt den Bewegungsmalus durch Flüsse auf."/>
	<entry name="Tyranids/BroodProgenitor" value="<string name='Actions/Tyranids/BroodProgenitor'/>"/>
	<entry name="Tyranids/BroodProgenitorDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="Tyranids/BroodProgenitorFlavor" value="<string name='Actions/Tyranids/BroodProgenitorFlavor'/>"/>
	<entry name="Tyranids/ChameleonicSkin" value="Chamäleonschuppen"/>
	<entry name="Tyranids/ChameleonicSkinDescription" value="Ermöglicht Abwehrfeuer-Attacken mit Nahkampfwaffen."/>
	<entry name="Tyranids/CityDamage" value="Halle der Spinnen"/>
	<entry name="Tyranids/CityDamageDescription" value="Verursacht in jeder Runde Schaden."/>
	<entry name="Tyranids/CityDamageFlavor" value="Wer eine ausgewachsene Tyranidenstadt betritt, muss sich so fühlen, als befände er sich im Inneren eines biologisch generierten feindseligen Riesen, der im Begriff ist, den Eindringling zu verdauen. Er findet dort riesige, mit Gift ummantelte Darmzotten, endlose Reihen von Zähnen und Schließmuskeln in der Größe von Marktplätzen vor, die sich unverhofft öffnen und ganze Armeen in säurehaltige Verdauungstümpel stürzen lassen können."/>
	<entry name="Tyranids/CityGrowth" value="Aggressive Expansion"/>
	<entry name="Tyranids/CityGrowthDescription" value="Erhöht die Wachstumsrate."/>
	<entry name="Tyranids/CityGrowthFlavor" value="Den Tyraniden geht es bei ihren Städten nicht um Größe, sondern um Produktivität. Solange eine Stadt dazu in der Lage ist, effektiv Truppen zu produzieren, spielt die vorhandene Landmenge keine Rolle. Sollte die Stadt es jedoch als nötig erachten zu expandieren, so kann dies äußerst schnell vonstattengehen. Schließmuskeln öffnen sich und stoßen Hakensehnen mit Samenkapseln aus, die überall in der Umgebung landen und wie bei Stachelwürgern aufbrechen. Binnen kürzester Zeit ist das Gebiet besetzt und ein neues Gebäude entsteht."/>
	<entry name="Tyranids/CityLoyalty" value="Intelligenzdispersion"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Verringert den Loyalitätsmalus durch die Anzahl der Städte."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="Die gesamte Kolonie – von den großen Produktionsorganismen über die kleineren Kreaturen zur Aufrechterhaltung der Prozesse bis hin zum Substrat selbst – wird mit geringen Mengen einer grauen Neuralsubstanz versorgt, damit die Schwarmkreaturen besser kontrolliert werden können."/>
	<entry name="Tyranids/CityPopulationLimit" value="Biogenese-Organellen"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Erhöht das Bevölkerungslimit."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="Man kann nicht sagen, dass eine tyranidische Schwarmstadt von Leben erfüllt ist. Willenlose „Organellen“-Kreaturen werden erzeugt und erledigen überlebenswichtige Aufgaben, bevor sie reassimiliert werden. An der Stadt können strukturelle Änderungen vorgenommen werden, um diese Organismen in einem kürzeren Zeitfenster erschaffen und reabsorbieren zu können, wodurch sich ihre Gesamtzahl erhöhen lässt."/>
	<entry name="Tyranids/CityProduction" value="Propulsive Vermehrung"/>
	<entry name="Tyranids/CityProductionDescription" value="Erhöht die Produktionsrate."/>
	<entry name="Tyranids/CityProductionFlavor" value="Zu warten, bis neugeborene Tyraniden ihre Brutkammer verlassen, wäre recht ineffizient. Genetoren stießen auf angepasste Gebäude, in denen die neugeborenen Kreaturen mithilfe eines ruckartigen elektrischen Impulses aus ihrer Brutkammer ausgestoßen werden, woraufhin sie sogleich erneut verwendet werden kann."/>
	<entry name="Tyranids/CityTier2" value="Absorber-Dispersion"/>
	<entry name="Tyranids/CityTier2Description" value="Erhöht den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="Tyranids/CityTier2Flavor" value="Wenn die Kolonie herangewachsen ist, müssen die Absorber nicht mehr aus Gründen der Sicherheit in riesigen Schwärmen operieren. Das Schwarmbewusstsein kann sie auf ein größeres Gebiet verteilen und somit weitflächiger Biomasse gewinnen."/>
	<entry name="Tyranids/DiffusionField" value="<string name='Actions/Tyranids/DiffusionField'/>"/>
	<entry name="Tyranids/DiffusionFieldDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="Tyranids/DiffusionFieldFlavor" value="<string name='Actions/Tyranids/DiffusionFieldFlavor'/>"/>
	<entry name="Tyranids/Dominion" value="<string name='Actions/Tyranids/Dominion'/>"/>
	<entry name="Tyranids/DominionDescription" value="<string name='Actions/Tyranids/DominionDescription'/>"/>
	<entry name="Tyranids/DominionFlavor" value="<string name='Actions/Tyranids/DominionFlavor'/>"/>
	<entry name="Tyranids/ExploitWeaknesses" value="<string name='Actions/Tyranids/ExploitWeaknesses'/>"/>
	<entry name="Tyranids/ExploitWeaknessesDescription" value="Verringert die Panzerung."/>
	<entry name="Tyranids/ExploitWeaknessesFlavor" value="<string name='Actions/Tyranids/ExploitWeaknessesFlavor'/>"/>
	<entry name="Tyranids/FeederBeast" value="Fressbestie"/>
	<entry name="Tyranids/FeederBeastDescription" value="Der Schaden, der von dieser Einheit verursacht wird, wird in Heilung umgewandelt."/>
	<entry name="Tyranids/GraspingTail" value="<string name='Actions/Tyranids/GraspingTail'/>"/>
	<entry name="Tyranids/GraspingTailDescription" value="Verringert die Anzahl der Angriffe."/>
	<entry name="Tyranids/GraspingTailFlavor" value="<string name='Actions/Tyranids/GraspingTailFlavor'/>"/>
	<entry name="Tyranids/HiveCommander" value="<string name='Actions/Tyranids/HiveCommander'/>"/>
	<entry name="Tyranids/HiveCommanderDescription" value="Erhöht den Schaden und die Schadensreduktion."/>
	<entry name="Tyranids/HiveCommanderFlavor" value="<string name='Actions/Tyranids/HiveCommanderFlavor'/>"/>
	<entry name="Tyranids/IndescribableHorror" value="<string name='Actions/Tyranids/IndescribableHorror'/>"/>
	<entry name="Tyranids/IndescribableHorrorDescription" value="Verringert in jeder Runde die Moral."/>
	<entry name="Tyranids/IndescribableHorrorFlavor" value="<string name='Actions/Tyranids/IndescribableHorrorFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="Gant-Instinkte"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Verringert die laufenden Biomassekosten."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="Das Schwarmbewusstsein gibt seinem Schwarm in der Regel keinen Entscheidungsspielraum und hält – abgesehen von unbewussten Verhaltensweisen der Kreaturen – alle Fäden in der Hand. Bisweilen lässt es den Griff jedoch etwas lockerer, damit die kleineren Organismen eigenmächtiger handeln können, soweit es dem Schwarmbewusstsein dienlich ist."/>
	<entry name="Tyranids/InstinctiveBehaviour" value="Instinktives Verhalten"/>
	<entry name="Tyranids/InstinctiveBehaviourDescription" value="<string name='Traits/Tyranids/InstinctiveBehaviourLurkDescription'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourLurkFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourFeed" value="Instinktives Verhalten (Fressen)"/>
	<entry name="Tyranids/InstinctiveBehaviourFeedDescription" value="Die Einheit verliert in jeder Runde Trefferpunkte, solange sie nicht in Reichweite einer Synapsenkreatur ist."/>
	<entry name="Tyranids/InstinctiveBehaviourFeedFlavor" value="Viele Tyranidenorganismen fallen in ihre Urinstinkte zurück, wenn sie nicht vom Willen des Schwarmbewusstseins kontrolliert oder koordiniert werden."/>
	<entry name="Tyranids/InstinctiveBehaviourHunt" value="Instinktives Verhalten (Jagen)"/>
	<entry name="Tyranids/InstinctiveBehaviourHuntDescription" value="Die Einheit verliert in jeder Runde Bewegungspunkte, solange sie nicht in Reichweite einer Synapsenkreatur ist."/>
	<entry name="Tyranids/InstinctiveBehaviourHuntFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourLurk" value="Instinktives Verhalten (Lauern)"/>
	<entry name="Tyranids/InstinctiveBehaviourLurkDescription" value="Die Moral der Einheit sinkt in jeder Runde, solange sie nicht in Reichweite einer Synapsenkreatur ist."/>
	<entry name="Tyranids/InstinctiveBehaviourLurkFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride" value="Instinktives Verhalten unterdrücken"/>
	<entry name="Tyranids/InstinctiveBehaviourOverrideDescription" value="Die Einheit lässt sich nicht von ihren Instinkten leiten."/>
	<entry name="Tyranids/InstinctiveBehaviourOverrideFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveFire" value="Instinktives Feuern"/>
	<entry name="Tyranids/InstinctiveFireDescription" value="Kann nur die Einheit ins Visier nehmen, die sich dieser Einheit am nächsten befindet."/>
	<entry name="Tyranids/InstinctiveFireFlavor" value="Tyrannozyten werden von der Schwarmflotte als Transportmittel gezüchtet, um ihre wichtigsten Truppen aus dem Weltraum anzulanden und sie näher an die Biomasse des Feindes zu bringen. Wenn der ursprüngliche Zweck erfüllt ist, ist die Arbeit dieser nahezu unbewussten Kreaturen jedoch noch nicht getan. Schwebend lassen sie weiterhin einen höllischen organischen Säureregen niedergehen und peitschen mit ihren fuchtelnden Tentakeln auf alles in ihrer Nähe ein."/>
	<entry name="Tyranids/LivingBatteringRam" value="<string name='Actions/Tyranids/LivingBatteringRam'/>"/>
	<entry name="Tyranids/LivingBatteringRamDescription" value="Erhöht den Schaden."/>
	<entry name="Tyranids/LivingBatteringRamFlavor" value="<string name='Actions/Tyranids/LivingBatteringRamFlavor'/>"/>
	<entry name="Tyranids/LongRangedDamage" value="Okulare Ausdehnung"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="Mit gewöhnlicher binokularer Wahrnehmung kann es mitunter schwierig sein, Gegner aus größeren Entfernungen ins Visier zu nehmen. Dagegen kann Abhilfe geschaffen werden, indem der Organismus mit mehr und vielfältigeren Okularsensoren angepasst wird."/>
	<entry name="Tyranids/MassIncubation" value="<string name='Actions/Tyranids/MassIncubation'/>"/>
	<entry name="Tyranids/MassIncubationDescription" value="<string name='Actions/Tyranids/MassIncubationDescription'/>"/>
	<entry name="Tyranids/MassIncubationFlavor" value="<string name='Actions/Tyranids/MassIncubationFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="Biowaffen-Symbiose"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="Als man das erste Mal auf die Tyraniden stieß – insbesondere auf die Zoats, die als deren Sklavenrasse gelten –, stellten sie und ihre Waffen unterschiedliche Organismen dar. Mit der Zeit verschmolzen die Tyraniden und ihre Waffen jedoch mehr und mehr zu einer Einheit."/>
	<entry name="Tyranids/Onslaught" value="<string name='Actions/Tyranids/Onslaught'/>"/>
	<entry name="Tyranids/OnslaughtDescription" value="Erhöht die Bewegung."/>
	<entry name="Tyranids/OnslaughtFlavor" value="<string name='Actions/Tyranids/OnslaughtFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="Verringert die Genauigkeit."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation" value="Beuteadaption"/>
	<entry name="Tyranids/PreyAdaptationDescription" value="Das Schwarmbewusstsein erhält Forschungspunkte, wenn der Malantroph Erfahrungspunkte im Kampf erhält."/>
	<entry name="Tyranids/PreyAdaptationFlavor" value="Der Malantroph extrahiert nicht nur neuartiges Genmaterial aus Gefallenen. Er ist auch dazu in der Lage, die erlernten Muster, die von einer Kreatur zur nächsten weitergegeben wurden, zu erfassen und in sich aufzunehmen, damit sie dann auf die Kreaturen des Schwarmbewusstseins übertragen werden können."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="Geburtskanäle"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Verringert die laufenden Einflusskosten."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="Geburtskanäle sind eine einfache strukturelle Änderung in den großen Organismen, die die Tyranidenbauten bilden. Man könnte sie als biologische Fließbänder bezeichnen, die die Erschaffung vieler Kreaturen parallel und ohne direkte Kontrolle des Schwarmbewusstseins ermöglichen."/>
	<entry name="Tyranids/PsychicBarrier" value="Psionische Barriere"/>
	<entry name="Tyranids/PsychicBarrierDescription" value="Erhöht die Unverwundbar-Schadensreduktion."/>
	<entry name="Tyranids/PsychicBarrierFlavor" value="Selbst Schreckenskreaturen der Tyraniden von der Größe eines Maleceptoren verfügen über natürliche Verteidigungseigenschaften. Abgesehen von seiner schieren Masse und seiner widerstandsfähigen Panzerung projiziert der Maleceptor jedoch auch eine wirkungsvolle psionische Barriere, die eingehende Geschosse und Energieladungen ablenken oder einfach verdampfen lassen kann, bevor sie auf ihr Ziel treffen."/>
	<entry name="Tyranids/PsychicOverload" value="<string name='Actions/Tyranids/PsychicOverload'/>"/>
	<entry name="Tyranids/PsychicOverloadDescription" value="Trifft mit erhöhter Genauigkeit."/>
	<entry name="Tyranids/PsychicOverloadFlavor" value="Die ätherischen Pseudopodien, die vom Hirngewebe eines Maleceptoren projiziert werden, sind Manifestationen des Schattens im Warp selbst – der vernichtenden psionischen Präsenz des Schwarmbewusstseins. Sollten sie das Bewusstsein eines geschwächten Gegners auch nur ansatzweise erreichen, erfährt dieser unmittelbar die erschreckende Unermesslichkeit dieser Immanenz – und stirbt schnell, grausam und explosionsartig."/>
	<entry name="Tyranids/RakingStrike" value="Schlitzender Schlag"/>
	<entry name="Tyranids/RakingStrikeDescription" value="Erhöht die Anzahl der Nahkampfangriffe der Schwarmdrude und erhöht zusätzlich dazu die Anzahl der Nahkampfangriffe gegen Flieger."/>
	<entry name="Tyranids/RakingStrikeFlavor" value="Durch Verlängern und Stärken der Klingensporne auf der Unterseite einer Schwarmdrude kann sie tödliche Angriffe im Vorbeifliegen durchführen. Zudem sorgen geschärfte Flügelspitzen dafür, dass sie feindliche Piloten direkt angreifen kann."/>
	<entry name="Tyranids/RapaciousHunger" value="<string name='Actions/Tyranids/RapaciousHunger'/>"/>
	<entry name="Tyranids/RapaciousHungerDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="Tyranids/RapaciousHungerFlavor" value="<string name='Actions/Tyranids/RapaciousHungerFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="Synapsenarchevore."/>
	<entry name="Tyranids/Reclamation2Description" value="Verringert die Einflusskosten."/>
	<entry name="Tyranids/Reclamation2Flavor" value="Verdauungstümpel scheinen die einfachsten Organismen der Tyraniden zu sein, schließlich handelt es sich dabei lediglich um blubbernde, säurehaltige Gruben zur Verarbeitung von Biomasse. Da sie jedoch im Lebenszyklus der Tyraniden eine entscheidende Rolle spielen, wurden sie über viele Generationen hinweg optimiert. Im Zuge dieser Anpassung wurden Kontrollsynapsen an den Rändern der Verdauungstümpel angebracht, damit der Zersetzungsvorgang weniger vom Schwarmbewusstsein kontrolliert werden muss."/>
	<entry name="Tyranids/Reclamation3" value="Assimilierungsbeschleuniger"/>
	<entry name="Tyranids/Reclamation3Description" value="Entfernt die Abklingzeit."/>
	<entry name="Tyranids/Reclamation3Flavor" value="Nur wenn das Rückverwandeln ein effizienter Prozess ist, ist es dem Schwarmbewusstsein auch wirklich von Nutzen. Je weniger Biomasse durch Rückverwandlung gewonnen wird, desto höher ist die Wahrscheinlichkeit einer Niederlage. Dank dieser Anpassung findet eine kontinuierliche Rückverwandlung statt, was zu einer hohen Wiederverwertbarkeitsrate führt."/>
	<entry name="Tyranids/Regeneration" value="Regeneration"/>
	<entry name="Tyranids/RegenerationDescription" value="Stellt in jeder Runde Trefferpunkte wieder her."/>
	<entry name="Tyranids/RegenerationFlavor" value="Manche Tyraniden haben die Fähigkeit, sich von schrecklichen Verletzungen zu erholen, die eigentlich tödlich enden müssten."/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="Aasfresser-Adaption"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Verringert die laufenden Einflusskosten."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="Das Schwarmbewusstsein gibt seinem Schwarm in der Regel keinen Entscheidungsspielraum und hält – abgesehen von unbewussten Verhaltensweisen der Kreaturen – alle Fäden in der Hand. Bisweilen lässt es den Griff jedoch etwas lockerer, damit die kleineren Organismen eigenmächtiger handeln können, soweit es dem Schwarmbewusstsein dienlich ist."/>
	<entry name="Tyranids/ScourgeOfTheBrood" value="<string name='Actions/Tyranids/ScourgeOfTheBrood'/>"/>
	<entry name="Tyranids/ScourgeOfTheBroodDescription" value="Erhöht den Schaden."/>
	<entry name="Tyranids/ScourgeOfTheBroodFlavor" value="<string name='Actions/Tyranids/ScourgeOfTheBroodFlavor'/>"/>
	<entry name="Tyranids/ShadowInTheWarp" value="<string name='Actions/Tyranids/ShadowInTheWarp'/>"/>
	<entry name="Tyranids/ShadowInTheWarpDescription" value="Verringert in jeder Runde die Moral."/>
	<entry name="Tyranids/ShadowInTheWarpFlavor" value="<string name='Actions/Tyranids/ShadowInTheWarpFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="Gigabohrerschwärme"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Erhöht den Panzerungsdurchschlag."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="Im einzigartigen und zugleich abstoßenden Gigabohrer-Nest tummeln sich Bohrkäfer – azide Insektoiden, die sich in rasender Kampfeswut selbst aus Waffen katapultieren. Gigabohrerschwärme bestehen aus weniger, dafür aber deutlich größeren Käfern, die entsprechend mehr Schaden verursachen."/>
	<entry name="Tyranids/SingularPurpose" value="<string name='Actions/Tyranids/SingularPurpose'/>"/>
	<entry name="Tyranids/SingularPurposeDescription" value="Erhöht bei Schwarmboten die Genauigkeit und den Schaden gegen diese Einheit."/>
	<entry name="Tyranids/SingularPurposeFlavor" value="<string name='Actions/Tyranids/SingularPurposeFlavor'/>"/>
	<entry name="Tyranids/SporeCloud" value="<string name='Actions/Tyranids/SporeCloud'/>"/>
	<entry name="Tyranids/SporeCloudDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="Tyranids/SporeCloudFlavor" value="<string name='Actions/Tyranids/SporeCloudFlavor'/>"/>
	<entry name="Tyranids/SymbioticTargeting" value="Symbiotisches Zielen"/>
	<entry name="Tyranids/SymbioticTargetingDescription" value="Erhöht die Fernkampfgenauigkeit, wenn die Einheit stationär bleibt."/>
	<entry name="Tyranids/SynapseLink" value="Synapsenverbindung"/>
	<entry name="Tyranids/SynapseLinkDescription" value="Die Einheit kann moralisch nicht aufgewühlt oder gebrochen werden, ist immun gegen Angst und wird nicht von instinktivem Verhalten gelenkt."/>
	<entry name="Tyranids/SynapseLinkFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/SynapticBacklash" value="Synaptische Gegenreaktion"/>
	<entry name="Tyranids/SynapticBacklashDescription" value="Termaganten im Wirkungsbereich erleiden Schaden, wenn der Tervigon stirbt."/>
	<entry name="Tyranids/SynapticBacklashFlavor" value="Wenngleich sich Tyraniden nicht zueinander hingezogen fühlen, beobachten sie sehr genau, wenn ihresgleichen sterben oder in den Verdauungstümpel wandern. Das psychische Trauma, das sie beim Tod eines Tervigons erleiden, ist ungewöhnlich, zumal es dem Schwarmbewusstsein augenscheinlich nicht gelingt, etwas dagegen zu unternehmen. Möglicherweise ist diese Traumaresonanz tief im Genmaterial verankert und stammt noch von einer längst ausgestorbenen Rasse – ein Indiz dafür, dass das Schwarmbewusstsein selten Neues erschafft, sondern eher plagiiert."/>
	<entry name="Tyranids/ToxinSacs" value="Toxinkammern"/>
	<entry name="Tyranids/ToxinSacsDescription" value="Erhöht den Schaden von Nahkampfwaffen gegen Infanterieeinheiten und Monströse Kreaturen."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="Diese parasitären Drüsen sondern abscheuliche Flüssigkeiten ab, um die Klauen, Krallen und Zähne des Tyraniden mit einer giftigen Schicht zu bedecken."/>
	<entry name="Tyranids/Tunnel2" value="Echiureische Mauern"/>
	<entry name="Tyranids/Tunnel2Description" value="Erhöht die Trefferpunkte."/>
	<entry name="Tyranids/Tunnel2Flavor" value="Brutkoloniemauern wachsen schnell, bestehen jedoch aus eher zarten Organismen – nach Maßstäben der Tyraniden. Genetoren entdeckten jedoch angepasste Organismen, die langsamer wachsen, aber mit der Zeit Muskelmasse bilden, wodurch sie widerstandsfähiger gegenüber feindlichen Einwirkungen sind."/>
	<entry name="Tyranids/UnnaturalResilience" value="Unnatürliche Widerstandskraft"/>
	<entry name="Tyranids/UnnaturalResilienceDescription" value="Erhöht die Unverwundbar-Schadensreduktion und gewährt eine erhöhte Schadensreduktion „Verletzungen ignorieren“."/>
	<entry name="Tyranids/UnnaturalResilienceFlavor" value="Der Schwarmbote kann Verletzungen wegstecken, die andere Kreaturen außer Gefecht setzen würden, indem er sich einfach an die neuen Gegebenheiten anpasst. So kann er weiterhin seinen Sinn und Zweck erfüllen, der ihm von der Schwarmkönigin auferlegt wurde."/>
	<entry name="Tyranids/VehiclesUpkeep" value="Megant-Instinkt"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Verringert die laufenden Biomassekosten."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="Das Schwarmbewusstsein gibt seinem Schwarm (mit Ausnahme seiner Alphakreaturen) für gewöhnlich keinen Entscheidungsspielraum und hält – abgesehen von unbewussten Verhaltensweisen der Kreaturen – alle Fäden in der Hand. Bisweilen lässt es den Griff jedoch etwas lockerer, damit größere Organismen kleinere Organismen lenken oder gar jagen können, um ohne direkte Kontrolle zu überleben."/>
	<entry name="Tyranids/WarpField" value="Warpfeld"/>
	<entry name="Tyranids/WarpFieldDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="Tyranids/WarpFieldFlavor" value="Zoantrophen sind wichtige Knotenpunkte für die psionische Macht des Schwarmbewusstseins und werden daher mit einem stark ausgeprägten Selbsterhaltungstrieb geschaffen. Deshalb projizieren sie zu ihrem eigenen Schutz in der Schlacht instinktiv ein starkes Warpfeld. Dieser geistige Schild schimmert nur dort auf, wo sowohl das Feuer leichter als auch schwerer Waffen gleichermaßen harmlos gegen ihn prasselt. Ansonsten ist er unsichtbar."/>
	<entry name="TwinLinked" value="Synchronisiert"/>
	<entry name="TwinLinkedDescription" value="Erhöht die Genauigkeit."/>
	<entry name="TwinLinkedFlavor" value="Diese Waffen sind an dasselbe Zielerfassungssystem gekoppelt, wodurch sich ihre Genauigkeit erhöht."/>
	<entry name="Uncommon" value="Ungewöhnlich"/>
	<entry name="UncommonDescription" value="Klassifizierung."/>
	<entry name="UnendingHorde" value="Endlose Horde"/>
	<entry name="UnendingHordeDescription" value="Erhöht die Schadensreduktion."/>
	<entry name="UnendingHordeFlavor" value="Wie Zombies aus alten Geschichten von Terra lassen sich Seuchenwandler nur dadurch außer Gefecht setzen, dass man von ihnen so gut wie nichts übrig lässt. Ihr lebloses Fleisch spürt keinen Schmerz, sodass Angriffe, die einen normalen Menschen kampfunfähig machen würden, von einem Seuchenwandler allenfalls als lästig empfunden werden, während er einfach weiter kichert und stöhnt…"/>
	<entry name="Unique" value="Einzigartig"/>
	<entry name="UniqueDescription" value="Die Anzahl der Einheiten dieses Typs ist begrenzt."/>
	<entry name="Unwieldy" value="Unhandlich"/>
	<entry name="UnwieldyDescription" value="Verringert die Genauigkeit."/>
	<entry name="UnwieldyFlavor" value="Diese Waffe ist sehr groß und sperrig, weswegen schnelle Angriffe unmöglich sind."/>
	<entry name="VectoredAfterburners" value="Schubvektorsteuerung"/>
	<entry name="VectoredAfterburnersDescription" value="Erhöht die Bewegung und die Fernkampf-Schadensreduktion."/>
	<entry name="Vehicle" value="Fahrzeug"/>
	<entry name="VehicleDescription" value="Hebt den Malus für schwere Waffen und Geschütze auf. Erhöht den Bewegungsmalus in Wäldern und imperialen Ruinen."/>
	<entry name="VehicleFlavor" value="Ein Krieg wird nicht nur von Kämpfern entschieden, sondern auch von mächtigen Kriegsmaschinen und Panzern."/>
	<entry name="VeryBulky" value="Sehr massig"/>
	<entry name="VeryBulkyDescription" value="Die Einheit benötigt in Transportern zwei zusätzliche Transportfelder."/>
	<entry name="VeryBulkyFlavor" value="<string name='Traits/BulkyFlavor'/>"/>
	<entry name="VoidShield" value="Deflektorschild"/>
	<entry name="VoidShieldDescription" value="Erhöht die Fernkampf-Schadensreduktion."/>
	<entry name="VoidShieldFlavor" value="<string name='Actions/AstraMilitarum/ProjectedVoidShieldFlavor'/>"/>
	<entry name="VoxCaster" value="Vox-Transmitter"/>
	<entry name="VoxCasterDescription" value="Verringert den Moralverlust."/>
	<entry name="VoxCasterFlavor" value="Der Vox-Transmitter ist ein zuverlässiges Kommunikationssystem, das über Mikrostrahlentransmitter mit dem taktischen Befehlsnetzwerk verbunden ist."/>
	<entry name="Waaagh" value="Waaagh!"/>
	<entry name="WaaaghDescription" value="Erhöht die Anzahl der Angriffe."/>
	<entry name="WaaaghFlavor" value="Der Waaagh vereint pure Lebensfreude, Kreuzfahrertum, eine immense psionische Kraft, eine konkret fühlbare Aura des Glaubens und vielleicht die Orkgötter selbst in einem zähnefletschenden, gedärmezerreißenden Akt aggressiver Wonne, der die Orks auf ihren Wegen quer durch die Galaxie zu den Schlachtfeldern antreibt. Der Waaagh ist die Essenz orkischen Seins."/>
	<entry name="Walker" value="Läufer"/>
	<entry name="WalkerDescription" value="<string name='Traits/DozerBladeDescription'/>"/>
	<entry name="Weakened" value="Geschwächt"/>
	<entry name="WeakenedDescription" value="Verringert die Bewegung und den Schaden."/>
	<entry name="WireWeed" value="<string name='Features/WireWeed'/>"/>
	<entry name="WireWeedDescription" value="Verursacht in jeder Runde Schaden."/>
	<entry name="WireWeedFlavor" value="<string name='Features/WireWeedFlavor'/>"/>
	<entry name="Witchfire" value="Hexenfeuer"/>
	<entry name="WitchfireDescription" value="Klassifizierung."/>
	<entry name="Zzap" value="Zzap"/>
	<entry name="ZzapFlavor" value="Zzapwummen feuern instabile Blitze ab und sind dazu in der Lage, mit einem knisternden Funkenregen selbst die dickste Panzerung von feindlichen Fahrzeugen zu durchbrechen. Allerdings neigen sie zu Überhitzung und können den Schützen mit einem Stromschlag töten."/>
	<entry name="Zealot" value="Zelot"/>
	<entry name="ZealotDescription" value="Verringert den Moralverlust, erhöht den Nahkampfschaden und sorgt für Immunität gegen Angst und Niederhalten."/>
	<entry name="ZealotFlavor" value="Zeloten kämpfen mit der Kraft ihrer Überzeugung ohne Rücksicht auf Verluste in ihren eigenen Reihen weiter – ganz gleich, wie groß der Schrecken des Krieges auch sein mag."/>
</language>
