<?xml version="1.0" encoding="utf-8"?>
<menu:patchNotesHUD extends="HUD" layout="Flow" layout.alignment="MiddleCenter" layout.gap="0 0" layout.direction="TopToBottom" preferredSize="1240 720" showEffect="FadeInRight" hideEffect="FadeOutRight">
	<label caption="<style name='ShadowedMenuTitle'/><string name='GUI/PatchNotes'/>"/>
	<image texture="GUI/HeadlineBar" color="GUI/HeadlineBar"/>
	<component preferredSize="FillParent 30"/>
	<container layout.alignment="TopCenter" layout.gap="20 20" preferredSize="FillParent 540">
		<list name="patchNotesList" surface.texture="GUI/ShadowedSurface" minItemHeight="32" preferredSize="282 WrapContent" maxSize="0 540" weights="1 FillAll"/>
		<scrollableContainer name="patchNotesInfoContainer" surface.texture="GUI/ShadowedSurface" preferredSize="600 WrapContent" maxSize="0 540" weights="1 FillAll">
			<label name="patchNotesInfoLabel" preferredSize="FillParent WrapContent"/>
		</scrollableContainer>
	</container>
	<component preferredSize="FillParent 30"/>
	<container layout.alignment="TopCenter" layout.gap="40 40" preferredSize="FillParent WrapContent">
		<navigationOKButton/>
	</container>
</menu:patchNotesHUD>
