<?xml version="1.0" encoding="utf-8"?>
<language>

	<!-- Buildings -->
	<entry name="AdeptusMechanicus/Aircraft" value="<string name='Buildings/AdeptusMechanicus/Aircraft'/>"/>
	<entry name="AdeptusMechanicus/AircraftDescription" value="<string name='Buildings/AdeptusMechanicus/AircraftDescription'/>"/>
	<entry name="AdeptusMechanicus/AircraftFlavor" value="<string name='Buildings/AdeptusMechanicus/AircraftFlavor'/>"/>
	<entry name="AdeptusMechanicus/Construction" value="<string name='Buildings/AdeptusMechanicus/Construction'/>"/>
	<entry name="AdeptusMechanicus/ConstructionDescription" value="<string name='Buildings/AdeptusMechanicus/ConstructionDescription'/>"/>
	<entry name="AdeptusMechanicus/ConstructionFlavor" value="<string name='Buildings/AdeptusMechanicus/ConstructionFlavor'/>"/>
	<entry name="AdeptusMechanicus/Heroes" value="<string name='Buildings/AdeptusMechanicus/Heroes'/>"/>
	<entry name="AdeptusMechanicus/HeroesDescription" value="<string name='Buildings/AdeptusMechanicus/HeroesDescription'/>"/>
	<entry name="AdeptusMechanicus/HeroesFlavor" value="<string name='Buildings/AdeptusMechanicus/HeroesFlavor'/>"/>
	<entry name="AdeptusMechanicus/Housing" value="<string name='Buildings/AdeptusMechanicus/Housing'/>"/>
	<entry name="AdeptusMechanicus/HousingDescription" value="<string name='Buildings/AdeptusMechanicus/HousingDescription'/>"/>
	<entry name="AdeptusMechanicus/HousingFlavor" value="<string name='Buildings/AdeptusMechanicus/HousingFlavor'/>"/>
	<entry name="AdeptusMechanicus/Loyalty" value="<string name='Buildings/AdeptusMechanicus/Loyalty'/>"/>
	<entry name="AdeptusMechanicus/LoyaltyDescription" value="<string name='Buildings/AdeptusMechanicus/LoyaltyDescription'/>"/>
	<entry name="AdeptusMechanicus/LoyaltyFlavor" value="<string name='Buildings/AdeptusMechanicus/LoyaltyFlavor'/>"/>
	<entry name="AdeptusMechanicus/Vehicles" value="<string name='Buildings/AdeptusMechanicus/Vehicles'/>"/>
	<entry name="AdeptusMechanicus/VehiclesDescription" value="<string name='Buildings/AdeptusMechanicus/VehiclesDescription'/>"/>
	<entry name="AdeptusMechanicus/VehiclesFlavor" value="<string name='Buildings/AdeptusMechanicus/VehiclesFlavor'/>"/>
	<entry name="AstraMilitarum/Aircraft" value="<string name='Buildings/AstraMilitarum/Aircraft'/>"/>
	<entry name="AstraMilitarum/AircraftDescription" value="<string name='Buildings/AstraMilitarum/AircraftDescription'/>"/>
	<entry name="AstraMilitarum/AircraftFlavor" value="<string name='Buildings/AstraMilitarum/AircraftFlavor'/>"/>
	<entry name="AstraMilitarum/Construction" value="<string name='Buildings/AstraMilitarum/Construction'/>"/>
	<entry name="AstraMilitarum/ConstructionDescription" value="<string name='Buildings/AstraMilitarum/ConstructionDescription'/>"/>
	<entry name="AstraMilitarum/ConstructionFlavor" value="<string name='Buildings/AstraMilitarum/ConstructionFlavor'/>"/>
	<entry name="AstraMilitarum/Heroes" value="<string name='Buildings/AstraMilitarum/Heroes'/>"/>
	<entry name="AstraMilitarum/HeroesDescription" value="<string name='Buildings/AstraMilitarum/HeroesDescription'/>"/>
	<entry name="AstraMilitarum/HeroesFlavor" value="<string name='Buildings/AstraMilitarum/HeroesFlavor'/>"/>
	<entry name="AstraMilitarum/Housing" value="<string name='Buildings/AstraMilitarum/Housing'/>"/>
	<entry name="AstraMilitarum/HousingDescription" value="<string name='Buildings/AstraMilitarum/HousingDescription'/>"/>
	<entry name="AstraMilitarum/HousingFlavor" value="<string name='Buildings/AstraMilitarum/HousingFlavor'/>"/>
	<entry name="AstraMilitarum/Loyalty" value="<string name='Buildings/AstraMilitarum/Loyalty'/>"/>
	<entry name="AstraMilitarum/LoyaltyDescription" value="<string name='Buildings/AstraMilitarum/LoyaltyDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyFlavor" value="<string name='Buildings/AstraMilitarum/LoyaltyFlavor'/>"/>
	<entry name="AstraMilitarum/Psykers" value="<string name='Buildings/AstraMilitarum/Psykers'/>"/>
	<entry name="AstraMilitarum/PsykersDescription" value="<string name='Buildings/AstraMilitarum/PsykersDescription'/>"/>
	<entry name="AstraMilitarum/PsykersFlavor" value="<string name='Buildings/AstraMilitarum/PsykersFlavor'/>"/>
	<entry name="AstraMilitarum/Upgrades" value="<string name='Buildings/AstraMilitarum/Upgrades'/>"/>
	<entry name="AstraMilitarum/UpgradesDescription" value="<string name='Buildings/AstraMilitarum/UpgradesDescription'/>"/>
	<entry name="AstraMilitarum/UpgradesFlavor" value="<string name='Buildings/AstraMilitarum/UpgradesFlavor'/>"/>
	<entry name="AstraMilitarum/Vehicles" value="<string name='Buildings/AstraMilitarum/Vehicles'/>"/>
	<entry name="AstraMilitarum/VehiclesDescription" value="<string name='Buildings/AstraMilitarum/VehiclesDescription'/>"/>
	<entry name="AstraMilitarum/VehiclesFlavor" value="<string name='Buildings/AstraMilitarum/VehiclesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Aircraft" value="<string name='Buildings/ChaosSpaceMarines/Aircraft'/>"/>
	<entry name="ChaosSpaceMarines/AircraftDescription" value="<string name='Buildings/ChaosSpaceMarines/AircraftDescription'/>"/>
	<entry name="ChaosSpaceMarines/AircraftFlavor" value="<string name='Buildings/ChaosSpaceMarines/AircraftFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Construction" value="<string name='Buildings/ChaosSpaceMarines/Construction'/>"/>
	<entry name="ChaosSpaceMarines/ConstructionDescription" value="<string name='Buildings/ChaosSpaceMarines/ConstructionDescription'/>"/>
	<entry name="ChaosSpaceMarines/ConstructionFlavor" value="<string name='Buildings/ChaosSpaceMarines/ConstructionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Heroes" value="<string name='Buildings/ChaosSpaceMarines/Heroes'/>"/>
	<entry name="ChaosSpaceMarines/HeroesDescription" value="<string name='Buildings/ChaosSpaceMarines/HeroesDescription'/>"/>
	<entry name="ChaosSpaceMarines/HeroesFlavor" value="<string name='Buildings/ChaosSpaceMarines/HeroesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Housing" value="<string name='Buildings/ChaosSpaceMarines/Housing'/>"/>
	<entry name="ChaosSpaceMarines/HousingDescription" value="<string name='Buildings/ChaosSpaceMarines/HousingDescription'/>"/>
	<entry name="ChaosSpaceMarines/HousingFlavor" value="<string name='Buildings/ChaosSpaceMarines/HousingFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Infantry" value="<string name='Buildings/ChaosSpaceMarines/Infantry'/>"/>
	<entry name="ChaosSpaceMarines/InfantryDescription" value="<string name='Buildings/ChaosSpaceMarines/InfantryDescription'/>"/>
	<entry name="ChaosSpaceMarines/InfantryFlavor" value="<string name='Buildings/ChaosSpaceMarines/InfantryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Loyalty" value="<string name='Buildings/ChaosSpaceMarines/Loyalty'/>"/>
	<entry name="ChaosSpaceMarines/LoyaltyDescription" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyDescription'/>"/>
	<entry name="ChaosSpaceMarines/LoyaltyFlavor" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Vehicles" value="<string name='Buildings/ChaosSpaceMarines/Vehicles'/>"/>
	<entry name="ChaosSpaceMarines/VehiclesDescription" value="<string name='Buildings/ChaosSpaceMarines/VehiclesDescription'/>"/>
	<entry name="ChaosSpaceMarines/VehiclesFlavor" value="<string name='Buildings/ChaosSpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Drukhari/Aircraft" value="<string name='Buildings/Drukhari/Aircraft'/>"/>
	<entry name="Drukhari/AircraftDescription" value="<string name='Buildings/Drukhari/AircraftDescription'/>"/>
	<entry name="Drukhari/AircraftFlavor" value="<string name='Buildings/Drukhari/AircraftFlavor'/>"/>
	<entry name="Drukhari/Construction" value="<string name='Buildings/Drukhari/Construction'/>"/>
	<entry name="Drukhari/ConstructionDescription" value="<string name='Buildings/Drukhari/ConstructionDescription'/>"/>
	<entry name="Drukhari/ConstructionFlavor" value="<string name='Buildings/Drukhari/ConstructionFlavor'/>"/>
	<entry name="Drukhari/Heroes" value="<string name='Buildings/Drukhari/Heroes'/>"/>
	<entry name="Drukhari/HeroesDescription" value="<string name='Buildings/Drukhari/HeroesDescription'/>"/>
	<entry name="Drukhari/HeroesFlavor" value="<string name='Buildings/Drukhari/HeroesFlavor'/>"/>
	<entry name="Drukhari/Housing" value="<string name='Buildings/Drukhari/Housing'/>"/>
	<entry name="Drukhari/HousingDescription" value="<string name='Buildings/Drukhari/HousingDescription'/>"/>
	<entry name="Drukhari/HousingFlavor" value="<string name='Buildings/Drukhari/HousingFlavor'/>"/>
	<entry name="Drukhari/Loyalty" value="<string name='Buildings/Drukhari/Loyalty'/>"/>
	<entry name="Drukhari/LoyaltyDescription" value="<string name='Buildings/Drukhari/LoyaltyDescription'/>"/>
	<entry name="Drukhari/LoyaltyFlavor" value="<string name='Buildings/Drukhari/LoyaltyFlavor'/>"/>
	<entry name="Drukhari/Vehicles" value="<string name='Buildings/Drukhari/Vehicles'/>"/>
	<entry name="Drukhari/VehiclesDescription" value="<string name='Buildings/Drukhari/VehiclesDescription'/>"/>
	<entry name="Drukhari/VehiclesFlavor" value="<string name='Buildings/Drukhari/VehiclesFlavor'/>"/>
	<entry name="Eldar/Aircraft" value="<string name='Buildings/Eldar/Aircraft'/>"/>
	<entry name="Eldar/AircraftDescription" value="<string name='Buildings/Eldar/AircraftDescription'/>"/>
	<entry name="Eldar/AircraftFlavor" value="<string name='Buildings/Eldar/AircraftFlavor'/>"/>
	<entry name="Eldar/Construction" value="<string name='Buildings/Eldar/Construction'/>"/>
	<entry name="Eldar/ConstructionDescription" value="<string name='Buildings/Eldar/ConstructionDescription'/>"/>
	<entry name="Eldar/ConstructionFlavor" value="<string name='Buildings/Eldar/ConstructionFlavor'/>"/>
	<entry name="Eldar/Heroes" value="<string name='Buildings/Eldar/Heroes'/>"/>
	<entry name="Eldar/HeroesDescription" value="<string name='Buildings/Eldar/HeroesDescription'/>"/>
	<entry name="Eldar/HeroesFlavor" value="<string name='Buildings/Eldar/HeroesFlavor'/>"/>
	<entry name="Eldar/Housing" value="<string name='Buildings/Eldar/Housing'/>"/>
	<entry name="Eldar/HousingDescription" value="<string name='Buildings/Eldar/HousingDescription'/>"/>
	<entry name="Eldar/HousingFlavor" value="<string name='Buildings/Eldar/HousingFlavor'/>"/>
	<entry name="Eldar/Infantry" value="<string name='Buildings/Eldar/Infantry'/>"/>
	<entry name="Eldar/InfantryDescription" value="<string name='Buildings/Eldar/InfantryDescription'/>"/>
	<entry name="Eldar/InfantryFlavor" value="<string name='Buildings/Eldar/InfantryFlavor'/>"/>
	<entry name="Eldar/Loyalty" value="<string name='Buildings/Eldar/Loyalty'/>"/>
	<entry name="Eldar/LoyaltyDescription" value="<string name='Buildings/Eldar/LoyaltyDescription'/>"/>
	<entry name="Eldar/LoyaltyFlavor" value="<string name='Buildings/Eldar/LoyaltyFlavor'/>"/>
	<entry name="Eldar/Vehicles" value="<string name='Buildings/Eldar/Vehicles'/>"/>
	<entry name="Eldar/VehiclesDescription" value="<string name='Buildings/Eldar/VehiclesDescription'/>"/>
	<entry name="Eldar/VehiclesFlavor" value="<string name='Buildings/Eldar/VehiclesFlavor'/>"/>
	<entry name="Necrons/Aircraft" value="<string name='Buildings/Necrons/Aircraft'/>"/>
	<entry name="Necrons/AircraftDescription" value="<string name='Buildings/Necrons/AircraftDescription'/>"/>
	<entry name="Necrons/AircraftFlavor" value="<string name='Buildings/Necrons/AircraftFlavor'/>"/>
	<entry name="Necrons/Construction" value="<string name='Buildings/Necrons/Construction'/>"/>
	<entry name="Necrons/ConstructionDescription" value="<string name='Buildings/Necrons/ConstructionDescription'/>"/>
	<entry name="Necrons/ConstructionFlavor" value="<string name='Buildings/Necrons/ConstructionFlavor'/>"/>
	<entry name="Necrons/Heroes" value="<string name='Buildings/Necrons/Heroes'/>"/>
	<entry name="Necrons/HeroesDescription" value="<string name='Buildings/Necrons/HeroesDescription'/>"/>
	<entry name="Necrons/HeroesFlavor" value="<string name='Buildings/Necrons/HeroesFlavor'/>"/>
	<entry name="Necrons/Housing" value="<string name='Buildings/Necrons/Housing'/>"/>
	<entry name="Necrons/HousingDescription" value="<string name='Buildings/Necrons/HousingDescription'/>"/>
	<entry name="Necrons/HousingFlavor" value="<string name='Buildings/Necrons/HousingFlavor'/>"/>
	<entry name="Necrons/Loyalty" value="<string name='Buildings/Necrons/Loyalty'/>"/>
	<entry name="Necrons/LoyaltyDescription" value="<string name='Buildings/Necrons/LoyaltyDescription'/>"/>
	<entry name="Necrons/LoyaltyFlavor" value="<string name='Buildings/Necrons/LoyaltyFlavor'/>"/>
	<entry name="Necrons/Vehicles" value="<string name='Buildings/Necrons/Vehicles'/>"/>
	<entry name="Necrons/VehiclesDescription" value="<string name='Buildings/Necrons/VehiclesDescription'/>"/>
	<entry name="Necrons/VehiclesFlavor" value="<string name='Buildings/Necrons/VehiclesFlavor'/>"/>
	<entry name="Orks/Beasts" value="<string name='Buildings/Orks/Beasts'/>"/>
	<entry name="Orks/BeastsDescription" value="<string name='Buildings/Orks/BeastsDescription'/>"/>
	<entry name="Orks/BeastsFlavor" value="<string name='Buildings/Orks/BeastsFlavor'/>"/>
	<entry name="Orks/Colonizers" value="<string name='Buildings/Orks/Colonizers'/>"/>
	<entry name="Orks/ColonizersDescription" value="<string name='Buildings/Orks/ColonizersDescription'/>"/>
	<entry name="Orks/ColonizersFlavor" value="<string name='Buildings/Orks/ColonizersFlavor'/>"/>
	<entry name="Orks/Construction" value="<string name='Buildings/Orks/Construction'/>"/>
	<entry name="Orks/ConstructionDescription" value="<string name='Buildings/Orks/ConstructionDescription'/>"/>
	<entry name="Orks/ConstructionFlavor" value="<string name='Buildings/Orks/ConstructionFlavor'/>"/>
	<entry name="Orks/Heroes" value="<string name='Buildings/Orks/Heroes'/>"/>
	<entry name="Orks/HeroesDescription" value="<string name='Buildings/Orks/HeroesDescription'/>"/>
	<entry name="Orks/HeroesFlavor" value="<string name='Buildings/Orks/HeroesFlavor'/>"/>
	<entry name="Orks/Housing" value="<string name='Buildings/Orks/Housing'/>"/>
	<entry name="Orks/HousingDescription" value="<string name='Buildings/Orks/HousingDescription'/>"/>
	<entry name="Orks/HousingFlavor" value="<string name='Buildings/Orks/HousingFlavor'/>"/>
	<entry name="Orks/Loyalty" value="<string name='Buildings/Orks/Loyalty'/>"/>
	<entry name="Orks/LoyaltyDescription" value="<string name='Buildings/Orks/LoyaltyDescription'/>"/>
	<entry name="Orks/LoyaltyFlavor" value="<string name='Buildings/Orks/LoyaltyFlavor'/>"/>
	<entry name="Orks/Vehicles" value="<string name='Buildings/Orks/Vehicles'/>"/>
	<entry name="Orks/VehiclesDescription" value="<string name='Buildings/Orks/VehiclesDescription'/>"/>
	<entry name="Orks/VehiclesFlavor" value="<string name='Buildings/Orks/VehiclesFlavor'/>"/>
	<entry name="SistersOfBattle/Auxiliaries" value="<string name='Buildings/SistersOfBattle/Auxiliaries'/>"/>
	<entry name="SistersOfBattle/AuxiliariesDescription" value="<string name='Buildings/SistersOfBattle/AuxiliariesDescription'/>"/>
	<entry name="SistersOfBattle/AuxiliariesFlavor" value="<string name='Buildings/SistersOfBattle/AuxiliariesFlavor'/>"/>
	<entry name="SistersOfBattle/Construction" value="<string name='Buildings/SistersOfBattle/Construction'/>"/>
	<entry name="SistersOfBattle/ConstructionDescription" value="<string name='Buildings/SistersOfBattle/ConstructionDescription'/>"/>
	<entry name="SistersOfBattle/ConstructionFlavor" value="<string name='Buildings/SistersOfBattle/ConstructionFlavor'/>"/>
	<entry name="SistersOfBattle/Heroes" value="<string name='Buildings/SistersOfBattle/Heroes'/>"/>
	<entry name="SistersOfBattle/HeroesDescription" value="<string name='Buildings/SistersOfBattle/HeroesDescription'/>"/>
	<entry name="SistersOfBattle/HeroesFlavor" value="<string name='Buildings/SistersOfBattle/HeroesFlavor'/>"/>
	<entry name="SistersOfBattle/Housing" value="<string name='Buildings/SistersOfBattle/Housing'/>"/>
	<entry name="SistersOfBattle/HousingDescription" value="<string name='Buildings/SistersOfBattle/HousingDescription'/>"/>
	<entry name="SistersOfBattle/HousingFlavor" value="<string name='Buildings/SistersOfBattle/HousingFlavor'/>"/>
	<entry name="SistersOfBattle/Loyalty" value="<string name='Buildings/SistersOfBattle/Loyalty'/>"/>
	<entry name="SistersOfBattle/LoyaltyDescription" value="<string name='Buildings/SistersOfBattle/LoyaltyDescription'/>"/>
	<entry name="SistersOfBattle/LoyaltyFlavor" value="<string name='Buildings/SistersOfBattle/LoyaltyFlavor'/>"/>
	<entry name="SistersOfBattle/Vehicles" value="<string name='Buildings/SistersOfBattle/Vehicles'/>"/>
	<entry name="SistersOfBattle/VehiclesDescription" value="<string name='Buildings/SistersOfBattle/VehiclesDescription'/>"/>
	<entry name="SistersOfBattle/VehiclesFlavor" value="<string name='Buildings/SistersOfBattle/VehiclesFlavor'/>"/>
	<entry name="SpaceMarines/Aircraft" value="<string name='Buildings/SpaceMarines/Aircraft'/>"/>
	<entry name="SpaceMarines/AircraftDescription" value="<string name='Buildings/SpaceMarines/AircraftDescription'/>"/>
	<entry name="SpaceMarines/AircraftFlavor" value="<string name='Buildings/SpaceMarines/AircraftFlavor'/>"/>
	<entry name="SpaceMarines/Construction" value="<string name='Buildings/SpaceMarines/Construction'/>"/>
	<entry name="SpaceMarines/ConstructionDescription" value="<string name='Buildings/SpaceMarines/ConstructionDescription'/>"/>
	<entry name="SpaceMarines/ConstructionFlavor" value="<string name='Buildings/SpaceMarines/ConstructionFlavor'/>"/>
	<entry name="SpaceMarines/GeneseedBunker" value="<string name='Buildings/SpaceMarines/GeneseedBunker'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerDescription" value="<string name='Buildings/SpaceMarines/GeneseedBunkerDescription'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerFlavor" value="<string name='Buildings/SpaceMarines/GeneseedBunkerFlavor'/>"/>
	<entry name="SpaceMarines/Heroes" value="<string name='Buildings/SpaceMarines/Heroes'/>"/>
	<entry name="SpaceMarines/HeroesDescription" value="<string name='Buildings/SpaceMarines/HeroesDescription'/>"/>
	<entry name="SpaceMarines/HeroesFlavor" value="<string name='Buildings/SpaceMarines/HeroesFlavor'/>"/>
	<entry name="SpaceMarines/Housing" value="<string name='Buildings/SpaceMarines/Housing'/>"/>
	<entry name="SpaceMarines/HousingDescription" value="<string name='Buildings/SpaceMarines/HousingDescription'/>"/>
	<entry name="SpaceMarines/HousingFlavor" value="<string name='Buildings/SpaceMarines/HousingFlavor'/>"/>
	<entry name="SpaceMarines/Loyalty" value="<string name='Buildings/SpaceMarines/Loyalty'/>"/>
	<entry name="SpaceMarines/LoyaltyDescription" value="<string name='Buildings/SpaceMarines/LoyaltyDescription'/>"/>
	<entry name="SpaceMarines/LoyaltyFlavor" value="<string name='Buildings/SpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="SpaceMarines/Vehicles" value="<string name='Buildings/SpaceMarines/Vehicles'/>"/>
	<entry name="SpaceMarines/VehiclesDescription" value="<string name='Buildings/SpaceMarines/VehiclesDescription'/>"/>
	<entry name="SpaceMarines/VehiclesFlavor" value="<string name='Buildings/SpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Tau/Aircraft" value="<string name='Buildings/Tau/Aircraft'/>"/>
	<entry name="Tau/AircraftDescription" value="<string name='Buildings/Tau/AircraftDescription'/>"/>
	<entry name="Tau/AircraftFlavor" value="<string name='Buildings/Tau/AircraftFlavor'/>"/>
	<entry name="Tau/Construction" value="<string name='Buildings/Tau/Construction'/>"/>
	<entry name="Tau/ConstructionDescription" value="<string name='Buildings/Tau/ConstructionDescription'/>"/>
	<entry name="Tau/ConstructionFlavor" value="<string name='Buildings/Tau/ConstructionFlavor'/>"/>
	<entry name="Tau/Heroes" value="<string name='Buildings/Tau/Heroes'/>"/>
	<entry name="Tau/HeroesDescription" value="<string name='Buildings/Tau/HeroesDescription'/>"/>
	<entry name="Tau/HeroesFlavor" value="<string name='Buildings/Tau/HeroesFlavor'/>"/>
	<entry name="Tau/Housing" value="<string name='Buildings/Tau/Housing'/>"/>
	<entry name="Tau/HousingDescription" value="<string name='Buildings/Tau/HousingDescription'/>"/>
	<entry name="Tau/HousingFlavor" value="<string name='Buildings/Tau/HousingFlavor'/>"/>
	<entry name="Tau/Loyalty" value="<string name='Buildings/Tau/Loyalty'/>"/>
	<entry name="Tau/LoyaltyDescription" value="<string name='Buildings/Tau/LoyaltyDescription'/>"/>
	<entry name="Tau/LoyaltyFlavor" value="<string name='Buildings/Tau/LoyaltyFlavor'/>"/>
	<entry name="Tau/MonstrousCreatures" value="<string name='Buildings/Tau/MonstrousCreatures'/>"/>
	<entry name="Tau/MonstrousCreaturesDescription" value="<string name='Buildings/Tau/MonstrousCreaturesDescription'/>"/>
	<entry name="Tau/MonstrousCreaturesFlavor" value="<string name='Buildings/Tau/MonstrousCreaturesFlavor'/>"/>
	<entry name="Tau/Vehicles" value="<string name='Buildings/Tau/Vehicles'/>"/>
	<entry name="Tau/VehiclesDescription" value="<string name='Buildings/Tau/VehiclesDescription'/>"/>
	<entry name="Tau/VehiclesFlavor" value="<string name='Buildings/Tau/VehiclesFlavor'/>"/>
	<entry name="Tyranids/Aircraft" value="<string name='Buildings/Tyranids/Aircraft'/>"/>
	<entry name="Tyranids/AircraftDescription" value="<string name='Buildings/Tyranids/AircraftDescription'/>"/>
	<entry name="Tyranids/AircraftFlavor" value="<string name='Buildings/Tyranids/AircraftFlavor'/>"/>
	<entry name="Tyranids/Construction" value="<string name='Buildings/Tyranids/Construction'/>"/>
	<entry name="Tyranids/ConstructionDescription" value="<string name='Buildings/Tyranids/ConstructionDescription'/>"/>
	<entry name="Tyranids/ConstructionFlavor" value="<string name='Buildings/Tyranids/ConstructionFlavor'/>"/>
	<entry name="Tyranids/Heroes" value="<string name='Buildings/Tyranids/Heroes'/>"/>
	<entry name="Tyranids/HeroesDescription" value="<string name='Buildings/Tyranids/HeroesDescription'/>"/>
	<entry name="Tyranids/HeroesFlavor" value="<string name='Buildings/Tyranids/HeroesFlavor'/>"/>
	<entry name="Tyranids/Housing" value="<string name='Buildings/Tyranids/Housing'/>"/>
	<entry name="Tyranids/HousingDescription" value="<string name='Buildings/Tyranids/HousingDescription'/>"/>
	<entry name="Tyranids/HousingFlavor" value="<string name='Buildings/Tyranids/HousingFlavor'/>"/>
	<entry name="Tyranids/Loyalty" value="<string name='Buildings/Tyranids/Loyalty'/>"/>
	<entry name="Tyranids/LoyaltyDescription" value="<string name='Buildings/Tyranids/LoyaltyDescription'/>"/>
	<entry name="Tyranids/LoyaltyFlavor" value="<string name='Buildings/Tyranids/LoyaltyFlavor'/>"/>
	<entry name="Tyranids/Thropes" value="<string name='Buildings/Tyranids/Thropes'/>"/>
	<entry name="Tyranids/ThropesDescription" value="<string name='Buildings/Tyranids/ThropesDescription'/>"/>
	<entry name="Tyranids/ThropesFlavor" value="<string name='Buildings/Tyranids/ThropesFlavor'/>"/>
	<entry name="Tyranids/Vehicles" value="<string name='Buildings/Tyranids/Vehicles'/>"/>
	<entry name="Tyranids/VehiclesDescription" value="<string name='Buildings/Tyranids/VehiclesDescription'/>"/>
	<entry name="Tyranids/VehiclesFlavor" value="<string name='Buildings/Tyranids/VehiclesFlavor'/>"/>
	
	<!-- Edicts -->
	<entry name="AstraMilitarum/AircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictDescription" value="<string name='Actions/AstraMilitarumAircraftProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/DefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarum/DefenseEdictDescription" value="<string name='Actions/AstraMilitarumDefenseEdictDescription'/>"/>
	<entry name="AstraMilitarum/DefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarum/EnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarum/EnergyEdictDescription" value="<string name='Actions/AstraMilitarumEnergyEdictDescription'/>"/>
	<entry name="AstraMilitarum/EnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/FoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarum/FoodEdictDescription" value="<string name='Actions/AstraMilitarumFoodEdictDescription'/>"/>
	<entry name="AstraMilitarum/FoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarum/GrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarum/GrowthEdictDescription" value="<string name='Actions/AstraMilitarumGrowthEdictDescription'/>"/>
	<entry name="AstraMilitarum/GrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictDescription" value="<string name='Actions/AstraMilitarumInfantryProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictDescription" value="<string name='Actions/AstraMilitarumLoyaltyEdictDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/OreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarum/OreEdictDescription" value="<string name='Actions/AstraMilitarumOreEdictDescription'/>"/>
	<entry name="AstraMilitarum/OreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictDescription" value="<string name='Actions/AstraMilitarumPsykerProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/ResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarum/ResearchEdictDescription" value="<string name='Actions/AstraMilitarumResearchEdictDescription'/>"/>
	<entry name="AstraMilitarum/ResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictDescription" value="<string name='Actions/AstraMilitarumVehicleProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>
		
	<!-- Units -->
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptor'/>"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorDescription" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorDescription'/>"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorFlavor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorFlavor'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriest'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestDescription" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestDescription'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestFlavor" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestFlavor'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobot" value="<string name='Units/Neutral/KastelanRobot'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobotDescription" value="<string name='Units/Neutral/KastelanRobotDescription'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobotFlavor" value="<string name='Units/Neutral/KastelanRobotFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacher" value="<string name='Units/AdeptusMechanicus/KataphronBreacher'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherDescription" value="<string name='Units/AdeptusMechanicus/KataphronBreacherDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherFlavor" value="<string name='Units/AdeptusMechanicus/KataphronBreacherFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer" value="<string name='Units/AdeptusMechanicus/KataphronDestroyer'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerDescription" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerFlavor" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerFlavor'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusader" value="<string name='Units/AdeptusMechanicus/KnightCrusader'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusaderDescription" value="<string name='Units/AdeptusMechanicus/KnightCrusaderDescription'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusaderFlavor" value="<string name='Units/AdeptusMechanicus/KnightCrusaderFlavor'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawler'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerDescription" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerDescription'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerFlavor" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerFlavor'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizor'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorDescription" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorDescription'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorFlavor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhound'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundDescription" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundDescription'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundFlavor" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator" value="<string name='Units/AdeptusMechanicus/SicarianInfiltrator'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorDescription" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorDescription'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorFlavor" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker" value="<string name='Units/AdeptusMechanicus/SicarianRuststalker'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerDescription" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerDescription'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerFlavor" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal" value="<string name='Units/AdeptusMechanicus/SkitariiMarshal'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalDescription" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="<string name='Units/AdeptusMechanicus/SkitariiRanger'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerDescription" value="<string name='Units/AdeptusMechanicus/SkitariiRangerDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiRangerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegrator'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorDescription'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDunerider" value="<string name='Units/AdeptusMechanicus/SkorpiusDunerider'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderDescription'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderFlavor'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoon" value="<string name='Units/AdeptusMechanicus/SydonianDragoon'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoonDescription" value="<string name='Units/AdeptusMechanicus/SydonianDragoonDescription'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoonFlavor" value="<string name='Units/AdeptusMechanicus/SydonianDragoonFlavor'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominus" value="<string name='Units/AdeptusMechanicus/TechPriestDominus'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestDominusDescription'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestDominusFlavor'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus" value="<string name='Units/AdeptusMechanicus/TechPriestManipulus'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusDescription'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusFlavor'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRider" value="<string name='Units/AstraMilitarum/AttilanRoughRider'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderDescription" value="<string name='Units/AstraMilitarum/AttilanRoughRiderDescription'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderFlavor" value="<string name='Units/AstraMilitarum/AttilanRoughRiderFlavor'/>"/>
	<entry name="AstraMilitarum/Baneblade" value="<string name='Units/AstraMilitarum/Baneblade'/>"/>
	<entry name="AstraMilitarum/BanebladeDescription" value="<string name='Units/AstraMilitarum/BanebladeDescription'/>"/>
	<entry name="AstraMilitarum/BanebladeFlavor" value="<string name='Units/AstraMilitarum/BanebladeFlavor'/>"/>
	<entry name="AstraMilitarum/Basilisk" value="<string name='Units/AstraMilitarum/Basilisk'/>"/>
	<entry name="AstraMilitarum/BasiliskDescription" value="<string name='Units/AstraMilitarum/BasiliskDescription'/>"/>
	<entry name="AstraMilitarum/BasiliskFlavor" value="<string name='Units/AstraMilitarum/BasiliskFlavor'/>"/>
	<entry name="AstraMilitarum/Bullgryn" value="<string name='Units/AstraMilitarum/Bullgryn'/>"/>
	<entry name="AstraMilitarum/BullgrynDescription" value="<string name='Units/AstraMilitarum/BullgrynDescription'/>"/>
	<entry name="AstraMilitarum/BullgrynFlavor" value="<string name='Units/AstraMilitarum/BullgrynFlavor'/>"/>
	<entry name="AstraMilitarum/Chimera" value="<string name='Units/AstraMilitarum/Chimera'/>"/>
	<entry name="AstraMilitarum/ChimeraDescription" value="<string name='Units/AstraMilitarum/ChimeraDescription'/>"/>
	<entry name="AstraMilitarum/ChimeraFlavor" value="<string name='Units/AstraMilitarum/ChimeraFlavor'/>"/>
	<entry name="AstraMilitarum/DevilDog" value="<string name='Units/AstraMilitarum/DevilDog'/>"/>
	<entry name="AstraMilitarum/DevilDogDescription" value="<string name='Units/AstraMilitarum/DevilDogDescription'/>"/>
	<entry name="AstraMilitarum/DevilDogFlavor" value="<string name='Units/AstraMilitarum/DevilDogFlavor'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery" value="<string name='Units/AstraMilitarum/FieldOrdnanceBattery'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryDescription" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryDescription'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryFlavor" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryFlavor'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquad'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadDescription" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadDescription'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadFlavor" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadFlavor'/>"/>
	<entry name="AstraMilitarum/Hydra" value="<string name='Units/AstraMilitarum/Hydra'/>"/>
	<entry name="AstraMilitarum/HydraDescription" value="<string name='Units/AstraMilitarum/HydraDescription'/>"/>
	<entry name="AstraMilitarum/HydraFlavor" value="<string name='Units/AstraMilitarum/HydraFlavor'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTank" value="<string name='Units/AstraMilitarum/LemanRussBattleTank'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankDescription" value="<string name='Units/AstraMilitarum/LemanRussBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankFlavor" value="<string name='Units/AstraMilitarum/LemanRussBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/LordCommissar" value="<string name='Units/AstraMilitarum/LordCommissar'/>"/>
	<entry name="AstraMilitarum/LordCommissarDescription" value="<string name='Units/AstraMilitarum/LordCommissarDescription'/>"/>
	<entry name="AstraMilitarum/LordCommissarFlavor" value="<string name='Units/AstraMilitarum/LordCommissarFlavor'/>"/>
	<entry name="AstraMilitarum/MarauderBomber" value="<string name='Units/AstraMilitarum/MarauderBomber'/>"/>
	<entry name="AstraMilitarum/MarauderBomberDescription" value="<string name='Units/AstraMilitarum/MarauderBomberDescription'/>"/>
	<entry name="AstraMilitarum/MarauderBomberFlavor" value="<string name='Units/AstraMilitarum/MarauderBomberFlavor'/>"/>
	<entry name="AstraMilitarum/PrimarisPsyker" value="<string name='Units/AstraMilitarum/PrimarisPsyker'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerDescription" value="<string name='Units/AstraMilitarum/PrimarisPsykerDescription'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerFlavor" value="<string name='Units/AstraMilitarum/PrimarisPsykerFlavor'/>"/>
	<entry name="AstraMilitarum/Ratling" value="<string name='Units/AstraMilitarum/Ratling'/>"/>
	<entry name="AstraMilitarum/RatlingDescription" value="<string name='Units/AstraMilitarum/RatlingDescription'/>"/>
	<entry name="AstraMilitarum/RatlingFlavor" value="<string name='Units/AstraMilitarum/RatlingFlavor'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTank" value="<string name='Units/AstraMilitarum/RogalDornBattleTank'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTankDescription" value="<string name='Units/AstraMilitarum/RogalDornBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTankFlavor" value="<string name='Units/AstraMilitarum/RogalDornBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/ScoutSentinel" value="<string name='Units/AstraMilitarum/ScoutSentinel'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelDescription" value="<string name='Units/AstraMilitarum/ScoutSentinelDescription'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelFlavor" value="<string name='Units/AstraMilitarum/ScoutSentinelFlavor'/>"/>
	<entry name="AstraMilitarum/TankCommander" value="<string name='Units/AstraMilitarum/TankCommander'/>"/>
	<entry name="AstraMilitarum/TankCommanderDescription" value="<string name='Units/AstraMilitarum/TankCommanderDescription'/>"/>
	<entry name="AstraMilitarum/TankCommanderFlavor" value="<string name='Units/AstraMilitarum/TankCommanderFlavor'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseer" value="<string name='Units/AstraMilitarum/TechpriestEnginseer'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerDescription" value="<string name='Units/AstraMilitarum/TechpriestEnginseerDescription'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerFlavor" value="<string name='Units/AstraMilitarum/TechpriestEnginseerFlavor'/>"/>
	<entry name="AstraMilitarum/TempestusScion" value="<string name='Units/AstraMilitarum/TempestusScion'/>"/>
	<entry name="AstraMilitarum/TempestusScionDescription" value="<string name='Units/AstraMilitarum/TempestusScionDescription'/>"/>
	<entry name="AstraMilitarum/TempestusScionFlavor" value="<string name='Units/AstraMilitarum/TempestusScionFlavor'/>"/>
	<entry name="AstraMilitarum/Thunderbolt" value="<string name='Units/AstraMilitarum/Thunderbolt'/>"/>
	<entry name="AstraMilitarum/ThunderboltDescription" value="<string name='Units/AstraMilitarum/ThunderboltDescription'/>"/>
	<entry name="AstraMilitarum/ThunderboltFlavor" value="<string name='Units/AstraMilitarum/ThunderboltFlavor'/>"/>
	<entry name="AstraMilitarum/Valkyrie" value="<string name='Units/AstraMilitarum/Valkyrie'/>"/>
	<entry name="AstraMilitarum/ValkyrieDescription" value="<string name='Units/AstraMilitarum/ValkyrieDescription'/>"/>
	<entry name="AstraMilitarum/ValkyrieFlavor" value="<string name='Units/AstraMilitarum/ValkyrieFlavor'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsyker" value="<string name='Units/AstraMilitarum/WyrdvanePsyker'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerDescription" value="<string name='Units/AstraMilitarum/WyrdvanePsykerDescription'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerFlavor" value="<string name='Units/AstraMilitarum/WyrdvanePsykerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaider'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderDescription" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn" value="<string name='Units/ChaosSpaceMarines/ChaosSpawn'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnDescription" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="<string name='Units/ChaosSpaceMarines/ChaosTerminator'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorDescription" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrince" value="<string name='Units/ChaosSpaceMarines/DaemonPrince'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceDescription" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceDescription'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceFlavor" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DarkDisciple" value="<string name='Units/ChaosSpaceMarines/DarkDisciple'/>"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleDescription" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleDescription'/>"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleFlavor" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Defiler" value="<string name='Units/ChaosSpaceMarines/Defiler'/>"/>
	<entry name="ChaosSpaceMarines/DefilerDescription" value="<string name='Units/ChaosSpaceMarines/DefilerDescription'/>"/>
	<entry name="ChaosSpaceMarines/DefilerFlavor" value="<string name='Units/ChaosSpaceMarines/DefilerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Havoc" value="<string name='Units/ChaosSpaceMarines/Havoc'/>"/>
	<entry name="ChaosSpaceMarines/HavocDescription" value="<string name='Units/ChaosSpaceMarines/HavocDescription'/>"/>
	<entry name="ChaosSpaceMarines/HavocFlavor" value="<string name='Units/ChaosSpaceMarines/HavocFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Helbrute" value="<string name='Units/ChaosSpaceMarines/Helbrute'/>"/>
	<entry name="ChaosSpaceMarines/HelbruteDescription" value="<string name='Units/ChaosSpaceMarines/HelbruteDescription'/>"/>
	<entry name="ChaosSpaceMarines/HelbruteFlavor" value="<string name='Units/ChaosSpaceMarines/HelbruteFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerker'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerDescription" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerDescription'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerFlavor" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="<string name='Units/ChaosSpaceMarines/Forgefiend'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendDescription" value="<string name='Units/ChaosSpaceMarines/ForgefiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendFlavor" value="<string name='Units/ChaosSpaceMarines/ForgefiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession" value="<string name='Units/ChaosSpaceMarines/MasterOfPossession'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionDescription" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionDescription'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionFlavor" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Maulerfiend" value="<string name='Units/ChaosSpaceMarines/Maulerfiend'/>"/>
	<entry name="ChaosSpaceMarines/MaulerfiendDescription" value="<string name='Units/ChaosSpaceMarines/MaulerfiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/MaulerfiendFlavor" value="<string name='Units/ChaosSpaceMarines/MaulerfiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrown" value="<string name='Units/ChaosSpaceMarines/NoctilithCrown'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrownDescription" value="Grants Chaos Cultists the ability to construct fortifications."/>
	<entry name="ChaosSpaceMarines/NoctilithCrownFlavor" value="<string name='Units/ChaosSpaceMarines/NoctilithCrownFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Obliterator" value="<string name='Units/ChaosSpaceMarines/Obliterator'/>"/>
	<entry name="ChaosSpaceMarines/ObliteratorDescription" value="<string name='Units/ChaosSpaceMarines/ObliteratorDescription'/>"/>
	<entry name="ChaosSpaceMarines/ObliteratorFlavor" value="<string name='Units/ChaosSpaceMarines/ObliteratorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarine" value="<string name='Units/ChaosSpaceMarines/RubricMarine'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineDescription" value="<string name='Units/ChaosSpaceMarines/RubricMarineDescription'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineFlavor" value="<string name='Units/ChaosSpaceMarines/RubricMarineFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Venomcrawler" value="<string name='Units/ChaosSpaceMarines/Venomcrawler'/>"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerDescription" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerDescription'/>"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerFlavor" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Warpsmith" value="<string name='Units/ChaosSpaceMarines/Warpsmith'/>"/>
	<entry name="ChaosSpaceMarines/WarpsmithDescription" value="<string name='Units/ChaosSpaceMarines/WarpsmithDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpsmithFlavor" value="<string name='Units/ChaosSpaceMarines/WarpsmithFlavor'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalon" value="<string name='Units/ChaosSpaceMarines/WarpTalon'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonDescription" value="<string name='Units/ChaosSpaceMarines/WarpTalonDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonFlavor" value="<string name='Units/ChaosSpaceMarines/WarpTalonFlavor'/>"/>
	<entry name="Drukhari/Cronos" value="<string name='Units/Drukhari/Cronos'/>"/>
	<entry name="Drukhari/CronosDescription" value="<string name='Units/Drukhari/CronosDescription'/>"/>
	<entry name="Drukhari/CronosFlavor" value="<string name='Units/Drukhari/CronosFlavor'/>"/>
	<entry name="Drukhari/Haemonculus" value="<string name='Units/Drukhari/Haemonculus'/>"/>
	<entry name="Drukhari/HaemonculusDescription" value="<string name='Units/Drukhari/HaemonculusDescription'/>"/>
	<entry name="Drukhari/HaemonculusFlavor" value="<string name='Units/Drukhari/HaemonculusFlavor'/>"/>
	<entry name="Drukhari/Hellion" value="<string name='Units/Drukhari/Hellion'/>"/>
	<entry name="Drukhari/HellionDescription" value="<string name='Units/Drukhari/HellionDescription'/>"/>
	<entry name="Drukhari/HellionFlavor" value="<string name='Units/Drukhari/HellionFlavor'/>"/>
	<entry name="Drukhari/Incubi" value="<string name='Units/Drukhari/Incubi'/>"/>
	<entry name="Drukhari/IncubiDescription" value="<string name='Units/Drukhari/IncubiDescription'/>"/>
	<entry name="Drukhari/IncubiFlavor" value="<string name='Units/Drukhari/IncubiFlavor'/>"/>
	<entry name="Drukhari/KabaliteTrueborn" value="<string name='Units/Drukhari/KabaliteTrueborn'/>"/>
	<entry name="Drukhari/KabaliteTruebornDescription" value="<string name='Units/Drukhari/KabaliteTruebornDescription'/>"/>
	<entry name="Drukhari/KabaliteTruebornFlavor" value="<string name='Units/Drukhari/KabaliteTruebornFlavor'/>"/>
	<entry name="Drukhari/Mandrake" value="<string name='Units/Drukhari/Mandrake'/>"/>
	<entry name="Drukhari/MandrakeDescription" value="<string name='Units/Drukhari/MandrakeDescription'/>"/>
	<entry name="Drukhari/MandrakeFlavor" value="<string name='Units/Drukhari/MandrakeFlavor'/>"/>
	<entry name="Drukhari/Raider" value="<string name='Units/Drukhari/Raider'/>"/>
	<entry name="Drukhari/RaiderDescription" value="<string name='Units/Drukhari/RaiderDescription'/>"/>
	<entry name="Drukhari/RaiderFlavor" value="<string name='Units/Drukhari/RaiderFlavor'/>"/>
	<entry name="Drukhari/Ravager" value="<string name='Units/Drukhari/Ravager'/>"/>
	<entry name="Drukhari/RavagerDescription" value="<string name='Units/Drukhari/RavagerDescription'/>"/>
	<entry name="Drukhari/RavagerFlavor" value="<string name='Units/Drukhari/RavagerFlavor'/>"/>
	<entry name="Drukhari/Reaver" value="<string name='Units/Drukhari/Reaver'/>"/>
	<entry name="Drukhari/ReaverDescription" value="<string name='Units/Drukhari/ReaverDescription'/>"/>
	<entry name="Drukhari/ReaverFlavor" value="<string name='Units/Drukhari/ReaverFlavor'/>"/>
	<entry name="Drukhari/Scourge" value="<string name='Units/Drukhari/Scourge'/>"/>
	<entry name="Drukhari/ScourgeDescription" value="<string name='Units/Drukhari/ScourgeDescription'/>"/>
	<entry name="Drukhari/ScourgeFlavor" value="<string name='Units/Drukhari/ScourgeFlavor'/>"/>
	<entry name="Drukhari/Succubus" value="<string name='Units/Drukhari/Succubus'/>"/>
	<entry name="Drukhari/SuccubusDescription" value="<string name='Units/Drukhari/SuccubusDescription'/>"/>
	<entry name="Drukhari/SuccubusFlavor" value="<string name='Units/Drukhari/SuccubusFlavor'/>"/>
	<entry name="Drukhari/Talos" value="<string name='Units/Drukhari/Talos'/>"/>
	<entry name="Drukhari/TalosDescription" value="<string name='Units/Drukhari/TalosDescription'/>"/>
	<entry name="Drukhari/TalosFlavor" value="<string name='Units/Drukhari/TalosFlavor'/>"/>
	<entry name="Drukhari/Tantalus" value="<string name='Units/Drukhari/Tantalus'/>"/>
	<entry name="Drukhari/TantalusDescription" value="<string name='Units/Drukhari/TantalusDescription'/>"/>
	<entry name="Drukhari/TantalusFlavor" value="<string name='Units/Drukhari/TantalusFlavor'/>"/>
	<entry name="Drukhari/VoidravenBomber" value="<string name='Units/Drukhari/VoidravenBomber'/>"/>
	<entry name="Drukhari/VoidravenBomberDescription" value="<string name='Units/Drukhari/VoidravenBomberDescription'/>"/>
	<entry name="Drukhari/VoidravenBomberFlavor" value="<string name='Units/Drukhari/VoidravenBomberFlavor'/>"/>
	<entry name="Drukhari/Wrack" value="<string name='Units/Drukhari/Wrack'/>"/>
	<entry name="Drukhari/WrackDescription" value="<string name='Units/Drukhari/WrackDescription'/>"/>
	<entry name="Drukhari/WrackFlavor" value="<string name='Units/Drukhari/WrackFlavor'/>"/>
	<entry name="Drukhari/Wyche" value="<string name='Units/Drukhari/Wyche'/>"/>
	<entry name="Drukhari/WycheDescription" value="<string name='Units/Drukhari/WycheDescription'/>"/>
	<entry name="Drukhari/WycheFlavor" value="<string name='Units/Drukhari/WycheFlavor'/>"/>
	<entry name="Eldar/AvatarOfKhaine" value="<string name='Units/Eldar/AvatarOfKhaine'/>"/>
	<entry name="Eldar/AvatarOfKhaineDescription" value="<string name='Units/Eldar/AvatarOfKhaineDescription'/>"/>
	<entry name="Eldar/AvatarOfKhaineFlavor" value="<string name='Units/Eldar/AvatarOfKhaineFlavor'/>"/>
	<entry name="Eldar/DarkReaper" value="<string name='Units/Eldar/DarkReaper'/>"/>
	<entry name="Eldar/DarkReaperDescription" value="<string name='Units/Eldar/DarkReaperDescription'/>"/>
	<entry name="Eldar/DarkReaperFlavor" value="<string name='Units/Eldar/DarkReaperFlavor'/>"/>
	<entry name="Eldar/FarseerSkyrunner" value="<string name='Units/Eldar/FarseerSkyrunner'/>"/>
	<entry name="Eldar/FarseerSkyrunnerDescription" value="<string name='Units/Eldar/FarseerSkyrunnerDescription'/>"/>
	<entry name="Eldar/FarseerSkyrunnerFlavor" value="<string name='Units/Eldar/FarseerSkyrunnerFlavor'/>"/>
	<entry name="Eldar/FireDragon" value="<string name='Units/Eldar/FireDragon'/>"/>
	<entry name="Eldar/FireDragonDescription" value="<string name='Units/Eldar/FireDragonDescription'/>"/>
	<entry name="Eldar/FireDragonFlavor" value="<string name='Units/Eldar/FireDragonFlavor'/>"/>
	<entry name="Eldar/FirePrism" value="<string name='Units/Eldar/FirePrism'/>"/>
	<entry name="Eldar/FirePrismDescription" value="<string name='Units/Eldar/FirePrismDescription'/>"/>
	<entry name="Eldar/FirePrismFlavor" value="<string name='Units/Eldar/FirePrismFlavor'/>"/>
	<entry name="Eldar/HemlockWraithfighter" value="<string name='Units/Eldar/HemlockWraithfighter'/>"/>
	<entry name="Eldar/HemlockWraithfighterDescription" value="<string name='Units/Eldar/HemlockWraithfighterDescription'/>"/>
	<entry name="Eldar/HemlockWraithfighterFlavor" value="<string name='Units/Eldar/HemlockWraithfighterFlavor'/>"/>
	<entry name="Eldar/Hornet" value="<string name='Units/Eldar/Hornet'/>"/>
	<entry name="Eldar/HornetDescription" value="<string name='Units/Eldar/HornetDescription'/>"/>
	<entry name="Eldar/HornetFlavor" value="<string name='Units/Eldar/HornetFlavor'/>"/>
	<entry name="Eldar/HowlingBanshee" value="<string name='Units/Eldar/HowlingBanshee'/>"/>
	<entry name="Eldar/HowlingBansheeDescription" value="<string name='Units/Eldar/HowlingBansheeDescription'/>"/>
	<entry name="Eldar/HowlingBansheeFlavor" value="<string name='Units/Eldar/HowlingBansheeFlavor'/>"/>
	<entry name="Eldar/Ranger" value="<string name='Units/Eldar/Ranger'/>"/>
	<entry name="Eldar/RangerDescription" value="<string name='Units/Eldar/RangerDescription'/>"/>
	<entry name="Eldar/RangerFlavor" value="<string name='Units/Eldar/RangerFlavor'/>"/>
	<entry name="Eldar/Scorpion" value="<string name='Units/Eldar/Scorpion'/>"/>
	<entry name="Eldar/ScorpionDescription" value="<string name='Units/Eldar/ScorpionDescription'/>"/>
	<entry name="Eldar/ScorpionFlavor" value="<string name='Units/Eldar/ScorpionFlavor'/>"/>
	<entry name="Eldar/Spiritseer" value="<string name='Units/Eldar/Spiritseer'/>"/>
	<entry name="Eldar/SpiritseerDescription" value="<string name='Units/Eldar/SpiritseerDescription'/>"/>
	<entry name="Eldar/SpiritseerFlavor" value="<string name='Units/Eldar/SpiritseerFlavor'/>"/>
	<entry name="Eldar/VaulsWrathSupportBattery" value="<string name='Units/Eldar/VaulsWrathSupportBattery'/>"/>
	<entry name="Eldar/VaulsWrathSupportBatteryDescription" value="<string name='Units/Eldar/VaulsWrathSupportBatteryDescription'/>"/>
	<entry name="Eldar/VaulsWrathSupportBatteryFlavor" value="<string name='Units/Eldar/VaulsWrathSupportBatteryFlavor'/>"/>
	<entry name="Eldar/Vyper" value="<string name='Units/Eldar/Vyper'/>"/>
	<entry name="Eldar/VyperDescription" value="<string name='Units/Eldar/VyperDescription'/>"/>
	<entry name="Eldar/VyperFlavor" value="<string name='Units/Eldar/VyperFlavor'/>"/>
	<entry name="Eldar/WarWalker" value="<string name='Units/Eldar/WarWalker'/>"/>
	<entry name="Eldar/WarWalkerDescription" value="<string name='Units/Eldar/WarWalkerDescription'/>"/>
	<entry name="Eldar/WarWalkerFlavor" value="<string name='Units/Eldar/WarWalkerFlavor'/>"/>
	<entry name="Eldar/Warlock" value="<string name='Units/Eldar/Warlock'/>"/>
	<entry name="Eldar/WarlockDescription" value="<string name='Units/Eldar/WarlockDescription'/>"/>
	<entry name="Eldar/WarlockFlavor" value="<string name='Units/Eldar/WarlockFlavor'/>"/>
	<entry name="Eldar/WaveSerpent" value="<string name='Units/Eldar/WaveSerpent'/>"/>
	<entry name="Eldar/WaveSerpentDescription" value="<string name='Units/Eldar/WaveSerpentDescription'/>"/>
	<entry name="Eldar/WaveSerpentFlavor" value="<string name='Units/Eldar/WaveSerpentFlavor'/>"/>
	<entry name="Eldar/Wraithblade" value="<string name='Units/Eldar/Wraithblade'/>"/>
	<entry name="Eldar/WraithbladeDescription" value="<string name='Units/Eldar/WraithbladeDescription'/>"/>
	<entry name="Eldar/WraithbladeFlavor" value="<string name='Units/Eldar/WraithbladeFlavor'/>"/>
	<entry name="Eldar/Wraithknight" value="<string name='Units/Eldar/Wraithknight'/>"/>
	<entry name="Eldar/WraithknightDescription" value="<string name='Units/Eldar/WraithknightDescription'/>"/>
	<entry name="Eldar/WraithknightFlavor" value="<string name='Units/Eldar/WraithknightFlavor'/>"/>
	<entry name="Eldar/Wraithlord" value="<string name='Units/Eldar/Wraithlord'/>"/>
	<entry name="Eldar/WraithlordDescription" value="<string name='Units/Eldar/WraithlordDescription'/>"/>
	<entry name="Eldar/WraithlordFlavor" value="<string name='Units/Eldar/WraithlordFlavor'/>"/>
	<entry name="Necrons/AnnihilationBarge" value="<string name='Units/Necrons/AnnihilationBarge'/>"/>
	<entry name="Necrons/AnnihilationBargeDescription" value="<string name='Units/Necrons/AnnihilationBargeDescription'/>"/>
	<entry name="Necrons/AnnihilationBargeFlavor" value="<string name='Units/Necrons/AnnihilationBargeFlavor'/>"/>
	<entry name="Necrons/CanoptekReanimator" value="<string name='Units/Necrons/CanoptekReanimator'/>"/>
	<entry name="Necrons/CanoptekReanimatorDescription" value="<string name='Units/Necrons/CanoptekReanimatorDescription'/>"/>
	<entry name="Necrons/CanoptekReanimatorFlavor" value="<string name='Units/Necrons/CanoptekReanimatorFlavor'/>"/>
	<entry name="Necrons/CanoptekSpyder" value="<string name='Units/Necrons/CanoptekSpyder'/>"/>
	<entry name="Necrons/CanoptekSpyderDescription" value="<string name='Units/Necrons/CanoptekSpyderDescription'/>"/>
	<entry name="Necrons/CanoptekSpyderFlavor" value="<string name='Units/Necrons/CanoptekSpyderFlavor'/>"/>
	<entry name="Necrons/CanoptekWraith" value="<string name='Units/Necrons/CanoptekWraith'/>"/>
	<entry name="Necrons/CanoptekWraithDescription" value="<string name='Units/Necrons/CanoptekWraithDescription'/>"/>
	<entry name="Necrons/CanoptekWraithFlavor" value="<string name='Units/Necrons/CanoptekWraithFlavor'/>"/>
	<entry name="Necrons/Cryptek" value="<string name='Units/Necrons/Cryptek'/>"/>
	<entry name="Necrons/CryptekDescription" value="<string name='Units/Necrons/CryptekDescription'/>"/>
	<entry name="Necrons/CryptekFlavor" value="<string name='Units/Necrons/CryptekFlavor'/>"/>
	<entry name="Necrons/Deathmark" value="<string name='Units/Necrons/Deathmark'/>"/>
	<entry name="Necrons/DeathmarkDescription" value="<string name='Units/Necrons/DeathmarkDescription'/>"/>
	<entry name="Necrons/DeathmarkFlavor" value="<string name='Units/Necrons/DeathmarkFlavor'/>"/>
	<entry name="Necrons/DestroyerLord" value="<string name='Units/Necrons/DestroyerLord'/>"/>
	<entry name="Necrons/DestroyerLordDescription" value="<string name='Units/Necrons/DestroyerLordDescription'/>"/>
	<entry name="Necrons/DestroyerLordFlavor" value="<string name='Units/Necrons/DestroyerLordFlavor'/>"/>
	<entry name="Necrons/DoomScythe" value="<string name='Units/Necrons/DoomScythe'/>"/>
	<entry name="Necrons/DoomScytheDescription" value="<string name='Units/Necrons/DoomScytheDescription'/>"/>
	<entry name="Necrons/DoomScytheFlavor" value="<string name='Units/Necrons/DoomScytheFlavor'/>"/>
	<entry name="Necrons/DoomsdayArk" value="<string name='Units/Necrons/DoomsdayArk'/>"/>
	<entry name="Necrons/DoomsdayArkDescription" value="<string name='Units/Necrons/DoomsdayArkDescription'/>"/>
	<entry name="Necrons/DoomsdayArkFlavor" value="<string name='Units/Necrons/DoomsdayArkFlavor'/>"/>
	<entry name="Necrons/FlayedOne" value="<string name='Units/Necrons/FlayedOne'/>"/>
	<entry name="Necrons/FlayedOneDescription" value="<string name='Units/Necrons/FlayedOneDescription'/>"/>
	<entry name="Necrons/FlayedOneFlavor" value="<string name='Units/Necrons/FlayedOneFlavor'/>"/>
	<entry name="Necrons/GhostArk" value="<string name='Units/Necrons/GhostArk'/>"/>
	<entry name="Necrons/GhostArkDescription" value="<string name='Units/Necrons/GhostArkDescription'/>"/>
	<entry name="Necrons/GhostArkFlavor" value="<string name='Units/Necrons/GhostArkFlavor'/>"/>
	<entry name="Necrons/HeavyDestroyer" value="<string name='Units/Necrons/HeavyDestroyer'/>"/>
	<entry name="Necrons/HeavyDestroyerDescription" value="<string name='Units/Necrons/HeavyDestroyerDescription'/>"/>
	<entry name="Necrons/HeavyDestroyerFlavor" value="<string name='Units/Necrons/HeavyDestroyerFlavor'/>"/>
	<entry name="Necrons/Immortal" value="<string name='Units/Necrons/Immortal'/>"/>
	<entry name="Necrons/ImmortalDescription" value="<string name='Units/Necrons/ImmortalDescription'/>"/>
	<entry name="Necrons/ImmortalFlavor" value="<string name='Units/Necrons/ImmortalFlavor'/>"/>
	<entry name="Necrons/Lord" value="<string name='Units/Necrons/Lord'/>"/>
	<entry name="Necrons/LordDescription" value="<string name='Units/Necrons/LordDescription'/>"/>
	<entry name="Necrons/LordFlavor" value="<string name='Units/Necrons/LordFlavor'/>"/>
	<entry name="Necrons/Monolith" value="<string name='Units/Necrons/Monolith'/>"/>
	<entry name="Necrons/MonolithDescription" value="<string name='Units/Necrons/MonolithDescription'/>"/>
	<entry name="Necrons/MonolithFlavor" value="<string name='Units/Necrons/MonolithFlavor'/>"/>
	<entry name="Necrons/NightScythe" value="<string name='Units/Necrons/NightScythe'/>"/>
	<entry name="Necrons/NightScytheDescription" value="<string name='Units/Necrons/NightScytheDescription'/>"/>
	<entry name="Necrons/NightScytheFlavor" value="<string name='Units/Necrons/NightScytheFlavor'/>"/>
	<entry name="Necrons/Obelisk" value="<string name='Units/Necrons/Obelisk'/>"/>
	<entry name="Necrons/ObeliskDescription" value="<string name='Units/Necrons/ObeliskDescription'/>"/>
	<entry name="Necrons/ObeliskFlavor" value="<string name='Units/Necrons/ObeliskFlavor'/>"/>
	<entry name="Necrons/SkorpekhDestroyer" value="<string name='Units/Necrons/SkorpekhDestroyer'/>"/>
	<entry name="Necrons/SkorpekhDestroyerDescription" value="<string name='Units/Necrons/SkorpekhDestroyerDescription'/>"/>
	<entry name="Necrons/SkorpekhDestroyerFlavor" value="<string name='Units/Necrons/SkorpekhDestroyerFlavor'/>"/>
	<entry name="Necrons/TombBlade" value="<string name='Units/Necrons/TombBlade'/>"/>
	<entry name="Necrons/TombBladeDescription" value="<string name='Units/Necrons/TombBladeDescription'/>"/>
	<entry name="Necrons/TombBladeFlavor" value="<string name='Units/Necrons/TombBladeFlavor'/>"/>
	<entry name="Necrons/TranscendentCtan" value="<string name='Units/Necrons/TranscendentCtan'/>"/>
	<entry name="Necrons/TranscendentCtanDescription" value="<string name='Units/Necrons/TranscendentCtanDescription'/>"/>
	<entry name="Necrons/TranscendentCtanFlavor" value="<string name='Units/Necrons/TranscendentCtanFlavor'/>"/>
	<entry name="Necrons/TriarchPraetorian" value="<string name='Units/Necrons/TriarchPraetorian'/>"/>
	<entry name="Necrons/TriarchPraetorianDescription" value="<string name='Units/Necrons/TriarchPraetorianDescription'/>"/>
	<entry name="Necrons/TriarchPraetorianFlavor" value="<string name='Units/Necrons/TriarchPraetorianFlavor'/>"/>
	<entry name="Necrons/TriarchStalker" value="<string name='Units/Necrons/TriarchStalker'/>"/>
	<entry name="Necrons/TriarchStalkerDescription" value="<string name='Units/Necrons/TriarchStalkerDescription'/>"/>
	<entry name="Necrons/TriarchStalkerFlavor" value="<string name='Units/Necrons/TriarchStalkerFlavor'/>"/>
	<entry name="Orks/Battlewagon" value="<string name='Units/Orks/Battlewagon'/>"/>
	<entry name="Orks/BattlewagonDescription" value="<string name='Units/Orks/BattlewagonDescription'/>"/>
	<entry name="Orks/BattlewagonFlavor" value="<string name='Units/Orks/BattlewagonFlavor'/>"/>
	<entry name="Orks/BigMek" value="<string name='Units/Orks/BigMek'/>"/>
	<entry name="Orks/BigMekDescription" value="<string name='Units/Orks/BigMekDescription'/>"/>
	<entry name="Orks/BigMekFlavor" value="<string name='Units/Orks/BigMekFlavor'/>"/>
	<entry name="Orks/BurnaBommer" value="<string name='Units/Orks/BurnaBommer'/>"/>
	<entry name="Orks/BurnaBommerDescription" value="<string name='Units/Orks/BurnaBommerDescription'/>"/>
	<entry name="Orks/BurnaBommerFlavor" value="<string name='Units/Orks/BurnaBommerFlavor'/>"/>
	<entry name="Orks/BurnaBoy" value="<string name='Units/Orks/BurnaBoy'/>"/>
	<entry name="Orks/BurnaBoyDescription" value="<string name='Units/Orks/BurnaBoyDescription'/>"/>
	<entry name="Orks/BurnaBoyFlavor" value="<string name='Units/Orks/BurnaBoyFlavor'/>"/>
	<entry name="Orks/Dakkajet" value="<string name='Units/Orks/Dakkajet'/>"/>
	<entry name="Orks/DakkajetDescription" value="<string name='Units/Orks/DakkajetDescription'/>"/>
	<entry name="Orks/DakkajetFlavor" value="<string name='Units/Orks/DakkajetFlavor'/>"/>
	<entry name="Orks/DeffDread" value="<string name='Units/Orks/DeffDread'/>"/>
	<entry name="Orks/DeffDreadDescription" value="<string name='Units/Orks/DeffDreadDescription'/>"/>
	<entry name="Orks/DeffDreadFlavor" value="<string name='Units/Orks/DeffDreadFlavor'/>"/>
	<entry name="Orks/Deffkopta" value="<string name='Units/Orks/Deffkopta'/>"/>
	<entry name="Orks/DeffkoptaDescription" value="<string name='Units/Orks/DeffkoptaDescription'/>"/>
	<entry name="Orks/DeffkoptaFlavor" value="<string name='Units/Orks/DeffkoptaFlavor'/>"/>
	<entry name="Orks/FlashGitz" value="<string name='Units/Orks/FlashGitz'/>"/>
	<entry name="Orks/FlashGitzDescription" value="<string name='Units/Orks/FlashGitzDescription'/>"/>
	<entry name="Orks/FlashGitzFlavor" value="<string name='Units/Orks/FlashGitzFlavor'/>"/>
	<entry name="Orks/GargantuanSquiggoth" value="<string name='Units/Orks/GargantuanSquiggoth'/>"/>
	<entry name="Orks/GargantuanSquiggothDescription" value="<string name='Units/Orks/GargantuanSquiggothDescription'/>"/>
	<entry name="Orks/GargantuanSquiggothFlavor" value="<string name='Units/Orks/GargantuanSquiggothFlavor'/>"/>
	<entry name="Orks/Gorkanaut" value="<string name='Units/Orks/Gorkanaut'/>"/>
	<entry name="Orks/GorkanautDescription" value="<string name='Units/Orks/GorkanautDescription'/>"/>
	<entry name="Orks/GorkanautFlavor" value="<string name='Units/Orks/GorkanautFlavor'/>"/>
	<entry name="Orks/KillBursta" value="<string name='Units/Orks/KillBursta'/>"/>
	<entry name="Orks/KillBurstaDescription" value="<string name='Units/Orks/KillBurstaDescription'/>"/>
	<entry name="Orks/KillBurstaFlavor" value="<string name='Units/Orks/KillBurstaFlavor'/>"/>
	<entry name="Orks/KillaKan" value="<string name='Units/Orks/KillaKan'/>"/>
	<entry name="Orks/KillaKanDescription" value="<string name='Units/Orks/KillaKanDescription'/>"/>
	<entry name="Orks/KillaKanFlavor" value="<string name='Units/Orks/KillaKanFlavor'/>"/>
	<entry name="Orks/Meganob" value="<string name='Units/Orks/Meganob'/>"/>
	<entry name="Orks/MeganobDescription" value="<string name='Units/Orks/MeganobDescription'/>"/>
	<entry name="Orks/MeganobFlavor" value="<string name='Units/Orks/MeganobFlavor'/>"/>
	<entry name="Orks/MegatrakkScrapjet" value="<string name='Units/Orks/MegatrakkScrapjet'/>"/>
	<entry name="Orks/MegatrakkScrapjetDescription" value="<string name='Units/Orks/MegatrakkScrapjetDescription'/>"/>
	<entry name="Orks/MegatrakkScrapjetFlavor" value="<string name='Units/Orks/MegatrakkScrapjetFlavor'/>"/>
	<entry name="Orks/Mek" value="<string name='Units/Orks/Mek'/>"/>
	<entry name="Orks/MekDescription" value="<string name='Units/Orks/MekDescription'/>"/>
	<entry name="Orks/MekFlavor" value="<string name='Units/Orks/MekFlavor'/>"/>
	<entry name="Orks/MekGun" value="<string name='Units/Orks/MekGun'/>"/>
	<entry name="Orks/MekGunDescription" value="<string name='Units/Orks/MekGunDescription'/>"/>
	<entry name="Orks/MekGunFlavor" value="<string name='Units/Orks/MekGunFlavor'/>"/>
	<entry name="Orks/Painboy" value="<string name='Units/Orks/Painboy'/>"/>
	<entry name="Orks/PainboyDescription" value="<string name='Units/Orks/PainboyDescription'/>"/>
	<entry name="Orks/PainboyFlavor" value="<string name='Units/Orks/PainboyFlavor'/>"/>
	<entry name="Orks/SquighogBoy" value="<string name='Units/Orks/SquighogBoy'/>"/>
	<entry name="Orks/SquighogBoyDescription" value="<string name='Units/Orks/SquighogBoyDescription'/>"/>
	<entry name="Orks/SquighogBoyFlavor" value="<string name='Units/Orks/SquighogBoyFlavor'/>"/>
	<entry name="Orks/Tankbusta" value="<string name='Units/Orks/Tankbusta'/>"/>
	<entry name="Orks/TankbustaDescription" value="<string name='Units/Orks/TankbustaDescription'/>"/>
	<entry name="Orks/TankbustaFlavor" value="<string name='Units/Orks/TankbustaFlavor'/>"/>
	<entry name="Orks/Warbiker" value="<string name='Units/Orks/Warbiker'/>"/>
	<entry name="Orks/WarbikerDescription" value="<string name='Units/Orks/WarbikerDescription'/>"/>
	<entry name="Orks/WarbikerFlavor" value="<string name='Units/Orks/WarbikerFlavor'/>"/>
	<entry name="Orks/Warboss" value="<string name='Units/Orks/Warboss'/>"/>
	<entry name="Orks/WarbossDescription" value="<string name='Units/Orks/WarbossDescription'/>"/>
	<entry name="Orks/WarbossFlavor" value="<string name='Units/Orks/WarbossFlavor'/>"/>
	<entry name="Orks/Warbuggy" value="<string name='Units/Orks/Warbuggy'/>"/>
	<entry name="Orks/WarbuggyDescription" value="<string name='Units/Orks/WarbuggyDescription'/>"/>
	<entry name="Orks/WarbuggyFlavor" value="<string name='Units/Orks/WarbuggyFlavor'/>"/>
	<entry name="Orks/Weirdboy" value="<string name='Units/Orks/Weirdboy'/>"/>
	<entry name="Orks/WeirdboyDescription" value="<string name='Units/Orks/WeirdboyDescription'/>"/>
	<entry name="Orks/WeirdboyFlavor" value="<string name='Units/Orks/WeirdboyFlavor'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellant" value="<string name='Units/SistersOfBattle/ArcoFlagellant'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellantDescription" value="<string name='Units/SistersOfBattle/ArcoFlagellantDescription'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellantFlavor" value="<string name='Units/SistersOfBattle/ArcoFlagellantFlavor'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter" value="<string name='Units/SistersOfBattle/AvengerStrikeFighter'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterDescription" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterDescription'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterFlavor" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterFlavor'/>"/>
	<entry name="SistersOfBattle/Castigator" value="<string name='Units/SistersOfBattle/Castigator'/>"/>
	<entry name="SistersOfBattle/CastigatorDescription" value="<string name='Units/SistersOfBattle/CastigatorDescription'/>"/>
	<entry name="SistersOfBattle/CastigatorFlavor" value="<string name='Units/SistersOfBattle/CastigatorFlavor'/>"/>
	<entry name="SistersOfBattle/CelestianSacresant" value="<string name='Units/SistersOfBattle/CelestianSacresant'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantDescription" value="<string name='Units/SistersOfBattle/CelestianSacresantDescription'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantFlavor" value="<string name='Units/SistersOfBattle/CelestianSacresantFlavor'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancer" value="<string name='Units/SistersOfBattle/CerastusKnightLancer'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerDescription" value="<string name='Units/SistersOfBattle/CerastusKnightLancerDescription'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerFlavor" value="<string name='Units/SistersOfBattle/CerastusKnightLancerFlavor'/>"/>
	<entry name="SistersOfBattle/Dialogus" value="<string name='Units/SistersOfBattle/Dialogus'/>"/>
	<entry name="SistersOfBattle/DialogusDescription" value="<string name='Units/SistersOfBattle/DialogusDescription'/>"/>
	<entry name="SistersOfBattle/DialogusFlavor" value="<string name='Units/SistersOfBattle/DialogusFlavor'/>"/>
	<entry name="SistersOfBattle/Dominion" value="<string name='Units/SistersOfBattle/Dominion'/>"/>
	<entry name="SistersOfBattle/DominionDescription" value="<string name='Units/SistersOfBattle/DominionDescription'/>"/>
	<entry name="SistersOfBattle/DominionFlavor" value="<string name='Units/SistersOfBattle/DominionFlavor'/>"/>
	<entry name="SistersOfBattle/Exorcist" value="<string name='Units/SistersOfBattle/Exorcist'/>"/>
	<entry name="SistersOfBattle/ExorcistDescription" value="<string name='Units/SistersOfBattle/ExorcistDescription'/>"/>
	<entry name="SistersOfBattle/ExorcistFlavor" value="<string name='Units/SistersOfBattle/ExorcistFlavor'/>"/>
	<entry name="SistersOfBattle/Headquarters" value="<string name='Buildings/SistersOfBattle/Headquarters'/>"/>
	<entry name="SistersOfBattle/HeadquartersDescription" value="<string name='Units/SistersOfBattle/HeadquartersDescription'/>"/>
	<entry name="SistersOfBattle/HeadquartersFlavor" value="<string name='Buildings/SistersOfBattle/HeadquartersFlavor'/>"/>
	<entry name="SistersOfBattle/Hospitaller" value="<string name='Units/SistersOfBattle/Hospitaller'/>"/>
	<entry name="SistersOfBattle/HospitallerDescription" value="<string name='Units/SistersOfBattle/HospitallerDescription'/>"/>
	<entry name="SistersOfBattle/HospitallerFlavor" value="<string name='Units/SistersOfBattle/HospitallerFlavor'/>"/>
	<entry name="SistersOfBattle/Imagifier" value="<string name='Units/SistersOfBattle/Imagifier'/>"/>
	<entry name="SistersOfBattle/ImagifierDescription" value="<string name='Units/SistersOfBattle/ImagifierDescription'/>"/>
	<entry name="SistersOfBattle/ImagifierFlavor" value="<string name='Units/SistersOfBattle/ImagifierFlavor'/>"/>
	<entry name="SistersOfBattle/Mortifier" value="<string name='Units/SistersOfBattle/Mortifier'/>"/>
	<entry name="SistersOfBattle/MortifierDescription" value="<string name='Units/SistersOfBattle/MortifierDescription'/>"/>
	<entry name="SistersOfBattle/MortifierFlavor" value="<string name='Units/SistersOfBattle/MortifierFlavor'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="<string name='Units/SistersOfBattle/ParagonWarsuit'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="<string name='Units/SistersOfBattle/ParagonWarsuitDescription'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="<string name='Units/SistersOfBattle/ParagonWarsuitFlavor'/>"/>
	<entry name="SistersOfBattle/Retributor" value="<string name='Units/SistersOfBattle/Retributor'/>"/>
	<entry name="SistersOfBattle/RetributorDescription" value="<string name='Units/SistersOfBattle/RetributorDescription'/>"/>
	<entry name="SistersOfBattle/RetributorFlavor" value="<string name='Units/SistersOfBattle/RetributorFlavor'/>"/>
	<entry name="SistersOfBattle/SaintCelestine" value="<string name='Units/SistersOfBattle/SaintCelestine'/>"/>
	<entry name="SistersOfBattle/SaintCelestineDescription" value="<string name='Units/SistersOfBattle/SaintCelestineDescription'/>"/>
	<entry name="SistersOfBattle/SaintCelestineFlavor" value="<string name='Units/SistersOfBattle/SaintCelestineFlavor'/>"/>
	<entry name="SistersOfBattle/SisterRepentia" value="<string name='Units/SistersOfBattle/SisterRepentia'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaDescription" value="<string name='Units/SistersOfBattle/SisterRepentiaDescription'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaFlavor" value="<string name='Units/SistersOfBattle/SisterRepentiaFlavor'/>"/>
	<entry name="SistersOfBattle/Zephyrim" value="<string name='Units/SistersOfBattle/Zephyrim'/>"/>
	<entry name="SistersOfBattle/ZephyrimDescription" value="<string name='Units/SistersOfBattle/ZephyrimDescription'/>"/>
	<entry name="SistersOfBattle/ZephyrimFlavor" value="<string name='Units/SistersOfBattle/ZephyrimFlavor'/>"/>
	<entry name="SpaceMarines/Apothecary" value="<string name='Units/SpaceMarines/Apothecary'/>"/>
	<entry name="SpaceMarines/ApothecaryDescription" value="<string name='Units/SpaceMarines/ApothecaryDescription'/>"/>
	<entry name="SpaceMarines/ApothecaryFlavor" value="<string name='Units/SpaceMarines/ApothecaryFlavor'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannon" value="<string name='Units/SpaceMarines/AquilaMacroCannon'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonDescription" value="<string name='Units/SpaceMarines/AquilaMacroCannonDescription'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonFlavor" value="<string name='Units/SpaceMarines/AquilaMacroCannonFlavor'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="<string name='Units/SpaceMarines/AssaultSpaceMarine'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineDescription" value="<string name='Units/SpaceMarines/AssaultSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineFlavor" value="<string name='Units/SpaceMarines/AssaultSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/AssaultTerminator" value="<string name='Units/SpaceMarines/AssaultTerminator'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorDescription" value="<string name='Units/SpaceMarines/AssaultTerminatorDescription'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorFlavor" value="<string name='Units/SpaceMarines/AssaultTerminatorFlavor'/>"/>
	<entry name="SpaceMarines/Captain" value="<string name='Units/SpaceMarines/Captain'/>"/>
	<entry name="SpaceMarines/CaptainDescription" value="<string name='Units/SpaceMarines/CaptainDescription'/>"/>
	<entry name="SpaceMarines/CaptainFlavor" value="<string name='Units/SpaceMarines/CaptainFlavor'/>"/>
	<entry name="SpaceMarines/Chaplain" value="<string name='Units/SpaceMarines/Chaplain'/>"/>
	<entry name="SpaceMarines/ChaplainDescription" value="<string name='Units/SpaceMarines/ChaplainDescription'/>"/>
	<entry name="SpaceMarines/ChaplainFlavor" value="<string name='Units/SpaceMarines/ChaplainFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorCenturion" value="<string name='Units/SpaceMarines/DevastatorCenturion'/>"/>
	<entry name="SpaceMarines/DevastatorCenturionDescription" value="<string name='Units/SpaceMarines/DevastatorCenturionDescription'/>"/>
	<entry name="SpaceMarines/DevastatorCenturionFlavor" value="<string name='Units/SpaceMarines/DevastatorCenturionFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="<string name='Units/SpaceMarines/DevastatorSpaceMarine'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineDescription" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineFlavor" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/Dreadnought" value="<string name='Units/SpaceMarines/Dreadnought'/>"/>
	<entry name="SpaceMarines/DreadnoughtDescription" value="<string name='Units/SpaceMarines/DreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/DreadnoughtFlavor" value="<string name='Units/SpaceMarines/DreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Hunter" value="<string name='Units/SpaceMarines/Hunter'/>"/>
	<entry name="SpaceMarines/HunterDescription" value="<string name='Units/SpaceMarines/HunterDescription'/>"/>
	<entry name="SpaceMarines/HunterFlavor" value="<string name='Units/SpaceMarines/HunterFlavor'/>"/>
	<entry name="SpaceMarines/LandRaider" value="<string name='Units/SpaceMarines/LandRaider'/>"/>
	<entry name="SpaceMarines/LandRaiderDescription" value="<string name='Units/SpaceMarines/LandRaiderDescription'/>"/>
	<entry name="SpaceMarines/LandRaiderFlavor" value="<string name='Units/SpaceMarines/LandRaiderFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeeder" value="<string name='Units/SpaceMarines/LandSpeeder'/>"/>
	<entry name="SpaceMarines/LandSpeederDescription" value="<string name='Units/SpaceMarines/LandSpeederDescription'/>"/>
	<entry name="SpaceMarines/LandSpeederFlavor" value="<string name='Units/SpaceMarines/LandSpeederFlavor'/>"/>
	<entry name="SpaceMarines/Librarian" value="<string name='Units/SpaceMarines/Librarian'/>"/>
	<entry name="SpaceMarines/LibrarianDescription" value="<string name='Units/SpaceMarines/LibrarianDescription'/>"/>
	<entry name="SpaceMarines/LibrarianFlavor" value="<string name='Units/SpaceMarines/LibrarianFlavor'/>"/>	
	<entry name="SpaceMarines/Predator" value="<string name='Units/SpaceMarines/Predator'/>"/>
	<entry name="SpaceMarines/PredatorDescription" value="<string name='Units/SpaceMarines/PredatorDescription'/>"/>
	<entry name="SpaceMarines/PredatorFlavor" value="<string name='Units/SpaceMarines/PredatorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressor" value="<string name='Units/SpaceMarines/PrimarisAggressor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressorDescription" value="<string name='Units/SpaceMarines/PrimarisAggressorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisAggressorFlavor" value="<string name='Units/SpaceMarines/PrimarisAggressorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisHellblaster" value="<string name='Units/SpaceMarines/PrimarisHellblaster'/>"/>
	<entry name="SpaceMarines/PrimarisHellblasterDescription" value="<string name='Units/SpaceMarines/PrimarisHellblasterDescription'/>"/>
	<entry name="SpaceMarines/PrimarisHellblasterFlavor" value="<string name='Units/SpaceMarines/PrimarisHellblasterFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisInceptor" value="<string name='Units/SpaceMarines/PrimarisInceptor'/>"/>
	<entry name="SpaceMarines/PrimarisInceptorDescription" value="<string name='Units/SpaceMarines/PrimarisInceptorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisInceptorFlavor" value="<string name='Units/SpaceMarines/PrimarisInceptorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessor" value="<string name='Units/SpaceMarines/PrimarisIntercessor'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessorDescription" value="<string name='Units/SpaceMarines/PrimarisIntercessorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessorFlavor" value="<string name='Units/SpaceMarines/PrimarisIntercessorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATV" value="<string name='Units/SpaceMarines/PrimarisInvaderATV'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATVDescription" value="<string name='Units/SpaceMarines/PrimarisInvaderATVDescription'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATVFlavor" value="<string name='Units/SpaceMarines/PrimarisInvaderATVFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutioner'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerDescription" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerDescription'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerFlavor" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerFlavor'/>"/>
	<entry name="SpaceMarines/Razorback" value="<string name='Units/SpaceMarines/Razorback'/>"/>
	<entry name="SpaceMarines/RazorbackDescription" value="<string name='Units/SpaceMarines/RazorbackDescription'/>"/>
	<entry name="SpaceMarines/RazorbackFlavor" value="<string name='Units/SpaceMarines/RazorbackFlavor'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnought" value="<string name='Units/SpaceMarines/RedemptorDreadnought'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtDescription" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtFlavor" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Scout" value="<string name='Units/SpaceMarines/Scout'/>"/>
	<entry name="SpaceMarines/ScoutDescription" value="<string name='Units/SpaceMarines/ScoutDescription'/>"/>
	<entry name="SpaceMarines/ScoutFlavor" value="<string name='Units/SpaceMarines/ScoutFlavor'/>"/>
	<entry name="SpaceMarines/ScoutBiker" value="<string name='Units/SpaceMarines/ScoutBiker'/>"/>
	<entry name="SpaceMarines/ScoutBikerDescription" value="<string name='Units/SpaceMarines/ScoutBikerDescription'/>"/>
	<entry name="SpaceMarines/ScoutBikerFlavor" value="<string name='Units/SpaceMarines/ScoutBikerFlavor'/>"/>
	<entry name="SpaceMarines/StormravenGunship" value="<string name='Units/SpaceMarines/StormravenGunship'/>"/>
	<entry name="SpaceMarines/StormravenGunshipDescription" value="<string name='Units/SpaceMarines/StormravenGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormravenGunshipFlavor" value="<string name='Units/SpaceMarines/StormravenGunshipFlavor'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike" value="<string name='Units/SpaceMarines/StormSpeederThunderstrike'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeDescription" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeDescription'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeFlavor" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeFlavor'/>"/>
	<entry name="SpaceMarines/StormtalonGunship" value="<string name='Units/SpaceMarines/StormtalonGunship'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipDescription" value="<string name='Units/SpaceMarines/StormtalonGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipFlavor" value="<string name='Units/SpaceMarines/StormtalonGunshipFlavor'/>"/>
	<entry name="SpaceMarines/Terminator" value="<string name='Units/SpaceMarines/Terminator'/>"/>
	<entry name="SpaceMarines/TerminatorDescription" value="<string name='Units/SpaceMarines/TerminatorDescription'/>"/>
	<entry name="SpaceMarines/TerminatorFlavor" value="<string name='Units/SpaceMarines/TerminatorFlavor'/>"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="<string name='Units/SpaceMarines/ThunderfireCannon'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonDescription" value="<string name='Units/SpaceMarines/ThunderfireCannonDescription'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonFlavor" value="<string name='Units/SpaceMarines/ThunderfireCannonFlavor'/>"/>
	<entry name="SpaceMarines/Vindicator" value="<string name='Units/SpaceMarines/Vindicator'/>"/>
	<entry name="SpaceMarines/VindicatorDescription" value="<string name='Units/SpaceMarines/VindicatorDescription'/>"/>
	<entry name="SpaceMarines/VindicatorFlavor" value="<string name='Units/SpaceMarines/VindicatorFlavor'/>"/>
	<entry name="SpaceMarines/Whirlwind" value="<string name='Units/SpaceMarines/Whirlwind'/>"/>
	<entry name="SpaceMarines/WhirlwindDescription" value="<string name='Units/SpaceMarines/WhirlwindDescription'/>"/>
	<entry name="SpaceMarines/WhirlwindFlavor" value="<string name='Units/SpaceMarines/WhirlwindFlavor'/>"/>
	<entry name="Tau/BroadsideBattlesuit" value="<string name='Units/Tau/BroadsideBattlesuit'/>"/>
	<entry name="Tau/BroadsideBattlesuitDescription" value="<string name='Units/Tau/BroadsideBattlesuitDescription'/>"/>
	<entry name="Tau/BroadsideBattlesuitFlavor" value="<string name='Units/Tau/BroadsideBattlesuitFlavor'/>"/>
	<entry name="Tau/BuilderDrone" value="<string name='Units/Tau/BuilderDrone'/>"/>
	<entry name="Tau/BuilderDroneDescription" value="<string name='Units/Tau/BuilderDroneDescription'/>"/>
	<entry name="Tau/BuilderDroneFlavor" value="<string name='Units/Tau/BuilderDroneFlavor'/>"/>
	<entry name="Tau/Commander" value="<string name='Units/Tau/Commander'/>"/>
	<entry name="Tau/CommanderDescription" value="<string name='Units/Tau/CommanderDescription'/>"/>
	<entry name="Tau/CommanderFlavor" value="<string name='Units/Tau/CommanderFlavor'/>"/>
	<entry name="Tau/CrisisBattlesuit" value="<string name='Units/Tau/CrisisBattlesuit'/>"/>
	<entry name="Tau/CrisisBattlesuitDescription" value="<string name='Units/Tau/CrisisBattlesuitDescription'/>"/>
	<entry name="Tau/CrisisBattlesuitFlavor" value="<string name='Units/Tau/CrisisBattlesuitFlavor'/>"/>
	<entry name="Tau/Devilfish" value="<string name='Units/Tau/Devilfish'/>"/>
	<entry name="Tau/DevilfishDescription" value="<string name='Units/Tau/DevilfishDescription'/>"/>
	<entry name="Tau/DevilfishFlavor" value="<string name='Units/Tau/DevilfishFlavor'/>"/>
	<entry name="Tau/Ethereal" value="<string name='Units/Tau/Ethereal'/>"/>
	<entry name="Tau/EtherealDescription" value="<string name='Units/Tau/EtherealDescription'/>"/>
	<entry name="Tau/EtherealFlavor" value="<string name='Units/Tau/EtherealFlavor'/>"/>
	<entry name="Tau/FireWarriorBreacher" value="<string name='Units/Tau/FireWarriorBreacher'/>"/>
	<entry name="Tau/FireWarriorBreacherDescription" value="<string name='Units/Tau/FireWarriorBreacherDescription'/>"/>
	<entry name="Tau/FireWarriorBreacherFlavor" value="<string name='Units/Tau/FireWarriorBreacherFlavor'/>"/>
	<entry name="Tau/GunDrone" value="<string name='Units/Tau/GunDrone'/>"/>
	<entry name="Tau/GunDroneDescription" value="Grants Fire Warriors, Fire Warriors Breachers, XV8 Crisis Battlesuits, XV25 Stealth Battlesuits, XV88 Broadside Battlesuits, Cadre Fireblade, Ethereal and Commander the ability to deploy temporary basic combat drones."/>
	<entry name="Tau/GunDroneFlavor" value="<string name='Units/Tau/GunDroneFlavor'/>"/>
	<entry name="Tau/HammerheadGunship" value="<string name='Units/Tau/HammerheadGunship'/>"/>
	<entry name="Tau/HammerheadGunshipDescription" value="<string name='Units/Tau/HammerheadGunshipDescription'/>"/>
	<entry name="Tau/HammerheadGunshipFlavor" value="<string name='Units/Tau/HammerheadGunshipFlavor'/>"/>
	<entry name="Tau/KrootoxRider" value="<string name='Units/Tau/KrootoxRider'/>"/>
	<entry name="Tau/KrootoxRiderDescription" value="<string name='Units/Tau/KrootoxRiderDescription'/>"/>
	<entry name="Tau/KrootoxRiderFlavor" value="<string name='Units/Tau/KrootoxRiderFlavor'/>"/>
	<entry name="Tau/MarkerDrone" value="<string name='Units/Tau/MarkerDrone'/>"/>
	<entry name="Tau/MarkerDroneDescription" value="Grants Fire Warriors, Fire Warriors Warriors, XV8 Crisis Battlesuits, XV25 Stealth Battlesuits, XV88 Broadside Battlesuits, Cadre Fireblade, Ethereal and Commander the ability to deploy temporary drones that mark enemies for increased damage."/>
	<entry name="Tau/MarkerDroneFlavor" value="<string name='Units/Tau/MarkerDroneFlavor'/>"/>
	<entry name="Tau/Pathfinder" value="<string name='Units/Tau/Pathfinder'/>"/>
	<entry name="Tau/PathfinderDescription" value="<string name='Units/Tau/PathfinderDescription'/>"/>
	<entry name="Tau/PathfinderFlavor" value="<string name='Units/Tau/PathfinderFlavor'/>"/>
	<entry name="Tau/RiptideBattlesuit" value="<string name='Units/Tau/RiptideBattlesuit'/>"/>
	<entry name="Tau/RiptideBattlesuitDescription" value="<string name='Units/Tau/RiptideBattlesuitDescription'/>"/>
	<entry name="Tau/RiptideBattlesuitFlavor" value="<string name='Units/Tau/RiptideBattlesuitFlavor'/>"/>
	<entry name="Tau/RVarnaBattlesuit" value="<string name='Units/Tau/RVarnaBattlesuit'/>"/>
	<entry name="Tau/RVarnaBattlesuitDescription" value="<string name='Units/Tau/RVarnaBattlesuitDescription'/>"/>
	<entry name="Tau/RVarnaBattlesuitFlavor" value="<string name='Units/Tau/RVarnaBattlesuitFlavor'/>"/>
	<entry name="Tau/ShieldDrone" value="<string name='Units/Tau/ShieldDrone'/>"/>
	<entry name="Tau/ShieldDroneDescription" value="Grants Fire Warriors, Fire Warriors Breachers, XV8 Crisis Battlesuits, XV25 Stealth Battlesuits, XV88 Broadside Battlesuits, Cadre Fireblade, Ethereal and Commander the ability to deploy temporary drones that shield allies."/>
	<entry name="Tau/ShieldDroneFlavor" value="<string name='Units/Tau/ShieldDroneFlavor'/>"/>
	<entry name="Tau/SkyRayGunship" value="<string name='Units/Tau/SkyRayGunship'/>"/>
	<entry name="Tau/SkyRayGunshipDescription" value="<string name='Units/Tau/SkyRayGunshipDescription'/>"/>
	<entry name="Tau/SkyRayGunshipFlavor" value="<string name='Units/Tau/SkyRayGunshipFlavor'/>"/>
	<entry name="Tau/StealthBattlesuit" value="<string name='Units/Tau/StealthBattlesuit'/>"/>
	<entry name="Tau/StealthBattlesuitDescription" value="<string name='Units/Tau/StealthBattlesuitDescription'/>"/>
	<entry name="Tau/StealthBattlesuitFlavor" value="<string name='Units/Tau/StealthBattlesuitFlavor'/>"/>
	<entry name="Tau/Stormsurge" value="<string name='Units/Tau/Stormsurge'/>"/>
	<entry name="Tau/StormsurgeDescription" value="<string name='Units/Tau/StormsurgeDescription'/>"/>
	<entry name="Tau/StormsurgeFlavor" value="<string name='Units/Tau/StormsurgeFlavor'/>"/>
	<entry name="Tau/SunSharkBomber" value="<string name='Units/Tau/SunSharkBomber'/>"/>
	<entry name="Tau/SunSharkBomberDescription" value="<string name='Units/Tau/SunSharkBomberDescription'/>"/>
	<entry name="Tau/SunSharkBomberFlavor" value="<string name='Units/Tau/SunSharkBomberFlavor'/>"/>
	<entry name="Tau/TidewallGunrig" value="<string name='Units/Tau/TidewallGunrig'/>"/>
	<entry name="Tau/TidewallGunrigDescription" value="Grants Builder Drones the ability to construct a heavily armed fortification that can be moved by transporting troops."/>
	<entry name="Tau/TidewallGunrigFlavor" value="<string name='Units/Tau/TidewallGunrigFlavor'/>"/>
	<entry name="Tau/TigerShark" value="<string name='Units/Tau/TigerShark'/>"/>
	<entry name="Tau/TigerSharkDescription" value="<string name='Units/Tau/TigerSharkDescription'/>"/>
	<entry name="Tau/TigerSharkFlavor" value="<string name='Units/Tau/TigerSharkFlavor'/>"/>
	<entry name="Tyranids/Biovore" value="<string name='Units/Tyranids/Biovore'/>"/>
	<entry name="Tyranids/BiovoreDescription" value="<string name='Units/Tyranids/BiovoreDescription'/>"/>
	<entry name="Tyranids/BiovoreFlavor" value="<string name='Units/Tyranids/BiovoreFlavor'/>"/>
	<entry name="Tyranids/Carnifex" value="<string name='Units/Tyranids/Carnifex'/>"/>
	<entry name="Tyranids/CarnifexDescription" value="<string name='Units/Tyranids/CarnifexDescription'/>"/>
	<entry name="Tyranids/CarnifexFlavor" value="<string name='Units/Tyranids/CarnifexFlavor'/>"/>
	<entry name="Tyranids/Exocrine" value="<string name='Units/Tyranids/Exocrine'/>"/>
	<entry name="Tyranids/ExocrineDescription" value="<string name='Units/Tyranids/ExocrineDescription'/>"/>
	<entry name="Tyranids/ExocrineFlavor" value="<string name='Units/Tyranids/ExocrineFlavor'/>"/>
	<entry name="Tyranids/Gargoyle" value="<string name='Units/Tyranids/Gargoyle'/>"/>
	<entry name="Tyranids/GargoyleDescription" value="<string name='Units/Tyranids/GargoyleDescription'/>"/>
	<entry name="Tyranids/GargoyleFlavor" value="<string name='Units/Tyranids/GargoyleFlavor'/>"/>
	<entry name="Tyranids/Haruspex" value="<string name='Units/Tyranids/Haruspex'/>"/>
	<entry name="Tyranids/HaruspexDescription" value="<string name='Units/Tyranids/HaruspexDescription'/>"/>
	<entry name="Tyranids/HaruspexFlavor" value="<string name='Units/Tyranids/HaruspexFlavor'/>"/>
	<entry name="Tyranids/HiveTyrant" value="<string name='Units/Tyranids/HiveTyrant'/>"/>
	<entry name="Tyranids/HiveTyrantDescription" value="<string name='Units/Tyranids/HiveTyrantDescription'/>"/>
	<entry name="Tyranids/HiveTyrantFlavor" value="<string name='Units/Tyranids/HiveTyrantFlavor'/>"/>
	<entry name="Tyranids/HiveGuard" value="<string name='Units/Tyranids/HiveGuard'/>"/>
	<entry name="Tyranids/HiveGuardDescription" value="<string name='Units/Tyranids/HiveGuardDescription'/>"/>
	<entry name="Tyranids/HiveGuardFlavor" value="<string name='Units/Tyranids/HiveGuardFlavor'/>"/>
	<entry name="Tyranids/Hormagaunt" value="<string name='Units/Tyranids/Hormagaunt'/>"/>
	<entry name="Tyranids/HormagauntDescription" value="<string name='Units/Tyranids/HormagauntDescription'/>"/>
	<entry name="Tyranids/HormagauntFlavor" value="<string name='Units/Tyranids/HormagauntFlavor'/>"/>
	<entry name="Tyranids/Lictor" value="<string name='Units/Tyranids/Lictor'/>"/>
	<entry name="Tyranids/LictorDescription" value="<string name='Units/Tyranids/LictorDescription'/>"/>
	<entry name="Tyranids/LictorFlavor" value="<string name='Units/Tyranids/LictorFlavor'/>"/>
	<entry name="Tyranids/Maleceptor" value="<string name='Units/Tyranids/Maleceptor'/>"/>
	<entry name="Tyranids/MaleceptorDescription" value="<string name='Units/Tyranids/MaleceptorDescription'/>"/>
	<entry name="Tyranids/MaleceptorFlavor" value="<string name='Units/Tyranids/MaleceptorFlavor'/>"/>
	<entry name="Tyranids/NornEmissary" value="<string name='Units/Tyranids/NornEmissary'/>"/>
	<entry name="Tyranids/NornEmissaryDescription" value="<string name='Units/Tyranids/NornEmissaryDescription'/>"/>
	<entry name="Tyranids/NornEmissaryFlavor" value="<string name='Units/Tyranids/NornEmissaryFlavor'/>"/>
	<entry name="Tyranids/Ravener" value="<string name='Units/Tyranids/Ravener'/>"/>
	<entry name="Tyranids/RavenerDescription" value="<string name='Units/Tyranids/RavenerDescription'/>"/>
	<entry name="Tyranids/RavenerFlavor" value="<string name='Units/Tyranids/RavenerFlavor'/>"/>
	<entry name="Tyranids/ScythedHierodule" value="<string name='Units/Tyranids/ScythedHierodule'/>"/>
	<entry name="Tyranids/ScythedHieroduleDescription" value="<string name='Units/Tyranids/ScythedHieroduleDescription'/>"/>
	<entry name="Tyranids/ScythedHieroduleFlavor" value="<string name='Units/Tyranids/ScythedHieroduleFlavor'/>"/>
	<entry name="Tyranids/Tervigon" value="<string name='Units/Tyranids/Tervigon'/>"/>
	<entry name="Tyranids/TervigonDescription" value="<string name='Units/Tyranids/TervigonDescription'/>"/>
	<entry name="Tyranids/TervigonFlavor" value="<string name='Units/Tyranids/TervigonFlavor'/>"/>
	<entry name="Tyranids/Trygon" value="<string name='Units/Tyranids/Trygon'/>"/>
	<entry name="Tyranids/TrygonDescription" value="<string name='Units/Tyranids/TrygonDescription'/>"/>
	<entry name="Tyranids/TrygonFlavor" value="<string name='Units/Tyranids/TrygonFlavor'/>"/>
	<entry name="Tyranids/Tyrannofex" value="<string name='Units/Tyranids/Tyrannofex'/>"/>
	<entry name="Tyranids/TyrannofexDescription" value="<string name='Units/Tyranids/TyrannofexDescription'/>"/>
	<entry name="Tyranids/TyrannofexFlavor" value="<string name='Units/Tyranids/TyrannofexFlavor'/>"/>
	<entry name="Tyranids/Venomthrope" value="<string name='Units/Tyranids/Venomthrope'/>"/>
	<entry name="Tyranids/VenomthropeDescription" value="<string name='Units/Tyranids/VenomthropeDescription'/>"/>
	<entry name="Tyranids/VenomthropeFlavor" value="<string name='Units/Tyranids/VenomthropeFlavor'/>"/>
	<entry name="Tyranids/Warrior" value="<string name='Units/Tyranids/Warrior'/>"/>
	<entry name="Tyranids/WarriorDescription" value="<string name='Units/Tyranids/WarriorDescription'/>"/>
	<entry name="Tyranids/WarriorFlavor" value="<string name='Units/Tyranids/WarriorFlavor'/>"/>
	<entry name="Tyranids/Zoanthrope" value="<string name='Units/Tyranids/Zoanthrope'/>"/>
	<entry name="Tyranids/ZoanthropeDescription" value="<string name='Units/Tyranids/ZoanthropeDescription'/>"/>
	<entry name="Tyranids/ZoanthropeFlavor" value="<string name='Units/Tyranids/ZoanthropeFlavor'/>"/>

	<!-- Other -->
	<entry name="SmokeLauncher" value="Smoke Launcher"/>
	<entry name="SmokeLauncherDescription" value="Grants ground vehicles the ability to launch a smoke screen that increases the ranged damage reduction."/>
	<entry name="SmokeLauncherFlavor" value="Some vehicles have small launchers mounted onto them that carry smoke canisters. These are used to temporarily obscure the vehicle behind billowing clouds of smoke, allowing it to cross open areas in greater safety—although it does so at the cost of being able to fire its own weapons."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegration'/>"/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Increases the building research output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegrationFlavor'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Increases the armour penetration of assault weapons."/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceDescription'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
	<entry name="AdeptusMechanicus/BlessedConduits" value="Blessed Conduits"/>
	<entry name="AdeptusMechanicus/BlessedConduitsDescription" value="Reduces the cooldown of Power Surge."/>
	<entry name="AdeptusMechanicus/BlessedConduitsFlavor" value="“And when at last he came upon the vehicle, he perceived the distress of the engine therein and forthwith struck the rune and it was good. Thereupon the engine ignited and was filled with strength…“<br/>—Lord of the Engines, 16th Tome, Verse 2001"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Ability for Ironstride Ballistarii, Sydonian Dragoons, Skorpius Dunerider, Onager Dunecrawler and Skorpius Disintegrator that reduces the morale loss of adjacent Adeptus Mechanicus units."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistDescription'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier2" value="<string name='Traits/AdeptusMechanicus/CityTier2'/>"/>
	<entry name="AdeptusMechanicus/CityTier2Description" value="<string name='Traits/AdeptusMechanicus/CityTier2Description'/>"/>
	<entry name="AdeptusMechanicus/CityTier2Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier2Flavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier3" value="<string name='Traits/AdeptusMechanicus/CityTier3'/>"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="<string name='Traits/AdeptusMechanicus/CityTier3Description'/>"/>
	<entry name="AdeptusMechanicus/CityTier3Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier3Flavor'/>"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubber" value="<string name='Weapons/CognisHeavyStubber'/>"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberDescription" value="Grants Onager Dunecrawlers a Cognis Heavy Stubber."/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberFlavor" value="<string name='Weapons/CognisHeavyStubberFlavor'/>"/>
	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Traits/AdeptusMechanicus/CommandUplink'/>"/>
	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Ability for Archaeopter Transvector and Archaeopter Stratoraptor that reduces the morale loss of adjacent Adeptus Mechanicus units."/>
	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Traits/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTether" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTether'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Increases the morale of Skitarii Vanguards and Skitarii Rangers."/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmour" value="Graian Protocols"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourFlavor" value="The integration of Graian protocols into a Skitarii’s command circuits further enhances their already impressive survivability. The Forge World of Graia is known for its refusal to ever yield, given that their logic is simply irrefutable."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmour" value="Agripinaan Ablation"/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourDescription" value="Increases the armour of vehicles."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourFlavor" value="Living by the Eye of Terror after the fall of Cadia has transformed the Adeptus Mechanicus on Agripinaa into defensive experts—particularly, when it comes to keeping their machines running under fire."/>
	<entry name="AdeptusMechanicus/FidorumVossPrime" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrime'/>"/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Increases the building influence output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrimeFlavor'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrathDescription" value="Grants Ironstrider Ballistarius, Sydonian Dragoons, Fulgurite Electro-Priests, Onager Dunecrawler, Pteraxii Sterylizors and Knight Crusader the ability to perform more devastating attacks."/>
	<entry name="AdeptusMechanicus/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Increases the armour penetration of heavy weapons."/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiah'/>"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Increases the building loyalty output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiahFlavor'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulDescription'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightDescription'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancer" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancer'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerDescription" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerDescription'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerFlavor" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerFlavor'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisation" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisation'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationDescription'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationFlavor'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/Omnispex" value="<string name='Traits/AdeptusMechanicus/Omnispex'/>"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="Allows Skitarii Vanguard and Skitarii Rangers to ignore ranged damage reduction."/>
	<entry name="AdeptusMechanicus/OmnispexFlavor" value="<string name='Traits/AdeptusMechanicus/OmnispexFlavor'/>"/>
	<entry name="AdeptusMechanicus/OptateRestrictions" value="<string name='Traits/AdeptusMechanicus/OptateRestrictions'/>"/>
	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Increases the population limit of Hab Fanes."/>
	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="<string name='Traits/AdeptusMechanicus/OptateRestrictionsFlavor'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocols'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Increases the growth rate of cities"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocolsFlavor'/>"/>
	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmDescription'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SolarReflectors" value="<string name='Traits/AdeptusMechanicus/SolarReflectors'/>"/>
	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Increases the building energy output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="<string name='Traits/AdeptusMechanicus/SolarReflectorsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeams'/>"/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Increases the building food output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeamsFlavor'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenment" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenment'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentDescription'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentFlavor'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralism" value="<string name='Traits/AdeptusMechanicus/TerranGeneralism'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismDescription'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismFlavor'/>"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiency" value="Thermo-Exchange Efficiency"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyDescription" value="Increases the bonus output of Power Surge."/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyFlavor" value="Paradoxically, slowing down the inputs to the Thermo-Exchanger and feeding the plasma through a second set of heat couplings and condensers can stretch the length of time it can operate beyond nominal goals—or at least reduce the amount of lives lost and repairs needed."/>
	<entry name="AdeptusMechanicus/TriplexNecessity" value="<string name='Traits/AdeptusMechanicus/TriplexNecessity'/>"/>
	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Increases the building ore output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="<string name='Traits/AdeptusMechanicus/TriplexNecessityFlavor'/>"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannon" value="<string name='Weapons/TwinLinkedIcarusAutocannon'/>"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonDescription" value="Grants Knight Crusaders a Twin-Linked Icarus Autocannon."/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonFlavor" value="<string name='Weapons/TwinLinkedIcarusAutocannonFlavor'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Increases the morale loss of enemy units adjacent to Sicarian Infiltrators."/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
	<entry name="AdeptusMechanicus/XenariteAcceptance" value="Xenarite Acceptance"/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceDescription" value="Increases the flat amount of research gained from Ruins of Vaul."/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceFlavor" value="It seems the Xenarite Tech-Priests from Stygies VIII are having their way on Gladius Prime too—setting up observation units and drones at every Outpost to understand the Xenos better. The practical applications in terms of research are immediately obvious…"/>
	<entry name="AstraMilitarum/AdditionalHeavyBolters" value="Additional Heavy Bolters"/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersDescription" value="Grants Baneblades, Leman Russ Battle Tanks, Rogal Dorn Battle Tanks and Valkyries extra Heavy Bolters."/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachine" value="<string name='Actions/AwakenTheMachine'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachineDescription" value="Grants Techpriest Enginseers the ability to boost the damage of vehicles."/>
	<entry name="AstraMilitarum/AwakenTheMachineFlavor" value="<string name='Actions/AwakenTheMachineFlavor'/>"/>
	<entry name="AstraMilitarum/BanebladeLascannon" value="Baneblade Lascannons"/>
	<entry name="AstraMilitarum/BanebladeLascannonDescription" value="Grants Baneblades extra lascannons."/>
	<entry name="AstraMilitarum/BanebladeLascannonFlavor" value="<string name='Weapons/LascannonFlavor'/>"/>
	<entry name="AstraMilitarum/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Increases the armour penetration of grenade, missile and blast weapons."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Increases the armour penetration of bolt weapons."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BruteShield" value="<string name='Traits/BruteShield'/>"/>
	<entry name="AstraMilitarum/BruteShieldDescription" value="Increases the damage and damage reduction of Bullgryns."/>
	<entry name="AstraMilitarum/BruteShieldFlavor" value="<string name='Traits/BruteShieldFlavor'/>"/>
	<entry name="AstraMilitarum/CamoNetting" value="<string name='Traits/CamoNetting'/>"/>
	<entry name="AstraMilitarum/CamoNettingDescription" value="Increases the ranged damage reduction of ground vehicles."/>
	<entry name="AstraMilitarum/CamoNettingFlavor" value="<string name='Traits/CamoNettingFlavor'/>"/>
	<entry name="AstraMilitarum/ChaffLauncher" value="Chaff Launcher"/>
	<entry name="AstraMilitarum/ChaffLauncherDescription" value="Grants Thunderbolts and Marauder Bombers the ability to launch chaffs that increase ranged damage reduction."/>
	<entry name="AstraMilitarum/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="AstraMilitarum/CityTier2" value="<string name='Traits/AstraMilitarum/CityTier2'/>"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Increases the tile acquisition radius of cities."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="<string name='Traits/AstraMilitarum/CityTier2Flavor'/>"/>
	<entry name="AstraMilitarum/CityTier3" value="<string name='Traits/AstraMilitarum/CityTier3'/>"/>
	<entry name="AstraMilitarum/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="<string name='Traits/AstraMilitarum/CityTier3Flavor'/>"/>
	<entry name="AstraMilitarum/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="AstraMilitarum/DozerBladeDescription" value="Reduces the movement penalty for tanks in forests and imperial ruins."/>
	<entry name="AstraMilitarum/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="AstraMilitarum/ExtraInfantryArmourFlavor" value="Most infantry in the 41st millenium are equipped with flak armour or its equivalent. If a commander wants their troops to have more than a slight chance of survival, they'll equip them with something closer to the Astra Militarum armaplas-derived carapace armour."/>
	<entry name="AstraMilitarum/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="AstraMilitarum/ExtraVehicleArmourDescription" value="Increases the armour of vehicles."/>
	<entry name="AstraMilitarum/ExtraVehicleArmourFlavor" value="Adding extra armour plates to tanks might be the height of heresy for Tech-Priests, but for the soldiers of the Astra Militarum it's the norm. Ablative plating or custom armour for dealing with particular weapons isn't unheard of."/>
	<entry name="AstraMilitarum/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="AstraMilitarum/FragGrenadeDescription" value="Grants Bullgryns, Field Ordnance Batteries, Guardsmen, Heavy Weapons Squads, Lord Commissars, Primaris Psykers, Techpriest Enginseers and Tempestus Scions the ability to throw anti-infantry grenades."/>
	<entry name="AstraMilitarum/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AstraMilitarum/HammerOfWrathDescription" value="Grants Scout Sentinels and Bullgryns the ability to perform more devastating attacks."/>
	<entry name="AstraMilitarum/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissileDescription" value="Grants ground vehicles the ability to fire hunter-killer missiles."/>
	<entry name="AstraMilitarum/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialSplendour" value="<string name='Traits/ImperialSplendour'/>"/>
	<entry name="AstraMilitarum/ImperialSplendourDescription" value="Increases the influence of Astra Militarum cities."/>
	<entry name="AstraMilitarum/ImperialSplendourFlavor" value="<string name='Traits/ImperialSplendourFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpoint" value="<string name='Units/AstraMilitarum/ImperialStrongpoint'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpointDescription" value="Grants Techpriest Enginseers the ability to construct fortifications with Heavy Bolters."/>
	<entry name="AstraMilitarum/ImperialStrongpointFlavor" value="<string name='Units/AstraMilitarum/ImperialStrongpointFlavor'/>"/>
	<entry name="AstraMilitarum/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="AstraMilitarum/KrakGrenadeDescription" value="Grants Field Ordnance Batteries, Guardsmen, Heavy Weapons Squads, Lord Commissars, Techpriest Enginseers and Tempestus Scions the ability to throw anti-armour grenades."/>
	<entry name="AstraMilitarum/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Increases the armour penetration of las weapons."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="AstraMilitarum/MediPack" value="<string name='Actions/MediPack'/>"/>
	<entry name="AstraMilitarum/MediPackDescription" value="Grants Guardsmen and Tempestus Scions the ability to heal themselves during combat."/>
	<entry name="AstraMilitarum/MediPackFlavor" value="<string name='Actions/MediPackFlavor'/>"/>
	<entry name="AstraMilitarum/Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="AstraMilitarum/MisfortuneDescription" value="Grants Wyrdvane Psykers the ability to curse enemy units so they take increased damage."/>
	<entry name="AstraMilitarum/MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="AstraMilitarum/RecoveryGear" value="<string name='Traits/RecoveryGear'/>"/>
	<entry name="AstraMilitarum/RecoveryGearDescription" value="Increases the healing rate for ground vehicles."/>
	<entry name="AstraMilitarum/RecoveryGearFlavor" value="<string name='Traits/RecoveryGearFlavor'/>"/>
	<entry name="AstraMilitarum/RelicPlating" value="<string name='Traits/RelicPlating'/>"/>
	<entry name="AstraMilitarum/RelicPlatingDescription" value="Increases the witchfire damage reduction of ground vehicles."/>
	<entry name="AstraMilitarum/RelicPlatingFlavor" value="<string name='Traits/RelicPlatingFlavor'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissile" value="<string name='Weapons/SkystrikeMissile'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissileDescription" value="Grants Thunderbolts anti-air missiles."/>
	<entry name="AstraMilitarum/SkystrikeMissileFlavor" value="<string name='Weapons/SkystrikeMissileFlavor'/>"/>
	<entry name="AstraMilitarum/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilots" value="<string name='Traits/TrainedSentinelPilots'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsDescription" value="Increases the damage of Scout Sentinels."/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsFlavor" value="<string name='Traits/TrainedSentinelPilotsFlavor'/>"/>
	<entry name="AstraMilitarum/VoidShieldGenerator" value="<string name='Units/AstraMilitarum/VoidShieldGenerator'/>"/>
	<entry name="AstraMilitarum/VoidShieldGeneratorDescription" value="Grants Techpriest Enginseers the ability to construct shield generators that grant ranged damage reduction to units in range."/>
	<entry name="AstraMilitarum/VoidShieldGeneratorFlavor" value="<string name='Units/AstraMilitarum/VoidShieldGeneratorFlavor'/>"/>
	<entry name="AstraMilitarum/VoxCaster" value="<string name='Traits/VoxCaster'/>"/>
	<entry name="AstraMilitarum/VoxCasterDescription" value="Reduces the morale loss for Guardsmen and Tempestus Scions."/>
	<entry name="AstraMilitarum/VoxCasterFlavor" value="<string name='Traits/VoxCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculum'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Grants Champions of Chaos the chance to gain permanently increased accuracy when killing an enemy (Boon of Chaos)."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculumFlavor'/>"/>
	<entry name="ChaosSpaceMarines/BlastDamage" value="<string name='Traits/ChaosSpaceMarines/BlastDamage'/>"/>
	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Increases the armour penetration of grenade, missile and blast weapons."/>
	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Bloated" value="<string name='Traits/ChaosSpaceMarines/Bloated'/>"/>
	<entry name="ChaosSpaceMarines/BloatedDescription" value="Grants Champions of Chaos the chance to restore their hitpoints when killing an enemy."/>
	<entry name="ChaosSpaceMarines/BloatedFlavor" value="<string name='Traits/ChaosSpaceMarines/BloatedFlavor'/>"/>
	<entry name="ChaosSpaceMarines/BoltDamage" value="<string name='Traits/ChaosSpaceMarines/BoltDamage'/>"/>
	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Increases the armour penetration of bolt weapons."/>
	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosRising" value="Chaos Rising"/>
	<entry name="ChaosSpaceMarines/ChaosRisingDescription" value="Reduces the cost of founding new cities."/>
	<entry name="ChaosSpaceMarines/ChaosRisingFlavor" value="The Imperial population of Gladius know little of Chaos, but in the hell of the Xenos invasions their faith in the distant Emperor has been shaken. It is easy for your cultists and apostles to turn their minds to the Gods—and to their doom."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="<string name='Traits/ChaosSpaceMarines/CityTier2'/>"/>
	<entry name="ChaosSpaceMarines/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier2Flavor'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3" value="<string name='Traits/ChaosSpaceMarines/CityTier3'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier3Flavor'/>"/>
	<entry name="ChaosSpaceMarines/CrystallineBody" value="<string name='Traits/ChaosSpaceMarines/CrystallineBody'/>"/>
	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Grants Champions of Chaos the chance to gain permanently increased hitpoints when killing an enemy (Boon of Chaos)."/>
	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="<string name='Traits/ChaosSpaceMarines/CrystallineBodyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Grants Chaos Rhinos, Defilers and Chaos Land Raiders the ability to prevent overwatch attacks."/>
	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourFlavor" value="Most infantry in the 41st millenium are equipped with flak armour or its equivalent. If a commander wants their troops to have more than a slight chance of survival, they'll equip them with something closer to the Astra Militarum armaplas-derived carapace armour."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourDescription" value="Increases the armour of vehicles."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourFlavor" value="Adding extra armour plates to tanks might be the height of heresy for Tech-Priests, but for the soldiers of the Astra Militarum it's the norm. Ablative plating or custom armour for dealing with particular weapons isn't unheard of."/>
	<entry name="ChaosSpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="ChaosSpaceMarines/FragGrenadeDescription" value="Grants Chaos Lords, Chaos Space Marines, Havocs, Khorne Berzerkers, Masters of Possession and Warpsmiths the ability to throw anti-infantry grenades."/>
	<entry name="ChaosSpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutation" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutation'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="Grants Chaos Lord, Chaos Space Marines, Chaos Terminators, Havocs, Khorne Berzerkers, Master of Possession, Warp Talons and Warpsmith one random unlocked Boon of Chaos."/>
	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutationFlavor'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrathDescription" value="Grants Daemon Princes, Defilers, Forgefiends, Greater Brass Scorpions, Helbrutes, Maulerfiends, Venomcrawlers and Warp Talons the ability to perform more devastating attacks."/>
	<entry name="ChaosSpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/HavocLauncher" value="<string name='Weapons/HavocLauncher'/>"/>
	<entry name="ChaosSpaceMarines/HavocLauncherDescription" value="Grants Chaos Rhinos and Chaos Land Raiders a mid-range blast weapon."/>
	<entry name="ChaosSpaceMarines/HavocLauncherFlavor" value="<string name='Weapons/HavocLauncherFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlame" value="<string name='Actions/ChaosSpaceMarines/IconOfFlame'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlameDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlameFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeance" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeance'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenadeDescription" value="Grants Chaos Lord, Chaos Space Marines, Havocs, Khorne Berzerkers, Master of Possession and Warpsmith the ability to throw anti-armour grenades."/>
	<entry name="ChaosSpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LasDamage" value="<string name='Traits/ChaosSpaceMarines/LasDamage'/>"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Increases the armour penetration of las and plasma weapons."/>
	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/LasDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Mechanoid" value="<string name='Traits/ChaosSpaceMarines/Mechanoid'/>"/>
	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Grants Champions of Chaos the chance to gain permanently increased armour when killing an enemy (Boon of Chaos)."/>
	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="<string name='Traits/ChaosSpaceMarines/MechanoidFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MeleeDamage" value="<string name='Traits/ChaosSpaceMarines/MeleeDamage'/>"/>
	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="ChaosSpaceMarines/MeltaBombDescription" value="Grants Chaos Lord, Chaos Space Marines, Havocs and Khorne Berzerkers the ability to deploy a melta bomb that is highly effective against heavy vehicles and fortifications."/>
	<entry name="ChaosSpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGods" value="Our Lives for the Gods"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsDescription" value="Increases the growth rate of Cultist Sacrifice."/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsFlavor" value="So many now lie dead at cultists’ blades on Gladius that each death is meaningless, empty, shallow. But it also increases the effect of their sacrifice, as they drag this world closer to the Dark Gods’ fallen hell dimension, and it becomes easier for dark boons to breach the barrier with the Immaterium."/>
	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
	<entry name="ChaosSpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="ChaosSpaceMarines/SmokeLauncherDescription" value="Grants Chaos Rhinos, Defilers and Chaos Land Raiders the ability to launch a smoke screen that increases the ranged damage reduction."/>
	<entry name="ChaosSpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="ChaosSpaceMarines/TemporalDistortion" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortion'/>"/>
	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Grants Champions of Chaos the chance to gain permanently increased movement when killing an enemy (Boon of Chaos)."/>
	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWar'/>"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Reduces the morale loss, and increases the melee accuracy of infantry against units of the Space Marines faction."/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWarFlavor'/>"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoyles" value="Warpflame Gargoyles"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesDescription" value="Grants damage over time to Chaos Rhino, Defiler and Chaos Land Raider weapons."/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesFlavor" value="The muzzles of this vehicle's guns flicker with unnatural fire."/>
	<entry name="ChaosSpaceMarines/WarpFrenzy" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzy'/>"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Grants Champions of Chaos the chance to gain permanently increased attacks when killing an enemy (Boon of Chaos)."/>
	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzyFlavor'/>"/>
	<entry name="CompendiumFlavor" value="Each faction expands its cities in a different way. Sometimes, it's an Ork Warboss joining his Boyz to literally lift and move his shielding walls further out, or perhaps a Necron Lord ordering his slaves to dig more of his ancient tomb from the planet's surface. However, they do it, it's necessary in order to make room for ingenious new buildings that expand their faction's capabilities."/>
	<entry name="Drukhari/AssaultWeaponBonus" value="<string name='Traits/Drukhari/AssaultWeaponBonus'/>"/>
	<entry name="Drukhari/AssaultWeaponBonusDescription" value="<string name='Upgrades/Eldar/AssaultWeaponBonusDescription'/>"/>
	<entry name="Drukhari/AssaultWeaponBonusFlavor" value="<string name='Traits/Drukhari/AssaultWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgrade" value="Targetted Assassinations"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="Increases the loyalty of Drukhari cities."/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="Death is a matter of course in the streets of a Drukhari city, where corpses lie slumped on every corner, waiting for the Ur-Ghuls to drag them away… but the deaths of Dracons and Kabal nobles are more rare. Or they were until the Archon’s incubi discovered yet another plot. Now even the slightest hint of disloyalty will result in another noble body on a spike above the Archon’s sleeping quarters…"/>
	<entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
	<entry name="Drukhari/BonusResourcesDescription" value="<string name='Actions/Drukhari/BonusResourcesDescription'/>"/>
	<entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
	<entry name="Drukhari/CityTier2" value="<string name='Traits/Drukhari/CityTier2'/>"/>
	<entry name="Drukhari/CityTier2Description" value="<string name='Traits/Drukhari/CityTier2Description'/>"/>
	<entry name="Drukhari/CityTier2Flavor" value="<string name='Traits/Drukhari/CityTier2Flavor'/>"/>
	<entry name="Drukhari/CityTier3" value="<string name='Traits/Drukhari/CityTier3'/>"/>
	<entry name="Drukhari/CityTier3Description" value="<string name='Traits/Drukhari/CityTier3Description'/>"/>
	<entry name="Drukhari/CityTier3Flavor" value="<string name='Traits/Drukhari/CityTier3Flavor'/>"/>
	<entry name="Drukhari/CombatDrugsUpgrade" value="Cult Cold Trader"/>
	<entry name="Drukhari/CombatDrugsUpgradeDescription" value="Grants all Drukhari infantry the abiltiy to use combat drugs."/>
	<entry name="Drukhari/CombatDrugsUpgradeFlavor" value="Though combat drugs are in widespread use throughout Drukhari society, it is the Wych Cults that are happiest using them, despite their deleterious effect on their physiology and lifespan. Having a Wych cult liaison with access to their huge store of horrifying compounds is a serious boon to an Kabal making realspace raids."/>
	<entry name="Drukhari/CorsairOutpostsUpgrade" value="A Soul Rush"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="Increases the growth by default of Drukhari cities."/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="Rumours are spreading like the plague in Commorragh: of the wealth of Gladius Prime; of its billion suffering humans trapped beneath the Warp Storm; and of the strange wraithbone core to the planet, filled with lost Aeldari souls… true or not, the Drukhari come. But who could have started those rumours..?"/>
	<entry name="Drukhari/EnergyBuildingBonus" value="<string name='Traits/Drukhari/EnergyBuildingBonus'/>"/>
	<entry name="Drukhari/EnergyBuildingBonusDescription" value="Increases the energy ouput of energy production buildings."/>
	<entry name="Drukhari/EnergyBuildingBonusFlavor" value="<string name='Traits/Drukhari/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
	<entry name="Drukhari/EnhancedAethersailsDescription" value="Grants Raider, Ravager and Tantalus the ability to increase their movement."/>
	<entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
	<entry name="Drukhari/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Drukhari/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="Drukhari/ExtraInfantryArmourFlavor" value="Equipping an entire army with Ghostplate would be ruinously expensive. But for the favoured few, the Archon has splashed out. Constructed of strange resinous materials and shot through with pockets of lighter-than-air gas, Ghostplate provides considerable protection whilst weighing almost nothing."/>
	<entry name="Drukhari/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Drukhari/ExtraVehicleArmourDescription" value="<string name='Traits/ExtraVehicleArmourDescription'/>"/>
	<entry name="Drukhari/ExtraVehicleArmourFlavor" value="Whether equipped with Flickerfields or Night Shields, the vehicles of the Drukhari are hard to spot and harder to target. Integrating Ghostplate into the more prized machines is expensive, but reduces their weight and increases their speed, further reducing the likelihood that they can be hit."/>
	<entry name="Drukhari/FieldRepairs" value="<string name='Actions/Drukhari/FieldRepairs'/>"/>
	<entry name="Drukhari/FieldRepairsDescription" value="Grants all Drukhari vehicles the ability to restore hitpoints."/>
	<entry name="Drukhari/FieldRepairsFlavor" value="<string name='Actions/Drukhari/FieldRepairsFlavor'/>"/>
	<entry name="Drukhari/GraveLotus" value="Grave Lotus"/>
	<entry name="Drukhari/GraveLotusDescription" value="Combat drugs grant additional melee damage."/>
	<entry name="Drukhari/GraveLotusFlavor" value="In Devil's Orchard, noisome hanging gardens of Grave Lotus sprout from a mosaic of the dead. A vivid purple fungus, the lotus steals the fading strength of the recently dead to further its own growth. The Wych Cults steal in their turn, imbibing the lotus in liquid form to boost their own physical powers."/>
	<entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
	<entry name="Drukhari/GrislyTrophiesDescription" value="Grants Venom, Raider, Ravager and Tantalus an aura that reduces the morale loss of adjacent allied Drukhari units."/>
	<entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
	<entry name="Drukhari/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Drukhari/HammerOfWrathDescription" value="Grants Cronos, Hellions, Reavers, Scourges and Talos the ability to perform more devastating attacks."/>
	<entry name="Drukhari/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Drukhari/HaywireGrenade" value="<string name='Weapons/HaywireGrenade'/>"/>
	<entry name="Drukhari/HaywireGrenadeDescription" value="Grants Kabalite Warriors, Succubus and Wyches the ability to throw anti-vehicle grenades."/>
	<entry name="Drukhari/HaywireGrenadeFlavor" value="<string name='Weapons/HaywireGrenadeFlavor'/>"/>
	<entry name="Drukhari/HeavyWeaponBonus" value="<string name='Traits/Drukhari/HeavyWeaponBonus'/>"/>
	<entry name="Drukhari/HeavyWeaponBonusDescription" value="Increases the armour penetration of heavy weapons."/>
	<entry name="Drukhari/HeavyWeaponBonusFlavor" value="<string name='Traits/Drukhari/HeavyWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/Hypex" value="Hypex"/>
	<entry name="Drukhari/HypexDescription" value="Combat drugs grant move through cover."/>
	<entry name="Drukhari/HypexFlavor" value="Capturing a Psychneuein is a perilous quest, but one who does so can sell it to the Wych Cults for a high price indeed. The drug Hypex, when distilled from the insectoid creature's cerebral fluids, boosts the already-sharp reaction speed of the Drukhari to truly astonishing levels."/>
	<entry name="Drukhari/MeleeWeaponBonus" value="<string name='Traits/Drukhari/MeleeWeaponBonus'/>"/>
	<entry name="Drukhari/MeleeWeaponBonusDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="Drukhari/MeleeWeaponBonusFlavor" value="<string name='Traits/Drukhari/MeleeWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/NightShields" value="<string name='Traits/Drukhari/NightShields'/>"/>
	<entry name="Drukhari/NightShieldsDescription" value="Increases the ranged damage reduction of Raiders, Ravagers, Razorwing Jetfighters, Tantalus and Voidraven Bombers."/>
	<entry name="Drukhari/NightShieldsFlavor" value="<string name='Traits/Drukhari/NightShieldsFlavor'/>"/>
	<entry name="Drukhari/Painbringer" value="Painbringer"/>
	<entry name="Drukhari/PainbringerDescription" value="Combat drugs grant feel no pain damage reduction."/>
	<entry name="Drukhari/PainbringerFlavor" value="Only the exiled Duke Sliscus can claim a steady supply of Painbringer. Amongst the rarest of all augmentative elixirs, it hardens the imbiber's skin into a flexible sheath as resilient as cured leather. This process is agonising in the extreme, though its advocate consider the pain a trivial price to pay."/>
	<entry name="Drukhari/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
	<entry name="Drukhari/PlasmaGrenadeDescription" value="Grants Archon, Scourges, Succubus and Wyches the ability to throw versatile grenades."/>
	<entry name="Drukhari/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
	<entry name="Drukhari/RaiderFortress" value="<string name='Actions/Drukhari/RaiderFortress'/>"/>
	<entry name="Drukhari/RaiderFortressDescription" value="Grants the ability to found new cities on claimed Webway Gates."/>
	<entry name="Drukhari/RaiderFortressFlavor" value="<string name='Actions/Drukhari/RaiderFortressFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsDamage" value="<string name='Traits/Drukhari/RaidersTacticsDamage'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageDescription" value="Increases the damage of infantry Drukhari units when they disembark from transport."/>
	<entry name="Drukhari/RaidersTacticsDamageFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageReduction" value="<string name='Traits/Drukhari/RaidersTacticsDamageReduction'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Increases the invulnerable damage reduction of infantry Drukhari units when they disembark from transport."/>
	<entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageReductionFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsHealingRate" value="<string name='Traits/Drukhari/RaidersTacticsHealingRate'/>"/>
	<entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Increases the healing rate of infantry units embarked in transport."/>
	<entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="<string name='Traits/Drukhari/RaidersTacticsHealingRateFlavor'/>"/>
	<entry name="Drukhari/SacrificeToKhaine" value="<string name='Traits/Drukhari/SacrificeToKhaine'/>"/>
	<entry name="Drukhari/SacrificeToKhaineDescription" value="<string name='Actions/Drukhari/SacrificeToKhaineDescription'/>"/>
	<entry name="Drukhari/SacrificeToKhaineFlavor" value="<string name='Traits/Drukhari/SacrificeToKhaineFlavor'/>"/>
	<entry name="Drukhari/ShroudGate" value="<string name='Traits/Drukhari/ShroudGate'/>"/>
	<entry name="Drukhari/ShroudGateDescription" value="Increases the ranged damage reduction of units passing through a webway gate or a webway portal."/>
	<entry name="Drukhari/ShroudGateFlavor" value="<string name='Traits/Drukhari/ShroudGateFlavor'/>"/>
	<entry name="Drukhari/SoulHungerCost" value="Soul Locus"/>
	<entry name="Drukhari/SoulHungerCostDescription" value="Reduces the cost of Soul Hunger abilities."/>
	<entry name="Drukhari/SoulHungerCostFlavor" value="As the Drukhari’s raid into Realspace becomes more entrenched, so the connections between this world and Commorragh become more and more numerous. Even when sealed physically, it’s still plausible for the Kabal to draw power from these connections and to feed stolen life back through them."/>
	<entry name="Drukhari/SoulHungerLoyalty" value="<string name='Traits/Drukhari/SoulHungerLoyalty'/>"/>
	<entry name="Drukhari/SoulHungerLoyaltyDescription" value="<string name='Actions/Drukhari/SoulHungerLoyaltyDescription'/>"/>
	<entry name="Drukhari/SoulHungerLoyaltyFlavor" value="<string name='Traits/Drukhari/SoulHungerLoyaltyFlavor'/>"/>
	<entry name="Drukhari/SoulHungerOutposts" value="<string name='Traits/Drukhari/SoulHungerOutposts'/>"/>
	<entry name="Drukhari/SoulHungerOutpostsDescription" value="<string name='Actions/Drukhari/SoulHungerOutpostsDescription'/>"/>
	<entry name="Drukhari/SoulHungerOutpostsFlavor" value="<string name='Traits/Drukhari/SoulHungerOutpostsFlavor'/>"/>
	<entry name="Drukhari/SoulHungerUpgrade" value="Soul Sadists"/>
	<entry name="Drukhari/SoulHungerUpgradeDescription" value="Increases the influence granted by Drukhari units when they kill an enemy."/>
	<entry name="Drukhari/SoulHungerUpgradeFlavor" value="“You must drug them. Kill the guards. Open the gate. Wake them carefully. Lead them out to freedom. Give them hope of escape. Start the pursuit in earnest. Fake your death. Show them an exit. Then reveal the charade. And return them to the torture chamber, grinning wickedly. Without touching them, you have imbued them with a rich, spiritual pain that will never leave.” – Gyrthineus Roche, Archon of The Last Blade"/>
	<entry name="Drukhari/FeastOfTorment" value="<string name='Traits/Drukhari/FeastOfTorment'/>"/>
	<entry name="Drukhari/FeastOfTormentDescription" value="<string name='Actions/Drukhari/FeastOfTormentDescription'/>"/>
	<entry name="Drukhari/FeastOfTormentFlavor" value="<string name='Traits/Drukhari/FeastOfTormentFlavor'/>"/>
	<entry name="Drukhari/SoulShelling" value="<string name='Actions/Drukhari/SoulShelling'/>"/>
	<entry name="Drukhari/SoulShellingDescription" value="<string name='Actions/Drukhari/SoulShellingDescription'/>"/>
	<entry name="Drukhari/SoulShellingFlavor" value="<string name='Actions/Drukhari/SoulShellingFlavor'/>"/>
	<entry name="Drukhari/Splintermind" value="Splintermind"/>
	<entry name="Drukhari/SplintermindDescription" value="Combat drugs grant morale loss reduction."/>
	<entry name="Drukhari/SplintermindFlavor" value="Splintermind is made from the ground crystal remains of a dead Eldar Farseer. Though it does not ensure prescience, this dust-like substance allows those who take it to think in several directions at once – an invaluable asset, for the confusion of battle takes its toll on even the most rigorous battle plan."/>
	<entry name="Drukhari/TormentGrenadeLaunchers" value="<string name='Weapons/TormentGrenadeLaunchers'/>"/>
	<entry name="Drukhari/TormentGrenadeLaunchersDescription" value="Grants Raiders, Ravagers and Tantalus Torment Grenade Launchers."/>
	<entry name="Drukhari/TormentGrenadeLaunchersFlavor" value="<string name='Weapons/TormentGrenadeLaunchersFlavor'/>"/>
	<entry name="Drukhari/WealthPlunder" value="<string name='Actions/Drukhari/WealthPlunder'/>"/>
	<entry name="Drukhari/WealthPlunderDescription" value="<string name='Actions/Drukhari/WealthPlunderDescription'/>"/>
	<entry name="Drukhari/WealthPlunderFlavor" value="<string name='Actions/Drukhari/WealthPlunderFlavor'/>"/>
	<entry name="Drukhari/WeaponRacks" value="<string name='Traits/Drukhari/WeaponRacks'/>"/>
	<entry name="Drukhari/WeaponRacksDescription" value="Grants twin-linked to the ranged weapons of units disembarking from a Raider or a Tantalus."/>
	<entry name="Drukhari/WeaponRacksFlavor" value="<string name='Traits/Drukhari/WeaponRacksFlavor'/>"/>
	<entry name="Drukhari/WebwayTravelAction" value="Realspace Irruption"/>
	<entry name="Drukhari/WebwayTravelActionDescription" value="Removes the action cost from Webway Travel."/>
	<entry name="Drukhari/WebwayTravelActionFlavor" value="Though passing through a webway gate is simplicity itself, co-ordinating the movements of a large army or formation can make them vulnerable. For the Drukhari, this is solved by the creation of temporary gates around any permanent portals, allowing the entire group to move rapidly through at once."/>
	<entry name="Eldar/AircraftBuildingBonus" value="<string name='Traits/Eldar/AircraftBuildingBonus'/>"/>
	<entry name="Eldar/AircraftBuildingBonusDescription" value="Increases the production output of Portal Spires."/>
	<entry name="Eldar/AircraftBuildingBonusFlavor" value="<string name='Traits/Eldar/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Eldar/AssaultWeaponBonus" value="<string name='Traits/Eldar/AssaultWeaponBonus'/>"/>
	<entry name="Eldar/AssaultWeaponBonusDescription" value="Increases the armour penetration of assault weapons."/>
	<entry name="Eldar/AssaultWeaponBonusFlavor" value="<string name='Traits/Eldar/AssaultWeaponBonusFlavor'/>"/>
	<entry name="Eldar/AsuryaniArrivalsBonus" value="Farseer's Summons"/>
	<entry name="Eldar/AsuryaniArrivalsBonusDescription" value="Reduces the cost of Asuryani Arrivals."/>
	<entry name="Eldar/AsuryaniArrivalsBonusFlavor" value="“My word is more than mere advice: it is an imprecation filtered through the consciousness of our storied dead, striving to bring one iota of order to the disordered universe. Our race understands that and listens to my word, not as that of a dictator but as an attentive student.”<br/>  — Farseer Kataimon of Malantai"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2" value="Craftworlds' Pledge"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Description" value="Reduces the cooldown of Asuryani Arrivals."/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Flavor" value="“When the Warlocks and Farseers of multiple craftworlds share a vision—a literal vision of the future—then they and their craftworlds work as one. They will denude their own halls of life and spirit to fulfil their pledge to that future.”<br/>  — Lecture transcripts, Grigomen Delr, Rogue Trader and amateur Xenologist"/>
	<entry name="Eldar/CityTier2" value="<string name='Traits/Eldar/CityTier2'/>"/>
	<entry name="Eldar/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Eldar/CityTier2Flavor" value="<string name='Traits/Eldar/CityTier2Flavor'/>"/>
	<entry name="Eldar/CityTier3" value="<string name='Traits/Eldar/CityTier3'/>"/>
	<entry name="Eldar/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Eldar/CityTier3Flavor" value="<string name='Traits/Eldar/CityTier3Flavor'/>"/>
	<entry name="Eldar/CleansingFlame" value="<string name='Actions/CleansingFlame'/>"/>
	<entry name="Eldar/CleansingFlameDescription" value="Grants Warlocks the ability to blast adjacent foes with white-hot psychic flame."/>
	<entry name="Eldar/CleansingFlameFlavor" value="<string name='Actions/CleansingFlameFlavor'/>"/>
	<entry name="Eldar/ConstructionBuildingBonus" value="<string name='Traits/Eldar/ConstructionBuildingBonus'/>"/>
	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Increases the production output of Bonesinger Chantries."/>
	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="<string name='Traits/Eldar/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Eldar/CrackShot" value="<string name='Traits/Eldar/CrackShot'/>"/>
	<entry name="Eldar/CrackShotDescription" value="Increases the accuracy and armour penetration of Fire Dragons."/>
	<entry name="Eldar/CrackShotFlavor" value="<string name='Traits/Eldar/CrackShotFlavor'/>"/>
	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Traits/Eldar/CrystalTargetingMatrix'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixDescription" value="Grants Fire Prisms, Hornets, Wave Serpents and War Walkers the ability to temporarily increase their accuracy."/>
	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Traits/Eldar/CrystalTargetingMatrixFlavor'/>"/>
	<entry name="Eldar/Dominate" value="<string name='Actions/Dominate'/>"/>
	<entry name="Eldar/DominateDescription" value="Grants Hemlock Wraithfighters the ability to stun enemy units that are neither vehicles nor fortifications."/>
	<entry name="Eldar/DominateFlavor" value="<string name='Actions/DominateFlavor'/>"/>
	<entry name="Eldar/ExpertHunter" value="<string name='Traits/Eldar/ExpertHunter'/>"/>
	<entry name="Eldar/ExpertHunterDescription" value="Increases the damage of Shining Spears against monstrous creatures, vehicles and fortifications."/>
	<entry name="Eldar/ExpertHunterFlavor" value="<string name='Traits/Eldar/ExpertHunterFlavor'/>"/>
	<entry name="Eldar/ExtraInfantryArmour" value="Labyrinthian Mesh Armour"/>
	<entry name="Eldar/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="Eldar/ExtraInfantryArmourFlavor" value="The Aeldari’s thermoplas mesh armour isn’t designed with a focus solely on defense—it also enables the user to move as freely as if it weren’t there. However, by further involuting its complex dissipation protocols, it is possible to increase its resilience with no loss of mobility."/>
	<entry name="Eldar/ExtraVehicleArmour" value="Wraithbone Infusion"/>
	<entry name="Eldar/ExtraVehicleArmourDescription" value="Increases the armour of vehicles."/>
	<entry name="Eldar/ExtraVehicleArmourFlavor" value="All Aeldari vehicles and structures are literally ‘sung’ into existence, by Bonesingers whose command of their vocal and psychic range is the constructive counterpart of the Howling Banshee’s war cry. Yet, with Gladius Prime’s resources and alteration in their internal harmonies, the Bonesingers can weave greater power into their creations, further increasing their resilience."/>
	<entry name="Eldar/FoodBuildingBonus" value="<string name='Traits/Eldar/FoodBuildingBonus'/>"/>
	<entry name="Eldar/FoodBuildingBonusDescription" value="Increases the food output of Isha's Gardens."/>
	<entry name="Eldar/FoodBuildingBonusFlavor" value="<string name='Traits/Eldar/FoodBuildingBonusFlavor'/>"/>
	<entry name="Eldar/GhostwalkMatrix" value="Ghostwalk Matrix"/>
	<entry name="Eldar/GhostwalkMatrixDescription" value="Allows Fire Prisms, Hornets, Wave Serpents and War Walkers to move through cover."/>
	<entry name="Eldar/GhostwalkMatrixFlavor" value="A ghostwalk matrix utilises the knowledge and wisdom contained within a spirit stone to guide the vehicle on its path."/>
	<entry name="Eldar/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Eldar/HammerOfWrathDescription" value="Grants Shining Spears, Farseer Skyrunners, Avatar of Khaine, War Walkers, Wraithlords and Wraithknights the ability to perform more devastating attacks."/>
	<entry name="Eldar/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Eldar/HeavyWeaponBonus" value="<string name='Traits/Eldar/HeavyWeaponBonus'/>"/>
	<entry name="Eldar/HeavyWeaponBonusDescription" value="Increases the armour penetration of heavy weapons."/>
	<entry name="Eldar/HeavyWeaponBonusFlavor" value="<string name='Traits/Eldar/HeavyWeaponBonusFlavor'/>"/>
	<entry name="Eldar/HoloFields" value="<string name='Traits/Eldar/HoloFields'/>"/>
	<entry name="Eldar/HoloFieldsDescription" value="Grants Fire Prisms, Hornets and Wave Serpents invulnerable damage reduction after having moved."/>
	<entry name="Eldar/HoloFieldsFlavor" value="<string name='Traits/Eldar/HoloFieldsFlavor'/>"/>
	<entry name="Eldar/InfantryBuildingBonus" value="<string name='Traits/Eldar/InfantryBuildingBonus'/>"/>
	<entry name="Eldar/InfantryBuildingBonusDescription" value="Increases the production output of Asuryan's Crucibles."/>
	<entry name="Eldar/InfantryBuildingBonusFlavor" value="<string name='Traits/Eldar/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Eldar/InfluenceBuildingBonus" value="<string name='Traits/Eldar/InfluenceBuildingBonus'/>"/>
	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Increases the influence output of Domes of the Seers."/>
	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="<string name='Traits/Eldar/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Eldar/MarksmansEye" value="<string name='Traits/Eldar/MarksmansEye'/>"/>
	<entry name="Eldar/MarksmansEyeDescription" value="Increases the accuracy of Crimson Hunters."/>
	<entry name="Eldar/MarksmansEyeFlavor" value="<string name='Traits/Eldar/MarksmansEyeFlavor'/>"/>
	<entry name="Eldar/MeleeWeaponBonus" value="<string name='Traits/Eldar/MeleeWeaponBonus'/>"/>
	<entry name="Eldar/MeleeWeaponBonusDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="Eldar/MeleeWeaponBonusFlavor" value="<string name='Traits/Eldar/MeleeWeaponBonusFlavor'/>"/>
	<entry name="Eldar/OreBuildingBonus" value="<string name='Traits/Eldar/OreBuildingBonus'/>"/>
	<entry name="Eldar/OreBuildingBonusDescription" value="Increases the ore output of Altmarls of Vaul."/>
	<entry name="Eldar/OreBuildingBonusFlavor" value="<string name='Traits/Eldar/OreBuildingBonusFlavor'/>"/>
	<entry name="Eldar/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
	<entry name="Eldar/PlasmaGrenadeDescription" value="Grants Guardians and Autarchs the ability to throw versatile grenades."/>
	<entry name="Eldar/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
	<entry name="Eldar/ResearchBuildingBonus" value="<string name='Traits/Eldar/ResearchBuildingBonus'/>"/>
	<entry name="Eldar/ResearchBuildingBonusDescription" value="Increases the research output of Spirit Ossuaries."/>
	<entry name="Eldar/ResearchBuildingBonusFlavor" value="<string name='Traits/Eldar/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Eldar/SpiritPreservationBonus" value="Escutcheon Mountings"/>
	<entry name="Eldar/SpiritPreservationBonusDescription" value="Increases the energy gain when Aeldari units die."/>
	<entry name="Eldar/SpiritPreservationBonusFlavor" value="For time immemorial, we have mounted our spirit stones in simple casings, trusting to the skill of their owners to preserve them. Yet, the ancient webway structures we have encountered on Gladius Prime demonstrate that a complex shielding can be built into any wraithbone casing, allowing us to snatch a greater number of our dead warriors from the maw of She Who Waits."/>
	<entry name="Eldar/SpiritStones" value="<string name='Traits/Eldar/SpiritStones'/>"/>
	<entry name="Eldar/SpiritStonesDescription" value="Reduces the morale loss of Fire Prisms, Hornets, War Walkers and Wave Serpents."/>
	<entry name="Eldar/SpiritStonesFlavor" value="<string name='Traits/Eldar/SpiritStonesFlavor'/>"/>
	<entry name="Eldar/StarEngines" value="<string name='Traits/Eldar/StarEngines'/>"/>
	<entry name="Eldar/StarEnginesDescription" value="Increases the movement of Fire Prisms, Hornets, Scorpions, War Walkers and Wave Serpents."/>
	<entry name="Eldar/StarEnginesFlavor" value="<string name='Traits/Eldar/StarEnginesFlavor'/>"/>
	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
	<entry name="Eldar/TranscendentBlissDescription" value="Grants cities the ability to spend influence for a temporary increase in loyalty."/>
	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
	<entry name="Eldar/TranscendentBlissBonus" value="Doom of the Aeldari"/>
	<entry name="Eldar/TranscendentBlissBonusDescription" value="Increases the loyalty gain from Transcendent Bliss."/>
	<entry name="Eldar/TranscendentBlissBonusFlavor" value="“To an unfettered Aeldari mind there is neither sanity nor madness, but merely a wave of perfect existence fulfilled by its own savage momentum.”<br/>  — Ralamine Mung, Ordo Xenos"/>
	<entry name="Eldar/VectoredEngines" value="<string name='Traits/Eldar/VectoredEngines'/>"/>
	<entry name="Eldar/VectoredEnginesDescription" value="Grants Fire Prisms, Hornets, War Walkers, Wave Serpents and Scorpions the ability to temporarily increase their armour against enemy ranged weapons."/>
	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Traits/Eldar/VectoredEnginesFlavor'/>"/>
	<entry name="Eldar/VehicleBuildingBonus" value="<string name='Traits/Eldar/VehicleBuildingBonus'/>"/>
	<entry name="Eldar/VehicleBuildingBonusDescription" value="Increases the production output of Greater Ingresses."/>
	<entry name="Eldar/VehicleBuildingBonusFlavor" value="<string name='Traits/Eldar/VehicleBuildingBonusFlavor'/>"/>
	<entry name="Eldar/WarShout" value="<string name='Actions/Eldar/WarShout'/>"/>
	<entry name="Eldar/WarShoutDescription" value="Grants Howling Banshees the ability to demoralize adjacent enemies."/>
	<entry name="Eldar/WarShoutFlavor" value="<string name='Actions/Eldar/WarShoutFlavor'/>"/>
	<entry name="Eldar/WebwayGateBonus" value="Webway Cartography"/>
	<entry name="Eldar/WebwayGateBonusDescription" value="Removes the cost of activating Webway Gates."/>
	<entry name="Eldar/WebwayGateBonusFlavor" value="Only the Harlequins and the custodians of the Black Library truly know anything of the Webway’s transdimensional layout. But by gifting us a portion of that knowledge, pertaining only to this world, the Librarians have ensured we can re-open these ancient gates with ease."/>
	<entry name="Eldar/WebwayGateBonus2" value="Webway Constriction"/>
	<entry name="Eldar/WebwayGateBonus2Description" value="Removes the action cost from Webway Travel."/>
	<entry name="Eldar/WebwayGateBonus2Flavor" value="Having received guidance from the Black Library on Gladius’ unique Webway layout, the spiritseers have planned out the shortest paths between any two gates, making the path between them as frictionless as any other step we might take."/>
	<entry name="Eldar/WebwayRedoubt" value="Webway Redoubt"/>
	<entry name="Eldar/WebwayRedoubtDescription" value="Grants the ability to found new cities on claimed Webway Gates."/>
	<entry name="Eldar/WebwayRedoubtFlavor" value="<string name='Actions/Eldar/WebwayRedoubtFlavor'/>"/>
	<entry name="Eldar/WraithknightStarcannon" value="Wraithknight Starcannons"/>
	<entry name="Eldar/WraithknightStarcannonDescription" value="Grants Wraithknights two Starcannons."/>
	<entry name="Eldar/WraithknightStarcannonFlavor" value="<string name='Weapons/StarcannonFlavor'/>"/>
	<entry name="Necrons/AircraftBuildingBonus" value="<string name='Traits/Necrons/AircraftBuildingBonus'/>"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Increases the production output of Nameless Causeways."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="<string name='Traits/Necrons/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Necrons/AttackCityBonus" value="<string name='Traits/Necrons/AttackCityBonus'/>"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Increases the accuracy of units against enemy units in cities."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="<string name='Traits/Necrons/AttackCityBonusFlavor'/>"/>
	<entry name="Necrons/BlastDamage" value="<string name='Traits/Necrons/BlastDamage'/>"/>
	<entry name="Necrons/BlastDamageDescription" value="Increases the armour penetration of blast and template weapons."/>
	<entry name="Necrons/BlastDamageFlavor" value="<string name='Traits/Necrons/BlastDamageFlavor'/>"/>
	<entry name="Necrons/CityDefenseBonus" value="Ineffable Obstacles"/>
	<entry name="Necrons/CityDefenseBonusDescription" value="Increases damage reduction of units in cities."/>
	<entry name="Necrons/CityDefenseBonusFlavor" value="Attacks made on the Tomb City's defenders now mysteriously fail—energy attacks fade, graviton weapons are deflected, and physical projectiles seem to be snatched from the sky by the tombs' Cyclopean walls."/>
	<entry name="Necrons/CityTier2" value="<string name='Traits/Necrons/CityTier2'/>"/>
	<entry name="Necrons/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier2Flavor" value="<string name='Traits/Necrons/CityTier2Flavor'/>"/>
	<entry name="Necrons/CityTier3" value="<string name='Traits/Necrons/CityTier3'/>"/>
	<entry name="Necrons/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier3Flavor" value="<string name='Traits/Necrons/CityTier3Flavor'/>"/>
	<entry name="Necrons/ConstructionBuildingBonus" value="<string name='Traits/Necrons/ConstructionBuildingBonus'/>"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Increases the production output of Slave Mastabas."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="<string name='Traits/Necrons/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor" value="<string name='Actions/Necrons/DimensionalCorridor'/>"/>
	<entry name="Necrons/DimensionalCorridorDescription" value="Grants infantry the ability to teleport to cities and Monoliths."/>
	<entry name="Necrons/DimensionalCorridorFlavor" value="<string name='Actions/Necrons/DimensionalCorridorFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor2" value="Dimensional Stability"/>
	<entry name="Necrons/DimensionalCorridor2Description" value="Reduces the influence cost of Dimensional Corridor."/>
	<entry name="Necrons/DimensionalCorridor2Flavor" value="Drawing upon the power of the Old One's abandoned technology has allowed the Necrons to stabilise their recall technology, greatly reducing the cost of warriors phasing out to Eternity Gates."/>
	<entry name="Necrons/DimensionalCorridor3" value="Dimensional Sanction"/>
	<entry name="Necrons/DimensionalCorridor3Description" value="Removes the action and movement cost of Dimensional Corridor."/>
	<entry name="Necrons/DimensionalCorridor3Flavor" value="A cunning Cryptek's sanction has allowed the Necrons to fine-tune their recall technology, heavily increasing the acquisition accuracy when they phase out."/>
	<entry name="Necrons/EnergyBuildingBonus" value="<string name='Traits/Necrons/EnergyBuildingBonus'/>"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Increases the energy ouput of Energy Cores."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="<string name='Traits/Necrons/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Necrons/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="Necrons/ExtraInfantryArmourFlavor" value="Even after millenia of war and sleep, the Necron Crypteks have not lost their desire for innovation. Making minor adaptations to their necrodermis, they are able to increase the survivability of their troops against the weapons of today."/>
	<entry name="Necrons/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Necrons/ExtraVehicleArmourDescription" value="Increases the armour of vehicles and Canoptek units."/>
	<entry name="Necrons/ExtraVehicleArmourFlavor" value="To an outside eye, these Necron vehicles are identical to their predecessors. To a Cryptek or perhaps even a learned Aeldari, they can see that the material of the vehicle has increased its resilience, somehow without affecting its density or weight."/>
	<entry name="Necrons/GaussDamage" value="<string name='Traits/GaussDamage'/>"/>
	<entry name="Necrons/GaussDamageDescription" value="Increases the armour penetration of gauss weapons."/>
	<entry name="Necrons/GaussDamageFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="Necrons/GaussPylon" value="<string name='Units/Necrons/GaussPylon'/>"/>
	<entry name="Necrons/GaussPylonDescription" value="Grants cities the ability to raise powerful gauss fortifications from the ground."/>
	<entry name="Necrons/GaussPylonFlavor" value="<string name='Units/Necrons/GaussPylonFlavor'/>"/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="Increases the witchfire damage reduction of Canoptek Spyders and adjacent allied units."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GrowthBonus" value="<string name='Traits/Necrons/GrowthBonus'/>"/>
	<entry name="Necrons/GrowthBonusDescription" value="Increases the growth rate of Necron cities."/>
	<entry name="Necrons/GrowthBonusFlavor" value="<string name='Traits/Necrons/GrowthBonusFlavor'/>"/>
	<entry name="Necrons/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Necrons/HammerOfWrathDescription" value="Grants Destroyer Lords, Tomb Blades, Canoptek Spyders, Transcendent C'tan, Triarch Praetorians and Triarch Stalkers the ability to perform more devastating attacks."/>
	<entry name="Necrons/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Necrons/HousingBuildingBonus" value="<string name='Traits/Necrons/HousingBuildingBonus'/>"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Increases the population limit of Shelters."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="<string name='Traits/Necrons/HousingBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfantryBuildingBonus" value="<string name='Traits/Necrons/InfantryBuildingBonus'/>"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Increases the production output of Summoning Cores."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="<string name='Traits/Necrons/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfluenceBuildingBonus" value="<string name='Traits/Necrons/InfluenceBuildingBonus'/>"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Increases the influence output of Stelae."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="<string name='Traits/Necrons/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Necrons/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="Necrons/LivingMetal2" value="Immortal Forms"/>
	<entry name="Necrons/LivingMetal2Description" value="Increases the healing of Living Metal."/>
	<entry name="Necrons/LivingMetal2Flavor" value="In some abstruse sense, the Necron's machines now remember their former glory and endlessly strive to regain it, flowing their living metal rapidly back to cover any damage."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="<string name='Traits/Necrons/LoyaltyBuildingBonus'/>"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Increases the loyalty output of Baroque Shrines."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="<string name='Traits/Necrons/LoyaltyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/MeleeDamage" value="<string name='Traits/Necrons/MeleeDamage'/>"/>
	<entry name="Necrons/MeleeDamageDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="Necrons/MeleeDamageFlavor" value="<string name='Traits/Necrons/MeleeDamageFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="<string name='Traits/Necrons/Nebuloscope'/>"/>
	<entry name="Necrons/NebuloscopeDescription" value="Allows Tomb Blades to ignore ranged damage reduction."/>
	<entry name="Necrons/NebuloscopeFlavor" value="<string name='Traits/Necrons/NebuloscopeFlavor'/>"/>
	<entry name="Necrons/NecrodermisRepair2" value="Accelerated Regrowth"/>
	<entry name="Necrons/NecrodermisRepair2Description" value="Increases the hitpoint restoration from Necrodermis Repair."/>
	<entry name="Necrons/NecrodermisRepair2Flavor" value="The Crypteks have improved the nanomechanical structure of the Necrons' living metal bodies once again, allowing them to recover from almost any damage."/>
	<entry name="Necrons/NecrodermisRepair3" value="Dire Necrodermis"/>
	<entry name="Necrons/NecrodermisRepair3Description" value="Removes the cooldown of Necrodermis Repair."/>
	<entry name="Necrons/NecrodermisRepair3Flavor" value="The living metal that constitutes the Necron bodies and vehicles now constantly writhes, rebuilding itself from moment to moment."/>
	<entry name="Necrons/OreBuildingBonus" value="<string name='Traits/Necrons/OreBuildingBonus'/>"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Increases the ore output of Al-Khemic Quarries."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="<string name='Traits/Necrons/OreBuildingBonusFlavor'/>"/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Grants Annihilation Barges, Ghost Arks, Doomsday Arks and Triarch Stalkers invulnerable damage reduction that goes on cooldown at the start of the next turn if damage is taken."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/RapidRiseBonus" value="The Lord's Command"/>
	<entry name="Necrons/RapidRiseBonusDescription" value="Reduces the cost of Rapid Rise."/>
	<entry name="Necrons/RapidRiseBonusFlavor" value="The Lord strengthens his patron-client protocols, so that his orders are carried out with more speed and less thought—if less free will."/>
	<entry name="Necrons/ReanimationProtocols2" value="Efficient Reanimation Protocols"/>
	<entry name="Necrons/ReanimationProtocols2Description" value="Increases the healing of Reanimation Protocols."/>
	<entry name="Necrons/ReanimationProtocols2Flavor" value="With these Cryptek-derived repair and revival systems, Necrons are even more likely to shrug off apparently-fatal damage."/>
	<entry name="Necrons/ResearchBuildingBonus" value="<string name='Traits/Necrons/ResearchBuildingBonus'/>"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Increases the research output of Forbidden Archives."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="<string name='Traits/Necrons/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ScarabHive" value="<string name='Actions/Necrons/ScarabHive'/>"/>
	<entry name="Necrons/ScarabHiveDescription" value="Grants Canoptek Spyders the ability to construct Canoptek Scarabs."/>
	<entry name="Necrons/ScarabHiveFlavor" value="<string name='Actions/Necrons/ScarabHiveFlavor'/>"/>
	<entry name="Necrons/SeismicAssault" value="Seismic Assault"/>
	<entry name="Necrons/SeismicAssaultDescription" value="Grants Transcendent C'tan and Tesseract Vaults the ability to perform a devastating strikedown."/>
	<entry name="Necrons/SeismicAssaultFlavor" value="<string name='Weapons/SeismicAssaultTranscendentFlavor'/>"/>
	<entry name="Necrons/ShieldVane" value="<string name='Traits/Necrons/ShieldVane'/>"/>
	<entry name="Necrons/ShieldVaneDescription" value="Increases the armour of Tomb Blades."/>
	<entry name="Necrons/ShieldVaneFlavor" value="<string name='Traits/Necrons/ShieldVaneFlavor'/>"/>
	<entry name="Necrons/TeslaDamage" value="<string name='Traits/TeslaDamage'/>"/>
	<entry name="Necrons/TeslaDamageDescription" value="Increases the armour penetration of tesla weapons."/>
	<entry name="Necrons/TeslaDamageFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="Necrons/TheBoundCoalescent" value="<string name='Actions/Necrons/TheBoundCoalescent'/>"/>
	<entry name="Necrons/TheBoundCoalescentDescription" value="Grants Transcendent C'tan the ability to merge with Obelisks to create Tesseract Vaults."/>
	<entry name="Necrons/TheBoundCoalescentFlavor" value="<string name='Actions/Necrons/TheBoundCoalescentFlavor'/>"/>
	<entry name="Necrons/VehiclesBuildingBonus" value="<string name='Traits/Necrons/VehiclesBuildingBonus'/>"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Increases the production output of Hypostyle Temples."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="<string name='Traits/Necrons/VehiclesBuildingBonusFlavor'/>"/>
	<entry name="Orks/AmmoRunt" value="<string name='Actions/AmmoRunt'/>"/>
	<entry name="Orks/AmmoRuntDescription" value="Grants Big Meks, Flash Gitz and Mek Gunz the ability to increase their ranged accuracy."/>
	<entry name="Orks/AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Orks/BattlewagonBigShootas" value="Battlewagon Big Shootas"/>
	<entry name="Orks/BattlewagonBigShootasDescription" value="Grants Battlwagons big shootas."/>
	<entry name="Orks/BattlewagonBigShootasFlavor" value="<string name='Weapons/BigShootaFlavor'/>"/>
	<entry name="Orks/BattlewagonRokkitLaunchas" value="Battlewagon Rokkit Launchas"/>
	<entry name="Orks/BattlewagonRokkitLaunchasDescription" value="Grants Battlewagons rokkit launchas."/>
	<entry name="Orks/BattlewagonRokkitLaunchasFlavor" value="<string name='Weapons/RokkitLaunchaFlavor'/>"/>
	<entry name="Orks/Bigbomm" value="<string name='Weapons/Bigbomm'/>"/>
	<entry name="Orks/BigbommDescription" value="Grants Deffkoptas the ability to drop anti-infantry bombs."/>
	<entry name="Orks/BigbommFlavor" value="<string name='Weapons/BigbommFlavor'/>"/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/Orks/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="Increases the armour penetration of grenade, missile and blast weapons."/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/Orks/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="<string name='Traits/Orks/BoltDamage'/>"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="<string name='Traits/Orks/BoltDamageFlavor'/>"/>
	<entry name="Orks/BonusBeastsProduction" value="<string name='Traits/Orks/BonusBeastsProduction'/>"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="<string name='Traits/Orks/BonusBeastsProductionDescription'/>"/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="<string name='Traits/Orks/BonusBeastsProductionFlavor'/>"/>
	<entry name="Orks/BonusColonizersProduction" value="<string name='Traits/Orks/BonusColonizersProduction'/>"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="<string name='Traits/Orks/BonusColonizersProductionDescription'/>"/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="<string name='Traits/Orks/BonusColonizersProductionFlavor'/>"/>
	<entry name="Orks/BonusInfantryProduction" value="<string name='Traits/Orks/BonusInfantryProduction'/>"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="<string name='Traits/Orks/BonusInfantryProductionDescription'/>"/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="<string name='Traits/Orks/BonusInfantryProductionFlavor'/>"/>
	<entry name="Orks/BonusVehiclesProduction" value="<string name='Traits/Orks/BonusVehiclesProduction'/>"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="<string name='Traits/Orks/BonusVehiclesProductionDescription'/>"/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="<string name='Traits/Orks/BonusVehiclesProductionFlavor'/>"/>
	<entry name="Orks/Bosspole" value="Bosspole"/>
	<entry name="Orks/BosspoleDescription" value="Further reduces the morale loss of Mob Rule for Big Meks, Boyz, Meganobz, Tankbustas, Warbikers and Warbosses."/>
	<entry name="Orks/BosspoleFlavor" value="Ork Nobz often sport a trophy pole that shows they are not to be messed with. A Nob with a Bosspole often finds it comes in handy when cracking heads to restore some order in the heat of battle."/>
	<entry name="Orks/CityEnergy" value="<string name='Traits/Orks/CityEnergy'/>"/>
	<entry name="Orks/CityEnergyDescription" value="Increases the energy output of Ork cities."/>
	<entry name="Orks/CityEnergyFlavor" value="<string name='Traits/Orks/CityEnergyFlavor'/>"/>
	<entry name="Orks/CityGrowth" value="<string name='Traits/Orks/CityGrowth'/>"/>
	<entry name="Orks/CityGrowthDescription" value="<string name='Traits/Orks/CityGrowthDescription'/>"/>
	<entry name="Orks/CityGrowthFlavor" value="<string name='Traits/Orks/CityGrowthFlavor'/>"/>
	<entry name="Orks/CityInfluence" value="<string name='Traits/Orks/CityInfluence'/>"/>
	<entry name="Orks/CityInfluenceDescription" value="Increases the influence output of Ork cities."/>
	<entry name="Orks/CityInfluenceFlavor" value="<string name='Traits/Orks/CityInfluenceFlavor'/>"/>
	<entry name="Orks/CityLoyalty" value="<string name='Traits/Orks/CityLoyalty'/>"/>
	<entry name="Orks/CityLoyaltyDescription" value="Increases the loyalty output of Ork cities."/>
	<entry name="Orks/CityLoyaltyFlavor" value="<string name='Traits/Orks/CityLoyaltyFlavor'/>"/>
	<entry name="Orks/CityPopulationLimit" value="<string name='Traits/Orks/CityPopulationLimit'/>"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Increases the population limit of Ork cities."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="<string name='Traits/Orks/CityPopulationLimitFlavor'/>"/>
	<entry name="Orks/CityResearch" value="<string name='Traits/Orks/CityResearch'/>"/>
	<entry name="Orks/CityResearchDescription" value="Increases the research output of Ork cities."/>
	<entry name="Orks/CityResearchFlavor" value="<string name='Traits/Orks/CityResearchFlavor'/>"/>
	<entry name="Orks/CityTier2" value="<string name='Traits/Orks/CityTier2'/>"/>
	<entry name="Orks/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier2Flavor" value="<string name='Traits/Orks/CityTier2Flavor'/>"/>
	<entry name="Orks/CityTier3" value="<string name='Traits/Orks/CityTier3'/>"/>
	<entry name="Orks/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier3Flavor" value="<string name='Traits/Orks/CityTier3Flavor'/>"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeath" value="Permanent Decomposition"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathDescription" value="Causes units to create permanent orkoid fungus when they die."/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathFlavor" value="<string name='Traits/Orks/CreateOrkoidFungusOnDeathFlavor'/>"/>
	<entry name="Orks/DakkajetSupaShoota" value="Dakkajet Supa Shoota"/>
	<entry name="Orks/DakkajetSupaShootaDescription" value="Grants Dakkajets a supa shoota."/>
	<entry name="Orks/DakkajetSupaShootaFlavor" value="<string name='Weapons/TwinLinkedSupaShootaFlavor'/>"/>
	<entry name="Orks/EavyArmour" value="<string name='Traits/EavyArmour'/>"/>
	<entry name="Orks/EavyArmourDescription" value="Increases the armour of Boyz and Warbosses."/>
	<entry name="Orks/EavyArmourFlavor" value="<string name='Traits/EavyArmourFlavor'/>"/>
	<entry name="Orks/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Orks/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="Orks/ExtraInfantryArmourFlavor" value="To any other race, Ork armour seems ridiculous: great lumps of thick metal plates, attached by cables, nails and nonsensical apparatuses, painted gold by lumpen Meks and grotz. But with a little bit of Ork belief, it works like a charm."/>
	<entry name="Orks/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Orks/ExtraVehicleArmourDescription" value="Increases the armour of vehicles."/>
	<entry name="Orks/ExtraVehicleArmourFlavor" value="When a Mek is faced with a Warboss angry that his buggies and battlewagons keep getting blown up, he'll rapidly order his grots to dig out some scrap and nail extra armour anywhere it fits. He knows the Speed Freeks will soon enough peel it off, after all."/>
	<entry name="Orks/Flyboss" value="<string name='Traits/Orks/Flyboss'/>"/>
	<entry name="Orks/FlybossDescription" value="Increases the ranged accuracy of Dakkajets against flyers, jetbikes and skimmers."/>
	<entry name="Orks/FlybossFlavor" value="<string name='Traits/Orks/FlybossFlavor'/>"/>
	<entry name="Orks/GrabbinKlaw" value="<string name='Actions/GrabbinKlaw'/>"/>
	<entry name="Orks/GrabbinKlawDescription" value="Grants Battlewagons the ability to immobilize enemy ground vehicles."/>
	<entry name="Orks/GrabbinKlawFlavor" value="<string name='Actions/GrabbinKlawFlavor'/>"/>
	<entry name="Orks/GrotRiggers" value="Grot Riggers"/>
	<entry name="Orks/GrotRiggersDescription" value="Grants Warbuggies, Killa Kans, Battlewagons, Deff Dreads, Gorkanauts and Kill Burstas passive hitpoint regeneration."/>
	<entry name="Orks/GrotRiggersFlavor" value="<string name='Traits/GrotRiggersFlavor'/>"/>
	<entry name="Orks/HealingRate" value="Green Flood"/>
	<entry name="Orks/HealingRateDescription" value="Increases the healing rate of units."/>
	<entry name="Orks/HealingRateFlavor" value="Through the co-operation of a team of Bad Doks and Runtherdz, your Warlord has prescribed a new, 'more Orky' diet, to make the boyz bigger and stronger, quicker. Whether the cold Oil Squigs for breakfast and Faceeater Squigs for dinner actually has any physical effect is immaterial—the boyz believe it, and that makes it work."/>
	<entry name="Orks/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Orks/HammerOfWrathDescription" value="Grants Deff Dreads, Deffkoptas, Gargantuan Squiggoths, Gorkanauts, Killa Kans and Warbikers the ability to perform more devastating attacks."/>
	<entry name="Orks/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="<string name='Traits/Orks/MeleeDamage'/>"/>
	<entry name="Orks/MeleeDamageDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="Orks/MeleeDamageFlavor" value="<string name='Traits/Orks/MeleeDamageFlavor'/>"/>
	<entry name="Orks/MightMakesRight2" value="Fear Da Orks!"/>
	<entry name="Orks/MightMakesRight2Description" value="Increases the influence gain of units dealing damage."/>
	<entry name="Orks/MightMakesRight2Flavor" value="When a Waaagh! is powerful enough, it has something like a multiplying effect, with every blow struck further enhancing the leaking psychic field, in a sort of ultra-violent Orky feedback loop."/>
	<entry name="Orks/OrkoidFungusBonusHealingRate" value="<string name='Traits/OrkoidFungusBonusHealingRate'/>"/>
	<entry name="Orks/OrkoidFungusBonusHealingRateDescription" value="Increases the healing from orkoid fungus."/>
	<entry name="Orks/OrkoidFungusBonusHealingRateFlavor" value="<string name='Traits/OrkoidFungusBonusHealingRateFlavor'/>"/>
	<entry name="Orks/OrkoidFungusFood" value="<string name='Traits/OrkoidFungusFood'/>"/>
	<entry name="Orks/OrkoidFungusFoodDescription" value="Increases the food output of tiles with orkoid fungus."/>
	<entry name="Orks/OrkoidFungusFoodFlavor" value="<string name='Traits/OrkoidFungusFoodFlavor'/>"/>
	<entry name="Orks/RedPaintJob" value="Red Paint Job"/>
	<entry name="Orks/RedPaintJobDescription" value="Increases the damage of Warbuggies, Megatrakk Scrapjets, Battlewagons, Dakkajets and Burna-Bommers."/>
	<entry name="Orks/RedPaintJobFlavor" value="<string name='Traits/RedPaintJobFlavor'/>"/>
	<entry name="Orks/Scavenger2" value="Grot Scavengers"/>
	<entry name="Orks/Scavenger2Description" value="Increases the amount of salvaged ore when killing enemy units."/>
	<entry name="Orks/Scavenger2Flavor" value="A well organised Runtherd will have trained his Grots to gather and sort scrap for the Orks, as well as avoiding the more dangerous shell casings and munitions."/>
	<entry name="Orks/SkorchaMissile" value="<string name='Weapons/SkorchaMissile'/>"/>
	<entry name="Orks/SkorchaMissileDescription" value="Grants Burna-Bommers anti-infantry missiles that ignore cover."/>
	<entry name="Orks/SkorchaMissileFlavor" value="<string name='Weapons/SkorchaMissileFlavor'/>"/>
	<entry name="Orks/Stikkbomb" value="<string name='Weapons/Stikkbomb'/>"/>
	<entry name="Orks/StikkbombDescription" value="Grants infantry the ability to throw anti-infantry grenades."/>
	<entry name="Orks/StikkbombFlavor" value="<string name='Weapons/StikkbombFlavor'/>"/>
	<entry name="Orks/TankbustaBomb" value="<string name='Weapons/TankbustaBomb'/>"/>
	<entry name="Orks/TankbustaBombDescription" value="Grants Tankbustas the ability to throw anti-armour bombs."/>
	<entry name="Orks/TankbustaBombFlavor" value="<string name='Weapons/TankbustaBombFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolter" value="Additional Heavy Bolter"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterDescription" value="Grants Immolators, Exorcists and Castigators one additional Heavy Bolter."/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalSacredRite" value="Doubled Faith"/>
	<entry name="SistersOfBattle/AdditionalSacredRiteDescription" value="Two Sacred Rites can be simultaneously active."/>
	<entry name="SistersOfBattle/AdditionalSacredRiteFlavor" value="“Once, our ritual chorals were singular—solos or harmonies sung in sacred voice. Today, we weave our hymns together, to amplify their dual themes, so that we may praise the God-Emperor in as many ways as possible.”<br/>  — Unknown remembrancer, Gospel of the Webway"/>
	<entry name="SistersOfBattle/AircraftsMissiles" value="Aircraft Missiles"/>
	<entry name="SistersOfBattle/AircraftsMissilesDescription" value="Grants Skystrike Missiles to Lightning Fighters and Hellstrike Missiles to Avenger Strike Fighters."/>
	<entry name="SistersOfBattle/AircraftsMissilesFlavor" value="These missiles allow the aircrafts to effectively engage a variety of targets; Hellstrike missiles for missions targeting armoured vehicles, Skystrike missiles for enemy air forces."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagus'/>"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Increases the armour of Mortifiers."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagusFlavor'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonus'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Increases the armour penetration of assault weapons."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/AvengeTheMartyrs" value="Avenge the Martyrs"/>
	<entry name="SistersOfBattle/AvengeTheMartyrsDescription" value="Increases the morale loss reduction from Avenging Zeal."/>
	<entry name="SistersOfBattle/AvengeTheMartyrsFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/ChaffLauncher" value="Chaff Launcher"/>
	<entry name="SistersOfBattle/ChaffLauncherDescription" value="Grants Lightning Fighters and Avenger Strike Fighters the ability to launch chaffs that increase ranged damage reduction."/>
	<entry name="SistersOfBattle/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfane" value="<string name='Actions/SistersOfBattle/ChaseTheProfane'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneDescription" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneDescription'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneFlavor" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="<string name='Traits/SistersOfBattle/CityGrowth'/>"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Increases the growth rate of cities."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="<string name='Traits/SistersOfBattle/CityGrowthFlavor'/>"/>
	<entry name="SistersOfBattle/CityTier2" value="<string name='Traits/SistersOfBattle/CityTier2'/>"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Increases the tile acquisition radius of the main city."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="<string name='Traits/SistersOfBattle/CityTier2Flavor'/>"/>
	<entry name="SistersOfBattle/CityTier3" value="<string name='Traits/SistersOfBattle/CityTier3'/>"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Increases the tile acquisition radius of the main city."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="<string name='Traits/SistersOfBattle/CityTier3Flavor'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaith" value="<string name='Actions/SistersOfBattle/ConvictionOfFaith'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithDescription" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithFlavor" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SistersOfBattle/DozerBladeDescription" value="Reduces the movement penalty for Immolators, Exorcists and Castigators in forests and imperial ruins."/>
	<entry name="SistersOfBattle/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="<string name='Actions/SistersOfBattle/EternalCrusade'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="<string name='Actions/SistersOfBattle/EternalCrusadeDescription'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="<string name='Actions/SistersOfBattle/EternalCrusadeFlavor'/>"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Expert Fighters"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Increases the accuracy of Lightning Fighters and Avenger Strike Fighters."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="<string name='Traits/SistersOfBattle/ExpertFightersFlavor'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="SistersOfBattle/ExtraInfantryArmourFlavor" value="Constructed from thick ceramite plates, the power armour worn by the Adepta Sororitas is based upon the same archaic systems as that worn by the brethren of the Adeptus Astartes. It provides the same degree of armoured protection, yet must forego the more advanced support systems and strength enhancing abilities, for the Battle Sisters do not possess a Space Marine’s ability to interface directly with their own armour."/>
	<entry name="SistersOfBattle/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SistersOfBattle/ExtraVehicleArmourDescription" value="Increases the armour of vehicles."/>
	<entry name="SistersOfBattle/ExtraVehicleArmourFlavor" value="The vehicles of the Adepta Sororitas are demonstrations of faith, so it is crucial to maintain and protect the exposed artefacts they bear. So preserved, these vehicles will more easily cover the advance of the order Militants of the Adepta Sororitas."/>
	<entry name="SistersOfBattle/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SistersOfBattle/FragGrenadeDescription" value="Grants Battle Sisters, Canonesses, Celestian Sacresants, Dialogus, Dominions, Hospitallers, Imagifiers, Paragon Warsuits, Retributors, Saint Celestine, Sisters Repentia, Zephyrims the ability to throw anti-infantry grenades."/>
	<entry name="SistersOfBattle/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/HammerOfWrath" value="<string name='Traits/HammerOfWrath'/>"/>
	<entry name="SistersOfBattle/HammerOfWrathDescription" value="Grants Cerastus Knight-Lancer, Mortifiers, Paragon Warsuits, Saint Celestine and Zephyrim the ability to perform more devastating attacks."/>
	<entry name="SistersOfBattle/HammerOfWrathFlavor" value="<string name='Traits/HammerOfWrathFlavor'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonus'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Increases the armour penetration of heavy weapons."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SistersOfBattle/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SistersOfBattle/KrakGrenadeDescription" value="Grants Battle Sisters, Canonesses, Dialogus, Dominions, Hospitallers, Imagifiers, Retributors, Sisters Repentia and Zephyrims the ability to throw anti-armour grenades."/>
	<entry name="SistersOfBattle/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/LaudHailer" value="<string name='Traits/SistersOfBattle/LaudHailer'/>"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="Grants Castigators, Exorcists and Immolators an aura allowing adjacent shaken units to perform Acts of Faith."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Traits/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Traits/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Grants feel no pain damage reduction to allied infantry units adjacent to a Hospitaller unit."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Traits/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonus'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SistersOfBattle/MeltaBombDescription" value="Grants Battle Sisters, Canonesses, Dialogus, Dominions, Retributors, Sisters Repentia and Zephyrims the ability to deploy a melta bomb that is highly effective against heavy vehicles and fortifications."/>
	<entry name="SistersOfBattle/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SistersOfBattle/MinistorumIndoctrination" value="Ministorum Indoctrination"/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationDescription" value="Grants Martyr Spirit to Lightning Fighters, Avengers Strike Fighters and Cerastus Knight-Lancers."/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationFlavor" value="“Through long exposure to our rituals and prayers, our allies of fortune—the Imperial Navy and the handful of Imperial Knight-Lancers who had survived—began to take part in our rites and internalize our beliefs too. Faith, it appears, is contagious.”<br/>  — Unknown remembrancer, Gospel of the Webway"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisation'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationDescription'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationFlavor'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="<string name='Actions/SistersOfBattle/PurifyingRecitations'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsDescription'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsFlavor'/>"/>
	<entry name="SistersOfBattle/RagingFervour" value="<string name='Actions/SistersOfBattle/RagingFervour'/>"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="<string name='Actions/SistersOfBattle/RagingFervourDescription'/>"/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="<string name='Actions/SistersOfBattle/RagingFervourFlavor'/>"/>
	<entry name="SistersOfBattle/RitualizedCeremonies" value="Ritualized Ceremonies"/>
	<entry name="SistersOfBattle/RitualizedCeremoniesDescription" value="Reduces the cost of Sacred Rites."/>
	<entry name="SistersOfBattle/RitualizedCeremoniesFlavor" value="“At first, we prayed on the move, in moments of peace in ruins or caves. In time, as the conflict stabilized into endless, grinding war, our rituals and prayers stabilized too, to form a regime and routine.”<br/>  — Unknown remembrancer, Gospel of the Webway"/>
	<entry name="SistersOfBattle/SacralVigor" value="<string name='Actions/SistersOfBattle/SacralVigor'/>"/>
	<entry name="SistersOfBattle/SacralVigorDescription" value="<string name='Actions/SistersOfBattle/SacralVigorDescription'/>"/>
	<entry name="SistersOfBattle/SacralVigorFlavor" value="<string name='Actions/SistersOfBattle/SacralVigorFlavor'/>"/>
	<entry name="SistersOfBattle/SanctifiedWorld" value="Sanctified World"/>
	<entry name="SistersOfBattle/SanctifiedWorldDescription" value="Increases the loyalty bonus granted by Convent of Faith."/>
	<entry name="SistersOfBattle/SanctifiedWorldFlavor" value="Most Orders spread the illumination of the God-Emperor to worlds far beyond their primary sanctuaries, establishing far-flung missions and subsidiary chapels to extend the influence of their Order and the Ecclesiarchy."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="<string name='Traits/SistersOfBattle/SimulacrumImperialis'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisDescription'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisFlavor'/>"/>
	<entry name="SistersOfBattle/SisterSuperior" value="<string name='Traits/SistersOfBattle/SisterSuperior'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="<string name='Traits/SistersOfBattle/SisterSuperiorDescription'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="<string name='Traits/SistersOfBattle/SisterSuperiorFlavor'/>"/>
	<entry name="SistersOfBattle/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SistersOfBattle/UniversalCatechism" value="Universal Catechism"/>
	<entry name="SistersOfBattle/UniversalCatechismDescription" value="Grants Shield of Faith to Lightning Fighters, Avengers Strike Fighters and Cerastus Knight-Lancers."/>
	<entry name="SistersOfBattle/UniversalCatechismFlavor" value="Imperial troops requisitioned for the Wars of Faith often end up praying side-by-side with the sisters of the Adepta Sororitas before each battle, finding guidance in their convictions."/>
	<entry name="SistersOfBattle/VengefulSpirit" value="<string name='Actions/SistersOfBattle/VengefulSpirit'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="<string name='Actions/SistersOfBattle/VengefulSpiritDescription'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="<string name='Actions/SistersOfBattle/VengefulSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrder" value="Vow of the Militant Order"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderDescription" value="Units keep their Shield of Faith if they are broken."/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderFlavor" value="<string name='Traits/SistersOfBattle/ShieldOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="<string name='Actions/SistersOfBattle/WarmachinesWrath'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="<string name='Actions/SistersOfBattle/WarmachinesWrathDescription'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="<string name='Actions/SistersOfBattle/WarmachinesWrathFlavor'/>"/>
	<entry name="SpaceMarines/AssaultDoctrine" value="<string name='Traits/AssaultDoctrine'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineDescription" value="<string name='Actions/AssaultDoctrineDescription'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineFlavor" value="<string name='Traits/AssaultDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/SpaceMarines/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="Increases the armour penetration of grenade, missile and blast weapons."/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/SpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="SpaceMarines/BolsterDefencesDescription" value="Grants Thunderfire Cannons the ability to increase the ranged damage reduction of the target tile."/>
	<entry name="SpaceMarines/BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/SpaceMarines/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/SpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDrill" value="<string name='Traits/BolterDrill'/>"/>
	<entry name="SpaceMarines/BolterDrillDescription" value="<string name='Actions/BolterDrillDescription'/>"/>
	<entry name="SpaceMarines/BolterDrillFlavor" value="<string name='Traits/BolterDrillFlavor'/>"/>
	<entry name="SpaceMarines/CeramitePlating" value="<string name='Traits/CeramitePlating'/>"/>
	<entry name="SpaceMarines/CeramitePlatingDescription" value="Increases the armour of Stormraven and Stormtalon Gunships."/>
	<entry name="SpaceMarines/CeramitePlatingFlavor" value="<string name='Traits/CeramitePlatingFlavor'/>"/>
	<entry name="SpaceMarines/ChapterUnity" value="<string name='Traits/ChapterUnity'/>"/>
	<entry name="SpaceMarines/ChapterUnityDescription" value="Increases the loyalty output of the Great Hall."/>
	<entry name="SpaceMarines/ChapterUnityFlavor" value="<string name='Traits/ChapterUnityFlavor'/>"/>
	<entry name="SpaceMarines/CityTier2" value="<string name='Traits/SpaceMarines/CityTier2'/>"/>
	<entry name="SpaceMarines/CityTier2Description" value="Increases the tile acquisition radius of the city."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="<string name='Traits/SpaceMarines/CityTier2Flavor'/>"/>
	<entry name="SpaceMarines/CityTier3" value="<string name='Traits/SpaceMarines/CityTier3'/>"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="<string name='Traits/SpaceMarines/CityTier3Flavor'/>"/>
	<entry name="SpaceMarines/CityTier4" value="<string name='Traits/SpaceMarines/CityTier4'/>"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="<string name='Traits/SpaceMarines/CityTier4Flavor'/>"/>
	<entry name="SpaceMarines/ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="SpaceMarines/ClusterMinesDescription" value="Grants Scout Bikers the ability to place cluster mines."/>
	<entry name="SpaceMarines/ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="SpaceMarines/CombatShield" value="<string name='Traits/CombatShield'/>"/>
	<entry name="SpaceMarines/CombatShieldDescription" value="Increases damage reduction of Assault Space Marines."/>
	<entry name="SpaceMarines/CombatShieldFlavor" value="<string name='Traits/CombatShieldFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrine" value="<string name='Traits/DevastatorDoctrine'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineDescription" value="<string name='Actions/DevastatorDoctrineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineFlavor" value="<string name='Traits/DevastatorDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SpaceMarines/DozerBladeDescription" value="Reduces the movement penalty for Hunters, Predators, Razorbacks and Whirlwinds in forests and imperial ruins."/>
	<entry name="SpaceMarines/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourFlavor" value="Whilst purity seals may well keep an Adeptus Astartes in the field for longer, faith doesn't stop all bolts. By upgrading to later versions of their Power Armour, Space Marine infantry improve their survivability substantially."/>
	<entry name="SpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraVehicleArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourFlavor" value="When the proper unguents have been spread and blessings made, a Techmarine may well make some minor changes to his charges, in line with Codex Astartes of course, to improve survivability."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Stronghold Shielding"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Grants Fortresses of Redemption an invulnerable damage reduction."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="<string name='Traits/SpaceMarines/FortressOfRedemptionDamageReductionFlavor'/>"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSilo" value="Fortress of Redemption Missile Silo"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloDescription" value="Grants Fortresses of Redemption a krakstorm missile silo."/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloFlavor" value="<string name='Weapons/KrakstormMissileSiloFlavor'/>"/>
	<entry name="SpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SpaceMarines/FragGrenadeDescription" value="Grants Apothecaries, Assault Space Marines, Captains, Devastator Space Marines, Librarians, Scouts, Scout Bikers, Tactical Space Marines and Thunderfire Cannons the ability to throw anti-infantry grenades."/>
	<entry name="SpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="SpaceMarines/HammerOfWrathDescription" value="Grants Assault Space Marines, Dreadnoughts and Scout Bikers the ability to perform more devastating attacks."/>
	<entry name="SpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="SpaceMarines/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SpaceMarines/HurricaneBolter" value="Hurricane Bolter"/>
	<entry name="SpaceMarines/HurricaneBolterDescription" value="Grants Stormraven Gunships a hurricane bolter."/>
	<entry name="SpaceMarines/HurricaneBolterFlavor" value="First used by the Black Templars Chapter, hurricane bolters combine the punishing firepower of multiple twin-linked boltguns to produce a truly withering storm of shells."/>
	<entry name="SpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SpaceMarines/KrakGrenadeDescription" value="Grants Apothecaries, Assault Space Marines, Captains, Devastator Space Marines, Librarians, Scouts, Scout Bikers, Tactical Space Marines and Thunderfire Cannons the ability to throw anti-armour grenades."/>
	<entry name="SpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMelta" value="<string name='Weapons/MultiMelta'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaDescription" value="Grants Land Speeders and Land Raiders a multi-melta weapon."/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaFlavor" value="<string name='Weapons/MultiMeltaFlavor'/>"/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/SpaceMarines/LasDamage'/>"/>
	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Upgrades/AstraMilitarum/LasDamageDescription'/>"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/SpaceMarines/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/LastStand" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LastStandDescription" value="Increases the morale of all Space Marine units."/>
	<entry name="SpaceMarines/LastStandFlavor" value="<string name='Traits/LastStandFlavor'/>"/>
	<entry name="SpaceMarines/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LocatorBeacon" value="<string name='Traits/LocatorBeacon'/>"/>
	<entry name="SpaceMarines/LocatorBeaconDescription" value="Causes orbital deployment to not consume action points when deploying adjacent to Scout Bikers or Stormraven Gunships."/>
	<entry name="SpaceMarines/LocatorBeaconFlavor" value="<string name='Traits/LocatorBeaconFlavor'/>"/>
	<entry name="SpaceMarines/MachineEmpathy" value="<string name='Traits/MachineEmpathy'/>"/>
	<entry name="SpaceMarines/MachineEmpathyDescription" value="<string name='Actions/MachineEmpathyDescription'/>"/>
	<entry name="SpaceMarines/MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="<string name='Traits/SpaceMarines/MeleeDamage'/>"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="<string name='Traits/SpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SpaceMarines/MeltaBombDescription" value="Grants Tactical Space Marines, Assault Space Marines, Devastator Space Marines, Scouts and Scout Bikers the ability to deploy a melta bomb that is highly effective against heavy vehicles and fortifications."/>
	<entry name="SpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
	<entry name="SpaceMarines/OmniscopeDescription" value="Grants Devastator Centurions the ability to bypass ranged damaged reduction."/>
	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalBombardment" value="<string name='Actions/OrbitalBombardment'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentDescription" value="<string name='Actions/OrbitalBombardmentDescription'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentFlavor" value="<string name='Actions/OrbitalBombardmentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalDeployment" value="<string name='Actions/OrbitalDeployment'/>"/>
	<entry name="SpaceMarines/OrbitalDeploymentDescription" value="Grants units the ability to deploy via drop pod anywhere on the battlefield."/>
	<entry name="SpaceMarines/OrbitalDeploymentFlavor" value="<string name='Actions/OrbitalDeploymentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalScan" value="<string name='Actions/OrbitalScan'/>"/>
	<entry name="SpaceMarines/OrbitalScanDescription" value="<string name='Actions/OrbitalScanDescription'/>"/>
	<entry name="SpaceMarines/OrbitalScanFlavor" value="<string name='Actions/OrbitalScanFlavor'/>"/>
	<entry name="SpaceMarines/PredatorLascannon" value="Additional Heavy Bolters"/>
	<entry name="SpaceMarines/PredatorLascannonDescription" value="Grants Fortresses of Redemption, Predators and Aquila Macro-Cannons extra Heavy Bolters."/>
	<entry name="SpaceMarines/PredatorLascannonFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SpaceMarines/SiegeMasters" value="<string name='Traits/SiegeMasters'/>"/>
	<entry name="SpaceMarines/SiegeMastersDescription" value="<string name='Actions/SiegeMastersDescription'/>"/>
	<entry name="SpaceMarines/SiegeMastersFlavor" value="<string name='Actions/SiegeMastersFlavor'/>"/>
	<entry name="SpaceMarines/SiegeShield" value="<string name='Traits/SiegeShield'/>"/>
	<entry name="SpaceMarines/SiegeShieldDescription" value="Increases the armour of Vindicators and reduces the movement penalty in forests and imperial ruins."/>
	<entry name="SpaceMarines/SiegeShieldFlavor" value="<string name='Traits/SiegeShieldFlavor'/>"/>
	<entry name="SpaceMarines/Signum" value="<string name='Actions/Signum'/>"/>
	<entry name="SpaceMarines/SignumDescription" value="Grants Devastator Space Marines the ability to negate the penalty for heavy, ordnance and salvo weapons."/>
	<entry name="SpaceMarines/SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SpaceMarines/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SpaceMarines/TacticalDoctrine" value="<string name='Traits/TacticalDoctrine'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineDescription" value="<string name='Actions/TacticalDoctrineDescription'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/TeleportHomer" value="<string name='Traits/TeleportHomer'/>"/>
	<entry name="SpaceMarines/TeleportHomerDescription" value="Causes orbital deployment to not consume action points when deploying Chaplain, Assault Terminators and Terminators adjacent to Tactical Space Marines or Scouts."/>
	<entry name="SpaceMarines/TeleportHomerFlavor" value="<string name='Traits/TeleportHomerFlavor'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeak" value="<string name='Traits/TheFleshIsWeak'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakDescription" value="<string name='Actions/TheFleshIsWeakDescription'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="Tau/AdvancedTargetingSystem" value="<string name='Traits/Tau/AdvancedTargetingSystem'/>"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Battlesuit support system and vehicle upgrade that increases the ranged accuracy."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="<string name='Traits/Tau/AdvancedTargetingSystemFlavor'/>"/>
	<entry name="Tau/AutomatedRepairSystem" value="<string name='Traits/Tau/AutomatedRepairSystem'/>"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Grants vehicles hitpoint restoration each turn."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="<string name='Traits/Tau/AutomatedRepairSystemFlavor'/>"/>
	<entry name="Tau/BlacksunFilter" value="<string name='Traits/Tau/BlacksunFilter'/>"/>
	<entry name="Tau/BlacksunFilterDescription" value="Increases the sight of vehicles, Ethereal, Commander, Pathfinders and Battlesuits."/>
	<entry name="Tau/BlacksunFilterFlavor" value="<string name='Traits/Tau/BlacksunFilterFlavor'/>"/>
	<entry name="Tau/BlastDamage" value="<string name='Traits/Tau/BlastDamage'/>"/>
	<entry name="Tau/BlastDamageDescription" value="Increases the armour penetration of flame and missile weapons."/>
	<entry name="Tau/BlastDamageFlavor" value="<string name='Traits/Tau/BlastDamageFlavor'/>"/>
	<entry name="Tau/BoltDamage" value="<string name='Traits/Tau/BoltDamage'/>"/>
	<entry name="Tau/BoltDamageDescription" value="Increases the armour penetration of burst and rail weapons."/>
	<entry name="Tau/BoltDamageFlavor" value="<string name='Traits/Tau/BoltDamageFlavor'/>"/>
	<entry name="Tau/BondingKnifeRitual" value="<string name='Actions/Tau/BondingKnifeRitual'/>"/>
	<entry name="Tau/BondingKnifeRitualDescription" value="Grants Fire Warriors, Fire Warriors Breachers, Pathfinders, XV25 Stealth Battlesuits, XV8 Crisis Battlesuits, XV88 Broadside Battlesuits, XV95 Ghostkeel Battlesuits and XV104 Riptide Battlesuits the ability to restore their morale."/>
	<entry name="Tau/BondingKnifeRitualFlavor" value="<string name='Actions/Tau/BondingKnifeRitualFlavor'/>"/>
	<entry name="Tau/CityTier2" value="<string name='Traits/Tau/CityTier2'/>"/>
	<entry name="Tau/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier2Flavor" value="<string name='Traits/Tau/CityTier2Flavor'/>"/>
	<entry name="Tau/CityTier3" value="<string name='Traits/Tau/CityTier3'/>"/>
	<entry name="Tau/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier3Flavor" value="<string name='Traits/Tau/CityTier3Flavor'/>"/>
	<entry name="Tau/CounterfireDefenceSystem" value="<string name='Traits/Tau/CounterfireDefenceSystem'/>"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Battlesuit support system that increases the accuracy of overwatch attacks."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Traits/Tau/CounterfireDefenceSystemFlavor'/>"/>
	<entry name="Tau/DisruptionPod" value="<string name='Traits/Tau/DisruptionPod'/>"/>
	<entry name="Tau/DisruptionPodDescription" value="Increases vehicle ranged damage reduction."/>
	<entry name="Tau/DisruptionPodFlavor" value="<string name='Traits/Tau/DisruptionPodFlavor'/>"/>
	<entry name="Tau/DroneController" value="<string name='Traits/Tau/DroneController'/>"/>
	<entry name="Tau/DroneControllerDescription" value="Battlesuit support system that increases the accuracy of adjacent drones."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Traits/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/EMPGrenade" value="<string name='Weapons/EMPGrenade'/>"/>
	<entry name="Tau/EMPGrenadeDescription" value="Grants Fire Warriors, Fire Warriors Breachers and Pathfinders the ability to throw anti-vehicle grenades."/>
	<entry name="Tau/EMPGrenadeFlavor" value="<string name='Weapons/EMPGrenadeFlavor'/>"/>
	<entry name="Tau/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tau/ExtraInfantryArmourDescription" value="Increases the armour of infantry and monstrous creatures."/>
	<entry name="Tau/ExtraInfantryArmourFlavor" value="Further studying the chitinous carapace of our Thraxian comrades has revealed substantial structural improvements we can make to the composition of our Cadre and Battle Suit armour."/>
	<entry name="Tau/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Tau/ExtraVehicleArmourDescription" value="Increases the armour of vehicles."/>
	<entry name="Tau/ExtraVehicleArmourFlavor" value="Unlike the stagnant Techpriests of Mars, the Earth Caste are endlessly innovating and creating. Fio’tak is the material that is the current peak of their creativity, a hard, ultra-dense, nano-crystalline metal alloy used sparingly on their finest creations."/>
	<entry name="Tau/FlechetteDischarger" value="<string name='Traits/Tau/FlechetteDischarger'/>"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Grants vehicles the ability to damage melee attackers."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="<string name='Traits/Tau/FlechetteDischargerFlavor'/>"/>
	<entry name="Tau/ForTheGreaterGoodBonus" value="Charpactin Ambassaspores"/>
	<entry name="Tau/ForTheGreaterGoodBonusDescription" value="Reduces the influence cost of For The Greater Good."/>
	<entry name="Tau/ForTheGreaterGoodBonusFlavor" value="When our Water Caste diplomats are in the field, they sometimes find it useful to take along our fungoid Charpactin allies, whose strobing ultraviolet communications have a sedative, almost hypnotic, effect on nearly all races."/>
	<entry name="Tau/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tau/HammerOfWrathDescription" value="Grants XV95 Ghostkeel Battlesuits, XV104 Riptide Battlesuits, XV107 R'Varna Battlesuits and KV128 Stormsurges the ability to perform more devastating attacks."/>
	<entry name="Tau/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tau/LasDamage" value="<string name='Traits/Tau/LasDamage'/>"/>
	<entry name="Tau/LasDamageDescription" value="Increases the armour penetration of fusion, ion, plasma and pulse weapons."/>
	<entry name="Tau/LasDamageFlavor" value="<string name='Traits/Tau/LasDamageFlavor'/>"/>
	<entry name="Tau/PhotonGrenade" value="<string name='Weapons/PhotonGrenade'/>"/>
	<entry name="Tau/PhotonGrenadeDescription" value="Grants Fire Warriors, Fire Warriors Breachers, Pathfinders and Cadre Fireblades the ability to throw blinding grenades."/>
	<entry name="Tau/PhotonGrenadeFlavor" value="<string name='Weapons/PhotonGrenadeFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="<string name='Traits/Tau/PointDefenceTargetingRelay'/>"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Grants vehicles increased overwatch damage against enemy units adjacent to other friendly units."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="<string name='Traits/Tau/PointDefenceTargetingRelayFlavor'/>"/>
	<entry name="Tau/PurchaseEnergy" value="<string name='Actions/Tau/PurchaseEnergy'/>"/>
	<entry name="Tau/PurchaseEnergyDescription" value="Grants the ability to purchase energy with influence."/>
	<entry name="Tau/PurchaseEnergyFlavor" value="<string name='Actions/Tau/PurchaseEnergyFlavor'/>"/>
	<entry name="Tau/PurchaseFood" value="<string name='Actions/Tau/PurchaseFood'/>"/>
	<entry name="Tau/PurchaseFoodDescription" value="Grants the ability to purchase food with influence."/>
	<entry name="Tau/PurchaseFoodFlavor" value="<string name='Actions/Tau/PurchaseFoodFlavor'/>"/>
	<entry name="Tau/PurchaseOre" value="<string name='Actions/Tau/PurchaseOre'/>"/>
	<entry name="Tau/PurchaseOreDescription" value="Grants the ability to purchase ore with influence."/>
	<entry name="Tau/PurchaseOreFlavor" value="<string name='Actions/Tau/PurchaseOreFlavor'/>"/>
	<entry name="Tau/PurchasePopulation" value="<string name='Actions/Tau/PurchasePopulation'/>"/>
	<entry name="Tau/PurchasePopulationDescription" value="Grants the ability to purchase population with influence."/>
	<entry name="Tau/PurchasePopulationFlavor" value="<string name='Actions/Tau/PurchasePopulationFlavor'/>"/>
	<entry name="Tau/PurchaseResearch" value="<string name='Actions/Tau/PurchaseResearch'/>"/>
	<entry name="Tau/PurchaseResearchDescription" value="Grants the ability to purchase research with influence."/>
	<entry name="Tau/PurchaseResearchFlavor" value="<string name='Actions/Tau/PurchaseResearchFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="<string name='Traits/Tau/RipykaVa'/>"/>
	<entry name="Tau/RipykaVaDescription" value="Reduces the cooldown of the Commander's metastrategies."/>
	<entry name="Tau/RipykaVaFlavor" value="<string name='Traits/Tau/RipykaVaFlavor'/>"/>
	<entry name="Tau/SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="Tau/SeekerMissileDescription" value="Grants vehicles and XV88 Broadside Battlesuits a missile weapon."/>
	<entry name="Tau/SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Tau/SensorSpines" value="Sensor Spines"/>
	<entry name="Tau/SensorSpinesDescription" value="Grants vehicles move through cover."/>
	<entry name="Tau/SensorSpinesFlavor" value="Sensor spines are used to feed data to an advanced ground-following flight control system, plotting safe courses through treacherous terrain that avoid traps and mines that might lie hidden from sight."/>
	<entry name="Tau/ShieldGenerator" value="<string name='Traits/Tau/ShieldGenerator'/>"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Battlesuit support system that grants invulnerable damage reduction."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Traits/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StimulantInjector" value="<string name='Traits/Tau/StimulantInjector'/>"/>
	<entry name="Tau/StimulantInjectorDescription" value="Battlesuit support system that increases feel no pain damage reduction."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Traits/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/SubversionBonus" value="Seditious Research"/>
	<entry name="Tau/SubversionBonusDescription" value="Increases the loyalty penalty of Subvert City."/>
	<entry name="Tau/SubversionBonusFlavor" value="Whilst conducting oppositional research, the T’au found the phrase “think like your foe” in a human military textbook. The Water Caste have taken it to heart and thoroughly investigate the unfulfilled needs and desires of a given settlement—whether shivering Necron slaves or squig-hungry Ork boys—before fomenting rebellion."/>
	<entry name="Tau/TacticalSupportTurret" value="<string name='Weapons/TacticalSupportTurret'/>"/>
	<entry name="Tau/TacticalSupportTurretDescription" value="Grants Fire Warriors an additional weapon when stationary."/>
	<entry name="Tau/TacticalSupportTurretFlavor" value="<string name='Weapons/TacticalSupportTurretFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="<string name='Traits/Tau/UtopiaBonus'/>"/>
	<entry name="Tau/UtopiaBonusDescription" value="<string name='Traits/Tau/UtopiaBonusDescription'/>"/>
	<entry name="Tau/UtopiaBonusFlavor" value="<string name='Traits/Tau/UtopiaBonusFlavor'/>"/>
	<entry name="Tau/VectoredRetroThrusters" value="<string name='Traits/Tau/VectoredRetroThrusters'/>"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Battlesuit support system that increases movement and allows the unit to ignore enemy zone of control."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Traits/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="<string name='Traits/Tau/VelocityTracker'/>"/>
	<entry name="Tau/VelocityTrackerDescription" value="Battlesuit support system that increases the ranged accuracy against flyers."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Traits/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tyranids/AcidBlood" value="Acid Blood"/>
	<entry name="Tyranids/AcidBloodDescription" value="Grants Tyranid monstrous creatures and Tyranid Primes the trait to damage all melee attackers."/>
	<entry name="Tyranids/AcidBloodFlavor" value="<string name='Traits/Tyranids/AcidBloodFlavor'/>"/>
	<entry name="Tyranids/AdrenalGlands" value="Adrenal Glands"/>
	<entry name="Tyranids/AdrenalGlandsDescription" value="Grants units increased movement and increased melee damage."/>
	<entry name="Tyranids/AdrenalGlandsFlavor" value="Adrenal glands saturate their host's bodies with chemicals that boost the creature's metabolism to a hyperactive state of frenzy."/>
	<entry name="Tyranids/BiomorphDamage" value="<string name='Traits/Tyranids/BiomorphDamage'/>"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Increases the armour penetration of biomorphs."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="<string name='Traits/Tyranids/BiomorphDamageFlavor'/>"/>
	<entry name="Tyranids/BioPlasma" value="Carnifex Bio-Plasma"/>
	<entry name="Tyranids/BioPlasmaDescription" value="Grants Carnifexes an additional ranged weapon."/>
	<entry name="Tyranids/BioPlasmaFlavor" value="<string name='Weapons/BioPlasmaFlavor'/>"/>
	<entry name="Tyranids/BoneMace" value="Carnifex Bone Mace"/>
	<entry name="Tyranids/BoneMaceDescription" value="Grants Carnifexes an additional melee weapon."/>
	<entry name="Tyranids/BoneMaceFlavor" value="<string name='Weapons/BoneMaceFlavor'/>"/>
	<entry name="Tyranids/CityCost" value="Metamorphic Malanthropes"/>
	<entry name="Tyranids/CityCostDescription" value="Reduces the cost of founding new cities."/>
	<entry name="Tyranids/CityCostFlavor" value="The role of the Malanthrope in founding new Tyranid hives has been little studied by Imperial Genetors, as in most circumstances they're dead or fleeing when the hive is founded. However, it's believed that they transport the city seed to new locations when a forward brood location is required. Unusual Malanthropes have sometimes been observed, with specialised physical structures that enable them to perform this task more efficiently."/>
	<entry name="Tyranids/CityDamage" value="<string name='Traits/Tyranids/CityDamage'/>"/>
	<entry name="Tyranids/CityDamageDescription" value="Enemy units in Tyranid cities take damage each turn."/>
	<entry name="Tyranids/CityDamageFlavor" value="<string name='Traits/Tyranids/CityDamageFlavor'/>"/>
	<entry name="Tyranids/CityGrowth" value="<string name='Traits/Tyranids/CityGrowth'/>"/>
	<entry name="Tyranids/CityGrowthDescription" value="Increases the growth rate of Tyranid cities."/>
	<entry name="Tyranids/CityGrowthFlavor" value="<string name='Traits/Tyranids/CityGrowthFlavor'/>"/>
	<entry name="Tyranids/CityLoyalty" value="<string name='Traits/Tyranids/CityLoyalty'/>"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Decreases the loyalty penalty from the amount of Tyranid cities."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="<string name='Traits/Tyranids/CityLoyaltyFlavor'/>"/>
	<entry name="Tyranids/CityPopulationLimit" value="<string name='Traits/Tyranids/CityPopulationLimit'/>"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Increases the population limit of Tyranid cities."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="<string name='Traits/Tyranids/CityPopulationLimitFlavor'/>"/>
	<entry name="Tyranids/CityProduction" value="<string name='Traits/Tyranids/CityProduction'/>"/>
	<entry name="Tyranids/CityProductionDescription" value="Increases the production output of Tyranid cities."/>
	<entry name="Tyranids/CityProductionFlavor" value="<string name='Traits/Tyranids/CityProductionFlavor'/>"/>
	<entry name="Tyranids/CityTier2" value="<string name='Traits/Tyranids/CityTier2'/>"/>
	<entry name="Tyranids/CityTier2Description" value="Increases the tile acquisition radius of the city."/>
	<entry name="Tyranids/CityTier2Flavor" value="<string name='Traits/Tyranids/CityTier2Flavor'/>"/>
	<entry name="Tyranids/ConsumeTile2" value="Efficient Digestion"/>
	<entry name="Tyranids/ConsumeTile2Description" value="Decreases the influence cost of consuming tiles."/>
	<entry name="Tyranids/ConsumeTile2Flavor" value="Whilst a Malanthrope or Ripper's razor-edged jaws have no problem eating their way through flesh, bone or even plasteel, they are slowed by the necessity of consuming the huge quantities of soil and rock the Hive Mind demands at scale and speed. It is presumed that specialised organisms are deployed to consume these—though, again, none have survived to report them."/>
	<entry name="Tyranids/Deathspitter" value="Ravener Deathspitter"/>
	<entry name="Tyranids/DeathspitterDescription" value="Grants Raveners a ranged weapon."/>
	<entry name="Tyranids/DeathspitterFlavor" value="<string name='Weapons/DeathspitterFlavor'/>"/>
	<entry name="Tyranids/DesiccatorLarvae" value="<string name='Weapons/DesiccatorLarvae'/>"/>
	<entry name="Tyranids/DesiccatorLarvaeDescription" value="Grants Hive Tyrants, Tervigons and Tyrannofexes a template weapon."/>
	<entry name="Tyranids/DesiccatorLarvaeFlavor" value="<string name='Weapons/DesiccatorLarvaeFlavor'/>"/>
	<entry name="Tyranids/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourFlavor" value="The Hive Mind is a frugal mentality. Why sacrifice resources armouring troops that will be reclaimed anyway? Only when the gamble makes sense, does it invest in extra-thick chitin and bone for its fauna."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmour" value="<string name='Traits/ExtraMonstrousCreatureArmour'/>"/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourDescription" value="Increases the armour of monstrous creatures."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourFlavor" value="Through a combination of hardened carapaces, disruptive fields and deadened nerve endings, the Hive Mind can alter the resilience of its larger creatures easily."/>
	<entry name="Tyranids/FleshHooks" value="<string name='Weapons/FleshHooks'/>"/>
	<entry name="Tyranids/FleshHooksDescription" value="Grants Tyranid Warriors, Tyranid Primes and Lictors an additional ranged weapon."/>
	<entry name="Tyranids/FleshHooksFlavor" value="<string name='Weapons/FleshHooksFlavor'/>"/>
	<entry name="Tyranids/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tyranids/HammerOfWrathDescription" value="Grants Exocrines, Gargoyles, Haruspexes, Hive Crones, Hive Tyrants, Maleceptors, Scythed Hierodules, Tervigons, Trygons, Tyrannocytes and Tyrannofexes the ability to perform more devastating attacks."/>
	<entry name="Tyranids/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="<string name='Traits/Tyranids/InfantryUpkeep'/>"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Reduces the biomass upkeep of infantry."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="<string name='Traits/Tyranids/InfantryUpkeepFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2" value="Savage Apressants"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Description" value="Reduces the influence cost of overriding instinctive behaviour."/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Flavor" value="Maintaining control of its troops is a major concern of the hive mind and one it seeks to overcome by various adaptations, such as its synapse creatures. A simpler one is to reduce the natural savagery of its units when they run feral, so they require less psychic effort to bring back under control."/>
	<entry name="Tyranids/LongRangedDamage" value="<string name='Traits/Tyranids/LongRangedDamage'/>"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Increases the armour penetration of long-ranged weapons."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="<string name='Traits/Tyranids/LongRangedDamageFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="<string name='Traits/Tyranids/MeleeDamage'/>"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Increases the armour penetration of melee weapons."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="<string name='Traits/Tyranids/MeleeDamageFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="Grants Zoanthropes the ability to curse enemy units so they have decreased accuracy."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation2" value="Malanthropic Gustarflagelli"/>
	<entry name="Tyranids/PreyAdaptation2Description" value="Increases the research gained when enemies die in the area of a Malanthrope."/>
	<entry name="Tyranids/PreyAdaptation2Flavor" value="An unusual adaptation of the Malanthropic form, gustarflagelli are filament thin tendrils that surround a malanthrope like a fleshy, wriggling cloud. It's presumed that they enable the Malanthrope to gather and retain more genetic data from recently-deceased enemies."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="<string name='Traits/Tyranids/ProductionBuildingUpkeep'/>"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Reduces the influence upkeep of production buildings."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ProductionBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="<string name='Traits/Tyranids/Reclamation2'/>"/>
	<entry name="Tyranids/Reclamation2Description" value="Reduces the influence cost of reclamation."/>
	<entry name="Tyranids/Reclamation2Flavor" value="<string name='Traits/Tyranids/Reclamation2Flavor'/>"/>
	<entry name="Tyranids/Reclamation3" value="<string name='Traits/Tyranids/Reclamation3'/>"/>
	<entry name="Tyranids/Reclamation3Description" value="Removes the cooldown of reclamation."/>
	<entry name="Tyranids/Reclamation3Flavor" value="<string name='Traits/Tyranids/Reclamation3Flavor'/>"/>
	<entry name="Tyranids/Regeneration" value="Regeneration"/>
	<entry name="Tyranids/RegenerationDescription" value="Grants Tyranid monstrous creatures and Tyranid Primes hitpoint restoration each turn."/>
	<entry name="Tyranids/RegenerationFlavor" value="<string name='Traits/RegenerationFlavor'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="<string name='Traits/Tyranids/ResourceBuildingUpkeep'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Reduces the influence upkeep of resource buildings."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ResourceBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="<string name='Traits/Tyranids/ShortRangedDamage'/>"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Increases the armour penetration of short-ranged weapons."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="<string name='Traits/Tyranids/ShortRangedDamageFlavor'/>"/>
	<entry name="Tyranids/StingerSalvo" value="<string name='Weapons/StingerSalvo'/>"/>
	<entry name="Tyranids/StingerSalvoDescription" value="Grants Hive Crones an additional ranged weapon."/>
	<entry name="Tyranids/StingerSalvoFlavor" value="<string name='Weapons/StingerSalvoFlavor'/>"/>
	<entry name="Tyranids/ThresherScythe" value="<string name='Weapons/ThresherScythe'/>"/>
	<entry name="Tyranids/ThresherScytheDescription" value="Grants Exocrines and Haruspexes an additional melee weapon."/>
	<entry name="Tyranids/ThresherScytheFlavor" value="<string name='Weapons/ThresherScytheFlavor'/>"/>
	<entry name="Tyranids/ToxinSacs" value="<string name='Traits/Tyranids/ToxinSacs'/>"/>
	<entry name="Tyranids/ToxinSacsDescription" value="Increases the damage of melee weapons against infantry and monstrous creatures."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="<string name='Traits/Tyranids/ToxinSacsFlavor'/>"/>
	<entry name="Tyranids/Toxinspike" value="Trygon Toxinspike"/>
	<entry name="Tyranids/ToxinspikeDescription" value="Grants Trygons an additional melee weapon."/>
	<entry name="Tyranids/ToxinspikeFlavor" value="<string name='Weapons/ToxinspikeFlavor'/>"/>
	<entry name="Tyranids/Tunnel2" value="<string name='Traits/Tyranids/Tunnel2'/>"/>
	<entry name="Tyranids/Tunnel2Description" value="Increases the hitpoints of Broodhives."/>
	<entry name="Tyranids/Tunnel2Flavor" value="<string name='Traits/Tyranids/Tunnel2Flavor'/>"/>
	<entry name="Tyranids/VehiclesUpkeep" value="<string name='Traits/Tyranids/VehiclesUpkeep'/>"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Reduces the biomass upkeep of monstrous creatures."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="<string name='Traits/Tyranids/VehiclesUpkeepFlavor'/>"/>
	
	<entry name="Missing" value="Missing"/>
</language>
