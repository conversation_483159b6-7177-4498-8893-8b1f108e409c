<?xml version="1.0" encoding="utf-8"?>
<unit>
	<model>
		<unit mesh="Units/Tyranids/HiveTyrant"
				material="Units/Tyranids/HiveTyrant"
				idleAnimation="Units/Tyranids/HiveTyrantIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="1.0"
				scale="1 1 1"
				position="0 0 64"
				bloodBone="BloodBone"/>
	</model>
	<weapons>
		<weapon name="DesiccatorLarvae" requiredUpgrade="Tyranids/DesiccatorLarvae">
			<model>
				<flamerWeapon muzzleBone="BloodBone"/>
			</model>
		</weapon>
		<weapon name="HeavyVenomCannon">
			<model>
				<projectileWeapon muzzleBone=".Muzzle" fireInterval="0.566666666667"/>
			</model>
		</weapon>
		<weapon name="PrehensilePincer">
			<model>
				<weapon fireInterval="10.0"/>
			</model>
		</weapon>
		<weapon name="RendingClaws">
			<model>
				<weapon fireInterval="10.0"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>				
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="6.0"/> <!-- %biomassUpkeep base tier=9 factor=1 -->
				<biomassCost base="120.0"/> <!-- %biomassCost base tier=9 factor=1 -->
				<hitpointsMax base="30.0"/> <!-- %hitpointsMax base toughness=6 wounds=5 -->
				<influenceUpkeep base="9.0"/> <!-- %influenceUpkeep base tier=9 factor=1.5 -->
				<influenceCost base="180.0"/> <!-- %influenceCost base tier=9 factor=1.5 -->
				<itemSlots base="6"/>
				<levelMax base="9"/>
				<meleeAccuracy base="12"/> <!-- %meleeAccuracy base weaponSkill=8 -->
				<meleeAttacks base="1"/>
				<strengthDamage base="3"/> <!-- %strengthDamage base strength=6 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=10 -->
				<movementMax base="6"/>
				<productionCost base="54.0"/> <!-- %productionCost base tier=9 factor=1 -->
				<rangedAccuracy base="8"/> <!-- %rangedAccuracy base ballisticSkill=4 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseHeroesScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/HiveTyrantAttack"
						beginFire="0.666666666667"
						endFire="2.66666666667"
						chargeAnimation="Units/Tyranids/HiveTyrantCharge"
						chargeBeginFire="0.4"
						chargeEndFire="1.33333333333"
						meleeAnimation="Units/Tyranids/HiveTyrantMelee"
						meleeBeginSwing="0.0"
						meleeEndSwing="1.0"/>
			</model>				
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/HiveTyrantDie"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="1.0"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/HiveTyrantMove"
						sound="Units/Tyranids/HiveTyrantMove"
						soundCount="2"/>
			</model>
		</move>
		<genericUnitAbility name="Tyranids/SynapseCreature"
				passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/SynapseLink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<genericUnitAbility name="Tyranids/ShadowInTheWarp" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1" excludeRadius="0">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/ShadowInTheWarp"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<genericUnitAbility name="Tyranids/IndescribableHorror"
				passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/IndescribableHorror"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<jink consumedActionPoints="0" consumedMovement="0" requiredActionPoints="0" cooldown="3">
			<model>
				<action sound="Actions/ChargeUp"/>
			</model>
			<conditions>
				<unit>
					<notAttackedThisTurn/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait duration="1" name="Jink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</jink>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				requiredActionPoints="0"
				requiredUpgrade="Tyranids/HammerOfWrath">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="HammerOfWrath" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>		
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
		<psychicScream cooldown="2"
				name="Tyranids/PsychicScream"
				rank="-1"
				rankMax="2">
			<model>
				<action animation="Units/Tyranids/HiveTyrantAbility"
						bone="BloodBone"
						beginFire="0.67"
						sound="Weapons/PsychicScream"
						soundCount="3"
						soundDelay="0.67"/>
			</model>
			<modifiers>
				<modifier>
					<effects>
						<damage mulMin="0.0" mulMax="1.0"/>
					</effects>
				</modifier>
			</modifiers>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit" radius="1" excludeRadius="0">
							<modifiers>
								<modifier>
									<conditions>
										<unit>
											<enemy/>
										</unit>
									</conditions>
									<effects>
										<weaponDamage weapon="PsychicScream"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</psychicScream>
		<onslaught consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/Onslaught"
				rank="-1"
				rankMax="2"
				requiredActionPoints="0">
			<model>
				<action animation="Units/Tyranids/HiveTyrantAbility"
						sound="Actions/HiveMindBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit" radius="1">
							<modifiers>
								<modifier>
									<conditions>
										<unit>
											<allied/>
											<movement greater="0"/>
										</unit>
									</conditions>
									<effects>
										<addTrait name="Tyranids/Onslaught" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</onslaught>
		<genericUnitAbility name="Tyranids/Dominion"
				passive="1"
				rank="-1"
				rankMax="2">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radiusMin="2" radiusMax="4">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/SynapseLink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/Dominion"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<hiveCommander consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="10"
				elite="1"
				name="Tyranids/HiveCommander"
				rank="-1"
				rankMax="0"
				requiredActionPoints="0">
			<model>
				<action animation="Units/Tyranids/HiveTyrantAbility"
						sound="Actions/HiveMindEliteBuff"/>
			</model>			
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit" radius="3">
							<modifiers>
								<modifier>
									<conditions>
										<unit>
											<allied/>
											<faction name="Tyranids"/>
										</unit>
									</conditions>
									<effects>
										<addTrait name="Tyranids/HiveCommander" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hiveCommander>
		<levelUp/>
		<shop/>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="Flyer"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<!-- <trait name="MoveThroughCover"/> -->
		<trait name="Hero"/>
		<trait name="MonstrousCreature"/>
		<trait name="Psyker"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<trait name="Relentless"/>
		<trait name="Smash"/>
		<trait name="Tyranids/ToxinSacs" requiredUpgrade="Tyranids/ToxinSacs"/>
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
	</traits>
</unit>
