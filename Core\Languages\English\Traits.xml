<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="Artefacts/Accuracy" value="<string name='Units/Neutral/Artefacts/Accuracy'/>"/>
	<entry name="Artefacts/AccuracyDescription" value="Increases the accuracy."/>
	<entry name="Artefacts/Armor" value="<string name='Units/Neutral/Artefacts/Armor'/>"/>
	<entry name="Artefacts/ArmorDescription" value="Increases the armor."/>
	<entry name="Artefacts/ArmorPenetration" value="<string name='Units/Neutral/Artefacts/ArmorPenetration'/>"/>
	<entry name="Artefacts/ArmorPenetrationDescription" value="Increases the armor penetration."/>
	<entry name="Artefacts/Damage" value="<string name='Units/Neutral/Artefacts/Damage'/>"/>
	<entry name="Artefacts/DamageDescription" value="Increases the damage."/>
	<entry name="Artefacts/Healing" value="<string name='Units/Neutral/Artefacts/Healing'/>"/>
	<entry name="Artefacts/HealingDescription" value="Restores hitpoints each turn."/>
	<entry name="Artefacts/Hitpoints" value="<string name='Units/Neutral/Artefacts/Hitpoints'/>"/>
	<entry name="Artefacts/HitpointsDescription" value="Increases the hitpoints."/>
	<entry name="Artefacts/Loyalty" value="<string name='Units/Neutral/Artefacts/Loyalty'/>"/>
	<entry name="Artefacts/LoyaltyDescription" value="Increases the loyalty."/>
	<entry name="Artefacts/Movement" value="<string name='Units/Neutral/Artefacts/Movement'/>"/>
	<entry name="Artefacts/MovementDescription" value="Increases the movement."/>
	<entry name="Artefacts/Sight" value="<string name='Units/Neutral/Artefacts/Sight'/>"/>
	<entry name="Artefacts/SightDescription" value="Increases the sight."/>
	
	<entry name="Items/AdamantiumWeaveVest" value="<string name='Items/AdamantiumWeaveVest'/>"/>
	<entry name="Items/AdamantiumWeaveVestDescription" value="<string name='Items/AdamantiumWeaveVestDescription'/>"/>
	<entry name="Items/AdamantiumWeaveVestFlavor" value="<string name='Items/AdamantiumWeaveVestFlavor'/>"/>
	<entry name="Items/ArmaplasBracers" value="<string name='Items/ArmaplasBracers'/>"/>
	<entry name="Items/ArmaplasBracersDescription" value="<string name='Items/ArmaplasBracersDescription'/>"/>
	<entry name="Items/ArmaplasBracersFlavor" value="<string name='Items/ArmaplasBracersFlavor'/>"/>
	<entry name="Items/AxeOfBlindFury" value="<string name='Items/AxeOfBlindFury'/>"/>
	<entry name="Items/AxeOfBlindFuryDescription" value="<string name='Items/AxeOfBlindFuryDescription'/>"/>
	<entry name="Items/AxeOfBlindFuryFlavor" value="<string name='Items/AxeOfBlindFuryFlavor'/>"/>
	<entry name="Items/CombatStimulant" value="<string name='Items/CombatStimulant'/>"/>
	<entry name="Items/CombatStimulantDescription" value="<string name='Items/CombatStimulantDescription'/>"/>
	<entry name="Items/CombatStimulantFlavor" value="<string name='Items/CombatStimulantFlavor'/>"/>
	<entry name="Items/ConcealedWeaponSystem" value="<string name='Items/ConcealedWeaponSystem'/>"/>
	<entry name="Items/ConcealedWeaponSystemDescription" value="<string name='Items/ConcealedWeaponSystemDescription'/>"/>
	<entry name="Items/ConcealedWeaponSystemFlavor" value="<string name='Items/ConcealedWeaponSystemFlavor'/>"/>
	<entry name="Items/DuskBlade" value="<string name='Items/DuskBlade'/>"/>
	<entry name="Items/DuskBladeDescription" value="<string name='Items/DuskBladeDescription'/>"/>
	<entry name="Items/DuskBladeFlavor" value="<string name='Items/DuskBladeFlavor'/>"/>
	<entry name="Items/EnduranceImplant" value="<string name='Items/EnduranceImplant'/>"/>
	<entry name="Items/EnduranceImplantDescription" value="<string name='Items/EnduranceImplantDescription'/>"/>
	<entry name="Items/EnduranceImplantFlavor" value="<string name='Items/EnduranceImplantFlavor'/>"/>
	<entry name="Items/EntropicLocum" value="<string name='Items/EntropicLocum'/>"/>
	<entry name="Items/EntropicLocumDescription" value="<string name='Items/EntropicLocumDescription'/>"/>
	<entry name="Items/EntropicLocumFlavor" value="<string name='Items/EntropicLocumFlavor'/>"/>
	<entry name="Items/FaolchusWing" value="<string name='Items/FaolchusWing'/>"/>
	<entry name="Items/FaolchusWingDescription" value="<string name='Items/FaolchusWingDescription'/>"/>
	<entry name="Items/FaolchusWingFlavor" value="<string name='Items/FaolchusWingFlavor'/>"/>
	<entry name="Items/LightningGauntlet" value="<string name='Items/LightningGauntlet'/>"/>
	<entry name="Items/LightningGauntletDescription" value="<string name='Items/LightningGauntletDescription'/>"/>
	<entry name="Items/LightningGauntletFlavor" value="<string name='Items/LightningGauntletFlavor'/>"/>
	<entry name="Items/MourningBladeOfLazaerek" value="<string name='Items/MourningBladeOfLazaerek'/>"/>
	<entry name="Items/MourningBladeOfLazaerekDescription" value="<string name='Items/MourningBladeOfLazaerekDescription'/>"/>
	<entry name="Items/MourningBladeOfLazaerekFlavor" value="<string name='Items/MourningBladeOfLazaerekFlavor'/>"/>
	<entry name="Items/OmniScope" value="<string name='Items/OmniScope'/>"/>
	<entry name="Items/OmniScopeDescription" value="<string name='Items/OmniScopeDescription'/>"/>
	<entry name="Items/OmniScopeFlavor" value="<string name='Items/OmniScopeFlavor'/>"/>
	<entry name="Items/PoweredGauntlet" value="<string name='Items/PoweredGauntlet'/>"/>
	<entry name="Items/PoweredGauntletDescription" value="<string name='Items/PoweredGauntletDescription'/>"/>
	<entry name="Items/PoweredGauntletFlavor" value="<string name='Items/PoweredGauntletFlavor'/>"/>
	<entry name="Items/ScrollsOfMagnus" value="<string name='Items/ScrollsOfMagnus'/>"/>
	<entry name="Items/ScrollsOfMagnusDescription" value="<string name='Items/ScrollsOfMagnusDescription'/>"/>
	<entry name="Items/ScrollsOfMagnusFlavor" value="<string name='Items/ScrollsOfMagnusFlavor'/>"/>
	<entry name="Items/SightlessHelm" value="<string name='Items/SightlessHelm'/>"/>
	<entry name="Items/SightlessHelmDescription" value="<string name='Items/SightlessHelmDescription'/>"/>
	<entry name="Items/SightlessHelmFlavor" value="<string name='Items/SightlessHelmFlavor'/>"/>
	<entry name="Items/TantalisingIcon" value="<string name='Items/TantalisingIcon'/>"/>
	<entry name="Items/TantalisingIconDescription" value="<string name='Items/TantalisingIconDescription'/>"/>
	<entry name="Items/TantalisingIconFlavor" value="<string name='Items/TantalisingIconFlavor'/>"/>
	<entry name="Items/TemporaryShield" value="<string name='Items/TemporaryShield'/>"/>
	<entry name="Items/TemporaryShieldDescription" value="<string name='Items/TemporaryShieldDescription'/>"/>
	<entry name="Items/TemporaryShieldFlavor" value="<string name='Items/TemporaryShieldFlavor'/>"/>
	<entry name="Items/UltraWidebandAuspex" value="<string name='Items/UltraWidebandAuspex'/>"/>
	<entry name="Items/UltraWidebandAuspexDescription" value="<string name='Items/UltraWidebandAuspexDescription'/>"/>
	<entry name="Items/UltraWidebandAuspexFlavor" value="<string name='Items/UltraWidebandAuspexFlavor'/>"/>	
	<entry name="Items/VolcanisShroud" value="<string name='Items/VolcanisShroud'/>"/>
	<entry name="Items/VolcanisShroudDescription" value="<string name='Items/VolcanisShroudDescription'/>"/>
	<entry name="Items/VolcanisShroudFlavor" value="<string name='Items/VolcanisShroudFlavor'/>"/>
	<entry name="Items/ZoatHideJerkin" value="<string name='Items/ZoatHideJerkin'/>"/>
	<entry name="Items/ZoatHideJerkinDescription" value="<string name='Items/ZoatHideJerkinDescription'/>"/>
	<entry name="Items/ZoatHideJerkinFlavor" value="<string name='Items/ZoatHideJerkinFlavor'/>"/>
	
	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="Adjacency Integration"/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Increases the research output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="The rediscovery of knowledge is such a holy rite, that supporting rites have been mandated throughout the Hive. As population density grows, the faithful constantly pour libations and imprecate the city’s integrated machine spirits to give up their secrets to the Tech-Priests in the Librarium Omnis."/>
	<entry name="AdeptusMechanicus/AggressionOverride" value="<string name='Actions/AdeptusMechanicus/AggressionOverride'/>"/>
	<entry name="AdeptusMechanicus/AggressionOverrideDescription" value="Increases the attacks."/>
	<entry name="AdeptusMechanicus/AggressionOverrideFlavor" value="<string name='Actions/AdeptusMechanicus/AggressionOverrideFlavor'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="Increases the movement, but decreases the armour."/>
	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="Dogma Metalica"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="The zealots of Metalica are famed for bringing with them an ear-shattering clamour, redolent of their forge world’s endless industry—and for not pausing in their relentless advance, obliterating their foes mid-march."/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="Increases the ranged accuracy."/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
	<entry name="AdeptusMechanicus/Bionics" value="Bionics"/>
	<entry name="AdeptusMechanicus/BionicsDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="AdeptusMechanicus/BionicsFlavor" value="It’s a rare Adeptus Mechanicus Magos who goes completely unaugmented, since the days of Arkhan Land. Some, vainer, conceal their augmentation, but most bionic augmentation is deliberately crude and inhuman, acting as physical symbols of the Omnissiah’s blessing."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Actions/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Reduces the morale loss."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Actions/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="Increases the armour, but decreases the movement."/>
	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiah" value="Canticles of the Omnissiah"/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiahDescription" value="Classification."/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiahFlavor" value="In times of war, the disciples of the Omnissiah incant complex war-blessings. These are as much optimisation subroutines as they are expressions of faith in their all-knowing, all-comprehending deity."/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="Increases the melee accuracy."/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier2" value="Mavoraforming"/>
	<entry name="AdeptusMechanicus/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="AdeptusMechanicus/CityTier2Flavor" value="Terraforming is the ironically-named process of making a planet more livable for humans, exactly unlike the devastated bulk of Earth, hidden beneath the continent-sprawling Imperial Palace. Mavoraforming has no such pretensions, turning a planet into an exploited, poisoned dotted with incredibly productive cities, just like home."/>
	<entry name="AdeptusMechanicus/CityTier3" value="Hive World Approval"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="AdeptusMechanicus/CityTier3Flavor" value="The final conversion of a planet to a Hive World is visible from space. Gone are the rolling hills, the valleys, the trees. Vanished the lakes, seas and breathable atmosphere. Biting grit blows over dust bowls where a few recalcitrant Imperials eke out subsistence farms in the shadows of sky-piercing Hive spires, filled with billions of souls."/>
	<entry name="AdeptusMechanicus/Cognis" value="Cognis"/>
	<entry name="AdeptusMechanicus/CognisDescription" value="Limits accuracy loss."/>
	<entry name="AdeptusMechanicus/CognisFlavor" value="<string name='Weapons/CognisFlavor'/>"/>
	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Actions/AdeptusMechanicus/CommandUplink'/>"/>
	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Reduces the morale loss."/>
	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Actions/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="Increases the melee accuracy, but decreases the ranged accuracy."/>
	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ControlEdict" value="<string name='Actions/AdeptusMechanicus/ControlEdict'/>"/>
	<entry name="AdeptusMechanicus/ControlEdictDescription" value="Removes Doctrina Imperatives malus."/>
	<entry name="AdeptusMechanicus/ControlEdictFlavor" value="<string name='Actions/AdeptusMechanicus/ControlEdictFlavor'/>"/>
	<entry name="AdeptusMechanicus/DartingHunters" value="Darting Hunters"/>
	<entry name="AdeptusMechanicus/DartingHuntersDescription" value="Actions do not consume movement."/>
	<entry name="AdeptusMechanicus/DartingHuntersFlavor" value="Pteraxii’s reflexes are accentuated by paring back elements of cogitation that impede their primary function."/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermon" value="<string name='Actions/AdeptusMechanicus/DataBlessedAutosermon'/>"/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermonDescription" value="Grants research when killing an enemy."/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermonFlavor" value="<string name='Actions/AdeptusMechanicus/DataBlessedAutosermonFlavor'/>"/>
	<entry name="AdeptusMechanicus/DoctrinaImperatives" value="Doctrina Imperatives"/>
	<entry name="AdeptusMechanicus/DoctrinaImperativesDescription" value="Classification."/>
	<entry name="AdeptusMechanicus/DoctrinaImperativesFlavor" value="The Skitarii are fearsome foes, relentless in the pursuit of the Omnissiah’s agenda and equipped with the most advanced weaponry in the Imperium. Ultimately, though, each is but a cybernetic vessel for the will of the Tech-Priests. In the heat of battle, the Skitarii will be remotely controlled by data imperatives that boost their minds and bodies to inhuman levels."/>
	<entry name="AdeptusMechanicus/Dunestrider" value="Dunestrider"/>
	<entry name="AdeptusMechanicus/DunestriderDescription" value="Increases the movement."/>
	<entry name="AdeptusMechanicus/DunestriderFlavor" value="Some Skitarii are able to march across the most hostile terrain at relentless pace, their augmetic limbs never tiring nor wearing out."/>
	<entry name="AdeptusMechanicus/EmanatusForceField" value="Emanatus Force Field"/>
	<entry name="AdeptusMechanicus/EmanatusForceFieldDescription" value="Increases the damage reduction."/>
	<entry name="AdeptusMechanicus/EmanatusForceFieldFlavor" value="The overlapping force fields generated by Dunecrawlers are marvels of military science. Much like the refractor fields common to the lesser orders of the Martian Priesthood, they disperse hostile energies into the atmosphere, each incoming bullet transformed into little more than a flash of actinic blue light and a sudden tang of ozone."/>
	<entry name="AdeptusMechanicus/EnhancedDataTether" value="Enhanced Data-Tether"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Increases the morale."/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="Seen as the mouthpieces of the Tech-Priests, who in turn are the prophets of the Machine God himself, those honoured with bearing enhanced data-tethers are obeyed without hesitation by their reverent Skitarii comrades."/>
	<entry name="AdeptusMechanicus/EnrichedRounds" value="<string name='Actions/AdeptusMechanicus/EnrichedRounds'/>"/>
	<entry name="AdeptusMechanicus/EnrichedRoundsDescription" value="Increases the damage."/>
	<entry name="AdeptusMechanicus/EnrichedRoundsFlavor" value="<string name='Actions/AdeptusMechanicus/EnrichedRoundsFlavor'/>"/>
	<entry name="AdeptusMechanicus/EnslavedToThePast" value="Enslaved to the Past"/>
	<entry name="AdeptusMechanicus/EnslavedToThePastDescription" value="Increases the research cost."/>
	<entry name="AdeptusMechanicus/EnslavedToThePastFlavor" value="Ultimately the Cult’s citadels of knowledge are built upon a foundation of lies. The ability to truly innovate has long been lost, replaced with a reverence for the times when Humanity was the architect of its own destiny. No longer the master of its creations, the Cult Mechanicus is enslaved to the past. It maintains the glories of yesteryear with rite, dogma and edict instead of discernment and comprehension. Even the theoretically simple process of activating a weapon is preceded by the application of ritual oils, the burning of sacred resins and the chanting of long and complex hymns. And yet so long as the process works – or rather, so long as the Cult’s armies can obliterate those who displease them – the Tech-Priests are content to tread the slippery path toward entropy and ignorance."/>
	<entry name="AdeptusMechanicus/GuldiresOrison" value="Guldires Orison"/>
	<entry name="AdeptusMechanicus/GuldiresOrisonDescription" value="Reduces the accuracy."/>
	<entry name="AdeptusMechanicus/GuldiresOrisonFlavor" value="Guldire was a Chief Warpsmith of the Word Bearers under Erebus. His ‘Orison’ is a prayer of pure corrupted machine code, carrying demonic whispers from the heart of the Warp. Though rendered non-lethal by Imperial defenses, it is still incredibly distracting…"/>
	<entry name="AdeptusMechanicus/FidorumVossPrime" value="Fidorum Voss Prime"/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Increases the influence output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="The Cult Mechanicus Priesthood venerate the Forge World of Voss Prime for its loyalty above all others—its loyalty to the Imperium is second-to-none. Even when Mars itself fell to the Dark Mechanicum, Voss Prime continued to churn out armaments and was instrumental in the defeat of the Horus Heresy."/>
	<entry name="AdeptusMechanicus/GalvanicField" value="<string name='Actions/AdeptusMechanicus/GalvanicField'/>"/>
	<entry name="AdeptusMechanicus/GalvanicFieldDescription" value="Increases the armour penetration."/>
	<entry name="AdeptusMechanicus/GalvanicFieldFlavor" value="<string name='Actions/AdeptusMechanicus/GalvanicFieldFlavor'/>"/>
	<entry name="AdeptusMechanicus/EntropicDestabilisation" value="Entropic Destabilisation"/>
	<entry name="AdeptusMechanicus/EntropicDestabilisationDescription" value="Grants damage reduction."/>
	<entry name="AdeptusMechanicus/EntropicDestabilisationFlavor" value="You are invigorated by the Omnissiah’s blessing! Nothing can stand in your way (is this a dream? Did you see the Omnissiah?!)"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="Scriptorum Ordinatus"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="The Ordinati are unique titan-sized devices, each built for a unique purpose and closely guarded. Your researches have unearthed approved teachings taken from the creation of Ordinatus Oberon, a track-mounted starship cannon employed during the Wars for Armageddon."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="Icons of the Omnissiah"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Increases the loyalty output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="Protect us, master. Free us, Machine God. The Omnissiah comes. The Omnissiah comes. THE OMNISSIAH COMES. Punish the flesh."/>
	<entry name="AdeptusMechanicus/IncenseCloud" value="Incense Cloud"/>
	<entry name="AdeptusMechanicus/IncenseCloudDescription" value="Increases the ranged damage reduction."/>
	<entry name="AdeptusMechanicus/IncenseCloudFlavor" value="The secrets behind Aldebrac Vingh’s creation of the perpetual motion engines that power the ancient Ironstriders has been lost, so they are venerated indeed by the Cultists of Mars. The sacred incense that enshrouds them is both a symbol of their veneration and an effective cover against enemy fire."/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="Reduces the morale loss."/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="Increases the melee damage."/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
	<entry name="AdeptusMechanicus/IonShield" value="Ion Shield"/>
	<entry name="AdeptusMechanicus/IonShieldDescription" value="Increases the damage reduction."/>
	<entry name="AdeptusMechanicus/IonShieldFlavor" value="Knights carry potent field generators called ion shields. These devices use ancient technology to project an energy field across a narrow arc. By moving the position of the shield so that it intercepts enemy attacks, a Knight is able to survive even the heaviest fire, whilst still being able to fire its own weapons in return. The exact setting and positioning of the shield is essential, as the ion shield is only designed to deflect and slow shots, rather than absorb them in the manner of the void shields used on Imperial Titans. This means the effectiveness of the shield is dependent on the skill and experience of its operator."/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCult" value="<string name='Actions/AdeptusMechanicus/LordOfTheMachineCult'/>"/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCultDescription" value="Increases the accuracy."/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCultFlavor" value="<string name='Actions/AdeptusMechanicus/LordOfTheMachineCultFlavor'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisation" value="Lucian Specialisation"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="Increases the resource output of non-headquarters buildings of the same type on a tile."/>
	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="Inside the hollow Forge World of Lucius is a trapped sun, but to the Adeptus Mechanicus that is less interesting than the planet’s specialisations—a unique metal “Luciun” and warp travel. Magi of Lucius encourage specialisation in depth."/>
	<entry name="AdeptusMechanicus/MechanicusLocum" value="<string name='Actions/AdeptusMechanicus/MechanicusLocum'/>"/>
	<entry name="AdeptusMechanicus/MechanicusLocumDescription" value="<string name='Actions/AdeptusMechanicus/MechanicusLocumDescription'/>"/>
	<entry name="AdeptusMechanicus/MechanicusLocumFlavor" value="<string name='Actions/AdeptusMechanicus/MechanicusLocumFlavor'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="Ryzan Savagery"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="Despite the planet’s specialisation in plasma and shielding devices, so common are Ork invasions on the Forge World of Ryza that the defenders are known for their Ork-esque enthusiasm for melee combat."/>
	<entry name="AdeptusMechanicus/MonolithicBuildings" value="Monolithic Buildings"/>
	<entry name="AdeptusMechanicus/MonolithicBuildingsDescription" value="Increases the resource output of non-headquarters buildings of the same type on a tile. Decreases the output of non-headquarters buildings of differing types on a tile."/>
	<entry name="AdeptusMechanicus/MonolithicBuildingsFlavor" value="“In uniformity, specialisations. In specialisation, efficiency. In efficiency, ecstasy.”<br/>—Kelphor Zhuko-Dim, Arch Flagellant"/>
	<entry name="AdeptusMechanicus/NeurostaticInterface" value="Neurostatic Interface"/>
	<entry name="AdeptusMechanicus/NeurostaticInterfaceDescription" value="Increases the damage reduction when attacked by adjacent enemy units."/>
	<entry name="AdeptusMechanicus/NeurostaticInterfaceFlavor" value="Amidst the audio-visual cacophony accompanied by a Sicarian Infiltrator attack is wide-band electromagnetic interference, designed to cripple enemy systems and set nerves jangling."/>
	<entry name="AdeptusMechanicus/Omnispex" value="Omnispex"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="Bypasses enemy ranged damage reduction."/>
	<entry name="AdeptusMechanicus/OmnispexFlavor" value="The omnispex carries a raptor-class machine spirit that can read heat emissions, data signatures, and biological waveforms even at extreme range. Should it be kept focussed for an extended period of time, it will determine the weak points of those it scrutinises and pass them on to its master."/>
	<entry name="AdeptusMechanicus/OptateRestrictions" value="Optate Restrictions"/>
	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Increases the population limit."/>
	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="The Optates who control Hive City populations have agreed to release breeding restrictions. In practice, this means removal of anaphrodisiacs from ration packs, rather than the construction of new buildings—the existing living spaces just get that much more crowded instead…"/>
	<entry name="AdeptusMechanicus/PowerSurge" value="<string name='Actions/AdeptusMechanicus/PowerSurge'/>"/>
	<entry name="AdeptusMechanicus/PowerSurgeDescription" value="Increases the output of non-headquarters buildings."/>
	<entry name="AdeptusMechanicus/PowerSurgeFlavor" value="<string name='Actions/AdeptusMechanicus/PowerSurgeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="Increases the ranged accuracy, but decreases the melee accuracy."/>
	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/RadPoisoning" value="Rad Poisoning"/>
	<entry name="AdeptusMechanicus/RadPoisoningDescription" value="Increases the damage against infantry and monstrous creature units."/>
	<entry name="AdeptusMechanicus/RadSaturation" value="<string name='Actions/AdeptusMechanicus/RadSaturation'/>"/>
	<entry name="AdeptusMechanicus/RadSaturationDescription" value="Deals damage each turn."/>
	<entry name="AdeptusMechanicus/RadSaturationFlavor" value="<string name='Actions/AdeptusMechanicus/RadSaturationFlavor'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="Reclaimator Protocols"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Increases the growth rate."/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="Nothing must be wasted—not a drop of food, not an watt of energy, not a scrap of flesh. Executions are minimised in favour of servitor conversions, prompting an increased vigour in all other activities…"/>
	<entry name="AdeptusMechanicus/ServoSkullUplink" value="<string name='Actions/AdeptusMechanicus/ServoSkullUplink'/>"/>
	<entry name="AdeptusMechanicus/ServoSkullUplinkDescription" value="Increases the damage."/>
	<entry name="AdeptusMechanicus/ServoSkullUplinkFlavor" value="<string name='Actions/AdeptusMechanicus/ServoSkullUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="Increases the ranged damage reduction."/>
	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
	<entry name="AdeptusMechanicus/SiphonVigour" value="Siphon Vigour"/>
	<entry name="AdeptusMechanicus/SiphonVigourDescription" value="Killing an enemy unit increases the invulnerable damage reduction."/>
	<entry name="AdeptusMechanicus/SiphonVigourFlavor" value="<string name='Traits/AdeptusMechanicus/SiphonedVigourFlavor'/>"/>
	<entry name="AdeptusMechanicus/SiphonedVigour" value="Siphoned Vigour"/>
	<entry name="AdeptusMechanicus/SiphonedVigourDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="AdeptusMechanicus/SiphonedVigourFlavor" value="They might be obsessed with wasting energy, but the Electro-Priests are no hypocrites. When an enemy dies to the blows of their electroleech staves, the protective Voltagheist field is invigorated with bio electric energy, repulsing even the strongest attacks."/>
	<entry name="AdeptusMechanicus/SolarReflectors" value="Solar Reflectors"/>
	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Increases the energy output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="Planning, planning, planning. The Adeptus Mechanicus Magi design each city so it works as a functioning whole, each part supporting the next. Solar reflectors on all structures direct the small amount of energy they capture through Gladius Prime’s storm systems to the nearest Thermo-Exchanger Shrine."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="Soylens Acquisition Teams"/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Increases the food output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="The Reclaimators employ a small number of the undercity’s population in an unsavory role. These Soylens Acquisition Teams are better known as bodysnatchers and they aggressively hunt out the dead (or nearly dead) ensuring that any future rations always have the very freshest soylens protein…"/>
	<entry name="AdeptusMechanicus/StygianEnlightenment" value="Stygian Enlightenment"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="Reduces the research cost."/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="Some Tech-Priests are more open to studying Xenos technology than others. Only the Forge World of Stygies VIII consistently escapes punishment for these Xenarite tendencies, partly because of their world’s importance, partly because it cripples any attackers. Following their path is certainly a fast way to speed up access to forbidden technologies."/>
	<entry name="AdeptusMechanicus/TerranGeneralism" value="Terran Generalism"/>
	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="Increases the production output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="Though many Forge Worlds have specialised towards particular needs and act as independent empires within the human sphere, it’s worth remembering that most Tech-Priests are generalists, loyally supporting the Imperium in its drive to the stars—and following the orders of Earth as much as Mars."/>
	<entry name="AdeptusMechanicus/Transonic" value="Transonic"/>
	<entry name="AdeptusMechanicus/TransonicDescription" value="Increases damage and temporarily increases armor penetration after attacking."/>
	<entry name="AdeptusMechanicus/TransonicFlavor" value="<string name='Weapons/TransonicBladeFlavor'/>"/>
	<entry name="AdeptusMechanicus/TransonicEffect" value="Transonic Resonance"/>
	<entry name="AdeptusMechanicus/TransonicEffectDescription" value="Increases armor penetration."/>
	<entry name="AdeptusMechanicus/TransonicEffectFlavor" value="<string name='Weapons/TransonicBladeFlavor'/>"/>
	<entry name="AdeptusMechanicus/TriplexNecessity" value="Triplex Necessity"/>
	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Increases the minerals output for each building in an adjacent tile."/>
	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="Following new designs discovered on Triplex Phall, during the construction of new structures, care is taken to explore any subterranean areas and map out any potential ore veins. With a Haemotrope Reactor, its plasma can be directed down these prepared routes to more efficiently extract the ore."/>
	<entry name="AdeptusMechanicus/VoltagheistField" value="Voltagheist Field"/>
	<entry name="AdeptusMechanicus/VoltagheistFieldDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="AdeptusMechanicus/VoltagheistFieldFlavor" value="Nimbuses of pure energy surround all Electro-Priests, crackling from their bare skin tocoalesce into sparking pockets of electromagnetism that hover like will-o-the-wisps above a drowned corpse. When incoming projectiles or energy beams threaten an Electro-Priest these tiny voltaic ghosts will often intercede, shattering or dissipating the threats in puffs of burning ozone. When the wearer of the field charges the foe, those same voltagheists ground upon nearby enemies in bursts of electric force."/>
	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Increases the morale loss."/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
	<entry name="AdeptusMechanicus/WrathOfMars" value="<string name='Actions/AdeptusMechanicus/WrathOfMars'/>"/>
	<entry name="AdeptusMechanicus/WrathOfMarsDescription" value="Increases the damage."/>
	<entry name="AdeptusMechanicus/WrathOfMarsFlavor" value="<string name='Actions/AdeptusMechanicus/WrathOfMarsFlavor'/>"/>
	<entry name="AerialAttack" value="Aerial Attack"/>
	<entry name="AerialAttackDescription" value="Can only target flyers."/>
	<entry name="Agile" value="<string name='Actions/Agile'/>"/>
	<entry name="AgileDescription" value="<string name='Actions/AgileDescription'/>"/>
	<entry name="AmmoRunt" value="Ammo Runt"/>
	<entry name="AmmoRuntDescription" value="Increases the ranged accuracy."/>
	<entry name="AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Amphibious" value="Amphibious"/>
	<entry name="AmphibiousDescription" value="The unit can move over water and ignores the penalties of rivers."/>
	<entry name="AmphibiousFlavor" value="Very few of the standard military vehicles of the 41st millenium are designed with amphibious assault in mind—to reduce weight by eliminating ammunition and armour is often seen as a step too far. However, the Imperial Chimera was designed many millennia ago to be the ultimate flexible light tank and is quite capable of fording rivers."/>
	<entry name="AndTheyShallKnowNoFear" value="And They Shall Know No Fear"/>
	<entry name="AndTheyShallKnowNoFearDescription" value="Reduces the morale loss and grants immunity to fear."/>
	<entry name="AndTheyShallKnowNoFearFlavor" value="Some warriors refuse to surrender, fighting on whatever the odds."/>
	<entry name="Animosity" value="Animosity"/>
	<entry name="AnimosityDescription" value="Reduces the attacks."/>
	<entry name="AnimosityFlavor" value="When the tides of Waaagh! flow, the Orks are visibly larger and stronger—but when the tides ebb, the Orks are weakened too."/>
	<entry name="AntiGravUpwash" value="Anti-Grav Upwash"/>
	<entry name="AntiGravUpwashDescription" value="Increases the movement when the health is above 66%."/>
	<entry name="ApocalypticBarrage" value="Apocalyptic Barrage"/>
	<entry name="ApocalypticBarrageDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticBarrageFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ApocalypticBlast" value="Apocalyptic Blast"/>
	<entry name="ApocalypticBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ApocalypticMegaBlast" value="Apocalyptic Mega-Blast"/>
	<entry name="ApocalypticMegaBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticMegaBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ArmoriumCherub" value="<string name='Actions/ArmoriumCherub'/>"/>
	<entry name="ArmoriumCherubDescription" value="Increases the accuracy."/>
	<entry name="ArmoriumCherubFlavor" value="<string name='Actions/ArmoriumCherubFlavor'/>"/>
	<entry name="Armourbane" value="Armourbane"/>
	<entry name="ArmourbaneDescription" value="Increases the armour penetration."/>
	<entry name="ArmourbaneFlavor" value="This weapon has been crafted with one aim in mind: to pierce the hides of armoured vehicles."/>
	<entry name="Artefact" value="Artefact"/>
	<entry name="ArtefactDescription" value="Classification."/>
	<entry name="ArtefactFlavor" value="Over the millennia, something in the Old One structures dotting Gladius has attracted many races to this planet. They have brought with them objects with extraordinary powers, meaning the surface is littered with alien technologies of all sizes and kinds."/>
	<entry name="Assault" value="Assault"/>
	<entry name="AssaultDescription" value="Classification."/>
	<entry name="AssaultDoctrine" value="Assault Doctrine"/>
	<entry name="AssaultDoctrineDescription" value="Increases the accuracy."/>
	<entry name="AssaultDoctrineFlavor" value="<string name='Actions/AssaultDoctrineFlavor'/>"/>
	<entry name="AssaultVehicle" value="Assault Vehicle"/>
	<entry name="AssaultVehicleDescription" value="Disembarking does not consume movement."/>
	<entry name="AssaultVehicleFlavor" value="This vehicle is specifically designed to disgorge troops into the thick of the fray."/>
	<entry name="AstraMilitarum/BlastDamage" value="Improved Frag Casings"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Increases the armour penetration."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="By using a more advanced engraving protocol on the casing, the lethality of explosive is much improved."/>
	<entry name="AstraMilitarum/BoltDamage" value="Kraken Bolts"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Increases the armour penetration."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="An Astartes-derived upgrade to the standard bolter, this kit allows a bolter to fire Kraken penetrator rounds, hard-tipped projectiles with a heavier explosive charge. These heavier rounds are much favoured by the Space Marine Deathwatch alien hunters."/>
	<entry name="AstraMilitarum/CityTier2" value="Substructure Extension"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="One day, this will be a Hive City—a billion people in one structure, towering over a poisoned world—but first, this settlement needs to expand a little, to set the foundations for the galaxies' biggest conurbations."/>
	<entry name="AstraMilitarum/CityTier3" value="Substructure Supply Tunnels"/>
	<entry name="AstraMilitarum/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="The Departmento Munitorum might be inefficient, but they can plan and the Techpriest Explorators and Servitors can build. These bunkered tunnels allow the Astra Militarum commander to oversee a much larger city area, without losing defensive control."/>
	<entry name="AstraMilitarum/ShootSharpAndScarper" value="Shoot Sharp and Scarper"/>
	<entry name="AstraMilitarum/ShootSharpAndScarperDescription" value="Actions do not consume movement."/>
	<entry name="AstraMilitarum/ShootSharpAndScarperFlavor" value="What Ratlings lack militarily in, well, everything, they make up in knowing when it’s the right time to shoot and when it’s the right time to run."/>
	<entry name="AstraMilitarumAircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarumAircraftProductionEdictDescription" value="Increases the production output."/>
	<entry name="AstraMilitarumAircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumDefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarumDefenseEdictDescription" value="Increases the armour."/>
	<entry name="AstraMilitarumDefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarumEnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarumEnergyEdictDescription" value="Increases the energy output."/>
	<entry name="AstraMilitarumEnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarumFoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarumFoodEdictDescription" value="Increases the food output."/>
	<entry name="AstraMilitarumFoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarumGrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarumGrowthEdictDescription" value="Increases the growth rate."/>
	<entry name="AstraMilitarumGrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarumInfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarumInfantryProductionEdictDescription" value="Increases the production output."/>
	<entry name="AstraMilitarumInfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumInfluenceEdict" value="<string name='Actions/AstraMilitarumInfluenceEdict'/>"/>
	<entry name="AstraMilitarumInfluenceEdictDescription" value="Increases the influence output."/>
	<entry name="AstraMilitarumInfluenceEdictFlavor" value="<string name='Actions/AstraMilitarumInfluenceEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="Hotshot Power Packs"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Increases the armour penetration."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="These superior modular packs convert the standard lasgun into a 'Hellgun', with improved range and power, but reduced power pack capacity and degraded reliability, requiring constant maintenance. Even units that already have dedicated Hotshot lasguns benefit from more reliable Hotshot power pack supplies."/>
	<entry name="AstraMilitarumLoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarumLoyaltyEdictDescription" value="Increases the loyalty output."/>
	<entry name="AstraMilitarumLoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarumOreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarumOreEdictDescription" value="Increases the ore output."/>
	<entry name="AstraMilitarumOreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarumPsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarumPsykerProductionEdictDescription" value="Increases the production output."/>
	<entry name="AstraMilitarumPsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarumResearchEdictDescription" value="Increases the research output."/>
	<entry name="AstraMilitarumResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarumVehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarumVehicleProductionEdictDescription" value="Increases the production output."/>
	<entry name="AstraMilitarumVehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>
	<entry name="AversionToLight" value="Aversion to Light"/>
	<entry name="AversionToLightDescription" value="Increases the damage taken from flame and melta weapons."/>
	<entry name="AversionToLightFlavor" value="The horrors of the Umbra shard flourish at night, in the dark corners of Gladius. But bring a light to them and they shrink, the nightmares turning away. And bring a flamer to them…"/>
	<entry name="Barrage" value="Barrage"/>
	<entry name="BarrageDescription" value="Does not require line of sight, but cannot overwatch."/>
	<entry name="BarrageFlavor" value="<string name='Weapons/BarrageFlavor'/>"/>
	<entry name="Beam" value="Beam"/>
	<entry name="BeamDescription" value="Hits with increased accuracy against multiple group members of the target unit."/>
	<entry name="Bike" value="Bike"/>
	<entry name="BikeDescription" value="Classification."/>
	<entry name="BikeFlavor" value="Units mounted on Bikes excel at vanguard strikes. They are able to use their fast speed to strike deep into enemy territory, complete their mission and escape before an enemy is able to react. These warriors are often regarded as dangerously hot-headed risk-takers, but their effectiveness cannot be denied."/>
	<entry name="Bladestorm" value="Bladestorm"/>
	<entry name="BladestormDescription" value="Increases the damage and armour penetration."/>
	<entry name="Blast" value="Blast"/>
	<entry name="BlastDescription" value="Hits multiple group members of the target unit."/>
	<entry name="BlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="Blighted" value="Blighted"/>
	<entry name="BlightedDescription" value="Deals damage each turn."/>
	<entry name="Blind" value="Blind"/>
	<entry name="BlindDescription" value="Reduces the accuracy."/>
	<entry name="BlindFlavor" value="This attack looses a brilliant flare of light, searing the sight of the victim and forcing him to fight blind for a few moments."/>
	<entry name="Blinding" value="Blinding"/>
	<entry name="BlindingDescription" value="Reduces the accuracy of the target infantry or monstrous creature unit."/>
	<entry name="BloodBlessing" value="Blood Blessing"/>
	<entry name="BloodBlessingDescription" value="Increases the attacks."/>
	<entry name="BloodBlessingFlavor" value="<string name='Features/SkullAltarFlavor'/>"/>
	<entry name="BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="BolsterDefencesDescription" value="Increases the ranged damage reduction."/>
	<entry name="BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="BoltWeapon" value="Bolt Weapon"/>
	<entry name="BoltWeaponFlavor" value="<string name='Weapons/BoltFlavor'/>"/>
	<entry name="BolterDrill" value="Bolter Drill"/>
	<entry name="BolterDrillDescription" value="Increases the accuracy."/>
	<entry name="BolterDrillFlavor" value="<string name='Actions/BolterDrillFlavor'/>"/>
	<entry name="Bomb" value="Bomb"/>
	<entry name="BombDescription" value="Fixed accuracy."/>
	<entry name="Bosspole" value="Bosspole"/>
	<entry name="BosspoleDescription" value="Reduces the morale loss based on the amount of allied units in the area."/>
	<entry name="BosspoleFlavor" value="Ork Nobz often sport a trophy pole that shows they are not to be messed with. A Nob with a Bosspole often finds it comes in handy when cracking heads to restore some order in the heat of battle."/>
	<entry name="BringItDown" value="“Bring it Down!”"/>
	<entry name="BringItDownDescription" value="Increases the armour penetration."/>
	<entry name="Broken" value="Broken"/>
	<entry name="BrokenDescription" value="Reduces the accuracy and increases the damage taken."/>
	<entry name="BruteShield" value="Brute Shield"/>
	<entry name="BruteShieldDescription" value="Increases the damage and damage reduction."/>
	<entry name="BruteShieldFlavor" value="These shields resemble large and resilient energised bucklers. They are carried by some Bullgryns, serving equally well in combat as a defensive measure and handy bludgeon."/>
	<entry name="Bulky" value="Bulky"/>
	<entry name="BulkyDescription" value="Requires an additional cargo slot in a transport."/>
	<entry name="BulkyFlavor" value="This creature is so massive it takes up an inordinate amount of space in any vehicle or building it enters."/>
	<entry name="CamoNetting" value="Camo Netting"/>
	<entry name="CamoNettingDescription" value="Increases the ranged damage reduction."/>
	<entry name="CamoNettingFlavor" value="Whether rare cameleoline netting or crude webbing woven with local flora, camo netting helps conceal a vehicle from prying eyes."/>
	<entry name="Capturable" value="Capturable"/>
	<entry name="CapturableDescription" value="The unit can be captured by another adjacent unit."/>
	<entry name="CeramitePlating" value="Ceramite Plating"/>
	<entry name="CeramitePlatingDescription" value="Increases the armour."/>
	<entry name="CeramitePlatingFlavor" value="These hull plates are thrice-blessed by the Chapter's Techmarines and anointed with the seven sacred unguents of thermic warding to protect against the extreme conditions of orbital re-entry. Such precautions also serve to thwart the fury of certain weapons, absorbing and dispersing even the most extreme temperatures and microwave emissions."/>
	<entry name="Chaff" value="Chaff"/>
	<entry name="ChaffDescription" value="Increases the ranged damage reduction."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="Arcane Occulum"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Boon of Chaos that increases the accuracy."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="A blood-shot eye pushes through the flesh."/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGlory" value="Aura of Dark Glory"/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGloryDescription" value="Increases invulnerable damage reduction."/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGloryFlavor" value="Some champions of Chaos are so blessed by their patron deity that they are supernaturallу protected from harm. They might be surrounded by a powerful, crackling bubble of psychic energy, or else bullets aimed at them may be strangely turned aside just before they strike. In either case, it is clear that the Dark Gods are watching over them."/>
	<entry name="ChaosSpaceMarines/BlastDamage" value="Daemonbone Casings"/>
	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Increases the armour penetration."/>
	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="The Warpsmiths may have only trapped minor horrors in the casings of these explosive shells, but their rage at the point of impact isn’t minor—as they shatter and return to the warp, they rage and snatch at anything nearby."/>
	<entry name="ChaosSpaceMarines/Bloated" value="Bloated"/>
	<entry name="ChaosSpaceMarines/BloatedDescription" value="Restores hitpoints."/>
	<entry name="ChaosSpaceMarines/BloatedFlavor" value="Though Nurgle’s cursed diseases rot a man both physically and spiritually, they also imbue their subjects decaying flesh with inhuman resilience. To such bloated forms, spreading their pox can often bring with it minor blessings from the plague God."/>
	<entry name="ChaosSpaceMarines/BloodForTheBloodGod" value="Blood for the Blood God"/>
	<entry name="ChaosSpaceMarines/BloodForTheBloodGodDescription" value="Increases the attacks."/>
	<entry name="ChaosSpaceMarines/BloodRage" value="Blood Rage"/>
	<entry name="ChaosSpaceMarines/BloodRageDescription" value="Increases the melee attacks and movement."/>
	<entry name="ChaosSpaceMarines/BloodRageFlavor" value="The Helbrute launches itself at nearby enemies, driving its cursed Dreadnought prison past its ancient safety limits into behaviour thoroughly daemonic."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/BoltDamage" value="Warpborne Bolts"/>
	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Increases the armour penetration."/>
	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="Bridging the promethium-powered Inferno Bolts of the Imperium and the psychically-powered Inferno Bolts of the Thousand Sons Traitor Legion, Warpborne Bolt skip through the Warp to unleash superheated chemical fire upon their targets."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="Call of the Dark Gods"/>
	<entry name="ChaosSpaceMarines/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="The people of the Imperium will listen to any lies, believe any tale, which tells them that they’re not doomed. That way, sadly, lies perdition, and the gates of Chaos stand wide open to the lost and the damned—at least, on the way in."/>
	<entry name="ChaosSpaceMarines/CityTier3" value="Nexus of Doom"/>
	<entry name="ChaosSpaceMarines/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="The worshippers of Chaos have shed their pretences. Where before they drew souls to them by their weaknesses or by daemon seduction, now they do it by whips and chains. Packs of slavers and warbands hunt their city’s hinterland for lives to sacrifice. Only the strongest can escape their reach; the weak are grist to the mill for Chaos."/>
	<entry name="ChaosSpaceMarines/Crazed" value="Crazed"/>
	<entry name="ChaosSpaceMarines/CrazedDescription" value="If the unit was damaged in the previous turn, it gains one of the following traits at random for one turn: Fire Frenzy, Rising Fury, Blood Rage."/>
	<entry name="ChaosSpaceMarines/CrazedFlavor" value="All Helbrutes are psychotic and dangerous monstrosities—a veteran of the Long War driven insane by tortured entombment. So attacking a Helbrute might seem rational, but if you fail to destroy it outright, you will only make it angrier and crazier."/> <!-- Hellbrute. -->
	<entry name="ChaosSpaceMarines/ChampionOfChaos" value="Champion of Chaos"/>
	<entry name="ChaosSpaceMarines/ChampionOfChaosDescription" value="Killing an enemy unit may reward this unit with an unlocked Boon of Chaos or transform non-heroes into a Chaos Spawn or Daemon Prince."/>
	<entry name="ChaosSpaceMarines/ChampionOfChaosFlavor" value="It is not unusual for the Chaos powers to bestow strange boons and mutations upon those who kill in their name. Not all of these boons are beneficial—the dark ones are as fickle as they are inscrutable, and even their most ardent followers are little more than pawns in a celestial game."/>
	<entry name="ChaosSpaceMarines/CrystallineBody" value="Crystalline Body"/>
	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Boon of Chaos that increases the hitpoints."/>
	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="The champion's flesh changes to diamond."/>
	<entry name="ChaosSpaceMarines/CultistSacrifice" value="<string name='Actions/ChaosSpaceMarines/CultistSacrifice'/>"/>
	<entry name="ChaosSpaceMarines/CultistSacrificeDescription" value="Increases the growth rate."/>
	<entry name="ChaosSpaceMarines/CultistSacrificeFlavor" value="<string name='Actions/ChaosSpaceMarines/CultistSacrificeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Daemonforge" value="<string name='Actions/ChaosSpaceMarines/Daemonforge'/>"/>
	<entry name="ChaosSpaceMarines/DaemonforgeDescription" value="Increases the damage and armour penetration."/>
	<entry name="ChaosSpaceMarines/DaemonforgeFlavor" value="<string name='Actions/ChaosSpaceMarines/DaemonforgeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DarkGlory" value="<string name='Actions/ChaosSpaceMarines/DarkGlory'/>"/>
	<entry name="ChaosSpaceMarines/DarkGloryDescription" value="<string name='Actions/ChaosSpaceMarines/DarkGloryDescription'/>"/>
	<entry name="ChaosSpaceMarines/DarkGloryFlavor" value="<string name='Actions/ChaosSpaceMarines/DarkGloryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DevourerOfSouls" value="Devourer of Souls"/>
	<entry name="ChaosSpaceMarines/DevourerOfSoulsDescription" value="Restores hitpoints each turn and each time this unit kills an enemy unit."/>
	<entry name="ChaosSpaceMarines/DevourerOfSoulsFlavor" value="Venomcrawlers are perhaps the most sophisticated works of the Warpsmith, spider-like Daemon Engines that crave empyric energy and will consume other daemonic entities to get it—or even the living."/> <!-- Venomcrawler -->
	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Prevents the unit from doing overwatch attacks."/>
	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/FireFrenzy" value="Fire Frenzy"/>
	<entry name="ChaosSpaceMarines/FireFrenzyDescription" value="Reduces movement and increases the ranged attacks."/>
	<entry name="ChaosSpaceMarines/FireFrenzyFlavor" value="In response to the pitiful fire of mortals, the Hellbrute unloads its weapons relentlessly, blind to anything but the deafening roar of its daemonic ammunition."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/DeferredAbsolution" value="<string name='Actions/ChaosSpaceMarines/DeferredAbsolution'/>"/>
	<entry name="ChaosSpaceMarines/DeferredAbsolutionDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="ChaosSpaceMarines/DeferredAbsolutionFlavor" value="<string name='Actions/ChaosSpaceMarines/DeferredAbsolutionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutation" value="Gift of Mutation"/>
	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="At the start of the next turn, the unit gains a new random unlocked Boon of Chaos and this trait ends."/>
	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="The Dark Gods have granted their champion a malign reward, which is as likely to be a razor-sharp appendage as it is to be an inconveniently placed tongue."/>
	<entry name="ChaosSpaceMarines/GraspingPseudopods" value="Grasping Pseudopods"/>
	<entry name="ChaosSpaceMarines/GraspingPseudopodsDescription" value="Increases the melee attacks."/>
	<entry name="ChaosSpaceMarines/GraspingPseudopodsFlavor" value="From the spawn’s twisting form come meaty tentacles, flailing and gibbering at nearby opponents. Come too close and they reflexively wrap themselves around a target, drawing it to whatever maws and teeth the spawn currently has."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/IchorBlood" value="<string name='Actions/ChaosSpaceMarines/IchorBlood'/>"/>
	<entry name="ChaosSpaceMarines/IchorBloodDescription" value="<string name='Actions/ChaosSpaceMarines/IchorBloodDescription'/>"/>
	<entry name="ChaosSpaceMarines/IchorBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/IchorBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="Increases the melee damage reduction when attacked by fearful enemies."/>
	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="Increases the damage reduction."/>
	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="Increases the damage."/>
	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/InfernalFusillade" value="<string name='Actions/ChaosSpaceMarines/InfernalFusillade'/>"/>
	<entry name="ChaosSpaceMarines/InfernalFusilladeDescription" value="Increases the attacks with bolt weapons."/>
	<entry name="ChaosSpaceMarines/InfernalFusilladeFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalFusillade'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustry" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustry'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustryDescription" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustryDescription'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustryFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/InfernalPower" value="<string name='Actions/ChaosSpaceMarines/InfernalPower'/>"/>
	<entry name="ChaosSpaceMarines/InfernalPowerDescription" value="Increases the accuracy and damage."/>
	<entry name="ChaosSpaceMarines/InfernalPowerFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalPowerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LasDamage" value="Forbidden Energies"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Increases the armour penetration."/>
	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="Going beyond the Imperium’s proscribed technologies has allowed the Dark Mechanicum to unlock more lethal and more horrifying weapon capabilities."/>
	<entry name="ChaosSpaceMarines/LasherTendrils" value="<string name='Actions/ChaosSpaceMarines/LasherTendrils'/>"/>
	<entry name="ChaosSpaceMarines/LasherTendrilsDescription" value="Decreases the melee attacks."/>
	<entry name="ChaosSpaceMarines/LasherTendrilsFlavor" value="<string name='Actions/ChaosSpaceMarines/LasherTendrilsFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LoathsomeAura" value="<string name='Actions/ChaosSpaceMarines/LoathsomeAura'/>"/>
	<entry name="ChaosSpaceMarines/LoathsomeAuraDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="ChaosSpaceMarines/LoathsomeAuraFlavor" value="<string name='Actions/ChaosSpaceMarines/LoathsomeAuraFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MalevolentLocus" value="<string name='Actions/ChaosSpaceMarines/MalevolentLocus'/>"/>
	<entry name="ChaosSpaceMarines/MalevolentLocusDescription" value="Deals damage each turn."/>
	<entry name="ChaosSpaceMarines/MalevolentLocusFlavor" value="<string name='Actions/ChaosSpaceMarines/MalevolentLocusFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MaliciousVolleys" value="Malicious Volleys"/>
	<entry name="ChaosSpaceMarines/MaliciousVolleysDescription" value="If the unit remains stationary, they fire Rapid Fire weapons with increased attacks at all ranges."/>
	<entry name="ChaosSpaceMarines/MaliciousVolleysFlavor" value="To a Heretic Astartes the boltgun is far more than a weapon, it is an instrument of his anger and the bringer of death to his foes."/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="Increases the attacks."/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="Increases the hitpoints."/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="Increases the movement."/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="Increases the damage reduction."/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Mechanoid" value="Mechanoid"/>
	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Boon of Chaos that increases the armour."/>
	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="The champion's flesh bonds with his armour."/>
	<entry name="ChaosSpaceMarines/MeleeDamage" value="Conjoined Weaponry"/>
	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Increases the armour penetration."/>
	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="It’s normal for soldiers to grow close to a trusted weapon—but the marines of the Traitor Legions have gone further. Over the centuries, the troops of the Adeptus Astartes have literally grown into their weaponry, becoming one with their guns and blades, exponentially increasing their effectiveness."/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerror" value="Multi-legged Terror"/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerrorDescription" value="Increases the Stomp attacks."/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerrorFlavor" value="The titanic weight of the iron and brass that make up the non-Empyrean parts of the Brass Scorpion rests on six lethally sharp limbs. When it scuttles forward with unexpected, groundshattering speed, it easily trample any smaller foe."/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReason" value="Mutated Beyond Reason"/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReasonDescription" value="At the start of each turn, the unit gains one of the following random mutations for one turn: Grasping Pseudopods, Subcutaneous Armour, Toxic Haemorrhage."/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReasonFlavor" value="When they fall from their god’s favour, Chaos worshippers are blessed and cursed in equal measure, turning into the foul Chaos Spawn—writhing masses of mutation, endlessly sprouting new features at their god’s whim."/>
	<entry name="ChaosSpaceMarines/Possession" value="<string name='Actions/ChaosSpaceMarines/Possession'/>"/>
	<entry name="ChaosSpaceMarines/PossessionDescription" value="<string name='Actions/ChaosSpaceMarines/PossessionDescription'/>"/>
	<entry name="ChaosSpaceMarines/PossessionFlavor" value="<string name='Actions/ChaosSpaceMarines/PossessionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/PrinceOfChaos" value="<string name='Actions/ChaosSpaceMarines/PrinceOfChaos'/>"/>
	<entry name="ChaosSpaceMarines/PrinceOfChaosDescription" value="Increases the accuracy."/>
	<entry name="ChaosSpaceMarines/PrinceOfChaosFlavor" value="<string name='Actions/ChaosSpaceMarines/PrinceOfChaosFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergy" value="<string name='Actions/ChaosSpaceMarines/ReservoirOfDaemonicEnergy'/>"/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergyDescription" value="Reduces the cooldown of Incursion."/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergyFlavor" value="<string name='Actions/ChaosSpaceMarines/ReservoirOfDaemonicEnergyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="Increases the production output."/>
	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="Increases the production and research output."/>
	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="Increases the food output and growth rate."/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="Increases the influence and loyalty output."/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RisingFury" value="Rising Fury"/>
	<entry name="ChaosSpaceMarines/RisingFuryDescription" value="Increases the melee attacks."/>
	<entry name="ChaosSpaceMarines/RisingFuryFlavor" value="The Helbrute’s rage overtakes it, throwing it into a frenzy of blows against anything nearby."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGod" value="Runes of the Blood God"/>
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGodDescription" value="Returns damage from witchfire attacks."/>
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGodFlavor" value="Inscribed on the Brass Scorpion by the adepts of the Dark Mechanicum are shimmering runes, dancing like flames, writhing on the surface. Psykers foolish enough to attempt an assault will find their minds seared by Khorne’s eternal rage."/>
	<entry name="ChaosSpaceMarines/ShatterDefences" value="<string name='Actions/ChaosSpaceMarines/ShatterDefences'/>"/>
	<entry name="ChaosSpaceMarines/ShatterDefencesDescription" value="Reduces the ranged damage reduction."/>
	<entry name="ChaosSpaceMarines/ShatterDefencesFlavor" value="<string name='Actions/ChaosSpaceMarines/ShatterDefencesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/SiegeCrawler" value="Siege Crawler"/>
	<entry name="ChaosSpaceMarines/SiegeCrawlerDescription" value="Increases the armour penetration against fortifications."/>
	<entry name="ChaosSpaceMarines/SiegeCrawlerFlavor" value="Maulerfiends lack ranged weapons of any kind, unusual for a vehicle of their size. Yet their agility and their various cutting weapons mean that they are a huge threat to static fortifications, able to explore them, find a weak spot, and exploit it."/> <!-- Maulerfiend. -->
	<entry name="ChaosSpaceMarines/SubcutaneousArmour" value="Subcutaneous Armour"/>
	<entry name="ChaosSpaceMarines/SubcutaneousArmourDescription" value="Increases the armour."/>
	<entry name="ChaosSpaceMarines/SubcutaneousArmourFlavor" value="Space Marine armour, even as old as that owned by the Traitor Legions, integrates with the unique Black Carapace interface of an Adeptus Astartes. But over the millenia, many Chaos Space Marines have become one with their armour, with the Black Carapace extending throughout their flesh and bones."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/TemporalDistortion" value="Temporal Distortion"/>
	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Boon of Chaos that increases the movement."/>
	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="Time is altered around the champion."/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhage" value="Toxic Haemorrhage"/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhageDescription" value="Increases the damage against infantry and monstrous creature units."/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhageFlavor" value="Spurting vile vapours and heinous fluids, the Chaos Spawn’s cursed innards could spell the death of a less-blessed creature."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="Veterans of the Long War"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Reduces the morale loss, and increases the melee accuracy against units of the Space Marines faction."/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="Many Chaos Space Marine factions have been locked in a constant, grinding war against the Imperium of Man for centuries, if not millennia. The burning hate they feel for their loyalist brothers has had time to fester, now eclipsing all other emotions. This applies, above all, to the original nine Traitor Legions who flocked to Horus' banner ten millennia ago and continue their war against their loyal brethren to this day."/>
	<entry name="ChaosSpaceMarines/WarpFrenzy" value="Warp Frenzy"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Boon of Chaos that increases the attacks."/>
	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="The champion is consumed with anger."/>
	<entry name="ChaosSpaceMarines/WorthyOffering" value="<string name='Actions/ChaosSpaceMarines/WorthyOffering'/>"/>
	<entry name="ChaosSpaceMarines/WorthyOfferingDescription" value="<string name='Actions/ChaosSpaceMarines/WorthyOfferingDescription'/>"/>
	<entry name="ChaosSpaceMarines/WorthyOfferingFlavor" value="<string name='Actions/ChaosSpaceMarines/WorthyOfferingFlavor'/>"/>
	<entry name="ChapterUnity" value="Chapter Unity"/>
	<entry name="ChapterUnityDescription" value="Increases the loyalty output."/>
	<entry name="ChapterUnityFlavor" value="The relics housed in the Great Hall remind the Adeptus Astartes of the heroes of the past, and that one day their armour, weapons and bones could also be revered here."/>
	<entry name="City" value="City"/>
	<entry name="CityDescription" value="Increases the damage reduction and the healing rate for allied units. Decreases the movement cost of units. Increases the ranged damage reduction of infantry units."/>
	<entry name="ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="ClusterMinesDescription" value="Deals damage upon entering the tile."/>
	<entry name="ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="CombatShield" value="Combat Shield"/>
	<entry name="CombatShieldDescription" value="Increases the damage reduction."/>
	<entry name="CombatShieldFlavor" value="A combat shield is a lighter version of the storm shield fitted to the wearer's vambrace, leaving their hand free to wield another weapon whilst providing protection from incoming attacks."/>
	<entry name="CompendiumFlavor" value="If the warriors of the 41st millenium were identical, these wars would be a matter of sheer manpower—and humanity would have won long ago. Instead, each race, troop type and soldier has their own traits, which make them ideal for certain situations—and leave them horribly exposed in others."/>
	<entry name="Concussion" value="Concussion"/>
	<entry name="ConcussionDescription" value="Reduces the accuracy."/>
	<entry name="Concussive" value="Concussive"/>
	<entry name="ConcussiveDescription" value="Temporarily reduces the accuracy of the target infantry or monstrous creature unit."/>
	<entry name="ConcussiveFlavor" value="Some weapons are designed to leave any foe that manages to survive their strike disoriented and easy to slay."/>
	<entry name="ConvergentTargeting" value="Convergent Targeting"/>
	<entry name="ConvergentTargetingDescription" value="Increases the accuracy when adjacent to an allied Thunderfire Cannon."/>
	<entry name="CultAmbush" value="Cult Ambush"/>
	<entry name="CultAmbushDescription" value="Increases the accuracy of overwatch attacks."/>
	<entry name="CultAmbushFlavor" value="Genestealer Cults plan meticulously before each strike, their strategies honed to give every advantage when they rise up as one."/>
	<entry name="CurseOfTheWalkingPox" value="Curse of the Walking Pox"/>
	<entry name="CurseOfTheWalkingPoxDescription" value="Converts damage into healing for this unit."/>
	<entry name="CurseOfTheWalkingPoxFlavor" value="The rictus grin adorning each Poxwalker belies the torment their souls are in, trapped inside their mutated, dead bodies that fight on for Nurgle, infecting more innocents with the Walking Plague as they go."/>
	<entry name="Daemon" value="Daemon"/>
	<entry name="DaemonDescription" value="Increases the damage reduction."/>
	<entry name="DaemonFlavor" value="The creatures of the Warp are many and foul, with infinite variety, but there are some characteristics that they all share."/>
	<entry name="Damaged" value="Damaged"/>
	<entry name="DamagedDescription" value="Decreases the accuracy."/>
	<entry name="Deathshriek" value="Deathshriek"/>
	<entry name="DeathshriekDescription" value="Damages attacker on death."/>
	<entry name="DeathshriekFlavor" value="At the point of an Umbra’s death, all those around it receive a vision—of the long ago creature that was torn asunder and cast into the Warp, whilst a voice, horrifying and alluring, whispers a curse… “LINGER…”"/>
	<entry name="DeedsOfGlory" value="Deeds of Glory"/>
	<entry name="DeedsOfGloryDescription" value="<string name='Actions/DeedsOfGloryDescription'/>"/>
	<entry name="DestroyerWeapon" value="Destroyer Weapon"/>
	<entry name="DestroyerWeaponDescription" value="Increases the damage."/>
	<entry name="DevastatorDoctrine" value="Devastator Doctrine"/>
	<entry name="DevastatorDoctrineDescription" value="Increases the accuracy."/>
	<entry name="DevastatorDoctrineFlavor" value="<string name='Actions/DevastatorDoctrineFlavor'/>"/>
	<entry name="Discipline" value="Discipline"/>
	<entry name="DisciplineDescription" value="Increases the accuracy."/>
	<entry name="DisciplineFlavor" value="<string name='Actions/AuraOfDisciplineFlavor'/>"/>
	<entry name="DistortScythe" value="Distort Scythe"/>
	<entry name="DistortScytheDescription" value="Increases the damage."/>
	<entry name="DistortScytheFlavor" value="<string name='Weapons/DistortionFlavor'/>"/>
	<entry name="DogmaAstrates" value="Dogma Astartes"/>
	<entry name="DogmaAstratesDescription" value="<string name='Actions/DogmaAstratesDescription'/>"/>
	<entry name="DogmaAstratesFlavor" value="<string name='Actions/DogmaAstratesFlavor'/>"/>
	<entry name="DozerBlade" value="Dozer Blade"/>
	<entry name="DozerBladeDescription" value="Reduces the movement penalty in forests and imperial ruins."/>
	<entry name="DozerBladeFlavor" value="Dozer blades are heavy ploughs, blades, rams, or scoops, used to clear obstacles from the vehicle's path."/>
	<entry name="Drukhari/AncientEvil" value="<string name='Actions/Drukhari/AncientEvil'/>"/>
	<entry name="Drukhari/AncientEvilDescription" value="Attacked units lose morale."/>
	<entry name="Drukhari/AncientEvilFlavor" value="<string name='Actions/Drukhari/AncientEvilFlavor'/>"/>
	<entry name="Drukhari/AssaultWeaponBonus" value="Deviant Designs"/>
	<entry name="Drukhari/AssaultWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="Drukhari/AssaultWeaponBonusFlavor" value="The weaponmasters of the Kabalite foundries possess access to weapon variants normally considered impossible or totally unethical. Which, of course, is simply a question about how much a particular Archon is willing to pay to get them."/>
	<entry name="Drukhari/BetrayalCulture" value="Betrayal Culture"/>
	<entry name="Drukhari/BetrayalCultureDescription" value="Drukhari cities have lower loyalty by default. They get additional loyalty as they gather Influence."/>
	<entry name="Drukhari/BetrayalCultureFlavor" value="To the depraved Drukhari, it seems normal for a child betraying their parents into an eternity of torture just for the merest inkling of advantage. There is no trust in a Drukhari city, only power. The more powerful you are, the more will flock to your side."/>
	<entry name="Drukhari/BetrayalCultureUpgrade" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgrade'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgradeDescription'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgradeDescription'/>"/>
	<entry name="Drukhari/BladeArtists" value="Blade Artists"/>
	<entry name="Drukhari/BladeArtistsDescription" value="Increases the armour penetration in melee."/>
	<entry name="Drukhari/BladeArtistsFlavor" value="Every denizen of Commorragh learns from a young age the value of blades, and all are adept in their usage, whether those wielded by their cruel hands or as part of their razor-edged armour."/>
	<entry name="Drukhari/BladeWhip" value="Blade Whip"/> 
	<entry name="Drukhari/BladeWhipDescription" value="Increases accuracy."/>
	<entry name="Drukhari/BladeWhipFlavor" value="Only the Lacerai about the Wych cults regularly utilise the segmented blade known as a Razorflail, but their ability with it is legendary. Attacks that seem certain to miss curve to connect, shifting between whips and blades at will."/>
	<entry name="Drukhari/BloodDancer" value="<string name='Actions/Drukhari/BloodDancer'/>"/>
	<entry name="Drukhari/BloodDancerDescription" value="<string name='Actions/Drukhari/BloodDancerDescription'/>"/>
	<entry name="Drukhari/BloodDancerFlavor" value="<string name='Actions/Drukhari/BloodDancerFlavor'/>"/>
	<entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
	<entry name="Drukhari/BonusResourcesDescription" value="Increases the output of buildings."/>
	<entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
	<entry name="Drukhari/BridesOfDeath" value="<string name='Actions/Drukhari/BridesOfDeath'/>"/>
	<entry name="Drukhari/BridesOfDeathDescription" value="Increases the melee damage."/>
	<entry name="Drukhari/BridesOfDeathFlavor" value="<string name='Actions/Drukhari/BridesOfDeathFlavor'/>"/>
	<entry name="Drukhari/CityTier2" value="Swollen With Sin"/>
	<entry name="Drukhari/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="Drukhari/CityTier2Flavor" value="As the Drukhari raiders become accustomed to a planet, their raiding base spreads out more widely, attracting more of the vilest residents of the galaxy, and able to support a larger population of the vile inheritors to the Aeldari. The slums and torture gardens of the city spread further outwards and downwards…"/>
	<entry name="Drukhari/CityTier3" value="Domain Of Despair"/>
	<entry name="Drukhari/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="Drukhari/CityTier3Flavor" value="So wealthy have the Archons and Dracons of this pirate realm become that they have established palaces for themselves here, knowing full well that they cannot stay, lest She Who Thirsts consume their souls completely. Of course, whilst they are here, they need a constant supply of captives to slake their inhuman lusts and stave off the rotting of their souls."/>
	<entry name="Drukhari/CombatDrugs" value="Combat Drugs"/>
	<entry name="Drukhari/CombatDrugsDescription" value="Increases various combat abilities."/>
	<entry name="Drukhari/CombatDrugsFlavor" value="Though they drastically shorten the life expectancy of the user, chemical stimulants are widely used to heighten combat performance."/>
	<entry name="Drukhari/CorsairOutposts" value="Corsair Outposts"/>
	<entry name="Drukhari/CorsairOutpostsDescription" value="Drukhari cities have lower growth by default. Controlled outposts grant additional growth."/>
	<entry name="Drukhari/CorsairOutpostsFlavor" value="Though these townships appear like cities to the herd races, the Drukhari will not remain in them long unless they know there is wealth to be found nearby. But more successful raiding bases will attract more Drukhari, keen to get their share of the murder and pain."/>
	<entry name="Drukhari/CorsairOutpostsUpgrade" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgrade'/>"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgradeDescription'/>"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgradeDescription'/>"/>
	<entry name="Drukhari/Dodge" value="Dodge"/>
	<entry name="Drukhari/DodgeDescription" value="Increases the melee damage reduction."/>
	<entry name="Drukhari/DodgeFlavor" value="The diverse warriors – known as Hekatarii – within the Wych Cults wield a wildly divergent array of exotic tools to maim, entrap, slash and skewer. They wear little in the way of armour; their defence is their speed and agility, displaying their arrogance as they dodge the clumsy blows of the foe."/>
	<entry name="Drukhari/EnergyBuildingBonus" value="Relaxed Ilmaea Manifolds"/>
	<entry name="Drukhari/EnergyBuildingBonusDescription" value="Increases the energy output."/>
	<entry name="Drukhari/EnergyBuildingBonusFlavor" value="This power station is only accessing the tiniest fraction of the power of one of the Drukhari’s Ilmaea—the captured Black Suns that ring Commorragh. Relaxing the safety manifolds just a touch boosts the power commensurately—as well as massively increasing the chance the Ilmaea errs and consumes Gladius Prime in a gulp. That’s a risk the near-immortal Drukhari are happy to take."/>
	<entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
	<entry name="Drukhari/EnhancedAethersailsDescription" value="<string name='Actions/Drukhari/EnhancedAethersailsDescription'/>"/>
	<entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
	<entry name="Drukhari/Flickerfield" value="Flickerfield"/>
	<entry name="Drukhari/FlickerfieldDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="Drukhari/FlickerfieldFlavor" value="Flickerfields are highly advanced optical force shields that make the vehicle they are fitted to appear to flicker in and out of reality."/>
	<entry name="Drukhari/GhostplateArmour" value="Ghostplate Armour"/>
	<entry name="Drukhari/GhostplateArmourDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="Drukhari/GhostplateArmourFlavor" value="Those Drukhari who desire a substantial amount of protection whilst retaining a high degree of mobility wear armour made from hardened resins and shot through with pockets of lighter-than-air gas. Ghostplate armour also incorporates minor forcefield technology the better to protect its wearer."/>
	<entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
	<entry name="Drukhari/GrislyTrophiesDescription" value="Reduces the morale loss."/>
	<entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
	<entry name="Drukhari/HeavyWeaponBonus" value="Equipoise Ordnance"/>
	<entry name="Drukhari/HeavyWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="Drukhari/HeavyWeaponBonusFlavor" value="“Do not hope for a new day, little mortals. The Drukhari have you now and our grip is unshakeable. I assure you, all you will know now is eternal night—and eternal torment.” – Gyrthineus Roche, Archon of The Last Blade"/>
	<entry name="Drukhari/MasterOfPain" value="<string name='Actions/Drukhari/MasterOfPain'/>"/>
	<entry name="Drukhari/MasterOfPainDescription" value="Grants additional feel no pain damage reduction."/>
	<entry name="Drukhari/MasterOfPainFlavor" value="<string name='Actions/Drukhari/MasterOfPainFlavor'/>"/>
	<entry name="Drukhari/MeleeWeaponBonus" value="Blade Aesthetics, Perfected"/>
	<entry name="Drukhari/MeleeWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="Drukhari/MeleeWeaponBonusFlavor" value="“Pain is the only universal constant. Pain is all. It is the key to creation and destruction both. Thus does he who masters pain become a god.” – Urien Rakarth, Master of the Haemonculi"/>
	<entry name="Drukhari/NightShields" value="Night Shields"/>
	<entry name="Drukhari/NightShieldsDescription" value="Increases the ranged damage reduction."/>
	<entry name="Drukhari/NightShieldsFlavor" value="The vehicle is covered by a broad-spectrum displacement field, enveloping it in cold and inky darkness. Foes find the vehicle hard to target, hidden as it is within a cloak of roiling shadow."/>
	<entry name="Drukhari/NoEscape" value="<string name='Actions/Drukhari/NoEscape'/>"/>
	<entry name="Drukhari/NoEscapeDescription" value="Reduces the movement."/>
	<entry name="Drukhari/NoEscapeFlavor" value="<string name='Actions/Drukhari/NoEscapeFlavor'/>"/>
	<entry name="Drukhari/Overlord" value="<string name='Actions/Drukhari/Overlord'/>"/>
	<entry name="Drukhari/OverlordDescription" value="Increases the accuracy."/>
	<entry name="Drukhari/OverlordFlavor" value="<string name='Actions/Drukhari/OverlordFlavor'/>"/>
	<entry name="Drukhari/PowerFromPain" value="Power from Pain"/>
	<entry name="Drukhari/PowerFromPainDescription" value="Grants combat bonuses as the unit levels up. Increases damage reduction at and above level 3, increases melee damage at and above level 6 and reduces the morale loss at level 10."/>
	<entry name="Drukhari/PowerFromPainFlavor" value="As Drukhari feed on the souls of their foes, they become imbued with supernatural might, eventually turning into killing machines."/>
	<entry name="Drukhari/RaidersTactics" value="Raiders Tactics"/>
	<entry name="Drukhari/RaidersTacticsDescription" value="Grants bonus traits when this unit disembarks from a transport."/>
	<entry name="Drukhari/RaidersTacticsFlavor" value="“Why do we ride atop these elegant craft? The better to hear the screams of our prey as we ride them down, to savour the fear etched on their faces, to taste the tantalising tang of their blood in the air as an appetiser before the feast. But most of all we ride them so that the slaughter may begin as soon as possible.” – Dariaq Bladetongue of the Pierced Eye"/>
	<entry name="Drukhari/RaidersTacticsDamage" value="Raider Assault"/>
	<entry name="Drukhari/RaidersTacticsDamageDescription" value="Increases the damage."/>
	<entry name="Drukhari/RaidersTacticsDamageFlavor" value="Millenia of depredations on the mortal plane mean the Drukhari are inhuman opportunity predators unparalleled in history. This is partly from centuries of experience, partly from evolution and partly from the adaptations of the Haemonculi. When they emerge from their skycraft, they fall upon their enemy like descending eagles."/>
	<entry name="Drukhari/RaidersTacticsDamageReduction" value="Raider Evasion"/>
	<entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="The Drukhari live to inflict pain—and there are crucial moments in their existence that exemplify this. When an incubi’s glaive falls; when a Wych torments her victim in the arena; and the split-second of deadly surprise as the Drukhari raiders leap from their skycraft…"/>
	<entry name="Drukhari/RaidersTacticsHealingRate" value="Raider Readiness"/>
	<entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Increases the healing rate when this unit is embarked in a vehicle."/>
	<entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="“Friends—I call you that, though I know you’d knife me in the back at a glance from Vect—friends, hold yourselves ready. The moment is almost here, the herd do not even know we’re coming. In but minutes, we will descend, flay, rend, torment, murder and drink their souls… with style, of course. We’re not savages.” – Gyrthineus Roche, Archon of The Last Blade"/>
	<entry name="Drukhari/Shadowfield" value="<string name='Actions/Drukhari/Shadowfield'/>"/>
	<entry name="Drukhari/ShadowfieldDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="Drukhari/ShadowfieldFlavor" value="<string name='Actions/Drukhari/ShadowfieldFlavor'/>"/>
	<entry name="Drukhari/SacrificeToKhaine" value="Sacrifice to Khaine"/>
	<entry name="Drukhari/SacrificeToKhaineDescription" value="Increases the damage."/>
	<entry name="Drukhari/SacrificeToKhaineFlavor" value="The Drukhari do not worship the old Aeldari gods or the OId Ones, save for one—Kaela Mensha Khaine, god of murder and war. Only a handful of Drukhari pay fealty to Khaine and most of those are Incubi, the dark mirrors of the Aeldari aspect warriors. Every time they kill with their ritual glaives, it is in praise of Khaine."/>
	<entry name="Drukhari/ShroudGate" value="Shroud Gate"/>
	<entry name="Drukhari/ShroudGateDescription" value="Increases the ranged damage reduction."/>
	<entry name="Drukhari/ShroudGateFlavor" value="“It was a wise to lay the ambush at the webway gate, brave little human. Hush, now. But you did not know when and where we would emerge, and that was your mistake. To we Drukhari, the gates are an unnecessary convenience… but your suffering, that I am afraid, is a necessity.” – Gyrthineus Roche, Archon of The Last Blade"/>
	<entry name="Drukhari/SoulHunger" value="Soul Hunger"/>
	<entry name="Drukhari/SoulHungerDescription" value="Grants influence when killing an enemy."/>
	<entry name="Drukhari/SoulHungerFlavor" value="The only form of sustenance that refreshes a Drukhari—the only thing that makes their immortal lives at all palatable and endurable, as Slaanesh saps their souls—is the suffering of others. And those Archons who can ensure a regular supply of victims and of pain receive loyalty—or rather, the nearest to it that a Drukhari can manage…"/>
	<entry name="Drukhari/SoulHungerLoyalty" value="Soulbread and Circuses"/>
	<entry name="Drukhari/SoulHungerLoyaltyDescription" value="Increases the loyalty output."/>
	<entry name="Drukhari/SoulHungerLoyaltyFlavor" value="In a world so devoid of loyalty, it pays to keep the masses on side—who knows when you will need to drive a lynch mob after your rival, for example? When an Archon pays for a Wych cult to perform in the arena or dispenses food, weapons or victims to the desperate of Commorragh’s undercity, every Drukhari understands that he does it not from compassion, but out of calculation."/>
	<entry name="Drukhari/SoulHungerOutposts" value="Soul Tithe"/>
	<entry name="Drukhari/SoulHungerOutpostsDescription" value="Increases the resource output."/>
	<entry name="Drukhari/SoulHungerOutpostsFlavor" value="Of course, all Drukhari Kabalites are thoroughly beholden to their Archon and would do nothing to lose their favour… yet, somehow, still, there manages to be a thriving black market in stolen resources, especially further away from the seat of power. So when the order comes in to send their tithes, it’s surprisingly easy to gather enough."/>
	<entry name="Drukhari/SoulHarvest" value="<string name='Actions/Drukhari/SoulHarvest'/>"/>
	<entry name="Drukhari/SoulHarvestDescription" value="Increases the damage and grants influence when killing an enemy."/>
	<entry name="Drukhari/SoulHarvestFlavor" value="<string name='Actions/Drukhari/SoulHarvestFlavor'/>"/>
	<entry name="Drukhari/FeastOfTorment" value="Feast of Torment"/>
	<entry name="Drukhari/FeastOfTormentDescription" value="Restores hitpoints each turn."/>
	<entry name="Drukhari/FeastOfTormentFlavor" value="Encouraging the Drukhari to acts of sadism is unnecessary, it comes so naturally to them. However, they still fear their Archon’s wrath, so giving them explicit permission to torture the innocents of Gladius Prime rather than ship them back to Commorragh is sometimes necessary. As a side note, innocent blood has a wonderfully rejuvenating effect."/>
	<entry name="Drukhari/SpiritProbe" value="<string name='Actions/Drukhari/SpiritProbe'/>"/>
	<entry name="Drukhari/SpiritProbeDescription" value="Increases the damage reduction."/>
	<entry name="Drukhari/SpiritProbeFlavor" value="<string name='Actions/Drukhari/SpiritProbeFlavor'/>"/>
	<entry name="Drukhari/ToweringArrogance" value="<string name='Actions/Drukhari/ToweringArrogance'/>"/>
	<entry name="Drukhari/ToweringArroganceDescription" value="Decreases the morale loss."/>
	<entry name="Drukhari/ToweringArroganceFlavor" value="<string name='Actions/Drukhari/ToweringArroganceFlavor'/>"/>
	<entry name="Drukhari/WealthPlunder" value="Studies in Agony"/>
	<entry name="Drukhari/WealthPlunderDescription" value="Grants research when killing an enemy."/>
	<entry name="Drukhari/WealthPlunderFlavor" value="“Near to death, I hold them. The secrets they whisper. The pleas. Tactics, inventions, knowledge of the other side, revelations… everything spilled. To stop the pain. Why should it stop, though. When it is so… productive.” – Arkanic, Haemonculus to Kabal of The Last Blade"/>
	<entry name="Drukhari/WeaponRacks" value="Weapon Racks"/>
	<entry name="Drukhari/WeaponRacksDescription" value="Grants twin-linked for one turn to units that disembark from this vehicle."/>
	<entry name="Drukhari/WeaponRacksFlavor" value="Some Drukhari vehicles carry additional racks of anti-personnel weaponry on their decks. This allows passengers to literally empty their weapons’ magazines in great raking fusillades, before discarding their spent guns in favour of fully-loaded replacements as they disembark."/>
	<entry name="Drukhari/WhirlingDeath" value="<string name='Actions/Drukhari/WhirlingDeath'/>"/>
	<entry name="Drukhari/WhirlingDeathDescription" value="Hits all group members of the target unit."/>
	<entry name="Drukhari/WhirlingDeathFlavor" value="<string name='Actions/Drukhari/WhirlingDeathFlavor'/>"/>
	<entry name="EavyArmour" value="'Eavy Armor"/>
	<entry name="EavyArmourDescription" value="Increases the armour."/>
	<entry name="EavyArmourFlavor" value="Ork 'eavy armour is hammered out of scrap iron, sheet metal and the looted battle-plate of fallen foes. Though its fit is dubious, 'eavy armour provides a solid defence for its wearer."/>
	<entry name="Eldar/AerobaticGrace" value="Aerobatic Grace"/>
	<entry name="Eldar/AerobaticGraceDescription" value="Grants ranged damage reduction if the unit has moved this turn."/>
	<entry name="Eldar/AerobaticGraceFlavor" value="While the Harlequins are the true masters of dance amongst the Aeldari, the Shining Spears approach them in their control of their jetbikes, seeming to pirouette between storms of bolter fire as they charge their foe."/>
	<entry name="Eldar/AircraftBuildingBonus" value="Kurnous' Summons"/>
	<entry name="Eldar/AircraftBuildingBonusDescription" value="Increases the production output."/>
	<entry name="Eldar/AircraftBuildingBonusFlavor" value="The Crimson Hunters owe their allegiance to Kurnous, the Aeldari God of the Hunt. Though he is 60 million years dead, when his name is invoked they redouble their efforts, both in recruitment and construction."/>
	<entry name="Eldar/AncientDoom" value="Ancient Doom"/>
	<entry name="Eldar/AncientDoomDescription" value="Increases the accuracy and damage taken against Chaos units."/>
	<entry name="Eldar/AncientDoomFlavor" value="The Aeldari loathe and fear She Who Thirsts above all else, for in Slaanesh they see their doom made manifest."/>
	<entry name="Eldar/AssaultWeaponBonus" value="Sub-Molecular Ammunition"/>
	<entry name="Eldar/AssaultWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="Eldar/AssaultWeaponBonusFlavor" value="The unique shuriken ammunition of the Aeldari consists of monomolecular spinning blades fired at great speed. But molecules are so limiting when there are smaller, more deadly structures to use."/>
	<entry name="Eldar/AssuredDestruction" value="Assured Destruction"/>
	<entry name="Eldar/AssuredDestructionDescription" value="Increases the damage against vehicles."/>
	<entry name="Eldar/AssuredDestructionFlavor" value="“If a captain is foolish enough to allow five Aeldari with meltaguns and several millenia of combat experience to get within 20 metres of his tank… well, frankly, he deserves to be a molten puddle of ichor on the floor.”<br/>  — Commissar Gruber"/>
	<entry name="Eldar/AsuryaniArrivals" value="<string name='Actions/Eldar/AsuryaniArrivals'/>"/>
	<entry name="Eldar/AsuryaniArrivalsDescription" value="<string name='Actions/Eldar/AsuryaniArrivalsDescription'/>"/>
	<entry name="Eldar/AsuryaniArrivalsFlavor" value="<string name='Actions/Eldar/AsuryaniArrivalsFlavor'/>"/>
	<entry name="Eldar/AutarchsAssault" value="<string name='Actions/Eldar/AutarchsAssault'/>"/>
	<entry name="Eldar/AutarchsAssaultDescription" value="Increases the damage."/>
	<entry name="Eldar/AutarchsAssaultFlavor" value="<string name='Actions/Eldar/AutarchsAssaultFlavor'/>"/>
	<entry name="Eldar/AutarchsAssaultPassive" value="<string name='Actions/Eldar/AutarchsAssault'/>"/>
	<entry name="Eldar/BansheeMask" value="Banshee Mask"/>
	<entry name="Eldar/BansheeMaskDescription" value="<string name='Traits/InfiltrateDescription'/>"/>
	<entry name="Eldar/BansheeMaskFlavor" value="These amplify the Aeldari’s battle cry, inflicting psychic paralysis."/>
	<entry name="Eldar/BattleFocus" value="Battle Focus"/>
	<entry name="Eldar/BattleFocusDescription" value="Actions do not consume movement."/>
	<entry name="Eldar/BattleFocusFlavor" value="When the Asuryani don their war masks, they enter a battle trance so focused that they flow across the battlefield like quicksilver, killing their foes without breaking stride."/>
	<entry name="Eldar/CityTier2" value="Exodite Foothold"/>
	<entry name="Eldar/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="Eldar/CityTier2Flavor" value="Despite the millenia they’ve spent drifting the stars, some Rangers of the Craftworld Aeldari have passed time on the Exodite worlds and Maiden worlds, and have learned how to establish life planetside, growing food and constructing their wraithbone buildings in the ancient ways."/>
	<entry name="Eldar/CityTier3" value="Exodite Infrastructure"/>
	<entry name="Eldar/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="Eldar/CityTier3Flavor" value="Experienced Exodite construction designs are being employed by the Craftworld’s Bonesingers here. Only having worked on the ancient Craftworlds, the idea of building a city for the first time is something utterly new to their minds."/>
	<entry name="Eldar/Command" value="<string name='Actions/Eldar/Command'/>"/>
	<entry name="Eldar/CommandDescription" value="Increases the accuracy."/>
	<entry name="Eldar/CommandFlavor" value="<string name='Actions/Eldar/CommandFlavor'/>"/>
	<entry name="Eldar/ConstructionBuildingBonus" value="Bonesinger Convocation"/>
	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Increases the production output."/>
	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="Bonesingers are rare and cherished, even among the scattered Aeldari—but they have come in numbers to the Farseer’s summons, to capture this unique planet for their dying race."/>
	<entry name="Eldar/CrackShot" value="Crack Shot"/>
	<entry name="Eldar/CrackShotDescription" value="Increases the accuracy and armour penetration."/>
	<entry name="Eldar/CrackShotFlavor" value="The Fire Dragons are rightly feared as tank hunters and none more so than their Exarch, who leads his squad from the front, inspiring his squad to more destruction with his own devastating feats."/>
	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Actions/Eldar/CrystalTargetingMatrix'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixDescription" value="<string name='Actions/Eldar/CrystalTargetingMatrixDescription'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Actions/Eldar/CrystalTargetingMatrixFlavor'/>"/>
	<entry name="Eldar/Doom" value="<string name='Actions/Eldar/Doom'/>"/>
	<entry name="Eldar/DoomDescription" value="Increases the damage taken."/>
	<entry name="Eldar/DoomFlavor" value="<string name='Actions/Eldar/DoomFlavor'/>"/>
	<entry name="Eldar/ExpertHunter" value="Expert Hunter"/>
	<entry name="Eldar/ExpertHunterDescription" value="Increases the damage against monstrous creatures, vehicles and fortifications."/>
	<entry name="Eldar/ExpertHunterFlavor" value="The Shining Spears weaponry might be short-ranged but their Star Lances and Laser Lances are incredibly powerful, as they weave around larger targets, spying out their weak points."/>
	<entry name="Eldar/FoodBuildingBonus" value="Exquisite Cultivation"/>
	<entry name="Eldar/FoodBuildingBonusDescription" value="Increases the food output."/>
	<entry name="Eldar/FoodBuildingBonusFlavor" value="So intensely do the Aeldari feel that even their basic foodstuffs are works of art, cultivated by craftsmen that have spent millenia perfecting their cuisine. With a wider range of ingredients and seasonings, they can work miracles with the seeming simplest foodstuffs."/>
	<entry name="Eldar/Forceshield" value="Forceshield"/>
	<entry name="Eldar/ForceshieldDescription" value="Increases the damage reduction."/>
	<entry name="Eldar/ForceshieldFlavor" value="These powerful shield projectors can deflect almost any blow."/>
	<entry name="Eldar/HeavyWeaponBonus" value="Engine of Vaul"/>
	<entry name="Eldar/HeavyWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="Eldar/HeavyWeaponBonusFlavor" value="Whilst humanity’s plasma weaponry is often fatal to its users, the elegant Aeldari equivalents harbour no such threats. And now a chance discovery on Gladius Prime’s surface has allowed the Bonesingers and Exarchs to further improve their focus."/>
	<entry name="Eldar/HoloFields" value="Holo-Fields"/>
	<entry name="Eldar/HoloFieldsDescription" value="Increases the damage reduction if the unit has moved this turn."/>
	<entry name="Eldar/HoloFieldsFlavor" value="Harnessing kinetic energy to shimmer and distort the vehicle’s silhouette, holo-fields prevent the foe from accurately targeting the craft as it sweeps across the battlefield."/>
	<entry name="Eldar/InescapableAccuracy" value="Inescapable Accuracy"/>
	<entry name="Eldar/InescapableAccuracyDescription" value="Increases the accuracy against bikes, jetbikes, flyers and vehicles that moved this turn."/>
	<entry name="Eldar/InescapableAccuracyFlavor" value="Inherited aeons of combat experience against their kin, the raiding Drukhari, has taught the Dark Reaper Aspect Warriors of the Craftworld Aeldari how to track even their own jinking, holofield-protected jetbikes. Hitting such targets with their deadly Reaper missiles is simple to them—and all part of their emulation of Khaine in his role of The Destroyer."/>
	<entry name="Eldar/InfantryBuildingBonus" value="Asuryan's Summons"/>
	<entry name="Eldar/InfantryBuildingBonusDescription" value="Increases the production output."/>
	<entry name="Eldar/InfantryBuildingBonusFlavor" value="Asurmen is the founder of the path of the Warrior and the first of the Phoenix Lords, the creators of the Aspect Shrines. Invoking his name will surely bring more warriors to Gladius Prime, to seize this planet for the Aeldari."/>
	<entry name="Eldar/InfluenceBuildingBonus" value="Lileath's Oracle"/>
	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Increases the influence output."/>
	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="Very few mortal beings have ever had the foresight of a Farseer—a power perhaps derived from the lost Aeldari goddess Lileath, who predicted the death of the gods at the hands of the mortal Aeldari. An oracle that invokes her name draws the eyes of all Aeldari upon you…"/>
	<entry name="Eldar/Jinx" value="Jinx"/>
	<entry name="Eldar/JinxDescription" value="Decreases the armour."/>
	<entry name="Eldar/JinxFlavor" value="<string name='Actions/Eldar/ProtectJinxFlavor'/>"/>
	<entry name="Eldar/KhaineAwakened" value="<string name='Actions/Eldar/KhaineAwakened'/>"/>
	<entry name="Eldar/KhaineAwakenedDescription" value="Increases the melee attacks and melee damage, decreases the morale loss and grants immunity to fear and pinning."/>
	<entry name="Eldar/KhaineAwakenedFlavor" value="<string name='Actions/Eldar/KhaineAwakenedFlavor'/>"/>
	<entry name="Eldar/KhainesMight" value="<string name='Actions/Eldar/KhainesMight'/>"/>
	<entry name="Eldar/KhainesMightDescription" value="Increases the melee attacks."/>
	<entry name="Eldar/KhainesMightFlavor" value="<string name='Actions/Eldar/KhainesMightFlavor'/>"/>
	<entry name="Eldar/LinkedFire" value="<string name='Actions/Eldar/LinkedFire'/>"/>
	<entry name="Eldar/LinkedFireDescription" value="Increases the damage and armour penetration of the Prism Cannon."/>
	<entry name="Eldar/LinkedFireFlavor" value="<string name='Actions/Eldar/LinkedFireFlavor'/>"/>
	<entry name="Eldar/MarksmansEye" value="Marksman's Eye"/>
	<entry name="Eldar/MarksmansEyeDescription" value="Increases the accuracy."/>
	<entry name="Eldar/MarksmansEyeFlavor" value="Only a Crimson Hunter would consider piloting the fastest, most manuevrable Aeldari craft to involve so little challenge, that they also are able to aim and fire three separate weapons with differing fire rates at the same time."/>
	<entry name="Eldar/MeleeWeaponBonus" value="Sub-Molecular Planes"/>
	<entry name="Eldar/MeleeWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="Eldar/MeleeWeaponBonusFlavor" value="The monomolecular filaments and blades of the Aeldari are lethal enough by themselves, but with the smallest addition of phasing technology (as used in the Harlequin’s Caress), they pass through even the thickest armour."/>
	<entry name="Eldar/MindWar" value="<string name='Actions/Eldar/MindWar'/>"/>
	<entry name="Eldar/MindWarDescription" value="Decreases the accuracy."/>
	<entry name="Eldar/MindWarFlavor" value="<string name='Actions/Eldar/MindWarFlavor'/>"/>
	<entry name="Eldar/MoltenBody" value="Molten Body"/>
	<entry name="Eldar/MoltenBodyDescription" value="Immune to flame and melta weapons."/>
	<entry name="Eldar/MoltenBodyFlavor" value="The Avatar of Khaine isn’t merely the last fragment of a dying god—more practically, it’s a construct of super-heated iron with a core that is literally lava, animated by the psychic power of the Aeldari and the god’s undying rage. Heating it up only makes it more angry…"/>
	<entry name="Eldar/OreBuildingBonus" value="Extraction Efficiency"/>
	<entry name="Eldar/OreBuildingBonusDescription" value="Increases the ore output."/>
	<entry name="Eldar/OreBuildingBonusFlavor" value="The Aeldari require metals less than other races, given that almost all of their structures and vehicles are psychic energy made tangible, in the form of wraithbone. But for the small amount they need, there are always improvements to be made."/>
	<entry name="Eldar/PowerField" value="Power Field"/>
	<entry name="Eldar/PowerFieldDescription" value="Increases the damage reduction."/>
	<entry name="Eldar/PowerFieldFlavor" value="Power fields reroute a portion of the vehicle’s energy supply to project a glimmering shield of protection around the vehicle."/>
	<entry name="Eldar/Protect" value="Protect"/>
	<entry name="Eldar/ProtectDescription" value="Increases the armour."/>
	<entry name="Eldar/ProtectFlavor" value="<string name='Actions/Eldar/ProtectJinxFlavor'/>"/>
	<entry name="Eldar/ReaperRangefinder" value="Reaper Rangefinder"/>
	<entry name="Eldar/ReaperRangefinderDescription" value="Ignores ranged damage reduction granted by Jink and Skilled Jink."/>
	<entry name="Eldar/ReaperRangefinderFlavor" value="Mounted in the helmet vanes of the Dark Reapers are highly advanced targeters that calculate telemetries in the blink of an eye."/>
	<entry name="Eldar/RemnantsOfTheFall" value="Remnants of the Fall"/>
	<entry name="Eldar/RemnantsOfTheFallDescription" value="Decreases the growth rate."/>
	<entry name="Eldar/RemnantsOfTheFallFlavor" value="Since the birth of Slaanesh, the remaining Aeldari have struggled to survive in a hostile galaxy. Their aeon-long lifespans mean that they always had a low fertility rate and slow gestation, so they struggle to replace those who fall in battle. On many Craftworlds, the dead far outnumber the living."/>
	<entry name="Eldar/ResearchBuildingBonus" value="Black Librarians"/>
	<entry name="Eldar/ResearchBuildingBonusDescription" value="Increases the research output."/>
	<entry name="Eldar/ResearchBuildingBonusFlavor" value="The Farseers of the Black Library dispatched us on this errand and now they offer their support. With access to their archives, we should be able to rediscover lost technologies and more rapidly improve our foothold on this planet."/>
	<entry name="Eldar/ReturnOfTheAeldari" value="<string name='Actions/Eldar/ReturnOfTheAeldari'/>"/>
	<entry name="Eldar/ReturnOfTheAeldariDescription" value="<string name='Actions/Eldar/ReturnOfTheAeldariDescription'/>"/>
	<entry name="Eldar/ReturnOfTheAeldariFlavor" value="<string name='Actions/Eldar/ReturnOfTheAeldariFlavor'/>"/>
	<entry name="Eldar/RuneArmour" value="Rune Armour"/>
	<entry name="Eldar/RuneArmourDescription" value="Increases the damage reduction."/>
	<entry name="Eldar/RuneArmourFlavor" value="Aeldari psykers fashion themselves elegant armour decorated with wraithbone runes. Said to pulse in time with the wearer’s heartbeat, rune armour offers protection against attacks both spiritual and physical in nature."/>
	<entry name="Eldar/Scattershield" value="Scattershield"/>
	<entry name="Eldar/ScattershieldDescription" value="Increases the damage reduction and blinds infantry and monstrous creature melee attackers."/>
	<entry name="Eldar/ScattershieldFlavor" value="Used to protect precious Aeldari war-constructs, scattershields are gigantic fan-shaped shield generators that convert the energy of incoming attacks into blinding sprays of multicoloured light."/>
	<entry name="Eldar/SerpentShield" value="Serpent Shield"/>
	<entry name="Eldar/SerpentShieldDescription" value="Increases the armour while Serpent Shield weapon is not on cooldown."/>
	<entry name="Eldar/SerpentShieldFlavor" value="<string name='Weapons/SerpentShieldFlavor'/>"/>
	<entry name="Eldar/Skyhunter" value="Skyhunter"/>
	<entry name="Eldar/SkyhunterDescription" value="Increases the armour penetration against flyers."/>
	<entry name="Eldar/SkyhunterFlavor" value="The spirit of a lost Exarch of Biel-Tan has infused our Crimson Hunters, revealing secrets from millenia of battling the foes of the Aeldari. Her rediscovered wisdom will enable us to dominate the skies as never before."/>
	<entry name="Eldar/SpiritMark" value="<string name='Actions/Eldar/SpiritMark'/>"/>
	<entry name="Eldar/SpiritMarkDescription" value="Increases the accuracy."/>
	<entry name="Eldar/SpiritMarkFlavor" value="<string name='Actions/Eldar/SpiritMarkFlavor'/>"/>
	<entry name="Eldar/SpiritPreservation" value="Spirit Preservation"/>
	<entry name="Eldar/SpiritPreservationDescription" value="Grants energy on death."/>
	<entry name="Eldar/SpiritPreservationFlavor" value="We are few in number and She Who Thirsts waits in the Warp, to consume our souls. However, we wear Spirit Stones to capture our essence at the moment of death; though every Aeldari shudders in distaste at the thought, when recovered these spirits can be used to guide our war machines."/>
	<entry name="Eldar/SpiritStones" value="Spirit Stones"/>
	<entry name="Eldar/SpiritStonesDescription" value="Reduces the morale loss."/>
	<entry name="Eldar/SpiritStonesFlavor" value="Some Aeldari vehicles incorporate large spirit stones with a captive animus that can control the vehicle should it be disabled."/>
	<entry name="Eldar/StarEngines" value="Star Engines"/>
	<entry name="Eldar/StarEnginesDescription" value="Increases the movement."/>
	<entry name="Eldar/StarEnginesFlavor" value="Whilst all Aeldari vehicles are swift and agile, those that mount star engines are often able to move faster than the eye can follow. Lesser races can only marvel at the phenomenal speed and manoeuvrability of a craft so equipped."/>
	<entry name="Eldar/TitanHoloFields" value="Titan Holo-Fields"/>
	<entry name="Eldar/TitanHoloFieldsDescription" value="<string name='Traits/Eldar/HoloFieldsDescription'/>"/>
	<entry name="Eldar/TitanHoloFieldsFlavor" value="“I was watching the best-best light show in the Hive, all elsparks and bang-bangs and shinies and sooo psychedelic… then I wowed around and the Irregulars was dead-dead and that alien tank just rolled on past me like I was plastidough.”<br/>  — Trooper Grande, sole survivor of the Necromundan Fourth Irregulars"/>
	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
	<entry name="Eldar/TranscendentBlissDescription" value="<string name='Actions/Eldar/TranscendentBlissDescription'/>"/>
	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
	<entry name="Eldar/VectorDancer" value="<string name='Actions/Eldar/VectorDancer'/>"/>
	<entry name="Eldar/VectorDancerDescription" value="<string name='Actions/Eldar/VectorDancerDescription'/>"/>
	<entry name="Eldar/VectorDancerFlavor" value="<string name='Actions/Eldar/VectorDancerFlavor'/>"/>
	<entry name="Eldar/VectoredEngines" value="<string name='Actions/Eldar/VectoredEngines'/>"/>
	<entry name="Eldar/VectoredEnginesDescription" value="<string name='Actions/Eldar/VectoredEnginesDescription'/>"/>
	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Actions/Eldar/VectoredEnginesFlavor'/>"/>
	<entry name="Eldar/VehicleBuildingBonus" value="Vaul's Summons"/>
	<entry name="Eldar/VehicleBuildingBonusDescription" value="Increases the production output."/>
	<entry name="Eldar/VehicleBuildingBonusFlavor" value="A spirit stone recovered from Gladius proved to be that of an ancient Bonesinger, lost here millenia past. With their insights into the planet’s structure and resources, we’re able to greatly increase our production."/>
	<entry name="Enslaved" value="Enslaved"/>
	<entry name="EnslavedDescription" value="Kill Enslaver to free the unit."/>
	<entry name="EreWeGo" value="'Ere We Go!"/>
	<entry name="EreWeGoDescription" value="Increases the movement."/>
	<entry name="EreWeGoFlavor" value="<string name='Actions/EreWeGoFlavor'/>"/>
	<entry name="ExtraInfantryArmour" value="Extra Infantry Armour"/>
	<entry name="ExtraInfantryArmourDescription" value="Increases the armour."/>
	<entry name="ExtraMonstrousCreatureArmour" value="Extra Monstrous Creature Armour"/>
	<entry name="ExtraMonstrousCreatureArmourDescription" value="Increases the armour."/>
	<entry name="ExtraVehicleArmour" value="Extra Vehicle Armour"/>
	<entry name="ExtraVehicleArmourDescription" value="Increases the armour."/>
	<entry name="Fear" value="Fear"/>
	<entry name="FearDescription" value="Reduces the morale each turn."/>
	<entry name="FearFlavor" value="<string name='Actions/AuraOfFearFlavor'/>"/>
	<entry name="Fearless" value="Fearless"/>
	<entry name="FearlessDescription" value="Reduces the morale loss and grants immunity to fear and pinning."/>
	<entry name="FearlessFlavor" value="Fearless troops never give up and seldom take full use of cover—even if it would be wiser to do so."/>
	<entry name="FeelNoPain" value="Feel No Pain"/>
	<entry name="FeelNoPainDescription" value="Increases the damage reduction."/>
	<entry name="FeelNoPainFlavor" value="Whether through force of will, bionic augmentation or foul sorcery, this warrior can fight despite fearsome wounds."/>
	<entry name="Flail" value="Flail"/>
	<entry name="FlailDescription" value="Increases the melee damage reduction."/>
	<entry name="FlailFlavor" value="Equipped with a flail weapon, these warriors hinder attackers on approach."/>
	<entry name="Flame" value="Flame"/>
	<entry name="FlameDescription" value="Classification."/>
	<entry name="Fleet" value="Fleet"/>
	<entry name="FleetDescription" value="Increases the movement."/>
	<entry name="FleetFlavor" value="Preternaturally agile, these warriors can cover ground more quickly than their plodding foes."/>
	<entry name="Fleshbane" value="Fleshbane"/>
	<entry name="FleshbaneDescription" value="Increases the damage against infantry and monstrous creature units."/>
	<entry name="Flyer" value="Flyer"/>
	<entry name="FlyerDescription" value="Can fly over cliffs, water and enemy units. Cannot capture artefacts or outposts. Ignores zone of control of enemy ground units. Cannot be hit by ground melee weapons and are hard to hit by ground weapons without skyfire. No penalty for heavy and ordnance weapons."/>
	<entry name="FlyerFlavor" value="The airspace above a battle is thronged with activity. Fighters and bomber craft hurtle through the skies, duelling with one another providing fire support for the troops on the ground."/>
	<entry name="Forest" value="Forest"/>
	<entry name="ForestDescription" value="Increases the ranged damage reduction of infantry units."/>
	<entry name="ForestFlavor" value="<string name='Features/ForestFlavor'/>"/>
	<entry name="ForestStealth" value="Forest Stealth"/>
	<entry name="ForestStealthDescription" value="Increases the ranged damage reduction while in a forest."/>
	<entry name="Fortification" value="Fortification"/>
	<entry name="FortificationDescription" value="Stationary and fortified unit. If armed, it exerts control over adjacent outposts."/>
	<entry name="FullThrottle" value="“Full Throttle!”"/>
	<entry name="FullThrottleDescription" value="Increases the movement."/>
	<entry name="FuelledByRage" value="Fuelled By Rage"/>
	<entry name="FuelledByRageDescription" value="Increases the attacks when the unit's hitpoints decrease."/>
	<entry name="FuriousCharge" value="Furious Charge"/>
	<entry name="FuriousChargeDescription" value="Increases the melee damage."/>
	<entry name="FuriousChargeFlavor" value="Some warriors use the impetus of the charge to fuel their own fury."/>
	<entry name="Gargantuan" value="Gargantuan"/>
	<entry name="GargantuanDescription" value="Classification."/>
	<entry name="GargantuanFlavor" value="Gargantuan creatures are of such immense size that they can take on entire armies. They tower over the battlefield, making the ground shudder as they advance upon the foe, crushing lesser creatures beneath their feet as they lumber forwards."/>
	<entry name="Gauss" value="Gauss"/>
	<entry name="GaussDescription" value="Scales the minimum damage with the hitpoints of the target unit."/>
	<entry name="GaussFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="GaussDamage" value="Atomic Flayers"/>
	<entry name="GaussDamageDescription" value="Increases the armour penetration."/>
	<entry name="GaussDamageFlavor" value="For a galaxy full of cruel, monstrous or insane weaponry, Gauss technology is particularly feared. A bolter shell will just explode inside you, but a Gauss weapon will flay you alive, atom by atom."/>
	<entry name="GetsHot" value="Gets Hot"/>
	<entry name="GetsHotDescription" value="The unit loses hitpoints each time a ranged weapon fires."/>
	<entry name="GetsHotFlavor" value="Some weapons are fuelled by unstable power sources and risk overheating with each shot—often to the detriment of the wielder."/>
	<entry name="Graviton" value="Graviton"/>
	<entry name="GravitonDescription" value="Scales the damage with the armour of the target unit."/>
	<entry name="GravitonFlavor" value="Some weapons crush their enemies within their own armour."/>
	<entry name="Grenade" value="Grenade"/>
	<entry name="GrenadeDescription" value="Classification."/>
	<entry name="GrotRiggers" value="Grot Riggers"/>
	<entry name="GrotRiggersDescription" value="Restores hitpoints each turn."/>
	<entry name="GrotRiggersFlavor" value="Whether hurriedly re-attaching gubbinz with rivet guns, or just getting out and pushing, a crew of grot riggers can help to keep an Ork vehicle in the fight long after it should have fallen apart."/>
	<entry name="GroundAttack" value="Ground Attack"/>
	<entry name="GroundAttackDescription" value="Can only target ground units."/>
	<entry name="GunnersKillOnSight" value="“Gunners, Kill on Sight!”"/>
	<entry name="GunnersKillOnSightDescription" value="Increases the ranged attacks."/>
	<entry name="HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="HammerOfWrathDescription" value="Increases the damage of non-Bomb weapons."/>
	<entry name="HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Hammerhand" value="<string name='Actions/Hammerhand'/>"/>
	<entry name="HammerhandDescription" value="<string name='Actions/HammerhandDescription'/>"/>
	<entry name="HammerhandFlavor" value="<string name='Actions/HammerhandFlavor'/>"/>
	<entry name="Hallucination" value="<string name='Actions/Hallucination'/>"/>
	<entry name="HallucinationDescription" value="Decreases the attacks."/>
	<entry name="HallucinationFlavor" value="<string name='Actions/HallucinationFlavor'/>"/>
	<entry name="HarvestResourceFeatures" value="Harvest Resource Tiles"/>
	<entry name="HarvestResourceFeaturesDescription" value="Harvests resources from adjacent special resource features."/>
	<entry name="Haywire" value="Haywire"/>
	<entry name="HaywireDescription" value="Increases the damage and ignores armour against vehicles and fortifications."/>
	<entry name="HaywireFlavor" value="Haywire weapons send out powerful electromagnetic pulses."/>
	<entry name="Headquarters" value="Headquarters"/>
	<entry name="HeadquartersDescription" value="Destroying the headquarters unit destroys the entire city."/>
	<entry name="HeavyWeapon" value="Heavy"/>
	<entry name="HeavyWeaponDescription" value="Reduces the accuracy after moving."/>
	<entry name="HellstormTemplate" value="Hellstorm Template"/>
	<entry name="HellstormTemplateDescription" value="<string name='Traits/BeamDescription'/>"/>
	<entry name="Hero" value="Hero"/>
	<entry name="HeroDescription" value="Increases the damage reduction of infantry units."/>
	<entry name="HeroFlavor" value="What is it to be a hero? Many would define one as an unusually brave, smart or strong member of a species. In a universe of endless war, that sadly tends to mean someone who is very, very good at causing the deaths of their enemies, and somehow surviving."/>
	<entry name="HighPower" value="High Power"/>
	<entry name="HighPowerDescription" value="Active before moving."/>
	<entry name="HitAndRun" value="Hit & Run"/>
	<entry name="HitAndRunDescription" value="Ignores enemy zone of control."/>
	<entry name="HitAndRunFlavor" value="Some troops employ a flexible battle stance, engaging the foe at close quarters one moment, before peeling off to strike with renewed vigour the next."/>
	<entry name="Homing" value="Homing"/>
	<entry name="HomingDescription" value="Does not require line of sight."/>
	<entry name="IgnoresCover" value="Ignores Cover"/>
	<entry name="IgnoresCoverDescription" value="Ignores the ranged damage reduction of the target unit."/>
	<entry name="IgnoresCoverFlavor" value="This weapon fires ammunition that cheats an enemy of his shelter."/>
	<entry name="Illuminated" value="Illuminated"/>
	<entry name="IlluminatedDescription" value="Reduces the ranged damage reduction."/>
	<entry name="Immobilized" value="Immobilised"/>
	<entry name="ImmobilizedDescription" value="The unit cannot move."/>
	<entry name="ImperialRuin" value="<string name='Features/ImperialRuin'/>"/>
	<entry name="ImperialRuinDescription" value="Increases the ranged damage reduction of infantry units."/>
	<entry name="ImperialRuinFlavor" value="<string name='Features/ImperialRuinFlavor'/>"/>
	<entry name="ImperialSplendour" value="Imperial Splendour"/>
	<entry name="ImperialSplendourDescription" value="Increases the influence."/>
	<entry name="ImperialSplendourFlavor" value="A successful planetary governor will reflect his domain's newfound wealth with monuments to the Imperium—mammoth plascrete basilica and statues of Space Marines, Primarchs and the Emperor."/>
	<entry name="Infiltrate" value="Infiltrate"/>
	<entry name="InfiltrateDescription" value="Prevents the unit from being targeted by overwatch attacks."/>
	<entry name="InfiltrateFlavor" value="Many armies employ reconnaissance troops who sit concealed for days, just waiting for the right moment in which to strike."/>
	<entry name="InstantDeath" value="Instant Death"/>
	<entry name="InstantDeathDescription" value="Greatly increases the damage against infantry and monstrous creature units."/>
	<entry name="InstantDeathFlavor" value="Some blows can slay an enemy outright, no matter how hardy he may be."/>
	<entry name="Invulnerable" value="Invulnerable"/>
	<entry name="InvulnerableDescription" value="This unit is invulnerable."/>
	<entry name="IronHalo" value="Iron Halo"/>
	<entry name="IronHaloDescription" value="Increases the damage reduction."/>
	<entry name="IronHaloFlavor" value="The iron halo is an honour granted to Space Marine commanders and a symbol of their exceptional bravery and wisdom. Usually mounted upon the backpack or built into an armoured collar, it contains an energy field that wards against even the most potent of enemy weapons."/>
	<entry name="IronWill" value="Iron Will"/>
	<entry name="IronWillDescription" value="<string name='Actions/IronWillDescription'/>"/>
	<entry name="IronWillFlavor" value="<string name='Actions/IronWillFlavor'/>"/>
	<entry name="ItWillNotDie" value="It Will Not Die"/>
	<entry name="ItWillNotDieDescription" value="Restores hitpoints each turn."/>
	<entry name="ItWillNotDieFlavor" value="In the dark corners of the galaxy, there are creatures that heal at a terrifying speed."/>
	<entry name="JetPack" value="Jet Pack"/>
	<entry name="JetPackDescription" value="The unit can move over water. Ignores the penalties of rivers and wire weed."/>
	<entry name="JetPackFlavor" value="Jet packs are designed to provide stable firing platforms rather than a means of getting into close combat."/>
	<entry name="Jetbike" value="Jetbike"/>
	<entry name="JetbikeDescription" value="The unit can move over water. Ignores the penalties of rivers and wire weed."/>
	<entry name="JetbikeFlavor" value="The technology to make reliable and small fast skimmers is more difficult than it looks, and for many years only the Aeldari had the technical know-how. More recently, the Orks have developed their own clunky version, the Deffkopta, and the emerging Necrons have brought with them unpredictable Tomb Blades."/>
	<entry name="Jink" value="<string name='Actions/Jink'/>"/>
	<entry name="JinkDescription" value="Increases the ranged damage reduction but reduces the accuracy."/>
	<entry name="JinkFlavor" value="<string name='Actions/JinkFlavor'/>"/>
	<entry name="Killshot" value="Killshot"/>
	<entry name="KillshotDescription" value="Increases the damage against monstrous creatures, vehicles and fortifications when adjacent to an allied Predator."/>
	<entry name="KillshotFlavor" value="There are few things that can stand before the combined might of a Predator Assassin Squadron—a trio of Predators—their Space Marine gunners creating interlocking kill-zones to bring down even massive xenos creatures or slab-sided war machines in a focussed barrage cannon fire."/>
	<entry name="Lance" value="Lance"/>
	<entry name="LanceDescription" value="Limits the armour of the target unit."/>
	<entry name="LanceFlavor" value="The terror of tank commanders, a lance weapon fires a concentrated beam of energy that can bore through any armour, regardless of thickness."/>
	<entry name="LargeBlast" value="Large Blast"/>
	<entry name="LargeBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="LargeBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="LastStand" value="Last Stand"/>
	<entry name="LastStandDescription" value="Increases the morale."/>
	<entry name="LastStandFlavor" value="“Here we stand and here shall we die, unbroken and unbowed. Though the very hand of death itself come for us, we will spit our defiance to the end!”<br/>  — Chaplain Armand Titus of the Howling Griffons"/>
	<entry name="LifeDrain" value="Life Drain"/>
	<entry name="LifeDrainDescription" value="Increases the damage against infantry and monstrous creature units."/>
	<entry name="LifeSteal" value="Life Steal"/>
	<entry name="LifeStealDescription" value="Converts damage into healing for this and adjacent infantry and monstrous creature units."/>
	<entry name="LinebreakerBombardment" value="Linebreaker Bombardment"/>
	<entry name="LinebreakerBombardmentDescription" value="Ignores the ranged damage reduction of the target unit when adjacent to an allied Vindicator."/>
	<entry name="Luminagen" value="Luminagen"/>
	<entry name="LuminagenDescription" value="Temporarily decreases the ranged damage reduction of the target unit."/>
	<entry name="LocatorBeacon" value="Locator Beacon"/>
	<entry name="LocatorBeaconDescription" value="Causes orbital deployment to not consume action points when deploying adjacent to this unit."/>
	<entry name="LocatorBeaconFlavor" value="Locator beacons are often carried by Scout Bikers or mounted onto Drop Pods, and provide a signalling package, broad-spectrum communicators, and geo-positional tracking. When activated, the beacon uploads detailed positional information to the tactical grid, allowing precision reinforcement by reserve forces."/>
	<entry name="LowPower" value="Low Power"/>
	<entry name="LowPowerDescription" value="Active after moving."/>
	<entry name="MachineEmpathy" value="Machine Empathy"/>
	<entry name="MachineEmpathyDescription" value="Restores hitpoints."/>
	<entry name="MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="MannedWeapon" value="Manned Weapon"/>
	<entry name="MannedWeaponDescription" value="Requires cargo to fire."/>
	<entry name="MassiveBlast" value="Massive Blast"/>
	<entry name="MassiveBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="MassiveBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="MasterCrafted" value="Master-Crafted"/>
	<entry name="MasterCraftedDescription" value="Increases the accuracy."/>
	<entry name="MasterCraftedFlavor" value="Some weapons are lovingly maintained artefacts, crafted with a skill now lost. Though the exact form of master-crafting varies, it is always considered to be the pinnacle of the weaponsmith’s art."/>
	<entry name="Melee" value="Melee"/>
	<entry name="MeleeDescription" value="Cannot overwatch."/>
	<entry name="MeleeFlavor" value="<string name='Weapons/MeleeFlavor'/>"/>
	<entry name="Melta" value="Melta"/>
	<entry name="MeltaDescription" value="Increases the armour penetration at half range."/>
	<entry name="MeltaFlavor" value="<string name='Weapons/MeltaFlavor'/>"/>
	<entry name="MindControl" value="Mind Control"/>
	<entry name="MindControlDescription" value="Reduces the morale each turn."/>
	<entry name="Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="MisfortuneDescription" value="Increases the damage taken."/>
	<entry name="MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="Missing" value="Missing"/>
	<entry name="MobRule" value="Mob Rule"/>
	<entry name="MobRuleDescription" value="Reduces the morale loss based on the amount of allied units in the area."/>
	<entry name="MobRuleFlavor" value="Orks are simplistic, brutal creatures who love to fight and draw confidence from possessing strength in numbers."/>
	<entry name="MobileCommand" value="Mobile Command"/>
	<entry name="MobileCommandDescription" value="Passive abilities work for cargo embarked on this transport."/>
	<entry name="MobileCommandFlavor" value="A transport vehicle is typically sealed, rendering any troops ineffective (but protected) until they reach their destination. Unlike gun slits, communication equipment doesn’t compromise the protection, as the Imperial Chimera command variant proves out, enabling commanders to continue to direct their forces."/>
	<entry name="Monofilament" value="Monofilament"/>
	<entry name="MonofilamentDescription" value="Inversely scales the accuracy with the maximum movement of the target unit. Increases the armour penetration."/>
	<entry name="MonofilamentFlavor" value="<string name='Weapons/MonofilamentFlavor'/>"/>
	<entry name="MonstrousCreature" value="Monstrous Creature"/>
	<entry name="MonstrousCreatureDescription" value="Classification."/>
	<entry name="MonstrousCreatureFlavor" value="These are towering giants that are capable of crushing a tank–like the Tyranid Carnifex, a creature bioengineered and evolved to become a living battering ram."/>
	<entry name="MoraleSoak" value="Morale Soak"/>
	<entry name="MoraleSoakDescription" value="Reduces the damage by the target's morale."/>
	<entry name="MoveThroughCover" value="Move Through Cover"/>
	<entry name="MoveThroughCoverDescription" value="Negates the movement penalty in forests and imperial ruins."/>
	<entry name="MoveThroughCoverFlavor" value="Some warriors are skilled at moving over broken and tangled terrain."/>
	<entry name="Necrons/AircraftBuildingBonus" value="Orphic Starkness"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Increases the production output."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="This inhuman expanse of stone now seems to bend impossibly, filling more space than there is. The Necron flying machines stretch and flicker in and out of existence as they traverse it."/>
	<entry name="Necrons/AttackCityBonus" value="Static Targetting"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Increases the accuracy against units in cities."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="The Necrons' technology may have been raised from dusty ancient tombs, but few races in the past 60 million years have matched their prowess in manipulating dimensional variables. No wall, bunker or laser grid is a defense against such weaponry."/>
	<entry name="Necrons/BlastDamage" value="Dispersed Targetting"/>
	<entry name="Necrons/BlastDamageDescription" value="Increases the armour penetration."/>
	<entry name="Necrons/BlastDamageFlavor" value="Human targeting systems tend to focus on accuracy, narrowing the dispersal of a weapon's shots. Counter-intuitively, the more advanced Necron targeting systems spread a weapon's attacks out, yet somehow still increase the chance of each shot to hit."/>
	<entry name="Necrons/BloodyCrusade" value="<string name='Actions/Necrons/BloodyCrusade'/>"/>
	<entry name="Necrons/BloodyCrusadeDescription" value="Increases the damage."/>
	<entry name="Necrons/CityTier2" value="Preliminary Excavation"/>
	<entry name="Necrons/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="Necrons/CityTier2Flavor" value="More of the ancient city has been uncovered by the digging slaves, who are now turned to restoring the long-buried buildings."/>
	<entry name="Necrons/CityTier3" value="Unearthed City"/>
	<entry name="Necrons/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="Necrons/CityTier3Flavor" value="The entirety of the buried necropolis has now been revealed, resplendent in its mind-bending horror."/>
	<entry name="Necrons/ConstructionBuildingBonus" value="Slave Enrichment"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Increases the production output."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="Minor modifications to your slaves' bodies and minds enables them to sleep less and work more, for as long as their fragile bodies last."/>
	<entry name="Necrons/CtanNecrodermis" value="C'tan Necrodermis"/>
	<entry name="Necrons/CtanNecrodermisDescription" value="Increases the damage reduction."/>
	<entry name="Necrons/CtanNecrodermisBlast" value="C'tan Necrodermis Blast"/>
	<entry name="Necrons/CtanNecrodermisBlastDescription" value="Damages nearby units when dying."/>
	<entry name="Necrons/DefensiveProtocols" value="<string name='Actions/Necrons/DefensiveProtocols'/>"/>
	<entry name="Necrons/DefensiveProtocolsDescription" value="Increases the armour."/>
	<entry name="Necrons/DestructionProtocols" value="<string name='Actions/Necrons/DestructionProtocols'/>"/>
	<entry name="Necrons/DestructionProtocolsDescription" value="Increases the damage taken."/>
	<entry name="Necrons/Dynasty" value="<string name='Actions/Necrons/Dynasty'/>"/>
	<entry name="Necrons/DynastyDescription" value="<string name='Actions/Necrons/DynastyDescription'/>"/>
	<entry name="Necrons/EnergyBuildingBonus" value="Empyreal Accumulators"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Increases the energy output."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="Little has changed on the outside of this baroque sarcophagus—yet, inside its unheimlich interior, dark Cryptek devices are stabilising a much improved device."/>
	<entry name="Necrons/EntropicStrike" value="Entropic Strike"/>
	<entry name="Necrons/EntropicStrikeDescription" value="Scales the minimum damage with the hitpoints of the target unit."/>
	<entry name="Necrons/EternityGate" value="Eternity Gate"/>
	<entry name="Necrons/EternityGateDescription" value="Necron units can teleport to adjacent tiles."/>
	<entry name="Necrons/EternityGateFlavor" value="A Monolith's eternity gate is a dimensional corridor between the battlefield and a tomb world, allowing legions of Necron warriors to cross vast distances and enter the fray with a single step."/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="Increases the witchfire damage reduction."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GravityPulse" value="<string name='Actions/Necrons/GravityPulse'/>"/>
	<entry name="Necrons/GravityPulseDescription" value="Reduces the movement of and deals damage to flyers, jetbikes and skimmers in range."/>
	<entry name="Necrons/GrowthBonus" value="Reanimation Rituals"/>
	<entry name="Necrons/GrowthBonusDescription" value="Increases the growth rate."/>
	<entry name="Necrons/GrowthBonusFlavor" value="Establishing more efficient protocols for recovering the long-buried Necrons from their tombs reduces the loss from clumsy slave excavators."/>
	<entry name="Necrons/HardwiredForDestruction" value="Hardwired For Destruction"/>
	<entry name="Necrons/HardwiredForDestructionDescription" value="Increases the accuracy."/>
	<entry name="Necrons/HardwiredForDestructionFlavor" value="The Necrons of the Destroyer Cult are hate-fuelled harvesters of the living, obsessed with the eradication of all sentient beings. They task the Crypteks with modifying their bodies to turn themselves into optimised killing machines. On Gladius Prime, the recently risen Skorpekh Destroyers demonstrate that perfectly, landing brutally accurate blows with their great blades."/>
	<entry name="Necrons/HousingBuildingBonus" value="Shelter Compression"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Increases the population limit."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="By altering the dimensional constraints of each shelter, many more Necrons can be accommodated for repair or maintenance."/>
	<entry name="Necrons/HuntersFromHyperspace" value="<string name='Actions/Necrons/HuntersFromHyperspace'/>"/>
	<entry name="Necrons/HuntersFromHyperspaceDescription" value="<string name='Actions/Necrons/HuntersFromHyperspaceDescription'/>"/>
	<entry name="Necrons/HuntersFromHyperspaceFlavor" value="<string name='Actions/Necrons/HuntersFromHyperspaceFlavor'/>"/>
	<entry name="Necrons/ImmuneToNaturalLaw" value="Immune to Natural Law"/>
	<entry name="Necrons/ImmuneToNaturalLawDescription" value="The unit can move over water, through enemy units and ignores the penalties of rivers and wire weed."/>
	<entry name="Necrons/InfantryBuildingBonus" value="Core Refinements"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Increases the production ouput."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="With better tools and less constraints, the Crypteks and Canopteks are able to recover a higher proportion of the buried Necrons, faster."/>
	<entry name="Necrons/InfluenceBuildingBonus" value="Haunting Inscriptions"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Increases the influence ouput."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="Woe betide any mortal man who gazes on these inscriptions—their sharp edges seem to waver in the corner of the eye, driving fear into human hearts."/>
	<entry name="Necrons/InvasionBeams" value="Invasion Beams"/>
	<entry name="Necrons/InvasionBeamsDescription" value="Disembarking does not consume movement."/>
	<entry name="Necrons/JetCharge" value="<string name='Actions/Necrons/JetCharge'/>"/>
	<entry name="Necrons/JetChargeDescription" value="Increases the damage reduction."/>
	<entry name="Necrons/LivingMetal" value="Living Metal"/>
	<entry name="Necrons/LivingMetalDescription" value="Restores hitpoints each turn."/>
	<entry name="Necrons/LivingMetalFlavor" value="It's understandable why the Necrons build their vehicles from the same living metal as their own bodies, given how durable it is and how well-suited it is to repairing damage sustained in the field."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="Baroque Covenant"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Increases the loyalty ouput."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="More of the Shrine's hidden tale is now revealed, though the story still ends before the Silent King can revenge himself on the voracious Star Gods that stripped his people of their souls."/>
	<entry name="Necrons/MeleeDamage" value="Necrodermic Blades"/>
	<entry name="Necrons/MeleeDamageDescription" value="Increases the armour penetration."/>
	<entry name="Necrons/MeleeDamageFlavor" value="Made from impossibly thin slices of the Necron's utilitarian living metal, these blades pass through most skin, bone and armour with disturbing ease."/>
	<entry name="Necrons/NanoscarabReanimationBeam" value="Nanoscarab Reanimation Beam"/>
	<entry name="Necrons/NanoscarabReanimationBeamDescription" value="Restores the hitpoints."/>
	<entry name="Necrons/NanoscarabReanimationBeamFlavor" value="<string name='Actions/Necrons/NanoscarabReanimationBeamFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="Nebuloscope"/>
	<entry name="Necrons/NebuloscopeDescription" value="Bypasses enemy ranged damage reduction."/>
	<entry name="Necrons/NebuloscopeFlavor" value="This arcane device allows the Tomb Blade's pilot to track his prey through different dimensions, leaving them no place to hide."/>
	<entry name="Necrons/OreBuildingBonus" value="Trusted Slaves"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Increases the ore ouput."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="Putting some faith in the enslaved young races of Old One creation and improving their lot might seem foolish, but it increases productivity."/>
	<entry name="Necrons/Chronometron" value="<string name='Actions/Necrons/Chronometron'/>"/>
	<entry name="Necrons/ChronometronDescription" value="Increases the damage reduction."/>
	<entry name="Necrons/ChronometronFlavor" value="<string name='Actions/Necrons/ChronometronFlavor'/>"/>
	<entry name="Necrons/PhaseShiftGenerator" value="<string name='Actions/Necrons/PhaseShiftGenerator'/>"/>
	<entry name="Necrons/PhaseShiftGeneratorDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="Necrons/PhaseShiftGeneratorFlavor" value="<string name='Actions/Necrons/PhaseShiftGeneratorFlavor'/>"/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/ReanimationProtocols" value="Reanimation Protocols"/>
	<entry name="Necrons/ReanimationProtocolsDescription" value="Restores hitpoints each turn."/>
	<entry name="Necrons/ReanimationProtocolsFlavor" value="Necrons have sophisticated self-repair systems that return even critically damaged warriors to the fight."/>
	<entry name="Necrons/Reaper" value="<string name='Actions/Necrons/Reaper'/>"/>
	<entry name="Necrons/ReaperFlavor" value="<string name='Actions/Necrons/ReaperFlavor'/>"/>
	<entry name="Necrons/ResearchBuildingBonus" value="Cryptek Datastyles"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Increases the research ouput."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="Dreaming up new technologies is the purview of the Crypteks—and with Datastyles they can access shared information without the risks presented by a direct mind-to-mind connection."/>
	<entry name="Necrons/ShieldVane" value="Shield Vane"/>
	<entry name="Necrons/ShieldVaneDescription" value="Increases the armour."/>
	<entry name="Necrons/ShieldVaneFlavor" value="Tomb Blades that are deployed directly into the midst of a world's defences are often equipped with additional armour panels."/>
	<entry name="Necrons/SleepingSentry" value="<string name='Actions/Necrons/SleepingSentry'/>"/>
	<entry name="Necrons/SleepingSentryDescription" value="Increases the damage reduction but prevents the unit from moving or taking an action."/>
	<entry name="Necrons/TargetRelayed" value="Target Relayed"/>
	<entry name="Necrons/TargetRelayedDescription" value="Increases the ranged accuracy of Necrons against the unit."/>
	<entry name="Necrons/TargetRelayedFlavor" value="<string name='Traits/Necrons/TargetingRelayFlavor'/>"/>
	<entry name="Necrons/TargetingRelay" value="Targeting Relay"/>
	<entry name="Necrons/TargetingRelayDescription" value="Temporarily increases the ranged accuracy of Necrons against the target unit."/>
	<entry name="Necrons/TargetingRelayFlavor" value="Triarch Stalkers might appear fragile, but their unique Quantum Shielding keeps them in one piece, in order that their Targeting Relay can amplify the firepower of nearby Necrons."/>
	<entry name="Necrons/Technomancer" value="Technomancer"/>
	<entry name="Necrons/TechnomancerDescription" value="Increases the damage reduction."/>
	<entry name="Necrons/VehiclesBuildingBonus" value="Canoptek Workers"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Increases the production ouput."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="Assigning Canoptek constructors to maintain and repair the various long-dormant war machines greatly speeds up their refurbishment."/>
	<entry name="Necrons/VengeanceOfTheEnchained" value="Vengeance of the Enchained"/>
	<entry name="Necrons/VengeanceOfTheEnchainedDescription" value="Death explosion causes extra damage."/>
	<entry name="Necrons/WraithForm" value="Wraith Form"/>
	<entry name="Necrons/WraithFormDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="Necrons/WraithFormFlavor" value="As a Canoptek device, the Wraith was intended to maintain the sleeping Necrons through the millennia they slumbered and was able to dimensionally destabilise itself to repair failing internal components. Now its charges are awoken, it uses the same technology to flit across the battlefield as enemy munitions pass harmlessly through it."/>
	<entry name="Necrons/Wraithflight" value="Wraithflight"/>
	<entry name="Necrons/WraithflightDescription" value="<string name='Traits/Necrons/ImmuneToNaturalLawDescription'/>"/>
	<entry name="Necrons/WraithflightFlavor" value="Their dimensional destabilisation matrix doesn’t just make Wraiths almost impossible to kill—it also allows them to pass through any obstacle, be that rivers, wire weed or even enemy warriors."/>
	<entry name="Necrons/WrithingWorldscape" value="<string name='Actions/Necrons/WrithingWorldscape'/>"/>
	<entry name="NoEscape" value="No Escape"/>
	<entry name="NoEscapeDescription" value="Increases the attacks against open-topped units."/>
	<entry name="OpenTopped" value="Open-Topped"/>
	<entry name="OpenToppedDescription" value="Classification."/>
	<entry name="OpenToppedFlavor" value="Some vehicles have only a little armour, making them more vulnerable to damage because of their lightweight construction. However, such vehicles make for excellent assault transports as their passengers can disembark with much greater ease."/>
	<entry name="OrationOfRestoration" value="Oration of Restoration"/>
	<entry name="OrationOfRestorationDescription" value="Restores hitpoints."/>
	<entry name="Ordnance" value="Ordnance"/>
	<entry name="OrdnanceDescription" value="Increases the armour penetration against vehicles and fortifications. Not usable by infantry after moving."/>
	<entry name="OrkoidFungus" value="Orkoid Fungus"/>
	<entry name="OrkoidFungusDescription" value="Restores hitpoints each turn of Ork ground units."/>
	<entry name="OrkoidFungusFlavor" value="Orks have a weirdly symbiotic relationship with the fungus that spreads across their planets—it's genetically identical to them, Squigs and Grots, and all these species tend to emerge from it rather than being born. Ork units depleted by battle would be wise to retreat to these fields to acquire (literally) fresh recruits."/>
	<entry name="OrkoidFungusBonusHealingRate" value="Fungal Forests"/>
	<entry name="OrkoidFungusBonusHealingRateDescription" value="Restores hitpoints each turn of Ork ground units."/>
	<entry name="OrkoidFungusBonusHealingRateFlavor" value="Once the Ork fungal spores are well-established across a planet, they can resemble forests—albeit, comprised of immense mushrooms, spewing out feral Orks, Grots and Squigs."/>
	<entry name="OrkoidFungusFood" value="Mushling Bloom"/>
	<entry name="OrkoidFungusFoodDescription" value="Increases the food output."/>
	<entry name="OrkoidFungusFoodFlavor" value="The tiny Snotlings, like all Orkoids, are derived from the same base fungus genetic structure. Unlike other Orkoids though, particularly lazy Snotlings can settle back into the fungus patch, and revert to become giant half-mushroom creatures, a particular delicacy for their larger Ork brethren."/>
	<entry name="Orks/BeastSnagga" value="Beast Snagga"/>
	<entry name="Orks/BeastSnaggaDescription" value="Increases accuracy against vehicles and monstrous creatures and increases invulnerable damage reduction."/>
	<entry name="Orks/BeastSnaggaFlavor" value="Spying the largest or most dangerous threats on the battlefield – besides the Orks themselves – Beast Snaggas chase them down with gleeful enthusiasm."/>
	<entry name="Orks/BigBoss" value="<string name='Actions/Orks/BigBoss'/>"/>
	<entry name="Orks/BigBossDescription" value="Increases the hitpoints."/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="<string name='Traits/AstraMilitarum/BlastDamageDescription'/>"/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="Bigga Boltz"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Traits/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="Like the Space Marine Kraken bolts, this Mek barrel modification allows this Shoota to hold even larger calibre ammunition, letting the lucky recipient show off by toting an even larger gun that hits even harder."/>
	<entry name="Orks/BonusBeastsProduction" value="Bigger Whips"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="Increases the production output of Runtherd Groundz."/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="“We's gonna need a bigger whip.”<br/>  — Runtherd Gark Snotsmeer, shortly before a Squiggoth stepped on him"/>
	<entry name="Orks/BonusColonizersProduction" value="Scrap Hammers"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="Increases the production output of Mek Bitz Yards."/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="When there isn't enough small-sized scrap to hand to build their ungainly creations, Mekboys acquire more by devising weird and wonderful machines which ravage the ruins and wreckage of their conquered worlds."/>
	<entry name="Orks/BonusInfantryProduction" value="More Dakka"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="Increases the production output of Pile O' Dakkas."/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="“Da best shoota I eva made, dat iz. Loadza barrulz, so dat it's ded shooty. 'Sept dat wun, 'cos dat's da skorcha, dat's burny insted. Yeah, good an' propa. An' da bullitz is 'splosiv…dey goez boom inna fings wot you'z shootin.' An' dat button dere…dat's da best bit. Wot it duz, see, iz…iz…oh, zog. Nah, it's nuffin' boss. Nah, you'z don't need ta see wot dat button duz…'onist. Don't push it!”<br/>  — Last words, Nazdakka Boomsnik, Mek"/>
	<entry name="Orks/BonusVehiclesProduction" value="Bodge Jobs"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="Increases the production output of Kults ov Speed."/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="Most Ork technology shouldn't work—vital components are often missing or the device is simply impossible. The Ork's single-minded belief—realised in the form of the Waaagh! energy that surrounds them—fills in the gaps. That's why speeding up the production of Ork vehicles and weaponry has almost no effect upon its quality…"/>
	<entry name="Orks/BustHeads" value="<string name='Actions/Orks/BustHeads'/>"/>
	<entry name="Orks/BustHeadsDescription" value="Reduces the morale loss."/>
	<entry name="Orks/BustHeadsFlavor" value="<string name='Actions/Orks/BustHeadsFlavor'/>"/>
	<entry name="Orks/ChannelMentalEmissions" value="<string name='Actions/Orks/ChannelMentalEmissions'/>"/>
	<entry name="Orks/ChannelMentalEmissionsDescription" value="Increases the research output."/>
	<entry name="Orks/CityEnergy" value="Kraklin Gubbinz"/>
	<entry name="Orks/CityEnergyDescription" value="Increases the energy output."/>
	<entry name="Orks/CityEnergyFlavor" value="Green and red lightning arcs between the intricate prongs of these power amplification devices, frying any Greenskin who wanders too near. It's quite the spectator sport, but the technology is missing key components so couldn't possibly bear out the Meks' claims of its increase in energy output—save for the Orks' reality-altering faith that it does."/>
	<entry name="Orks/CityGrowth" value="Boss Speakers"/>
	<entry name="Orks/CityGrowthDescription" value="Increases the production output of Scrap Shedz."/>
	<entry name="Orks/CityGrowthFlavor" value="“Naw, I don't want zogging tweeterz. I want gorkawooferz… wunz that go up ta eleven!”<br/>  — Bleeding Ears Merfnik, Goff Warlord and Rocka"/>
	<entry name="Orks/CityInfluence" value="Big Shinies"/>
	<entry name="Orks/CityInfluenceDescription" value="Increases the influence output."/>
	<entry name="Orks/CityInfluenceFlavor" value="“I don't knows what dems is, but I wants 'em.”<br/>  — Warboss Flashgrub Nitwiz, gazing upon the hammered gold doors of the Basilica Imperialis on Catiline VII"/>
	<entry name="Orks/CityLoyalty" value="Trophy Poles"/>
	<entry name="Orks/CityLoyaltyDescription" value="Increases the loyalty output."/>
	<entry name="Orks/CityLoyaltyFlavor" value="There's nothing that so stirs up the vim of an Ork as the sight of their defeated enemies (or their remaining parts) strung up high for all to see."/>
	<entry name="Orks/CityPopulationLimit" value="Deep Diggaz"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Increases the population limit."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="Using the Mek's latest contraptions, the Ork shacks and forges now extend deep beneath the city's surface. Sure, the occasional collapse drags Orks screaming into the depths, but it's worth it for the extra legroom."/>
	<entry name="Orks/CityResearch" value="Finking Rooms"/>
	<entry name="Orks/CityResearchDescription" value="Increases the research output."/>
	<entry name="Orks/CityResearchFlavor" value="Given that technology is wired into the genes of normal Orks, a smart Warboss will find that dedicating space, manpower and time to locking his Mekz inside a small room full of scrap and ordering them come up with some really good Dakka invariably pays off."/>
	<entry name="Orks/CityTier2" value="Slum Town"/>
	<entry name="Orks/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="Orks/CityTier2Flavor" value="Outside each fortress' original walls, a slum town of Gretchin, Squigs, Snotlings and low-tier Orks has sprung up, whilst the insidious green fungus has spread everywhere, remaking this world in the Orks' genetic image."/>
	<entry name="Orks/CityTier3" value="Shanty City"/>
	<entry name="Orks/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="Orks/CityTier3Flavor" value="The Warboss' fame has spread across the entire planet, with the invisible Waaagh! energies paralleling its path. Wherever Orks are freshly-spawned, they feel the irresistible tugging towards the Warboss' cities, which are now sprawled across the nearby landscape in endless slums and shanties."/>
	<entry name="Orks/CreateOrkoidFungusOnDeath" value="Rapid Decomposition"/>
	<entry name="Orks/CreateOrkoidFungusOnDeathDescription" value="Causes units to create orkoid fungus when they die."/>
	<entry name="Orks/CreateOrkoidFungusOnDeathFlavor" value="“Exhibit 27: a single 'spore' of xenos species #0451 'Ork'. Acolytes, you should note the fungoid texture and microhomuncular structures. We theorise that these 'spores' generate new 'Orks' in any sufficiently fertile setting. When an Ork reaches maturity (typically 30 minutes after emergence) it emits these constantly—but they are released en masse at the moment of death. Please don't drop it.”<br/>  — Lecture transcripts, Grigomen “Orklover” Delr, Rogue Trader and amateur Xenologist"/>
	<entry name="Orks/CyborkImplants" value="<string name='Actions/Orks/CyborkImplants'/>"/>
	<entry name="Orks/CyborkImplantsDescription" value="Increases the damage and damage reduction."/>
	<entry name="Orks/ExtraBitz" value="<string name='Actions/Orks/ExtraBitz'/>"/>
	<entry name="Orks/ExtraBitzDescription" value="Reduces the food, ore and energy upkeep."/>
	<entry name="Orks/ExperimentalProcedure" value="<string name='Actions/Orks/ExperimentalProcedure'/>"/>
	<entry name="Orks/ExperimentalProcedureDescription" value="Increases the damage and damage reduction."/>
	<entry name="Orks/Flyboss" value="Flyboss"/>
	<entry name="Orks/FlybossDescription" value="Increases the ranged accuracy against flyers, jetbikes and skimmers."/>
	<entry name="Orks/FlybossFlavor" value="Flybosses are ace pilots who have survived more dogfights than they can count (even when using their toes)."/>
	<entry name="Orks/Gitfinda" value="Gitfinda"/>
	<entry name="Orks/GitfindaDescription" value="Increases the ranged accuracy if the unit remains stationary."/>
	<entry name="Orks/GitfindaFlavor" value="These can be elaborate ocular bionics, monocular head-sets, oversized telescopes, or Mork knows what else. The function of a gitfinda is to improve the accuracy of its user to near-average levels."/>
	<entry name="Orks/GreenTide" value="Green Tide"/>
	<entry name="Orks/GreenTideFlavor" value="Orks are pretty damn hard to kill. Orks seemingly killed in one battle often return in the next, either fixed up by a Mad Dok or simply miraculously healed—and tougher and stronger for the experience."/>
	<entry name="Orks/GreenTideGrowth" value="<string name='Traits/Orks/GreenTide'/>"/>
	<entry name="Orks/GreenTideGrowthDescription" value="Increases the growth rate."/>
	<entry name="Orks/GreenTideGrowthFlavor" value="<string name='Traits/Orks/GreenTideFlavor'/>"/>
	<entry name="Orks/GreenTideHealing" value="<string name='Traits/Orks/GreenTide'/>"/>
	<entry name="Orks/GreenTideHealingDescription" value="Increases the healing rate."/>
	<entry name="Orks/GreenTideHealingFlavor" value="<string name='Traits/Orks/GreenTideFlavor'/>"/>
	<entry name="Orks/GrotGunner" value="Grot Gunner"/>
	<entry name="Orks/GrotGunnerDescription" value="Increases the accuracy of Big Shootas and Twin-Linked Big Shootas."/>
	<entry name="Orks/KustomForceField" value="<string name='Actions/Orks/KustomForceField'/>"/>
	<entry name="Orks/KustomForceFieldDescription" value="Increases the ranged damage reduction."/>
	<entry name="Orks/KustomForceFieldFlavor" value="<string name='Actions/Orks/KustomForceFieldFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="Bigga Choppas"/>
	<entry name="Orks/MeleeDamageDescription" value="Increases the armour penetration."/>
	<entry name="Orks/MeleeDamageFlavor" value="When Orks think about upgrades, they don't think very hard. Mega-armour, Gigashootas… and bigga choppas. If you can carry it, you can swing it, and if you can swing it, you can hit someone with it."/>
	<entry name="Orks/MightMakesRight" value="Might Makes Right"/>
	<entry name="Orks/MightMakesRightDescription" value="Grants influence when attacking."/>
	<entry name="Orks/MightMakesRightFlavor" value="The Orks only really feel alive when they're in a Waaagh! and a Waaagh! can only thrive through constant battle. As long as Orks are fighting, the Waaagh! should grow."/>
	<entry name="Orks/ProphetOfTheWaaagh" value="<string name='Actions/Orks/ProphetOfTheWaaagh'/>"/>
	<entry name="Orks/ProphetOfTheWaaaghDescription" value="Grants influence based on the upkeep when attacking."/>
	<entry name="Orks/Scavenger" value="Scavenger"/>
	<entry name="Orks/ScavengerDescription" value="Grants ore when killing an enemy."/>
	<entry name="Orks/ScavengerFlavor" value="Orks aren't known for their love of labour, preferring to sit around between battles picking bits of Squig out of their mouths and fighting each other. So rather than mine minerals from the ground, they tend to salvage them from, well, anywhere. Or rather their Grot underlings do."/>
	<entry name="Orks/WarbikeTurboBoost" value="<string name='Actions/Orks/WarbikeTurboBoost'/>"/>
	<entry name="Orks/WarbikeTurboBoostDescription" value="<string name='Actions/Orks/WarbikeTurboBoostDescription'/>"/>
	<entry name="Orks/WarbikeTurboBoostFlavor" value="<string name='Actions/Orks/WarbikeTurboBoostFlavor'/>"/>
	<entry name="Orks/Warpath" value="<string name='Actions/Orks/Warpath'/>"/>
	<entry name="Orks/WarpathDescription" value="<string name='Actions/Orks/WarpathDescription'/>"/>
	<entry name="Orks/WarpathFlavor" value="<string name='Actions/Orks/WarpathFlavor'/>"/>
	<entry name="Orks/WingMissiles" value="<string name='Weapons/WingMissiles'/>"/>
	<entry name="Orks/WingMissilesDescription" value="Increases the accuracy against vehicles."/>
	<entry name="Outpost" value="Outpost"/>
	<entry name="OutpostDescription" value="Increases the damage reduction and the healing rate of allied units. Increases the ranged damage reduction of infantry units."/>
	<entry name="Outflank" value="Outflank"/>
	<entry name="OutflankDescription" value="Increases the accuracy when adjacent to an allied unit."/>
	<entry name="OutflankFlavor" value="The best way to surprise an enemy is to strike from an unexpected quarter."/>
	<entry name="Pinned" value="Pinned"/>
	<entry name="PinnedDescription" value="Reduces the movement and ranged accuracy, increases the ranged damage reduction and prevents the unit from doing overwatch attacks."/>
	<entry name="PinnedFlavor" value="Coming under fire without knowing where the shots are coming from, or having ordnance rain down from the skies, can shake the resolve of even the bravest warriors, making them dive flat and cling to whatever cover presents itself."/>
	<entry name="Pinning" value="Pinning"/>
	<entry name="PinningDescription" value="Temporarily reduces the movement of the target fearful infantry unit and prevents it from doing overwatch attacks."/>
	<entry name="PinningFlavor" value="<string name='Traits/PinnedFlavor'/>"/>
	<entry name="Poisoned" value="Poisoned"/>
	<entry name="PoisonedDescription" value="Increases the damage against infantry and monstrous creature units."/>
	<entry name="PoisonedFlavor" value="There are many virulent and lethal poisons in the dark future. It is simplicity itself to adapt such toxins for battlefield use. It does not matter whether they coat weapon-blades or bullets, or are secreted by alien monstrosities – all are lethal."/>
	<entry name="PowerOfTheMachineSpirit" value="<string name='Actions/PowerOfTheMachineSpirit'/>"/>
	<entry name="PowerOfTheMachineSpiritDescription" value="<string name='Actions/PowerOfTheMachineSpiritDescription'/>"/>
	<entry name="PowerOfTheMachineSpiritFlavor" value="<string name='Actions/PowerOfTheMachineSpiritFlavor'/>"/>
	<entry name="PrecisionShots" value="Precision Shots"/>
	<entry name="PrecisionShotsDescription" value="Increases the accuracy."/>
	<entry name="PreferredEnemy" value="Preferred Enemy"/>
	<entry name="PreferredEnemyDescription" value="Increases the accuracy."/>
	<entry name="PreferredEnemyFlavor" value="Many of the galaxy's warriors train hard to overcome a particular foe, allowing them to predict the enemy's battlestances and thus land a blow or shot with greater ease."/>
	<entry name="PrimaryWeapon" value="Primary Weapon"/>
	<entry name="PrimaryWeaponDescription" value="Increases the armour penetration."/>
	<entry name="PsychicBlock" value="Psychic Block"/>
	<entry name="PsychicBlockDescription" value="Blocks the damage and effects of the next enemy psychic power."/>
	<entry name="PsychicHood" value="Psychic Hood"/>
	<entry name="PsychicHoodDescription" value="Increases the witchfire damage reduction."/>
	<entry name="PsychicLash" value="Psychic Lash"/>
	<entry name="PsychicLashDescription" value="Attacks penetrate all armour."/>
	<entry name="PsychicPower" value="Psychic Power"/>
	<entry name="PsychicPowerDescription" value="Classification."/>
	<entry name="PsychneueinInfest" value="Psychneuein Infest"/>
	<entry name="PsychneueinInfestDescription" value="Causes the target unit to spawn a Psychneuein when killed."/>
	<entry name="PsychneueinInfestation" value="Psychneuein Infestation"/>
	<entry name="PsychneueinInfestationDescription" value="Spawns a Psychneuein when dying."/>
	<entry name="Psyker" value="Psyker"/>
	<entry name="PsykerDescription" value="Classification."/>
	<entry name="PsykerFlavor" value="Psykers are battlefield mystics who harness the power of the Warp."/>
	<entry name="Rage" value="Rage"/>
	<entry name="RageDescription" value="Increases the attacks."/>
	<entry name="RageFlavor" value="Bloodlust is a powerful weapon on the battlefield, spurring a warrior to hack his foes apart in a flurry of mindless (but eminently satisfying) carnage."/>
	<entry name="Rampage" value="Rampage"/>
	<entry name="RampageDescription" value="Increases the attacks when there are more enemies than allies in adjacent tiles."/>
	<entry name="RampageFlavor" value="For some warriors, being outnumbered is not a cause for despair, but a call to set about their foes with a berserk counter-attack."/>
	<entry name="RapidFire" value="Rapid Fire"/>
	<entry name="RapidFireDescription" value="Doubles the attacks at half range."/>
	<entry name="RecoveryGear" value="Recovery Gear"/>
	<entry name="RecoveryGearDescription" value="Increases the healing rate."/>
	<entry name="RecoveryGearFlavor" value="Many crews load their vehicles with collections of tools, tow cables and other useful kit that can make the difference between digging an immobilized vehicle out of a tight spot or having to abandon it to its fate."/>
	<entry name="RedPaintJob" value="Red Paint Job"/>
	<entry name="RedPaintJobDescription" value="Increases the damage."/>
	<entry name="RedPaintJobFlavor" value="Orks believe that a vehicle painted red can outstrip a similar vehicle that isn't. As odd as it may seem, they're not wrong."/>
	<entry name="RefractorField" value="Refractor Field"/>
	<entry name="RefractorFieldDescription" value="Increases the damage reduction."/>
	<entry name="RefractorFieldFlavor" value="Often carried by high-ranking officers and Imperial heroes, shimmering refractor fields refract incoming energy around their bearer, batting aside blasts and swinging blades that would otherwise lay them low."/>
	<entry name="Regeneration" value="Regeneration"/>
	<entry name="RegenerationDescription" value="Restores hitpoints each turn."/>
	<entry name="RegenerationFlavor" value="Some units have the ability to recover from horrendous wounds and injuries that should have proven fatal."/>
	<entry name="Relentless" value="Relentless"/>
	<entry name="RelentlessDescription" value="Negates the penalty for heavy, ordnance and salvo weapons."/>
	<entry name="RelentlessFlavor" value="Relentless warriors are strong of arm—nothing can slow their implacable advance."/>
	<entry name="RelicPlating" value="Relic Plating"/>
	<entry name="RelicPlatingDescription" value="Increases the witchfire damage reduction."/>
	<entry name="RelicPlatingFlavor" value="Occasionally a crew will achieve an empathic relationship with the machine spirit of their battle tank. When such a crew perishes, their remains may be interred within their vehicle, spirits lingering protectively to drive away the baleful energies of the void."/>
	<entry name="Rending" value="Rending"/>
	<entry name="RendingDescription" value="Increases the damage and armour penetration."/>
	<entry name="RendingFlavor" value="Some weapons can inflict critical strikes against which no armour can protect."/>
	<entry name="RepulsorGrid" value="Repulsor Grid"/>
	<entry name="RepulsorGridDescription" value="Increases the ranged damage reduction and returns ranged damage from weapons that are not blast, template or witchfire back to the attacker."/>
	<entry name="RitesOfWar" value="Rites of War"/>
	<entry name="RitesOfWarDescription" value="Increases the damage."/>
	<entry name="RitesOfWarFlavor" value="<string name='Actions/RitesOfWarFlavor'/>"/>
	<entry name="Rosarius" value="Rosarius"/>
	<entry name="RosariusDescription" value="Increases the damage reduction."/>
	<entry name="RosariusFlavor" value="A rosarius is worn by Space Marine Chaplains for protection and as a symbol of office. It emits a protective energy field around the wearer capable of deflecting blows and shots that would smash a ferrocrete bunker. It is said that the stronger the bearer's belief in the might of the Emperor, the stronger a rosarius' force field will be."/>
	<entry name="RuinsStealth" value="Ruins Stealth"/>
	<entry name="RuinsStealthDescription" value="Increases the ranged damage reduction while in imperial ruins."/>
	<entry name="Salvo" value="Salvo"/>
	<entry name="SalvoDescription" value="Halves the attacks and range if the unit has moved."/>
	<entry name="SavantInterlocution" value="Savant Interlocution"/>
	<entry name="SavantInterlocutionDescription" value="Increases the ranged accuracy against flyers and skimmers when adjacent to an allied Hunter."/>
	<entry name="SavantLock" value="Savant Lock"/>
	<entry name="SavantLockDescription" value="Increases the accuracy against flyers, jetbikes and skimmers."/>
	<entry name="SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="SeekerMissileDescription" value="Reduced range against targets not acquired by markerlight. Does not require line of sight against targets acquired by markerlight."/>
	<entry name="SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Seeking" value="Seeking"/>
	<entry name="SeekingDescription" value="Increases the ranged accuracy against flyers."/>
	<entry name="Shaken" value="Shaken"/>
	<entry name="ShakenDescription" value="Reduces the accuracy and increases the damage taken."/>
	<entry name="Shielded" value="Shielded"/>
	<entry name="ShieldedDescription" value="Increases the damage reduction."/>
	<entry name="ShieldedFlavor" value="Some warriors are protected by more than mere physical armour. They may be shielded by force fields, enveloped in mystical energies or have a constitution that can shrug off hits that would put holes in a battle tank."/>
	<entry name="Shop" value="Shop"/>
	<entry name="ShopDescription" value="Allows hero units to purchase and sell items."/>
	<entry name="Shred" value="Shred"/>
	<entry name="ShredDescription" value="Increases the damage."/>
	<entry name="ShredFlavor" value="Some weapons and warriors strike in a flurry of blows, tearing flesh asunder in a series of brutal strikes."/>
	<entry name="Shrouded" value="Shrouded"/>
	<entry name="ShroudedDescription" value="Increases the ranged damage reduction."/>
	<entry name="ShroudedFlavor" value="The source of the darkness around these warriors matters not—only a lucky shot has any chance of piercing the shroud that hides them from view."/>
	<entry name="SiegeMasters" value="Siege Masters"/>
	<entry name="SiegeMastersDescription" value="Increases the damage against enemy units in cities and fortifications."/>
	<entry name="SiegeShield" value="Siege Shield"/>
	<entry name="SiegeShieldDescription" value="Increases the armour and reduces the movement penalty in forests and imperial ruins."/>
	<entry name="SiegeShieldFlavor" value="Many Vindicators are equipped with an enormous bulldozer blade, allowing them to shoulder aside battlefield detritus without risk."/>
	<entry name="Signum" value="Signum"/>
	<entry name="SignumDescription" value="Increases the ranged accuracy."/>
	<entry name="SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SistersOfBattle/ActOfFaith" value="Act of Faith"/>
	<entry name="SistersOfBattle/ActOfFaithDescription" value="Can perform Acts of Faith if the unit is not broken or shaken."/>
	<entry name="SistersOfBattle/ActOfFaithFlavor" value="The Adepta Sororitas can draw upon the wellspring of their faith and call upon the Emperor to guide their actions. So does absolute belief in the Imperial Creed allow the Sisters of Battle to perform the seemingly impossible upon the battlefield. Yet miracles are not to be relied upon as a matter of course. At the heart of the Imperial Creed is the belief that the divine Emperor relies on his followers to create their own salvation, but also that if the situation is sufficiently bleak, he will intervene to deliver his true servants."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="Anchorite Sarcophagus"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Increases the armour."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="To those Repentia who not only fled, but betrayed their Sisters in battle, an even worse fate awaits. After being wired into the crucible of their Mortifier they are further entombed behind thick adamantine casing. This sarcophagus protects their tortured bodies from incoming fire and desperately swung blades, denying them the release of death."/>
	<entry name="SistersOfBattle/AngelicVisage" value="Angelic Visage"/>
	<entry name="SistersOfBattle/AngelicVisageDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/AngelicVisageFlavor" value="Beatifically smiling, Zephyrim deflect the strikes of their foes with fluid grace before landing a killing blow of their own, typically with a point-blank headshot from a bolt pistol or a strike of their power sword."/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemed" value="Anguish Of The Unredeemed"/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemedDescription" value="Damages melee attacker on death."/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemedFlavor" value="Mortifiers blaze a trail of destruction with their flamers before charging headlong into the foe, guilt and pain driving them on, heedless of danger. Even as they die, they strike at their enemy, striving for redemption with every moment."/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherine" value="Armour Of Saint Katherine"/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherineDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherineFlavor" value="Ever since this revered suit of armour was anointed with a vial of blood from the martyred Saint Katherine it has been believed to have sacred powers of protection."/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="Furious Fusillade"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="“The right attack, carefully placed, can defeat the most overwhelming of enemies. However, if we do not know the right attack? Then an overwhelming, inchoate assault will hit the right spot, eventually…”<br/>  — Canoness Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/AvengingZeal" value="Avenging Zeal"/>
	<entry name="SistersOfBattle/AvengingZealDescription" value="Reduces the morale loss."/>
	<entry name="SistersOfBattle/AvengingZealFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/BerserkKillingMachine" value="Berserk Killing Machine"/>
	<entry name="SistersOfBattle/BerserkKillingMachineDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/BerserkKillingMachineFlavor" value="Tormented by neuro-agoniser arrays that amplify her self-loathing and divorced from her Sisters’ prayers by an enclosing baffle around her head, the unfortunate pilot’s spiritual suffering fuels the Mortifier. They storm forward as terrifying shock troops, before crashing into their foe."/>
	<entry name="SistersOfBattle/BloodyResolution" value="Bloody Resolution"/>
	<entry name="SistersOfBattle/BloodyResolutionDescription" value="Reduces the morale loss."/>
	<entry name="SistersOfBattle/BloodyResolutionFlavor" value="<string name='Traits/SistersOfBattle/MartyrdomFlavor'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnance" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnance'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnanceDescription" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnanceDescription'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnanceFlavor" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnanceFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="Novitiates Recruitment"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Increases the growth rate."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="The Ork invasion and subsequent war on Gladius Prime left many, many orphans. Sending missionaries and preachers into the wastes to recruit for the armies of the Imperium will bolster our city’s growth."/>
	<entry name="SistersOfBattle/CityTier2" value="Preceptory Annexes"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="Given the numbers of refugees pouring into the Preceptory from the ruined hives and wastelands of Gladius Prime, the Canoness Superior has ordered that we sanctify new land for use, bringing any natives into the fold—willingly or not."/>
	<entry name="SistersOfBattle/CityTier3" value="Sanctuary Fortress"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="The Preceptory is more of a city now, resembling the hive cities that once dotted the planet’s surface. Beneath its bulk lies an undercity, above it tower spires and chantries. Perhaps only the Fortress-Monasteries of the Adeptus Astartes rival it in scale and holy adornment."/>
	<entry name="SistersOfBattle/DivineDeliverance" value="<string name='Actions/SistersOfBattle/DivineDeliverance'/>"/>
	<entry name="SistersOfBattle/DivineDeliveranceDescription" value="<string name='Actions/SistersOfBattle/DivineDeliveranceDescription'/>"/>
	<entry name="SistersOfBattle/DivineDeliveranceFlavor" value="<string name='Actions/SistersOfBattle/DivineDeliveranceFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="Eternal Crusade"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="Increases the production output."/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="“And we shall not rest, for the God-Emperor’s eye is on us. He sees every soul saved and knows that the number imperilled far outweighs it. We cannot stop, we cannot slow—ours is an eternal crusade.”<br/>  — Canoness Vandire, In Memoriam De Virtute"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Expert Fighters"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Increases the accuracy."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="In most strike wings a handful of aircrafts are flown by veteran pilotes. These fighter aces are truly dangerous foes, able to predict the reaction of their prey with uncanny precision. Few of them survive beyond one or two campaigns as they draw the most dangerous missions where only the most experienced prevail."/>
	<entry name="SistersOfBattle/FlankSpeed" value="Flank Speed"/>
	<entry name="SistersOfBattle/FlankSpeedDescription" value="Increases the movement, but prevents the use of ranged weapons."/>
	<entry name="SistersOfBattle/FlankSpeedFlavor" value="“One should not question the nobility of Questor Imperialis. But for faith, none can match the Knight-Lancer, which goes into battle armed only with a spear and its own prodigious speed, praying that it makes it to the enemy lines. It is a fine match for our forces.”<br/>  — Canoness Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="Enhanced Ordnance"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="“Over the shells of the Castigator, we sprinkle the soil of holy Terra, raise our voices in prayer, and genuflect before the Shrine of St Katherine. Then we add 25% more explosive to each shell and cap them with adamantium.”<br/>  — Canoness Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/IonGauntletShield" value="Ion Gauntlet Shield"/>
	<entry name="SistersOfBattle/IonGauntletShieldDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/IonGauntletShieldFlavor" value="The ion gauntlet shield generator mounted in the Cerastus’ right gauntlet assembly is more concentrated than the directional ion shield mounted on the Knight Paladin, but lacks its tactical flexibility."/>
	<entry name="SistersOfBattle/KeepersOfTheFaith" value="Keepers of the Faith"/>
	<entry name="SistersOfBattle/KeepersOfTheFaithDescription" value="Prevents overwatch, but increases damage reduction."/>
	<entry name="SistersOfBattle/KeepersOfTheFaithFlavor" value="“Reason says, fire when you see the enemy. Reason says, use cover when the enemy fires at you. Reason says, die when you are shot. Reason does not understand our faith and how we keep it.”<br/>  — Canoness Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/KnightParry" value="Knight Parry"/>
	<entry name="SistersOfBattle/KnightParryDescription" value="Reduces the melee accuracy."/>
	<entry name="SistersOfBattle/KnightParryFlavor" value="The ion gauntlet shield generator mounted in the Cerastus’ right gauntlet assembly can be used to deflect the mightiest hits in close-combat."/>
	<entry name="SistersOfBattle/LaudHailer" value="Laud Hailer"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="This unit can still perform acts of faith if it is shaken."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Actions/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/LeadTheRighteous" value="<string name='Actions/SistersOfBattle/LeadTheRighteous'/>"/>
	<entry name="SistersOfBattle/LeadTheRighteousDescription" value="Increases the accuracy."/>
	<entry name="SistersOfBattle/LeadTheRighteousFlavor" value="<string name='Actions/SistersOfBattle/LeadTheRighteousFlavor'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperor" value="<string name='Actions/SistersOfBattle/LightOfTheEmperor'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperorDescription" value="<string name='Actions/SistersOfBattle/LightOfTheEmperorDescription'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperorFlavor" value="<string name='Actions/SistersOfBattle/LightOfTheEmperorFlavor'/>"/>
	<entry name="SistersOfBattle/Martyrdom" value="Martyrdom"/>
	<entry name="SistersOfBattle/MartyrdomDescription" value="On death, reduces the morale loss of all allied Adepta Sororitas units."/>
	<entry name="SistersOfBattle/MartyrdomFlavor" value="The Sisters of the Adepta Sororitas do not give in to despair when their leaders are slain. Instead, the blood of these martyred heroes only strengthens their resolve, the sacrifice inspiring them to great acts of heroism."/>
	<entry name="SistersOfBattle/MartyrSpirit" value="Martyr Spirit"/>
	<entry name="SistersOfBattle/MartyrSpiritDescription" value="On death, reduces the morale loss of adjacent allied units with Shield of Faith."/>
	<entry name="SistersOfBattle/MartyrSpiritFlavor" value="“The loss of our Sisters and auxiliaries saddened us—but encouraged us to redouble our efforts.”<br/>  — Unknown remembrancer, Gospel of the Webway"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Actions/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Actions/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="Blessed Blades"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Increases the armour penetration."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="The weapons of the Adepta Sororitas differ little from those requisitioned by the Astra Militarum, save in two regards: the quality of their maintenance; and the sacred benisons and supplications made over them. This latter only can explain the miracles that occur in combat, where these blessed blades find the smallest weakness time and time again."/>
	<entry name="SistersOfBattle/MiraculousIntervention" value="<string name='Actions/SistersOfBattle/MiraculousIntervention'/>"/>
	<entry name="SistersOfBattle/MiraculousInterventionDescription" value="<string name='Actions/SistersOfBattle/MiraculousInterventionDescription'/>"/>
	<entry name="SistersOfBattle/MiraculousInterventionFlavor" value="<string name='Actions/SistersOfBattle/MiraculousInterventionFlavor'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="Ministorum Mobilisation"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="Increases the requisitions output."/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="The rites of the Ecclesiarchy are not merely militaristic: prayers and liturgies exist for all functions of the Imperium. To be the focus of such a rite, whether Famulus or Pronatus or even the most base worker, drives them on to greater efforts."/>
	<entry name="SistersOfBattle/OathOfFaith" value="Oath Of Faith"/>
	<entry name="SistersOfBattle/OathOfFaithDescription" value="Reduces the accuracy and prevents the use of Shield of Faith."/>
	<entry name="SistersOfBattle/OathOfFaithFlavor" value="“We all took an oath, to defend the Imperium, to purge the heretic and alien. It supported us, drove us on through pain and fear. But when our sisters finally broke and fled, our oath haunted them… If they survived their flight, they would return as Sisters Repentia…”<br/>  — Unknown remembrancer, Gospel of the Webway"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="Paragon Warsuit"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="Among the holy arsenal of an Order Militant, the ancient Paragon Warsuits are regarded as sacred vestments with a noble will of their own. Only the most worth Celestians can master its spirit for use in battle."/>
	<entry name="SistersOfBattle/Protected" value="Protected"/>
	<entry name="SistersOfBattle/ProtectedDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/ProtectedFlavor" value="<string name='Actions/SistersOfBattle/BodyguardFlavor'/>"/>
	<entry name="SistersOfBattle/PsyShock" value="PsyShock"/>
	<entry name="SistersOfBattle/PsyShockDescription" value="Stuns Psykers."/>
	<entry name="SistersOfBattle/PsyShockFlavor" value="<string name='Weapons/CondemnorBoltgunSilverStake'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="Purifying Recitations"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="Increases the damage."/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="“Our foes call us cold, inhuman, unfeeling. But we feel, we mourn, we rage. Let them feel our warmth—in our flamers, our meltas.”<br/>  — Canoness Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/RagingFervour" value="Raging Fervour"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="Increases the damage."/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="“The weapons in our hands are the instruments of our faith. Each bolt is a prayer sent to the heart of the faithless, spreading its word into their very being.”<br/>  — Canoness Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/RighteousJudgement" value="<string name='Actions/SistersOfBattle/RighteousJudgement'/>"/>
	<entry name="SistersOfBattle/RighteousJudgementDescription" value="<string name='Actions/SistersOfBattle/RighteousJudgementDescription'/>"/>
	<entry name="SistersOfBattle/RighteousJudgementFlavor" value="<string name='Actions/SistersOfBattle/RighteousJudgementFlavor'/>"/>
	<entry name="SistersOfBattle/SacresantShield" value="Sacresant Shield"/>
	<entry name="SistersOfBattle/SacresantShieldDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/SacresantShieldFlavor" value="The faithful Celestian Sacresants stand firm against the worst horrors of the galaxy. Hordes of mutants and heretics crash futilely against their shield walls before being laid low with righteous fury."/>
	<entry name="SistersOfBattle/SaintlyBlessings" value="Saintly Blessings"/>
	<entry name="SistersOfBattle/SaintlyBlessingsDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/SaintlyBlessingsFlavor" value="<string name='Actions/SistersOfBattle/SaintlyBlessingsFlavor'/>"/>
	<entry name="SistersOfBattle/ShieldOfFaith" value="Shield of Faith"/>
	<entry name="SistersOfBattle/ShieldOfFaithDescription" value="Increases the damage reduction."/>
	<entry name="SistersOfBattle/ShieldOfFaithFlavor" value="Members of the Adepta Sororitas are taught that faith is a shield stronger than any armour. Such is the power of their belief that the Emperor will protect them that the Adepta Sororitas can shrug off the most severe of wounds and resist the witchcraft of enemy sorcerers."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="Simulacrum Imperialis"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="Reduces the cooldown of Acts of Faith."/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="These holy symbols of the Ecclesiarchy were once carried by one of the Imperium’s many saints, or may even be wrought from their bones. They are wellsprings of inspiration and faith and it is a great honour to carry such an irreplaceable relic into battle."/>
	<entry name="SistersOfBattle/SisterSuperior" value="Sister Superior"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="Increases the morale of infantry and Paragon Warsuits units."/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="It is the Sisters Superior who form the true lynchpins of each squad of Battle Sisters. Speaking with authority derived from years of combat experience and supreme faith in the God-Emperor, these remarkable officers ensure that every Sister under their command fights to the full extent of her abilities and endurance, thereby maximising the strategic impact of the squad as a whole."/>
	<entry name="SistersOfBattle/SolaceInAnguish" value="<string name='Actions/SistersOfBattle/SolaceInAnguish'/>"/>
	<entry name="SistersOfBattle/SolaceInAnguishDescription" value="<string name='Actions/SistersOfBattle/SolaceInAnguishDescription'/>"/>
	<entry name="SistersOfBattle/SolaceInAnguishFlavor" value="<string name='Actions/SistersOfBattle/SolaceInAnguishFlavor'/>"/>
	<entry name="SistersOfBattle/StirringRhetoric" value="<string name='Actions/SistersOfBattle/StirringRhetoric'/>"/>
	<entry name="SistersOfBattle/StirringRhetoricDescription" value="Increases the armour."/>
	<entry name="SistersOfBattle/StirringRhetoricFlavor" value="<string name='Actions/SistersOfBattle/StirringRhetoricFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithful" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithful'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithfulDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithfulDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithfulFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithfulFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoic" value="<string name='Actions/SistersOfBattle/TaleOfTheStoic'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoicDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheStoicDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoicFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheStoicFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarrior" value="<string name='Actions/SistersOfBattle/TaleOfTheWarrior'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarriorDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheWarriorDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarriorFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheWarriorFlavor'/>"/>
	<entry name="SistersOfBattle/ThePassion" value="<string name='Actions/SistersOfBattle/ThePassion'/>"/>
	<entry name="SistersOfBattle/ThePassionDescription" value="Increases the melee accuracy."/>
	<entry name="SistersOfBattle/ThePassionFlavor" value="<string name='Actions/SistersOfBattle/ThePassionFlavor'/>"/>
	<entry name="SistersOfBattle/UsedActOfFaith" value="Used Act Of Faith"/>
	<entry name="SistersOfBattle/UsedActOfFaithDescription" value="This unit used its act of faith ability this turn."/>
	<entry name="SistersOfBattle/UsedActOfFaithFlavor" value="<string name='Traits/SistersOfBattle/UsedActOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/UsedSacredRite" value="Used 1 Sacred Rite"/>
	<entry name="SistersOfBattle/UsedSacredRite2" value="Used 2 Sacred Rites"/>
	<entry name="SistersOfBattle/VengefulSpirit" value="Vengeful Spirit"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="Returns damage back to the attacker."/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="“And in death, we have one last chance—a release from worry. Once dying, there is nothing holding back our fury, no need for self-preservation. We can use up all that we are, take all the blow that come our way, for one last chance at justice.”<br/>  — Canoness Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/WarHymn" value="<string name='Actions/SistersOfBattle/WarHymn'/>"/>
	<entry name="SistersOfBattle/WarHymnDescription" value="Increases the attacks."/>
	<entry name="SistersOfBattle/WarHymnFlavor" value="<string name='Actions/SistersOfBattle/WarHymnFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="Mechanised Wrath"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="Increases the production output."/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="“In those last days, the fervour spread. The Rite of Suppliance was performed, to demand the utmost from the Machine Spirits that ran the Manufactora and the Hangars where the ancient war engines were anointed. They did not disappoint.”<br/>  — Unknown remembrancer, Gospel of the Webway"/>
	<entry name="SkilledJink" value="<string name='Actions/SkilledJink'/>"/>
	<entry name="SkilledJinkDescription" value="<string name='Traits/JinkDescription'/>"/>
	<entry name="SkilledJinkFlavor" value="<string name='Actions/SkilledJinkFlavor'/>"/>
	<entry name="Skimmer" value="Skimmer"/>
	<entry name="SkimmerDescription" value="The unit can move over water. Ignores the penalties of rivers and wire weed. Reduces the movement penalty in forests and imperial ruins."/>
	<entry name="SkimmerFlavor" value="Some highly advanced vehicles are fitted with anti-gravity drives that allow them to skim swiftly over tough terrain and intervening troops, making them perfect for surprise flanking attacks."/>
	<entry name="SkullAltar" value="<string name='Features/SkullAltar'/>"/>
	<entry name="SkullAltarDescription" value="Provides a reward to units entering the tile."/>
	<entry name="SkullAltarFlavor" value="<string name='Features/SkullAltarFlavor'/>"/>
	<entry name="SkullsForTheSkullThrone" value="Skulls for the Skull Throne"/>
	<entry name="SkullsForTheSkullThroneDescription" value="Increases the damage reduction."/>
	<entry name="SkullsForTheSkullThroneFlavor" value="Khorne respects only the strong and boundlessly murderous."/>	
	<entry name="Skyfire" value="Skyfire"/>
	<entry name="SkyfireDescription" value="Increases the accuracy against flyers. Reduces the accuracy against ground units that are not skimmer, jetbike or jet pack."/>
	<entry name="SkyfireFlavor" value="Skyfire weapons excel at shooting down enemy aircraft and skimmers."/>
	<entry name="SlowAndPurposeful" value="Slow and Purposeful"/>
	<entry name="SlowAndPurposefulDescription" value="Negates the penalty for heavy and ordnance weapons."/>
	<entry name="SlowAndPurposefulFlavor" value="Many warriors are steady but sure, slow to advance but no less deadly for it."/>
	<entry name="Slowed" value="Slowed"/>
	<entry name="SlowedDescription" value="Reduces the movement."/>
	<entry name="Smash" value="Smash"/>
	<entry name="SmashDescription" value="Increases the melee armour penetration."/>
	<entry name="SmashFlavor" value="For the most fearsome of creatures, a single blow is sufficient to breach a tank's armour or crush a living creature to bloody pulp."/>
	<entry name="SmokeScreen" value="Smoke Screen"/>
	<entry name="SmokeScreenDescription" value="Increases the ranged damage reduction."/>
	<entry name="SmokeScreenFlavor" value="<string name='Actions/CreateSmokeScreenFlavor'/>"/>
	<entry name="Sniper" value="Sniper"/>
	<entry name="SniperDescription" value="Increases the damage and armour penetration against infantry and monstrous creature units."/>
	<entry name="SniperFlavor" value="Sniper weapons are precision instruments, used to pick out a target’s weak points."/>
	<entry name="SonicBoom" value="Sonic Boom"/>
	<entry name="SonicBoomDescription" value="Increases the damage against flyers."/>
	<entry name="SoulBlaze" value="Soul Blaze"/>
	<entry name="SoulBlazeDescription" value="Deals damage each turn."/>
	<entry name="SoulBlazed" value="Soul Blazed"/>
	<entry name="SoulBlazedDescription" value="<string name='Traits/SoulBlazeDescription'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="<string name='Traits/AstraMilitarum/BlastDamageDescription'/>"/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Traits/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDiscipline" value="Bolter Discipline"/>
	<entry name="SpaceMarines/BolterDisciplineDescription" value="<string name='Traits/ChaosSpaceMarines/MaliciousVolleysDescription'/>"/>
	<entry name="SpaceMarines/BolterDisciplineFlavor" value="To a Space Marine, the boltgun is more than a weapon—it is an instrument of Mankind’s divinity, the bringer of death to his foes."/>
	<entry name="SpaceMarines/CityTier2" value="Fortress Expansion"/>
	<entry name="SpaceMarines/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="The second stage of the Fortress' construction has started. With the core facilities established, the Techmarines are authorised to expand their operations and create a second defensive line."/>
	<entry name="SpaceMarines/CityTier3" value="Advanced Redoubts"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="The third stage of the Fortress construction involves the creation of independent garrisons and buttresses along an additional line of city walls. In the shadow of those cyclopean barriers, Servitors and serfs spend their lives, scurrying from one task to another."/>
	<entry name="SpaceMarines/CityTier4" value="Fortress Supreme"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="With this expansion, the Master Artificer declares the fortress complete. Now it rivals in scale a Hive City. Inside its impregnable walls, you could be anywhere in the Imperium. Outside, the local tribes come from enormous distances to pay tribute, whilst their finest warriors hope to be accepted for the lethal trials that might lead to their being modified into a Space Marine."/>
	<entry name="SpaceMarines/CloseQuartersFirepower" value="Close-quarters Firepower"/>
	<entry name="SpaceMarines/CloseQuartersFirepowerDescription" value="Increases the ranged armour penetration."/>
	<entry name="SpaceMarines/CloseQuartersFirepowerFlavor" value="At shorter ranges, the range of weapons designed by Belisarius Cawl for his Primaris marines are lethally effective, encouraging the Emperor’s finest to close the distance with their foes."/>
	<entry name="SpaceMarines/DutyEternal" value="Duty Eternal"/>
	<entry name="SpaceMarines/DutyEternalDescription" value="Increases invulnerable damage reduction."/>
	<entry name="SpaceMarines/DutyEternalFlavor" value="All Adeptus Astartes possess an indomitable will. Yet those few Primaris given the privilege of being encased in a Redemptor Dreadnought after their effective death know a deeper responsibility, of living up to this unique honour. They will fight on no matter how grievous the injury or how severe the damage."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Stronghold Shielding"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="Gladius Prime is already proving far more hostile than the Adeptus Administratum's worst forecasts. With the Fortress' physical defenses under almost constant assault, the Techmarines approve installation of a void shield. Once constructed, this shimmering dome of power is sufficient to warp away the firepower of entire battalions (or even a rogue Imperial Knight) before collapsing."/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Traits/AstraMilitarum/LasDamageDescription'/>"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="Close Combat Mastery"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="Increases the armour penetration."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="Certain Adeptus Astartes chapters—the Space Wolves, the Blood Drinkers, the Minotaurs—are renowned for their mastery of close-quarters combat. To come to blows with even the lowliest Scout Marine from such a chapter is to fight generations of knowledge and training."/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
	<entry name="SpaceMarines/OmniscopeDescription" value="<string name='Actions/SpaceMarines/OmniscopeDescription'/>"/>
	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/RepulsorField" value="<string name='Actions/SpaceMarines/RepulsorField'/>"/>
	<entry name="SpaceMarines/RepulsorFieldDescription" value="Reduces the movement."/>
	<entry name="SpaceMarines/RepulsorFieldFlavor" value="<string name='Actions/SpaceMarines/RepulsorFieldFlavor'/>"/>
	<entry name="SpaceMarines/SuppressiveBombardment" value="Suppressive Bombardment"/>
	<entry name="SpaceMarines/SuppressiveBombardmentDescription" value="Increases the damage and attacks pin down enemy infantry when adjacent to another Whirlwind."/>
	<entry name="SpaceMarines/SuppressiveBombardmentFlavor" value="The Codex Astartes dictates that Whirlwinds operate best in larger groups, laying down a rolling barrage that cannot be avoided but must instead be endured. Even when sheltered, lighter-armoured troops and vehicles are utterly devastated by the relentless rain of warheads."/>
	<entry name="SpaceMarines/StormShield" value="Storm Shield"/>
	<entry name="SpaceMarines/StormShieldDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="SpaceMarines/StormShieldFlavor" value="A storm shield is a large, solid shield that has an energy field generator built into it. Though the bulk of the shield offers physical protection, it is the energy field which is more impressive, as it is capable of deflecting almost any attack. Even blows that would normally cut through Terminator armour are turned aside with ease by the protective energies of the storm shield."/>
	<entry name="SpaceMarines/Thunderstrike" value="Thunderstrike"/>
	<entry name="SpaceMarines/ThunderstrikeDescription" value="Increases ranged damage from Space Marines units against targets attacked by this unit."/>
	<entry name="SpaceMarines/ThunderstrikeFlavor" value="The heavily armed Storm Speeders designed by Belisarius Cawl are specialised against certain targets, with the Thunderstrike focused on eliminating enemy vehicles and key targets. Indeed, a foe crippled by its armaments becomes even more vulnerable to follow-up attacks by fellow battle brothers."/>
	<entry name="SpaceMarines/ThunderstrikeTarget" value="<string name='Traits/SpaceMarines/Thunderstrike'/>"/>
	<entry name="SpaceMarines/ThunderstrikeTargetDescription" value="Increases the ranged damage from Space Marines units against this unit."/>
	<entry name="SpaceMarines/ThunderstrikeTargetFlavor" value="<string name='Traits/SpaceMarines/ThunderstrikeFlavor'/>"/>
	<entry name="SpaceSlip" value="<string name='Actions/SpaceSlip'/>"/>
	<entry name="SpaceSlipDescription" value="Increases the damage reduction."/>
	<entry name="Stealth" value="Stealth"/>
	<entry name="StealthDescription" value="Increases the ranged damage reduction."/>
	<entry name="StealthFlavor" value="Some warriors are masters of disguise and concealment, able to fade into the ruin of a battlefield until they are ready to strike."/>
	<entry name="StrafingRun" value="Strafing Run"/>
	<entry name="StrafingRunDescription" value="Increases the ranged accuracy against ground units."/>
	<entry name="StrafingRunFlavor" value="This vehicle is designed as a ground attack craft, the spread and convergence distance of its weapons keyed to maximise carnage on the foes below."/>
	<entry name="Strikedown" value="Strikedown"/>
	<entry name="StrikedownDescription" value="Temporarily reduces the movement of target infantry unit."/>
	<entry name="StrikedownFlavor" value="A sufficiently powerful blow can knock even the mightiest warrior off his feet."/>
	<entry name="Stubborn" value="Stubborn"/>
	<entry name="StubbornDescription" value="Reduces the morale loss."/>
	<entry name="StubbornFlavor" value="Many warriors live and die according to the principle of 'death before dishonour'. Seldom do such warriors take a backward step in the face of danger."/>
	<entry name="Stunned" value="Stunned"/>
	<entry name="StunnedDescription" value="Prevents the unit from moving or acting."/>
	<entry name="Suicider" value="Suicider"/>
	<entry name="SuiciderDescription" value="Enemies do not gain experience when this unit kills itself."/>
	<entry name="Summon" value="Summon"/>
	<entry name="SummonDescription" value="Classification."/>
	<entry name="SuperHeavy" value="Super-Heavy"/>
	<entry name="SuperHeavyDescription" value="Classification."/>
	<entry name="SuperHeavyFlavor" value="Huge armour-clad constructions that each carry enough firepower to vaporise, smash or incinerate an entire army."/>
	<entry name="Supersonic" value="Supersonic"/>
	<entry name="SupersonicDescription" value="Increases the movement."/>
	<entry name="SupersonicFlavor" value="Supersonic vehicles are supremely fast, even by the normal standards of aircraft, making them exceptionally mobile in battle."/>
	<entry name="Swarms" value="Swarms"/>
	<entry name="SwarmsDescription" value="Damage taken is spread evenly across the whole unit group, but the group takes additional hits against blast and template weapons."/>
	<entry name="SwarmsFlavor" value="These creatures are so multitudinous that they cannot be picked out individually and must be fought as a group."/>
	<entry name="Swiftstrike" value="Swiftstrike"/>
	<entry name="SwiftstrikeDescription" value="Increases the attacks."/>
	<entry name="TacticalDoctrine" value="Tactical Doctrine"/>
	<entry name="TacticalDoctrineDescription" value="Increases the accuracy."/>
	<entry name="TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="Tank" value="Tank"/>
	<entry name="TankDescription" value="Classification."/>
	<entry name="TankFlavor" value="Tanks can use their mass as a weapon, driving right into and through densely packed enemies. This often throws the opposing battle line into disarray, as having some monstrous metal behemoth coming straight at you is unnerving for anybody."/>
	<entry name="TankHunters" value="Tank Hunters"/>
	<entry name="TankHuntersDescription" value="Increases the armour penetration against enemy vehicles."/>
	<entry name="TankHuntersFlavor" value="These veterans of armoured warfare are able to identify the weak points of enemy vehicles and target their fire appropriately."/>
	<entry name="Tau/AdvancedTargetingSystem" value="Advanced Targeting System"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Increases the ranged accuracy."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="An advanced targeting system assists the vehicle's gunner by identifying targets of particular value or danger and plotting fire plans to counter them."/>
	<entry name="Tau/AutomatedRepairSystem" value="Automated Repair System"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Restores hitpoints each turn."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="Tiny maintenance drones swarm over damaged systems to repair them in the midst of battle."/>
	<entry name="Tau/BlacksunFilter" value="Blacksun Filter"/>
	<entry name="Tau/BlacksunFilterDescription" value="Increases the sight."/>
	<entry name="Tau/BlacksunFilterFlavor" value="This optical filtering suite allows vehicle sensors to target enemies at full efficiency and range, even during night fighting operations."/>
	<entry name="Tau/BlastDamage" value="Blast Phasing"/>
	<entry name="Tau/BlastDamageDescription" value="Increases the armour penetration."/>
	<entry name="Tau/BlastDamageFlavor" value="Area-effect weaponry has always been better placed to attack large infantry formations than hard targets like vehicles—but the Earth Caste engineers have figured a way past that. By phase-shifting the energy components of explosive and incendiary weapons, they can ensure that at least some of their effect will ‘leak’ past any shielding."/>
	<entry name="Tau/BoltDamage" value="Brachyuran Assemblies"/>
	<entry name="Tau/BoltDamageDescription" value="Increases the armour penetration."/>
	<entry name="Tau/BoltDamageFlavor" value="Even the finest Earth Caste technician can’t assemble the components of their rail weapons by hand. But by entrusting this work to the tiny Brachyuran race and supplying them with miniaturised tools, they can shrink the scale and lower the fundamental deviation of any delicate operations, allowing for even more finerly tuned weaponry."/>
	<entry name="Tau/BreakComposure" value="<string name='Actions/Tau/BreakComposure'/>"/>
	<entry name="Tau/BreakComposureDescription" value="Attacked units lose morale."/>
	<entry name="Tau/BreakComposureFlavor" value="<string name='Actions/Tau/BreakComposureFlavor'/>"/>
	<entry name="Tau/CityTier2" value="Dissemination"/>
	<entry name="Tau/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="Tau/CityTier2Flavor" value="Once a city is a certain size, the Water Caste begin a diplomatic offensive to bring more civilians to the Way—bombarding them with tailored propaganda of every form, from pamphlast drops to vid, rad and loudhailer broadcasts."/>
	<entry name="Tau/CityTier3" value="Anthrazod Foundations"/>
	<entry name="Tau/CityTier3Description" value="Increases the tile acquisition radius."/>
	<entry name="Tau/CityTier3Flavor" value="The thick-skinned Anthrazods are a slow-thinking race normally employed by the T'au for dangerous, heavy-duty activities like asteroid mining—but directed by the Demiurg and Earth Caste, they can produce city substructures and transport tunnels like no-one else, linking up distant sectors with ease."/>
	<entry name="Tau/ClusterFire" value="Cluster Fire"/>
	<entry name="Tau/ClusterFireDescription" value="Increases attacks and damage against units that are bikes, jetbikes or very bulky. Greatly increases attacks and damage against units that are monstrous creatures, vehicles or fortifications."/>
	<entry name="Tau/ClusterFireFlavor" value="The R’Varna’s unique Pulse Submunition Cannons work at extreme ranges, saturating their target area with micro-bursts of plasma. The larger the target, the more burst-waves of plasma strike near-simultaneously, ripping it into smaller pieces with ease."/>
	<entry name="Tau/CounterfireDefenceSystem" value="Counterfire Defence System"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Increases the accuracy of overwatch attacks."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Actions/Tau/CounterfireDefenceSystemFlavor'/>"/>
	<entry name="Tau/DisruptionPod" value="Disruption Pod"/>
	<entry name="Tau/DisruptionPodDescription" value="Increases the ranged damage reduction."/>
	<entry name="Tau/DisruptionPodFlavor" value="A disruption pod throws out distorting images in both visual and magnetic spectra, making it hard to target the vehicle at range."/>
	<entry name="Tau/DroneController" value="Drone Controller"/>
	<entry name="Tau/DroneControllerDescription" value="Increases the accuracy of adjacent drones."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Actions/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/DroneControllerInRange" value="Drone Controller In Range"/>
	<entry name="Tau/DroneControllerInRangeDescription" value="Increases the accuracy."/>
	<entry name="Tau/DroneControllerInRangeFlavor" value="<string name='Actions/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/FieldAmplifierRelay" value="Field Amplifier Relay"/>
	<entry name="Tau/FieldAmplifierRelayDescription" value="Increases the invulnerable damage reduction when the unit is shielded."/>
	<entry name="Tau/FieldAmplifierRelayFlavor" value="Taking the form of a lightweight backpack unit, the field amplifier relay picks up the protective force field of the Shield Drone, spreading it in an energised umbrella over its bearer and beaming the signal on to other relays within range."/>
	<entry name="Tau/FireTeam" value="Fire Team"/>
	<entry name="Tau/FireTeamDescription" value="Increases the ranged accuracy when adjacent to an allied vehicle or monstrous creature."/>
	<entry name="Tau/FireTeamFlavor" value="Some battlesuits and battle tank sensor-systems can be networked to provide enhanced efficiency when they fight in dedicated fire teams."/>
	<entry name="Tau/FlechetteDischarger" value="Flechette Discharger"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Damages melee attackers."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="Powerful clusters of reactive charges are attached to the hulls of many T'au vehicles. If the enemy approaches, the clusters fire off vicious clouds of high velocity flechettes."/>
	<entry name="Tau/GhostkeelElectrowarfareSuite" value="Ghostkeel Electrowarfare Suite"/>
	<entry name="Tau/GhostkeelElectrowarfareSuiteDescription" value="Increases the ranged damage reduction against attacks from range 2 or higher."/>
	<entry name="Tau/GhostkeelElectrowarfareSuiteFlavor" value="The Ghostkeel's AI electrowarfare suite aggressively scans enemy targeting spectrums and invades the foe's sensor arrays, filling them with false information and scrap-data that makes it almost impossible to effectively fire upon the battlesuit at range."/>
	<entry name="Tau/HolophotonCountermeasures" value="<string name='Actions/Tau/HolophotonCountermeasures'/>"/>
	<entry name="Tau/HolophotonCountermeasuresDescription" value="Decreases the ranged accuracy."/>
	<entry name="Tau/HolophotonCountermeasuresFlavor" value="<string name='Actions/Tau/HolophotonCountermeasuresFlavor'/>"/>
	<entry name="Tau/IntegratedShieldGenerator" value="Integrated Shield Generator"/>
	<entry name="Tau/IntegratedShieldGeneratorDescription" value="Increases the invulnerable damage reduction and grants an improved ranged invulnerable damage reduction."/>
	<entry name="Tau/IntegratedShieldGeneratorFlavor" value="Unlike the Riptide, the R’Varna Battlesuit is intended for long-distance fire support and not mobility, allowing it to carry heavier armour. Similarly, its shield generator is still effective against close-ranged assaults, but operates optimally when the enemy is attacking from distance."/>
	<entry name="Tau/Kauyon" value="<string name='Actions/Tau/Kauyon'/>"/>
	<entry name="Tau/KauyonDescription" value="Increases the ranged damage reduction."/>
	<entry name="Tau/KauyonFlavor" value="<string name='Actions/Tau/KauyonFlavor'/>"/>
	<entry name="Tau/LasDamage" value="Mor'tonium Accelerators"/>
	<entry name="Tau/LasDamageDescription" value="Increases the armour penetration."/>
	<entry name="Tau/LasDamageFlavor" value="As humanity has discovered to its cost, over and over, using radioactive materials in weaponry is always dangerous. It’s reasonable to assume the T’au are cognisant of this, but choose to use the recently discovered Mor’tonium in their military technology anyway. Upon exposure to air, it rapidly decays into a massive burst of ionic energy that the T’au use."/>
	<entry name="Tau/MobileDefencePlatform" value="Mobile Defence Platform"/>
	<entry name="Tau/MobileDefencePlatformDescription" value="Increases the movement while transporting cargo."/>
	<entry name="Tau/Montka" value="<string name='Actions/Tau/Montka'/>"/>
	<entry name="Tau/MontkaDescription" value="Increases the damage against units below 50% hitpoints."/>
	<entry name="Tau/MontkaFlavor" value="<string name='Actions/Tau/MontkaFlavor'/>"/>
	<entry name="Tau/NetworkedMarkerlight" value="Networked Markerlight"/>
	<entry name="Tau/NetworkedMarkerlightDescription" value="Increases the ranged accuracy and ranged damage reduction bypass. The unit's attacks do not benefit from or consume Target Acquired."/>
	<entry name="Tau/NetworkedMarkerlightFlavor" value="These markerlights are networked directly into weapon systems, allowing them to deliver their payload with pinpoint accuracy."/>
	<entry name="Tau/NovaBoost" value="<string name='Actions/Tau/NovaBoost'/>"/>
	<entry name="Tau/NovaBoostDescription" value="Increases the movement."/>
	<entry name="Tau/NovaBoostFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaElectromagneticShockwave" value="<string name='Actions/Tau/NovaElectromagneticShockwave'/>"/>
	<entry name="Tau/NovaElectromagneticShockwaveDescription" value="Hits all group members of the target unit."/>
	<entry name="Tau/NovaElectromagneticShockwaveFlavor" value="<string name='Actions/Tau/NovaElectromagneticShockwaveFlavor'/>"/>
	<entry name="Tau/NovaFire" value="<string name='Actions/Tau/NovaFire'/>"/>
	<entry name="Tau/NovaFireDescription" value="Increases the attacks."/>
	<entry name="Tau/NovaFireFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaShield" value="<string name='Actions/Tau/NovaShield'/>"/>
	<entry name="Tau/NovaShieldDescription" value="Increases the damage reduction."/>
	<entry name="Tau/NovaShieldFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="Point Defence Targeting Relay"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Increases overwatch damage against enemy units adjacent to other friendly units."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="Designed to provide superior covering fire for nearby Fire caste units, a point defense relay automatically targets and engages enemies that attempt to assault."/>
	<entry name="Tau/PulseAccelerator" value="Pulse Accelerator"/>
	<entry name="Tau/PulseAcceleratorDescription" value="Increases the range of pulse weapons."/>
	<entry name="Tau/PulseAcceleratorFlavor" value="<string name='Actions/Tau/PulseAcceleratorFlavor'/>"/>
	<entry name="Tau/PulseBlaster" value="Pulse Blaster"/>
	<entry name="Tau/PulseBlasterDescription" value="Increases the damage and armor penetration of overwatch attacks."/>
	<entry name="Tau/PulseBlasterFlavor" value="Though the T’au rightly fear close-range warfare, the necessity of warfare on space hulks or labyrinthine Imperial hive worlds led to the development of the Pulse Blaster—colloquially called a Pulse Shotgun. Uniquely, the Blaster paints its target with negatively charged particules a moment before shooting, to increase the effect when the full plasma payload hits."/>
	<entry name="Tau/Rinyon" value="<string name='Actions/Tau/Rinyon'/>"/>
	<entry name="Tau/RinyonDescription" value="Increases the accuracy against units adjacent to other allied units."/>
	<entry name="Tau/RinyonFlavor" value="<string name='Actions/Tau/RinyonFlavor'/>"/>
	<entry name="Tau/RiptideShieldGenerator" value="Riptide Shield Generator"/>
	<entry name="Tau/RiptideShieldGeneratorDescription" value="Increases the damage reduction."/>
	<entry name="Tau/RiptideShieldGeneratorFlavor" value="Within a Riptide battlesuit's ablative shield is housed a small energy field generator whose potency can be further boosted by diverting power from the XV104's nova reactor."/>
	<entry name="Tau/Ripyka" value="<string name='Actions/Tau/Ripyka'/>"/>
	<entry name="Tau/RipykaDescription" value="<string name='Actions/Tau/RipykaDescription'/>"/>
	<entry name="Tau/RipykaFlavor" value="<string name='Actions/Tau/RipykaFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="Ripyka'va"/>
	<entry name="Tau/RipykaVaDescription" value="<string name='Actions/Tau/RipykaDescription'/>"/>
	<entry name="Tau/RipykaVaFlavor" value="Commander Coldflame was famed for her subtlety, her ability to layer tactics on tactics but somehow inspire her troops to strive to match her strategic complexity. Some whispered, however, that she concerned herself too deeply with the Greater Good for a mere Fire Warrior…"/>
	<entry name="Tau/SenseOfStone" value="<string name='Actions/Tau/SenseOfStone'/>"/>
	<entry name="Tau/SenseOfStoneDescription" value="Increases the damage reduction."/>
	<entry name="Tau/SenseOfStoneFlavor" value="<string name='Actions/Tau/SenseOfStoneFlavor'/>"/>
	<entry name="Tau/ShieldGenerator" value="Shield Generator"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Increases the damage reduction."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Actions/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StabilisingAnchors" value="Stabilising Anchors"/>
	<entry name="Tau/StabilisingAnchorsDescription" value="Increases the ranged attacks if the unit has not moved this turn."/>
	<entry name="Tau/StabilisingAnchorsFlavor" value="KV128 Stormsurge Ballistic Suits are too large to mount a jetpack like their Battle Suit counterparts. Instead, the Earth Caste scientist Bork'an designed them to lock into place once deployed, allowing them to divert all their reactor power to their immense weaponry."/>
	<entry name="Tau/StimulantInjector" value="Stimulant Injector"/>
	<entry name="Tau/StimulantInjectorDescription" value="Increases the damage reduction."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Actions/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/StormOfFire" value="<string name='Actions/Tau/StormOfFire'/>"/>
	<entry name="Tau/StormOfFireDescription" value="Increases the attacks."/>
	<entry name="Tau/StormOfFireFlavor" value="<string name='Actions/Tau/StormOfFireFlavor'/>"/>
	<entry name="Tau/SubvertCity" value="<string name='Actions/Tau/SubvertCity'/>"/>
	<entry name="Tau/SubvertCityDescription" value="Reduces the loyalty."/>
	<entry name="Tau/SubvertCityFlavor" value="<string name='Actions/Tau/SubvertCityFlavor'/>"/>
	<entry name="Tau/SupportSystems" value="Support Systems"/>
	<entry name="Tau/SupportSystemsDescription" value="Allows installation of support systems."/>
	<entry name="Tau/SupportSystemsFlavor" value="T’au Battle Suits are designed to be modular, so that the Earth Caste may customise them easily to suit different combat situations. Each suit only has a limited number of hardpoints though and reducing a suit’s firepower is always a hard choice."/>
	<entry name="Tau/SupportingFire" value="Supporting Fire"/>
	<entry name="Tau/SupportingFireDescription" value="Increases overwatch damage against enemy units adjacent to other allied units."/>
	<entry name="Tau/SupportingFireFlavor" value="Fire caste doctrine, as laid down in the Code of Fire, instructs every warrior to protect their comrades. Using overlapping fields of fire, teams provide each other this mutual support on the battlefield."/>
	<entry name="Tau/TargetAcquired" value="Target Acquired"/>
	<entry name="Tau/TargetAcquiredDescription" value="Increases the ranged accuracy of T'au against the unit. Reduces the ranged damage reduction of the unit against T'au. Ends after the unit is attacked by T'au."/>
	<entry name="Tau/TargetAcquiredFlavor" value="No other race is as committed to working together as the T’au, and the Markerlight is an excellent example of this. It’s a handheld targeting laser that is networked into the T’au’s information system, allowing for pinpoint strikes by other T’au forces."/>
	<entry name="Tau/TidewallShieldline" value="Tidewall Shieldline"/>
	<entry name="Tau/TidewallShieldlineDescription" value="Returns ranged damage from weapons that are not blast, template or witchfire back to the attacker."/>
	<entry name="Tau/TidewallShieldlineFlavor" value="The most commonly seen fortification utilised by the forces of the T'au Empire is the Tidewall Shieldline, a wall of energy behind which infantry can take cover. As the enemy's fusillades hiss and crack harmlessly from the Shieldline's refractive field, the Fire Warriors it protects unleash a blistering hail of pulse fire in return. Worse still for any aggressor attempting to drive the T'au from cover, this force-wall can redirect kinetic energy, sending las-blasts and armour-piercing shells ricocheting back into the enemy ranks."/>
	<entry name="Tau/TidewallShieldlineCity" value="<string name='Traits/Tau/TidewallShieldline'/>"/>
	<entry name="Tau/TidewallShieldlineCityDescription" value="<string name='Traits/Tau/TidewallShieldlineDescription'/>"/>
	<entry name="Tau/TidewallShieldlineCityFlavor" value="<string name='Traits/Tau/TidewallShieldlineFlavor'/>"/>
	<entry name="Tau/TidewallShieldlineOutpost" value="<string name='Traits/Tau/TidewallShieldline'/>"/>
	<entry name="Tau/TidewallShieldlineOutpostDescription" value="<string name='Traits/Tau/TidewallShieldlineDescription'/>"/>
	<entry name="Tau/TidewallShieldlineOutpostFlavor" value="<string name='Traits/Tau/TidewallShieldlineFlavor'/>"/>
	<entry name="Tau/Unity" value="<string name='Actions/Tau/Unity'/>"/>
	<entry name="Tau/UnityDescription" value="<string name='Actions/Tau/UnityDescription'/>"/>
	<entry name="Tau/UnityFlavor" value="<string name='Actions/Tau/UnityFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="In Diversity, Unity"/>
	<entry name="Tau/UtopiaBonusDescription" value="Increases the loyalty bonus from new building types."/>
	<entry name="Tau/UtopiaBonusFlavor" value="The ideal T’au city is an example to all others, displaying a perfect balance of the various castes of the T’au race with their client and auxiliary races, all living in harmony."/>
	<entry name="Tau/VectoredRetroThrusters" value="Vectored Retro-Thrusters"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Increases the movement and ignores enemy zone of control."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Actions/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="Velocity Tracker"/>
	<entry name="Tau/VelocityTrackerDescription" value="Increases the ranged accuracy against flyers."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Actions/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tau/VolleyFire" value="<string name='Actions/Tau/VolleyFire'/>"/>
	<entry name="Tau/VolleyFireDescription" value="Increases the ranged attacks if the unit has not moved."/>
	<entry name="Tau/VolleyFireFlavor" value="<string name='Actions/Tau/VolleyFireFlavor'/>"/>
	<entry name="Tau/ZephyrsGrace" value="<string name='Actions/Tau/ZephyrsGrace'/>"/>
	<entry name="Tau/ZephyrsGraceDescription" value="Increases the action points."/>
	<entry name="Tau/ZephyrsGraceFlavor" value="<string name='Actions/Tau/ZephyrsGraceFlavor'/>"/>
	<entry name="TelekineDome" value="Telekine Dome"/>
	<entry name="TelekineDomeDescription" value="Increases the ranged damage reduction."/>
	<entry name="TelekineDomeFlavor" value="<string name='Actions/TelekineDomeFlavor'/>"/>
	<entry name="TeleportHomer" value="Teleport Homer"/>
	<entry name="TeleportHomerDescription" value="Causes orbital deployment to not consume action points when deploying Chaplain, Assault Terminators and Terminators adjacent to this unit."/>
	<entry name="TeleportHomerFlavor" value="Teleport homers emit a powerful signal enabling orbiting Strike Cruisers to lock onto them with their teleportation equipment. By matching the exact coordinates of this signal, the risk of missing the intended mark is greatly reduced."/>
	<entry name="Template" value="Template"/>
	<entry name="TemplateDescription" value="<string name='Traits/BeamDescription'/>"/>
	<entry name="TerminatorArmour" value="Terminator Armour"/>
	<entry name="TerminatorArmourDescription" value="Increases the damage reduction."/>
	<entry name="TerminatorArmourFlavor" value="Terminator armour is the best protection a Space Marine can be equipped with. It is even said that Terminator armour can withstand the titanic energies at a plasma generator's core and that this was, in fact, the armour's original purpose."/>
	<entry name="Tesla" value="Tesla"/>
	<entry name="TeslaDescription" value="Increases the attacks."/>
	<entry name="TeslaFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="TeslaDamage" value="Viridian Discharge"/>
	<entry name="TeslaDamageDescription" value="Increases the armour penetration."/>
	<entry name="TeslaDamageFlavor" value="The luminous green lightning discharged by these exotic and ancient Necron weapons crackles with life, writhing and seemingly clawing at any target it hits, moving as if it had a mind of its own."/>
	<entry name="TheFleshIsWeak" value="The Flesh Is Weak"/>
	<entry name="TheFleshIsWeakDescription" value="Increases the damage reduction."/>
	<entry name="TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="TrainedSentinelPilots" value="Trained Sentinel Pilots"/>
	<entry name="TrainedSentinelPilotsDescription" value="Increases the damage."/>
	<entry name="TrainedSentinelPilotsFlavor" value="Sentinels are used in a wide variety of roles throughout the Imperium of man—from power lifters used to load starship weaponry to open-canopy scouts. Some Sentinels are equipped with improved armour and stabilisers to carry heavier weaponry. This changes the Sentinel's role from support and reconnaissance to that of a heavy fire platform."/>
	<entry name="Traktor" value="Traktor"/>
	<entry name="TraktorDescription" value="Immobilizes enemy flyers."/>
	<entry name="Transport" value="Transport"/>
	<entry name="TransportDescription" value="Allows transportation of infantry units and monstrous creatures."/>
	<entry name="TransportFlavor" value="Some can carry others across the battlefield, providing speed and protection. Of course, if the transport is destroyed, the passengers will be burnt alive in the explosion."/>
	<entry name="TurboBoost" value="Turbo-Boost"/>
	<entry name="TurboBoostDescription" value="Increases the movement."/>
	<entry name="Tusked" value="Tusked"/>
	<entry name="TuskedDescription" value="Increases the attacks of melee weapons."/>
	<entry name="Tyranids/AcidBlood" value="Acid Blood"/>
	<entry name="Tyranids/AcidBloodDescription" value="Enemies take damage when they cause melee damage."/>
	<entry name="Tyranids/AcidBloodFlavor" value="The alien blood spilt from certain Tyranids is so corrosive that it can eat through ceramite armour and dissolve flesh in mere moments."/>
	<entry name="Tyranids/AdaptiveBiology" value="<string name='Actions/Tyranids/AdaptiveBiology'/>"/>
	<entry name="Tyranids/AdaptiveBiologyDescription" value="Increases the damage reduction."/>
	<entry name="Tyranids/AdaptiveBiologyFlavor" value="<string name='Actions/Tyranids/AdaptiveBiologyFlavor'/>"/>
	<entry name="Tyranids/AlphaWarrior" value="<string name='Actions/Tyranids/AlphaWarrior'/>"/>
	<entry name="Tyranids/AlphaWarriorDescription" value="Increases the accuracy."/>
	<entry name="Tyranids/AlphaWarriorFlavor" value="<string name='Actions/Tyranids/AlphaWarriorFlavor'/>"/>
	<entry name="Tyranids/Biomorph" value="Biomorph"/>
	<entry name="Tyranids/BiomorphDescription" value="Classification."/>
	<entry name="Tyranids/BiomorphDamage" value="Biomorph Adaptation"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Increases the armour penetration."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="Far from being born perfectly ready for every situation, Tyranids have variable amounts of adaptability inherent in their DNA. Ramping this capability up is expensive in terms of resources, but allows them to change their behaviour and physical capabilities in the field."/>
	<entry name="Tyranids/BoundingLeap" value="Bounding Leap"/>
	<entry name="Tyranids/BoundingLeapDescription" value="Negates the movement penalty of rivers."/>
	<entry name="Tyranids/BroodProgenitor" value="<string name='Actions/Tyranids/BroodProgenitor'/>"/>
	<entry name="Tyranids/BroodProgenitorDescription" value="Increases the attacks."/>
	<entry name="Tyranids/BroodProgenitorFlavor" value="<string name='Actions/Tyranids/BroodProgenitorFlavor'/>"/>
	<entry name="Tyranids/ChameleonicSkin" value="Chameleonic Skin"/>
	<entry name="Tyranids/ChameleonicSkinDescription" value="Allows overwatch attacks with melee weapons."/>
	<entry name="Tyranids/CityDamage" value="The Spiders' Parlour"/>
	<entry name="Tyranids/CityDamageDescription" value="Deals damage each turn."/>
	<entry name="Tyranids/CityDamageFlavor" value="To enter into a mature Tyranid city, is to walk inside giants—hostile, biologically-crafted giants, determined to consume you. Toxin-coated villi lash infantry, valleys sprout teeth and close like maws, and town square-sized sphincters open suddenly, dumping entire unwary armies into acidic reclamation pools."/>
	<entry name="Tyranids/CityGrowth" value="Aggressive Expansion"/>
	<entry name="Tyranids/CityGrowthDescription" value="Increases the growth rate."/>
	<entry name="Tyranids/CityGrowthFlavor" value="Tyranid cities place little value on size, but focus instead on productivity. As long as a city is efficiently churning out troops, it doesn't matter if it occupies land. But when a city must expand, it can do it extremely rapidly—from sphincters fly hooked tendons, dragging seed pods out to the landscape, where they erupt like Barbed Stranglers. In moments the land is occupied, rapidly gestating a new structure."/>
	<entry name="Tyranids/CityLoyalty" value="Grey Matter Dispersion"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Decreases the loyalty penalty from city amount."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="Throughout the hive, from the great producer organisms, to the smaller maintenance creatures, to the substrate itself, small packages of grey matter are dispersed, increasing the ease with which swarm creatures can be controlled."/>
	<entry name="Tyranids/CityPopulationLimit" value="Biogenesis Organelles"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Increases the population limit."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="Nothing truly “lives” in a Tyranid city-hive. Mindless 'organelle' creatures spawn and perform crucial functions before being reassimilated. Alterations to the city structure can be made, enabling these organisms to be spawned and reabsorbed in much shorter windows, allowing for their overall population to increase."/>
	<entry name="Tyranids/CityProduction" value="Propulsive Parturition"/>
	<entry name="Tyranids/CityProductionDescription" value="Increases the production output."/>
	<entry name="Tyranids/CityProductionFlavor" value="Waiting for Tyranid newborns to leave their respective mother-buildings is relatively inefficient. Genetors have sighted certain adapted buildings, however, which eject the newborn creature from its birthing sac with an convulsive electric impulse, enabling it to be rapidly re-used."/>
	<entry name="Tyranids/CityTier2" value="Ripper Dispersion"/>
	<entry name="Tyranids/CityTier2Description" value="Increases the tile acquisition radius."/>
	<entry name="Tyranids/CityTier2Flavor" value="As the hive matures, the Rippers no longer need to swarm together in such huge numbers for safety. Dispersing them, allows the hive mind to co-ordinate their reclamation behaviour over a wider range."/>
	<entry name="Tyranids/DiffusionField" value="<string name='Actions/Tyranids/DiffusionField'/>"/>
	<entry name="Tyranids/DiffusionFieldDescription" value="Increases the ranged damage reduction."/>
	<entry name="Tyranids/DiffusionFieldFlavor" value="<string name='Actions/Tyranids/DiffusionFieldFlavor'/>"/>
	<entry name="Tyranids/Dominion" value="<string name='Actions/Tyranids/Dominion'/>"/>
	<entry name="Tyranids/DominionDescription" value="<string name='Actions/Tyranids/DominionDescription'/>"/>
	<entry name="Tyranids/DominionFlavor" value="<string name='Actions/Tyranids/DominionFlavor'/>"/>
	<entry name="Tyranids/ExploitWeaknesses" value="<string name='Actions/Tyranids/ExploitWeaknesses'/>"/>
	<entry name="Tyranids/ExploitWeaknessesDescription" value="Reduces the armour."/>
	<entry name="Tyranids/ExploitWeaknessesFlavor" value="<string name='Actions/Tyranids/ExploitWeaknessesFlavor'/>"/>
	<entry name="Tyranids/FeederBeast" value="Feeder-Beast"/>
	<entry name="Tyranids/FeederBeastDescription" value="Damage caused by this unit is converted into healing."/>
	<entry name="Tyranids/GraspingTail" value="<string name='Actions/Tyranids/GraspingTail'/>"/>
	<entry name="Tyranids/GraspingTailDescription" value="Reduces the attacks."/>
	<entry name="Tyranids/GraspingTailFlavor" value="<string name='Actions/Tyranids/GraspingTailFlavor'/>"/>
	<entry name="Tyranids/HiveCommander" value="<string name='Actions/Tyranids/HiveCommander'/>"/>
	<entry name="Tyranids/HiveCommanderDescription" value="Increases the damage and damage reduction."/>
	<entry name="Tyranids/HiveCommanderFlavor" value="<string name='Actions/Tyranids/HiveCommanderFlavor'/>"/>
	<entry name="Tyranids/IndescribableHorror" value="<string name='Actions/Tyranids/IndescribableHorror'/>"/>
	<entry name="Tyranids/IndescribableHorrorDescription" value="Reduces the morale each turn."/>
	<entry name="Tyranids/IndescribableHorrorFlavor" value="<string name='Actions/Tyranids/IndescribableHorrorFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="Gaunt Instincts"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Reduces the biomass upkeep."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="The Hive Mind typically maintains close control over its swarm, directing everything save for unconscious actions—but by loosening that control, the smaller organisms can be allowed just enough agency to forage for themselves."/>
	<entry name="Tyranids/InstinctiveBehaviour" value="Instinctive Behaviour"/>
	<entry name="Tyranids/InstinctiveBehaviourDescription" value="<string name='Traits/Tyranids/InstinctiveBehaviourLurkDescription'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourLurkFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourFeed" value="Instinctive Behaviour (Feed)"/>
	<entry name="Tyranids/InstinctiveBehaviourFeedDescription" value="The unit loses hitpoints each turn while not in range of a synapse creature."/>
	<entry name="Tyranids/InstinctiveBehaviourFeedFlavor" value="Unless controlled or coordinated by the domineering will of the Hive Mind, many Tyranid organisms will revert to their baser instincts."/>
	<entry name="Tyranids/InstinctiveBehaviourHunt" value="Instinctive Behaviour (Hunt)"/>
	<entry name="Tyranids/InstinctiveBehaviourHuntDescription" value="The unit loses movement each turn while not in range of a synapse creature."/>
	<entry name="Tyranids/InstinctiveBehaviourHuntFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourLurk" value="Instinctive Behaviour (Lurk)"/>
	<entry name="Tyranids/InstinctiveBehaviourLurkDescription" value="The unit loses morale each turn while not in range of a synapse creature."/>
	<entry name="Tyranids/InstinctiveBehaviourLurkFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride" value="Override Instinctive Behaviour"/>
	<entry name="Tyranids/InstinctiveBehaviourOverrideDescription" value="The unit's instinctive behaviour is overridden."/>
	<entry name="Tyranids/InstinctiveBehaviourOverrideFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveFire" value="Instinctive Fire"/>
	<entry name="Tyranids/InstinctiveFireDescription" value="Can only target the closest enemy unit."/>
	<entry name="Tyranids/InstinctiveFireFlavor" value="The Tyrannocyte is grown by the Hive Fleet as a delivery mechanism, to land its key troops from space and get them closer to the enemy’s biomass. Yet, after its initial purpose has been fulfilled, the near-mindless creature’s work is not done; floating up, it continues to rain organic, acidic hell down and lash its flensing tentacles at anything nearby."/>
	<entry name="Tyranids/LivingBatteringRam" value="<string name='Actions/Tyranids/LivingBatteringRam'/>"/>
	<entry name="Tyranids/LivingBatteringRamDescription" value="Increases the damage."/>
	<entry name="Tyranids/LivingBatteringRamFlavor" value="<string name='Actions/Tyranids/LivingBatteringRamFlavor'/>"/>
	<entry name="Tyranids/LongRangedDamage" value="Ocular Proliferation"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Increases the armour penetration."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="Targeting enemies at range can be difficult with simple binocular vision. A sensible adaptation is to increase the variety and number of ocular sensors available to the organism, to improve the targeting of all enemy types."/>
	<entry name="Tyranids/MassIncubation" value="<string name='Actions/Tyranids/MassIncubation'/>"/>
	<entry name="Tyranids/MassIncubationDescription" value="<string name='Actions/Tyranids/MassIncubationDescription'/>"/>
	<entry name="Tyranids/MassIncubationFlavor" value="<string name='Actions/Tyranids/MassIncubationFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="Bioweapon Symbiosis"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Increases the armour penetration."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="When the Tyranids were first encountered, particularly their forerunners the Zoats, their weaponry was typically a range of separate organisms. As time has gone by, the Tyranids and their weapons have grown closer together."/>
	<entry name="Tyranids/Onslaught" value="<string name='Actions/Tyranids/Onslaught'/>"/>
	<entry name="Tyranids/OnslaughtDescription" value="Increases the movement."/>
	<entry name="Tyranids/OnslaughtFlavor" value="<string name='Actions/Tyranids/OnslaughtFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="Reduces the accuracy."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation" value="Prey Adaptation"/>
	<entry name="Tyranids/PreyAdaptationDescription" value="The Hive mind gains research when the Malanthrope gains experience from combat."/>
	<entry name="Tyranids/PreyAdaptationFlavor" value="The Malanthrope doesn't just garner new genetic data from fallen corpses—it is also quite capable of observing the learned patterns communicated by creatures to one another, whether parent or teacher, and retaining that for encoding into the Hive Mind's creatures."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="Birthing Canals"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Reduces the influence upkeep."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="A simple shift in the structure of the great organisms that make up the Tyranid hive, birthing canals are something like biological production lines, enabling many creatures to be produced in parallel without the direct oversight of the Hive Mind."/>
	<entry name="Tyranids/PsychicBarrier" value="Psychic Barrier"/>
	<entry name="Tyranids/PsychicBarrierDescription" value="Increases the invulnerable damage reduction."/>
	<entry name="Tyranids/PsychicBarrierFlavor" value="Tyranid horrors the size of the Maleceptor are not without inherent defenses. But beyond its sheer bulk and resilient armour, the Malceptor also projects a formidable psychic barrier, capable of vaporizing or deflecting bullets and energy bolts fired at it before they strike it."/>
	<entry name="Tyranids/PsychicOverload" value="<string name='Actions/Tyranids/PsychicOverload'/>"/>
	<entry name="Tyranids/PsychicOverloadDescription" value="Hits with increased accuracy."/>
	<entry name="Tyranids/PsychicOverloadFlavor" value="The ethereal pseudopods projected by a Maleceptor’s encephalic tissue are manifestations of the Shadow in the Warp itself, the Hive Mind’s nullifying psychic presence. If these should even brush a weakened opponent’s consciousness, they will directly experience the horrifying immensity of that immanence—and die rapidly, horribly and explosively."/>
	<entry name="Tyranids/RakingStrike" value="Raking Strike"/>
	<entry name="Tyranids/RakingStrikeDescription" value="Increases the Hive Crone's melee attacks. Additionally increases the melee attacks against flyers."/>
	<entry name="Tyranids/RakingStrikeFlavor" value="Lengthening and hardening the bladed spurs on a Hive Crone's underside allows it to perform lethal fly-by strikes against enemies. In addition, sharpened wingtips enable the Crone to accurately target enemy pilots."/>
	<entry name="Tyranids/RapaciousHunger" value="<string name='Actions/Tyranids/RapaciousHunger'/>"/>
	<entry name="Tyranids/RapaciousHungerDescription" value="Increases the attacks."/>
	<entry name="Tyranids/RapaciousHungerFlavor" value="<string name='Actions/Tyranids/RapaciousHungerFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="Synapse Archevores"/>
	<entry name="Tyranids/Reclamation2Description" value="Reduces the influence cost."/>
	<entry name="Tyranids/Reclamation2Flavor" value="Reclamation pools may seem the simplest of Tyranid organisms—bubbling digestive pits to render down biomass—but given their core function to the Tyranid lifecycle, they have doubtless seen generations of design refinement. This particular adaptation places control synapses at each pool's edge to manage digestion with less Hive Mind oversight."/>
	<entry name="Tyranids/Reclamation3" value="Assimilation Accelerants"/>
	<entry name="Tyranids/Reclamation3Description" value="Removes the cooldown."/>
	<entry name="Tyranids/Reclamation3Flavor" value="Reclamation has to be an efficient process to best serve the Hive Mind—every basis point of biomass that has to be abandoned could spell defeat. This adaptation makes reclamation a continuous process, which enables high percentage biomass recovery."/>
	<entry name="Tyranids/Regeneration" value="Regeneration"/>
	<entry name="Tyranids/RegenerationDescription" value="Restores hitpoints each turn."/>
	<entry name="Tyranids/RegenerationFlavor" value="Some Tyranids have the ability to recover from horrendous wounds and injuries that should have proven fatal."/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="Scavenger Adaptation"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Reduces the influence upkeep."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="The Hive Mind typically maintains close control over its swarm, directing everything but unconscious actions—but by loosening that control, the smaller organisms can be allowed just enough agency to forage for themselves."/>
	<entry name="Tyranids/ScourgeOfTheBrood" value="<string name='Actions/Tyranids/ScourgeOfTheBrood'/>"/>
	<entry name="Tyranids/ScourgeOfTheBroodDescription" value="Increases the damage."/>
	<entry name="Tyranids/ScourgeOfTheBroodFlavor" value="<string name='Actions/Tyranids/ScourgeOfTheBroodFlavor'/>"/>
	<entry name="Tyranids/ShadowInTheWarp" value="<string name='Actions/Tyranids/ShadowInTheWarp'/>"/>
	<entry name="Tyranids/ShadowInTheWarpDescription" value="Reduces the morale each turn."/>
	<entry name="Tyranids/ShadowInTheWarpFlavor" value="<string name='Actions/Tyranids/ShadowInTheWarpFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="Gigaborer Hives"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Increases the armour penetration."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="The unique and disgusting Borer nest is comprised of a compact nest of Borer beetles, acidic insectoids that launch themselves in a frenzy from the weapon. Gigaborer Hives contain fewer, much larger beetles, simply enabling more damage."/>
	<entry name="Tyranids/SingularPurpose" value="<string name='Actions/Tyranids/SingularPurpose'/>"/>
	<entry name="Tyranids/SingularPurposeDescription" value="Norn Emissaries have increased accuracy and damage against this unit."/>
	<entry name="Tyranids/SingularPurposeFlavor" value="<string name='Actions/Tyranids/SingularPurposeFlavor'/>"/>
	<entry name="Tyranids/SporeCloud" value="<string name='Actions/Tyranids/SporeCloud'/>"/>
	<entry name="Tyranids/SporeCloudDescription" value="Increases the ranged damage reduction."/>
	<entry name="Tyranids/SporeCloudFlavor" value="<string name='Actions/Tyranids/SporeCloudFlavor'/>"/>
	<entry name="Tyranids/SymbioticTargeting" value="Symbiotic Targeting"/>
	<entry name="Tyranids/SymbioticTargetingDescription" value="Increases the ranged accuracy if the unit remains stationary."/>
	<entry name="Tyranids/SynapseLink" value="Synapse Link"/>
	<entry name="Tyranids/SynapseLinkDescription" value="The unit cannot be shaken or broken, is immune to fear and is not subject to instinctive behaviour."/>
	<entry name="Tyranids/SynapseLinkFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/SynapticBacklash" value="Synaptic Backlash"/>
	<entry name="Tyranids/SynapticBacklashDescription" value="Termagants in the area take damage when the Tervigon dies."/>
	<entry name="Tyranids/SynapticBacklashFlavor" value="Though Tyranids have no affection one another—they will watch calmly as their nest-mates die or walk into the reclamation pool—the psychic trauma a Tervigon's brood suffers at the moment of its death is odd, particularly given the Hive Mind's seeming inability to eliminate it. Perhaps it is a legacy of whatever long-dead race the Tervigon's genetic data was based on—and a symbol that the Hive Mind rarely innovates, but mostly plagiarises."/>
	<entry name="Tyranids/ToxinSacs" value="Toxin Sacs"/>
	<entry name="Tyranids/ToxinSacsDescription" value="Increases the damage of melee weapons against infantry and monstrous creature units."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="These parasitic glands secrete vile fluids, coating the Tyranid's claws, fangs and talons with a lethal variety of alien poisons."/>
	<entry name="Tyranids/Tunnel2" value="Echiurean Walls"/>
	<entry name="Tyranids/Tunnel2Description" value="Increases the hitpoints."/>
	<entry name="Tyranids/Tunnel2Flavor" value="Broodhive walls are grown rapidly, meaning they tend to be relatively flimsy creatures, for the Tyranids. Genetors have observed adaptations that grow more slowly, but accrete muscle mass over time, and generally are more resilient to enemy action."/>
	<entry name="Tyranids/UnnaturalResilience" value="Unnatural Resilience"/>
	<entry name="Tyranids/UnnaturalResilienceDescription" value="Increases invulnerable damage reduction and grants additional feel no pain damage reduction."/>
	<entry name="Tyranids/UnnaturalResilienceFlavor" value="Imbued with purpose by the Norn Queen, the Emissary adapts instantly to injuries that would cripple another creature, shrugging off death to pursue its goal."/>
	<entry name="Tyranids/VehiclesUpkeep" value="Megaunt Instincts"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Reduces the biomass upkeep."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="The Hive Mind typically maintains close control over its swarm, directing everything but unconscious actions, save for its alpha creatures—but by loosening that control, larger organisms can direct or predate on the smaller organisms, enabling them to survive without direct control."/>
	<entry name="Tyranids/WarpField" value="Warp Field"/>
	<entry name="Tyranids/WarpFieldDescription" value="Increases the damage reduction."/>
	<entry name="Tyranids/WarpFieldFlavor" value="Zoanthropes are vital nodes for harnessing the Hive Mind's psychic might and are created with a powerful sense of self-preservation. Therefore, they instinctively project a potent Warp field to protect themselves in battle – a mental shield that is invisible but for a slight shimmer when small-arms and heavy-weapons fire alike patters harmlessly against it."/>
	<entry name="TwinLinked" value="Twin-Linked"/>
	<entry name="TwinLinkedDescription" value="Increases the accuracy."/>
	<entry name="TwinLinkedFlavor" value="These weapons are grafted to the same targeting system for greater accuracy."/>
	<entry name="Uncommon" value="Uncommon"/>
	<entry name="UncommonDescription" value="Classification."/>
	<entry name="UnendingHorde" value="Unending Horde"/>
	<entry name="UnendingHordeDescription" value="Increases the damage reduction."/>
	<entry name="UnendingHordeFlavor" value="Like the zombies of ancient Terran fiction, Poxwalkers do not stop unless they are thoroughly destroyed. Their dead flesh is unfeeling, so that attacks that would put down a normal human are mere inconveniences as they giggle and groan onward…"/>
	<entry name="Unique" value="Unique"/>
	<entry name="UniqueDescription" value="Limits the amount of units of this type."/>
	<entry name="Unwieldy" value="Unwieldy"/>
	<entry name="UnwieldyDescription" value="Reduces the accuracy."/>
	<entry name="UnwieldyFlavor" value="This weapon is very large, and more than a little clumsy, making swift blows all but impossible to achieve."/>
	<entry name="VectoredAfterburners" value="Vectored Afterburners"/>
	<entry name="VectoredAfterburnersDescription" value="Increases the movement and ranged damage reduction."/>
	<entry name="Vehicle" value="Vehicle"/>
	<entry name="VehicleDescription" value="Negates the penalty for heavy and ordnance weapons. Increases the movement penalty in forests and imperial ruins."/>
	<entry name="VehicleFlavor" value="War is not a trade solely for living soldiers, but also for mighty war engines and tanks."/>
	<entry name="VeryBulky" value="Very Bulky"/>
	<entry name="VeryBulkyDescription" value="Requires two additional cargo slots in a transport."/>	
	<entry name="VeryBulkyFlavor" value="<string name='Traits/BulkyFlavor'/>"/>	
	<entry name="VoidShield" value="Void Shield"/>
	<entry name="VoidShieldDescription" value="Increases the ranged damage reduction."/>
	<entry name="VoidShieldFlavor" value="<string name='Actions/AstraMilitarum/ProjectedVoidShieldFlavor'/>"/>
	<entry name="VoxCaster" value="Vox-Caster"/>
	<entry name="VoxCasterDescription" value="Reduces the morale loss."/>
	<entry name="VoxCasterFlavor" value="A vox-caster is a reliable communications array connected to the tactical command net via tight-beam transmitters."/>
	<entry name="Waaagh" value="Waaagh!"/>
	<entry name="WaaaghDescription" value="Increases the attacks."/>
	<entry name="WaaaghFlavor" value="The Waaagh! is joie de vivre, crusade, immense psychic power, a tangible belief aura and perhaps the Ork gods themselves, all rolled into one teeth-rattling, gut-wrenching roar of delighted aggression pushing the Orks across the galaxy and into battle. It is the core of being an Ork."/>
	<entry name="Walker" value="Walker"/>
	<entry name="WalkerDescription" value="<string name='Traits/DozerBladeDescription'/>"/>
	<entry name="Weakened" value="Weakened"/>
	<entry name="WeakenedDescription" value="Reduces the movement and damage."/>
	<entry name="WireWeed" value="<string name='Features/WireWeed'/>"/>
	<entry name="WireWeedDescription" value="Deals damage each turn."/>
	<entry name="WireWeedFlavor" value="<string name='Features/WireWeedFlavor'/>"/>
	<entry name="Witchfire" value="Witchfire"/>
	<entry name="WitchfireDescription" value="Classification."/>
	<entry name="Zzap" value="Zzap"/>
	<entry name="ZzapFlavor" value="Zzap guns fire unstable bolts of lightning. They have the potential to punch through the hull of even the heaviest enemy vehicle amid crackling showers of sparks, but have a tendency to overload and electrocute the operating gunner."/>
	<entry name="Zealot" value="Zealot"/>
	<entry name="ZealotDescription" value="Reduces the morale loss, increases the melee damage and grants immunity to fear and pinning."/>
	<entry name="ZealotFlavor" value="Zealots fight on regardless of their casualties or the terrors of war; they are driven forwards by their conviction."/>
</language>
