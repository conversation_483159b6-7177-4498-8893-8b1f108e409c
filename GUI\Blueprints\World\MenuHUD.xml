<?xml version="1.0" encoding="utf-8"?>
<world:menuHUD extends="HUD" layout="Relative" layout.alignment="MiddleCenter" stayOnTop="1">
	<image name="backgroundImage" preferredSize="FillParent FillParent" fitting="ScaleAndCrop" texture="Images/LoadScreenBackground"/>
	<container layout="Relative" layout.alignment="BottomLeft" margin="4 0; 0 4" preferredSize="FillParent FillParent">
		<label name="worldSeedLabel" hint="<string name='GUI/WorldSeedHint'/>" style="<style name='ShadowedHeading' color='GUI/Gray'/>"/>
	</container>
	<container layout="Relative" layout.alignment="BottomRight" margin="0 0; 8 4" preferredSize="FillParent FillParent">
		<label name="versionLabel" style="<style name='ShadowedHeading' color='GUI/Gray'/>"/>
	</container>
	<container layout="Flow" layout.alignment="MiddleCenter" layout.gap="0 0" layout.direction="TopToBottom" preferredSize="1240 720">
		<label name="titleLabel" style="<style name='MainMenuTitle'/>" caption="<string name='GUI/Menu'/>"/>
		<image texture="GUI/HeadlineBar" color="GUI/HeadlineBar"/>
		<component preferredSize="FillParent 30"/>
		<list name="navigationList" visibleContentContainer.content.margin="0 0" minItemHeight="48" preferredSize="400 WrapContent">
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="continue" label.caption="<style color='GUI/Red'/><string name='GUI/Continue'/>"/>
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="pauseTurnTimer" label.caption="<string name='GUI/PauseTurnTimer'/>"/>
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="toggleSimultaneousTurns"/>
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="saveGame" label.caption="<string name='GUI/SaveGame'/>"/>
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="loadGame" label.caption="<string name='GUI/LoadGame'/>"/>
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="settings" label.caption="<string name='GUI/Settings'/>"/>
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="restart" label.caption="<string name='GUI/Restart'/>" visible="0"/> <!-- * Added restart button to the Menu to facilitate restarting the game with the same parameters, but a new seed. -->
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="surrender" label.caption="<string name='GUI/Surrender'/>"/> 
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="exitToMainMenu" label.caption="<string name='GUI/ExitToMainMenu'/>"/>
			<labeledListItem label.alignment="MiddleCenter" label.style="<style name='MainMenuNavigation'/>" name="exitToDesktop" label.caption="<string name='GUI/ExitToDesktop'/>"/>
		</list>
	</container>
</world:menuHUD>
