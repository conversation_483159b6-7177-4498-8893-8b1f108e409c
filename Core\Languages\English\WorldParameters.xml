<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="AIControlDisconnectedPlayers" value="AI-Control Disconnected Players"/>
	<entry name="AdaptiveTurnTimer" value="Adaptive Turn Timer"/>
	<entry name="ArcticRegionDensity" value="Arctic Region Density"/>
	<entry name="ArcticRegionDensity0" value="None"/>
	<entry name="ArcticRegionDensity1" value="Very Low"/>
	<entry name="ArcticRegionDensity2" value="Low"/>
	<entry name="ArcticRegionDensity3" value="Medium"/>
	<entry name="ArcticRegionDensity4" value="High"/>
	<entry name="ArcticRegionDensity5" value="Very High"/>	
	<entry name="ArtefactDensity" value="Artefact Density"/>
	<entry name="ArtefactDensity0" value="Very Low"/>
	<entry name="ArtefactDensity1" value="Low"/>
	<entry name="ArtefactDensity2" value="Medium"/>
	<entry name="ArtefactDensity3" value="High"/>
	<entry name="ArtefactDensity4" value="Very High"/>
	<entry name="AvoidDuplicateRandomFactions" value="Avoid Duplicate Random Factions"/>
	<entry name="AvoidDuplicateRandomFactionsHint" value="Adjust random faction chances so that each faction appears an equal amount of times in the game."/>
	<entry name="DesertRegionDensity" value="Desert Region Density"/>
	<entry name="DesertRegionDensity0" value="None"/>
	<entry name="DesertRegionDensity1" value="Very Low"/>
	<entry name="DesertRegionDensity2" value="Low"/>
	<entry name="DesertRegionDensity3" value="Medium"/>
	<entry name="DesertRegionDensity4" value="High"/>
	<entry name="DesertRegionDensity5" value="Very High"/>
	<entry name="Difficulty" value="Difficulty"/>
	<entry name="Difficulty0" value="Very Easy"/>
	<entry name="Difficulty0AdeptusMechanicus" value="Adsecularis"/>
	<entry name="Difficulty0AstraMilitarum" value="Conscript"/>
	<entry name="Difficulty0ChaosSpaceMarines" value="Cultist"/>
	<entry name="Difficulty0Drukhari" value="Parched"/>
	<entry name="Difficulty0Eldar" value="Guardian"/>
	<entry name="Difficulty0Necrons" value="Flayed One"/>
	<entry name="Difficulty0Orks" value="Squig"/>
	<entry name="Difficulty0Random" value="Very Weak"/>
	<entry name="Difficulty0SistersOfBattle" value="Novitiate"/>
	<entry name="Difficulty0SpaceMarines" value="Neophyte"/>
	<entry name="Difficulty0Tau" value="Gue’vesa"/>
	<entry name="Difficulty0Tyranids" value="Scarab"/>
	<entry name="Difficulty0FactionHint" value="%1%: moderate loyalty disadvantage given."/>
	<entry name="Difficulty1" value="Easy"/>
	<entry name="Difficulty1AdeptusMechanicus" value="Servitor"/>
	<entry name="Difficulty1AstraMilitarum" value="Guardsman"/>
	<entry name="Difficulty1ChaosSpaceMarines" value="Spawn"/>
	<entry name="Difficulty1Drukhari" value="Wrack"/>
	<entry name="Difficulty1Eldar" value="Aspect Warrior"/>
	<entry name="Difficulty1Necrons" value="Warrior"/>
	<entry name="Difficulty1Orks" value="Grot"/>
	<entry name="Difficulty1Random" value="Weak"/>
	<entry name="Difficulty1SistersOfBattle" value="Sister"/>
	<entry name="Difficulty1SpaceMarines" value="Battle-Brother"/>
	<entry name="Difficulty1Tau" value="Shas‘saal"/>
	<entry name="Difficulty1Tyranids" value="Termagant"/>
	<entry name="Difficulty1FactionHint" value="%1%: small loyalty disadvantage given."/>
	<entry name="Difficulty2" value="Medium"/>
	<entry name="Difficulty2AdeptusMechanicus" value="Enginseer"/>
	<entry name="Difficulty2AstraMilitarum" value="Sergeant"/>
	<entry name="Difficulty2ChaosSpaceMarines" value="Fallen Brother"/>
	<entry name="Difficulty2Drukhari" value="Kabalite"/>
	<entry name="Difficulty2Eldar" value="Ranger"/>
	<entry name="Difficulty2Necrons" value="Immortal"/>
	<entry name="Difficulty2Orks" value="Boy"/>
	<entry name="Difficulty2Random" value="Average"/>
	<entry name="Difficulty2SistersOfBattle" value="Celestine"/>
	<entry name="Difficulty2SpaceMarines" value="Sergeant"/>
	<entry name="Difficulty2Tau" value="Shas‘la"/>
	<entry name="Difficulty2Tyranids" value="Warrior"/>
	<entry name="Difficulty2FactionHint" value="%1%: no advantage or disadvantage given."/>
	<entry name="Difficulty3" value="Hard"/>
	<entry name="Difficulty3AdeptusMechanicus" value="Magos"/>
	<entry name="Difficulty3AstraMilitarum" value="Commissar"/>
	<entry name="Difficulty3ChaosSpaceMarines" value="Champion of Chaos"/>
	<entry name="Difficulty3Drukhari" value="Wych"/>
	<entry name="Difficulty3Eldar" value="Exarch"/>
	<entry name="Difficulty3Necrons" value="Triarch Praetorian"/>
	<entry name="Difficulty3Orks" value="Nob"/>
	<entry name="Difficulty3Random" value="Strong"/>
	<entry name="Difficulty3SistersOfBattle" value="Palatine"/>
	<entry name="Difficulty3SpaceMarines" value="Captain"/>
	<entry name="Difficulty3Tau" value="Shas‘ui"/>
	<entry name="Difficulty3Tyranids" value="Lictor"/>
	<entry name="Difficulty3FactionHint" value="%1%: small loyalty and level advantage given."/>
	<entry name="Difficulty4" value="Very Hard"/>
	<entry name="Difficulty4AdeptusMechanicus" value="Fabricator Locum"/>
	<entry name="Difficulty4AstraMilitarum" value="General"/>
	<entry name="Difficulty4ChaosSpaceMarines" value="Chaos Lord"/>
	<entry name="Difficulty4Drukhari" value="Haemonculus"/>
	<entry name="Difficulty4Eldar" value="Autarch"/>
	<entry name="Difficulty4Necrons" value="Cryptek"/>
	<entry name="Difficulty4Orks" value="Boss"/>
	<entry name="Difficulty4Random" value="Very Strong"/>
	<entry name="Difficulty4SistersOfBattle" value="Canoness"/>
	<entry name="Difficulty4SpaceMarines" value="Chapter-Master"/>
	<entry name="Difficulty4Tau" value="Shas‘vre"/>
	<entry name="Difficulty4Tyranids" value="Hive Tyrant"/>
	<entry name="Difficulty4FactionHint" value="%1%: moderate loyalty and level advantage given."/>
	<entry name="Difficulty5" value="Ultra Hard"/>
	<entry name="Difficulty5AdeptusMechanicus" value="Fabricator General"/>
	<entry name="Difficulty5AstraMilitarum" value="Lord Commander"/>
	<entry name="Difficulty5ChaosSpaceMarines" value="Daemon Prince"/>
	<entry name="Difficulty5Drukhari" value="Archon"/>
	<entry name="Difficulty5Eldar" value="Farseer"/>
	<entry name="Difficulty5Necrons" value="Overlord"/>
	<entry name="Difficulty5Orks" value="Warboss"/>
	<entry name="Difficulty5Random" value="Ultra Strong"/>
	<entry name="Difficulty5SistersOfBattle" value="Prioress"/>
	<entry name="Difficulty5SpaceMarines" value="Primarch"/>
	<entry name="Difficulty5Tau" value="Shas‘el"/>
	<entry name="Difficulty5Tyranids" value="Dominatrix"/>
	<entry name="Difficulty5FactionHint" value="%1%: large loyalty and level advantage given."/>
	<entry name="Difficulty6" value="Impossible"/>
	<entry name="Difficulty6AdeptusMechanicus" value="Omnissiah"/>
	<entry name="Difficulty6AstraMilitarum" value="Warmaster"/>
	<entry name="Difficulty6ChaosSpaceMarines" value="Dark God"/>
	<entry name="Difficulty6Drukhari" value="Dark Muse"/>
	<entry name="Difficulty6Eldar" value="Avatar"/>
	<entry name="Difficulty6Necrons" value="Silent King"/>
	<entry name="Difficulty6Orks" value="Prophet of Gork and Mork"/>
	<entry name="Difficulty6Random" value="Invincible"/>
	<entry name="Difficulty6SistersOfBattle" value="Abbess Sanctorum"/>
	<entry name="Difficulty6SpaceMarines" value="Emperor"/>
	<entry name="Difficulty6Tau" value="Aun'O"/>
	<entry name="Difficulty6Tyranids" value="Norn Queen"/>
	<entry name="Difficulty6FactionHint" value="%1%: extreme loyalty and level advantage given."/>
	<entry name="DifficultyHint" value="Modifies AI advantanges."/>
	<entry name="ForestDensity" value="Forest Density"/>
	<entry name="ForestDensity0" value="Very Low"/>
	<entry name="ForestDensity1" value="Low"/>
	<entry name="ForestDensity2" value="Medium"/>
	<entry name="ForestDensity3" value="High"/>
	<entry name="ForestDensity4" value="Very High"/>
	<entry name="GameName" value="Game Name"/>
	<entry name="GameSpeed" value="Game Pace"/>
	<entry name="GameVisibility" value="Game Visibility"/> 
	<entry name="GameVisibility0" value="Friends Only"/>
	<entry name="GameVisibility0Hint" value="Game is not publicly listed and only joinable through friends lists, invites or direct connect."/>
	<entry name="GameVisibility1" value="Private"/>
	<entry name="GameVisibility1Hint" value="Game is not publicly listed and only joinable through invites or direct connect."/>
	<entry name="GameVisibility2" value="Public"/>
	<entry name="GameVisibility2Hint" value="Game is publicly listed and is joinable by anyone."/>
	<entry name="Growth" value="Growth"/>
	<entry name="Growth0" value="Very Low"/>
	<entry name="Growth1" value="Low"/>
	<entry name="Growth2" value="Medium"/>
	<entry name="Growth3" value="High"/>
	<entry name="Growth4" value="Very High"/>	
	<entry name="HolySiteDensity" value="Holy Site Density"/>
	<entry name="HolySiteDensity0" value="Very Low"/>
	<entry name="HolySiteDensity1" value="Low"/>
	<entry name="HolySiteDensity2" value="Medium"/>
	<entry name="HolySiteDensity3" value="High"/>
	<entry name="HolySiteDensity4" value="Very High"/>	
	<entry name="ImperialRuinsDensity" value="Imperial Ruins Density"/>
	<entry name="ImperialRuinsDensity0" value="Very Low"/>
	<entry name="ImperialRuinsDensity1" value="Low"/>
	<entry name="ImperialRuinsDensity2" value="Medium"/>
	<entry name="ImperialRuinsDensity3" value="High"/>
	<entry name="ImperialRuinsDensity4" value="Very High"/>	
	<entry name="JokaeroTraderEncampmentDensity" value="Jokaero Trader Encampment Density"/>
	<entry name="JokaeroTraderEncampmentDensity0" value="Very Low"/>
	<entry name="JokaeroTraderEncampmentDensity1" value="Low"/>
	<entry name="JokaeroTraderEncampmentDensity2" value="Medium"/>
	<entry name="JokaeroTraderEncampmentDensity3" value="High"/>
	<entry name="JokaeroTraderEncampmentDensity4" value="Very High"/>	
	<entry name="LandMass" value="Land Mass"/>
	<entry name="LandMass0" value="Very Low"/>
	<entry name="LandMass1" value="Low"/>
	<entry name="LandMass2" value="Medium"/>
	<entry name="LandMass3" value="High"/>
	<entry name="LandMass4" value="Very High"/>
	<entry name="LordOfSkulls" value="Lord of Skulls DLC"/>
	<entry name="MapEditor" value="Debug Panel"/>
	<entry name="MaximumTechnologyTier" value="Maximum Technology Tier"/>
	<entry name="MaximumTechnologyTier0" value="0"/>
	<entry name="MaximumTechnologyTier1" value="1"/>
	<entry name="MaximumTechnologyTier2" value="2"/>
	<entry name="MaximumTechnologyTier3" value="3"/>
	<entry name="MaximumTechnologyTier4" value="4"/>
	<entry name="MaximumTechnologyTier5" value="5"/>
	<entry name="MaximumTechnologyTier6" value="6"/>
	<entry name="MaximumTechnologyTier7" value="7"/>
	<entry name="MaximumTechnologyTier8" value="8"/>
	<entry name="MaximumTechnologyTier9" value="9"/>
	<entry name="MaximumTechnologyTier10" value="10"/>
	<entry name="NecronTombDensity" value="Necron Tomb Density"/>
	<entry name="NecronTombDensity0" value="Very Low"/>
	<entry name="NecronTombDensity1" value="Low"/>
	<entry name="NecronTombDensity2" value="Medium"/>
	<entry name="NecronTombDensity3" value="High"/>
	<entry name="NecronTombDensity4" value="Very High"/>	
	<entry name="OrkoidFungusDensity" value="Orkoid Fungus Density"/>
	<entry name="OrkoidFungusDensity0" value="Very Low"/>
	<entry name="OrkoidFungusDensity1" value="Low"/>
	<entry name="OrkoidFungusDensity2" value="Medium"/>
	<entry name="OrkoidFungusDensity3" value="High"/>
	<entry name="OrkoidFungusDensity4" value="Very High"/>	
	<entry name="Pace" value="Game Speed"/>
	<entry name="Pace0" value="Very Fast"/>
	<entry name="Pace1" value="Fast"/>
	<entry name="Pace2" value="Standard"/>
	<entry name="Pace3" value="Slow"/>
	<entry name="Pace4" value="Very Slow"/>
	<entry name="Quests" value="Quests"/>
	<entry name="RegionDensity" value="Region Density"/>
	<entry name="RegionDensity0" value="Very Low"/>
	<entry name="RegionDensity1" value="Low"/>
	<entry name="RegionDensity2" value="Medium"/>
	<entry name="RegionDensity3" value="High"/>
	<entry name="RegionDensity4" value="Very High"/>
	<entry name="RegionSize" value="Region Size"/>
	<entry name="RegionSize0" value="Very Small"/>
	<entry name="RegionSize1" value="Small"/>
	<entry name="RegionSize2" value="Medium"/>
	<entry name="RegionSize3" value="Large"/>
	<entry name="RegionSize4" value="Very Large"/>
	<entry name="RequiredTechnologiesPerTier" value="Required Technologies Per Tier"/>
	<entry name="RequiredTechnologiesPerTier0" value="0"/>
	<entry name="RequiredTechnologiesPerTier1" value="1"/>
	<entry name="RequiredTechnologiesPerTier2" value="2"/>
	<entry name="RequiredTechnologiesPerTier3" value="3"/>
	<entry name="RequiredTechnologiesPerTier4" value="4"/>
	<entry name="RequiredTechnologiesPerTier5" value="5"/>
	<entry name="RequiredTechnologiesPerTier6" value="6"/>
	<entry name="RequiredTechnologiesPerTier7" value="7"/>
	<entry name="ResearchCost" value="Research Costs"/>
	<entry name="ResearchCost0" value="Very Low"/>
	<entry name="ResearchCost1" value="Low"/>
	<entry name="ResearchCost2" value="Medium"/>
	<entry name="ResearchCost3" value="High"/>
	<entry name="ResearchCost4" value="Very High"/>
	<entry name="ResearchedTechnologyTier" value="Researched Technology Tier"/>
	<entry name="ResearchedTechnologyTier0" value="0"/>
	<entry name="ResearchedTechnologyTier1" value="1"/>
	<entry name="ResearchedTechnologyTier2" value="2"/>
	<entry name="ResearchedTechnologyTier3" value="3"/>
	<entry name="ResearchedTechnologyTier4" value="4"/>
	<entry name="ResearchedTechnologyTier5" value="5"/>
	<entry name="ResearchedTechnologyTier6" value="6"/>
	<entry name="ResearchedTechnologyTier7" value="7"/>
	<entry name="ResearchedTechnologyTier8" value="8"/>
	<entry name="ResearchedTechnologyTier9" value="9"/>
	<entry name="ResearchedTechnologyTier10" value="10"/>
	<entry name="ResourceCost" value="Resource Costs"/>
	<entry name="ResourceCost0" value="Very Low"/>
	<entry name="ResourceCost1" value="Low"/>
	<entry name="ResourceCost2" value="Medium"/>
	<entry name="ResourceCost3" value="High"/>
	<entry name="ResourceCost4" value="Very High"/>
	<entry name="ResourceUpkeep" value="Resource Upkeeps"/>
	<entry name="ResourceUpkeep0" value="Very Low"/>
	<entry name="ResourceUpkeep1" value="Low"/>
	<entry name="ResourceUpkeep2" value="Medium"/>
	<entry name="ResourceUpkeep3" value="High"/>
	<entry name="ResourceUpkeep4" value="Very High"/>
	<entry name="RiverDensity" value="River Density"/>
	<entry name="RiverDensity0" value="Very Low"/>
	<entry name="RiverDensity1" value="Low"/>
	<entry name="RiverDensity2" value="Medium"/>
	<entry name="RiverDensity3" value="High"/>
	<entry name="RiverDensity4" value="Very High"/>	
	<entry name="RuinsOfVaulDensity" value="Ruins of Vaul Density"/>
	<entry name="RuinsOfVaulDensity0" value="Very Low"/>
	<entry name="RuinsOfVaulDensity1" value="Low"/>
	<entry name="RuinsOfVaulDensity2" value="Medium"/>
	<entry name="RuinsOfVaulDensity3" value="High"/>
	<entry name="RuinsOfVaulDensity4" value="Very High"/>	
	<entry name="Seed" value="World Seed"/>
	<entry name="SimultaneousTurns" value="Simultaneous Turns"/>
	<entry name="SimultaneousTurnsHint" value="If disabled, only members of the same team will act simultaneously."/>
	<entry name="Size" value="World Size"/>
	<entry name="Size0" value="Tiny"/>
	<entry name="Size0Hint" value="%1%: Recommended for 2 players."/>
	<entry name="Size1" value="Small"/>
	<entry name="Size1Hint" value="%1%: Recommended for 3 players."/>
	<entry name="Size2" value="Medium"/>
	<entry name="Size2Hint" value="%1%: Recommended for 4 players."/>
	<entry name="Size3" value="Large"/>
	<entry name="Size3Hint" value="%1%: Recommended for 6 players."/>
	<entry name="Size4" value="Huge"/>
	<entry name="Size4Hint" value="%1%: Recommended for 8 players."/>
	<entry name="SpecialResourceDensity" value="Special Resource Density"/>
	<entry name="SpecialResourceDensity0" value="Very Low"/>
	<entry name="SpecialResourceDensity1" value="Low"/>
	<entry name="SpecialResourceDensity2" value="Medium"/>
	<entry name="SpecialResourceDensity3" value="High"/>
	<entry name="SpecialResourceDensity4" value="Very High"/>
	<entry name="StartingResource" value="Starting Resources"/>
	<entry name="StartingResource0" value="Very Low"/>
	<entry name="StartingResource1" value="Low"/>
	<entry name="StartingResource2" value="Medium"/>
	<entry name="StartingResource3" value="High"/>
	<entry name="StartingResource4" value="Very High"/>
	<entry name="Supplement1" value="Reinforcement Pack DLC"/>
	<entry name="Supplement2" value="Tyranids DLC"/>
	<entry name="Supplement3" value="Chaos Space Marines DLC"/>
	<entry name="Supplement4" value="Fortification Pack DLC"/>
	<entry name="Supplement5" value="T'au DLC"/>
	<entry name="Supplement6" value="Assault Pack DLC"/>
	<entry name="Supplement7" value="Craftworld Aeldari DLC"/>
	<entry name="Supplement8" value="Specialist Pack DLC"/>
	<entry name="Supplement9" value="Adeptus Mechanicus DLC"/>
	<entry name="Supplement10" value="Escalation Pack DLC"/>
	<entry name="Supplement11" value="Adepta Sororitas DLC"/>
	<entry name="Supplement12" value="Firepower Pack DLC"/>
	<entry name="Supplement13" value="Drukhari DLC"/>
	<entry name="Supplement14" value="Demolition Pack DLC"/>
	<entry name="Supplement15" value="Ultima Founding DLC"/>
	<entry name="Supplement16" value="Onslaught Pack DLC"/>
	<entry name="TropicalRegionDensity" value="Tropical Region Density"/>
	<entry name="TropicalRegionDensity0" value="None"/>
	<entry name="TropicalRegionDensity1" value="Very Low"/>
	<entry name="TropicalRegionDensity2" value="Low"/>
	<entry name="TropicalRegionDensity3" value="Medium"/>
	<entry name="TropicalRegionDensity4" value="High"/>
	<entry name="TropicalRegionDensity5" value="Very High"/>	
	<entry name="TurnTimer" value="Turn Timer"/>
	<entry name="TurnTimer0" value="None"/>
	<entry name="TurnTimer1" value="Very Short (1 minute)"/>
	<entry name="TurnTimer2" value="Short (2 minutes)"/>
	<entry name="TurnTimer3" value="Moderate (3 minutes)"/>
	<entry name="TurnTimer4" value="Long (4 minutes)"/>
	<entry name="TurnTimer5" value="Very Long (5 minutes)"/>
	<entry name="WebwayGateDensity" value="Webway Gate Density"/>
	<entry name="WebwayGateDensity0" value="Very Low"/>
	<entry name="WebwayGateDensity1" value="Low"/>
	<entry name="WebwayGateDensity2" value="Medium"/>
	<entry name="WebwayGateDensity3" value="High"/>
	<entry name="WebwayGateDensity4" value="Very High"/>	
	<entry name="WildlifeDensity" value="Wildlife Density"/>
	<entry name="WildlifeDensity0" value="Very Low"/>
	<entry name="WildlifeDensity1" value="Low"/>
	<entry name="WildlifeDensity2" value="Medium"/>
	<entry name="WildlifeDensity3" value="High"/>
	<entry name="WildlifeDensity4" value="Very High"/>
	<entry name="WireWeedDensity" value="Wire Weed Density"/>
	<entry name="WireWeedDensity0" value="Very Low"/>
	<entry name="WireWeedDensity1" value="Low"/>
	<entry name="WireWeedDensity2" value="Medium"/>
	<entry name="WireWeedDensity3" value="High"/>
	<entry name="WireWeedDensity4" value="Very High"/>
	<entry name="WorldSeed" value="World Seed"/>
	<entry name="WorldSize" value="World Size"/>
	<entry name="VolcanicRegionDensity" value="Volcanic Region Density"/>
	<entry name="VolcanicRegionDensity0" value="None"/>
	<entry name="VolcanicRegionDensity1" value="Very Low"/>
	<entry name="VolcanicRegionDensity2" value="Low"/>
	<entry name="VolcanicRegionDensity3" value="Medium"/>
	<entry name="VolcanicRegionDensity4" value="High"/>
	<entry name="VolcanicRegionDensity5" value="Very High"/>
</language>
