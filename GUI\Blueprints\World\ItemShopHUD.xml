<?xml version="1.0" encoding="utf-8"?>
<world:itemShopHUD extends="HUD" layout="Flow" layout.alignment="MiddleCenter" layout.direction="TopToBottom" minSize="0 768">
	<contentContainer surface.texture="GUI/TopBar" name="topContainer" showEffect="FadeInTop" hideEffect="FadeOutTop" content.layout.alignment="BottomCenter" content.layout.gap="0 0" content.layout.direction="TopToBottom" preferredSize="FillParent FillParent" weights="FillAll 1" content.margin="16 0; 16 16">
		<label caption="<style name='MenuTitle'/><string name='GUI/ItemShop'/>" preferredSize="WrapContent 38"/>
		<image texture="GUI/HeadlineBar" color="GUI/HeadlineBar"/>
	</contentContainer>
	<contentContainer name="contentContainer" showEffect="FadeInLeft" hideEffect="FadeOutRight" content.layout.gap="20 20" content.margin="16 16" preferredSize="1250 WrapContent">
		<contentContainer surface.texture="GUI/ShadowedSurface" content.margin="0 4; 0 0" preferredSize="FillParent WrapContent" weights="1 FillAll">
			<label name="heroLabel" alignment="BottomCenter" style="<style name='Title'/>" preferredSize="FillParent WrapContent"/>
			<list name="heroItemList" minItemHeight="32" preferredSize="FillParent WrapContent"/>
		</contentContainer>
		<contentContainer surface.texture="GUI/ShadowedSurface" content.margin="0 4; 0 0" preferredSize="FillParent WrapContent" weights="1 FillAll">
			<label alignment="BottomCenter" style="<style name='Title'/>" caption="<string name='Features/JokaeroTraderEncampment'/>" preferredSize="FillParent WrapContent"/>
			<list name="shopItemList" minItemHeight="32" preferredSize="FillParent WrapContent" maxSize="0 508"/>
		</contentContainer>
		<image name="shopImage" preferredSize="240 522" texture=""/>
	</contentContainer>
	<contentContainer surface.texture="GUI/BottomBar" name="bottomContainer" showEffect="FadeInBottom" hideEffect="FadeOutBottom" content.layout.alignment="TopCenter" preferredSize="FillParent FillParent" weights="FillAll 1" content.margin="16 22; 16 0">
		<okButton/>
	</contentContainer>
</world:itemShopHUD>
