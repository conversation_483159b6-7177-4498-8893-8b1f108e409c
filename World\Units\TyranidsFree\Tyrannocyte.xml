<?xml version="1.0" encoding="utf-8"?>
<unit>
	<model>
		<unit mesh="Units/Tyranids/Tyrannocyte"
				material="Units/Tyranids/Tyrannocyte"
				scale="1.3 1.3 1.3"
				idleAnimation="Units/Tyranids/TyrannocyteIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				bloodBone="Core"/>
	</model>
	<weapons>
		<weapon name="BarbedStrangler" count="5">
			<model>
				<weapon muzzleBone="Muzzle" muzzleCount="3" fireInterval="0.4"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>				
				<armor base="6"/> <!-- %armor base armor=4+ -->
				<cargoSlots base="6"/> <!-- %cargoSlots base capacity=20 -->
				<biomassUpkeep base="2.0"/> <!-- %biomassUpkeep base tier=6 factor=1 -->
				<biomassCost base="40.0"/> <!-- %biomassCost base tier=6 factor=1 -->
				<hitpointsMax base="24.0"/> <!-- %hitpointsMax base toughness=5 wounds=6 -->
				<meleeAccuracy base="4"/> <!-- %meleeAccuracy base weaponSkill=2 -->
				<meleeAttacks base="3"/>
				<strengthDamage base="2"/> <!-- %strengthDamage base strength=5 -->
				<moraleMax base="6"/> <!-- %moraleMax base leadership=6 -->
				<movementMax base="3"/>
				<productionCost base="36.0"/> <!-- %productionCost base tier=6 factor=1 -->
				<rangedAccuracy base="4"/> <!-- %rangedAccuracy base ballisticSkill=2 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseVehiclesScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/TyrannocyteAttack"
						beginFire="1.0"
						endFire="1.9"/>
			</model>
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/TyrannocyteDie0"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="1.5"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>
		</die>
		<move>
			<model>
				<action animation="Units/Tyranids/TyrannocyteMove"
						sound="Units/Tyranids/MalanthropeMove"
						soundCount="2"/>
			</model>
		</move>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				requiredActionPoints="0"
				requiredUpgrade="Tyranids/HammerOfWrath">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="HammerOfWrath" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Fearless"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="Tyranids/InstinctiveFire"/>
		<trait name="MonstrousCreature"/>
		<trait name="MoveThroughCover"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<trait name="Relentless"/>
		<!-- <trait name="Smash"/> -->
		<trait name="Transport"/>
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
	</traits>
</unit>