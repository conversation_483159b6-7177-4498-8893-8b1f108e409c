<?xml version="1.0" encoding="utf-8"?>
<unit dlc="Supplement2">
	<model>
		<unit mesh="Units/Tyranids/HiveCrone"
				material="Units/Tyranids/HiveCrone"
				scale="0.9 0.9 0.9"
				idleAnimation="Units/Tyranids/HiveCroneIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="1.0"
				position="0 0 64"
				bloodBone="BloodBone"/>
	</model>
	<weapons>
		<weapon name="DroolCannon">
			<model>
				<projectileWeapon muzzleBone=".Muzzle" fireInterval="0.5"/>
			</model>
		</weapon>
		<weapon name="ScythingTalons">
			<model>
				<weapon fireInterval="0.8"/>
			</model>
		</weapon>
		<weapon name="StingerSalvo" requiredUpgrade="Tyranids/StingerSalvo">
			<model>
				<projectileWeapon muzzleBone="BloodBone" fireInterval="10.0"/>
			</model>
		</weapon>
		<weapon name="Tentaclids" slotName="Tentaclids" count="4" enabled="0">
			<model>
				<missileWeapon fireInterval="0.33" muzzleBone="TentaclidMuzzle"
						muzzleCount="2"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>				
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="6.0"/> <!-- %biomassUpkeep base tier=9 factor=1 -->
				<biomassCost base="120.0"/> <!-- %biomassCost base tier=9 factor=1 -->
				<hitpointsMax base="36.0"/> <!-- %hitpointsMax base toughness=6 wounds=6 -->
				<meleeAccuracy base="6"/> <!-- %meleeAccuracy base weaponSkill=3 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="3"/> <!-- %strengthDamage base strength=6 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=9 -->
				<movementMax base="6"/>
				<productionCost base="54.0"/> <!-- %productionCost base tier=9 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseAircraftScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/HiveCroneAttack"
						beginFire="1.33"
						endFire="2"
						chargeAnimation="Units/Tyranids/HiveCroneCharge"
						chargeBeginFire="0.366666666667"
						chargeEndFire="1.33333333333"
						meleeAnimation="Units/Tyranids/HiveCroneMelee"
						meleeBeginSwing="0.0666666666667"
						meleeEndSwing="1.0"/>
			</model>
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/HiveCroneDie"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="0.75"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>
		</die>
		<move>
			<model>
				<action animation="Units/Tyranids/HiveCroneMove"
						sound="Units/Tyranids/HiveTyrantMove"
						soundCount="2"/>
			</model>
		</move>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<jink consumedActionPoints="0" consumedMovement="0" requiredActionPoints="0" cooldown="3">
			<model>
				<action sound="Actions/ChargeUp"/>
			</model>
			<conditions>
				<unit>
					<notAttackedThisTurn/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait duration="1" name="Jink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</jink>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				requiredActionPoints="0"
				requiredUpgrade="Tyranids/HammerOfWrath">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="HammerOfWrath" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>
		<instinctiveBehaviourOverride consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/InstinctiveBehaviourOverride"
				requiredActionPoints="0">
			<model>
				<action sound="Actions/HiveMindBuff"/>
			</model>
			<modifiers>
				<modifier requiredUpgrade="Tyranids/InstinctiveBehaviourOverride2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
			</modifiers>
			<conditions>
				<unit>
					<noTrait name="Tyranids/InstinctiveBehaviourOverride"/>
					<noTrait name="Tyranids/SynapseLink"/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/InstinctiveBehaviourOverride" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</instinctiveBehaviourOverride>
		<useWeapon consumedActionPoints="0"
				cooldown="3"
				requiredActionPoints="0"
				weaponSlotName="Tentaclids">
			<model>
				<action animation="Units/Tyranids/HiveCroneAttack"
						beginFire="1.33"
						endFire="2"/>
			</model>
		</useWeapon>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Fearless"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="Flyer"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="Tyranids/InstinctiveBehaviour"/>
		<trait name="MonstrousCreature"/>
		<!-- <trait name="MoveThroughCover"/> -->
		<trait name="Tyranids/RakingStrike"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<trait name="Relentless"/>
		<trait name="Smash"/>
		<trait name="Tyranids/ToxinSacs" requiredUpgrade="Tyranids/ToxinSacs"/>
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
	</traits>
</unit>
