<?xml version="1.0" encoding="utf-8"?>
<unit dlc="Supplement2">
	<model>
		<unit mesh="Units/Tyranids/Carnifex"
				material="Units/Tyranids/Carnifex"
				scale="1.5 1.5 1.5"
				idleAnimation="Units/Tyranids/CarnifexIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				bloodBone="Bloodbone"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="BioPlasma" requiredUpgrade="Tyranids/BioPlasma">
			<model>
				<flamerWeapon muzzleBone="Bone008"/>
			</model>
		</weapon>
		<weapon name="BoneMace" requiredUpgrade="Tyranids/BoneMace">
			<model>
				<weapon fireInterval="1.0"/>
			</model>
		</weapon>
		<weapon name="ScythingTalons">
			<model>
				<weapon fireInterval="1.0"/>
			</model>
		</weapon>
		<weapon name="SpineBanks">
			<model>
				<projectileWeapon muzzleBone="Bloodbone"
						fireInterval="3.0"/>
			</model>
		</weapon>
		<weapon name="StranglethornCannon">
			<model>
				<projectileWeapon muzzleBone=".Muzzle" fireInterval="0.5"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="4.0"/> <!-- %biomassUpkeep base tier=8 factor=1 -->
				<biomassCost base="80.0"/> <!-- %biomassCost base tier=8 factor=1 -->
				<hitpointsMax base="30.0"/> <!-- %hitpointsMax base toughness=6 wounds=5 -->
				<meleeAccuracy base="6"/> <!-- %meleeAccuracy base weaponSkill=3 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="8"/> <!-- %strengthDamage base strength=9 -->
				<moraleMax base="6"/> <!-- %moraleMax base leadership=6 -->
				<movementMax base="3"/>
				<productionCost base="48.0"/> <!-- %productionCost base tier=8 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseVehiclesScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/CarnifexAttack"
						beginFire="0.833333333333"
						endFire="2.83333333333"
						chargeAnimation="Units/Tyranids/CarnifexCharge"
						chargeBeginFire="0.5"
						chargeEndFire="1.16666666667"
						meleeAnimation="Units/Tyranids/CarnifexMelee"
						meleeBeginSwing="0.333333333333"
						meleeEndSwing="0.666666666667"/>
			</model>
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/CarnifexDie"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="1.0"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>
		</die>
		<move>
			<model>
				<action animation="Units/Tyranids/CarnifexMove"
						sound="Units/Tyranids/CarnifexMove"
						soundCount="2"/>
			</model>
		</move>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/LivingBatteringRam"
				requiredActionPoints="0">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/LivingBatteringRam" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>
		<instinctiveBehaviourOverride consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/InstinctiveBehaviourOverride"
				requiredActionPoints="0">
			<model>
				<action sound="Actions/HiveMindBuff"/>
			</model>
			<modifiers>
				<modifier requiredUpgrade="Tyranids/InstinctiveBehaviourOverride2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
			</modifiers>
			<conditions>
				<unit>
					<noTrait name="Tyranids/InstinctiveBehaviourOverride"/>
					<noTrait name="Tyranids/SynapseLink"/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/InstinctiveBehaviourOverride" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</instinctiveBehaviourOverride>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="Fearless"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Tyranids/InstinctiveBehaviour"/>
		<trait name="MonstrousCreature"/>
		<trait name="MoveThroughCover"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<trait name="Relentless"/>
		<trait name="Smash"/>
		<trait name="Tyranids/ToxinSacs" requiredUpgrade="Tyranids/ToxinSacs"/>
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
	</traits>
</unit>