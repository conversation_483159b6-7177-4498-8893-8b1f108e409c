<?xml version="1.0" encoding="utf-8"?>
<unit dlc="Supplement6">
	<model>
		<unit mesh="Units/Tyranids/ScythedHierodule"
				material="Units/Tyranids/ScythedHierodule"
				scale="1.6 1.6 1.6"
				idleAnimation="Units/Tyranids/ScythedHieroduleIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				bloodBone="BloodBone"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="ScythingTalons" count="2">
			<model>
				<weapon fireInterval="10.0"/>
			</model>
		</weapon>
		<weapon name="BioAcidSpray">
			<model>
				<flamerWeapon muzzleBone="Muzzle"/>
			</model>
		</weapon>
		<weapon name="Stomp" slotName="Stomp" enabled="0"/>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>				
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="16.0"/> <!-- %biomassUpkeep base tier=12 factor=1 -->
				<biomassCost base="320.0"/> <!-- %biomassCost base tier=12 factor=1 -->
				<hitpointsMax base="72.0"/> <!-- %hitpointsMax base toughness=8 wounds=6 -->
				<meleeAccuracy base="8"/> <!-- %meleeAccuracy base weaponSkill=4 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="12"/> <!-- %strengthDamage base strength=10 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=10 -->
				<movementMax base="3"/>
				<productionCost base="72.0"/> <!-- %productionCost base tier=12 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseVehiclesScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/ScythedHieroduleAttack"
						beginFire="1.2"
						endFire="1.67"
						chargeAnimation="Units/Tyranids/ScythedHieroduleCharge"
						chargeBeginFire="0.1"
						chargeEndFire="1.0"
						meleeAnimation="Units/Tyranids/ScythedHieroduleMelee"
						meleeBeginSwing="0.67"
						meleeEndSwing="1.5"/>
			</model>
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/ScythedHieroduleDie0"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="1"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>
		</die>
		<move>
			<model>
				<action animation="Units/Tyranids/ScythedHieroduleMove"
						sound="Units/Tyranids/HaruspexMove"
						soundCount="2"/>
			</model>
		</move>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<turboBoost name="Agile"
				consumedMovement="0"
				cooldown="3"
				consumedActionPoints="0"
				requiredActionPoints="1">
			<model>
				<action sound="Actions/HiveMindBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait duration="1" name="Agile"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</turboBoost>
		<stomp consumedActionPoints="0"
				cooldown="1"
				requiredActionPoints="0"
				weaponSlotName="Stomp"/>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				requiredActionPoints="0"
				requiredUpgrade="Tyranids/HammerOfWrath">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="HammerOfWrath" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>
		<instinctiveBehaviourOverride consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/InstinctiveBehaviourOverride"
				requiredActionPoints="0">
			<model>
				<action sound="Actions/HiveMindBuff"/>
			</model>
			<modifiers>
				<modifier requiredUpgrade="Tyranids/InstinctiveBehaviourOverride2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
			</modifiers>
			<conditions>
				<unit>
					<noTrait name="Tyranids/InstinctiveBehaviourOverride"/>
					<noTrait name="Tyranids/SynapseLink"/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/InstinctiveBehaviourOverride" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</instinctiveBehaviourOverride>		
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood"/>
		<trait name="Tyranids/InstinctiveBehaviour"/>
		<trait name="Tyranids/ToxinSacs" requiredUpgrade="Tyranids/ToxinSacs"/>
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Fearless"/>
		<trait name="FeelNoPain"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="Gargantuan"/>
		<trait name="MonstrousCreature"/>
		<trait name="MoveThroughCover"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<!-- <trait name="Relentless"/> -->
		<trait name="Smash"/>
		<trait name="Strikedown"/>
	</traits>
</unit>
