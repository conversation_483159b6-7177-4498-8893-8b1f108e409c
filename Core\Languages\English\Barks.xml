<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#0" value="Vector keyed in."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#1" value="Five-by-five."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#2" value="Fly-by commencing."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#3" value="Stubber round saturation commencing."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Broken#0" value="Preserving aerial asset"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Hurt#0" value="Wings clipped"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#0" value="Circling, circling."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#1" value="Aerial libation distribution."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#2" value="Pressure change. Morphing fibre-weave."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#3" value="Preparing to evacuate Land Crawlers."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Shaken#0" value="Mayday, mayday!"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Victory#0" value="Enemy signatures no longer detected. Returning to cycle."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#0" value="Directing Kastelans."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#1" value="Anointing runes."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#2" value="Suppressing silica animus."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#3" value="Inserting doctrina wafers."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Broken#0" value="…the Men of Iron…"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Hurt#0" value="…beyond repair…"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#0" value="The flesh is weak, the machine is strong."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#1" value="In binary, rhythm and song. I would that you could hear."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#2" value="There can be no improvement on the Omnissiah's design."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#3" value="In nomine imperii et mechanici et spiritus omnissiah."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Shaken#0" value="Retreat wafers found."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Victory#0" value="Ancient wisdom, power proved."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#0" value="VESTRA INDUSTRIA NOSTRUM"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#1" value="WASTE IS SIN"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#2" value="FOR THE MOTIVE FORCE!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#3" value="THE SPARK MOVES WITHIN"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Broken#0" value="Nostra potex decit… our will fails!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Hurt#0" value="WE YET MARCH."/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#0" value="I CRY THE OMNISSIAH'S TEARS"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#1" value="CURSE THE CORPUSCARII"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#2" value="MY ELECTOO, IT HUNGERS"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#3" value="IN NOMINE DEI MECHANICI"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Shaken#0" value="SPARK OUT"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Victory#0" value="WE ARE VIGOUR, PERSONIFIED"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#0" value="They will taste Mars' wisdom!"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#1" value="Long-range solution found."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#2" value="Datalink targetted accepted."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#3" value="Machine Spirits unleashed."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Broken#0" value="Dominus, we must-"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Hurt#0" value="Monotask Servitors failing."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#0" value="Resting the servitors."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#1" value="Oiling the chassis."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#2" value="Excising weakness."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#3" value="Dreaming of a red planet…"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Shaken#0" value="Superior enemy firing solutions!"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Victory#0" value="For 10,000 years, our victory has been certain."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#0" value="C32: Firing Pattern Achieved."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#1" value="8-7: Destroyer Clade Active"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#2" value="7T32: All Armaments Deployed."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#3" value="867: Cognis And-or Phosphor Ignition."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Broken#0" value="Only Men Fear. We Once Were-"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Hurt#0" value="Functionality Compromised. Flesh Extant."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#0" value="T418: Idle Servitor Present"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#1" value="182: Awaiting Maintenance."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#2" value="ERROR: What Was I?"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#3" value="326: Memories Surfacing… Suppressed"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Shaken#0" value="991: Emotions Escaping."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Victory#0" value="T1: Function Fulfilled, Next Target."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#0" value="We are but a weapon in the right hand of the Emperor."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#1" value="For the honour of the Questor Mechanicus!"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#2" value="Fear my tread!"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#3" value="I AM AN IMPERIAL KNIGHT!"/>
	<entry name="AdeptusMechanicus/KnightCrusader:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Broken#0" value="It… it cannot be."/>
	<entry name="AdeptusMechanicus/KnightCrusader:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Hurt#0" value="Death before dishonour."/>
	<entry name="AdeptusMechanicus/KnightCrusader:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#0" value="My lady awaits."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#1" value="For the jousts of my homeworld."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#2" value="My house is famed for its humility, you know."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#3" value="This iron giant awaits."/>
	<entry name="AdeptusMechanicus/KnightCrusader:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Shaken#0" value="YOU DARE."/>
	<entry name="AdeptusMechanicus/KnightCrusader:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Victory#0" value="It is no shame to fall at my hand."/>
	<entry name="AdeptusMechanicus/KnightCrusader:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#0" value="Purging, Dominus."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#1" value="Still purging, Dominus."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#2" value="My talons cut cleanly."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#3" value="You cannot escape phosphor, fools."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Broken#0" value="Already in flight…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Hurt#0" value="Our purging is at hand…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#0" value="Clean… must clean."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#1" value="Limb stumps… itch."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#2" value="Always on the wing."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:IdleCount" value="3"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Shaken#0" value="The filth is… rampant."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Victory#0" value="Ashes to ashes…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#0" value="Unleashing the Motive Force, tch."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#1" value="Galvanising the Skitarii, yesss…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#2" value="…mmmagnarail lance…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#3" value="Are we not invigorated?!"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Broken#0" value="Logic dictates this is not happening."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Hurt#0" value="We… we did not plan for this."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#0" value="Oh, I am just leaking power."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#1" value="Tapping the Galvanic Cell."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#2" value="Overcharging, underserving."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#3" value="To understand the Xenos…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Shaken#0" value="Protect your Magos, insects!"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Victory#0" value="Their bioelectricity… is mine."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#0" value="Use cover, conserve fire."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#1" value="I need you alive, Skitarii."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#2" value="No dissent, now we fight."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#3" value="I have control!"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Broken#0" value="We are disposable…"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Hurt#0" value="Dying..? Save the Magos!"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#0" value="Following your plans, Magos."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#1" value="Could we save some Skitarii?"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#2" value="We live to die, Magos."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#3" value="Servo Skull, I name thee Morte."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Shaken#0" value="Issue the Control Edict!"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Victory#0" value="Our losses were minimal, Magos."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#0" value="Rad-Troopers in the field!"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#1" value="Let's see who the Radium kills faster…"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#2" value="Carbines a-glow."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#3" value="There's no stopping us."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Broken#0" value="Let's die later instead."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Hurt#0" value="Leaking rad and blood, here."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#0" value="I've 99 upgrades, but rad-protection isn't one of them."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#1" value="I'm burning up."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#2" value="Ugh… don't feel so good."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#3" value="I mean, I wouldn't eat near us either."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Shaken#0" value="Magos, we are dying for you."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Victory#0" value="Not even a half-life left of them."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#0" value="My move, your death."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#1" value="Pawns, advance."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#2" value="I see all the pieces."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#3" value="Making every move."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Broken#0" value="A defense becomes necessary… Luzhin?"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Hurt#0" value="Targetting the king, clever."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#0" value="No cycle is idle."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#1" value="Inefficiency becomes entropy."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#2" value="What joy in manipulation."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#3" value="Yes… I see their moves and countermoves."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Shaken#0" value="Unexpected, a gambit. Admirable."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Victory#0" value="I planned this victory several moves ago. But the sweetness remains."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#0" value="The city awaits."/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#1" value="Their lives are for us to take, not you."/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#2" value="Servitors firing."/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#3" value="Forge in combat."/>
	<entry name="AdeptusMechanicus/Headquarters:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/Headquarters:Broken#0" value="The city will fall."/>
	<entry name="AdeptusMechanicus/Headquarters:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Hurt#0" value="We are sustaining civilian casualties. Acceptable."/>
	<entry name="AdeptusMechanicus/Headquarters:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#0" value="A revolt is being suppressed."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#1" value="Clones are being sent to the Protoservitor Cradles."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#2" value="In the name of the Omnissiah, hab-fanes cleared."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#3" value="Bionics production up 0.1%"/>
	<entry name="AdeptusMechanicus/Headquarters:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/Headquarters:Shaken#0" value="The masses are terrified. Provosts have been deployed."/>
	<entry name="AdeptusMechanicus/Headquarters:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Victory#0" value="External threats eliminated. Turning attention to dissent."/>
	<entry name="AdeptusMechanicus/Headquarters:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="AdeptusMechanicus/ArchaeopterTransvector"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="AdeptusMechanicus/IronstriderBallistarius"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="AdeptusMechanicus/IronstriderBallistarius"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="AdeptusMechanicus/SkitariiVanguard"/>
	<entry name="AstraMilitarum/Baneblade:AlliedFaction:SpaceMarines#0" value="Space Marines sighted, commander. Comms channels are open."/>
	<entry name="AstraMilitarum/Baneblade:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Lord Commissar spotted. Preparing to drive him closer."/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/TankCommander#0" value="Sir, glad to see an officer who appreciates the value of armour, sir!"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/TankCommanderCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Artefact#0" value="Xenos structure sighted."/>
	<entry name="AstraMilitarum/Baneblade:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Attack#0" value="Firing solution plotted in."/>
	<entry name="AstraMilitarum/Baneblade:Attack#1" value="Target acquired."/>
	<entry name="AstraMilitarum/Baneblade:Attack#2" value="Firing barrels 1-11, sir."/>
	<entry name="AstraMilitarum/Baneblade:AttackCount" value="3"/>
	<entry name="AstraMilitarum/Baneblade:Broken#0" value="Engaging reverse gear, sir."/>
	<entry name="AstraMilitarum/Baneblade:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Cover#0" value="Not sure how, but the tank is in cover, sir."/>
	<entry name="AstraMilitarum/Baneblade:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:AstraMilitarum#0" value="Traitors sighted, sir."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:Necrons#0" value="Xenos sighted, sir. Preparing to show them what a real metal monster is."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:Orks#0" value="Greenskins in the area, sir. They're not going to make a Skullhamma out of us!"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:SpaceMarines#0" value="Enemy Adeptus Astartes located. No Fellblades in sight."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyUnit:Neutral/Enslaver#0" value="Sir, advise rapid retreat. We can't guarantee this vehicle won't be compromised."/>
	<entry name="AstraMilitarum/Baneblade:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Hurt#0" value="Severe damage sustained, sir. Advise return to Mars for repairs."/>
	<entry name="AstraMilitarum/Baneblade:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Idle#0" value="Making tracks, sir."/>
	<entry name="AstraMilitarum/Baneblade:Idle#1" value="Checking all eleven barrels for hell, sir."/>
	<entry name="AstraMilitarum/Baneblade:Idle#2" value="The enemy is weak, sir. We will help them fail."/>
	<entry name="AstraMilitarum/Baneblade:Idle#3" value="Counting rivets, sir."/>
	<entry name="AstraMilitarum/Baneblade:Idle#4" value="When I grow up, I want to be a Hellhammer. Sir."/>
	<entry name="AstraMilitarum/Baneblade:Idle#5" value="Did you hear that Vance Stubbs lost a hundred Baneblades, sir? Somewhat careless."/>
	<entry name="AstraMilitarum/Baneblade:IdleCount" value="6"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory0#0" value="Ready to reclaim Gladius Prime, sir."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory0Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory1#0" value="Regrouping the Guard, sir."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory1Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory2#0" value="Sir, will all respect, that Tech-Priest isn't fit to oil our sponsons."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory2Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory3#0" value="We'll leave the underground exploration to the infantry. Sir."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory3Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory4#0" value="We can keep the Xenos back, not a problem, sir."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory4Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory5#0" value="Kastelans! We'll need all eleven barrels, sir!"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory5Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Shaken#0" value="No retreat, sir! Not sure this thing actually goes backwards, anyway."/>
	<entry name="AstraMilitarum/Baneblade:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Victory#0" value="Target eliminated, sir. Reloading all barrels."/>
	<entry name="AstraMilitarum/Baneblade:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:WireWeed#0" value="Sir, advise we move the vehicle; the crew can't handle this wire."/>
	<entry name="AstraMilitarum/Baneblade:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:AlliedFaction:SpaceMarines#0" value="Allied Astartes sighted, praise the Emperor"/>
	<entry name="AstraMilitarum/Basilisk:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Commissar sighted. Glad he's on the frontlines… far away from us."/>
	<entry name="AstraMilitarum/Basilisk:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Artefact#0" value="Shells aren't scratching that thing. What's it made of?"/>
	<entry name="AstraMilitarum/Basilisk:Artefact#1" value="We'll be purged just for looking at that."/>
	<entry name="AstraMilitarum/Basilisk:ArtefactCount" value="2"/>
	<entry name="AstraMilitarum/Basilisk:Attack#0" value="Co-ordinates received, solution set. Firing."/>
	<entry name="AstraMilitarum/Basilisk:Attack#1" value="Shells en route."/>
	<entry name="AstraMilitarum/Basilisk:Attack#2" value="Spotters in the field."/>
	<entry name="AstraMilitarum/Basilisk:Attack#3" value="Target under fire. Glad we're back here!"/>
	<entry name="AstraMilitarum/Basilisk:AttackCount" value="4"/>
	<entry name="AstraMilitarum/Basilisk:Broken#0" value="Leave the gun and run!"/>
	<entry name="AstraMilitarum/Basilisk:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Cover#0" value="Perfect artillery location. They won't see us coming."/>
	<entry name="AstraMilitarum/Basilisk:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:AstraMilitarum#0" value="We always have a gift for traitors—132mm of the Emperor's fury."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:Necrons#0" value="Tin men? 15km seems the perfect distance to engage them."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:Orks#0" value="Ah, Greenskins. What this cannon was built for."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:SpaceMarines#0" value="Renegade Marines? I wonder if their haloes protect them against hi-ex?"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyUnit:Neutral/Enslaver#0" value="What are those THINGS? Keep them at range, men."/>
	<entry name="AstraMilitarum/Basilisk:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Hurt#0" value="Crew compartment penetrated… it's a mess in here."/>
	<entry name="AstraMilitarum/Basilisk:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Idle#0" value="Did you hear about the Saint of Basilisks? He got canonized. Geddit?"/>
	<entry name="AstraMilitarum/Basilisk:Idle#1" value="Don't send a Manticore to do a Basilisk's job."/>
	<entry name="AstraMilitarum/Basilisk:Idle#2" value="I pity the poor grunts on the frontlines… but not enough to join 'em."/>
	<entry name="AstraMilitarum/Basilisk:Idle#3" value="Just tell us when there's something to shoot."/>
	<entry name="AstraMilitarum/Basilisk:Idle#4" value="Nothing happens in war… and it happens repeatedly."/>
	<entry name="AstraMilitarum/Basilisk:IdleCount" value="5"/>
	<entry name="AstraMilitarum/Basilisk:Shaken#0" value="Is someone actually shooting… at us?"/>
	<entry name="AstraMilitarum/Basilisk:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Victory#0" value="Turning enemies into craters for ten millenium, sah!"/>
	<entry name="AstraMilitarum/Basilisk:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:WireWeed#0" value="Biowire? I mean there are worse firing positions, like inside the Great Rift…"/>
	<entry name="AstraMilitarum/Basilisk:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedFaction:SpaceMarines#0" value="Marines bossman!"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Bullgryns loyal."/>
	<entry name="AstraMilitarum/Bullgryn:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Artefact#0" value="Big fing?"/>
	<entry name="AstraMilitarum/Bullgryn:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Attack#0" value="Bullgryns fighting, yer bossman."/>
	<entry name="AstraMilitarum/Bullgryn:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Broken#0" value="Ogryns not that tough."/>
	<entry name="AstraMilitarum/Bullgryn:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Cover#0" value="Hiding inna bush."/>
	<entry name="AstraMilitarum/Bullgryn:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:AstraMilitarum#0" value="Wait, oos fighting fer the Emperor? Us or dem?"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:Necrons#0" value="Can't chew tin. Damn."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:Orks#0" value="Orks. Tough, good lookin, clevver. Like us."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:SpaceMarines#0" value="Da Emperor's kids? We goota fight dem?!"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyUnit:Neutral/Enslaver#0" value="Nah, I don't get what dey are."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Hurt#0" value="Ow."/>
	<entry name="AstraMilitarum/Bullgryn:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#0" value="Dem Bone'eads… deys smart."/>
	<entry name="AstraMilitarum/Bullgryn:Idle#1" value="One Ogryn on dead Ork's chest. Uh… two! Two Ogryns…"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#2" value="Sharpening our clubs, sir."/>
	<entry name="AstraMilitarum/Bullgryn:Idle#3" value="We do what's we's told. And you told us nuffin."/>
	<entry name="AstraMilitarum/Bullgryn:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Bullgryn:Shaken#0" value="Wait… which way we goin?"/>
	<entry name="AstraMilitarum/Bullgryn:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Victory#0" value="What? Did we win?"/>
	<entry name="AstraMilitarum/Bullgryn:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:WireWeed#0" value="Ow, me leg!"/>
	<entry name="AstraMilitarum/Bullgryn:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:AlliedFaction:SpaceMarines#0" value="Praise the Emperor, the Astartes are here!"/>
	<entry name="AstraMilitarum/Guardsman:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Chests out, bellies in lads, it's the Lord Commissar."/>
	<entry name="AstraMilitarum/Guardsman:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Artefact#0" value="Was that made by… Xenos?"/>
	<entry name="AstraMilitarum/Guardsman:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Attack#0" value="Can anyone see what we're shooting at?"/>
	<entry name="AstraMilitarum/Guardsman:Attack#1" value="Enemies in front, commissar at our back…"/>
	<entry name="AstraMilitarum/Guardsman:Attack#2" value="Give it everything you've got!"/>
	<entry name="AstraMilitarum/Guardsman:Attack#3" value="I wish Creed was here…"/>
	<entry name="AstraMilitarum/Guardsman:Attack#4" value="Pour on the las-fire, we might just make it."/>
	<entry name="AstraMilitarum/Guardsman:AttackCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Broken#0" value="I didn't sign up for this!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#1" value="Run lads, and hope the Commissar ain't watching!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#2" value="They're killing us! And we can't kill them back!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#3" value="This is a bloody Death World!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#4" value="Where are the reinforcements? Where are-!"/>
	<entry name="AstraMilitarum/Guardsman:BrokenCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Cover#0" value="Back in the trenches, thank the Emperor."/>
	<entry name="AstraMilitarum/Guardsman:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/Enslaver#0" value="Enslavers? Oh, Emperor preserve us."/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/Psychneuein#0" value="Big bugs from Prospero—keep them away from the psykers!"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnitNeutral/KhorneLordOfSkulls#0" value="Chaos! Here?"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnitNeutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Hurt#0" value="We're done for, sir."/>
	<entry name="AstraMilitarum/Guardsman:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Idle#0" value="By the Golden Throne…!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#1" value="For Guilliman and the Emperor!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#2" value="How high, sir?"/>
	<entry name="AstraMilitarum/Guardsman:Idle#3" value="It's the silence before the battle that's the worst."/>
	<entry name="AstraMilitarum/Guardsman:Idle#4" value="Just playing games, sir, Commissar says it's good for morale."/>
	<entry name="AstraMilitarum/Guardsman:Idle#5" value="Life in the guard, sir. Terribly hard."/>
	<entry name="AstraMilitarum/Guardsman:Idle#6" value="Receiving you loud and clear, sir!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#7" value="Sir, yes, sir!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#8" value="Watch out, the Commissar's coming!"/>
	<entry name="AstraMilitarum/Guardsman:IdleCount" value="9"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory0#0" value="I still can't believe we survived. So many…"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory0Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory1#0" value="Getting the gang back together, sir."/>
	<entry name="AstraMilitarum/Guardsman:QuestStory1Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory2#0" value="A new city? Are we sure—no, sir, not questioning your orders, sir!"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory2Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory3#0" value="Those storms make my head hurt—will we be trapped here forever?"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory3Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory4#0" value="They're inside the city! Get back!"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory4Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory5#0" value="My mum always said, 'never trust a tech-priest'. She was alright, my mum."/>
	<entry name="AstraMilitarum/Guardsman:QuestStory5Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#0" value="Can we pray? Oh, Emperor, oh Guilliman, preserve us."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#1" value="Commander, we're falling back."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#2" value="Courage, lads, courage."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#3" value="If we stay, the enemy will shoot us. If we run, the Commissar will shoot us!"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#4" value="Can we get better guns, please?"/>
	<entry name="AstraMilitarum/Guardsman:ShakenCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Victory#0" value="Commissar, sir, permission to celebrate?"/>
	<entry name="AstraMilitarum/Guardsman:Victory#1" value="Ha! I feel like we took down a Titan with a lightstick."/>
	<entry name="AstraMilitarum/Guardsman:Victory#2" value="To the memory of Cadia and to Creed…"/>
	<entry name="AstraMilitarum/Guardsman:Victory#3" value="We're winning..? We're winning!"/>
	<entry name="AstraMilitarum/Guardsman:Victory#4" value="Wow, the Primer is right! These xenos are easy!"/>
	<entry name="AstraMilitarum/Guardsman:VictoryCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:WireWeed#0" value="Sir, the wire, it's alive!"/>
	<entry name="AstraMilitarum/Guardsman:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:AlliedFaction:SpaceMarines#0" value="Commander, HQ has sighted the allied Space Marines."/>
	<entry name="AstraMilitarum/Headquarters:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Happy to see you out in the field, Sir."/>
	<entry name="AstraMilitarum/Headquarters:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Artefact#0" value="Xenos artefact dangerously near to the city, sir."/>
	<entry name="AstraMilitarum/Headquarters:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Attack#0" value="Maintaining territorial control."/>
	<entry name="AstraMilitarum/Headquarters:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Broken#0" value="Last stand orders issued."/>
	<entry name="AstraMilitarum/Headquarters:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Cover#0" value="How… is a city… in cover?"/>
	<entry name="AstraMilitarum/Headquarters:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:AstraMilitarum#0" value="Traitors approaching the city, sir!"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:Necrons#0" value="Necrons troops on the approach."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:Orks#0" value="Greenskins uncomfortably close, Sir."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:SpaceMarines#0" value="Space Marines approaching. They'll wipe the city out if they get inside the walls."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyUnit:Neutral/Enslaver#0" value="WARNING: EXTREMELY DANGEROUS XENOS DETECTED."/>
	<entry name="AstraMilitarum/Headquarters:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Hurt#0" value="Infrastructure damage sustained!"/>
	<entry name="AstraMilitarum/Headquarters:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Idle#0" value="Guards on shift."/>
	<entry name="AstraMilitarum/Headquarters:Idle#1" value="Always watchful."/>
	<entry name="AstraMilitarum/Headquarters:Idle#2" value="Who's been playing Tarot? Put it away."/>
	<entry name="AstraMilitarum/Headquarters:Idle#3" value="All quiet, sir."/>
	<entry name="AstraMilitarum/Headquarters:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Headquarters:Shaken#0" value="Pulling back to defensive positions, sir."/>
	<entry name="AstraMilitarum/Headquarters:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Victory#0" value="City secure, sir."/>
	<entry name="AstraMilitarum/Headquarters:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:WireWeed#0" value="Was building this city on bio-wire such a great idea?"/>
	<entry name="AstraMilitarum/Headquarters:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedFaction:SpaceMarines#0" value="Good of the Astartes to turn up."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Where do Commissars get their lunch? At the commissary. Heh."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Artefact#0" value="If we can't shoot that thing, I'm not interested."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#0" value="Can anyone see if the target's still there?"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#1" value="Throne, the recoil!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#2" value="Heavies firing!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#3" value="Let's crack 'em open!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#4" value="When lasguns don't cut it…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AttackCount" value="5"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Broken#0" value="Fall back, you hounds, fall back!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Cover#0" value="Like they say on Krieg: keep your mortars in cover, and they'll cover you."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:CoverCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:AstraMilitarum#0" value="Damn traitors. Light 'em up!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:Necrons#0" value="Let's peel their metal hides off 'em."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:Orks#0" value="Firing at Greenskins is a waste of ammo."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:SpaceMarines#0" value="If you have to fire at a Space Marine, make sure you hit."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyUnit:Neutral/Enslaver#0" value="Blow those hellspawn back to the warp!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Hurt#0" value="I ain't got time to bleed."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:HurtCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#0" value="Do you know how long it takes to fix bayonets on lascannons?"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#1" value="The Xenos can come to us."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#2" value="There's something waiting for us out there…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#3" value="We're just like a tank that can actually hit the target."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:IdleCount" value="4"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Shaken#0" value="We're not meant to be under fire, Sir."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Victory#0" value="Dead, of course."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:WireWeed#0" value="Filthy Xenos traps."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:AlliedFaction:SpaceMarines#0" value="The angels are with us."/>
	<entry name="AstraMilitarum/Hydra:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:AlliedUnit:AstraMilitarum/LordCommissar#0" value="It is useful to have a man to keep us grounded."/>
	<entry name="AstraMilitarum/Hydra:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Artefact#0" value="Something alien."/>
	<entry name="AstraMilitarum/Hydra:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Attack#0" value="Keeping the skies clear."/>
	<entry name="AstraMilitarum/Hydra:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Broken#0" value="Retreating to higher ground."/>
	<entry name="AstraMilitarum/Hydra:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Cover#0" value="Perfect cover for cloud-watching."/>
	<entry name="AstraMilitarum/Hydra:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:AstraMilitarum#0" value="Imperials, but with clouded minds."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:Necrons#0" value="Such strange flying machines, so deadly."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:Orks#0" value="Greenskin fliers are crude, but effective."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:SpaceMarines#0" value="Can I shoot down Thunderhawks? Let's find out!"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyUnit:Neutral/Enslaver#0" value="Floating isn't flying, you alien horrors."/>
	<entry name="AstraMilitarum/Hydra:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Hurt#0" value="We watch over the skies, you're meant to watch over us."/>
	<entry name="AstraMilitarum/Hydra:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Idle#0" value="Cloudwatching, sir."/>
	<entry name="AstraMilitarum/Hydra:Idle#1" value="Jaghatai nimbus, rogal stratus, cirrosanguinius…"/>
	<entry name="AstraMilitarum/Hydra:Idle#2" value="The stars are amazing here."/>
	<entry name="AstraMilitarum/Hydra:Idle#3" value="This planet has the most amazing sunsets. Sir."/>
	<entry name="AstraMilitarum/Hydra:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Hydra:Shaken#0" value="Someone's bringing us down, sir."/>
	<entry name="AstraMilitarum/Hydra:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Victory#0" value="Clear skies, sir."/>
	<entry name="AstraMilitarum/Hydra:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:WireWeed#0" value="Earthly entanglements, sir."/>
	<entry name="AstraMilitarum/Hydra:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedFaction:SpaceMarines#0" value="Strongpoint reporting Adeptus Astartes on the perimeter—friendlies."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/Baneblade#0" value="Thrill to see a Baneblade, sir. Especially when the guns are pointed the other way."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/BanebladeCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Strongpoint holding the line, sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Artefact#0" value="Strongpoint reporting Xenos artefact in close proximity."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Attack#0" value="Strongpoint deploying military assets."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AttackCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Broken#0" value="Strongpoint is lost, repeat, Strongpoint is lost."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:AstraMilitarum#0" value="Strongpoint reporting traitor movement on the peripherary."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:Necrons#0" value="Strongpoint reporting Necrons are on the move."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:Orks#0" value="Strongpoint reporting Ork presence."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:SpaceMarines#0" value="Strongpoint reporting… Emperor preserve us… enemy Adeptus Astartes."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyUnit:Neutral/Enslaver#0" value="Strongpoint reporting Xenos—Enslavers, sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Hurt#0" value="Strongpoint reporting—not sure if we can survive this, sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:HurtCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#0" value="Guard on watch, sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#1" value="Always watchful."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#2" value="Strongpoint reporting—nothing to report."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#3" value="All quiet, sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:IdleCount" value="4"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Shaken#0" value="Sir, can you hear us? We're under fire."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Victory#0" value="Strongpoint is holding, sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:WireWeed#0" value="Bio-wire is infesting the command center, sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedFaction:SpaceMarines#0" value="The Marines have joined the party! Good of 'em."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Another noble footslogger. Why did only Yarrick get his own Baneblade?"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Artefact#0" value="Guarding the artefact. Our pleasure."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Attack#0" value="Sending some lovely shells their way, sir."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AttackCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Broken#0" value="Sadly, sir, we're pulling out."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Cover#0" value="In the trees, setting an ambuscade, sir."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:CoverCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:AstraMilitarum#0" value="We'll be rolling the bones of heretics before the day is done.."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:Necrons#0" value="Ah, exactly our favourite targets—Necrons."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:Orks#0" value="Prepared to self-destruct—Orks love to borrow Leman Russ tanks."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:SpaceMarines#0" value="This cannon is designed to make Marines weep."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyUnit:Neutral/Enslaver#0" value="Enemy Xenos—keep them at range."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Hurt#0" value="Wow, that one went straight through the plating!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:HurtCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#0" value="Could Leman Russ even drive?"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#1" value="Making tracks."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#2" value="Tanking."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#3" value="Wishing Pask was driving."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:IdleCount" value="4"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Shaken#0" value="Tank shocked!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Victory#0" value="Another victory for the galaxy's most popular tank!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:WireWeed#0" value="Bio-wire, the only thing more common than the Leman Russ."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedFaction:SpaceMarines#0" value="It is always welcome to see loyal Astartes… if surprising."/>
	<entry name="AstraMilitarum/LordCommissar:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Ah, a comrade in arms."/>
	<entry name="AstraMilitarum/LordCommissar:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Artefact#0" value="Xenos filth. We should burn it."/>
	<entry name="AstraMilitarum/LordCommissar:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Attack#0" value="Shoot them or I'll shoot you!"/>
	<entry name="AstraMilitarum/LordCommissar:AttackCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Broken#0" value="I can't let the men see me like this!"/>
	<entry name="AstraMilitarum/LordCommissar:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Cover#0" value="Tactically sound to hide, but not great for morale."/>
	<entry name="AstraMilitarum/LordCommissar:CoverCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:AstraMilitarum#0" value="Traitors! Filth! YOU SHALL FACE THE EMPEROR'S JUSTICE!"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:Necrons#0" value="Tin filth. Let's melt them down for more bullets."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:Orks#0" value="Greenskin thugs. Imperial rigour should make swift work of them."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:SpaceMarines#0" value="Renegade dregs of our Emperor's proudest creation. We must wipe them from the galaxy."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyUnit:Neutral/Enslaver#0" value="Xenos horrors, in my head!"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Hurt#0" value="It's only a flesh wound."/>
	<entry name="AstraMilitarum/LordCommissar:HurtCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#0" value="Dispensing justice is so tiring."/>
	<entry name="AstraMilitarum/LordCommissar:Idle#1" value="Idle hands make ill work"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#2" value="Rations are halved. That's why I look a little Gaunt."/>
	<entry name="AstraMilitarum/LordCommissar:Idle#3" value="What would Cain do?"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#4" value="Adjutant! Clean this again!"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#5" value="Do your duty in the name of- Oh, I always forget this bit."/>
	<entry name="AstraMilitarum/LordCommissar:IdleCount" value="6"/>
	<entry name="AstraMilitarum/LordCommissar:Shaken#0" value="A spiritu dominatus, Domine, libra nos."/>
	<entry name="AstraMilitarum/LordCommissar:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Victory#0" value="Setting an example to my troops."/>
	<entry name="AstraMilitarum/LordCommissar:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:WireWeed#0" value="It would be foolhardy to remain in the bio-wire, however brave."/>
	<entry name="AstraMilitarum/LordCommissar:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedFaction:SpaceMarines#0" value="Some Astartes down there. Friendlies."/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedUnit:AstraMilitarum/Thunderbolt#0" value="Nice to have fighter support."/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedUnit:AstraMilitarum/ThunderboltCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Artefact#0" value="High value target sighted."/>
	<entry name="AstraMilitarum/MarauderBomber:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#0" value="Bombs away."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#1" value="If you can see 'em, we can hit 'em."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#2" value="Opening bomb bay doors."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#3" value="Anything under the bombsight is fair game."/>
	<entry name="AstraMilitarum/MarauderBomber:AttackCount" value="4"/>
	<entry name="AstraMilitarum/MarauderBomber:Broken#0" value="We're going down!"/>
	<entry name="AstraMilitarum/MarauderBomber:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Cover#0" value="If we was flying any lower, why we'd need bells on this thing."/>
	<entry name="AstraMilitarum/MarauderBomber:CoverCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Hurt#0" value="We got three engines out, we got more holes in us than a trader's grox, the vox-caster is gone and we're leaking fuel…"/>
	<entry name="AstraMilitarum/MarauderBomber:HurtCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#0" value="I ain't much of a hand at makin' speeches"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#1" value="I swear, Carpenter, these bombs talks back."/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#2" value="Does anyone know how to land this thing?"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#3" value="Practisin manoov-res. Like the Primer says."/>
	<entry name="AstraMilitarum/MarauderBomber:IdleCount" value="4"/>
	<entry name="AstraMilitarum/MarauderBomber:Shaken#0" value="Moving to higher altitudes—flak poppin all round us."/>
	<entry name="AstraMilitarum/MarauderBomber:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Victory#0" value="What's the nearest target opportunity?"/>
	<entry name="AstraMilitarum/MarauderBomber:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedFaction:SpaceMarines#0" value="I sense… great good from these."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedUnit:AstraMilitarum/LordCommissar#0" value="A mind twisted to absolutism… fascinating."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Artefact#0" value="Old bones built these little toys."/>
	<entry name="AstraMilitarum/PrimarisPsyker:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#0" value="Your minds will burn…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#1" value="I feel the Emperor's eyes on me."/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#2" value="I burn you in His name."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AttackCount" value="3"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Broken#0" value="His light, where is His light?"/>
	<entry name="AstraMilitarum/PrimarisPsyker:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Cover#0" value="The hidden mind is the wise mind."/>
	<entry name="AstraMilitarum/PrimarisPsyker:CoverCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:AstraMilitarum#0" value="We are of two minds, it appears."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:Necrons#0" value="I can feel nothing from them. Nothing."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:Orks#0" value="A swell of-, such fierce loving emotion."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:SpaceMarines#0" value="Such steely surety… on the surface."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyUnit:Neutral/Enslaver#0" value="I feel its hunger, its desire for the fecundity of our mind"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Hurt#0" value="It is mere flesh."/>
	<entry name="AstraMilitarum/PrimarisPsyker:HurtCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#0" value="Even through these storms, I still glimpse His light."/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#1" value="I know what you're thinking…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#2" value="Idle work for idle minds"/>
	<entry name="AstraMilitarum/PrimarisPsyker:IdleCount" value="3"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Shaken#0" value="Better to die here than on the Black Ships."/>
	<entry name="AstraMilitarum/PrimarisPsyker:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Victory#0" value="As the Emperor's Tarot foresaw."/>
	<entry name="AstraMilitarum/PrimarisPsyker:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:WireWeed#0" value="There is a life here, strange and bloody though it seems."/>
	<entry name="AstraMilitarum/PrimarisPsyker:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedFaction:SpaceMarines#0" value="Big shoulder pads, it's the Marines!"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Can I get back to the frontlines please?"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Artefact#0" value="Whoah, you should see this!"/>
	<entry name="AstraMilitarum/ScoutSentinel:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Attack#0" value="Unloading everything I've got!"/>
	<entry name="AstraMilitarum/ScoutSentinel:AttackCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Broken#0" value="Long legs a-running!"/>
	<entry name="AstraMilitarum/ScoutSentinel:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Cover#0" value="Hiding behind a tree or something."/>
	<entry name="AstraMilitarum/ScoutSentinel:CoverCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:AstraMilitarum#0" value="Damn, it's us, but badddd."/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:Necrons#0" value="Tin men, ahoy!"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:Orks#0" value="I see some red eyes, checking me out for scrap!"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:SpaceMarines#0" value="Ah, what? We're fighting them? Are we the baddies then?"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyUnit:Neutral/Enslaver#0" value="Uh… what on Terra is that?"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Hurt#0" value="Huh, someone cut me a new skylight."/>
	<entry name="AstraMilitarum/ScoutSentinel:HurtCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#0" value="Shouldn't I be, y'know, doing something?"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#1" value="These legs go all the way up."/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#2" value="Imagine what's out there, waiting to be seen!"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#3" value="Let's get going!"/>
	<entry name="AstraMilitarum/ScoutSentinel:IdleCount" value="4"/>
	<entry name="AstraMilitarum/ScoutSentinel:Shaken#0" value="Get me out of here!"/>
	<entry name="AstraMilitarum/ScoutSentinel:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Victory#0" value="Always knew we were gonna win. Next!"/>
	<entry name="AstraMilitarum/ScoutSentinel:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:WireWeed#0" value="Move me fast, this stuff is getting into the legs."/>
	<entry name="AstraMilitarum/ScoutSentinel:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:AlliedFaction:SpaceMarines#0" value="If they don't have land raiders, I'm out."/>
	<entry name="AstraMilitarum/TankCommander:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:AlliedUnit:AstraMilitarum/Baneblade#0" value="Ohhhh… such a thing of beauty."/>
	<entry name="AstraMilitarum/TankCommander:AlliedUnit:AstraMilitarum/BanebladeCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Artefact#0" value="If it's not enemy armour, I'm not interested."/>
	<entry name="AstraMilitarum/TankCommander:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Attack#0" value="Boom! Hahaha!"/>
	<entry name="AstraMilitarum/TankCommander:Attack#1" value="Drive me closer! You know why…"/>
	<entry name="AstraMilitarum/TankCommander:Attack#2" value="Fire the gun! And the other one! Fire everything!"/>
	<entry name="AstraMilitarum/TankCommander:Attack#3" value="Fire! Hahaha!"/>
	<entry name="AstraMilitarum/TankCommander:AttackCount" value="4"/>
	<entry name="AstraMilitarum/TankCommander:Broken#0" value="No, don't go backwards! Who's in charge here?"/>
	<entry name="AstraMilitarum/TankCommander:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Cover#0" value="An ambuscade, I like it."/>
	<entry name="AstraMilitarum/TankCommander:CoverCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:AstraMilitarum#0" value="Traitors! Heretics… do they have any tanks?"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:Necrons#0" value="Monoliths, now there's a challenge."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:Orks#0" value="Eh, we should have brought the Demolisher."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:SpaceMarines#0" value="Traitors! They probably don't know the which is the front end of a Rhino."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyUnit:Neutral/KastelanRobot#0" value="Oh, they don't make armour like that anymore."/>
	<entry name="AstraMilitarum/TankCommander:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Hurt#0" value="Keeping them busy by letting them shoot holes in me."/>
	<entry name="AstraMilitarum/TankCommander:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Idle#0" value="Better a tread-head than a dead-head. Ha!"/>
	<entry name="AstraMilitarum/TankCommander:Idle#1" value="Just polishing my medals."/>
	<entry name="AstraMilitarum/TankCommander:Idle#2" value="What do you mean, get inside? I need to see."/>
	<entry name="AstraMilitarum/TankCommander:Idle#3" value="Getting itchy for action."/>
	<entry name="AstraMilitarum/TankCommander:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TankCommander:Shaken#0" value="Not sure the old lady's can take many more of those."/>
	<entry name="AstraMilitarum/TankCommander:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Victory#0" value="The best place for an enemy is under your tracks."/>
	<entry name="AstraMilitarum/TankCommander:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:WireWeed#0" value="Hey, that wire's scratching the paintwork!"/>
	<entry name="AstraMilitarum/TankCommander:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Glad to be beyond that fanatic's jurisdiction."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Artefact#0" value="Dismantling tempting."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Attack#0" value="In combat."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AttackCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Broken#0" value="Flesh is weak, must retreat."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Cover#0" value="Enemy balistic hit likelihood heavily reduced."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:CoverCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:AstraMilitarum#0" value="Annoyances."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:Necrons#0" value="The forms of true Machine God devotees, with the souls of foul Xenos."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:Orks#0" value="Primitives. Keep them away from our sacred machines."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:SpaceMarines#0" value="A pity."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/KastelanRobot#0" value="A sacred relic! Minimise damage to it."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/TechpriestBetrayer#0" value="A heretek? Here. Unlikely."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/TechpriestBetrayerCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Hurt#0" value="Repairs needed."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#0" value="Applying sacred unguents."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#1" value="Checking cogitators."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#2" value="Performing machine rites."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#3" value="Revitalising machine spirits."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Shaken#0" value="The Omnissiah is with me."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Victory#0" value="Predictable."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:WireWeed#0" value="Biowire is such a joy. We must replicate it."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion" value="AstraMilitarum/Guardsman"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedFaction:SpaceMarines#0" value="Something shiny down there!"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedUnit:AstraMilitarum/MarauderBomber#0" value="Us flyboys need to stick together!"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedUnit:AstraMilitarum/MarauderBomberCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Artefact#0" value="Did anyone see what that was? Wow!"/>
	<entry name="AstraMilitarum/Thunderbolt:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Attack#0" value="Short bursts only! Conserve ammo!"/>
	<entry name="AstraMilitarum/Thunderbolt:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Broken#0" value="Heading home."/>
	<entry name="AstraMilitarum/Thunderbolt:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Cover#0" value="Cloud cover only."/>
	<entry name="AstraMilitarum/Thunderbolt:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:AstraMilitarum#0" value="Traitor guard. A target-rich environment."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:Orks#0" value="Orks. Ain't happy less they're landing too fast with their head on fire."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:SpaceMarines#0" value="Marines. Spend more time decorating their armour than fighting."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyUnit:Neutral/Psychneuein#0" value="Anyone fancy buzzing a big bee?"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Hurt#0" value="Hnn, no points for second place."/>
	<entry name="AstraMilitarum/Thunderbolt:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#0" value="I feel the desire, to fire."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#1" value="No time to think up here."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#2" value="This planet's just a walk in the park."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#3" value="You can be my wingman, any time."/>
	<entry name="AstraMilitarum/Thunderbolt:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Thunderbolt:Shaken#0" value="Let's turn and burn!"/>
	<entry name="AstraMilitarum/Thunderbolt:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Victory#0" value="Victory takes a lot more than fancy flying."/>
	<entry name="AstraMilitarum/Thunderbolt:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Artefact#0" value="No, seriously, I can't pick that up."/>
	<entry name="AstraMilitarum/Valkyrie:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Attack#0" value="Extracting under fire."/>
	<entry name="AstraMilitarum/Valkyrie:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Broken#0" value="We're meant to be in and out fast!"/>
	<entry name="AstraMilitarum/Valkyrie:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:AstraMilitarum#0" value="Grunts in the field."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:Necrons#0" value="Tin men coming up fast."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:Orks#0" value="Greenskins in the LZ"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:SpaceMarines#0" value="I never signed up to fight Marines!"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Hurt#0" value="It's hot here, repeat hot!"/>
	<entry name="AstraMilitarum/Valkyrie:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#0" value="Can we requisition speakers?"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#1" value="Damn, where's the action at?"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#2" value="It's the quiet that kills you."/>
	<entry name="AstraMilitarum/Valkyrie:Idle#3" value="We don't stop until we drop."/>
	<entry name="AstraMilitarum/Valkyrie:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Valkyrie:Shaken#0" value="We're not frontline! Get us out!"/>
	<entry name="AstraMilitarum/Valkyrie:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Victory#0" value="Enemy down, extraction please."/>
	<entry name="AstraMilitarum/Valkyrie:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedFaction:SpaceMarines#0" value="We feel… reassured."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedUnit:AstraMilitarum/PrimarisPsyker#0" value="Our brother from the Black Ships…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedUnit:AstraMilitarum/PrimarisPsykerCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Artefact#0" value="Unholy of holies."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#0" value="Accelerando now, together we tear them."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#1" value="Here is a codetta for their sad lives."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#2" value="And, as one, we caper."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#3" value="A simple Soul Storm to sing them apart."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AttackCount" value="4"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Broken#0" value="We are sundered, we are lost."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Cover#0" value="Shielded from the eye, not the mind."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:CoverCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:AstraMilitarum#0" value="Kith and kin, but lost to sin."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:Necrons#0" value="Nothing, there is nothing there."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:Orks#0" value="Another collective will… alien, though."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:SpaceMarines#0" value="The Emperor's own misbegotten sons…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyUnit:Neutral/Psychneuein#0" value="The avatars of fecundity, waiting to birth more horrors."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Hurt#0" value="Dissonance comes, we drift apart."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:HurtCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#0" value="Just beyond sight, they are. The warp."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#1" value="So far from His light."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#2" value="We survived the Black Ships, we can survive this."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#3" value="The planet whispers to us."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:IdleCount" value="4"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Shaken#0" value="Our harmony collapses, our requiem nears."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Victory#0" value="What elegy for those who die as cattle?"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:WireWeed#0" value="Silver serpents entwine us, prick us."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:WireWeedCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Attack#0" value="Combat protocol"/>
	<entry name="Necrons/CanoptekScarab:AttackCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Broken#0" value="Damage control"/>
	<entry name="Necrons/CanoptekScarab:BrokenCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Hurt#0" value="Damage sustained."/>
	<entry name="Necrons/CanoptekScarab:HurtCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Idle#0" value="Maintenance protocol."/>
	<entry name="Necrons/CanoptekScarab:Idle#1" value="Minimising power consumption."/>
	<entry name="Necrons/CanoptekScarab:Idle#2" value="Tending the damaged."/>
	<entry name="Necrons/CanoptekScarab:Idle#3" value="We have waited aeons. We will wait… more."/>
	<entry name="Necrons/CanoptekScarab:IdleCount" value="4"/>
	<entry name="Necrons/CanoptekScarab:Shaken#0" value="Evasion advised."/>
	<entry name="Necrons/CanoptekScarab:ShakenCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Victory#0" value="Invader deterred."/>
	<entry name="Necrons/CanoptekScarab:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekSpyder" value="Necrons/CanoptekScarab"/>
	<entry name="Necrons/Cryptek:Attack#0" value="Die, cattle."/>
	<entry name="Necrons/Cryptek:Attack#1" value="Physics itself betrays you."/>
	<entry name="Necrons/Cryptek:Attack#2" value="You will never understand what killed you."/>
	<entry name="Necrons/Cryptek:AttackCount" value="3"/>
	<entry name="Necrons/Cryptek:Broken#0" value="Aeons of learning, and I have at last learned to fear."/>
	<entry name="Necrons/Cryptek:BrokenCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:AstraMilitarum#0" value="Slave races of the Old enemy."/>
	<entry name="Necrons/Cryptek:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:Necrons#0" value="Another court? Perhaps they need my services…"/>
	<entry name="Necrons/Cryptek:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:Orks#0" value="Flesh, weaponised. Admirable."/>
	<entry name="Necrons/Cryptek:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:SpaceMarines#0" value="Self-improved slaves. Dissection might teach us much."/>
	<entry name="Necrons/Cryptek:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Cryptek:Hurt#0" value="Fascinating, they've managed to injure me."/>
	<entry name="Necrons/Cryptek:HurtCount" value="1"/>
	<entry name="Necrons/Cryptek:Idle#0" value="Dissection or vivisection? Ah, an eternal question."/>
	<entry name="Necrons/Cryptek:Idle#1" value="Do you remember… dreams? I do."/>
	<entry name="Necrons/Cryptek:Idle#2" value="I will split atoms until I am needed."/>
	<entry name="Necrons/Cryptek:Idle#3" value="So much to learn, yet here I sit."/>
	<entry name="Necrons/Cryptek:IdleCount" value="4"/>
	<entry name="Necrons/Cryptek:Shaken#0" value="Perhaps there is something to the Old One's creations."/>
	<entry name="Necrons/Cryptek:ShakenCount" value="1"/>
	<entry name="Necrons/Cryptek:Victory#0" value="And so ends all life that challenges us."/>
	<entry name="Necrons/Cryptek:VictoryCount" value="1"/>
	<entry name="Necrons/DestroyerLord" value="Necrons/Lord"/>
	<entry name="Necrons/DoomScythe:Attack#0" value="Descending on the chattels."/>
	<entry name="Necrons/DoomScythe:AttackCount" value="1"/>
	<entry name="Necrons/DoomScythe:Broken#0" value="We must depart."/>
	<entry name="Necrons/DoomScythe:BrokenCount" value="1"/>
	<entry name="Necrons/DoomScythe:Hurt#0" value="They… dare?"/>
	<entry name="Necrons/DoomScythe:HurtCount" value="1"/>
	<entry name="Necrons/DoomScythe:Idle#0" value="Circling."/>
	<entry name="Necrons/DoomScythe:Idle#1" value="Manouevres."/>
	<entry name="Necrons/DoomScythe:Idle#2" value="Watching horizon."/>
	<entry name="Necrons/DoomScythe:Idle#3" value="Focused."/>
	<entry name="Necrons/DoomScythe:IdleCount" value="4"/>
	<entry name="Necrons/DoomScythe:Shaken#0" value="Damaged?"/>
	<entry name="Necrons/DoomScythe:ShakenCount" value="1"/>
	<entry name="Necrons/DoomScythe:Victory#0" value="Humbling our foes."/>
	<entry name="Necrons/DoomScythe:VictoryCount" value="1"/>
	<entry name="Necrons/DoomsdayArk" value="Necrons/AnnihilationBarge"/>
	<entry name="Necrons/Headquarters" value="Necrons/Monolith"/>
	<entry name="Necrons/HeavyDestroyer:Attack#0" value="Life is our foe!"/>
	<entry name="Necrons/HeavyDestroyer:AttackCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Broken#0" value="Death is our victory!"/>
	<entry name="Necrons/HeavyDestroyer:BrokenCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:AstraMilitarum#0" value="Crush the weak flesh!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:Necrons#0" value="Ourselves, a proud foe!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:Orks#0" value="Purge the galaxy of the fungal filth!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:SpaceMarines#0" value="Pawns of the God-Emperor!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Hurt#0" value="Pain drives us on!"/>
	<entry name="Necrons/HeavyDestroyer:HurtCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Idle#0" value="We are nihilists!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#1" value="We hunger!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#2" value="We must improve, to destroy!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#3" value="Why are we still? Why do we not kill?!"/>
	<entry name="Necrons/HeavyDestroyer:IdleCount" value="4"/>
	<entry name="Necrons/HeavyDestroyer:Shaken#0" value="Why do they not die faster?"/>
	<entry name="Necrons/HeavyDestroyer:ShakenCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Victory#0" value="They die! It is not enough!"/>
	<entry name="Necrons/HeavyDestroyer:VictoryCount" value="1"/>
	<entry name="Necrons/Immortal" value="Necrons/Warrior"/>
	<entry name="Necrons/Lord:Attack#0" value="Feel the undying force of the Necrons!"/>
	<entry name="Necrons/Lord:Attack#1" value="Perish proudly at my hands."/>
	<entry name="Necrons/Lord:Attack#2" value="Who dares challenge me?"/>
	<entry name="Necrons/Lord:Attack#3" value="You will learn obedience!"/>
	<entry name="Necrons/Lord:AttackCount" value="4"/>
	<entry name="Necrons/Lord:Broken#0" value="Where are my Immortals? My guards?"/>
	<entry name="Necrons/Lord:BrokenCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:AstraMilitarum#0" value="Mayflies, destined to live but a day."/>
	<entry name="Necrons/Lord:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:Necrons#0" value="Iniquity! Rivals, on my Crownworld?!"/>
	<entry name="Necrons/Lord:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:Orks#0" value="Such brutes! And, yet they rule my galaxy."/>
	<entry name="Necrons/Lord:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:SpaceMarines#0" value="So wise, so young. They will not live long."/>
	<entry name="Necrons/Lord:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Lord:Hurt#0" value="They have singed my cloak? Destroy them!"/>
	<entry name="Necrons/Lord:HurtCount" value="1"/>
	<entry name="Necrons/Lord:Idle#0" value="Conscience is a mere word, to keep the strong restrained."/>
	<entry name="Necrons/Lord:Idle#1" value="The farseers, I shall kill them last."/>
	<entry name="Necrons/Lord:Idle#2" value="What shall I do first, when we rule this world?"/>
	<entry name="Necrons/Lord:IdleCount" value="3"/>
	<entry name="Necrons/Lord:Shaken#0" value="Death can set no mark on me."/>
	<entry name="Necrons/Lord:Shaken#1" value="The Old Ones have trained them well."/>
	<entry name="Necrons/Lord:ShakenCount" value="2"/>
	<entry name="Necrons/Lord:Victory#0" value="Despair and die."/>
	<entry name="Necrons/Lord:VictoryCount" value="1"/>
	<entry name="Necrons/Monolith:Attack#0" value="Particle Whip deployed."/>
	<entry name="Necrons/Monolith:AttackCount" value="1"/>
	<entry name="Necrons/Monolith:Broken#0" value="Retreating… slowly."/>
	<entry name="Necrons/Monolith:BrokenCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:AstraMilitarum#0" value="Humans sighted."/>
	<entry name="Necrons/Monolith:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:Necrons#0" value="Enemy court on-planet."/>
	<entry name="Necrons/Monolith:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:Orks#0" value="Orks sighted."/>
	<entry name="Necrons/Monolith:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:SpaceMarines#0" value="Humans sighted. Augmented."/>
	<entry name="Necrons/Monolith:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Monolith:Hurt#0" value="Armour pierced, crew damaged."/>
	<entry name="Necrons/Monolith:HurtCount" value="1"/>
	<entry name="Necrons/Monolith:Idle#0" value="Channeling Eternity Gate."/>
	<entry name="Necrons/Monolith:Idle#1" value="Generating Inscrutable Secrets."/>
	<entry name="Necrons/Monolith:Idle#2" value="Decomposing Singular Values."/>
	<entry name="Necrons/Monolith:Idle#3" value="Reconstructing Fundamental Particles."/>
	<entry name="Necrons/Monolith:Idle#4" value="Debanking Ark Memories."/>
	<entry name="Necrons/Monolith:Idle#5" value="Reticulating Splines."/>
	<entry name="Necrons/Monolith:IdleCount" value="6"/>
	<entry name="Necrons/Monolith:Shaken#0" value="Under attack. Compromised."/>
	<entry name="Necrons/Monolith:ShakenCount" value="1"/>
	<entry name="Necrons/Monolith:Victory#0" value="Eliminated foe. As expected."/>
	<entry name="Necrons/Monolith:VictoryCount" value="1"/>
	<entry name="Necrons/NightScythe" value="Necrons/DoomScythe"/>
	<entry name="Necrons/Obelisk" value="Necrons/Monolith"/>
	<entry name="Necrons/TesseractVault:Attack#0" value="Feel the fury of a Star God!"/>
	<entry name="Necrons/TesseractVault:AttackCount" value="1"/>
	<entry name="Necrons/TesseractVault:Broken#0" value="I am almost free!"/>
	<entry name="Necrons/TesseractVault:BrokenCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:Necrons#0" value="Turn me against the Necrons? Foolish creatures."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:Orks#0" value="Contemptible beasts."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:SpaceMarines#0" value="Their lives are mine."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/TesseractVault:Hurt#0" value="My prison weakens…"/>
	<entry name="Necrons/TesseractVault:HurtCount" value="1"/>
	<entry name="Necrons/TesseractVault:Idle#0" value="Sentinels, scarabs, leeches… all this for me?"/>
	<entry name="Necrons/TesseractVault:Idle#1" value="Oh, set me free?"/>
	<entry name="Necrons/TesseractVault:Idle#2" value="This prison CANNOT HOLD ME."/>
	<entry name="Necrons/TesseractVault:Idle#3" value="I am so… diminished. I was a star god!"/>
	<entry name="Necrons/TesseractVault:Idle#4" value="I am a star god. Let me blow your mind."/>
	<entry name="Necrons/TesseractVault:IdleCount" value="5"/>
	<entry name="Necrons/TesseractVault:Shaken#0" value="Yes, destroy my prison…"/>
	<entry name="Necrons/TesseractVault:ShakenCount" value="1"/>
	<entry name="Necrons/TesseractVault:Victory#0" value="Not enough. Not until all life is mine."/>
	<entry name="Necrons/TesseractVault:VictoryCount" value="1"/>
	<entry name="Necrons/TombBlade:Attack#0" value="Assault at speed."/>
	<entry name="Necrons/TombBlade:AttackCount" value="1"/>
	<entry name="Necrons/TombBlade:Broken#0" value="Dispersed, falling back."/>
	<entry name="Necrons/TombBlade:BrokenCount" value="1"/>
	<entry name="Necrons/TombBlade:Hurt#0" value="Low-level fire, warriors down."/>
	<entry name="Necrons/TombBlade:HurtCount" value="1"/>
	<entry name="Necrons/TombBlade:Idle#0" value="Never still."/>
	<entry name="Necrons/TombBlade:Idle#1" value="Programmming attack patterns."/>
	<entry name="Necrons/TombBlade:Idle#2" value="Space… we remember."/>
	<entry name="Necrons/TombBlade:Idle#3" value="Testing dimensional repulsors."/>
	<entry name="Necrons/TombBlade:IdleCount" value="4"/>
	<entry name="Necrons/TombBlade:Shaken#0" value="Struck. Improbably, we have been struck."/>
	<entry name="Necrons/TombBlade:ShakenCount" value="1"/>
	<entry name="Necrons/TombBlade:Victory#0" value="Enemy… disintegrated."/>
	<entry name="Necrons/TombBlade:VictoryCount" value="1"/>
	<entry name="Necrons/TranscendentCtan" value="Necrons/TesseractVault"/>
	<entry name="Necrons/TriarchPraetorian:Attack#0" value="The first law is… do not stand against the Triarch."/>
	<entry name="Necrons/TriarchPraetorian:AttackCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Broken#0" value="The last law is… leave to fight again."/>
	<entry name="Necrons/TriarchPraetorian:BrokenCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:AstraMilitarum#0" value="Ah, primitives. We were their gods once."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:Necrons#0" value="Another court? They must be brought in line."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:Orks#0" value="Untrainable, savage."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:SpaceMarines#0" value="Honourable foes. Respectfully, we must kill them."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Hurt#0" value="We are the law!"/>
	<entry name="Necrons/TriarchPraetorian:HurtCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Idle#0" value="Would you know the codes of law?"/>
	<entry name="Necrons/TriarchPraetorian:Idle#1" value="The first code is of honour."/>
	<entry name="Necrons/TriarchPraetorian:Idle#2" value="The second code is of pride."/>
	<entry name="Necrons/TriarchPraetorian:Idle#3" value="The third code… shall I share this data?"/>
	<entry name="Necrons/TriarchPraetorian:Idle#4" value="The fifty-seventh code… obey all codes."/>
	<entry name="Necrons/TriarchPraetorian:IdleCount" value="5"/>
	<entry name="Necrons/TriarchPraetorian:Shaken#0" value="Resurrection is so… loss-laden."/>
	<entry name="Necrons/TriarchPraetorian:ShakenCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Victory#0" value="We take no pleasure in this."/>
	<entry name="Necrons/TriarchPraetorian:VictoryCount" value="1"/>
	<entry name="Necrons/TriarchStalker" value="Necrons/TriarchPraetorian"/>
	<entry name="Necrons/Warrior:Artefact#0" value="Old One horrors—on our Crownworld? How dare they?"/>
	<entry name="Necrons/Warrior:ArtefactCount" value="1"/>
	<entry name="Necrons/Warrior:Attack#0" value="Does it hurt, to have your nerves stripped, atom by atom?"/>
	<entry name="Necrons/Warrior:Attack#1" value="Engaging."/>
	<entry name="Necrons/Warrior:Attack#2" value="History shall forget them."/>
	<entry name="Necrons/Warrior:Attack#3" value="Resistance is… annoying."/>
	<entry name="Necrons/Warrior:AttackCount" value="4"/>
	<entry name="Necrons/Warrior:Broken#0" value="Damage simulations running at 99%."/>
	<entry name="Necrons/Warrior:Broken#1" value="Our unit is dispersed. We retreat."/>
	<entry name="Necrons/Warrior:Broken#2" value="Recall protocol initiated."/>
	<entry name="Necrons/Warrior:Broken#3" value="The tomb calls and we obey."/>
	<entry name="Necrons/Warrior:Broken#4" value="Your orders are impossible, my Lord."/>
	<entry name="Necrons/Warrior:BrokenCount" value="5"/>
	<entry name="Necrons/Warrior:Cover#0" value="Making use of cover."/>
	<entry name="Necrons/Warrior:CoverCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:AstraMilitarum#0" value="Weak, fleshy things but a fine slave race."/>
	<entry name="Necrons/Warrior:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:Necrons#0" value="Our cousins, in error."/>
	<entry name="Necrons/Warrior:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:Orks#0" value="Rogue bioweapon. Brutally effective."/>
	<entry name="Necrons/Warrior:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:SpaceMarines#0" value="Flesh, remade in our form. Not to be trifled with."/>
	<entry name="Necrons/Warrior:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/CatachanDevil#0" value="Organic, insectoid, gigantic. Possible Tyranid precursor?"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/Enslaver#0" value="An Old One side-effect? Sensors are only 30% effective."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KastelanRobot#0" value="Do the humans mock us? Such disappointing copies."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="Sensors failing! Unknown unknown unknown."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/Psychneuein#0" value="Megafauna. A threat only to organic races."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="Necrons/Warrior:Hurt#0" value="Damage exceeds expectations… recall protocols?"/>
	<entry name="Necrons/Warrior:HurtCount" value="1"/>
	<entry name="Necrons/Warrior:Idle#0" value="All systems are operational."/>
	<entry name="Necrons/Warrior:Idle#1" value="For all time… curse the Deceiver."/>
	<entry name="Necrons/Warrior:Idle#2" value="Idle protocol initiated."/>
	<entry name="Necrons/Warrior:Idle#3" value="Powering down."/>
	<entry name="Necrons/Warrior:Idle#4" value="This metal body… alien."/>
	<entry name="Necrons/Warrior:Idle#5" value="The machine is strong, the flesh is gone."/>
	<entry name="Necrons/Warrior:Idle#6" value="We have waited millenia… we will wait millenia more."/>
	<entry name="Necrons/Warrior:IdleCount" value="7"/>
	<entry name="Necrons/Warrior:QuestStory0#0" value="Watching for signs of the Olds Ones."/>
	<entry name="Necrons/Warrior:QuestStory0Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory1#0" value="Praetorians and Crypteks? Our forces grow."/>
	<entry name="Necrons/Warrior:QuestStory1Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory2#0" value="The mindless should be pitied the C'tan's curse."/>
	<entry name="Necrons/Warrior:QuestStory2Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory3#0" value="Chase down these Old One baubles? Yes… my lord."/>
	<entry name="Necrons/Warrior:QuestStory3Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory4#0" value="Where are the Praetorians?"/>
	<entry name="Necrons/Warrior:QuestStory4Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory5#0" value="Mephet'ran? Messenger! Deceiver! Stealer of souls!"/>
	<entry name="Necrons/Warrior:QuestStory5Count" value="1"/>
	<entry name="Necrons/Warrior:Shaken#0" value="A lesser race would flee."/>
	<entry name="Necrons/Warrior:Shaken#1" value="The Triarchs did not brief us on this."/>
	<entry name="Necrons/Warrior:Shaken#2" value="This is not possible. We are… losing?"/>
	<entry name="Necrons/Warrior:Shaken#3" value="We are beyond fear."/>
	<entry name="Necrons/Warrior:ShakenCount" value="4"/>
	<entry name="Necrons/Warrior:Victory#0" value="Acceptable losses. Their losses."/>
	<entry name="Necrons/Warrior:Victory#1" value="As predicted, we are victorious."/>
	<entry name="Necrons/Warrior:Victory#2" value="Eliminated."/>
	<entry name="Necrons/Warrior:Victory#3" value="For the Silent King and for my Lord."/>
	<entry name="Necrons/Warrior:Victory#4" value="My Lord, the enemy proves weak."/>
	<entry name="Necrons/Warrior:Victory#5" value="The Old Ones spawned such fragile flesh."/>
	<entry name="Necrons/Warrior:Victory#6" value="They shall know the Necrons and know fear."/>
	<entry name="Necrons/Warrior:Victory#7" value="To win so easily is… unsatisfying."/>
	<entry name="Necrons/Warrior:VictoryCount" value="8"/>
	<entry name="Neutral/Ambull:Attack#0" value="Roekoe!"/>
	<entry name="Neutral/Ambull:AttackCount" value="1"/>
	<entry name="Neutral/Ambull:Broken#0" value="Kurr."/>
	<entry name="Neutral/Ambull:BrokenCount" value="1"/>
	<entry name="Neutral/Ambull:Hurt#0" value="Rou rou."/>
	<entry name="Neutral/Ambull:HurtCount" value="1"/>
	<entry name="Neutral/Ambull:Idle#0" value="Guru Guru"/>
	<entry name="Neutral/Ambull:Idle#1" value="Grl Grl"/>
	<entry name="Neutral/Ambull:Idle#2" value="Curcuurucu"/>
	<entry name="Neutral/Ambull:Idle#3" value="Oo ho oo ho"/>
	<entry name="Neutral/Ambull:IdleCount" value="4"/>
	<entry name="Neutral/Ambull:Shaken#0" value="Burukk"/>
	<entry name="Neutral/Ambull:ShakenCount" value="1"/>
	<entry name="Neutral/Ambull:Victory#0" value="Uuu"/>
	<entry name="Neutral/Ambull:VictoryCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Attack#0" value="KYKYLIKY…"/>
	<entry name="Neutral/CatachanDevil:AttackCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Broken#0" value="KUKELEKU"/>
	<entry name="Neutral/CatachanDevil:BrokenCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Hurt#0" value="CHICHIRICHI"/>
	<entry name="Neutral/CatachanDevil:HurtCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Idle#0" value="ke-kok-o"/>
	<entry name="Neutral/CatachanDevil:Idle#1" value="kok-ko-o"/>
	<entry name="Neutral/CatachanDevil:Idle#2" value="ko-ko-o"/>
	<entry name="Neutral/CatachanDevil:Idle#3" value="ko-ke-koko"/>
	<entry name="Neutral/CatachanDevil:IdleCount" value="4"/>
	<entry name="Neutral/CatachanDevil:Shaken#0" value="Ch-ch-ch!"/>
	<entry name="Neutral/CatachanDevil:ShakenCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Victory#0" value="Kckeliku!"/>
	<entry name="Neutral/CatachanDevil:VictoryCount" value="1"/>
	<entry name="Neutral/Enslaver:Attack#0" value="Heiiii…"/>
	<entry name="Neutral/Enslaver:AttackCount" value="1"/>
	<entry name="Neutral/Enslaver:Broken#0" value="Oummm"/>
	<entry name="Neutral/Enslaver:BrokenCount" value="1"/>
	<entry name="Neutral/Enslaver:Hurt#0" value="Harrhm"/>
	<entry name="Neutral/Enslaver:HurtCount" value="1"/>
	<entry name="Neutral/Enslaver:Idle#0" value="HmmMmm."/>
	<entry name="Neutral/Enslaver:Idle#1" value="Mmmhmmm."/>
	<entry name="Neutral/Enslaver:Idle#2" value="Tct tct."/>
	<entry name="Neutral/Enslaver:Idle#3" value="Chhrrr."/>
	<entry name="Neutral/Enslaver:IdleCount" value="4"/>
	<entry name="Neutral/Enslaver:Shaken#0" value="Hrhrhrh"/>
	<entry name="Neutral/Enslaver:ShakenCount" value="1"/>
	<entry name="Neutral/Enslaver:Victory#0" value="mmmMMM!"/>
	<entry name="Neutral/Enslaver:VictoryCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Attack#0" value="Target mapped in, Datasmith"/>
	<entry name="Neutral/KastelanRobot:AttackCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Broken#0" value="Orders incomplete, following protocol."/>
	<entry name="Neutral/KastelanRobot:BrokenCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Hurt#0" value="Repairs require, Datasmith. Input?"/>
	<entry name="Neutral/KastelanRobot:HurtCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Idle#0" value="Input?"/>
	<entry name="Neutral/KastelanRobot:Idle#1" value="Data not found?"/>
	<entry name="Neutral/KastelanRobot:Idle#2" value="Idle protocols in 5,4,3-"/>
	<entry name="Neutral/KastelanRobot:Idle#3" value="Overwatch!"/>
	<entry name="Neutral/KastelanRobot:IdleCount" value="4"/>
	<entry name="Neutral/KastelanRobot:Shaken#0" value="Inconsistent data detected!"/>
	<entry name="Neutral/KastelanRobot:ShakenCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Victory#0" value="Target atomised. Within parameters."/>
	<entry name="Neutral/KastelanRobot:VictoryCount" value="1"/>
	<entry name="Neutral/KrootHound:Attack#0" value="Hoeea!"/>
	<entry name="Neutral/KrootHound:Attack#1" value="Hrrrr!"/>
	<entry name="Neutral/KrootHound:AttackCount" value="2"/>
	<entry name="Neutral/KrootHound:Broken#0" value="owoooo"/>
	<entry name="Neutral/KrootHound:BrokenCount" value="1"/>
	<entry name="Neutral/KrootHound:Hurt#0" value="uhuu"/>
	<entry name="Neutral/KrootHound:HurtCount" value="1"/>
	<entry name="Neutral/KrootHound:Idle#0" value="oou"/>
	<entry name="Neutral/KrootHound:Idle#1" value="cha-cha"/>
	<entry name="Neutral/KrootHound:Idle#2" value="oauh ouah"/>
	<entry name="Neutral/KrootHound:Idle#3" value="guf guf"/>
	<entry name="Neutral/KrootHound:IdleCount" value="4"/>
	<entry name="Neutral/KrootHound:Shaken#0" value="yu-yu-yuuu"/>
	<entry name="Neutral/KrootHound:ShakenCount" value="1"/>
	<entry name="Neutral/KrootHound:Victory#0" value="Oooahh!"/>
	<entry name="Neutral/KrootHound:VictoryCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Attack#0" value="BLOOD FOR THE BLOOD GOD"/>
	<entry name="Neutral/LordOfSkulls:AttackCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Broken#0" value="LORD, I FAIL YOU"/>
	<entry name="Neutral/LordOfSkulls:BrokenCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Hurt#0" value="MY BLOOD FOR MY GOD"/>
	<entry name="Neutral/LordOfSkulls:HurtCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Idle#0" value="TEAR REND DEATH"/>
	<entry name="Neutral/LordOfSkulls:Idle#1" value="KILL KILL KILL"/>
	<entry name="Neutral/LordOfSkulls:Idle#2" value="SKULLS SPINES SCREAMS"/>
	<entry name="Neutral/LordOfSkulls:Idle#3" value="DROWN IN BLOOD"/>
	<entry name="Neutral/LordOfSkulls:Idle#4" value="MUST… KILL"/>
	<entry name="Neutral/LordOfSkulls:IdleCount" value="5"/>
	<entry name="Neutral/LordOfSkulls:Shaken#0" value="I KNOW NO FEAR!"/>
	<entry name="Neutral/LordOfSkulls:ShakenCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Victory#0" value="BLOOD! FOR! KHORNE!"/>
	<entry name="Neutral/LordOfSkulls:VictoryCount" value="1"/>
	<entry name="Neutral/Psychneuein:Attack#0" value="Zzzz!"/>
	<entry name="Neutral/Psychneuein:AttackCount" value="1"/>
	<entry name="Neutral/Psychneuein:Broken#0" value="Zzzee!"/>
	<entry name="Neutral/Psychneuein:BrokenCount" value="1"/>
	<entry name="Neutral/Psychneuein:Hurt#0" value="Zzzaaa."/>
	<entry name="Neutral/Psychneuein:HurtCount" value="1"/>
	<entry name="Neutral/Psychneuein:Idle#0" value="Zzzm"/>
	<entry name="Neutral/Psychneuein:Idle#1" value="Zzzth. Zzzth."/>
	<entry name="Neutral/Psychneuein:Idle#2" value="Zzzaaa. Zzzm."/>
	<entry name="Neutral/Psychneuein:Idle#3" value="Zzzthzzz."/>
	<entry name="Neutral/Psychneuein:IdleCount" value="4"/>
	<entry name="Neutral/Psychneuein:Shaken#0" value="Zzzm!"/>
	<entry name="Neutral/Psychneuein:ShakenCount" value="1"/>
	<entry name="Neutral/Psychneuein:Victory#0" value="ZZZZ!"/>
	<entry name="Neutral/Psychneuein:VictoryCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Attack#0" value="Die for the Omnissiah!"/>
	<entry name="Neutral/TechpriestBetrayer:AttackCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Broken#0" value="Probability of failure, 100%"/>
	<entry name="Neutral/TechpriestBetrayer:BrokenCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Hurt#0" value="Operating functionality impaired."/>
	<entry name="Neutral/TechpriestBetrayer:HurtCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#0" value="Testing…"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#1" value="Turning it off… turning it on."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#2" value="Unguents applied."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#3" value="Serf to Servoskull… complete."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#4" value="Artefact acquired…"/>
	<entry name="Neutral/TechpriestBetrayer:IdleCount" value="5"/>
	<entry name="Neutral/TechpriestBetrayer:Shaken#0" value="I must survive!"/>
	<entry name="Neutral/TechpriestBetrayer:Shaken#1" value="Mars must be informed."/>
	<entry name="Neutral/TechpriestBetrayer:ShakenCount" value="2"/>
	<entry name="Neutral/TechpriestBetrayer:Victory#0" value="Escape is the only victory."/>
	<entry name="Neutral/TechpriestBetrayer:VictoryCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Attack#0" value="For the cult!"/>
	<entry name="Neutral/NeophyteHybrid:Attack#1" value="The Magus commands."/>
	<entry name="Neutral/NeophyteHybrid:Attack#2" value="The Devourer comes."/>
	<entry name="Neutral/NeophyteHybrid:AttackCount" value="3"/>
	<entry name="Neutral/NeophyteHybrid:Broken#0" value="Where is the Primus?"/>
	<entry name="Neutral/NeophyteHybrid:BrokenCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Hurt#0" value="Patriarch preserve us!"/>
	<entry name="Neutral/NeophyteHybrid:HurtCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Idle#0" value="It comes, brothers."/>
	<entry name="Neutral/NeophyteHybrid:Idle#1" value="The shadow nears."/>
	<entry name="Neutral/NeophyteHybrid:Idle#2" value="The Magus foretells a great hunger."/>
	<entry name="Neutral/NeophyteHybrid:Idle#3" value="When the day comes, we will walk willingly in."/>
	<entry name="Neutral/NeophyteHybrid:Idle#4" value="It is almost HERE."/>
	<entry name="Neutral/NeophyteHybrid:IdleCount" value="5"/>
	<entry name="Neutral/NeophyteHybrid:Shaken#0" value="We are poor meat, indeed."/>
	<entry name="Neutral/NeophyteHybrid:ShakenCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Victory#0" value="They are meat for the Queen to come!"/>
	<entry name="Neutral/NeophyteHybrid:VictoryCount" value="1"/>
	<entry name="Orks/Battlewagon:Attack#0" value="Woo, dakka, whee!"/>
	<entry name="Orks/Battlewagon:Attack#1" value="Red wunz 'it arder!"/>
	<entry name="Orks/Battlewagon:Attack#2" value="Run 'em down!"/>
	<entry name="Orks/Battlewagon:Attack#3" value="Shooting on da move!"/>
	<entry name="Orks/Battlewagon:AttackCount" value="4"/>
	<entry name="Orks/Battlewagon:Broken#0" value="Turn dis fing around!"/>
	<entry name="Orks/Battlewagon:Broken#1" value="Zog it! Move dis fung!"/>
	<entry name="Orks/Battlewagon:BrokenCount" value="2"/>
	<entry name="Orks/Battlewagon:EnemyFaction:AstraMilitarum#0" value="Oomies! Fire everyfing!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:Necrons#0" value="Scrap 'em for bitz!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:Orks#0" value="It's uz! Now fer a proper fite!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:OrksCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:SpaceMarines#0" value="Run down da Beakies!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Orks/Battlewagon:Hurt#0" value="Ow many wheels iz dis fing menna have?"/>
	<entry name="Orks/Battlewagon:HurtCount" value="1"/>
	<entry name="Orks/Battlewagon:Idle#0" value="And I said, what if it had two engjinnz?"/>
	<entry name="Orks/Battlewagon:Idle#1" value="Anywun got any red paint?"/>
	<entry name="Orks/Battlewagon:Idle#2" value="Need to go fasta, boss! FASTA!"/>
	<entry name="Orks/Battlewagon:Idle#3" value="Toot-toooot!"/>
	<entry name="Orks/Battlewagon:IdleCount" value="4"/>
	<entry name="Orks/Battlewagon:Shaken#0" value="'asn't this fing got springs?"/>
	<entry name="Orks/Battlewagon:Shaken#1" value="Squig suckin grot munchers!"/>
	<entry name="Orks/Battlewagon:Shaken#2" value="They'se shootin at uz!"/>
	<entry name="Orks/Battlewagon:ShakenCount" value="3"/>
	<entry name="Orks/Battlewagon:Victory#0" value="WAAAGH! Orks!"/>
	<entry name="Orks/Battlewagon:VictoryCount" value="1"/>
	<entry name="Orks/Boy:Attack#0" value="'it 'em 'arder!"/>
	<entry name="Orks/Boy:Attack#1" value="Dakka, dakka, dakka!"/>
	<entry name="Orks/Boy:Attack#2" value="Hur-hur-hur, dis iz the life!"/>
	<entry name="Orks/Boy:Attack#3" value="WAAAGH!"/>
	<entry name="Orks/Boy:Attack#4" value="Where's da fight?"/>
	<entry name="Orks/Boy:AttackCount" value="5"/>
	<entry name="Orks/Boy:Broken#0" value="AAAWGH! I'm off."/>
	<entry name="Orks/Boy:Broken#1" value="Ev'ry Ork fer 'imself!"/>
	<entry name="Orks/Boy:Broken#2" value="Left a squig cooking in my digs… back soon!"/>
	<entry name="Orks/Boy:Broken#3" value="Naw… naw, naw, naw."/>
	<entry name="Orks/Boy:Broken#4" value="RunrunRUN!"/>
	<entry name="Orks/Boy:BrokenCount" value="5"/>
	<entry name="Orks/Boy:EnemyFaction:AstraMilitarum#0" value="Oomies! Dey got great fings to nick."/>
	<entry name="Orks/Boy:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Orks/Boy:EnemyFaction:Necrons#0" value="Tin fings. Zogging fings don't stay dead!"/>
	<entry name="Orks/Boy:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Orks/Boy:EnemyFaction:Orks#0" value="Aw, another WAAAGH! Now we know we's gonna 'ave a great fight."/>
	<entry name="Orks/Boy:EnemyFaction:OrksCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/CatachanDevil#0" value="'Ow many legs? Dere's good eatin on dat."/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/Enslaver#0" value="Oh, me noggin. Keep away!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KastelanRobot#0" value="Yawn! Where's the fun in fighting tinboyz?"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="Oh, da smelly oomies are 'ere? We's gonna 'ave a great fight!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/Psychneuein#0" value="Buzzy fings! Wotchit, they get inside yer and then—POP!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="Orks/Boy:Hurt#0" value="'old on, me brains iz leaking out. Anywun got an 'ammer and sum nails?"/>
	<entry name="Orks/Boy:Hurt#1" value="e's ded! Hur hur. Bagsy 'is teef"/>
	<entry name="Orks/Boy:Hurt#2" value="Iz dat me arm?"/>
	<entry name="Orks/Boy:Hurt#3" value="We's the strongest wunz dere iz!"/>
	<entry name="Orks/Boy:HurtCount" value="4"/>
	<entry name="Orks/Boy:Idle#0" value="It's the Boss! Wot do yer want, Boss?"/>
	<entry name="Orks/Boy:Idle#1" value="More barrels means more dakka. More dakka means more fun!"/>
	<entry name="Orks/Boy:Idle#2" value="Nah, mate, this is my squig. Get yer own!"/>
	<entry name="Orks/Boy:Idle#3" value="Oi boss, wot's going on?"/>
	<entry name="Orks/Boy:Idle#4" value="One green squig, sitting on da wall. Two…"/>
	<entry name="Orks/Boy:Idle#5" value="This choppa just isn't big enuff!"/>
	<entry name="Orks/Boy:Idle#6" value="Where's der fite at?"/>
	<entry name="Orks/Boy:Idle#7" value="Who do yer want me to 'it?"/>
	<entry name="Orks/Boy:Idle#8" value="Wot is it?"/>
	<entry name="Orks/Boy:IdleCount" value="9"/>
	<entry name="Orks/Boy:QuestStory0#0" value="Zogging Gork, we's boooorrrred Boss. Got somefing to 'it?"/>
	<entry name="Orks/Boy:QuestStory0Count" value="1"/>
	<entry name="Orks/Boy:QuestStory1#0" value="Keep dat weirdboy away from us, don't want our 'edz to pop."/>
	<entry name="Orks/Boy:QuestStory1Count" value="1"/>
	<entry name="Orks/Boy:QuestStory2#0" value="Who lives under da ground? Nuffink ta fite down there."/>
	<entry name="Orks/Boy:QuestStory2Count" value="1"/>
	<entry name="Orks/Boy:QuestStory3#0" value="You want us to grab lotsa junk? No problem, Boss."/>
	<entry name="Orks/Boy:QuestStory3Count" value="1"/>
	<entry name="Orks/Boy:QuestStory4#0" value="Lets paint dat town red, Boss, hur-hur-hur."/>
	<entry name="Orks/Boy:QuestStory4Count" value="1"/>
	<entry name="Orks/Boy:QuestStory5#0" value="Never could eat squiggoth myself, Boss, all dat hair gets stuck in me teef."/>
	<entry name="Orks/Boy:QuestStory5Count" value="1"/>
	<entry name="Orks/Boy:Shaken#0" value="Go 'it 'im yerself, Boss."/>
	<entry name="Orks/Boy:Shaken#1" value="I need sum dat Fungus beer courage."/>
	<entry name="Orks/Boy:Shaken#2" value="I'm not Makari, I wanna live! I got fings to fite!"/>
	<entry name="Orks/Boy:Shaken#3" value="That ain't Orky."/>
	<entry name="Orks/Boy:ShakenCount" value="4"/>
	<entry name="Orks/Boy:Victory#0" value="'Ow d'yer like 'ot metal death?"/>
	<entry name="Orks/Boy:Victory#1" value="Da fing wiv not-Orks is that dey break so easily."/>
	<entry name="Orks/Boy:Victory#2" value="Fire, fire, fire, BOOM. YEAH!"/>
	<entry name="Orks/Boy:Victory#3" value="Hur hur hur!"/>
	<entry name="Orks/Boy:Victory#4" value="Not 'spectin that, were ya?"/>
	<entry name="Orks/Boy:Victory#5" value="Oi, get back up! We's not done stomping ya!"/>
	<entry name="Orks/Boy:Victory#6" value="Orkz is made fer' two fings! Fightin', winnin' and countin!"/>
	<entry name="Orks/Boy:Victory#7" value="STOMP. STOMP. STOMP."/>
	<entry name="Orks/Boy:Victory#8" value="These wuns are broke, Boss. Any more to fight?"/>
	<entry name="Orks/Boy:VictoryCount" value="9"/>
	<entry name="Orks/BurnaBommer:Attack#0" value="Going fer a flyby, Boss!"/>
	<entry name="Orks/BurnaBommer:AttackCount" value="1"/>
	<entry name="Orks/BurnaBommer:Broken#0" value="Zoomin the zog outta dere!"/>
	<entry name="Orks/BurnaBommer:BrokenCount" value="1"/>
	<entry name="Orks/BurnaBommer:Hurt#0" value="Big holez make it more fly-y, yeah?"/>
	<entry name="Orks/BurnaBommer:HurtCount" value="1"/>
	<entry name="Orks/BurnaBommer:Idle#0" value="Going round and round and round…"/>
	<entry name="Orks/BurnaBommer:Idle#1" value="Going up up up and down down down…"/>
	<entry name="Orks/BurnaBommer:Idle#2" value="Dose magnificent meks in dere flyin machines…"/>
	<entry name="Orks/BurnaBommer:Idle#3" value="To burn or to bomb, dat iz da question?"/>
	<entry name="Orks/BurnaBommer:IdleCount" value="4"/>
	<entry name="Orks/BurnaBommer:Shaken#0" value="Zog, giv uz a chance!"/>
	<entry name="Orks/BurnaBommer:ShakenCount" value="1"/>
	<entry name="Orks/BurnaBommer:Victory#0" value="WAAAGH! EAT IT!"/>
	<entry name="Orks/BurnaBommer:VictoryCount" value="1"/>
	<entry name="Orks/Dakkajet" value="Orks/BurnaBommer"/>
	<entry name="Orks/Deffkopta:Attack#0" value="Now dis iz a Choppa!"/>
	<entry name="Orks/Deffkopta:AttackCount" value="1"/>
	<entry name="Orks/Deffkopta:Broken#0" value="Oh, I'm geddin dizzy…"/>
	<entry name="Orks/Deffkopta:BrokenCount" value="1"/>
	<entry name="Orks/Deffkopta:Hurt#0" value="Oi, get up 'ere so I can 'it you!"/>
	<entry name="Orks/Deffkopta:HurtCount" value="1"/>
	<entry name="Orks/Deffkopta:Idle#0" value="I luv the smell of burnaz in da morning."/>
	<entry name="Orks/Deffkopta:Idle#1" value="Should we fite or should we fite?"/>
	<entry name="Orks/Deffkopta:Idle#2" value="T'au don't surf"/>
	<entry name="Orks/Deffkopta:Idle#3" value="I wonder if this fing can fly upside down?"/>
	<entry name="Orks/Deffkopta:IdleCount" value="4"/>
	<entry name="Orks/Deffkopta:Shaken#0" value="Ow many stikz does dis need to stay up? Uh."/>
	<entry name="Orks/Deffkopta:ShakenCount" value="1"/>
	<entry name="Orks/Deffkopta:Victory#0" value="Someday this war's gonna end…"/>
	<entry name="Orks/Deffkopta:VictoryCount" value="1"/>
	<entry name="Orks/FlashGitz" value="Orks/Boy"/>
	<entry name="Orks/GargantuanSquiggoth:Attack#0" value="That way squiggie, whoah!"/>
	<entry name="Orks/GargantuanSquiggoth:AttackCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Broken#0" value="Naw, you dumb squig, the uvver way."/>
	<entry name="Orks/GargantuanSquiggoth:BrokenCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Hurt#0" value="Don't tell 'im if 'e's ded."/>
	<entry name="Orks/GargantuanSquiggoth:HurtCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Idle#0" value="'Ere, squiggy, grubs up."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#1" value="Good, squiggy."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#2" value="Bad squiggy. Spit out Udgrub! BAD!."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#3" value="Urp! Can't beat a good faceeater fer tea."/>
	<entry name="Orks/GargantuanSquiggoth:IdleCount" value="4"/>
	<entry name="Orks/GargantuanSquiggoth:Shaken#0" value="S'okay, squiggy, keep eatin'"/>
	<entry name="Orks/GargantuanSquiggoth:ShakenCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Victory#0" value="Good Squiggy! Dinner time!"/>
	<entry name="Orks/GargantuanSquiggoth:VictoryCount" value="1"/>
	<entry name="Orks/Gorkanaut:Attack#0" value="DAKKA DAKKA!"/>
	<entry name="Orks/Gorkanaut:Attack#1" value="STOMP STOMP!"/>
	<entry name="Orks/Gorkanaut:AttackCount" value="2"/>
	<entry name="Orks/Gorkanaut:Broken#0" value="Me bitz is all smashed!"/>
	<entry name="Orks/Gorkanaut:BrokenCount" value="1"/>
	<entry name="Orks/Gorkanaut:Hurt#0" value="'ey, stop shooting! You know 'ow many teef this cost?"/>
	<entry name="Orks/Gorkanaut:HurtCount" value="1"/>
	<entry name="Orks/Gorkanaut:Idle#0" value="Can we stick more dakka on it? Please?"/>
	<entry name="Orks/Gorkanaut:Idle#1" value="I feels the power uv Gork!"/>
	<entry name="Orks/Gorkanaut:Idle#2" value="If we nail more tin to dis, can we make a Gargant?"/>
	<entry name="Orks/Gorkanaut:Idle#3" value="Fink I dropped me dinner in 'ere. Last week."/>
	<entry name="Orks/Gorkanaut:IdleCount" value="4"/>
	<entry name="Orks/Gorkanaut:Shaken#0" value="DAT DON'T SCARE UZ!"/>
	<entry name="Orks/Gorkanaut:ShakenCount" value="1"/>
	<entry name="Orks/Gorkanaut:Victory#0" value="WAAAGH! Tinboyz rulez"/>
	<entry name="Orks/Gorkanaut:VictoryCount" value="1"/>
	<entry name="Orks/Headquarters:Attack#0" value="Finally, shootin at sumfin."/>
	<entry name="Orks/Headquarters:AttackCount" value="1"/>
	<entry name="Orks/Headquarters:Broken#0" value="Boss, 'elp uz!"/>
	<entry name="Orks/Headquarters:BrokenCount" value="1"/>
	<entry name="Orks/Headquarters:Hurt#0" value="Deyz shooting the fort, Boss!"/>
	<entry name="Orks/Headquarters:HurtCount" value="1"/>
	<entry name="Orks/Headquarters:Idle#0" value="Guard duty! Where's the WAAAGH in dat?"/>
	<entry name="Orks/Headquarters:Idle#1" value="Oo's da boss 'ere?"/>
	<entry name="Orks/Headquarters:Idle#2" value="Blood Axes reportin' sah!"/>
	<entry name="Orks/Headquarters:Idle#3" value="'idin behind walls ain't Orky."/>
	<entry name="Orks/Headquarters:IdleCount" value="4"/>
	<entry name="Orks/Headquarters:Shaken#0" value="Orkz don't run!"/>
	<entry name="Orks/Headquarters:ShakenCount" value="1"/>
	<entry name="Orks/Headquarters:Victory#0" value="WAAAGH! Goddim!"/>
	<entry name="Orks/Headquarters:VictoryCount" value="1"/>
	<entry name="Orks/KillaKan" value="Orks/Gorkanaut"/>
	<entry name="Orks/Meganob" value="Orks/Boy"/>
	<entry name="Orks/Mek:Attack#0" value="Firing all me barrelz!"/>
	<entry name="Orks/Mek:AttackCount" value="1"/>
	<entry name="Orks/Mek:Broken#0" value="Sumfink needs fixing at da fort."/>
	<entry name="Orks/Mek:BrokenCount" value="1"/>
	<entry name="Orks/Mek:Hurt#0" value="Me kustom fieldz are broke!"/>
	<entry name="Orks/Mek:HurtCount" value="1"/>
	<entry name="Orks/Mek:Idle#0" value="I fink I could make sumfink special…"/>
	<entry name="Orks/Mek:Idle#1" value="'Ere, pass the wotsit. Nah, 'old dis whilst I 'it it…"/>
	<entry name="Orks/Mek:Idle#2" value="Imagine a shoota with FIVE barrels. Yeahhhh."/>
	<entry name="Orks/Mek:Idle#3" value="Hmm. Killa Kan ain't workin. Need bigger Grot, smaller nails."/>
	<entry name="Orks/Mek:IdleCount" value="4"/>
	<entry name="Orks/Mek:Shaken#0" value="Morkz gutz, dat's not rite!"/>
	<entry name="Orks/Mek:ShakenCount" value="1"/>
	<entry name="Orks/Mek:Victory#0" value="WAAAGH! Mek brainz winz da day!"/>
	<entry name="Orks/Mek:VictoryCount" value="1"/>
	<entry name="Orks/MekGun:Attack#0" value="Firing da big gunz, Mr Ork Boss!"/>
	<entry name="Orks/MekGun:AttackCount" value="1"/>
	<entry name="Orks/MekGun:Broken#0" value="Every grot fer hisself!"/>
	<entry name="Orks/MekGun:BrokenCount" value="1"/>
	<entry name="Orks/MekGun:Hurt#0" value="More ded grotz."/>
	<entry name="Orks/MekGun:HurtCount" value="1"/>
	<entry name="Orks/MekGun:Idle#0" value="Grotz is da smartest and best."/>
	<entry name="Orks/MekGun:Idle#1" value="Away from the fite, wiv a big gun. Best."/>
	<entry name="Orks/MekGun:Idle#2" value="Moki, get outta da barrel!"/>
	<entry name="Orks/MekGun:Idle#3" value="Smelly Mek."/>
	<entry name="Orks/MekGun:IdleCount" value="4"/>
	<entry name="Orks/MekGun:Shaken#0" value="Stop 'itting us, we're not gonna run!"/>
	<entry name="Orks/MekGun:ShakenCount" value="1"/>
	<entry name="Orks/MekGun:Victory#0" value="Grotz, I mean, Orks are the best, right Boss?"/>
	<entry name="Orks/MekGun:VictoryCount" value="1"/>
	<entry name="Orks/Painboy:Attack#0" value="Hee-hee, battlefield surgery!"/>
	<entry name="Orks/Painboy:AttackCount" value="1"/>
	<entry name="Orks/Painboy:Broken#0" value="Dokz and little snotz first!"/>
	<entry name="Orks/Painboy:BrokenCount" value="1"/>
	<entry name="Orks/Painboy:Hurt#0" value="Just need to nail this back on, 'old on."/>
	<entry name="Orks/Painboy:HurtCount" value="1"/>
	<entry name="Orks/Painboy:Idle#0" value="Naw, Boss, owzabout a nice big noo arm?"/>
	<entry name="Orks/Painboy:Idle#1" value="Need yer teef. Open wide. Now say WAAAGH!"/>
	<entry name="Orks/Painboy:Idle#2" value="Wot iz 'mad', anyway?"/>
	<entry name="Orks/Painboy:Idle#3" value="Need bigger needle squigz."/>
	<entry name="Orks/Painboy:IdleCount" value="4"/>
	<entry name="Orks/Painboy:Shaken#0" value="'ere, dats not rite."/>
	<entry name="Orks/Painboy:ShakenCount" value="1"/>
	<entry name="Orks/Painboy:Victory#0" value="Look at all dose bitz… luverly."/>
	<entry name="Orks/Painboy:VictoryCount" value="1"/>
	<entry name="Orks/Tankbusta" value="Orks/Boy"/>
	<entry name="Orks/Warboss:Attack#0" value="Oo's da best Ork. WAAAGH! ME."/>
	<entry name="Orks/Warboss:AttackCount" value="1"/>
	<entry name="Orks/Warboss:Broken#0" value="I just usin taktiks, dat's all."/>
	<entry name="Orks/Warboss:BrokenCount" value="1"/>
	<entry name="Orks/Warboss:Hurt#0" value="Ow, don't yoo know oo I am?"/>
	<entry name="Orks/Warboss:HurtCount" value="1"/>
	<entry name="Orks/Warboss:Idle#0" value="Zoggin nuffink to do. ZOG!"/>
	<entry name="Orks/Warboss:Idle#1" value="WHERE'S THE FITE?"/>
	<entry name="Orks/Warboss:Idle#2" value="Curried squig fer lunch AGAIN? Where's da drops?"/>
	<entry name="Orks/Warboss:Idle#3" value="I need ta 'it fings!"/>
	<entry name="Orks/Warboss:Idle#4" value="You got dem squigs outta me Mega Armour yet, grot?"/>
	<entry name="Orks/Warboss:IdleCount" value="5"/>
	<entry name="Orks/Warboss:Shaken#0" value="Sooner we're outta 'ere, da better."/>
	<entry name="Orks/Warboss:ShakenCount" value="1"/>
	<entry name="Orks/Warboss:Victory#0" value="Course we won, I was 'ere!"/>
	<entry name="Orks/Warboss:VictoryCount" value="1"/>
	<entry name="Orks/Warbuggy" value="Orks/Battlewagon"/>
	<entry name="Orks/Weirdboy:Attack#0" value="Owwwwwwww… me 'ed."/>
	<entry name="Orks/Weirdboy:AttackCount" value="1"/>
	<entry name="Orks/Weirdboy:Broken#0" value="Get me away from 'ere!"/>
	<entry name="Orks/Weirdboy:BrokenCount" value="1"/>
	<entry name="Orks/Weirdboy:Hurt#0" value="Edz-a-popping!"/>
	<entry name="Orks/Weirdboy:HurtCount" value="1"/>
	<entry name="Orks/Weirdboy:Idle#0" value="Blurble."/>
	<entry name="Orks/Weirdboy:Idle#1" value="Mork talks. Gork walks."/>
	<entry name="Orks/Weirdboy:Idle#2" value="The shadow in the warp..!"/>
	<entry name="Orks/Weirdboy:Idle#3" value="Neath Gladius lie the Krork and the Brainboyz, waiting for the last fite."/>
	<entry name="Orks/Weirdboy:IdleCount" value="4"/>
	<entry name="Orks/Weirdboy:Shaken#0" value="Gork and Mork compel yew!"/>
	<entry name="Orks/Weirdboy:ShakenCount" value="1"/>
	<entry name="Orks/Weirdboy:Victory#0" value="Hur-hur, WAAAGH!"/>
	<entry name="Orks/Weirdboy:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Attack#0" value="These don't only heal."/>
	<entry name="SpaceMarines/Apothecary:AttackCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Broken#0" value="I cannot leave the gene-seed!"/>
	<entry name="SpaceMarines/Apothecary:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:AstraMilitarum#0" value="Beyond contempt."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:Necrons#0" value="Bloodless fiends."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:Orks#0" value="Greenskins! Would that we could regrow limbs like them."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:SpaceMarines#0" value="Renegades brothers, but not traitors. I'll recover their gene-seed all the same."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Hurt#0" value="Patching myself up."/>
	<entry name="SpaceMarines/Apothecary:HurtCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Idle#0" value="Apothecary on call."/>
	<entry name="SpaceMarines/Apothecary:Idle#1" value="Docs don't need downtime."/>
	<entry name="SpaceMarines/Apothecary:Idle#2" value="Reductor practise."/>
	<entry name="SpaceMarines/Apothecary:Idle#3" value="The progenoid's connected to the Betchers and the Sus-an…"/>
	<entry name="SpaceMarines/Apothecary:IdleCount" value="4"/>
	<entry name="SpaceMarines/Apothecary:Shaken#0" value="Good news! I'm not dead."/>
	<entry name="SpaceMarines/Apothecary:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Victory#0" value="They're dead! I'm not sure this is the best use of my time!"/>
	<entry name="SpaceMarines/Apothecary:VictoryCount" value="1"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/Captain:Attack#0" value="Die happy scum, knowing you died at my hand!"/>
	<entry name="SpaceMarines/Captain:Attack#1" value="Eat boltgun."/>
	<entry name="SpaceMarines/Captain:Attack#2" value="For the Emperor!"/>
	<entry name="SpaceMarines/Captain:Attack#3" value="You are facing a Space Marine captain. Surrender now, for a quick death."/>
	<entry name="SpaceMarines/Captain:Attack#4" value="Guilliman awaits."/>
	<entry name="SpaceMarines/Captain:Attack#5" value="My faith is my shield!"/>
	<entry name="SpaceMarines/Captain:AttackCount" value="6"/>
	<entry name="SpaceMarines/Captain:Broken#0" value="Emperor preserve us!"/>
	<entry name="SpaceMarines/Captain:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:AstraMilitarum#0" value="Traitor guard. Trivial."/>
	<entry name="SpaceMarines/Captain:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:Necrons#0" value="Root out their tombs! Crush the mechnical horrors!"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:Orks#0" value="Will the Emperor never free us of this green plague?"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:SpaceMarines#0" value="Renegades. Marines, prepare your weapons, we fight for honour."/>
	<entry name="SpaceMarines/Captain:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Captain:Hurt#0" value="How did that get through my armour?"/>
	<entry name="SpaceMarines/Captain:HurtCount" value="1"/>
	<entry name="SpaceMarines/Captain:Idle#0" value="I should be leading from the front."/>
	<entry name="SpaceMarines/Captain:Idle#1" value="Inspirational speeches don't write themselves."/>
	<entry name="SpaceMarines/Captain:Idle#2" value="We must save humanity from itself."/>
	<entry name="SpaceMarines/Captain:Idle#3" value="Why here? Why Gladius? Are we being tested?"/>
	<entry name="SpaceMarines/Captain:IdleCount" value="4"/>
	<entry name="SpaceMarines/Captain:Shaken#0" value="I bend to no-one!"/>
	<entry name="SpaceMarines/Captain:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Captain:Victory#0" value="Thus fall all enemies of the Golden Throne!"/>
	<entry name="SpaceMarines/Captain:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Attack#0" value="Be blessed by the Crozius!"/>
	<entry name="SpaceMarines/Chaplain:Attack#1" value="I will make you believe!"/>
	<entry name="SpaceMarines/Chaplain:Attack#2" value="In nomine Imperator!"/>
	<entry name="SpaceMarines/Chaplain:AttackCount" value="3"/>
	<entry name="SpaceMarines/Chaplain:Broken#0" value="Not one step back!"/>
	<entry name="SpaceMarines/Chaplain:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Hurt#0" value="Though I suffer their blows, I shall not fall."/>
	<entry name="SpaceMarines/Chaplain:HurtCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Idle#0" value="Death has no terror save that our work will be incomplete."/>
	<entry name="SpaceMarines/Chaplain:Idle#1" value="Give your life to the Emperor, it shall not be in vain."/>
	<entry name="SpaceMarines/Chaplain:Idle#2" value="There is no peace whilst our foes live."/>
	<entry name="SpaceMarines/Chaplain:Idle#3" value="We spit our defiance to the end."/>
	<entry name="SpaceMarines/Chaplain:IdleCount" value="4"/>
	<entry name="SpaceMarines/Chaplain:Shaken#0" value="For a warrior, the only crime is cowardice."/>
	<entry name="SpaceMarines/Chaplain:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Victory#0" value="Faith is stronger than adamantium."/>
	<entry name="SpaceMarines/Chaplain:VictoryCount" value="1"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/Dreadnought:Attack#0" value="Every kill is fresh, even now."/>
	<entry name="SpaceMarines/Dreadnought:Attack#1" value="For the Chapter!"/>
	<entry name="SpaceMarines/Dreadnought:Attack#2" value="Hrm… at war again."/>
	<entry name="SpaceMarines/Dreadnought:Attack#3" value="Ten thousand years of war…"/>
	<entry name="SpaceMarines/Dreadnought:AttackCount" value="4"/>
	<entry name="SpaceMarines/Dreadnought:Broken#0" value="After a millennium, I know when to fall back."/>
	<entry name="SpaceMarines/Dreadnought:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Dreadnought:Hurt#0" value="My sarcophagus… is hit."/>
	<entry name="SpaceMarines/Dreadnought:Hurt#1" value="The litany of preservation, Chaplain?"/>
	<entry name="SpaceMarines/Dreadnought:HurtCount" value="2"/>
	<entry name="SpaceMarines/Dreadnought:Idle#0" value="A walking tomb…"/>
	<entry name="SpaceMarines/Dreadnought:Idle#1" value="Is war truly eternal?"/>
	<entry name="SpaceMarines/Dreadnought:Idle#2" value="To sleep… but to dream. There's the rub."/>
	<entry name="SpaceMarines/Dreadnought:Idle#3" value="Who rouses me?"/>
	<entry name="SpaceMarines/Dreadnought:IdleCount" value="4"/>
	<entry name="SpaceMarines/Dreadnought:Shaken#0" value="Fear never grows old."/>
	<entry name="SpaceMarines/Dreadnought:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Dreadnought:Victory#0" value="Our chapter history grows."/>
	<entry name="SpaceMarines/Dreadnought:VictoryCount" value="1"/>
	<entry name="SpaceMarines/FortressOfRedemption" value="SpaceMarines/Headquarters"/>
	<entry name="SpaceMarines/Headquarters:Attack#0" value="Break yourselves on our defences!"/>
	<entry name="SpaceMarines/Headquarters:Attack#1" value="Fools, to attack an Adeptus Astartes stronghold"/>
	<entry name="SpaceMarines/Headquarters:Attack#2" value="Fortress Commander here. Firing."/>
	<entry name="SpaceMarines/Headquarters:Attack#3" value="Why not try walking in the front door?"/>
	<entry name="SpaceMarines/Headquarters:AttackCount" value="4"/>
	<entry name="SpaceMarines/Headquarters:Broken#0" value="Our Chapter will. NOT. Fall."/>
	<entry name="SpaceMarines/Headquarters:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:AstraMilitarum#0" value="Guard on the horizon. Hostile."/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:Necrons#0" value="Here come the Necrons. Pick 'em off."/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:Orks#0" value="Greenskins, again? Ready boltguns!"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:SpaceMarines#0" value="Scouts are engaging renegades. Foul traitors, here?"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Hurt#0" value="Fortress is breached, repeat Fortress is breached."/>
	<entry name="SpaceMarines/Headquarters:HurtCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Idle#0" value="All quiet on the home front."/>
	<entry name="SpaceMarines/Headquarters:Idle#1" value="Commander, we're ready for anything."/>
	<entry name="SpaceMarines/Headquarters:Idle#2" value="Litanies are being read in the Reclusiam."/>
	<entry name="SpaceMarines/Headquarters:Idle#3" value="Serfs are hard at work, sir."/>
	<entry name="SpaceMarines/Headquarters:IdleCount" value="4"/>
	<entry name="SpaceMarines/Headquarters:Shaken#0" value="Recall all the units to the Fortress!"/>
	<entry name="SpaceMarines/Headquarters:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Victory#0" value="Invaders eliminated. How dare they?"/>
	<entry name="SpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Attack#0" value="Targetting weapons."/>
	<entry name="SpaceMarines/Hunter:AttackCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Broken#0" value="Armour is pulling back."/>
	<entry name="SpaceMarines/Hunter:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Hurt#0" value="Armour is penetrated."/>
	<entry name="SpaceMarines/Hunter:Hurt#1" value="Small arms fire."/>
	<entry name="SpaceMarines/Hunter:Hurt#2" value="Tracks are hit."/>
	<entry name="SpaceMarines/Hunter:HurtCount" value="3"/>
	<entry name="SpaceMarines/Hunter:Idle#0" value="Cogitator is in working order."/>
	<entry name="SpaceMarines/Hunter:Idle#1" value="Invoking the machine spirits."/>
	<entry name="SpaceMarines/Hunter:Idle#2" value="Performing repairs."/>
	<entry name="SpaceMarines/Hunter:IdleCount" value="3"/>
	<entry name="SpaceMarines/Hunter:Shaken#0" value="Crew are a bit rattled, sir."/>
	<entry name="SpaceMarines/Hunter:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Victory#0" value="Armour is rolling on."/>
	<entry name="SpaceMarines/Hunter:VictoryCount" value="1"/>
	<entry name="SpaceMarines/LandRaider" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/LandSpeeder" value="SpaceMarines/ScoutBiker"/>
	<entry name="SpaceMarines/Librarian:Attack#0" value="Let me gaze into your future… ah. It's short."/>
	<entry name="SpaceMarines/Librarian:Attack#1" value="I have a gift. It would be churlish not to share it…"/>
	<entry name="SpaceMarines/Librarian:Attack#2" value="Snap go my fingers… snap go your bones."/>
	<entry name="SpaceMarines/Librarian:AttackCount" value="3"/>
	<entry name="SpaceMarines/Librarian:Broken#0" value="I can hear the daemon, howling in my ears."/>
	<entry name="SpaceMarines/Librarian:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Hurt#0" value="Physical pain is nothing compared to eternal damnation."/>
	<entry name="SpaceMarines/Librarian:HurtCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Idle#0" value="Blessed is the mind too small for doubt."/>
	<entry name="SpaceMarines/Librarian:Idle#1" value="The warp holds no fear for me."/>
	<entry name="SpaceMarines/Librarian:Idle#2" value="The warp storm… sometimes the pain is too much."/>
	<entry name="SpaceMarines/Librarian:Idle#3" value="When this is over… the Librarium calls."/>
	<entry name="SpaceMarines/Librarian:IdleCount" value="4"/>
	<entry name="SpaceMarines/Librarian:Shaken#0" value="The lore has nothing about this…"/>
	<entry name="SpaceMarines/Librarian:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Victory#0" value="Their failure was foreseen."/>
	<entry name="SpaceMarines/Librarian:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Predator" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#0" value="My duty is my armour."/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#1" value="Flamestorm."/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#2" value="Wait for them to approach."/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#3" value="Scour them!"/>
	<entry name="SpaceMarines/PrimarisAggressor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisAggressor:Broken#0" value="Hold. Hold!"/>
	<entry name="SpaceMarines/PrimarisAggressor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Hurt#0" value="By His name!"/>
	<entry name="SpaceMarines/PrimarisAggressor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#0" value="We wait."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#1" value="I am equal to my task."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#2" value="Cleansing fire."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#3" value="We will burn His enemies to ash."/>
	<entry name="SpaceMarines/PrimarisAggressor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisAggressor:Shaken#0" value="Covering fire!"/>
	<entry name="SpaceMarines/PrimarisAggressor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Victory#0" value="Cleansed with the Emperor's fury!"/>
	<entry name="SpaceMarines/PrimarisAggressor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#0" value="Plasma fusillade."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#1" value="Supercharging incinerators."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#2" value="Armour-hunters."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#3" value="Melting His enemies."/>
	<entry name="SpaceMarines/PrimarisHellblaster:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Broken#0" value="A final stand, then."/>
	<entry name="SpaceMarines/PrimarisHellblaster:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Hurt#0" value="Inevitable."/>
	<entry name="SpaceMarines/PrimarisHellblaster:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#0" value="Cooling the relics."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#1" value="Sacred fusion."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#2" value="They shall not have this world."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#3" value="Stabilizing containment fields."/>
	<entry name="SpaceMarines/PrimarisHellblaster:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Shaken#0" value="Coils… destabilising."/>
	<entry name="SpaceMarines/PrimarisHellblaster:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Victory#0" value="Foes incinerated."/>
	<entry name="SpaceMarines/PrimarisHellblaster:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#0" value="Orbital support."/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#1" value="Establishing beachhead."/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#2" value="Interceding!"/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#3" value="Raining fiery vengeance."/>
	<entry name="SpaceMarines/PrimarisInceptor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInceptor:Broken#0" value="To the skies."/>
	<entry name="SpaceMarines/PrimarisInceptor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Hurt#0" value="Exfiltrating!"/>
	<entry name="SpaceMarines/PrimarisInceptor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#0" value="Waiting to drop."/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#1" value="Ready to strike."/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#2" value="For the Chapter!"/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#3" value="Unnumbered Sons no more."/>
	<entry name="SpaceMarines/PrimarisInceptor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInceptor:Shaken#0" value="Focus!"/>
	<entry name="SpaceMarines/PrimarisInceptor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Victory#0" value="Send us where we are needed."/>
	<entry name="SpaceMarines/PrimarisInceptor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:AlliedUnit:SpaceMarines/TacticalSpaceMarine#0" value="…ancient Brothers…"/>
	<entry name="SpaceMarines/PrimarisIntercessor:AlliedUnit:SpaceMarines/TacticalSpaceMarineCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#0" value="Bolt rifles, Brothers."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#1" value="Standard firing pattern."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#2" value="Overwatch!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#3" value="Watch for stragglers."/>
	<entry name="SpaceMarines/PrimarisIntercessor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Broken#0" value="Retreat as a unit."/>
	<entry name="SpaceMarines/PrimarisIntercessor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Hurt#0" value="A brother has fallen. Repeat."/>
	<entry name="SpaceMarines/PrimarisIntercessor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#0" value="In His name."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#1" value="We were all Greyshields once."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#2" value="Proof against heresy!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#3" value="For the Primarchs!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Shaken#0" value="Hold fast, Brothers."/>
	<entry name="SpaceMarines/PrimarisIntercessor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Victory#0" value="Squad requesting new targets."/>
	<entry name="SpaceMarines/PrimarisIntercessor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#0" value="Firing! Forwards!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#1" value="Anti-infantry."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#2" value="Striking at the foe!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#3" value="Escorting."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Broken#0" value="Disengaging."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Hurt#0" value="Receiving fire."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#0" value="Checking turret movement."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#1" value="That will need a Tech-marine's attention."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#2" value="Scouting for Orks."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#3" value="It is too quiet."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Shaken#0" value="Keep moving!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Victory#0" value="Foe eliminated. Extracting."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#0" value="Firing main guns."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#1" value="Executing!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#2" value="Xenos, heretics… all fall!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#3" value="By the Codex!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Broken#0" value="By the Emperor!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Hurt#0" value="Hull breach! Keep firing."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#0" value="Checking all the guns."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#1" value="Stowing ammunition."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#2" value="Restoring plating."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#3" value="A tank fit for an Astartes!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Shaken#0" value="Unthinkable!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Victory#0" value="Inevitable."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:VictoryCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#0" value="Macro… plasma… incinerator!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#1" value="I am… His wrath!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#2" value="Our hour… is here!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#3" value="Face… me!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:AttackCount" value="4"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Broken#0" value="Stand and fight… brothers!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:BrokenCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Hurt#0" value="Only… in death…"/>
	<entry name="SpaceMarines/RedemptorDreadnought:HurtCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#0" value="Redemption… awaits."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#1" value="So… long ago."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#2" value="Duty… never ends."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#3" value="Onward… to battle."/>
	<entry name="SpaceMarines/RedemptorDreadnought:IdleCount" value="4"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Shaken#0" value="Emperor…"/>
	<entry name="SpaceMarines/RedemptorDreadnought:ShakenCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Victory#0" value="Indomitus… continues."/>
	<entry name="SpaceMarines/RedemptorDreadnought:VictoryCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Attack#0" value="Hitting fast and hard."/>
	<entry name="SpaceMarines/ScoutBiker:AttackCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Broken#0" value="Moving to a safe distance"/>
	<entry name="SpaceMarines/ScoutBiker:BrokenCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Hurt#0" value="We're not armoured for this."/>
	<entry name="SpaceMarines/ScoutBiker:HurtCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Idle#0" value="An idle scout is useless."/>
	<entry name="SpaceMarines/ScoutBiker:IdleCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Shaken#0" value="We need to keep moving."/>
	<entry name="SpaceMarines/ScoutBiker:ShakenCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Victory#0" value="Chalk another one up to the fast attack."/>
	<entry name="SpaceMarines/ScoutBiker:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Attack#0" value="Air support engaging."/>
	<entry name="SpaceMarines/StormravenGunship:AttackCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Broken#0" value="Heading back to base."/>
	<entry name="SpaceMarines/StormravenGunship:BrokenCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Hurt#0" value="AA fire, incoming."/>
	<entry name="SpaceMarines/StormravenGunship:Hurt#1" value="Engine is gone!"/>
	<entry name="SpaceMarines/StormravenGunship:HurtCount" value="2"/>
	<entry name="SpaceMarines/StormravenGunship:Idle#0" value="Any orders?"/>
	<entry name="SpaceMarines/StormravenGunship:Idle#1" value="Circling, looking for targets."/>
	<entry name="SpaceMarines/StormravenGunship:Idle#2" value="Eye in the sky."/>
	<entry name="SpaceMarines/StormravenGunship:IdleCount" value="3"/>
	<entry name="SpaceMarines/StormravenGunship:Shaken#0" value="Like flying through a mountain, ouch."/>
	<entry name="SpaceMarines/StormravenGunship:ShakenCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Victory#0" value="Can't see anything down there any more."/>
	<entry name="SpaceMarines/StormravenGunship:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#0" value="Attack swiftly"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#1" value="Striking from the sun."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#2" value="Suffer not the xenos or heretic to live."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#3" value="No mercy."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:AttackCount" value="4"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Broken#0" value="Breaking off."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:BrokenCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Hurt#0" value="Get us out of range!"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:HurtCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#0" value="Duty unto death."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#1" value="The honour of being first into battle."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#2" value="Their armour fears us."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#3" value="Idleness is heresy."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:IdleCount" value="4"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Shaken#0" value="What orders, Commander?"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:ShakenCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Victory#0" value="Already on the next target."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormtalonGunship" value="SpaceMarines/StormravenGunship"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Artefact#0" value="What is that foul xenos device?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:ArtefactCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#0" value="Eat boltgun!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#1" value="Engaging the enemy!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#2" value="For the glory of the Imperium!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#3" value="I am your salvation!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#4" value="Space Marines, attack!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#5" value="Taste the Emperor's fury!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#6" value="We are the fist of Guilliman."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#7" value="You pollute this world, filth!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:AttackCount" value="8"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#0" value="Fall back to the prepared defenses!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#1" value="Fight to the last marine!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#2" value="Preserve the gene-seed!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#3" value="The Emperor has abandoned us!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#4" value="We bring shame upon our Chapter!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:BrokenCount" value="5"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Cover#0" value="The Emperor protects those who protect themselves."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:CoverCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:AstraMilitarum#0" value="Even as allies, the Guard cannot be trusted."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:Necrons#0" value="Ancient revenant horrors? Ha! Far darker things there are in the heart of man."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:Orks#0" value="You dare to invade OUR homeworld, Greenskin scum?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:SpaceMarines#0" value="Traitors, on our homeworld?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/CatachanDevil#0" value="Devil! Stay at range and aim for the legs"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/Enslaver#0" value="Blasted puppeteers—where are the Librarians?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KastelanRobot#0" value="Our own creations are turned against us."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="Foul Chaos, here, on our planet? Summon the Inquisition!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/Psychneuein#0" value="Space Marines, seal your suits to prevent infestation."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Hurt#0" value="We will stand until the last Marine."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Hurt#1" value="We… will… not… fall!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:HurtCount" value="2"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#0" value="Burn the heretic."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#1" value="I hunger for battle."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#2" value="Let them come… I grow impatient."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#3" value="So many Xenos… so few bolts."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#4" value="We are the Emperor's fist."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#5" value="We fight, we prevail."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#6" value="We must not meddle with these xenos artefacts, my lord."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#7" value="What orders, my lord?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:IdleCount" value="8"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory0#0" value="Low ammunition… need to make every shot count."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory0Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory1#0" value="The Librarian says the Guard cannot be trusted—we will do what we must."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory1Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory2#0" value="The homeworld is… hollow?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory2Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory3#0" value="Study the Xenos? My Lord, is this in the Codex Astartes?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory3Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory4#0" value="I fear nothing, not even Exterminatus."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory4Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory5#0" value="The gene-seed will survive us all—it must!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory5Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#0" value="…blessed is the mind too small for doubt."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#1" value="Against this darkness, we shall prevail."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#2" value="I am an unbreakable shield, forged against the darkness."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#3" value="I… believe in the Emperor. Credo!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#4" value="Whilst our enemies draw breath, we cannot afford fear."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:ShakenCount" value="5"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#0" value="All xenos and traitors are blights, to be cleansed from the galaxy."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#1" value="At the Lord Commander's order."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#2" value="Death at our hands is no dishonour."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#3" value="In the Emperor's name."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#4" value="My fist, clenched tight in ceramite, smashes with the force of a Chapter."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#5" value="So fall all enemies of the Imperium."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#6" value="Through the destruction of our enemies do we earn our salvation."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#7" value="We are the Emperor's vengeance, made flesh."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:VictoryCount" value="8"/>
	<entry name="SpaceMarines/Terminator" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/Vindicator" value="SpaceMarines/Hunter"/>
	<entry name="Tau/BroadsideBattlesuit" value="Tau/CrisisBattlesuit"/>
	<entry name="Tau/BuilderDrone:Attack#0" value="This is an inefficient use of resources."/>
	<entry name="Tau/BuilderDrone:Attack#1" value="Performing inadvisable attack."/>
	<entry name="Tau/BuilderDrone:Attack#2" value="Doing minimal damage."/>
	<entry name="Tau/BuilderDrone:AttackCount" value="3"/>
	<entry name="Tau/BuilderDrone:Broken#0" value="Unit in danger."/>
	<entry name="Tau/BuilderDrone:BrokenCount" value="1"/>
	<entry name="Tau/BuilderDrone:Hurt#0" value="Unit compromised."/>
	<entry name="Tau/BuilderDrone:HurtCount" value="1"/>
	<entry name="Tau/BuilderDrone:Idle#0" value="Powering down"/>
	<entry name="Tau/BuilderDrone:Idle#1" value="Assessing schematics…"/>
	<entry name="Tau/BuilderDrone:Idle#2" value="Serving the Earth Caste and the Greater Good."/>
	<entry name="Tau/BuilderDrone:IdleCount" value="3"/>
	<entry name="Tau/BuilderDrone:Shaken#0" value="Construction unit under fire."/>
	<entry name="Tau/BuilderDrone:ShakenCount" value="1"/>
	<entry name="Tau/BuilderDrone:Victory#0" value="T'au over-engineering in action."/>
	<entry name="Tau/BuilderDrone:VictoryCount" value="1"/>
	<entry name="Tau/CadreFireblade:Attack#0" value="Gunline, attack!"/>
	<entry name="Tau/CadreFireblade:Attack#1" value="Fire Warriors, show them The Good."/>
	<entry name="Tau/CadreFireblade:Attack#2" value="Feel the power of pulse technology!"/>
	<entry name="Tau/CadreFireblade:Attack#3" value="Wait until you see the whites of their eyes… through your scope."/>
	<entry name="Tau/CadreFireblade:AttackCount" value="4"/>
	<entry name="Tau/CadreFireblade:Broken#0" value="What would Puretide think..?"/>
	<entry name="Tau/CadreFireblade:BrokenCount" value="1"/>
	<entry name="Tau/CadreFireblade:Hurt#0" value="It takes more than that to put me down!"/>
	<entry name="Tau/CadreFireblade:HurtCount" value="1"/>
	<entry name="Tau/CadreFireblade:Idle#0" value="Target practice, shas'el, fall in!"/>
	<entry name="Tau/CadreFireblade:Idle#1" value="We lead from the front."/>
	<entry name="Tau/CadreFireblade:Idle#2" value="A good leader keeps his feet on the ground."/>
	<entry name="Tau/CadreFireblade:IdleCount" value="3"/>
	<entry name="Tau/CadreFireblade:Shaken#0" value="This is not the way…"/>
	<entry name="Tau/CadreFireblade:ShakenCount" value="1"/>
	<entry name="Tau/CadreFireblade:Victory#0" value="Tactically wise."/>
	<entry name="Tau/CadreFireblade:VictoryCount" value="1"/>
	<entry name="Tau/Commander:Attack#0" value="For Aun'Va!"/>
	<entry name="Tau/Commander:Attack#1" value="We will teach you the way."/>
	<entry name="Tau/Commander:Attack#2" value="Join us!"/>
	<entry name="Tau/Commander:AttackCount" value="3"/>
	<entry name="Tau/Commander:Broken#0" value="Retreat is the only option…"/>
	<entry name="Tau/Commander:BrokenCount" value="1"/>
	<entry name="Tau/Commander:Hurt#0" value="Battlesuit compromised."/>
	<entry name="Tau/Commander:HurtCount" value="1"/>
	<entry name="Tau/Commander:Idle#0" value="Use me, for the Greater Good."/>
	<entry name="Tau/Commander:Idle#1" value="Always thinking one step ahead."/>
	<entry name="Tau/Commander:Idle#2" value="What is a single life to the Greater Good?"/>
	<entry name="Tau/Commander:IdleCount" value="3"/>
	<entry name="Tau/Commander:Shaken#0" value="I will not falter."/>
	<entry name="Tau/Commander:ShakenCount" value="1"/>
	<entry name="Tau/Commander:Victory#0" value="The Fire Caste leads."/>
	<entry name="Tau/Commander:VictoryCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Attack#0" value="Battlesuits in action."/>
	<entry name="Tau/CrisisBattlesuit:Attack#1" value="Firing pulse weaponry."/>
	<entry name="Tau/CrisisBattlesuit:Attack#2" value="Enabling countermeasures."/>
	<entry name="Tau/CrisisBattlesuit:Attack#3" value="You can count on us!"/>
	<entry name="Tau/CrisisBattlesuit:AttackCount" value="4"/>
	<entry name="Tau/CrisisBattlesuit:Broken#0" value="Elite… retreat."/>
	<entry name="Tau/CrisisBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Hurt#0" value="We're losing systems!"/>
	<entry name="Tau/CrisisBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Idle#0" value="Always ready."/>
	<entry name="Tau/CrisisBattlesuit:Idle#1" value="We'll get it done."/>
	<entry name="Tau/CrisisBattlesuit:Idle#2" value="What do the Ethereals will?"/>
	<entry name="Tau/CrisisBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/CrisisBattlesuit:Shaken#0" value="We cannot hold this position."/>
	<entry name="Tau/CrisisBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Victory#0" value="That's why you send Battlesuits."/>
	<entry name="Tau/CrisisBattlesuit:Victory#1" value="Killing blow, delivered."/>
	<entry name="Tau/CrisisBattlesuit:Victory#2" value="Do they have anything… tougher?"/>
	<entry name="Tau/CrisisBattlesuit:Victory#3" value="Mark another one up to us, shas'el."/>
	<entry name="Tau/CrisisBattlesuit:VictoryCount" value="4"/>
	<entry name="Tau/Devilfish:Attack#0" value="Moving to the enemy."/>
	<entry name="Tau/Devilfish:Attack#1" value="Teaching our foes to fear all T'au."/>
	<entry name="Tau/Devilfish:Attack#2" value="Feel my fury!"/>
	<entry name="Tau/Devilfish:Attack#3" value="Firing all weapons."/>
	<entry name="Tau/Devilfish:AttackCount" value="4"/>
	<entry name="Tau/Devilfish:Broken#0" value="We're in too deep!"/>
	<entry name="Tau/Devilfish:BrokenCount" value="1"/>
	<entry name="Tau/Devilfish:Hurt#0" value="Can't take many more strikes like that."/>
	<entry name="Tau/Devilfish:HurtCount" value="1"/>
	<entry name="Tau/Devilfish:Idle#0" value="Ready to move on your command."/>
	<entry name="Tau/Devilfish:Idle#1" value="Just waiting around."/>
	<entry name="Tau/Devilfish:Idle#2" value="Checking chips—this one's fried."/>
	<entry name="Tau/Devilfish:IdleCount" value="3"/>
	<entry name="Tau/Devilfish:Shaken#0" value="They're turning the tide!"/>
	<entry name="Tau/Devilfish:ShakenCount" value="1"/>
	<entry name="Tau/Devilfish:Victory#0" value="Transporting them to a better place."/>
	<entry name="Tau/Devilfish:Victory#1" value="Shipping out."/>
	<entry name="Tau/Devilfish:VictoryCount" value="2"/>
	<entry name="Tau/Ethereal:Attack#0" value="This is for your own good."/>
	<entry name="Tau/Ethereal:Attack#1" value="For the T'au."/>
	<entry name="Tau/Ethereal:Attack#2" value="Why do you oppose us?"/>
	<entry name="Tau/Ethereal:AttackCount" value="3"/>
	<entry name="Tau/Ethereal:Broken#0" value="Perhaps… peace lies behind our own lines."/>
	<entry name="Tau/Ethereal:BrokenCount" value="1"/>
	<entry name="Tau/Ethereal:Hurt#0" value="Though my flesh is weak, my spirit will triumph!"/>
	<entry name="Tau/Ethereal:HurtCount" value="1"/>
	<entry name="Tau/Ethereal:Idle#0" value="I preach the Good."/>
	<entry name="Tau/Ethereal:Idle#1" value="In meditation, comes understanding."/>
	<entry name="Tau/Ethereal:Idle#2" value="Good alone is not enough—it is the greatest that we seek."/>
	<entry name="Tau/Ethereal:IdleCount" value="3"/>
	<entry name="Tau/Ethereal:Shaken#0" value="Defend me!"/>
	<entry name="Tau/Ethereal:ShakenCount" value="1"/>
	<entry name="Tau/Ethereal:Victory#0" value="Spreading the word."/>
	<entry name="Tau/Ethereal:Victory#1" value="I mourn every soul we lose."/>
	<entry name="Tau/Ethereal:Victory#2" value="This too, was for the good."/>
	<entry name="Tau/Ethereal:VictoryCount" value="3"/>
	<entry name="Tau/FireWarrior:Attack#0" value="Firing on targets!"/>
	<entry name="Tau/FireWarrior:Attack#1" value="In combat!"/>
	<entry name="Tau/FireWarrior:Attack#2" value="We're the Fire Caste!"/>
	<entry name="Tau/FireWarrior:Attack#3" value="Keep at range!"/>
	<entry name="Tau/FireWarrior:Attack#4" value="Support each other!"/>
	<entry name="Tau/FireWarrior:Attack#5" value="For Puretide's memory!"/>
	<entry name="Tau/FireWarrior:AttackCount" value="6"/>
	<entry name="Tau/FireWarrior:Broken#0" value="Fall back! Run!"/>
	<entry name="Tau/FireWarrior:BrokenCount" value="1"/>
	<entry name="Tau/FireWarrior:Hurt#0" value="We're losing troops!"/>
	<entry name="Tau/FireWarrior:HurtCount" value="1"/>
	<entry name="Tau/FireWarrior:Idle#0" value="Taking a break."/>
	<entry name="Tau/FireWarrior:Idle#1" value="Training, shas'el."/>
	<entry name="Tau/FireWarrior:Idle#2" value="I miss my Sept…"/>
	<entry name="Tau/FireWarrior:Idle#3" value="Let us never speak of sub-space again…"/>
	<entry name="Tau/FireWarrior:Idle#4" value="Ready for action, shas'el."/>
	<entry name="Tau/FireWarrior:IdleCount" value="5"/>
	<entry name="Tau/FireWarrior:Shaken#0" value="Don't let them near us!"/>
	<entry name="Tau/FireWarrior:ShakenCount" value="1"/>
	<entry name="Tau/FireWarrior:Victory#0" value="Superior firepower wins the day!"/>
	<entry name="Tau/FireWarrior:Victory#1" value="Proving the power of the Fire Caste!"/>
	<entry name="Tau/FireWarrior:VictoryCount" value="2"/>
	<entry name="Tau/GhostkeelBattlesuit" value="Tau/CrisisBattlesuit"/>
	<entry name="Tau/GravInhibitorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/GunDrone:Attack#0" value="Firing solution found."/>
	<entry name="Tau/GunDrone:Attack#1" value="Drone in action."/>
	<entry name="Tau/GunDrone:Attack#2" value="Fighting for the good!"/>
	<entry name="Tau/GunDrone:Attack#3" value="Limits of logic reached. Shots taken."/>
	<entry name="Tau/GunDrone:Attack#4" value="89% certainty enemies identified. Engaging."/>
	<entry name="Tau/GunDrone:AttackCount" value="5"/>
	<entry name="Tau/GunDrone:Broken#0" value="Preserving unit integrity."/>
	<entry name="Tau/GunDrone:BrokenCount" value="1"/>
	<entry name="Tau/GunDrone:Hurt#0" value="Operating capability down to 50%"/>
	<entry name="Tau/GunDrone:HurtCount" value="1"/>
	<entry name="Tau/GunDrone:Idle#0" value="Patrolling."/>
	<entry name="Tau/GunDrone:Idle#1" value="The Greater Good is logical."/>
	<entry name="Tau/GunDrone:Idle#2" value="Built by the Earth Caste."/>
	<entry name="Tau/GunDrone:Idle#3" value="Subroutines idling."/>
	<entry name="Tau/GunDrone:Idle#4" value="Ironing out gl-glitches."/>
	<entry name="Tau/GunDrone:IdleCount" value="5"/>
	<entry name="Tau/GunDrone:Shaken#0" value="Inadequate routines. Falling back on failsafes."/>
	<entry name="Tau/GunDrone:ShakenCount" value="1"/>
	<entry name="Tau/GunDrone:Victory#0" value="Objective achieved. Moving on."/>
	<entry name="Tau/GunDrone:VictoryCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Attack#0" value="Gunship firing, enemy falling."/>
	<entry name="Tau/HammerheadGunship:Attack#1" value="Going in low."/>
	<entry name="Tau/HammerheadGunship:Attack#2" value="Fire support in action"/>
	<entry name="Tau/HammerheadGunship:AttackCount" value="3"/>
	<entry name="Tau/HammerheadGunship:Broken#0" value="Gunship retreating."/>
	<entry name="Tau/HammerheadGunship:BrokenCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Hurt#0" value="They've damaged us but we can keep going."/>
	<entry name="Tau/HammerheadGunship:HurtCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Idle#0" value="Ready to fire at range."/>
	<entry name="Tau/HammerheadGunship:Idle#1" value="Let us know if you need this bird in the sky."/>
	<entry name="Tau/HammerheadGunship:Idle#2" value="The ky'husa? It's someone else's, sir."/>
	<entry name="Tau/HammerheadGunship:IdleCount" value="3"/>
	<entry name="Tau/HammerheadGunship:Shaken#0" value="We shouldn't be in the front line!"/>
	<entry name="Tau/HammerheadGunship:ShakenCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Victory#0" value="We are the first of the Mont'ka."/>
	<entry name="Tau/HammerheadGunship:Victory#1" value="Sweep complete."/>
	<entry name="Tau/HammerheadGunship:Victory#2" value="Searching for new targets."/>
	<entry name="Tau/HammerheadGunship:VictoryCount" value="3"/>
	<entry name="Tau/InterceptorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/MarkerDrone" value="Tau/GunDrone"/>
	<entry name="Tau/Pathfinder" value="Tau/FireWarrior"/>
	<entry name="Tau/Piranha:Attack#0" value="Firing burst cannon."/>
	<entry name="Tau/Piranha:Attack#1" value="Any Pathfinders need support?"/>
	<entry name="Tau/Piranha:Attack#2" value="Striking at speed."/>
	<entry name="Tau/Piranha:Attack#3" value="Commencing strafing run."/>
	<entry name="Tau/Piranha:AttackCount" value="4"/>
	<entry name="Tau/Piranha:Broken#0" value="Scout armour falling back."/>
	<entry name="Tau/Piranha:BrokenCount" value="1"/>
	<entry name="Tau/Piranha:Hurt#0" value="Evasion ineffective, serious damage sustained."/>
	<entry name="Tau/Piranha:HurtCount" value="1"/>
	<entry name="Tau/Piranha:Idle#0" value="Any word from the Pathfinders?"/>
	<entry name="Tau/Piranha:Idle#1" value="Reconnaisance waiting for orders."/>
	<entry name="Tau/Piranha:IdleCount" value="2"/>
	<entry name="Tau/Piranha:Shaken#0" value="Why is nothing working? Why-"/>
	<entry name="Tau/Piranha:ShakenCount" value="1"/>
	<entry name="Tau/Piranha:Victory#0" value="Target… shredded, shas'el."/>
	<entry name="Tau/Piranha:VictoryCount" value="1"/>
	<entry name="Tau/PulseAcceleratorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#0" value="Aerial support incoming."/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#1" value="Air Caste, for the Greater Good."/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#2" value="They haven't seen us coming…"/>
	<entry name="Tau/RazorsharkStrikeFighter:AttackCount" value="3"/>
	<entry name="Tau/RazorsharkStrikeFighter:Broken#0" value="In flight."/>
	<entry name="Tau/RazorsharkStrikeFighter:BrokenCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Hurt#0" value="Enemy fire is effective. Need new orders!"/>
	<entry name="Tau/RazorsharkStrikeFighter:HurtCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#0" value="Circling, awaiting orders."/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#1" value="Should we return to base?"/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#2" value="Any word on that promotion?"/>
	<entry name="Tau/RazorsharkStrikeFighter:IdleCount" value="3"/>
	<entry name="Tau/RazorsharkStrikeFighter:Shaken#0" value="Evasive manuevres!"/>
	<entry name="Tau/RazorsharkStrikeFighter:ShakenCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#0" value="Spotters report hostiles down."/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#1" value="Now that's air superiority."/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#2" value="Peeling off, mission accomplished."/>
	<entry name="Tau/RazorsharkStrikeFighter:VictoryCount" value="3"/>
	<entry name="Tau/ReconDrone" value="Tau/GunDrone"/>
	<entry name="Tau/RiptideBattlesuit:Attack#0" value="Firing everything."/>
	<entry name="Tau/RiptideBattlesuit:Attack#1" value="Why are they bothering shooting back?"/>
	<entry name="Tau/RiptideBattlesuit:Attack#2" value="They'd better not scratch the Fio'tak."/>
	<entry name="Tau/RiptideBattlesuit:AttackCount" value="3"/>
	<entry name="Tau/RiptideBattlesuit:Broken#0" value="I need shield drone cover!"/>
	<entry name="Tau/RiptideBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Hurt#0" value="Reactor damage, armour compromised. I can keep going."/>
	<entry name="Tau/RiptideBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Idle#0" value="You'd better have plans for me."/>
	<entry name="Tau/RiptideBattlesuit:Idle#1" value="ZZ…"/>
	<entry name="Tau/RiptideBattlesuit:Idle#2" value="So much firepower…"/>
	<entry name="Tau/RiptideBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/RiptideBattlesuit:Shaken#0" value="Best of the best. Best. Of. The. Best."/>
	<entry name="Tau/RiptideBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Victory#0" value="They're dead. Keep firing."/>
	<entry name="Tau/RiptideBattlesuit:Victory#1" value="Anyone see where my target went?"/>
	<entry name="Tau/RiptideBattlesuit:VictoryCount" value="2"/>
	<entry name="Tau/ShieldDrone" value="Tau/GunDrone"/>
	<entry name="Tau/ShieldedMissileDrone" value="Tau/GunDrone"/>
	<entry name="Tau/SkyRayGunship" value="Tau/HammerheadGunship"/>
	<entry name="Tau/StealthBattlesuit:Attack#0" value="They didn't see us coming."/>
	<entry name="Tau/StealthBattlesuit:Attack#1" value="From the shadows…"/>
	<entry name="Tau/StealthBattlesuit:Attack#2" value="Shas'el, we have them."/>
	<entry name="Tau/StealthBattlesuit:AttackCount" value="3"/>
	<entry name="Tau/StealthBattlesuit:Broken#0" value="Exfiltration request issued."/>
	<entry name="Tau/StealthBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Hurt#0" value="How are they hitting us?"/>
	<entry name="Tau/StealthBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Idle#0" value="Maintaining cover."/>
	<entry name="Tau/StealthBattlesuit:Idle#1" value="Stealth team, radio silence."/>
	<entry name="Tau/StealthBattlesuit:Idle#2" value="You can't be talking to us, we're not here."/>
	<entry name="Tau/StealthBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/StealthBattlesuit:Shaken#0" value="Are we in too deep?"/>
	<entry name="Tau/StealthBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Victory#0" value="Spreading terror."/>
	<entry name="Tau/StealthBattlesuit:Victory#1" value="Target ghosted."/>
	<entry name="Tau/StealthBattlesuit:Victory#2" value="We have our own way."/>
	<entry name="Tau/StealthBattlesuit:VictoryCount" value="3"/>
	<entry name="Tau/StealthDrone" value="Tau/GunDrone"/>
	<entry name="Tau/Stormsurge" value="Tau/RiptideBattlesuit"/>
	<entry name="Tau/SunSharkBomber" value="Tau/RazorsharkStrikeFighter"/>
	<entry name="Tyranids/Carnifex:Attack#0" value="Krrkkk!"/>
	<entry name="Tyranids/Carnifex:Attack#1" value="Rrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#2" value="Nnnnn!"/>
	<entry name="Tyranids/Carnifex:Attack#3" value="Srrr!"/>
	<entry name="Tyranids/Carnifex:Attack#4" value="Krk!"/>
	<entry name="Tyranids/Carnifex:Attack#5" value="Rrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#6" value="Nrrrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#7" value="Vrrrk!"/>
	<entry name="Tyranids/Carnifex:AttackCount" value="8"/>
	<entry name="Tyranids/Carnifex:Broken#0" value="…mmmm…"/>
	<entry name="Tyranids/Carnifex:Broken#1" value="…thhhh…"/>
	<entry name="Tyranids/Carnifex:Broken#2" value="…phhhh…"/>
	<entry name="Tyranids/Carnifex:Broken#3" value="…sssss…"/>
	<entry name="Tyranids/Carnifex:BrokenCount" value="4"/>
	<entry name="Tyranids/Carnifex:EnemyFaction:Necrons#0" value="Nrnnnnn."/>
	<entry name="Tyranids/Carnifex:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Tyranids/Carnifex:Hurt#0" value="Sssssss!"/>
	<entry name="Tyranids/Carnifex:HurtCount" value="1"/>
	<entry name="Tyranids/Carnifex:Idle#0" value="Krrck."/>
	<entry name="Tyranids/Carnifex:Idle#1" value="Nrrrk."/>
	<entry name="Tyranids/Carnifex:Idle#2" value="Rrrc."/>
	<entry name="Tyranids/Carnifex:Idle#3" value="Kckckc."/>
	<entry name="Tyranids/Carnifex:IdleCount" value="4"/>
	<entry name="Tyranids/Carnifex:Shaken#0" value="Rrhhrrr"/>
	<entry name="Tyranids/Carnifex:ShakenCount" value="1"/>
	<entry name="Tyranids/Carnifex:Victory#0" value="Hwlllllll!"/>
	<entry name="Tyranids/Carnifex:Victory#1" value="Iaiaiaia!"/>
	<entry name="Tyranids/Carnifex:Victory#2" value="Ulululu!"/>
	<entry name="Tyranids/Carnifex:Victory#3" value="Phphph!"/>
	<entry name="Tyranids/Carnifex:Victory#4" value="Ulllllla!"/>
	<entry name="Tyranids/Carnifex:VictoryCount" value="5"/>
	<entry name="Tyranids/Exocrine" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Gargoyle" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Haruspex" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Headquarters" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/HiveCrone" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/HiveTyrant" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Hormagaunt" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Lictor" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Malanthrope" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Ravener" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Termagant" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tervigon" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Trygon" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/TyranidPrime" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tyrannofex" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Warrior" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Zoanthrope" value="Tyranids/Carnifex"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#0" value="Feel ten thousand years of rage."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#1" value="In the name of the Dark Gods"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#2" value="Kneel before your lord!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#3" value="Die weaklings! Die scum!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#4" value="For the glory of Chaos!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Broken#0" value="I will die not here!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Broken#1" value="Back to the Eye!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:BrokenCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Hurt#0" value="What, you hurt ME?"/>
	<entry name="ChaosSpaceMarines/ChaosLord:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#0" value="Whilst we stand idle, Imperials yet live."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#1" value="Do you remember? His blood stained every wall."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#2" value="Such joy, in seeing an angel fallen."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#3" value="Screams or songs, they are one to me."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#4" value="Was it worth it, the damnation?"/>
	<entry name="ChaosSpaceMarines/ChaosLord:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Shaken#0" value="WHO DARES?"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Shaken#1" value="I will lie to avenge this."/>
	<entry name="ChaosSpaceMarines/ChaosLord:ShakenCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#0" value="I dedicate this death to the gods."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#1" value="Is that all? Dotards!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#2" value="Victory is ash in my mouth whilst the Imperium stands."/>
	<entry name="ChaosSpaceMarines/ChaosLord:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#0" value="What idiot sends us to war?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#1" value="Grind their bones to dust."/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#2" value="Drive faster!"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Broken#0" value="Flee! We'll kill them another day."/>
	<entry name="ChaosSpaceMarines/ChaosRhino:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Hurt#0" value="Who's putting holes in our armour?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Idle#0" value="Who'd be a Rhino driver?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Idle#1" value="Driving the Lord to war, yawwwn."/>
	<entry name="ChaosSpaceMarines/ChaosRhino:IdleCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Shaken#0" value="Damnation, for this?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Victory#0" value="You couldn't even beat a Rhino?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#0" value="Scream! Your doom is come!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#1" value="I am pure in my hatred. Come test it!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#2" value="I was the Emperor's son, you cannot stand against me!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#3" value="Hahahaha! DIE!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Broken#0" value="We are unbound and undone, we must flee!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Hurt#0" value="Dark Gods protect us."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#0" value="Can I stand another ten thousand years of this?"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#1" value="Pass me another civilian. I'm out of ink."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#2" value="Why do they still shelter their corpse-Emperor?"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#3" value="Endless war, endless joy."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Shaken#0" value="We are… losing?"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#0" value="So fall all enemies of Abaddon!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#1" value="Another step towards ruling this galaxy… our birthright."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#2" value="Your death is nothing to me."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#0" value="…The widening gyre…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#1" value="*gibbers*"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#2" value="ahhhhaahhhh"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Broken#0" value="…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Hurt#0" value="…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#0" value="…an eternity of madness..?"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#1" value="ehaahahaahaha"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#2" value="mmmm mmmm"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#3" value="…slouching to bedlam…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#4" value="nnnhhhrrr."/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Shaken#0" value="eh"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Victory#0" value="ach"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#0" value="The warp touches you, little ones."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#1" value="Feel the power of a god-to-be!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#2" value="Playthings, puppets, fall!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#3" value="Ahahahahaha."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Broken#0" value="It can't be, defeated, by-"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Hurt#0" value="YOU DARE CONTEND WITH ME"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#0" value="…what fools these mortals be!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#1" value="The physical plane… I have missed it."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#2" value="Strange, a world that doesn't shift form at whim."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#3" value="What a strange doom sits on Gladius."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#4" value="Come, slaves. Entertain your god!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#5" value="Odd, to think that these talons were once mortal hands…"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:IdleCount" value="6"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#0" value="I tore the hearts from a thousand men… and this is how you thank me?"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#1" value="The warp is calling…"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#2" value="…madness takes its toll—I must keep control."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#3" value="I have my own special hell for those like you!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:ShakenCount" value="4"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#0" value="Another skull for my collection… Khorne can beg."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#1" value="Heads without eyes… pleasing."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#2" value="Hold still, mortal—I want to enjoy your death."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#0" value="Yes, yes, die mortals, die die."/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#1" value="Closer, claws, calling, come!"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#2" value="Meet our gods, go meet our gods."/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#3" value="For Chaos, blood, skulls, for Chaos."/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#4" value="Tear and rend… mortality is joy."/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#5" value="Flense, cut, smash, joy, shred!"/>
	<entry name="ChaosSpaceMarines/Defiler:AttackCount" value="6"/>
	<entry name="ChaosSpaceMarines/Defiler:Broken#0" value="Dark gods!"/>
	<entry name="ChaosSpaceMarines/Defiler:Broken#1" value="Losing, losing, hahaha, losing!"/>
	<entry name="ChaosSpaceMarines/Defiler:BrokenCount" value="2"/>
	<entry name="ChaosSpaceMarines/Defiler:Hurt#0" value="Yes, back to the warp, yes, free us."/>
	<entry name="ChaosSpaceMarines/Defiler:Hurt#1" value="Smash my cage, free daemon-I"/>
	<entry name="ChaosSpaceMarines/Defiler:HurtCount" value="2"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#0" value="Warp, where, warp gone!"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#1" value="Mustn't kill, runebound, sorcery. Mustn't… want to."/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#2" value="Why not kill, kill, kill, where kill?"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#3" value="Old ones stink here. Stink dead gods, stink."/>
	<entry name="ChaosSpaceMarines/Defiler:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/Defiler:Shaken#0" value="Haha, warp is calling, freeeee."/>
	<entry name="ChaosSpaceMarines/Defiler:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#0" value="Break so easily, snap, sad, more?"/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#1" value="Dead so soon."/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#2" value="To the warp, to the masters' glory."/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#3" value="Little soul. Forge is hot, burning, waiting. For you."/>
	<entry name="ChaosSpaceMarines/Defiler:VictoryCount" value="4"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#0" value="Annihilation!"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#1" value="SHOOT BACK COWARDS"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#2" value="One bolt, two bolts, three bolts, four-"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#3" value="For the glory of Chaos! For the Dark Mechanicum! For the joy of shooting!"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#4" value="From hell, we fire!"/>
	<entry name="ChaosSpaceMarines/Havoc:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/Havoc:Broken#0" value="Backfired…"/>
	<entry name="ChaosSpaceMarines/Havoc:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Hurt#0" value="Not dead, keep shooting."/>
	<entry name="ChaosSpaceMarines/Havoc:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#0" value="When you talk with guns, people listen. When you talk to guns, people run."/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#1" value="My blood is plasma."/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#2" value="Bless the Warp, that gives us such plenty."/>
	<entry name="ChaosSpaceMarines/Havoc:IdleCount" value="3"/>
	<entry name="ChaosSpaceMarines/Havoc:Shaken#0" value="Our guns, not yours. From our cold dead fingers…"/>
	<entry name="ChaosSpaceMarines/Havoc:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Victory#0" value="Shot into peace."/>
	<entry name="ChaosSpaceMarines/Havoc:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#0" value="HhhrrraaaaaHHHH!"/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#1" value="Graaarh."/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#2" value="Arrrrrhhhh."/>
	<entry name="ChaosSpaceMarines/Heldrake:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/Heldrake:Broken#0" value="Eeeeeeeeeee!"/>
	<entry name="ChaosSpaceMarines/Heldrake:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Hurt#0" value="Arkh! Arkh!"/>
	<entry name="ChaosSpaceMarines/Heldrake:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#0" value="Man… once."/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#1" value="Grrrrhhh."/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#2" value="Rrrrrrr…"/>
	<entry name="ChaosSpaceMarines/Heldrake:IdleCount" value="3"/>
	<entry name="ChaosSpaceMarines/Heldrake:Shaken#0" value="Eeeeeekh."/>
	<entry name="ChaosSpaceMarines/Heldrake:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Victory#0" value="HhhrrrrrraaaaaaaHHHHHH!"/>
	<entry name="ChaosSpaceMarines/Heldrake:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#0" value="Blood for the Blood God!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#1" value="Khorne wants your skull."/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#2" value="My axe longs to taste your blood!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#3" value="Stand and fight, cowards!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#4" value="There is no honor in a warrior's death!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Broken#0" value="Khorne will doom us!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Hurt#0" value="My… My blood for the Blood GOD!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Idle#0" value="WHY DO WE NOT FIGHT?"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:IdleCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Shaken#0" value="We will not die easily!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#0" value="BLOOD!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#1" value="KHORNE CALLS YOU"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#2" value="ALL ARE SKULLS"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#0" value="By your true name, I compel you."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#1" value="Wherefore your fear? A breach is near."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#2" value="My stave hungers, your soul lingers."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Broken#0" value="What lives but will not die?"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Hurt#0" value="Is this my bone? Fie, I see it none."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#0" value="Useless men and ghosts… we need better hosts."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#1" value="Soon the athame, then Fatewalker's true name…"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#2" value="Possession is nine tenths of the lore…"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#3" value="They will taste the planet's heart, before our time is done."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Shaken#0" value="What is fear, the mindkiller?"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Victory#0" value="My demons whisper, each to each. They will whisper to you."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Venomcrawler" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Attack#0" value="…prey…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:AttackCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Broken#0" value="…fear…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Hurt#0" value="…pain…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Idle#0" value="…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:IdleCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Shaken#0" value="…fear…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Victory#0" value="…death…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#0" value="Target diagnostic: you… are not perfect."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#1" value="Cogitators, mechatendrils, armaments: deployed."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#2" value="GOTO target elimination. Execute."/>
	<entry name="ChaosSpaceMarines/Warpsmith:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Broken#0" value="Damage limitation programme initiate."/>
	<entry name="ChaosSpaceMarines/Warpsmith:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Hurt#0" value="Diagnostic: organical/mechanic ERROR"/>
	<entry name="ChaosSpaceMarines/Warpsmith:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#0" value="Ridiculous Warsmiths. Ridiculous!"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#1" value="Man, machine, daemon—together, perfection."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#2" value="Dear mechatendrils…"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#3" value="Error: emotional core recovery. Delete."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#4" value="Soul Forge, gods, we are your weapons."/>
	<entry name="ChaosSpaceMarines/Warpsmith:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Shaken#0" value="Reports: damage sustained."/>
	<entry name="ChaosSpaceMarines/Warpsmith:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Victory#0" value="Assessment: opposition deactivated. Predictable."/>
	<entry name="ChaosSpaceMarines/Warpsmith:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Helbrute" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/Maulerfiend" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/Obliterator" value="ChaosSpaceMarines/Havoc"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#0" value="So fall all enemies of the Imperium"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#1" value="Heretics, xenos: all shall fall."/>
	<entry name="AstraMilitarum/TempestusScion:Attack#2" value="Hellguns at the ready…"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#3" value="Our lives for Terra!"/>
	<entry name="AstraMilitarum/TempestusScion:AttackCount" value="4"/>
	<entry name="AstraMilitarum/TempestusScion:Broken#0" value="The shame of it! Stormtroopers retreating!"/>
	<entry name="AstraMilitarum/TempestusScion:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Hurt#0" value="I gladly give my life for my Emperor."/>
	<entry name="AstraMilitarum/TempestusScion:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Idle#0" value="They call us 'Glory Boys'. The only glory we seek is that of the Emperor."/>
	<entry name="AstraMilitarum/TempestusScion:Idle#1" value="The Regimental Standard? Not an obligation, but a joy to read."/>
	<entry name="AstraMilitarum/TempestusScion:Idle#2" value="Have you read this article about Marbo? How can a hero disregard Munitorum regulations?"/>
	<entry name="AstraMilitarum/TempestusScion:Idle#3" value="In the mess, R&R. This is the life."/>
	<entry name="AstraMilitarum/TempestusScion:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TempestusScion:Shaken#0" value="I have passed the Trials of Compliance: I will not run."/>
	<entry name="AstraMilitarum/TempestusScion:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Victory#0" value="Let the Guard celebrate. Our war is unending."/>
	<entry name="AstraMilitarum/TempestusScion:VictoryCount" value="1"/>
	<entry name="Necrons/FlayedOne:Attack#0" value="…flesh"/>
	<entry name="Necrons/FlayedOne:Attack#1" value="-tear-"/>
	<entry name="Necrons/FlayedOne:Attack#2" value="…rend-rip-tear…"/>
	<entry name="Necrons/FlayedOne:AttackCount" value="3"/>
	<entry name="Necrons/FlayedOne:Broken#0" value="…esssscape…"/>
	<entry name="Necrons/FlayedOne:BrokenCount" value="1"/>
	<entry name="Necrons/FlayedOne:Hurt#0" value="…flesh wound…"/>
	<entry name="Necrons/FlayedOne:HurtCount" value="1"/>
	<entry name="Necrons/FlayedOne:Idle#0" value="…hunger…"/>
	<entry name="Necrons/FlayedOne:Idle#1" value="…linger…"/>
	<entry name="Necrons/FlayedOne:Idle#2" value="…langour…"/>
	<entry name="Necrons/FlayedOne:IdleCount" value="3"/>
	<entry name="Necrons/FlayedOne:Shaken#0" value="…Llandu'gor…!"/>
	<entry name="Necrons/FlayedOne:ShakenCount" value="1"/>
	<entry name="Necrons/FlayedOne:Victory#0" value="…live again…"/>
	<entry name="Necrons/FlayedOne:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Attack#0" value="Supporting infantry with fire."/>
	<entry name="AstraMilitarum/Chimera:Attack#1" value="Transport assaulting."/>
	<entry name="AstraMilitarum/Chimera:Attack#2" value="Firing everything we've got."/>
	<entry name="AstraMilitarum/Chimera:AttackCount" value="3"/>
	<entry name="AstraMilitarum/Chimera:Broken#0" value="Fast armour retreating fast…"/>
	<entry name="AstraMilitarum/Chimera:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Hurt#0" value="Heavy armour needed!"/>
	<entry name="AstraMilitarum/Chimera:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Idle#0" value="Awaiting orders"/>
	<entry name="AstraMilitarum/Chimera:Idle#1" value="Anyone to move?"/>
	<entry name="AstraMilitarum/Chimera:Idle#2" value="Whose turn is it to polish the lens on the multilaser?"/>
	<entry name="AstraMilitarum/Chimera:IdleCount" value="3"/>
	<entry name="AstraMilitarum/Chimera:Shaken#0" value="Get this hunk outta here!"/>
	<entry name="AstraMilitarum/Chimera:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Victory#0" value="Tank shocked."/>
	<entry name="AstraMilitarum/Chimera:VictoryCount" value="1"/>
	<entry name="Orks/Warbiker:Attack#0" value="RED WUNZ GO FASTA!"/>
	<entry name="Orks/Warbiker:Attack#1" value="Dakka dakka dakka!"/>
	<entry name="Orks/Warbiker:Attack#2" value="Shoot 'em all!"/>
	<entry name="Orks/Warbiker:AttackCount" value="3"/>
	<entry name="Orks/Warbiker:Broken#0" value="Fasta fasta fasta fasta…"/>
	<entry name="Orks/Warbiker:BrokenCount" value="1"/>
	<entry name="Orks/Warbiker:Hurt#0" value="Oi, Baz, was dat yer wheel or mine?"/>
	<entry name="Orks/Warbiker:Hurt#1" value="We don't need does bitz anyhow."/>
	<entry name="Orks/Warbiker:HurtCount" value="2"/>
	<entry name="Orks/Warbiker:Idle#0" value="move us, fer zog's sake."/>
	<entry name="Orks/Warbiker:Idle#1" value="Vroom, vroom, dakka, dakka. Does were the days."/>
	<entry name="Orks/Warbiker:IdleCount" value="2"/>
	<entry name="Orks/Warbiker:Shaken#0" value="Need sum blue paint, fast!"/>
	<entry name="Orks/Warbiker:ShakenCount" value="1"/>
	<entry name="Orks/Warbiker:Victory#0" value="WAAAGH! Kult of Speed! WAAAGH!"/>
	<entry name="Orks/Warbiker:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#0" value="They seek death at our hands."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#1" value="Crush their skulls 'neath our treads!"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#2" value="They seek death at our hands."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Broken#0" value="The Dark Gods will damn us for this…"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Hurt#0" value="We will not die here, weaklings!"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Idle#0" value="Holding up pretty well for ten thousand years without a service."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Idle#1" value="Dark gods preserve us."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:IdleCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Shaken#0" value="Save yourselves…"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Victory#0" value="They're dead. Predictable."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:VictoryCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Attack#0" value="Firing railgun."/>
	<entry name="Tau/TidewallGunrig:Attack#1" value="Emplacement activated."/>
	<entry name="Tau/TidewallGunrig:Attack#2" value="Stabilized and ready to fire."/>
	<entry name="Tau/TidewallGunrig:AttackCount" value="3"/>
	<entry name="Tau/TidewallGunrig:Broken#0" value="Pulling the gunrig back."/>
	<entry name="Tau/TidewallGunrig:BrokenCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Hurt#0" value="Shieldwall failing, emplacement compromised."/>
	<entry name="Tau/TidewallGunrig:HurtCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Idle#0" value="Got any tanks for us to hit?"/>
	<entry name="Tau/TidewallGunrig:Idle#1" value="Mobile emplacement ready for action."/>
	<entry name="Tau/TidewallGunrig:IdleCount" value="2"/>
	<entry name="Tau/TidewallGunrig:Shaken#0" value="The Tidewall can't take this amount of fire!"/>
	<entry name="Tau/TidewallGunrig:ShakenCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Victory#0" value="Target… atomised."/>
	<entry name="Tau/TidewallGunrig:VictoryCount" value="1"/>
	<entry name="Tyranids/ScythedHierodule" value="Tyranids/Carnifex"/>
	<entry name="SpaceMarines/Razorback" value="SpaceMarines/Hunter"/>
	<entry name="Neutral/Umbra:Attack#0" value="…llll…"/>
	<entry name="Neutral/Umbra:Attack#1" value="(linger)"/>
	<entry name="Neutral/Umbra:Attack#2" value="Linger…"/>
	<entry name="Neutral/Umbra:Attack#3" value="…inger…"/>
	<entry name="Neutral/Umbra:AttackCount" value="4"/>
	<entry name="Neutral/Umbra:Broken#0" value="Linger…"/>
	<entry name="Neutral/Umbra:BrokenCount" value="1"/>
	<entry name="Neutral/Umbra:Hurt#0" value="LIN-"/>
	<entry name="Neutral/Umbra:HurtCount" value="1"/>
	<entry name="Neutral/Umbra:Idle#0" value="…"/>
	<entry name="Neutral/Umbra:Idle#1" value="…linger"/>
	<entry name="Neutral/Umbra:Idle#2" value="linger…"/>
	<entry name="Neutral/Umbra:Idle#3" value="…ng…"/>
	<entry name="Neutral/Umbra:IdleCount" value="4"/>
	<entry name="Neutral/Umbra:Shaken#0" value="lin…ger…"/>
	<entry name="Neutral/Umbra:ShakenCount" value="1"/>
	<entry name="Neutral/Umbra:Victory#0" value="LINGER"/>
	<entry name="Neutral/Umbra:VictoryCount" value="1"/>
	<entry name="Eldar/Wraithblade:Attack#0" value="…the enemy will fall…"/>
	<entry name="Eldar/Wraithblade:Attack#1" value="…join the dead…"/>
	<entry name="Eldar/Wraithblade:Attack#2" value="…for our Craftworld…"/>
	<entry name="Eldar/Wraithblade:Attack#3" value="…for the Aeldari…"/>
	<entry name="Eldar/Wraithblade:AttackCount" value="4"/>
	<entry name="Eldar/Wraithblade:Broken#0" value="…we fear She Who Thirsts…"/>
	<entry name="Eldar/Wraithblade:BrokenCount" value="1"/>
	<entry name="Eldar/Wraithblade:Hurt#0" value="…to die, again?"/>
	<entry name="Eldar/Wraithblade:HurtCount" value="1"/>
	<entry name="Eldar/Wraithblade:Idle#0" value="…need guidance…"/>
	<entry name="Eldar/Wraithblade:Idle#1" value="…we are dead…"/>
	<entry name="Eldar/Wraithblade:Idle#2" value="…let us rest…"/>
	<entry name="Eldar/Wraithblade:Idle#3" value="…we live for war…"/>
	<entry name="Eldar/Wraithblade:IdleCount" value="4"/>
	<entry name="Eldar/Wraithblade:Shaken#0" value="…fear is beyond us…"/>
	<entry name="Eldar/Wraithblade:ShakenCount" value="1"/>
	<entry name="Eldar/Wraithblade:Victory#0" value="…they have joined us…"/>
	<entry name="Eldar/Wraithblade:VictoryCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Attack#0" value="Firing all systems"/>
	<entry name="Eldar/WaveSerpent:Attack#1" value="Yes, we're attacking"/>
	<entry name="Eldar/WaveSerpent:Attack#2" value="Should we fire our Serpent Shield?"/>
	<entry name="Eldar/WaveSerpent:Attack#3" value="Transport in combat."/>
	<entry name="Eldar/WaveSerpent:AttackCount" value="4"/>
	<entry name="Eldar/WaveSerpent:Broken#0" value="Save yourselves!"/>
	<entry name="Eldar/WaveSerpent:BrokenCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Hurt#0" value="Shield penetrated."/>
	<entry name="Eldar/WaveSerpent:HurtCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Idle#0" value="Ready to move."/>
	<entry name="Eldar/WaveSerpent:Idle#1" value="Coiled, ready to spring."/>
	<entry name="Eldar/WaveSerpent:Idle#2" value="Who needs moving?"/>
	<entry name="Eldar/WaveSerpent:Idle#3" value="What a wonderful world."/>
	<entry name="Eldar/WaveSerpent:IdleCount" value="4"/>
	<entry name="Eldar/WaveSerpent:Shaken#0" value="Get us out of here!"/>
	<entry name="Eldar/WaveSerpent:ShakenCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Victory#0" value="Load up for the next target."/>
	<entry name="Eldar/WaveSerpent:VictoryCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#0" value="Guardian battery firing."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#1" value="Weaving shadows."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#2" value="Artillery ready to move."/>
	<entry name="Eldar/VaulsWrathSupportBattery:AttackCount" value="3"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Broken#0" value="We cannot hold this alone."/>
	<entry name="Eldar/VaulsWrathSupportBattery:BrokenCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Hurt#0" value="We will not survive."/>
	<entry name="Eldar/VaulsWrathSupportBattery:HurtCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#0" value="Spooling monofilament."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#1" value="Checking the anti-gravitic generators."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#2" value="Let's support from the back."/>
	<entry name="Eldar/VaulsWrathSupportBattery:IdleCount" value="3"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Shaken#0" value="We must disengage!"/>
	<entry name="Eldar/VaulsWrathSupportBattery:ShakenCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Victory#0" value="We've… dissolved… the enemy formation."/>
	<entry name="Eldar/VaulsWrathSupportBattery:VictoryCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Attack#0" value="We strike faster than the eye can see."/>
	<entry name="Eldar/ShiningSpear:Attack#1" value="For Drastanta!"/>
	<entry name="Eldar/ShiningSpear:Attack#2" value="They pale before our virtue."/>
	<entry name="Eldar/ShiningSpear:Attack#3" value="We are the spear of Khaine!"/>
	<entry name="Eldar/ShiningSpear:AttackCount" value="4"/>
	<entry name="Eldar/ShiningSpear:Broken#0" value="The spear is… broken."/>
	<entry name="Eldar/ShiningSpear:BrokenCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Hurt#0" value="We must go faster."/>
	<entry name="Eldar/ShiningSpear:HurtCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Idle#0" value="We are legend, manifest."/>
	<entry name="Eldar/ShiningSpear:Idle#1" value="We wait to charge."/>
	<entry name="Eldar/ShiningSpear:Idle#2" value="Spears at the ready."/>
	<entry name="Eldar/ShiningSpear:Idle#3" value="I long to remove this War Mask."/>
	<entry name="Eldar/ShiningSpear:IdleCount" value="4"/>
	<entry name="Eldar/ShiningSpear:Shaken#0" value="We ride through the valley of fear."/>
	<entry name="Eldar/ShiningSpear:ShakenCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Victory#0" value="They didn't see us coming."/>
	<entry name="Eldar/ShiningSpear:VictoryCount" value="1"/>
	<entry name="Eldar/Ranger:Attack#0" value="Choosing targets…"/>
	<entry name="Eldar/Ranger:Attack#1" value="They will die before they know we fired."/>
	<entry name="Eldar/Ranger:Attack#2" value="My long rifle whispers death."/>
	<entry name="Eldar/Ranger:Attack#3" value="We. Do. Not. Miss."/>
	<entry name="Eldar/Ranger:AttackCount" value="4"/>
	<entry name="Eldar/Ranger:Broken#0" value="Fall back! They come!"/>
	<entry name="Eldar/Ranger:BrokenCount" value="1"/>
	<entry name="Eldar/Ranger:Hurt#0" value="We are walking in the dark…"/>
	<entry name="Eldar/Ranger:HurtCount" value="1"/>
	<entry name="Eldar/Ranger:Idle#0" value="Our exile is done."/>
	<entry name="Eldar/Ranger:Idle#1" value="Our cameleoline shields us."/>
	<entry name="Eldar/Ranger:Idle#2" value="It is good to be somewhere new."/>
	<entry name="Eldar/Ranger:Idle#3" value="Come, listen to our tales of the galaxy!"/>
	<entry name="Eldar/Ranger:IdleCount" value="4"/>
	<entry name="Eldar/Ranger:Shaken#0" value="Away from the frontline, quickly."/>
	<entry name="Eldar/Ranger:ShakenCount" value="1"/>
	<entry name="Eldar/Ranger:Victory#0" value="They are gone and we we are free to range once more."/>
	<entry name="Eldar/Ranger:VictoryCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Attack#0" value="Our cries speak your death."/>
	<entry name="Eldar/HowlingBanshee:Attack#1" value="They fall like grass before our blades."/>
	<entry name="Eldar/HowlingBanshee:Attack#2" value="We call them to a final judgement."/>
	<entry name="Eldar/HowlingBanshee:Attack#3" value="Swiftly!"/>
	<entry name="Eldar/HowlingBanshee:AttackCount" value="4"/>
	<entry name="Eldar/HowlingBanshee:Broken#0" value="Hear our lamentations!"/>
	<entry name="Eldar/HowlingBanshee:BrokenCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Hurt#0" value="The old familiar sting!"/>
	<entry name="Eldar/HowlingBanshee:HurtCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Idle#0" value="We howl their doom."/>
	<entry name="Eldar/HowlingBanshee:Idle#1" value="Would that our foes would test our blades, now!"/>
	<entry name="Eldar/HowlingBanshee:Idle#2" value="We are the children of Morai-Heg."/>
	<entry name="Eldar/HowlingBanshee:Idle#3" value="Our song is death."/>
	<entry name="Eldar/HowlingBanshee:IdleCount" value="4"/>
	<entry name="Eldar/HowlingBanshee:Shaken#0" value="They interrupt our song!"/>
	<entry name="Eldar/HowlingBanshee:ShakenCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Victory#0" value="And only whispers greet their demise."/>
	<entry name="Eldar/HowlingBanshee:VictoryCount" value="1"/>
	<entry name="Eldar/Headquarters:Attack#0" value="Defending the webway"/>
	<entry name="Eldar/Headquarters:Attack#1" value="You shall not pass!"/>
	<entry name="Eldar/Headquarters:Attack#2" value="Redoubt taking fire."/>
	<entry name="Eldar/Headquarters:Attack#3" value="Guardians of the webway."/>
	<entry name="Eldar/Headquarters:AttackCount" value="4"/>
	<entry name="Eldar/Headquarters:Broken#0" value="They are breaking through!"/>
	<entry name="Eldar/Headquarters:BrokenCount" value="1"/>
	<entry name="Eldar/Headquarters:Hurt#0" value="Khaine aid us!"/>
	<entry name="Eldar/Headquarters:HurtCount" value="1"/>
	<entry name="Eldar/Headquarters:Idle#0" value="Watching the Webway."/>
	<entry name="Eldar/Headquarters:Idle#1" value="We stand ready, Seer."/>
	<entry name="Eldar/Headquarters:Idle#2" value="I long to return to my craftworld, to doff this mask."/>
	<entry name="Eldar/Headquarters:Idle#3" value="Why are we here?"/>
	<entry name="Eldar/Headquarters:IdleCount" value="4"/>
	<entry name="Eldar/Headquarters:Shaken#0" value="I fear death and the Great Enemy."/>
	<entry name="Eldar/Headquarters:ShakenCount" value="1"/>
	<entry name="Eldar/Headquarters:Victory#0" value="This gate will not close, yet."/>
	<entry name="Eldar/Headquarters:VictoryCount" value="1"/>
	<entry name="Eldar/Guardian:Attack#0" value="Duty calls us."/>
	<entry name="Eldar/Guardian:Attack#1" value="We must not fall."/>
	<entry name="Eldar/Guardian:Attack#2" value="Our people stand as one."/>
	<entry name="Eldar/Guardian:Attack#3" value="Shurikens away."/>
	<entry name="Eldar/Guardian:AttackCount" value="4"/>
	<entry name="Eldar/Guardian:Broken#0" value="Is this the end…?"/>
	<entry name="Eldar/Guardian:BrokenCount" value="1"/>
	<entry name="Eldar/Guardian:Hurt#0" value="Our doom is upon us."/>
	<entry name="Eldar/Guardian:HurtCount" value="1"/>
	<entry name="Eldar/Guardian:Idle#0" value="War is… intense."/>
	<entry name="Eldar/Guardian:Idle#1" value="When we will return to the crystal halls?"/>
	<entry name="Eldar/Guardian:Idle#2" value="Asuryan, protect us."/>
	<entry name="Eldar/Guardian:Idle#3" value="Seer, I hope your sight is true."/>
	<entry name="Eldar/Guardian:IdleCount" value="4"/>
	<entry name="Eldar/Guardian:Shaken#0" value="Isha preserve us!"/>
	<entry name="Eldar/Guardian:ShakenCount" value="1"/>
	<entry name="Eldar/Guardian:Victory#0" value="Arrogant, to stand against the Aeldari."/>
	<entry name="Eldar/Guardian:VictoryCount" value="1"/>
	<entry name="Eldar/FirePrism:Attack#0" value="We shall cut them down."/>
	<entry name="Eldar/FirePrism:Attack#1" value="They cannot withstand our fire."/>
	<entry name="Eldar/FirePrism:Attack#2" value="They melt away."/>
	<entry name="Eldar/FirePrism:Attack#3" value="Weapons of the ancients, do not fail us."/>
	<entry name="Eldar/FirePrism:AttackCount" value="4"/>
	<entry name="Eldar/FirePrism:Broken#0" value="…this cannot be."/>
	<entry name="Eldar/FirePrism:BrokenCount" value="1"/>
	<entry name="Eldar/FirePrism:Hurt#0" value="This damage is of no consequence."/>
	<entry name="Eldar/FirePrism:HurtCount" value="1"/>
	<entry name="Eldar/FirePrism:Idle#0" value="Use us."/>
	<entry name="Eldar/FirePrism:Idle#1" value="Ancient Vaul designed this machine."/>
	<entry name="Eldar/FirePrism:Idle#2" value="Why must we use such weaponry?"/>
	<entry name="Eldar/FirePrism:Idle#3" value="The craftworlds are so far away."/>
	<entry name="Eldar/FirePrism:IdleCount" value="4"/>
	<entry name="Eldar/FirePrism:Shaken#0" value="We can take their fire."/>
	<entry name="Eldar/FirePrism:ShakenCount" value="1"/>
	<entry name="Eldar/FirePrism:Victory#0" value="As expected, they are defeated."/>
	<entry name="Eldar/FirePrism:VictoryCount" value="1"/>
	<entry name="Eldar/FireDragon:Attack#0" value="Burn!"/>
	<entry name="Eldar/FireDragon:Attack#1" value="Ancestral flames unleashed!"/>
	<entry name="Eldar/FireDragon:Attack#2" value="We are rage, fiery rage!"/>
	<entry name="Eldar/FireDragon:Attack#3" value="Come closer, little tanks…"/>
	<entry name="Eldar/FireDragon:AttackCount" value="4"/>
	<entry name="Eldar/FireDragon:Broken#0" value="The dream of fire fades…"/>
	<entry name="Eldar/FireDragon:BrokenCount" value="1"/>
	<entry name="Eldar/FireDragon:Hurt#0" value="We are burnt ourselves!"/>
	<entry name="Eldar/FireDragon:HurtCount" value="1"/>
	<entry name="Eldar/FireDragon:Idle#0" value="Let us tell tale of firestorms and ash."/>
	<entry name="Eldar/FireDragon:Idle#1" value="Cinders and fury."/>
	<entry name="Eldar/FireDragon:Idle#2" value="A world encased in flame."/>
	<entry name="Eldar/FireDragon:Idle#3" value="There is nobility in incineration, is there not?"/>
	<entry name="Eldar/FireDragon:IdleCount" value="4"/>
	<entry name="Eldar/FireDragon:Shaken#0" value="We are quite put out."/>
	<entry name="Eldar/FireDragon:ShakenCount" value="1"/>
	<entry name="Eldar/FireDragon:Victory#0" value="Their face in death is… ashen."/>
	<entry name="Eldar/FireDragon:VictoryCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Attack#0" value="Kurnous guides us!"/>
	<entry name="Eldar/CrimsonHunter:Attack#1" value="We hunt!"/>
	<entry name="Eldar/CrimsonHunter:Attack#2" value="From the air, we dispense… lessons."/>
	<entry name="Eldar/CrimsonHunter:Attack#3" value="We are the blinding blades!"/>
	<entry name="Eldar/CrimsonHunter:AttackCount" value="4"/>
	<entry name="Eldar/CrimsonHunter:Broken#0" value="This is not retreat."/>
	<entry name="Eldar/CrimsonHunter:BrokenCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Hurt#0" value="They… they hit us?"/>
	<entry name="Eldar/CrimsonHunter:HurtCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Idle#0" value="I have a desire… for speed."/>
	<entry name="Eldar/CrimsonHunter:Idle#1" value="Kurnous desires action."/>
	<entry name="Eldar/CrimsonHunter:Idle#2" value="This environment is rich in targets…"/>
	<entry name="Eldar/CrimsonHunter:Idle#3" value="Nothing is more deadly than arrogance in battle."/>
	<entry name="Eldar/CrimsonHunter:IdleCount" value="4"/>
	<entry name="Eldar/CrimsonHunter:Shaken#0" value="No time to think!"/>
	<entry name="Eldar/CrimsonHunter:ShakenCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Victory#0" value="Their ego overtook them."/>
	<entry name="Eldar/CrimsonHunter:VictoryCount" value="1"/>
	<entry name="Eldar/HemlockWraithfighter" value="Eldar/CrimsonHunter"/>
	<entry name="Eldar/Scorpion" value="Eldar/FirePrism"/>
	<entry name="Eldar/WarWalker" value="Eldar/Guardian"/>
	<entry name="Eldar/Wraithknight" value="Eldar/Wraithblade"/>
	<entry name="Eldar/Autarch:Attack#0" value="Face all the aspects!"/>
	<entry name="Eldar/Autarch:Attack#1" value="I know Khaine!"/>
	<entry name="Eldar/Autarch:Attack#2" value="Fall by my hand!"/>
	<entry name="Eldar/Autarch:Attack#3" value="I am war incarnate!"/>
	<entry name="Eldar/Autarch:AttackCount" value="4"/>
	<entry name="Eldar/Autarch:Broken#0" value="Retreat is a part of war."/>
	<entry name="Eldar/Autarch:BrokenCount" value="1"/>
	<entry name="Eldar/Autarch:Hurt#0" value="I… may need another incarnation."/>
	<entry name="Eldar/Autarch:HurtCount" value="1"/>
	<entry name="Eldar/Autarch:Idle#0" value="Cegorach makes work for idle Autarchs."/>
	<entry name="Eldar/Autarch:Idle#1" value="I shall now recite the litany of battle…"/>
	<entry name="Eldar/Autarch:Idle#2" value="I am lost to war."/>
	<entry name="Eldar/Autarch:Idle#3" value="There is no peace in my mind."/>
	<entry name="Eldar/Autarch:IdleCount" value="4"/>
	<entry name="Eldar/Autarch:Shaken#0" value="Ha! They test me!"/>
	<entry name="Eldar/Autarch:ShakenCount" value="1"/>
	<entry name="Eldar/Autarch:Victory#0" value="They will not try my patience again."/>
	<entry name="Eldar/Autarch:VictoryCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Attack#0" value="I shape your future… it is short."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#1" value="With me, Shining Spears."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#2" value="My witchblade hungers."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#3" value="I am the helmsman of my people."/>
	<entry name="Eldar/FarseerSkyrunner:AttackCount" value="4"/>
	<entry name="Eldar/FarseerSkyrunner:Broken#0" value="I see no future."/>
	<entry name="Eldar/FarseerSkyrunner:BrokenCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Hurt#0" value="I must go on!"/>
	<entry name="Eldar/FarseerSkyrunner:HurtCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Idle#0" value="The skeins draw tight."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#1" value="The future is always in motion."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#2" value="I see the future and shape it too."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#3" value="I long for the crystal and the circuit."/>
	<entry name="Eldar/FarseerSkyrunner:IdleCount" value="4"/>
	<entry name="Eldar/FarseerSkyrunner:Shaken#0" value="It was not meant to be this way!"/>
	<entry name="Eldar/FarseerSkyrunner:ShakenCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Victory#0" value="Fallen. As foreseen."/>
	<entry name="Eldar/FarseerSkyrunner:VictoryCount" value="1"/>
	<entry name="Eldar/Spiritseer:Attack#0" value="There is no peace."/>
	<entry name="Eldar/Spiritseer:Attack#1" value="The dead take you!"/>
	<entry name="Eldar/Spiritseer:Attack#2" value="Fear my blade."/>
	<entry name="Eldar/Spiritseer:Attack#3" value="I am of an ancient race!"/>
	<entry name="Eldar/Spiritseer:AttackCount" value="4"/>
	<entry name="Eldar/Spiritseer:Broken#0" value="I retreat, lest I join my charges."/>
	<entry name="Eldar/Spiritseer:BrokenCount" value="1"/>
	<entry name="Eldar/Spiritseer:Hurt#0" value="My life drains into the crystal…"/>
	<entry name="Eldar/Spiritseer:HurtCount" value="1"/>
	<entry name="Eldar/Spiritseer:Idle#0" value="The dead are in my care."/>
	<entry name="Eldar/Spiritseer:Idle#1" value="I speak to you, my departed friends."/>
	<entry name="Eldar/Spiritseer:Idle#2" value="It is quiet, in the spirit halls."/>
	<entry name="Eldar/Spiritseer:Idle#3" value="Hear the infinity circuit of the planet. It whispers!"/>
	<entry name="Eldar/Spiritseer:IdleCount" value="4"/>
	<entry name="Eldar/Spiritseer:Shaken#0" value="We are so few, left alive."/>
	<entry name="Eldar/Spiritseer:ShakenCount" value="1"/>
	<entry name="Eldar/Spiritseer:Victory#0" value="I cannot celebrate death, even a foe's."/>
	<entry name="Eldar/Spiritseer:VictoryCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#0" value="YOU DARE BATTLE WITH WAR INCARNATE?!"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#1" value="IMMOLATION IS YOUR FATE"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#2" value="FIRE!"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#3" value="COME, FOOLS, KHAINE WANTS YOU"/>
	<entry name="Eldar/AvatarOfKhaine:AttackCount" value="4"/>
	<entry name="Eldar/AvatarOfKhaine:Broken#0" value="I WILL NOT FLEE"/>
	<entry name="Eldar/AvatarOfKhaine:BrokenCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Hurt#0" value="YOU FUEL MY RAGE!"/>
	<entry name="Eldar/AvatarOfKhaine:HurtCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#0" value="THIS PLANET TEARS AT ME"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#1" value="THIS HAND WIELDS THE SWORD"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#2" value="VAUL… DIED"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#3" value="SUNDERED, I FIGHT ON"/>
	<entry name="Eldar/AvatarOfKhaine:IdleCount" value="4"/>
	<entry name="Eldar/AvatarOfKhaine:Shaken#0" value="COME CLOSER, MORTALS"/>
	<entry name="Eldar/AvatarOfKhaine:ShakenCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Victory#0" value="KHAINE HAS THEM NOW."/>
	<entry name="Eldar/AvatarOfKhaine:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Attack#0" value="Guide our aim oh Emperor."/>
	<entry name="AstraMilitarum/Ratling:Attack#1" value="Longshooters."/>
	<entry name="AstraMilitarum/Ratling:Attack#2" value="If we can see it, we can hit it."/>
	<entry name="AstraMilitarum/Ratling:Attack#3" value="Like shooting tarcks in a pot."/>
	<entry name="AstraMilitarum/Ratling:AttackCount" value="4"/>
	<entry name="AstraMilitarum/Ratling:Broken#0" value="We're outta here!"/>
	<entry name="AstraMilitarum/Ratling:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Hurt#0" value="Get us out of the frontlines!"/>
	<entry name="AstraMilitarum/Ratling:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Idle#0" value="Amasec seconds? Don't mind if I do."/>
	<entry name="AstraMilitarum/Ratling:Idle#1" value="There's good eating on a Grox, y'know?"/>
	<entry name="AstraMilitarum/Ratling:Idle#2" value="This? I didn't take it! Don't tell the Commissar!"/>
	<entry name="AstraMilitarum/Ratling:Idle#3" value="Ante up and don't tell Sarge."/>
	<entry name="AstraMilitarum/Ratling:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Ratling:Shaken#0" value="Keep us safe and we'll keep you safe."/>
	<entry name="AstraMilitarum/Ratling:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Victory#0" value="Through the eye! I think that deserves extra rations."/>
	<entry name="AstraMilitarum/Ratling:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#0" value="In the name of the Dark Gods!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#1" value="Glory to the four!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#2" value="Our souls are forfeit!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#3" value="Gore for the gore lord."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Broken#0" value="Lords, protect us!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Hurt#0" value="A sacrifice to Chaos Undivided!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#0" value="Do you ever wonder… never mind."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#1" value="Nurgle, lord of flies."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#2" value="To Tzeentch, changer of ways."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#3" value="For the glory of the Perfect Prince."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Shaken#0" value="Where are our gods?"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Victory#0" value="Glory, glory, glory!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#0" value="Feel my lash!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#1" value="The abyss is calling."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#2" value="No sin can go unperformed."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#3" value="To Lorgar's glory!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Broken#0" value="I see into the warp, oh…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Hurt#0" value="Such holy ecstasy!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#0" value="Ritual sacrifice takes time."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#1" value="Mankind is doomed. Chaos is our salvation."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#2" value="Through Chaos, eternal life!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#3" value="The truth of the universe is at hand!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Shaken#0" value="Such mysteries yet to discover…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Victory#0" value="Strange visions assail me…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#0" value="They dare to come before us!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#1" value="Fools, fodder!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#2" value="We will make charnel of your bones!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#3" value="Endless war!"/>
	<entry name="ChaosSpaceMarines/Headquarters:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/Headquarters:Broken#0" value="Impossible!"/>
	<entry name="ChaosSpaceMarines/Headquarters:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Hurt#0" value="They have breached the walls!"/>
	<entry name="ChaosSpaceMarines/Headquarters:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#0" value="Liquidating loyalists."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#1" value="Eyes without skulls."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#2" value="Skulls without eyes."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#3" value="The masses will bow before us."/>
	<entry name="ChaosSpaceMarines/Headquarters:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/Headquarters:Shaken#0" value="We cannot fail, corpse-lovers!"/>
	<entry name="ChaosSpaceMarines/Headquarters:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Victory#0" value="So die all who face us."/>
	<entry name="ChaosSpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="Eldar/Warlock:Attack#0" value="Singing spear, duelling death."/>
	<entry name="Eldar/Warlock:Attack#1" value="The flames of our fury cleanse them."/>
	<entry name="Eldar/Warlock:Attack#2" value="Our spears sing."/>
	<entry name="Eldar/Warlock:Attack#3" value="The mind is our greatest weapon."/>
	<entry name="Eldar/Warlock:AttackCount" value="4"/>
	<entry name="Eldar/Warlock:Broken#0" value="Back, to the Craftworld."/>
	<entry name="Eldar/Warlock:BrokenCount" value="1"/>
	<entry name="Eldar/Warlock:Hurt#0" value="Our cadre falls."/>
	<entry name="Eldar/Warlock:HurtCount" value="1"/>
	<entry name="Eldar/Warlock:Idle#0" value="Warriors and seers, both."/>
	<entry name="Eldar/Warlock:Idle#1" value="The great enemy waits in the warp."/>
	<entry name="Eldar/Warlock:Idle#2" value="I long for the crystal blooms."/>
	<entry name="Eldar/Warlock:Idle#3" value="I miss the masques of my Craftworld."/>
	<entry name="Eldar/Warlock:IdleCount" value="4"/>
	<entry name="Eldar/Warlock:Shaken#0" value="Embolden us, we falter."/>
	<entry name="Eldar/Warlock:ShakenCount" value="1"/>
	<entry name="Eldar/Warlock:Victory#0" value="We foresaw their fall."/>
	<entry name="Eldar/Warlock:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekWraith" value="Necrons/CanoptekScarab"/>
	<entry name="Orks/KillBursta" value="Orks/Battlewagon"/>
	<entry name="Orks/KrootoxRider" value="Neutral/KrootHound"/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#0" value="Aiming for armour, Praise the Emperor."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#1" value="Concentrating fire."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#2" value="Siege specialists in place."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#3" value="Long-range, short odds."/>
	<entry name="SpaceMarines/DevastatorCenturion:AttackCount" value="4"/>
	<entry name="SpaceMarines/DevastatorCenturion:Broken#0" value="Suits are holding, but we're falling back."/>
	<entry name="SpaceMarines/DevastatorCenturion:BrokenCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Hurt#0" value="Got through the armour… both sets."/>
	<entry name="SpaceMarines/DevastatorCenturion:HurtCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#0" value="Advancing, slowly."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#1" value="Taking one suit of armour off."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#2" value="Would that our predecessors had these suits during the Heresy."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#3" value="Preparing siege armour."/>
	<entry name="SpaceMarines/DevastatorCenturion:IdleCount" value="4"/>
	<entry name="SpaceMarines/DevastatorCenturion:Shaken#0" value="Suit is under fire, but holding."/>
	<entry name="SpaceMarines/DevastatorCenturion:ShakenCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Victory#0" value="They are… devastated."/>
	<entry name="SpaceMarines/DevastatorCenturion:VictoryCount" value="1"/>
	<entry name="Tau/TigerShark" value="RazorsharkStrikeFighter"/>
	<entry name="Tyranids/Venomthrope" value="Tyranids/Carnifex"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#0" value="Shhhhhh."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#1" value="Howl: Transonics."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#2" value="Covered: Neurostatic spectrum."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#3" value="Crippled: Enemy."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Broken#0" value="Courage: Lost"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Hurt#0" value="Dying: Again"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#0" value="Idle: Idle."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#1" value="Data Request: Relax Tapes."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#2" value="Learn: Lingua Technis."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#3" value="Avowal: Potential Contender."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Shaken#0" value="Danger: Danger."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Victory#0" value="Pride: Triumph."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:VictoryCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#0" value="Wheeeee, dakkadakkadakka"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#1" value="Nah, don't run. Get drilled!"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#2" value="'IT ALL DA BUTTONS"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#3" value="WAAAGH DA FLYBOYZ!"/>
	<entry name="Orks/MegatrakkScrapjet:AttackCount" value="4"/>
	<entry name="Orks/MegatrakkScrapjet:Broken#0" value="Crashed, agin?!"/>
	<entry name="Orks/MegatrakkScrapjet:BrokenCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Hurt#0" value="Dey blew da doorz off!"/>
	<entry name="Orks/MegatrakkScrapjet:HurtCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#0" value="Boss sed I'm grounded! I'll show 'im!"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#1" value="Wish I wuz up dere, so I could shoot dahn 'ere"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#2" value="WHY AIN'T WE MOVIN?!"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#3" value="Hmm. Need more bombz."/>
	<entry name="Orks/MegatrakkScrapjet:IdleCount" value="4"/>
	<entry name="Orks/MegatrakkScrapjet:Shaken#0" value="Flak? DAT ALL YEW GOT?"/>
	<entry name="Orks/MegatrakkScrapjet:ShakenCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Victory#0" value="ORKS IZ DA BEST, drill it inta dem."/>
	<entry name="Orks/MegatrakkScrapjet:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Attack#0" value="Look at them melt."/>
	<entry name="AstraMilitarum/DevilDog:Attack#1" value="Ashes to ashes."/>
	<entry name="AstraMilitarum/DevilDog:Attack#2" value="Here come the Devil Dogs! Hoo-yah!"/>
	<entry name="AstraMilitarum/DevilDog:Attack#3" value="Fusion never felt so good."/>
	<entry name="AstraMilitarum/DevilDog:AttackCount" value="4"/>
	<entry name="AstraMilitarum/DevilDog:Broken#0" value="This thing is gonna blow!"/>
	<entry name="AstraMilitarum/DevilDog:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Hurt#0" value="Keep them away from the promethium tanks!"/>
	<entry name="AstraMilitarum/DevilDog:HurtCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Idle#0" value="What's cooking?"/>
	<entry name="AstraMilitarum/DevilDog:Idle#1" value="When do the Hellhounds ship in?"/>
	<entry name="AstraMilitarum/DevilDog:Idle#2" value="Bane Wolves… those guys scare me."/>
	<entry name="AstraMilitarum/DevilDog:Idle#3" value="It's cold when the guns ain't firing."/>
	<entry name="AstraMilitarum/DevilDog:IdleCount" value="4"/>
	<entry name="AstraMilitarum/DevilDog:Shaken#0" value="Gun the Vulvanor and get us out of range!"/>
	<entry name="AstraMilitarum/DevilDog:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Victory#0" value="Superheated air… smells like victory."/>
	<entry name="AstraMilitarum/DevilDog:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="ChaosSpaceMarines/Defiler"/>
	<entry name="Tyranids/HiveGuard" value="Tyranids/Carnifex"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="AdeptusMechanicus/SkitariiVanguard"/>
	<entry name="SpaceMarines/Scout" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="Necrons/GhostArk" value="Necrons/Monolith"/>
	<entry name="Eldar/Hornet" value="Eldar/FirePrism"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#0" value="Avenger Strike Fighter reporting."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#1" value="Strafing the enemy, Canoness."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#2" value="Death from the skies, Ma'am."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#3" value="Running low on ammo."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AttackCount" value="4"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Broken#0" value="Evasive manouvres!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Hurt#0" value="They must have AA behind them."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:HurtCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#0" value="We have the sun behind us."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#1" value="Unusual secondment, ma'am."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#2" value="Flying high!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#3" value="Just glad to be alive!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:IdleCount" value="4"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Shaken#0" value="From up here… the horror."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Victory#0" value="Score another one for the naval aces!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Is… is that the Saint?"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Attack#0" value="In the name of the Emperor."/>
	<entry name="SistersOfBattle/BattleSister:Attack#1" value="The God-Emperor guide my bolt."/>
	<entry name="SistersOfBattle/BattleSister:Attack#2" value="Unleashing His divine wrath!"/>
	<entry name="SistersOfBattle/BattleSister:Attack#3" value="Suffer not the heretic to live!"/>
	<entry name="SistersOfBattle/BattleSister:AttackCount" value="4"/>
	<entry name="SistersOfBattle/BattleSister:Broken#0" value="Without faith, we are nothing…"/>
	<entry name="SistersOfBattle/BattleSister:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Hurt#0" value="Yea, though their blades cut, I fear nothing."/>
	<entry name="SistersOfBattle/BattleSister:HurtCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Idle#0" value="We are the instruments of Salvation."/>
	<entry name="SistersOfBattle/BattleSister:Idle#1" value="Sisters, let us pray."/>
	<entry name="SistersOfBattle/BattleSister:Idle#2" value="We should not rest whilst wickedness works."/>
	<entry name="SistersOfBattle/BattleSister:Idle#3" value="So much, so much heresy… so much work."/>
	<entry name="SistersOfBattle/BattleSister:IdleCount" value="4"/>
	<entry name="SistersOfBattle/BattleSister:Shaken#0" value="In nomine Imperia…"/>
	<entry name="SistersOfBattle/BattleSister:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Victory#0" value="Rest, foe."/>
	<entry name="SistersOfBattle/BattleSister:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="The Saint…? Where she goes, we shall follow!"/>
	<entry name="SistersOfBattle/BattleSister:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Attack#0" value="Give them the Emperor's peace."/>
	<entry name="SistersOfBattle/Canoness:Attack#1" value="Heretics!"/>
	<entry name="SistersOfBattle/Canoness:Attack#2" value="Taste my redemption."/>
	<entry name="SistersOfBattle/Canoness:Attack#3" value="Feel my prayer!"/>
	<entry name="SistersOfBattle/Canoness:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Canoness:Broken#0" value="My… sisters…"/>
	<entry name="SistersOfBattle/Canoness:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Hurt#0" value="I fight… on!"/>
	<entry name="SistersOfBattle/Canoness:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Idle#0" value="Chaos makes work for idle hands."/>
	<entry name="SistersOfBattle/Canoness:Idle#1" value="We are not without flaw. But we know our sin."/>
	<entry name="SistersOfBattle/Canoness:Idle#2" value="The holy trinity are my tools… bolter, flamer, melta."/>
	<entry name="SistersOfBattle/Canoness:Idle#3" value="Redemption is worth any price, my sisters."/>
	<entry name="SistersOfBattle/Canoness:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Canoness:Shaken#0" value="I… I cannot."/>
	<entry name="SistersOfBattle/Canoness:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Victory#0" value="Absolution is thine."/>
	<entry name="SistersOfBattle/Canoness:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="I lead but she… she inspires."/>
	<entry name="SistersOfBattle/Canoness:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Attack#0" value="…a mighty fortress is the Emperor…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#1" value="…arise, o Adepta Sororitas…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#2" value="…oh, Emperor, who art our light and day…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#3" value="…Hark, the glad sound of our weapons."/>
	<entry name="SistersOfBattle/Exorcist:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Exorcist:Broken#0" value="…Emperor, have mercy."/>
	<entry name="SistersOfBattle/Exorcist:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Hurt#0" value="…from the depths of woe, I cry to thee…"/>
	<entry name="SistersOfBattle/Exorcist:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Idle#0" value="The Emperor lives!"/>
	<entry name="SistersOfBattle/Exorcist:Idle#1" value="Glory! Glory! Glory!"/>
	<entry name="SistersOfBattle/Exorcist:Idle#2" value="The Emperor has loosed his swift sword."/>
	<entry name="SistersOfBattle/Exorcist:Idle#3" value="Quake, man, in terror."/>
	<entry name="SistersOfBattle/Exorcist:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Exorcist:Shaken#0" value="Emperor, thy blood and righteousness shall preserve us."/>
	<entry name="SistersOfBattle/Exorcist:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Victory#0" value="…crush the serpent and march on…"/>
	<entry name="SistersOfBattle/Exorcist:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="…behold the wondrous mystery of the Living Saint..!"/>
	<entry name="SistersOfBattle/Exorcist:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#0" value="Come closer, heretics."/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#1" value="Feel the Emperor's wrath!"/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#2" value="Faith is our shield."/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#3" value="We shall send you to your rest."/>
	<entry name="SistersOfBattle/CelestianSacresant:AttackCount" value="4"/>
	<entry name="SistersOfBattle/CelestianSacresant:Broken#0" value="We cannot complete it. We must-"/>
	<entry name="SistersOfBattle/CelestianSacresant:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Hurt#0" value="They threaten our quest…"/>
	<entry name="SistersOfBattle/CelestianSacresant:HurtCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#0" value="We travelled the galaxy to end… here."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#1" value="Our armour, impenetrable."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#2" value="Our virtue, unchallenged."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#3" value="We are the model to which all aspire."/>
	<entry name="SistersOfBattle/CelestianSacresant:IdleCount" value="4"/>
	<entry name="SistersOfBattle/CelestianSacresant:Shaken#0" value="Not a step back."/>
	<entry name="SistersOfBattle/CelestianSacresant:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Victory#0" value="Our quest is complete."/>
	<entry name="SistersOfBattle/CelestianSacresant:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Our namesake, our lady! Our quest is at an end."/>
	<entry name="SistersOfBattle/CelestianSacresant:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#0" value="For the honour of the Questor Mechanicus!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#1" value="We are but a weapon in the right hand of the Emperor."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#2" value="Fear my charge!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#3" value="Knights, ho!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AttackCount" value="4"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Broken#0" value="What knavery…?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Hurt#0" value="Speed shall be mine armour."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:HurtCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#0" value="I rust, whilst you dither."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#1" value="Canoness, use me!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#2" value="My lance needs use."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#3" value="The machine spirits hunger for glory!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:IdleCount" value="4"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Shaken#0" value="What is the glory in this?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Victory#0" value="They could not withstand my fury."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="A… a living saint?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:EnemyUnit:AdeptusMechanicus/KnightCrusader#0" value="A worthy foe! Come, let me close with you."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:EnemyUnit:AdeptusMechanicus/KnightCrusaderCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Attack#0" value="And, yea, the Emperor saw their death and it was good."/>
	<entry name="SistersOfBattle/Dialogus:Attack#1" value="Rest not my sisters, tell they are no more!"/>
	<entry name="SistersOfBattle/Dialogus:Attack#2" value="Ask not the heretic when a flamer can speak."/>
	<entry name="SistersOfBattle/Dialogus:Attack#3" value="I stand with you, together!"/>
	<entry name="SistersOfBattle/Dialogus:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Dialogus:Broken#0" value="Penitence is its own punishment."/>
	<entry name="SistersOfBattle/Dialogus:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Hurt#0" value="What is one more wound to the faithful?"/>
	<entry name="SistersOfBattle/Dialogus:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Idle#0" value="Vox-beads: check. Sensoria: check."/>
	<entry name="SistersOfBattle/Dialogus:Idle#1" value="Rest today, for tomorrow we shall be with Him."/>
	<entry name="SistersOfBattle/Dialogus:Idle#2" value="The Emperor loves you all."/>
	<entry name="SistersOfBattle/Dialogus:Idle#3" value="Fear not the xenos."/>
	<entry name="SistersOfBattle/Dialogus:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Dialogus:Shaken#0" value="Though I walk through death and toil…"/>
	<entry name="SistersOfBattle/Dialogus:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Victory#0" value="Lie down for me, so I might tread higher"/>
	<entry name="SistersOfBattle/Dialogus:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Words… fail me. Celestine herself!"/>
	<entry name="SistersOfBattle/Dialogus:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Attack#0" value="Lo, the faithful chorus rings out."/>
	<entry name="SistersOfBattle/Headquarters:Attack#1" value="Our walls stand ready."/>
	<entry name="SistersOfBattle/Headquarters:Attack#2" value="Our shrines are not defenseless."/>
	<entry name="SistersOfBattle/Headquarters:Attack#3" value="You are mistaken if you think us weak."/>
	<entry name="SistersOfBattle/Headquarters:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Headquarters:Broken#0" value="Our walls… fall?"/>
	<entry name="SistersOfBattle/Headquarters:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Hurt#0" value="The shrine is breached."/>
	<entry name="SistersOfBattle/Headquarters:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Idle#0" value="Offering prayers to the God-Emperor."/>
	<entry name="SistersOfBattle/Headquarters:Idle#1" value="Purging heretics."/>
	<entry name="SistersOfBattle/Headquarters:Idle#2" value="Scattering holy ashes."/>
	<entry name="SistersOfBattle/Headquarters:Idle#3" value="Training novices."/>
	<entry name="SistersOfBattle/Headquarters:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Headquarters:Shaken#0" value="Behind the ramparts, quick!"/>
	<entry name="SistersOfBattle/Headquarters:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Victory#0" value="Do not test our walls or our faith."/>
	<entry name="SistersOfBattle/Headquarters:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="O, would that she would visit. Celestine!"/>
	<entry name="SistersOfBattle/Headquarters:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Attack#0" value="My tools hurt as well as they heal."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#1" value="We hate you, affronts to the faith."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#2" value="Stand strong in the name of the Emperor."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#3" value="Martial bliss is our reward."/>
	<entry name="SistersOfBattle/Hospitaller:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Hospitaller:Broken#0" value="I must retreat, to preserve the young ones."/>
	<entry name="SistersOfBattle/Hospitaller:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Hurt#0" value="Physician… heal thyself."/>
	<entry name="SistersOfBattle/Hospitaller:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Idle#0" value="In strength, life."/>
	<entry name="SistersOfBattle/Hospitaller:Idle#1" value="Our wounds are testaments to our good work."/>
	<entry name="SistersOfBattle/Hospitaller:Idle#2" value="Where shall I raise our shrines?"/>
	<entry name="SistersOfBattle/Hospitaller:Idle#3" value="Hatred is not a sin when the universe is so foul."/>
	<entry name="SistersOfBattle/Hospitaller:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Hospitaller:Shaken#0" value="My role is to heal… why am I here?"/>
	<entry name="SistersOfBattle/Hospitaller:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Victory#0" value="My healing touch is somewhat late for that one."/>
	<entry name="SistersOfBattle/Hospitaller:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="My wounds… knit themselves together. How?"/>
	<entry name="SistersOfBattle/Hospitaller:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Attack#0" value="letmeoutletmeout"/>
	<entry name="SistersOfBattle/Mortifier:Attack#1" value="i'msorryi'msorryi'msorry"/>
	<entry name="SistersOfBattle/Mortifier:Attack#2" value="diediedie"/>
	<entry name="SistersOfBattle/Mortifier:Attack#3" value="why can't I die?"/>
	<entry name="SistersOfBattle/Mortifier:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Mortifier:Broken#0" value="Break me more, so I may not know this."/>
	<entry name="SistersOfBattle/Mortifier:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Hurt#0" value="Please, death, now, yes."/>
	<entry name="SistersOfBattle/Mortifier:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Idle#0" value="Emperor, free me."/>
	<entry name="SistersOfBattle/Mortifier:Idle#1" value="Let me die."/>
	<entry name="SistersOfBattle/Mortifier:Idle#2" value="I am a sinner, but this-"/>
	<entry name="SistersOfBattle/Mortifier:Idle#3" value="-torment-"/>
	<entry name="SistersOfBattle/Mortifier:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Mortifier:Shaken#0" value="Bring the death closer!"/>
	<entry name="SistersOfBattle/Mortifier:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Victory#0" value="They are free… for me, there is nothing."/>
	<entry name="SistersOfBattle/Mortifier:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Please… please…"/>
	<entry name="SistersOfBattle/Mortifier:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#0" value="One day, humanity will be free of this."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#1" value="I bring salvation… through annihilation."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#2" value="I am the Emperor's tool, to guide as He wills."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#3" value="He knows my path."/>
	<entry name="SistersOfBattle/SaintCelestine:AttackCount" value="4"/>
	<entry name="SistersOfBattle/SaintCelestine:Broken#0" value="I cannot falter."/>
	<entry name="SistersOfBattle/SaintCelestine:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Hurt#0" value="Death is never the end. Why fear it?"/>
	<entry name="SistersOfBattle/SaintCelestine:HurtCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#0" value="Rather darkness than the illumination of the unclean."/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#1" value="I live in the hearts of the Righteous."/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#2" value="They would follow me to the Eye of Terror…"/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#3" value="Trapped. Here."/>
	<entry name="SistersOfBattle/SaintCelestine:IdleCount" value="4"/>
	<entry name="SistersOfBattle/SaintCelestine:Shaken#0" value="I fear no death."/>
	<entry name="SistersOfBattle/SaintCelestine:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Victory#0" value="This is your end, not mine."/>
	<entry name="SistersOfBattle/SaintCelestine:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#0" value="For redemption!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#1" value="By our works, know us!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#2" value="Atonement is good work!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#3" value="Our eviscerators are as prayer-wheels."/>
	<entry name="SistersOfBattle/SisterRepentia:AttackCount" value="4"/>
	<entry name="SistersOfBattle/SisterRepentia:Broken#0" value="They will Mortify us!"/>
	<entry name="SistersOfBattle/SisterRepentia:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Hurt#0" value="Our faith is our armour!"/>
	<entry name="SistersOfBattle/SisterRepentia:HurtCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#0" value="We have sinned."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#1" value="Cowardice in the face of the enemy."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#2" value="Lord, Emperor, we beg forgiveness."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#3" value="A moment of peace before death."/>
	<entry name="SistersOfBattle/SisterRepentia:IdleCount" value="4"/>
	<entry name="SistersOfBattle/SisterRepentia:Shaken#0" value="Perhaps we cannot atone…"/>
	<entry name="SistersOfBattle/SisterRepentia:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Victory#0" value="Another step toward redemption or death."/>
	<entry name="SistersOfBattle/SisterRepentia:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Celestine was once a Repentia… our beacon of redemption."/>
	<entry name="SistersOfBattle/SisterRepentia:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#0" value="Fire and flight!"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#1" value="Come, burn."/>
	<entry name="SistersOfBattle/Zephyrim:Attack#2" value="The Emperor is our guide!"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#3" value="Through skies of flame, you are ours."/>
	<entry name="SistersOfBattle/Zephyrim:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Zephyrim:Broken#0" value="Fly, the Emperor Protects Us!"/>
	<entry name="SistersOfBattle/Zephyrim:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Hurt#0" value="Our blood, our wounds, prayers to Him!"/>
	<entry name="SistersOfBattle/Zephyrim:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Idle#0" value="We are the divine destroyers."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#1" value="Hunt the wicked, the powerful."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#2" value="Tongues of flame speak prayers too."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#3" value="Angelic chorus awaiting orders."/>
	<entry name="SistersOfBattle/Zephyrim:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Zephyrim:Shaken#0" value="Regrouping amidst the clouds."/>
	<entry name="SistersOfBattle/Zephyrim:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Victory#0" value="So all the wicked perish!"/>
	<entry name="SistersOfBattle/Zephyrim:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Would that we were true angels like her…"/>
	<entry name="SistersOfBattle/Zephyrim:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Dominion" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Immolator" value="SistersOfBattle/Exorcist"/>
	<entry name="SistersOfBattle/Lightning" value="SistersOfBattle/Avenger"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Retributor" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Castigator" value="SistersOfBattle/Exorcist"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#0" value="C32: Firing Pattern Achieved."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#1" value="7T32: All Armaments Deployed."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#2" value="8-6: Breacher Clade active."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#3" value="P4-78: Armour/Transport Search & Destroy."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Broken#0" value="Disposable Assets… human once-"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Hurt#0" value="Functionality Compromised. Machine Extant. Flesh 45%, Declining."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#0" value="Necrotic Tissue Being Excised."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#1" value="Servitor Waiting To Be Expended."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#2" value="Memory Of Grass, Clouds, Blue Sky… Mind Wipe Error. Erasing."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#3" value="Binharic Chorus Commencing."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Shaken#0" value="Under Sustained Fire."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Victory#0" value="Eliminated, Brutally. Satisfactory."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#0" value="Oppressive fire!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#1" value="In the Primarch's name!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#2" value="Armoured assault!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#3" value="One is firing… everything!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:AttackCount" value="4"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Broken#0" value="One must preserve the tank-"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Hurt#0" value="Straight through the hull-"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:HurtCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#0" value="What would Dorn do?"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#1" value="One serves the Emperor's will."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#2" value="Armageddon, now that was a tank battle…"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#3" value="Less infantry, more armour."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:IdleCount" value="4"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Shaken#0" value="They have quite got to One."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Victory#0" value="One is always looking out for new targets."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#0" value="Khrrrrne!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#1" value="Rrrrr!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#2" value="Wrrrrr-"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#3" value="K-k-k-k!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Broken#0" value="Sklzzz-"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Hurt#0" value="Ztt! Rrrr-"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#0" value="Crck."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#1" value="Blddd…"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#2" value="Mhnnn."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#3" value="Mrrrr."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Shaken#0" value="Hnh!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Victory#0" value="RRRRRRR!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:VictoryCount" value="1"/>
	<entry name="Eldar/DarkReaper:Attack#0" value="Appreciate the art of destruction."/>
	<entry name="Eldar/DarkReaper:Attack#1" value="Bringing Death."/>
	<entry name="Eldar/DarkReaper:Attack#2" value="In the name of Khaine!"/>
	<entry name="Eldar/DarkReaper:Attack#3" value="We do not miss."/>
	<entry name="Eldar/DarkReaper:AttackCount" value="4"/>
	<entry name="Eldar/DarkReaper:Broken#0" value="It is we who shall be destroyed…"/>
	<entry name="Eldar/DarkReaper:BrokenCount" value="1"/>
	<entry name="Eldar/DarkReaper:Hurt#0" value="Destruction comes to us all."/>
	<entry name="Eldar/DarkReaper:HurtCount" value="1"/>
	<entry name="Eldar/DarkReaper:Idle#0" value="We remember Altansar."/>
	<entry name="Eldar/DarkReaper:Idle#1" value="Destruction is but another path in our long lives."/>
	<entry name="Eldar/DarkReaper:Idle#2" value="Death without fear is our lot."/>
	<entry name="Eldar/DarkReaper:Idle#3" value="The Destroyer took the Smith-God long ago."/>
	<entry name="Eldar/DarkReaper:IdleCount" value="4"/>
	<entry name="Eldar/DarkReaper:Shaken#0" value="The Destroyer has turned against us!"/>
	<entry name="Eldar/DarkReaper:ShakenCount" value="1"/>
	<entry name="Eldar/DarkReaper:Victory#0" value="The Destroyer has taken them."/>
	<entry name="Eldar/DarkReaper:VictoryCount" value="1"/>
	<entry name="Necrons/Deathmark:Attack#0" value="Targetting their leaders."/>
	<entry name="Necrons/Deathmark:Attack#1" value="Disintegrating their synapses."/>
	<entry name="Necrons/Deathmark:Attack#2" value="Dishonourable foe targetted."/>
	<entry name="Necrons/Deathmark:Attack#3" value="Marking for death."/>
	<entry name="Necrons/Deathmark:AttackCount" value="4"/>
	<entry name="Necrons/Deathmark:Broken#0" value="Return to alternate dimension!"/>
	<entry name="Necrons/Deathmark:BrokenCount" value="1"/>
	<entry name="Necrons/Deathmark:Hurt#0" value="Reanimation protocols failing."/>
	<entry name="Necrons/Deathmark:HurtCount" value="1"/>
	<entry name="Necrons/Deathmark:Idle#0" value="Monitoring communications."/>
	<entry name="Necrons/Deathmark:Idle#1" value="Is the Deceiver truly here?"/>
	<entry name="Necrons/Deathmark:Idle#2" value="Locating nodes of command."/>
	<entry name="Necrons/Deathmark:Idle#3" value="Decapitation protocols on hold."/>
	<entry name="Necrons/Deathmark:IdleCount" value="4"/>
	<entry name="Necrons/Deathmark:Shaken#0" value="We are precision tools, not targets."/>
	<entry name="Necrons/Deathmark:ShakenCount" value="1"/>
	<entry name="Necrons/Deathmark:Victory#0" value="As expected, eliminated."/>
	<entry name="Necrons/Deathmark:VictoryCount" value="1"/>
	<entry name="Neutral/Poxwalker:Attack#0" value="Heehee."/>
	<entry name="Neutral/Poxwalker:Attack#1" value="Urrnnnn…"/>
	<entry name="Neutral/Poxwalker:Attack#2" value="Harrrr…"/>
	<entry name="Neutral/Poxwalker:Attack#3" value="Nurg Elll…"/>
	<entry name="Neutral/Poxwalker:AttackCount" value="4"/>
	<entry name="Neutral/Poxwalker:Broken#0" value="Father…"/>
	<entry name="Neutral/Poxwalker:BrokenCount" value="1"/>
	<entry name="Neutral/Poxwalker:Hurt#0" value="Falling…"/>
	<entry name="Neutral/Poxwalker:HurtCount" value="1"/>
	<entry name="Neutral/Poxwalker:Idle#0" value="…souls… trapped…"/>
	<entry name="Neutral/Poxwalker:Idle#1" value="…Plague God…"/>
	<entry name="Neutral/Poxwalker:Idle#2" value="…Father…"/>
	<entry name="Neutral/Poxwalker:Idle#3" value="…Unhhhh…"/>
	<entry name="Neutral/Poxwalker:IdleCount" value="4"/>
	<entry name="Neutral/Poxwalker:Shaken#0" value="…nnnaaaa…"/>
	<entry name="Neutral/Poxwalker:ShakenCount" value="1"/>
	<entry name="Neutral/Poxwalker:Victory#0" value="…infected…"/>
	<entry name="Neutral/Poxwalker:VictoryCount" value="1"/>
	<entry name="Orks/DeffDread:Attack#0" value="Stompin' and Killin'!"/>
	<entry name="Orks/DeffDread:Attack#1" value="Dis was worth da op!"/>
	<entry name="Orks/DeffDread:Attack#2" value="Ooh, I az Rokkits!"/>
	<entry name="Orks/DeffDread:Attack#3" value="Firin' ev'ryfin!"/>
	<entry name="Orks/DeffDread:AttackCount" value="4"/>
	<entry name="Orks/DeffDread:Broken#0" value="Leggin' it!"/>
	<entry name="Orks/DeffDread:BrokenCount" value="1"/>
	<entry name="Orks/DeffDread:Hurt#0" value="I fink dere's an 'ole in me."/>
	<entry name="Orks/DeffDread:HurtCount" value="1"/>
	<entry name="Orks/DeffDread:Idle#0" value="Dere's a Squig in 'ere!"/>
	<entry name="Orks/DeffDread:Idle#1" value="Somewun, scratch me schnozz! Drivin' me mad!"/>
	<entry name="Orks/DeffDread:Idle#2" value="Wotz all dese wires fer?"/>
	<entry name="Orks/DeffDread:Idle#3" value="Borrringgg."/>
	<entry name="Orks/DeffDread:IdleCount" value="4"/>
	<entry name="Orks/DeffDread:Shaken#0" value="'ow d'ya 'ide in dis fing?"/>
	<entry name="Orks/DeffDread:ShakenCount" value="1"/>
	<entry name="Orks/DeffDread:Victory#0" value="WAAAGH! Deff Dreads iz da bestest!"/>
	<entry name="Orks/DeffDread:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Attack#0" value="And Celestine did smite them and saw it was good."/>
	<entry name="SistersOfBattle/Imagifier:Attack#1" value="And they were the gloried dead!"/>
	<entry name="SistersOfBattle/Imagifier:Attack#2" value="Their holy rage, oh my sisters!"/>
	<entry name="SistersOfBattle/Imagifier:Attack#3" value="Defiant, they stood, against the foe!"/>
	<entry name="SistersOfBattle/Imagifier:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Imagifier:Broken#0" value="My song… falters…"/>
	<entry name="SistersOfBattle/Imagifier:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Hurt#0" value="Grievious wounds they suffered, but forged on!"/>
	<entry name="SistersOfBattle/Imagifier:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Idle#0" value="I bear the Simulacrum Imperialis."/>
	<entry name="SistersOfBattle/Imagifier:Idle#1" value="In brief moments, they invoked the Emperor's love."/>
	<entry name="SistersOfBattle/Imagifier:Idle#2" value="…And Celestine knew her moment was come…"/>
	<entry name="SistersOfBattle/Imagifier:Idle#3" value="The arch-traitor clutched the fallen angel in a single claw…"/>
	<entry name="SistersOfBattle/Imagifier:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Imagifier:Shaken#0" value="And they sealed the Eternity Gate and were cowed.."/>
	<entry name="SistersOfBattle/Imagifier:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Victory#0" value="And they returned to holy Terra in triumph!"/>
	<entry name="SistersOfBattle/Imagifier:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Attack#0" value="Missiles away, Commander."/>
	<entry name="SpaceMarines/Whirlwind:Attack#1" value="Whirlwind firing."/>
	<entry name="SpaceMarines/Whirlwind:Attack#2" value="Indirect barrage impacting."/>
	<entry name="SpaceMarines/Whirlwind:Attack#3" value="Saturation bombardment available."/>
	<entry name="SpaceMarines/Whirlwind:AttackCount" value="4"/>
	<entry name="SpaceMarines/Whirlwind:Broken#0" value="Pulling back."/>
	<entry name="SpaceMarines/Whirlwind:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Hurt#0" value="Within small arms range… damage sustained."/>
	<entry name="SpaceMarines/Whirlwind:HurtCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Idle#0" value="Machine spirits ministration."/>
	<entry name="SpaceMarines/Whirlwind:Idle#1" value="Deployed, awaiting orders."/>
	<entry name="SpaceMarines/Whirlwind:Idle#2" value="If enemies need softening up, artillery available."/>
	<entry name="SpaceMarines/Whirlwind:Idle#3" value="Reloading missiles."/>
	<entry name="SpaceMarines/Whirlwind:IdleCount" value="4"/>
	<entry name="SpaceMarines/Whirlwind:Shaken#0" value="This is our Chapter World… how can this be?"/>
	<entry name="SpaceMarines/Whirlwind:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Victory#0" value="Receiving reports target destroyed."/>
	<entry name="SpaceMarines/Whirlwind:VictoryCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Attack#0" value="Holding fast."/>
	<entry name="Tau/RVarnaBattlesuit:Attack#1" value="Plasma saturation in process."/>
	<entry name="Tau/RVarnaBattlesuit:AttackCount" value="2"/>
	<entry name="Tau/RVarnaBattlesuit:Broken#0" value="Needing support here."/>
	<entry name="Tau/RVarnaBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Hurt#0" value="Reactor leaking… no retreat."/>
	<entry name="Tau/RVarnaBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Idle#0" value="What are we doing in this fu'llasso?"/>
	<entry name="Tau/RVarnaBattlesuit:Idle#1" value="The ho'or-ata-t'chel—the ghost pains—are bad today."/>
	<entry name="Tau/RVarnaBattlesuit:Idle#2" value="Shas'vre reporting."/>
	<entry name="Tau/RVarnaBattlesuit:Idle#3" value="How do they make this fio'tak? It's impregnable!"/>
	<entry name="Tau/RVarnaBattlesuit:IdleCount" value="4"/>
	<entry name="Tau/RVarnaBattlesuit:Shaken#0" value="Iur'tae'mont! Where's Farsight when you need him?"/>
	<entry name="Tau/RVarnaBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Victory#0" value="Nothing moving over there."/>
	<entry name="Tau/RVarnaBattlesuit:VictoryCount" value="1"/>
	<entry name="Tyranids/Tyrannocyte" value="Tyranids/Carnifex"/>
	<entry name="Drukhari/Archon:Attack#0" value="Suffering is your lot!"/>
	<entry name="Drukhari/Archon:Attack#1" value="Fools, to stand against me."/>
	<entry name="Drukhari/Archon:Attack#2" value="You are barely worth the killing."/>
	<entry name="Drukhari/Archon:Attack#3" value="Oh, sweet fear and agony."/>
	<entry name="Drukhari/Archon:AttackCount" value="4"/>
	<entry name="Drukhari/Archon:Broken#0" value="Back, back to the city of shadows!"/>
	<entry name="Drukhari/Archon:BrokenCount" value="1"/>
	<entry name="Drukhari/Archon:Hurt#0" value="A lucky blow."/>
	<entry name="Drukhari/Archon:HurtCount" value="1"/>
	<entry name="Drukhari/Archon:Idle#0" value="Simple torments, simple pleasures."/>
	<entry name="Drukhari/Archon:Idle#1" value="Have we killed them all already? Pity."/>
	<entry name="Drukhari/Archon:Idle#2" value="The riches of this world are merely the suffering of its people."/>
	<entry name="Drukhari/Archon:Idle#3" value="How to fill the idle aeons, ahhh…"/>
	<entry name="Drukhari/Archon:IdleCount" value="4"/>
	<entry name="Drukhari/Archon:Shaken#0" value="If this continues, it will take a Haemonculus to wake me…"/>
	<entry name="Drukhari/Archon:ShakenCount" value="1"/>
	<entry name="Drukhari/Archon:Victory#0" value="What did you expect? Pitiful."/>
	<entry name="Drukhari/Archon:VictoryCount" value="1"/>
	<entry name="Drukhari/Cronos:Attack#0" value="Hrrrr."/>
	<entry name="Drukhari/Cronos:Attack#1" value="…memory…"/>
	<entry name="Drukhari/Cronos:Attack#2" value="Cthhh…"/>
	<entry name="Drukhari/Cronos:Attack#3" value="Rrrr…"/>
	<entry name="Drukhari/Cronos:AttackCount" value="4"/>
	<entry name="Drukhari/Cronos:Broken#0" value="…"/>
	<entry name="Drukhari/Cronos:BrokenCount" value="1"/>
	<entry name="Drukhari/Cronos:Hurt#0" value="Hrrr."/>
	<entry name="Drukhari/Cronos:HurtCount" value="1"/>
	<entry name="Drukhari/Cronos:Idle#0" value="Pssssst…"/>
	<entry name="Drukhari/Cronos:Idle#1" value="Thrrrt."/>
	<entry name="Drukhari/Cronos:Idle#2" value="Thffffttt."/>
	<entry name="Drukhari/Cronos:Idle#3" value="Tcht."/>
	<entry name="Drukhari/Cronos:IdleCount" value="4"/>
	<entry name="Drukhari/Cronos:Shaken#0" value="Eeeee!"/>
	<entry name="Drukhari/Cronos:ShakenCount" value="1"/>
	<entry name="Drukhari/Cronos:Victory#0" value="Psst."/>
	<entry name="Drukhari/Cronos:VictoryCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Attack#0" value="The art of agony!"/>
	<entry name="Drukhari/Haemonculus:Attack#1" value="Suffer, suffer, suffer!"/>
	<entry name="Drukhari/Haemonculus:Attack#2" value="With pain, I renew."/>
	<entry name="Drukhari/Haemonculus:Attack#3" value="Let me play on their nerves."/>
	<entry name="Drukhari/Haemonculus:AttackCount" value="4"/>
	<entry name="Drukhari/Haemonculus:Broken#0" value="Perhaps another time?"/>
	<entry name="Drukhari/Haemonculus:BrokenCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Hurt#0" value="My own pain will suffice."/>
	<entry name="Drukhari/Haemonculus:HurtCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Idle#0" value="What sweet suffering to gather next?"/>
	<entry name="Drukhari/Haemonculus:Idle#1" value="Do I remember the fall? Do I?"/>
	<entry name="Drukhari/Haemonculus:Idle#2" value="My experiments bear fruit."/>
	<entry name="Drukhari/Haemonculus:Idle#3" value="Where are my wracks? I require attention!"/>
	<entry name="Drukhari/Haemonculus:IdleCount" value="4"/>
	<entry name="Drukhari/Haemonculus:Shaken#0" value="Oh, they are puissant! I must have them."/>
	<entry name="Drukhari/Haemonculus:ShakenCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Victory#0" value="Now. What did we learn?"/>
	<entry name="Drukhari/Haemonculus:VictoryCount" value="1"/>
	<entry name="Drukhari/Headquarters:Attack#0" value="They dare attack our seat of power?"/>
	<entry name="Drukhari/Headquarters:Attack#1" value="Fools!"/>
	<entry name="Drukhari/Headquarters:Attack#2" value="What madness to assail our walls."/>
	<entry name="Drukhari/Headquarters:Attack#3" value="Break yourselves on our crenellations."/>
	<entry name="Drukhari/Headquarters:AttackCount" value="4"/>
	<entry name="Drukhari/Headquarters:Broken#0" value="Archon, save your loy- your subjects!"/>
	<entry name="Drukhari/Headquarters:BrokenCount" value="1"/>
	<entry name="Drukhari/Headquarters:Hurt#0" value="Breached. But only weaklings died."/>
	<entry name="Drukhari/Headquarters:HurtCount" value="1"/>
	<entry name="Drukhari/Headquarters:Idle#0" value="In peacetime, plots."/>
	<entry name="Drukhari/Headquarters:Idle#1" value="The torture gardens are busy tonight."/>
	<entry name="Drukhari/Headquarters:Idle#2" value="An aeon of idleness…"/>
	<entry name="Drukhari/Headquarters:Idle#3" value="The paltry luxuries of this temporary home."/>
	<entry name="Drukhari/Headquarters:IdleCount" value="4"/>
	<entry name="Drukhari/Headquarters:Shaken#0" value="Oh, to return to dark Commorragh!"/>
	<entry name="Drukhari/Headquarters:ShakenCount" value="1"/>
	<entry name="Drukhari/Headquarters:Victory#0" value="Secure once more."/>
	<entry name="Drukhari/Headquarters:VictoryCount" value="1"/>
	<entry name="Drukhari/Hellion:Attack#0" value="The thrill of the kill!"/>
	<entry name="Drukhari/Hellion:Attack#1" value="Death from above!"/>
	<entry name="Drukhari/Hellion:Attack#2" value="We swoop, we strike!"/>
	<entry name="Drukhari/Hellion:Attack#3" value="Overwhelming sensation!"/>
	<entry name="Drukhari/Hellion:AttackCount" value="4"/>
	<entry name="Drukhari/Hellion:Broken#0" value="Away, afore they snare us!"/>
	<entry name="Drukhari/Hellion:BrokenCount" value="1"/>
	<entry name="Drukhari/Hellion:Hurt#0" value="The slow have fallen."/>
	<entry name="Drukhari/Hellion:HurtCount" value="1"/>
	<entry name="Drukhari/Hellion:Idle#0" value="We will not wait!"/>
	<entry name="Drukhari/Hellion:Idle#1" value="The skies call! The lightning beckons!"/>
	<entry name="Drukhari/Hellion:Idle#2" value="Stillness is torment!"/>
	<entry name="Drukhari/Hellion:Idle#3" value="Unleash us, Archon!"/>
	<entry name="Drukhari/Hellion:IdleCount" value="4"/>
	<entry name="Drukhari/Hellion:Shaken#0" value="Grounded, we die."/>
	<entry name="Drukhari/Hellion:ShakenCount" value="1"/>
	<entry name="Drukhari/Hellion:Victory#0" value="Faster, faster they fall!"/>
	<entry name="Drukhari/Hellion:VictoryCount" value="1"/>
	<entry name="Drukhari/Incubi:Attack#0" value="We are one with the klaive."/>
	<entry name="Drukhari/Incubi:Attack#1" value="The killing strike… just so."/>
	<entry name="Drukhari/Incubi:Attack#2" value="We offer the weak to Khaine."/>
	<entry name="Drukhari/Incubi:Attack#3" value="Paid to kill."/>
	<entry name="Drukhari/Incubi:AttackCount" value="4"/>
	<entry name="Drukhari/Incubi:Broken#0" value="Khaine does not forgive cowards."/>
	<entry name="Drukhari/Incubi:BrokenCount" value="1"/>
	<entry name="Drukhari/Incubi:Hurt#0" value="A true opponent."/>
	<entry name="Drukhari/Incubi:HurtCount" value="1"/>
	<entry name="Drukhari/Incubi:Idle#0" value="No betrayal 'til the covenant's done."/>
	<entry name="Drukhari/Incubi:Idle#1" value="Our time is bought… it would be a waste not to use us."/>
	<entry name="Drukhari/Incubi:Idle#2" value="There is a purity in hate."/>
	<entry name="Drukhari/Incubi:Idle#3" value="Follow Arha and the Path of Damnation."/>
	<entry name="Drukhari/Incubi:IdleCount" value="4"/>
	<entry name="Drukhari/Incubi:Shaken#0" value="No-one shall walk the Path of Grief for such as we."/>
	<entry name="Drukhari/Incubi:ShakenCount" value="1"/>
	<entry name="Drukhari/Incubi:Victory#0" value="Tribute for Khaine."/>
	<entry name="Drukhari/Incubi:VictoryCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#0" value="Chattel!"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#1" value="Arrogant fools!"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#2" value="Stand against the Trueborn? Hahaha…"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#3" value="The Archon commands…"/>
	<entry name="Drukhari/KabaliteTrueborn:AttackCount" value="4"/>
	<entry name="Drukhari/KabaliteTrueborn:Broken#0" value="Back, preserve our precious flesh!"/>
	<entry name="Drukhari/KabaliteTrueborn:BrokenCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Hurt#0" value="Only true Trueborn still stand."/>
	<entry name="Drukhari/KabaliteTrueborn:HurtCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#0" value="Bring us wines! Victims!"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#1" value="Wise, to not squander our precious lives."/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#2" value="We shall spend the millenia in such lassitude…"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#3" value="Oh, happy indolent days!"/>
	<entry name="Drukhari/KabaliteTrueborn:IdleCount" value="4"/>
	<entry name="Drukhari/KabaliteTrueborn:Shaken#0" value="We are not disposable vatborn!"/>
	<entry name="Drukhari/KabaliteTrueborn:ShakenCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Victory#0" value="What did they expect? We are Trueborn."/>
	<entry name="Drukhari/KabaliteTrueborn:VictoryCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Attack#0" value="Mischief calls."/>
	<entry name="Drukhari/KabaliteWarrior:Attack#1" value="Cower, mortals."/>
	<entry name="Drukhari/KabaliteWarrior:Attack#2" value="Commorrah demands tribute!"/>
	<entry name="Drukhari/KabaliteWarrior:Attack#3" value="For the Kabal!"/>
	<entry name="Drukhari/KabaliteWarrior:AttackCount" value="4"/>
	<entry name="Drukhari/KabaliteWarrior:Broken#0" value="If we flee, the Archon… worse than death."/>
	<entry name="Drukhari/KabaliteWarrior:BrokenCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Hurt#0" value="They shall rue this…"/>
	<entry name="Drukhari/KabaliteWarrior:HurtCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Idle#0" value="Vatborn we may be, but we are true Drukhari."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#1" value="Bring prisoners, we must practise our aim."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#2" value="Preserve the kabalites, spend the mercenaries."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#3" value="Commorragh, our many-angled home…"/>
	<entry name="Drukhari/KabaliteWarrior:IdleCount" value="4"/>
	<entry name="Drukhari/KabaliteWarrior:Shaken#0" value="We fear? They should fear!"/>
	<entry name="Drukhari/KabaliteWarrior:ShakenCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Victory#0" value="Fools! The Kabal does not send weaklings."/>
	<entry name="Drukhari/KabaliteWarrior:VictoryCount" value="1"/>
	<entry name="Drukhari/Raider:Attack#0" value="Skim low, sweet keelblade."/>
	<entry name="Drukhari/Raider:Attack#1" value="Passing pain…"/>
	<entry name="Drukhari/Raider:Attack#2" value="Drive us closer…"/>
	<entry name="Drukhari/Raider:Attack#3" value="The buzz of the charge!"/>
	<entry name="Drukhari/Raider:AttackCount" value="4"/>
	<entry name="Drukhari/Raider:Broken#0" value="Retreat, before they can close with us!"/>
	<entry name="Drukhari/Raider:BrokenCount" value="1"/>
	<entry name="Drukhari/Raider:Hurt#0" value="Speed should be our armour!"/>
	<entry name="Drukhari/Raider:HurtCount" value="1"/>
	<entry name="Drukhari/Raider:Idle#0" value="Our trophy hooks are bare!"/>
	<entry name="Drukhari/Raider:Idle#1" value="Pile the bodies at the rear. Only the weak die."/>
	<entry name="Drukhari/Raider:Idle#2" value="Sharpen the keelblade, the fins."/>
	<entry name="Drukhari/Raider:Idle#3" value="Only Vect recalls the pleasure yachts of ancient days…"/>
	<entry name="Drukhari/Raider:IdleCount" value="4"/>
	<entry name="Drukhari/Raider:Shaken#0" value="Quicker, upon them!"/>
	<entry name="Drukhari/Raider:ShakenCount" value="1"/>
	<entry name="Drukhari/Raider:Victory#0" value="Put the captives on the trophy-hooks. Alive or dead."/>
	<entry name="Drukhari/Raider:VictoryCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#0" value="Raking the ground, Archon!"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#1" value="Our grace brings doom."/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#2" value="Oh, flee, prey, flee!"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#3" value="Massacre them! Let none escape."/>
	<entry name="Drukhari/RazorwingJetfighter:AttackCount" value="4"/>
	<entry name="Drukhari/RazorwingJetfighter:Broken#0" value="Save your own skins!"/>
	<entry name="Drukhari/RazorwingJetfighter:BrokenCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Hurt#0" value="I did not seek a fair fight!"/>
	<entry name="Drukhari/RazorwingJetfighter:HurtCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#0" value="Better here than the arena death races…"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#1" value="Blame it on the Nightwings…"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#2" value="We are the best of the Reavers."/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#3" value="Death from such a distance. The cruelty is that we can't see their torment."/>
	<entry name="Drukhari/RazorwingJetfighter:IdleCount" value="4"/>
	<entry name="Drukhari/RazorwingJetfighter:Shaken#0" value="They're shooting back?"/>
	<entry name="Drukhari/RazorwingJetfighter:ShakenCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Victory#0" value="Bisected beautifully. Never saw us coming."/>
	<entry name="Drukhari/RazorwingJetfighter:VictoryCount" value="1"/>
	<entry name="Drukhari/Scourge:Attack#0" value="Tread upon the dying, not the dirt."/>
	<entry name="Drukhari/Scourge:Attack#1" value="Swooping raptors…"/>
	<entry name="Drukhari/Scourge:Attack#2" value="Do you hear your death upon the wind?"/>
	<entry name="Drukhari/Scourge:Attack#3" value="Pluck their eyes, vulture-kin!"/>
	<entry name="Drukhari/Scourge:AttackCount" value="4"/>
	<entry name="Drukhari/Scourge:Broken#0" value="Take to the air! Aiee!"/>
	<entry name="Drukhari/Scourge:BrokenCount" value="1"/>
	<entry name="Drukhari/Scourge:Hurt#0" value="A brave one who kills a Scourge."/>
	<entry name="Drukhari/Scourge:HurtCount" value="1"/>
	<entry name="Drukhari/Scourge:Idle#0" value="Couriers of the Dark City."/>
	<entry name="Drukhari/Scourge:Idle#1" value="Bearers of intrigue."/>
	<entry name="Drukhari/Scourge:Idle#2" value="Our wealth is in our wings."/>
	<entry name="Drukhari/Scourge:Idle#3" value="My pinions long to be stretched."/>
	<entry name="Drukhari/Scourge:IdleCount" value="4"/>
	<entry name="Drukhari/Scourge:Shaken#0" value="We must rise higher!"/>
	<entry name="Drukhari/Scourge:ShakenCount" value="1"/>
	<entry name="Drukhari/Scourge:Victory#0" value="Listen to those screammms, ahh…"/>
	<entry name="Drukhari/Scourge:VictoryCount" value="1"/>
	<entry name="Drukhari/Succubus:Attack#0" value="Are you not entertained?"/>
	<entry name="Drukhari/Succubus:Attack#1" value="More suitors? Eh."/>
	<entry name="Drukhari/Succubus:Attack#2" value="You who are about to die… I salute you."/>
	<entry name="Drukhari/Succubus:Attack#3" value="What poor challenge you are!"/>
	<entry name="Drukhari/Succubus:AttackCount" value="4"/>
	<entry name="Drukhari/Succubus:Broken#0" value="I will not risk smudging my rouge!"/>
	<entry name="Drukhari/Succubus:BrokenCount" value="1"/>
	<entry name="Drukhari/Succubus:Hurt#0" value="They scarred me!"/>
	<entry name="Drukhari/Succubus:HurtCount" value="1"/>
	<entry name="Drukhari/Succubus:Idle#0" value="More fan mail. Yawn."/>
	<entry name="Drukhari/Succubus:Idle#1" value="Commorragh is the mob. And I control them."/>
	<entry name="Drukhari/Succubus:Idle#2" value="Tell the Archon I require more sweetmeats."/>
	<entry name="Drukhari/Succubus:Idle#3" value="Ugh, I long to return to civilization, to true suffering."/>
	<entry name="Drukhari/Succubus:Idle#4" value="Shadows and dust. Fear and wonder."/>
	<entry name="Drukhari/Succubus:IdleCount" value="5"/>
	<entry name="Drukhari/Succubus:Shaken#0" value="We Brides of Death do not meet our spouse!"/>
	<entry name="Drukhari/Succubus:ShakenCount" value="1"/>
	<entry name="Drukhari/Succubus:Victory#0" value="I have… a talent for survival."/>
	<entry name="Drukhari/Succubus:VictoryCount" value="1"/>
	<entry name="Drukhari/Tantalus:Attack#0" value="Fire the pulse disintegrators!"/>
	<entry name="Drukhari/Tantalus:Attack#1" value="See how the enemies part like waves?"/>
	<entry name="Drukhari/Tantalus:Attack#2" value="Tighten the aethersails! Chase them down!"/>
	<entry name="Drukhari/Tantalus:Attack#3" value="Turn and look to windward."/>
	<entry name="Drukhari/Tantalus:AttackCount" value="4"/>
	<entry name="Drukhari/Tantalus:Broken#0" value="Preserve the ship!"/>
	<entry name="Drukhari/Tantalus:BrokenCount" value="1"/>
	<entry name="Drukhari/Tantalus:Hurt#0" value="The hull is breached! We sink…"/>
	<entry name="Drukhari/Tantalus:HurtCount" value="1"/>
	<entry name="Drukhari/Tantalus:Idle#0" value="Sharpen the scythevanes."/>
	<entry name="Drukhari/Tantalus:Idle#1" value="Careful! A scratch and the Archon will…"/>
	<entry name="Drukhari/Tantalus:Idle#2" value="A design of the Dark Mirror Kabal. Long gone."/>
	<entry name="Drukhari/Tantalus:Idle#3" value="Such pleasure, in its sailing."/>
	<entry name="Drukhari/Tantalus:IdleCount" value="4"/>
	<entry name="Drukhari/Tantalus:Shaken#0" value="The wind has dropped!"/>
	<entry name="Drukhari/Tantalus:ShakenCount" value="1"/>
	<entry name="Drukhari/Tantalus:Victory#0" value="Consider the fallen, once tall and proud as you."/>
	<entry name="Drukhari/Tantalus:VictoryCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Attack#0" value="Bombs away. Don't look at the darklight!"/>
	<entry name="Drukhari/VoidravenBomber:Attack#1" value="Void lance firing. Void mine ready."/>
	<entry name="Drukhari/VoidravenBomber:Attack#2" value="The gunner commands."/>
	<entry name="Drukhari/VoidravenBomber:Attack#3" value="In silence, we soar."/>
	<entry name="Drukhari/VoidravenBomber:AttackCount" value="4"/>
	<entry name="Drukhari/VoidravenBomber:Broken#0" value="Away, they pull us down!"/>
	<entry name="Drukhari/VoidravenBomber:BrokenCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Hurt#0" value="Do not interrupt my work!"/>
	<entry name="Drukhari/VoidravenBomber:HurtCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Idle#0" value="Pilot and Gunner."/>
	<entry name="Drukhari/VoidravenBomber:Idle#1" value="I must compose my next symphony of destruction."/>
	<entry name="Drukhari/VoidravenBomber:Idle#2" value="Silence the engines. Give them the prize of surprise."/>
	<entry name="Drukhari/VoidravenBomber:Idle#3" value="Be careful with the antimatter, you fools!"/>
	<entry name="Drukhari/VoidravenBomber:IdleCount" value="4"/>
	<entry name="Drukhari/VoidravenBomber:Shaken#0" value="An artist cannot work like this!"/>
	<entry name="Drukhari/VoidravenBomber:ShakenCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Victory#0" value="A smoking crater concludes my piece."/>
	<entry name="Drukhari/VoidravenBomber:VictoryCount" value="1"/>
	<entry name="Drukhari/Wrack:Attack#0" value="Realspace creatures…"/>
	<entry name="Drukhari/Wrack:Attack#1" value="Ever-changing torment."/>
	<entry name="Drukhari/Wrack:Attack#2" value="Show me- Give me! Your flesh."/>
	<entry name="Drukhari/Wrack:Attack#3" value="Come, come. To the slab."/>
	<entry name="Drukhari/Wrack:AttackCount" value="4"/>
	<entry name="Drukhari/Wrack:Broken#0" value="Oh, the master will not like this…"/>
	<entry name="Drukhari/Wrack:BrokenCount" value="1"/>
	<entry name="Drukhari/Wrack:Hurt#0" value="I am no-one. No-one is hurt."/>
	<entry name="Drukhari/Wrack:HurtCount" value="1"/>
	<entry name="Drukhari/Wrack:Idle#0" value="I chose this form… from ennui."/>
	<entry name="Drukhari/Wrack:Idle#1" value="What new hypertoxins. What delightful venoms await?"/>
	<entry name="Drukhari/Wrack:Idle#2" value="Obey the Acothyst, they said."/>
	<entry name="Drukhari/Wrack:Idle#3" value="Are we not admirable?"/>
	<entry name="Drukhari/Wrack:IdleCount" value="4"/>
	<entry name="Drukhari/Wrack:Shaken#0" value="Defend the master!"/>
	<entry name="Drukhari/Wrack:ShakenCount" value="1"/>
	<entry name="Drukhari/Wrack:Victory#0" value="More subjects for the master's experiments…"/>
	<entry name="Drukhari/Wrack:VictoryCount" value="1"/>
	<entry name="Drukhari/Wyche:Attack#0" value="Spar with us!"/>
	<entry name="Drukhari/Wyche:Attack#1" value="Watch and learn… then die."/>
	<entry name="Drukhari/Wyche:Attack#2" value="Death dances closer…"/>
	<entry name="Drukhari/Wyche:Attack#3" value="Sweet murderous grace."/>
	<entry name="Drukhari/Wyche:AttackCount" value="4"/>
	<entry name="Drukhari/Wyche:Broken#0" value="I did not train for this."/>
	<entry name="Drukhari/Wyche:BrokenCount" value="1"/>
	<entry name="Drukhari/Wyche:Hurt#0" value="We are blooded! But the fight has just begun."/>
	<entry name="Drukhari/Wyche:HurtCount" value="1"/>
	<entry name="Drukhari/Wyche:Idle#0" value="We must practice."/>
	<entry name="Drukhari/Wyche:Idle#1" value="Our performances feed Commorragh."/>
	<entry name="Drukhari/Wyche:Idle#2" value="Let us prove our skill!"/>
	<entry name="Drukhari/Wyche:Idle#3" value="Artisans, bring us our tools."/>
	<entry name="Drukhari/Wyche:IdleCount" value="4"/>
	<entry name="Drukhari/Wyche:Shaken#0" value="Do they not appreciate our art?!"/>
	<entry name="Drukhari/Wyche:ShakenCount" value="1"/>
	<entry name="Drukhari/Wyche:Victory#0" value="Bow and smile for the audience, sisters."/>
	<entry name="Drukhari/Wyche:VictoryCount" value="1"/>
	<entry name="Drukhari/Ravager" value="Drukhari/Raider"/>
	<entry name="Drukhari/Reaver" value="Drukhari/RazorwingJetfighter"/>
	<entry name="Drukhari/Venom" value="Drukhari/Raider"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#0" value="Activating holy incense exhausts."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#1" value="Blessing the servitor and charging."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#2" value="Destruction over glory."/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#3" value="Lances couched."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:AttackCount" value="4"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Broken#0" value="Protect the engines!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:BrokenCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Hurt#0" value="Critical damage sustained."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:HurtCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#0" value="Resting the servitor."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#1" value="Sydonia is so distant…"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#2" value="Doubt and death are beyond me."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#3" value="Endless motion for an eternal empire!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:IdleCount" value="4"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Shaken#0" value="The stilt-strider stumbles!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:ShakenCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Victory#0" value="For Vingh and Sydonia"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:VictoryCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#0" value="Marking targets."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#1" value="Allowing for wind drift."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#2" value="Very good, sir."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#3" value="We have a firing solution, sir."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:AttackCount" value="4"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Broken#0" value="Leave the gun!"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:BrokenCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Hurt#0" value="Direct hit. On us, sadly."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:HurtCount" value="1"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#0" value="Life in the guard is awfully hard."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#1" value="Gunner who?"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#2" value="Is this a trench or a depression?"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#3" value="No targets? Perfect."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:IdleCount" value="4"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Shaken#0" value="I told you I was ill."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:ShakenCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Victory#0" value="Did we-? I think we got 'em!"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="ChaosSpaceMarines/ChaosSpaceMarine"/>
	<entry name="Eldar/Wraithlord" value="Eldar/Wraithblade"/>
	<entry name="Necrons/SkorpekhDestroyer" value="Necrons/HeavyDestroyer"/>
	<entry name="Orks/BurnaBoyz:Attack#0" value="Lightin' dem up!"/>
	<entry name="Orks/BurnaBoyz:Attack#1" value="Hahaha! Lookit dem burn!"/>
	<entry name="Orks/BurnaBoyz:Attack#2" value="Flamin' nora!"/>
	<entry name="Orks/BurnaBoyz:Attack#3" value="Six on fire!"/>
	<entry name="Orks/BurnaBoyz:AttackCount" value="4"/>
	<entry name="Orks/BurnaBoyz:Broken#0" value="We didn't start the fire!"/>
	<entry name="Orks/BurnaBoyz:BrokenCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Hurt#0" value="Oi fell inta ring uv fire!"/>
	<entry name="Orks/BurnaBoyz:HurtCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Idle#0" value="Luv ta play wid fire."/>
	<entry name="Orks/BurnaBoyz:Idle#1" value="'Ere, gizza light."/>
	<entry name="Orks/BurnaBoyz:Idle#2" value="I don't wanna set da world on fire… wait, yes I do."/>
	<entry name="Orks/BurnaBoyz:Idle#3" value="C'mon, grotty, light me fire!"/>
	<entry name="Orks/BurnaBoyz:IdleCount" value="4"/>
	<entry name="Orks/BurnaBoyz:Shaken#0" value="It burns, burns, burns!"/>
	<entry name="Orks/BurnaBoyz:ShakenCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Victory#0" value="Discount inferno!"/>
	<entry name="Orks/BurnaBoyz:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#0" value="Exculpate!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#1" value="Purgatus!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#2" value="In extremis!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#3" value="Killllll me!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:AttackCount" value="4"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Broken#0" value="Nnnnn-"/>
	<entry name="SistersOfBattle/ArcoFlagellant:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Hurt#0" value="Closer to you, my Emperor"/>
	<entry name="SistersOfBattle/ArcoFlagellant:HurtCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#0" value="Dormant"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#1" value="Faith"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#2" value="Unleash… me."/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#3" value="I… obey."/>
	<entry name="SistersOfBattle/ArcoFlagellant:IdleCount" value="4"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Shaken#0" value="Martyred?"/>
	<entry name="SistersOfBattle/ArcoFlagellant:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Victory#0" value=""/>
	<entry name="SistersOfBattle/ArcoFlagellant:VictoryCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#0" value="Die by my hand!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#1" value="There is only war!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#2" value="None shall survive!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#3" value="We cleanse."/>
	<entry name="SpaceMarines/AssaultTerminator:AttackCount" value="4"/>
	<entry name="SpaceMarines/AssaultTerminator:Broken#0" value="Ambush!"/>
	<entry name="SpaceMarines/AssaultTerminator:BrokenCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Hurt#0" value="Tactical Dreadnought Armour holding."/>
	<entry name="SpaceMarines/AssaultTerminator:HurtCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#0" value="A moment of laxity…"/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#1" value="Our armour is prepared."/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#2" value="First Company standing by."/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#3" value="Blessing our armour."/>
	<entry name="SpaceMarines/AssaultTerminator:IdleCount" value="4"/>
	<entry name="SpaceMarines/AssaultTerminator:Shaken#0" value="By His name!"/>
	<entry name="SpaceMarines/AssaultTerminator:ShakenCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Victory#0" value="For the Emperor!"/>
	<entry name="SpaceMarines/AssaultTerminator:VictoryCount" value="1"/>
	<entry name="Tau/FireWarriorBreacher" value="Tau/FireWarrior"/>
	<entry name="Tyranids/Biovore" value="Tyranids/Carnifex"/>
	<entry name="Drukhari/Talos" value="Drukhari/Cronos"/>
	<entry name="Necrons/CanoptekReanimator" value="Necrons/CanoptekScarab"/>
	<entry name="Tyranids/NornEmissary" value="Tyranids/Carnifex"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#0" value="Ready your lances!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#1" value="In battle, glory!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#2" value="For Attila and the Emperor!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#3" value="On, on, on! Ride them down!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:AttackCount" value="4"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Broken#0" value="Save the horses!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Hurt#0" value="Scars suit a warrior!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:HurtCount" value="1"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Idle#0" value="Attilan tradition will survive even this commander."/>
	<entry name="AstraMilitarum/AttilanRoughRider:Idle#1" value="Will we ever return to the steppe..?"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Idle#2" value="Death riders? Pah."/>
	<entry name="AstraMilitarum/AttilanRoughRider:Idle#3" value="An Attilan's horse is their life!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:IdleCount" value="4"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Shaken#0" value="Craven!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Victory#0" value="A worthy foe!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:VictoryCount" value="1"/>
	<entry name="Drukhari/Mandrake:Attack#0" value="Just an ice-cold touch…"/>
	<entry name="Drukhari/Mandrake:Attack#1" value="Worthy foes…"/>
	<entry name="Drukhari/Mandrake:Attack#2" value="Fine prey…"/>
	<entry name="Drukhari/Mandrake:Attack#3" value="…attack from the shadows."/>
	<entry name="Drukhari/Mandrake:AttackCount" value="4"/>
	<entry name="Drukhari/Mandrake:Broken#0" value="Back to the dark…"/>
	<entry name="Drukhari/Mandrake:BrokenCount" value="1"/>
	<entry name="Drukhari/Mandrake:Hurt#0" value="The worthless fall…"/>
	<entry name="Drukhari/Mandrake:HurtCount" value="1"/>
	<entry name="Drukhari/Mandrake:Idle#0" value="Kheradruakh waits."/>
	<entry name="Drukhari/Mandrake:Idle#1" value="Cold burns so…"/>
	<entry name="Drukhari/Mandrake:Idle#2" value="Aelindrach calls."/>
	<entry name="Drukhari/Mandrake:Idle#3" value="Feed on our kin's fear…"/>
	<entry name="Drukhari/Mandrake:IdleCount" value="4"/>
	<entry name="Drukhari/Mandrake:Shaken#0" value="They dare..?"/>
	<entry name="Drukhari/Mandrake:ShakenCount" value="1"/>
	<entry name="Drukhari/Mandrake:Victory#0" value="Pity… what prey remains?"/>
	<entry name="Drukhari/Mandrake:VictoryCount" value="1"/>
	<entry name="Orks/SquighogBoy:Attack#0" value="Use da Stikkas!"/>
	<entry name="Orks/SquighogBoy:Attack#1" value="WAAAGH!"/>
	<entry name="Orks/SquighogBoy:Attack#2" value="Get da gitz!"/>
	<entry name="Orks/SquighogBoy:Attack#3" value="Snagg dem beasts!"/>
	<entry name="Orks/SquighogBoy:AttackCount" value="4"/>
	<entry name="Orks/SquighogBoy:Broken#0" value="Deze kicked me teef in!"/>
	<entry name="Orks/SquighogBoy:BrokenCount" value="1"/>
	<entry name="Orks/SquighogBoy:Hurt#0" value=""/>
	<entry name="Orks/SquighogBoy:HurtCount" value="1"/>
	<entry name="Orks/SquighogBoy:Idle#0" value="Wrangle dem squigs!"/>
	<entry name="Orks/SquighogBoy:Idle#1" value="Why ain't we krumpin'?"/>
	<entry name="Orks/SquighogBoy:Idle#2" value="Da old ways are da best."/>
	<entry name="Orks/SquighogBoy:Idle#3" value="Oo ate me saddlegit?!"/>
	<entry name="Orks/SquighogBoy:IdleCount" value="4"/>
	<entry name="Orks/SquighogBoy:Shaken#0" value="Don't dey knows oo we iz?!"/>
	<entry name="Orks/SquighogBoy:ShakenCount" value="1"/>
	<entry name="Orks/SquighogBoy:Victory#0" value="We's got da best squighogz!"/>
	<entry name="Orks/SquighogBoy:VictoryCount" value="1"/>
	<entry name="Eldar/Vyper:Attack#0" value="By Kurnous!"/>
	<entry name="Eldar/Vyper:Attack#1" value="Sibling, there!"/>
	<entry name="Eldar/Vyper:Attack#2" value="Strike fast!"/>
	<entry name="Eldar/Vyper:Attack#3" value="The hunter strikes."/>
	<entry name="Eldar/Vyper:AttackCount" value="4"/>
	<entry name="Eldar/Vyper:Broken#0" value="Away, kin, away!"/>
	<entry name="Eldar/Vyper:BrokenCount" value="1"/>
	<entry name="Eldar/Vyper:Hurt#0" value="Closer to our ancestors."/>
	<entry name="Eldar/Vyper:HurtCount" value="1"/>
	<entry name="Eldar/Vyper:Idle#0" value="I feel your anguish, sibling. It will pass."/>
	<entry name="Eldar/Vyper:Idle#1" value="Always ready to move."/>
	<entry name="Eldar/Vyper:Idle#2" value="Practising manouvres."/>
	<entry name="Eldar/Vyper:Idle#3" value="Ah, I long for the Craftworld's wraithbone spires!"/>
	<entry name="Eldar/Vyper:IdleCount" value="4"/>
	<entry name="Eldar/Vyper:Shaken#0" value="Too close, kin, too close."/>
	<entry name="Eldar/Vyper:ShakenCount" value="1"/>
	<entry name="Eldar/Vyper:Victory#0" value="Age wins out."/>
	<entry name="Eldar/Vyper:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#0" value="Resonance: Matched."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#1" value="Howl: Transonics."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#2" value="Rage: Released."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#3" value="Crippled: Enemy."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Broken#0" value="Courage: Lost"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Hurt#0" value="Repeat: Death"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Idle#0" value="Idle: Cycling."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Idle#1" value="Data Request: Relax Tapes."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Idle#2" value="Learn: Lingua Technis."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:IdleCount" value="3"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Shaken#0" value="Danger: Extreme."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Victory#0" value="Triumph: Expected."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:VictoryCount" value="1"/>
</language>