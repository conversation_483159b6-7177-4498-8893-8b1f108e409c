<?xml version="1.0" encoding="utf-8"?>
<building>
	<modifiers>
		<modifier visible="0">
			<effects>
				<influenceUpkeep add="2"/>
				<biomassCost base="50"/>
				<productionCost base="36"/>
				<populationRequired base="1"/>
				<slotsRequired base="1"/>
			</effects>
		</modifier>
		<modifier>
			<effects>
				<biomass add="2"/>
				<loyalty add="1"/>
				<production add="6"/>
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseInfantryProductionScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<reclaimUnit cooldown="1"
				name="TyranidsClone/ReclaimUnit"
				interfaceSound="Interface/ReclaimUnit">
			<modifiers>
				<modifier visible="0">
					<effects>
						<influenceCost add="10"/>
					</effects>
				</modifier>
				<modifier requiredUpgrade="TyranidsClone/Reclamation2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
				<modifier requiredUpgrade="TyranidsClone/Reclamation3">
					<effects>
						<cooldown max="0"/>
					</effects>
				</modifier>
			</modifiers>
		</reclaimUnit>
		<produceUnit unit="TyranidsClone/Termagant"/>
		<produceUnit unit="TyranidsClone/Hormagaunt" requiredUpgrade="TyranidsClone/Hormagaunt"/>
		<produceUnit unit="TyranidsClone/Gargoyle" requiredUpgrade="TyranidsClone/Gargoyle"/>
		<produceUnit unit="TyranidsClone/Warrior" requiredUpgrade="TyranidsClone/Warrior"/>
		<produceUnit unit="TyranidsClone/HiveGuard" requiredUpgrade="TyranidsClone/HiveGuard"/>
		<produceUnit unit="TyranidsClone/Ravener" requiredUpgrade="TyranidsClone/Ravener"/>
		<produceUnit unit="TyranidsClone/Biovore" requiredUpgrade="TyranidsClone/Biovore"/>
		<produceUnit unit="TyranidsClone/Lictor" requiredUpgrade="TyranidsClone/Lictor"/>
	</actions>
	<traits>
		<trait name="TyranidsClone/ProductionBuildingUpkeep" requiredUpgrade="TyranidsClone/ProductionBuildingUpkeep"/>
	</traits>
</building>
