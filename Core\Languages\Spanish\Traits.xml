<?xml version="1.0" encoding="utf-8"?> 
<language>
	<entry name="Artefacts/Accuracy" value="<string name='Units/Neutral/Artefacts/Accuracy'/>"/>
	<entry name="Artefacts/AccuracyDescription" value="Aumenta la puntería."/>
	<entry name="Artefacts/Armor" value="<string name='Units/Neutral/Artefacts/Armor'/>"/>
	<entry name="Artefacts/ArmorDescription" value="Aumenta el blindaje."/>
	<entry name="Artefacts/ArmorPenetration" value="<string name='Units/Neutral/Artefacts/ArmorPenetration'/>"/>
	<entry name="Artefacts/ArmorPenetrationDescription" value="Aumenta la penetración del blindaje."/>
	<entry name="Artefacts/Damage" value="<string name='Units/Neutral/Artefacts/Damage'/>"/>
	<entry name="Artefacts/DamageDescription" value="Aumenta el daño."/>
	<entry name="Artefacts/Healing" value="<string name='Units/Neutral/Artefacts/Healing'/>"/>
	<entry name="Artefacts/HealingDescription" value="Restaura puntos de vida cada turno."/>
	<entry name="Artefacts/Hitpoints" value="<string name='Units/Neutral/Artefacts/Hitpoints'/>"/>
	<entry name="Artefacts/HitpointsDescription" value="Aumenta los puntos de vida."/>
	<entry name="Artefacts/Loyalty" value="<string name='Units/Neutral/Artefacts/Loyalty'/>"/>
	<entry name="Artefacts/LoyaltyDescription" value="Aumenta la lealtad."/>
	<entry name="Artefacts/Movement" value="<string name='Units/Neutral/Artefacts/Movement'/>"/>
	<entry name="Artefacts/MovementDescription" value="Aumenta el movimiento."/>
	<entry name="Artefacts/Sight" value="<string name='Units/Neutral/Artefacts/Sight'/>"/>
	<entry name="Artefacts/SightDescription" value="Aumenta la visión."/>
	<entry name="Items/AdamantiumWeaveVest" value="<string name='Items/AdamantiumWeaveVest'/>"/>
	<entry name="Items/AdamantiumWeaveVestDescription" value="<string name='Items/AdamantiumWeaveVestDescription'/>"/>
	<entry name="Items/AdamantiumWeaveVestFlavor" value="<string name='Items/AdamantiumWeaveVestFlavor'/>"/>
	<entry name="Items/ArmaplasBracers" value="<string name='Items/ArmaplasBracers'/>"/>
	<entry name="Items/ArmaplasBracersDescription" value="<string name='Items/ArmaplasBracersDescription'/>"/>
	<entry name="Items/ArmaplasBracersFlavor" value="<string name='Items/ArmaplasBracersFlavor'/>"/>
	<entry name="Items/AxeOfBlindFury" value="<string name='Items/AxeOfBlindFury'/>"/>
	<entry name="Items/AxeOfBlindFuryDescription" value="<string name='Items/AxeOfBlindFuryDescription'/>"/>
 	<entry name="Items/AxeOfBlindFuryFlavor" value="<string name='Items/AxeOfBlindFuryFlavor'/>"/>
	<entry name="Items/CombatStimulant" value="<string name='Items/CombatStimulant'/>"/>
	<entry name="Items/CombatStimulantDescription" value="<string name='Items/CombatStimulantDescription'/>"/>
	<entry name="Items/CombatStimulantFlavor" value="<string name='Items/CombatStimulantFlavor'/>"/>
	<entry name="Items/ConcealedWeaponSystem" value="<string name='Items/ConcealedWeaponSystem'/>"/>
	<entry name="Items/ConcealedWeaponSystemDescription" value="<string name='Items/ConcealedWeaponSystemDescription'/>"/>
	<entry name="Items/ConcealedWeaponSystemFlavor" value="<string name='Items/ConcealedWeaponSystemFlavor'/>"/>
	<entry name="Items/DuskBlade" value="<string name='Items/DuskBlade'/>"/>
	<entry name="Items/DuskBladeDescription" value="<string name='Items/DuskBladeDescription'/>"/>
	<entry name="Items/DuskBladeFlavor" value="<string name='Items/DuskBladeFlavor'/>"/>
	<entry name="Items/EnduranceImplant" value="<string name='Items/EnduranceImplant'/>"/>
	<entry name="Items/EnduranceImplantDescription" value="<string name='Items/EnduranceImplantDescription'/>"/>
	<entry name="Items/EnduranceImplantFlavor" value="<string name='Items/EnduranceImplantFlavor'/>"/>
	<entry name="Items/EntropicLocum" value="<string name='Items/EntropicLocum'/>"/>
	<entry name="Items/EntropicLocumDescription" value="<string name='Items/EntropicLocumDescription'/>"/>
	<entry name="Items/EntropicLocumFlavor" value="<string name='Items/EntropicLocumFlavor'/>"/>
	<entry name="Items/FaolchusWing" value="<string name='Items/FaolchusWing'/>"/>
	<entry name="Items/FaolchusWingDescription" value="<string name='Items/FaolchusWingDescription'/>"/>
	<entry name="Items/FaolchusWingFlavor" value="<string name='Items/FaolchusWingFlavor'/>"/>
	<entry name="Items/LightningGauntlet" value="<string name='Items/LightningGauntlet'/>"/>
	<entry name="Items/LightningGauntletDescription" value="<string name='Items/LightningGauntletDescription'/>"/>
	<entry name="Items/LightningGauntletFlavor" value="<string name='Items/LightningGauntletFlavor'/>"/>
	<entry name="Items/MourningBladeOfLazaerek" value="<string name='Items/MourningBladeOfLazaerek'/>"/>
	<entry name="Items/MourningBladeOfLazaerekDescription" value="<string name='Items/MourningBladeOfLazaerekDescription'/>"/>
	<entry name="Items/MourningBladeOfLazaerekFlavor" value="<string name='Items/MourningBladeOfLazaerekFlavor'/>"/>
	<entry name="Items/OmniScope" value="<string name='Items/OmniScope'/>"/>
 	<entry name="Items/OmniScopeDescription" value="<string name='Items/OmniScopeDescription'/>"/>
 	<entry name="Items/OmniScopeFlavor" value="<string name='Items/OmniScopeFlavor'/>"/>
	<entry name="Items/PoweredGauntlet" value="<string name='Items/PoweredGauntlet'/>"/>
	<entry name="Items/PoweredGauntletDescription" value="<string name='Items/PoweredGauntletDescription'/>"/>
	<entry name="Items/PoweredGauntletFlavor" value="<string name='Items/PoweredGauntletFlavor'/>"/>
	<entry name="Items/ScrollsOfMagnus" value="<string name='Items/ScrollsOfMagnus'/>"/>
 	<entry name="Items/ScrollsOfMagnusDescription" value="<string name='Items/ScrollsOfMagnusDescription'/>"/>
 	<entry name="Items/ScrollsOfMagnusFlavor" value="<string name='Items/ScrollsOfMagnusFlavor'/>"/>
	<entry name="Items/SightlessHelm" value="<string name='Items/SightlessHelm'/>"/>
	<entry name="Items/SightlessHelmDescription" value="<string name='Items/SightlessHelmDescription'/>"/>
	<entry name="Items/SightlessHelmFlavor" value="<string name='Items/SightlessHelmFlavor'/>"/>
 	<entry name="Items/TantalisingIcon" value="<string name='Items/TantalisingIcon'/>"/>
 	<entry name="Items/TantalisingIconDescription" value="<string name='Items/TantalisingIconDescription'/>"/>
 	<entry name="Items/TantalisingIconFlavor" value="<string name='Items/TantalisingIconFlavor'/>"/>
	<entry name="Items/TemporaryShield" value="<string name='Items/TemporaryShield'/>"/>
	<entry name="Items/TemporaryShieldDescription" value="<string name='Items/TemporaryShieldDescription'/>"/>
	<entry name="Items/TemporaryShieldFlavor" value="<string name='Items/TemporaryShieldFlavor'/>"/>
	<entry name="Items/UltraWidebandAuspex" value="<string name='Items/UltraWidebandAuspex'/>"/>
	<entry name="Items/UltraWidebandAuspexDescription" value="<string name='Items/UltraWidebandAuspexDescription'/>"/>
	<entry name="Items/UltraWidebandAuspexFlavor" value="<string name='Items/UltraWidebandAuspexFlavor'/>"/>	
	<entry name="Items/VolcanisShroud" value="<string name='Items/VolcanisShroud'/>"/>
	<entry name="Items/VolcanisShroudDescription" value="<string name='Items/VolcanisShroudDescription'/>"/>
	<entry name="Items/VolcanisShroudFlavor" value="<string name='Items/VolcanisShroudFlavor'/>"/>
	<entry name="Items/ZoatHideJerkin" value="<string name='Items/ZoatHideJerkin'/>"/>
	<entry name="Items/ZoatHideJerkinDescription" value="<string name='Items/ZoatHideJerkinDescription'/>"/>
	<entry name="Items/ZoatHideJerkinFlavor" value="<string name='Items/ZoatHideJerkinFlavor'/>"/>
 	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="Integración Adyacente"/>
 	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Aumenta la producción de investigación por cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="El redescubrimiento de conocimiento es un ritual tan sagrado, que se han ordenado ritos de apoyo a lo largo y ancho de la colmena. Según aumenta la densidad de población, los fieles constantemente escancian libaciones e imprecan a los espíritus máquina integrados de la ciudad para que revelen sus secretos a los tecnosacerdotes del Librarium Omnis."/>
 	<entry name="AdeptusMechanicus/AggressionOverride" value="<string name='Actions/AdeptusMechanicus/AggressionOverride'/>"/>
 	<entry name="AdeptusMechanicus/AggressionOverrideDescription" value="Aumenta los ataques."/>
 	<entry name="AdeptusMechanicus/AggressionOverrideFlavor" value="<string name='Actions/AdeptusMechanicus/AggressionOverrideFlavor'/>"/>
 	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
 	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="Aumenta el movimiento, pero reduce el blindaje."/>
 	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="Dogma Metálica"/>
 	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
 	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="Los zelotas de Metálica son conocidos por traer consigo un estridente clamor, recuerdo de las incansables industrias de sus mundos forja. Y también por no tomar pausa en su imparable avance, borrando a sus enemigos sin romper el paso."/>
 	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
 	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="Aumenta la precisión a distancia."/>
 	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Bionics" value="Biónicos"/>
 	<entry name="AdeptusMechanicus/BionicsDescription" value="Aumenta la reducción de daño invulnerable."/>
 	<entry name="AdeptusMechanicus/BionicsFlavor" value="Es raro el Magos del Adeptus Mechanicus que no haya augmentado su cuerpo, desde los tiempos de Arkhan Land. Algunos, más vanidosos, esconden sus augméticos, pero la mayoría de augmentos biónicos son deliberadamente crudos e inhumanos, actuando como símbolos físicos de la bendición del Omnissiah."/>
 	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Actions/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
 	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Reduce la pérdida de moral."/>
 	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Actions/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
 	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
 	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="Aumenta el blindaje, pero reduce el movimiento."/>
 	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiah" value="Cánticos del Omnissiah"/>
 	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiahDescription" value="Clasificación."/>
 	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiahFlavor" value="En tiempos de guerra, los discípulos del Omnissiah entonan complejas bendiciones guerreras. Son tanto subrutinas de optimización como expresión de su fe en su omnisciente, omnipresente, deidad."/>
 	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
 	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="Aumenta la precisión cuerpo a cuerpo."/>
 	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
 	<entry name="AdeptusMechanicus/CityTier2" value="Mavoraformación"/>
 	<entry name="AdeptusMechanicus/CityTier2Description" value="Aumenta el radio de adquisición de casillas."/>
 	<entry name="AdeptusMechanicus/CityTier2Flavor" value="La Terraformación es el, irónicamente denominado, proceso de hacer que un planeta sea más adecuado para los humanos, a diferencia de la devastada Terra, escondida bajo la masa del Palacio Imperial, que ocupa continentes. La Mavoraformación no tiene tantas pretensiones, pues se trata de explotar un planeta, envenenarlo, con ciudades increiblemente productivas, justo como en casa."/>
 	<entry name="AdeptusMechanicus/CityTier3" value="Aprobación de Mundo Colmena"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="Aumenta el radio de adquisición de casillas."/>
 	<entry name="AdeptusMechanicus/CityTier3Flavor" value="La conversión final de un planeta en un Mundo Colmena se puede ver desde el espacio. Desaparecen las colinas, los valles, los árboles. Se eliminan los lagos, los mares, la atmósfera respirable. Una mordiente arena sopla sobre zonas polvorientas, donde unos pocos Imperiales recalcitrantes arrancan su subsistencia de granjas situadas a la sombra de colosales agujas de colmena, llenas de miles de millones de almas."/>
 	<entry name="AdeptusMechanicus/Cognis" value="Cognis"/>
 	<entry name="AdeptusMechanicus/CognisDescription" value="Limita la pérdida de precisión."/>
 	<entry name="AdeptusMechanicus/CognisFlavor" value="<string name='Weapons/CognisFlavor'/>"/>
 	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Actions/AdeptusMechanicus/CommandUplink'/>"/>
 	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Reduce la pérdida de moral."/>
 	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Actions/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
 	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="Aumenta la precisión cuerpo a cuerpo, pero reduce la precisión a distancia."/>
 	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ControlEdict" value="<string name='Actions/AdeptusMechanicus/ControlEdict'/>"/>
 	<entry name="AdeptusMechanicus/ControlEdictDescription" value="Elimina las Doctrina Imperatives malus."/>
 	<entry name="AdeptusMechanicus/ControlEdictFlavor" value="<string name='Actions/AdeptusMechanicus/ControlEdictFlavor'/>"/>
 	<entry name="AdeptusMechanicus/DartingHunters" value="Rápidos Cazadores"/>
 	<entry name="AdeptusMechanicus/DartingHuntersDescription" value="Las acciones no consumen movimientos."/>
 	<entry name="AdeptusMechanicus/DartingHuntersFlavor" value="Los reflejos de los Pteraxii se acentuan al recortar elementos de cogitación que impiden su función primaria."/>
 	<entry name="AdeptusMechanicus/DataBlessedAutosermon" value="<string name='Actions/AdeptusMechanicus/DataBlessedAutosermon'/>"/>
 	<entry name="AdeptusMechanicus/DataBlessedAutosermonDescription" value="Otorga investigación al eliminar a un enemigo."/>
 	<entry name="AdeptusMechanicus/DataBlessedAutosermonFlavor" value="<string name='Actions/AdeptusMechanicus/DataBlessedAutosermonFlavor'/>"/>
 	<entry name="AdeptusMechanicus/DoctrinaImperatives" value="Doctrina Imperatives"/>
 	<entry name="AdeptusMechanicus/DoctrinaImperativesDescription" value="Clasificación."/>
 	<entry name="AdeptusMechanicus/DoctrinaImperativesFlavor" value="Los Skitarii son enemigos temibles, incansables en la persecución de la agenda del Omnissiah y equipados con el armamento más avanzado del Imperio. En el fondo, sin embargo, cada uno no es más que un recipiente cibernético de la voluntad de los tecnosacerdotes. En el calor de la batalla, los Skitarii serán controlados remotamente mediante imperativos de datos que potencian sus mentes y cuerpos hasta niveles inhumanos."/>
 	<entry name="AdeptusMechanicus/Dunestrider" value="Cruzadunas"/>
 	<entry name="AdeptusMechanicus/DunestriderDescription" value="Aumenta el movimiento."/>
 	<entry name="AdeptusMechanicus/DunestriderFlavor" value="Algunos Skitarii son capaces de marchar a través del terreno más hostil a un ritmo incansable, sin que sus extremidades auméticas se cansen o desgasten."/>
 	<entry name="AdeptusMechanicus/EmanatusForceField" value="Campo de Fuerza Emanatus"/>
 	<entry name="AdeptusMechanicus/EmanatusForceFieldDescription" value="Aumenta la reducción de daño."/>
 	<entry name="AdeptusMechanicus/EmanatusForceFieldFlavor" value="Los campos de fuerza entrelazados de los Trepadunas son maravillas de la ciencia militar. Como los campos refractores comunes a las órdenes más bajas del sacerdocio de Marte, dispersan energías hostiles en la atmósfera, convirtiendo a cada proyectil que los alcanza en poco más que un destello de actínica luz azul y un repentino olor a ozono."/>
 	<entry name="AdeptusMechanicus/EnhancedDataTether" value="Cadena de Datos Mejorada"/>
 	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Aumenta la moral."/>
 	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="Considerados los portavoces de los tecnosacerdotes, quien a su vez son los profetas del mismo Dios Máquina, aquellos honrados con las cadenas de datos mejoradas son obedecidos sin vacilación por sus reverentes camaradas Skitarii."/>
 	<entry name="AdeptusMechanicus/EnrichedRounds" value="<string name='Actions/AdeptusMechanicus/EnrichedRounds'/>"/>
 	<entry name="AdeptusMechanicus/EnrichedRoundsDescription" value="Aumenta el daño."/>
 	<entry name="AdeptusMechanicus/EnrichedRoundsFlavor" value="<string name='Actions/AdeptusMechanicus/EnrichedRoundsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/EnslavedToThePast" value="Esclavizado al Pasado"/>
 	<entry name="AdeptusMechanicus/EnslavedToThePastDescription" value="Aumenta el coste de investigación."/>
 	<entry name="AdeptusMechanicus/EnslavedToThePastFlavor" value="En el fondo, las ciudadelas del conocimiento del Culto están cimentadas sobre mentiras. Hace tiempo que se perdió la habilidad de innovar de verdad, reemplazada por la reverencia por los tiempos en los cuales la Humanidad era la arquitecta de su propio destino. Ya no es la maestra de sus creaciones, el Culto Mechanicus está esclavizado al pasado. Mantiene las glorias de antaño con ritos, dogmas y edictos en lugar de discernimiento y comprensión. Incluso el, en teoría, simple proceso de activar un arma se precede con la aplicación de aceites rituales, la quema de resinas sagradas y el cántico de largos y complejos himnos. Pero mientras el proceso funcione o, mejor dicho, mientras los ejércitos del Culto puedan eliminar a aquellos que les desagradan, los tecnosacerdotes se conforman con caminar por la traicionera senda hacia la entropía y la ignorancia."/>
 	<entry name="AdeptusMechanicus/GuldiresOrison" value="Guldires Orison"/>
 	<entry name="AdeptusMechanicus/GuldiresOrisonDescription" value="Reduce la precisión."/>
 	<entry name="AdeptusMechanicus/GuldiresOrisonFlavor" value="Guldire era un importante Herrero de la Disformidad de los Portadores de la Palabra bajo Erebus. Su ‘Orison’ es una oración de puro código máquina corrupto, portando susurros demoníacos desde el corazón de la disformidad. Aunque ha perdido su letalidad gracias a las defensas Imperiales, sigue siendo muy distractivo…"/>
 	<entry name="AdeptusMechanicus/FidorumVossPrime" value="Fidorum Voss Prime"/>
 	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Aumenta la producción de influencia para cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="El Sacerdocio del Culto Mechanicus venera, por encima de todos, al Mundo Forja de Voss Prime por su lealtad: su lealtad al Imperio es intachable. Incluso cuando el propio Marte cayó en manos del Mechanicum Oscuro, Voss Prime continuo produciendo armas como churros y fue instrumental en la derrota de la Herejía de Horus."/>
 	<entry name="AdeptusMechanicus/GalvanicField" value="<string name='Actions/AdeptusMechanicus/GalvanicField'/>"/>
 	<entry name="AdeptusMechanicus/GalvanicFieldDescription" value="Aumenta la penetración de blindaje."/>
 	<entry name="AdeptusMechanicus/GalvanicFieldFlavor" value="<string name='Actions/AdeptusMechanicus/GalvanicFieldFlavor'/>"/>
 	<entry name="AdeptusMechanicus/EntropicDestabilisation" value="Desestabilización Entrópica"/>
 	<entry name="AdeptusMechanicus/EntropicDestabilisationDescription" value="Otorga reducción de daño."/>
 	<entry name="AdeptusMechanicus/EntropicDestabilisationFlavor" value="¡Te sientes vigorizado por la bendición del Omnissiah! Nada puede interponerse en tu camino (¿Es un sueño? ¿¡Has visto al Omnissiah!?)"/>
 	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="Scriptorum Ordinatus"/>
 	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
 	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="Los Ordinati son dispositivos únicos con el tamaño de un titán, cada uno construido para un único propósito y celosamente guardados. Tus investigaciones han desenterrado enseñanzas aprobadas de la creación del Ordinatus Oberon, un cañón espacial montado sobre orugas, empleado durante las Guerras por Armageddon."/>
 	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="Iconos del Omnissiah"/>
 	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Aumenta la producción de lealtad de cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="Protégenos, maestro. Líbranos, Dios Máquina. El Omnissiah viene. El Omnissiah viene. EL OMNISSIAN VIENE. Castiga la carne."/>
 	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
 	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="Reduce la pérdida de moral."/>
 	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
	> <entry name="AdeptusMechanicus/IncenseCloud" value="Nube de Incienso"/>
	<entry name="AdeptusMechanicus/IncenseCloudDescription" value="Aumenta la reducción de daño a distancia."/>
	<entry name="AdeptusMechanicus/IncenseCloudFlavor" value="Los secretos detrás de la creación por Aldebrac Vingh de los motores de movimiento perpetuo que impulsan a los antiguos Ironstriders se han perdido, por lo que son realmente venerados por los Cultistas de Marte. El incienso sagrado que los envuelve es a la vez un símbolo de su veneración y una cobertura eficaz contra el fuego enemigo."/>
 	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
 	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="Aumenta el daño cuerpo a cuerpo."/>
 	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
 	<entry name="AdeptusMechanicus/IonShield" value="Escudo Iónico"/>
 	<entry name="AdeptusMechanicus/IonShieldDescription" value="Aumenta la reducción de daño."/>
 	<entry name="AdeptusMechanicus/IonShieldFlavor" value="Los Caballeros portan potentes generadores de campo llamados escudo de iones. Estos dispositivos emplean tecnología antigua para proyectar un campo de energía en un estrecho arco. Moviendo la posición del escudo de manera que bloquee los ataques enemigos, un Caballero puede sobrevivir al fuego más intenso, pudiendo responder con su propio armamento. El ajuste y posición del escudo son esenciales, puesto que el escudo de iones sólo está diseñado para desviar y ralentizar proyectiles, en vez de absorberlos como hacen los escudos de vacío empleados en los Titanes Imperiales. Esto implica que la efectividad del escudo depende de la habilidad y experiencia de su operador."/>
 	<entry name="AdeptusMechanicus/LordOfTheMachineCult" value="<string name='Actions/AdeptusMechanicus/LordOfTheMachineCult'/>"/>
 	<entry name="AdeptusMechanicus/LordOfTheMachineCultDescription" value="Aumenta la precisión."/>
 	<entry name="AdeptusMechanicus/LordOfTheMachineCultFlavor" value="<string name='Actions/AdeptusMechanicus/LordOfTheMachineCultFlavor'/>"/>
 	<entry name="AdeptusMechanicus/LucianSpecialisation" value="Especialización Lucian"/>
 	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="Aumenta la producción de recursos de edificios del mismo tipo que no sean cuartel general que se encuentren en la misma casilla."/>
 	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="Dentro del Mundo Forja hueco de Lucius se encuentra un sol, pero para el Adeptus Mechanicus esto es menos interesante que sus especializaciones: el metal único “Luciun” y el viaje a través de la disformidad. Los Magos de Lucius animan a perseguir la especialización profunda."/>
 	<entry name="AdeptusMechanicus/MechanicusLocum" value="<string name='Actions/AdeptusMechanicus/MechanicusLocum'/>"/>
 	<entry name="AdeptusMechanicus/MechanicusLocumDescription" value="<string name='Actions/AdeptusMechanicus/MechanicusLocumDescription'/>"/>
 	<entry name="AdeptusMechanicus/MechanicusLocumFlavor" value="<string name='Actions/AdeptusMechanicus/MechanicusLocumFlavor'/>"/>
 	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="Salvajismo Ryzan"/>
 	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
 	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="A pesar de la especialización del planeta en plasma y escudos, las invasiones Orkas son tan comunes en el Mundo Forja de Ryza que los defensores son conocidos por su gusto, muy Orko, por el combate cuerpo a cuerpo."/>
 	<entry name="AdeptusMechanicus/MonolithicBuildings" value="Edificios Monolíticos"/>
 	<entry name="AdeptusMechanicus/MonolithicBuildingsDescription" value="Aumenta la producción de recursos de edificios del mismo tipo situados en una casilla. Reduce la producción de edificios de diferente tipo que no sean cuarteles generales situados en una casilla."/>
 	<entry name="AdeptusMechanicus/MonolithicBuildingsFlavor" value=""/>
 	<entry name="AdeptusMechanicus/MonolithicBuildingsFlavor" value="En la uniformidad, especialización. En la especialización, eficiencia. En la eficiencia, éxtasis..”<br/>—Kelphor Zhuko-Dim, Archi Flagelador"/>
	<entry name="AdeptusMechanicus/NeurostaticInterface" value="Interfaz Neuroestática"/>
 	<entry name="AdeptusMechanicus/NeurostaticInterfaceDescription" value="Aumenta la reducción de daño cuando el ataque provenga de unidades enemigas adyacentes."/>
 	<entry name="AdeptusMechanicus/NeurostaticInterfaceFlavor" value="Entre la cacofonía audio-visual provocada por el ataque de un Infiltrador Sicariano, se puede encontrar una interferencia electromagnética de banda ancha, diseñada para incapacitar al enemigo e inutilizar sus nervios."/>
 	<entry name="AdeptusMechanicus/Omnispex" value="Omnispex"/>
 	<entry name="AdeptusMechanicus/OmnispexDescription" value="Ignora la reducción de daño a distancia del enemigo."/>
 	<entry name="AdeptusMechanicus/OmnispexFlavor" value="El omnispex monta un espíritu máquina de clase raptor que puede detectar emisiones de calor, firmas de datos y ondas biológicas incluso a distancias extremas. Si se mantiene focalizado durante un prolongado espacio de tiempo, determinará las debilidades de los objetivos escrutados y pasará esta información a sus maestros."/>
 	<entry name="AdeptusMechanicus/OptateRestrictions" value="Restricciones Optate"/>
 	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Aumenta el límite de población."/>
 	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="Los Optates que controlan las poblaciones de las ciudades-colmena han acordado levantar las restricciones a la reproducción. En la práctica, esto significa la retirada de los anafrodisíacos de las raciones, antes que la construcción de nuevos edificios: los dormitorios existentes estarán mucho más abarrotados…"/>
 	<entry name="AdeptusMechanicus/PowerSurge" value="<string name='Actions/AdeptusMechanicus/PowerSurge'/>"/>
 	<entry name="AdeptusMechanicus/PowerSurgeDescription" value="Aumenta la producción de edificios que no sean cuarteles generales."/>
 	<entry name="AdeptusMechanicus/PowerSurgeFlavor" value="<string name='Actions/AdeptusMechanicus/PowerSurgeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
 	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="Aumenta la precisión a distancia, pero reduce la de cuerpo a cuerpo."/>
 	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/RadPoisoning" value="Envenenamiento Rad"/>
 	<entry name="AdeptusMechanicus/RadPoisoningDescription" value="Aumenta el daño frente a unidades de infantería y criaturas monstruosas."/>
 	<entry name="AdeptusMechanicus/RadSaturation" value="<string name='Actions/AdeptusMechanicus/RadSaturation'/>"/>
 	<entry name="AdeptusMechanicus/RadSaturationDescription" value="Reduce los puntos de vida y el daño cuerpo a cuerpo."/>
 	<entry name="AdeptusMechanicus/RadSaturationFlavor" value="<string name='Actions/AdeptusMechanicus/RadSaturationFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="Protocolos Reclamadores"/>
 	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Aumenta la tasa de crecimiento."/>
 	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="Nada se desperdicia: ni una pizca de comida, ni un vatio de energía, ni un trozo de carne. Las ejecuciones se minimizan en favor de las conversione a servidores, lo que da como resultado un vigor renovado en todas las demás actividades…"/>
 	<entry name="AdeptusMechanicus/ServoSkullUplink" value="<string name='Actions/AdeptusMechanicus/ServoSkullUplink'/>"/>
 	<entry name="AdeptusMechanicus/ServoSkullUplinkDescription" value="Aumenta el daño."/>
 	<entry name="AdeptusMechanicus/ServoSkullUplinkFlavor" value="<string name='Actions/AdeptusMechanicus/ServoSkullUplinkFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
 	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="Aumenta la reducción del daño a distancia."/>
 	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SiphonVigour" value="Absorber Vigor"/>
 	<entry name="AdeptusMechanicus/SiphonVigourDescription" value="Eliminar a un enemigo aumenta la reducción de daño invulnerable."/>
 	<entry name="AdeptusMechanicus/SiphonVigourFlavor" value="<string name='Traits/AdeptusMechanicus/SiphonedVigourFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SiphonedVigour" value="Vigor Absorbido"/>
 	<entry name="AdeptusMechanicus/SiphonedVigourDescription" value="Aumenta la reducción de daño invulnerable."/>
 	<entry name="AdeptusMechanicus/SiphonedVigourFlavor" value="Puede que estén obsesionados con el desperdicio de energía, pero los electrosacerdotes no son hipócritas. Cuando un enemigo muere bajo los golpes de sus bastones electrosanguijuelas, los campos protectores Voltagheist se refuerzan con energía bioeléctrica, repeliendo incluso los ataques más poderosos."/>
 	<entry name="AdeptusMechanicus/SolarReflectors" value="Reflectores Solares"/>
 	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Aumenta la producción de energía de cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="Planificar, planificar, planificar. Los Magos del Adeptus Mechanicus diseñan cada ciudad de modo que funcione como un todo, cada parte apoyando a otras. Reflectores solares en todas las estructuras dirigen la pequeña cantidad de energía que capturan a través de los sistemas de tormentas de Gladius Prime hacia el Templo de Intercambio Térmico más cercano."/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="Equipos de Adquisición Soylens"/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Aumenta la producción de comida para edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="Los Reclamadores emplean un pequeño número de habitantes de los bajos fondos en un poco apetecible rol. Estos Equipos de Adquisición Soylens son más conocidos como robacadáveres, y buscan agresivamente los cuerpos de los muertos (o los casi muertos) para asegurar que las raciones futuras tengan las proteínas soylens más frescas…"/>
 	<entry name="AdeptusMechanicus/StygianEnlightenment" value="Iluminación Stygian"/>
 	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="Reduce el coste de investigación."/>
 	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="Algunos tecnosacerdotes están más abiertos que otros al estudio de la tecnología Xeno. Únicamente el Mundo Forja de Stygies VIII escapa al castigo de estas tendencias Xenaritas, en parte por la importancia de su mundo, en parte porque incapacita a cualquier atacante. Seguir su camino es, desde luego, una manera rápida de obtener acceso a tecnologías prohibidas."/>
 	<entry name="AdeptusMechanicus/TerranGeneralism" value="Generalismo Terrano"/>
 	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="Reduce la producción de recursos de edificios de diferente tipo que no son cuarteles generales en una casilla."/>
 	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="Aunque muchos Mundos Forja se han especializado en necesidades específicas y actuan como imperios independientes dentro de la esfera humana, merece la pena recordar que la mayoría de los tecnosacerdotes son generalistas, apoyando lealmente al Imperio en su camino a las estrellas, y siguiendo las órdenes tanto de Terra como de Marte."/>
	<entry name="AdeptusMechanicus/Transonic" value="Tránsonico"/>
	<entry name="AdeptusMechanicus/TransonicDescription" value="Aumenta el daño y aumenta temporalmente la penetración de armadura al atacar."/>
	<entry name="AdeptusMechanicus/TransonicFlavor" value="<string name='Weapons/TransonicBladeFlavor'/>"/>
	<entry name="AdeptusMechanicus/TransonicEffect" value="Resonancia Transónica"/>
	<entry name="AdeptusMechanicus/TransonicEffectDescription" value="Aumenta la penetración de armadura."/>
	<entry name="AdeptusMechanicus/TransonicEffectFlavor" value="<string name='Weapons/TransonicBladeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/TriplexNecessity" value="Necesidad Triplex"/>
 	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Aumenta la producción de minerales de cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="Siguiendo nuevos diseños descubiertos en Triplex Phall, durante la construcción de nuevas estructuras se presta atención a la exploración de cualquier área subterránea y se cartografían posibles vetas de minerales. Si se trata de un Reactor Haemótropo, su plasma se puede dirigir al subsuelo a través de rutas preparadas para extraer el mineral de manera más eficiente."/>
 	<entry name="AdeptusMechanicus/VoltagheistField" value="Campo Voltagheist"/>
 	<entry name="AdeptusMechanicus/VoltagheistFieldDescription" value="Aumenta la reducción de daño invulnerable."/>
 	<entry name="AdeptusMechanicus/VoltagheistFieldFlavor" value="Nimbos de pura energía envuelven a todos los electrosacerdotes, chasqueando desde su desnuda piel para crear pequeñas bolsas de electromagnetismo que levitan como fuegos de San Telmo sobre un cadaver ahogado. Cuando un proyectil o rayo de energía amenaza al electrosacerdote, estos pequeños fantasmas interceden rompiendo o dispersando las amenazas en nubes de ozono. Cuando el portador del campo carga contra el enemigo, esos mismos voltagheists atacan al enemigo con descargas de fuerza eléctrica."/>
 	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
 	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Aumenta la pérdida de moral."/>
 	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/WrathOfMars" value="<string name='Actions/AdeptusMechanicus/WrathOfMars'/>"/>
 	<entry name="AdeptusMechanicus/WrathOfMarsDescription" value="Aumenta el daño."/>
 	<entry name="AdeptusMechanicus/WrathOfMarsFlavor" value="<string name='Actions/AdeptusMechanicus/WrathOfMarsFlavor'/>"/>	
	<entry name="AerialAttack" value="Ataque Aéreo"/>
	<entry name="AerialAttackDescription" value="Solo puede atacar objetivos aéreos."/>
	<entry name="Agile" value="<string name='Actions/Agile'/>"/>
	<entry name="AgileDescription" value="<string name='Actions/AgileDescription'/>"/>
	<entry name="AmmoRunt" value="Kanijo munizionero"/>
	<entry name="AmmoRuntDescription" value="Aumenta la precisión a distancia."/>
	<entry name="AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Amphibious" value="Anfibio"/>
	<entry name="AmphibiousDescription" value="La unidad se puede mover sobre el agua e ignora las penalizaciones de los ríos."/>
	<entry name="AmphibiousFlavor" value="Muy pocos de los vehículos militares estándar del 41º millenium están diseñados con el asalto anfibio en mente-reducir el peso eliminando munición y blindaje se considera a menudo ir demasiado lejos. Sin embargo, la Quimera Imperial fue diseñada hace muchos milenios para ser el tanque ligero flexible definitivo y es bastante capaz de vadear ríos."/>
	<entry name="AndTheyShallKnowNoFear" value="Y no conocerán el miedo"/>
	<entry name="AndTheyShallKnowNoFearDescription" value="Reduce la perdida de moral y otorga inmunidad al miedo."/>
	<entry name="AndTheyShallKnowNoFearFlavor" value="Algunos guerreros se niegan a rendirse, luchando contra viento y marea."/>
	<entry name="Animosity" value="Animosidad"/>
	<entry name="AnimosityDescription" value="Disminuye los ataques."/>
	<entry name="AnimosityFlavor" value="Cuando las mareas de ¡Waaagh! fluyen, los orkos son aparentemente más grandes y fuertes—pero si la marea disminuye, los orkos también se debilitan."/>
	<entry name="AntiGravUpwash" value="Ascendencia gravitatoria"/>
	<entry name="AntiGravUpwashDescription" value="Aumenta el movimiento cuando la vida está por encima del 66%."/>
	<entry name="ApocalypticBarrage" value="Barrera de artillería apocalíptica"/>
	<entry name="ApocalypticBarrageDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticBarrageFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ApocalypticBlast" value="Explosión apocalíptica"/>
	<entry name="ApocalypticBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ApocalypticMegaBlast" value="Megaexplosión apocalítpica"/>
	<entry name="ApocalypticMegaBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticMegaBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ArmoriumCherub" value="<string name='Actions/ArmoriumCherub'/>"/>
	<entry name="ArmoriumCherubDescription" value="Aumenta la precisión."/>
	<entry name="ArmoriumCherubFlavor" value="<string name='Actions/ArmoriumCherubFlavor'/>"/>
	<entry name="Armourbane" value="Antiblindaje"/>
	<entry name="ArmourbaneDescription" value="Aumenta la penetración en armadura."/>
	<entry name="ArmourbaneFlavor" value="Este arma ha sido diseñada con un objetivo en mente: perforar la coraza de cualquier vehículo."/>
	<entry name="Artefact" value="Artefacto"/>
	<entry name="ArtefactDescription" value="Clasificación."/>
 	<entry name="ArtefactFlavor" value="A lo largo de los milenios, algo en las estructuras de los Antiguos que salpican Gladius ha atraído muchas razas a este planeta. Éstas han traído consigo extraordinarios poderes, lo que significa que la superficie está llena de tecnologías alienígenas de todos los tamaños y clases."/>
	<entry name="Assault" value="Asalto"/>
	<entry name="AssaultDescription" value="Clasificación."/>
	<entry name="AssaultDoctrine" value="Doctrina de asalto"/>
	<entry name="AssaultDoctrineDescription" value="Aumenta la precisión."/>
	<entry name="AssaultDoctrineFlavor" value="<string name='Actions/AssaultDoctrineFlavor'/>"/>
	<entry name="AssaultVehicle" value="Vehículo de asalto"/>
	<entry name="AssaultVehicleDescription" value="Desembarcar no consume puntos de movimiento."/>
	<entry name="AssaultVehicleFlavor" value="Este vehículo está específicamente diseñado para meter tropas en medio de la refriega."/>
	<entry name="AstraMilitarum/BlastDamage" value="Recubrimiento de fragmentación mejorado"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="Con el uso de un mejor protocolo de grabado avanzado en el casquillo, la letalidad del explosivo se ha aumentado."/>
	<entry name="AstraMilitarum/BoltDamage" value="Munición Kraken"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Aumenta la penetración de blindaje."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="Una actualización Astartes para el bólter estándar, este kit permite a un bólter disparar munición perforante kraken, proyectiles de punta dura con una mayor carga explosiva. Esta munición pesada es la favorita de los Marines Espaciales de los Guardianes de la Muerte."/>
	<entry name="AstraMilitarum/CityTier2" value="Subestructura de extensión"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Aumenta el rango de compra de casillas."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="Algún día, esto será una Ciudad Colmena—mil millones de personas en una estructura, imponiéndose por encima de un mundo envenenado—pero primero, este asentamiento necesita expandirse un poco más, para cimentar la base de las más grandes aglomeraciones de las galaxias."/>
	<entry name="AstraMilitarum/CityTier3" value="Subestructura de túneles de suministros"/>
	<entry name="AstraMilitarum/CityTier3Description" value="Aumenta el rango de compra de casillas."/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="El Departamento Munitorum puede ser ineficiente, pero puede planificar y los Tecnosacerdotes exploradores y los Servidores construir. Estos túneles bajo tierra permiten al comandante del Astra Militarum supervisar un mayor área de una ciudad, sin perder el control de las defensas."/>
	<entry name="AstraMilitarum/ShootSharpAndScarper" value="Dispara y escabullirse"/>
	<entry name="AstraMilitarum/ShootSharpAndScarperDescription" value="Las acciones no consumen movimiento."/>
	<entry name="AstraMilitarum/ShootSharpAndScarperFlavor" value="Lo que los Ratlings carecen militarmente en, bueno, todo, lo compensan en saber cuándo es el momento adecuado para disparar y cuándo es el momento adecuado para correr."/>
	<entry name="AstraMilitarumAircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarumAircraftProductionEdictDescription" value="Aumenta la generación de producción."/>
	<entry name="AstraMilitarumAircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumDefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarumDefenseEdictDescription" value="Aumenta el blindaje."/>
	<entry name="AstraMilitarumDefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarumEnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarumEnergyEdictDescription" value="Aumenta la generación de energía."/>
	<entry name="AstraMilitarumEnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarumFoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarumFoodEdictDescription" value="Aumenta la generación de comida."/>
	<entry name="AstraMilitarumFoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarumGrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarumGrowthEdictDescription" value="Aumenta la velocidad de crecimiento."/>
	<entry name="AstraMilitarumGrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarumInfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarumInfantryProductionEdictDescription" value="Aumenta la generación de producción."/>
	<entry name="AstraMilitarumInfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumInfluenceEdict" value="<string name='Actions/AstraMilitarumInfluenceEdict'/>"/>
	<entry name="AstraMilitarumInfluenceEdictDescription" value="Aumenta la generación de influencia."/>
	<entry name="AstraMilitarumInfluenceEdictFlavor" value="<string name='Actions/AstraMilitarumInfluenceEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="Hotshot power packs"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Aumenta la penetración de armadura."/>
 	<entry name="AstraMilitarum/LasDamageFlavor" value="Estos paquetes modulares de alto nivel convierten el arma láser estándar en un 'Hell Gun', con rango y potencia mejorados, pero con una capacidad de cargador de energía reducida y una seguridad degradada, que requieren un mantenimiento constante. Incluso las unidades que ya cuentan con armas láser Hotshot dedicadas se benefician del suministro de Hotshot power packs más confiables."/>
	<entry name="AstraMilitarumLoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarumLoyaltyEdictDescription" value="Aumenta la generación de lealtad."/>
	<entry name="AstraMilitarumLoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarumOreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarumOreEdictDescription" value="Aumenta la generación de minerales."/>
	<entry name="AstraMilitarumOreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarumPsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarumPsykerProductionEdictDescription" value="Aumenta la generación de producción."/>
	<entry name="AstraMilitarumPsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarumResearchEdictDescription" value="Aumenta la generación de investigación."/>
	<entry name="AstraMilitarumResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarumVehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarumVehicleProductionEdictDescription" value="Aumenta la generación de producción."/>
	<entry name="AstraMilitarumVehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>
	<entry name="AversionToLight" value="Aversión a la luz"/>
	<entry name="AversionToLightDescription" value="Aumenta el daño infligido por las armas de llama y de fusión."/>
	<entry name="AversionToLightFlavor" value="Los horrores del fragmento de Umbra florecen por la noche, en los oscuros rincones de Gladius. Pero si les traen una luz y se encogen, las pesadillas se alejan. Y trae una llama a ellos…"/>
	<entry name="Barrage" value="Barrera de artillería"/>
	<entry name="BarrageDescription" value="No requiere línea de visión, pero no puede realizar disparos defensivos."/>
	<entry name="BarrageFlavor" value="<string name='Weapons/BarrageFlavor'/>"/>
	<entry name="Beam" value="Rayo"/>
	<entry name="BeamDescription" value="Impacta con mayor puntería contra varios miembros del grupo de la unidad objetivo."/>
	<entry name="Bike" value="Moto"/>
	<entry name="BikeDescription" value="Classification."/>
	<entry name="BikeFlavor" value="Las unidades montadas en motos sobresalen en los ataques de vanguardia. Son capaces de usar su velocidad para atacar en territorio enemigo, completar su misión y escapar antes de que el enemigo sea capaz de reaccionar. Estos guerreros son a menudo considerados como peligrosamente temerarios, pero su efectividad no puede ser negada."/>
	<entry name="Bladestorm" value="Tormenta de filos"/>
	<entry name="BladestormDescription" value="Aumenta el daño y la penetración del blindaje."/>
	<entry name="Blast" value="Explosión"/>
	<entry name="BlastDescription" value="Impacta en varios miembros del grupo de la unidad objetivo."/>
	<entry name="BlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="Blighted" value="Peste"/>
	<entry name="BlightedDescription" value="Produce daño cada turno."/>
	<entry name="Blind" value="Cegar"/>
	<entry name="BlindDescription" value="Reduce la puntería."/>
	<entry name="BlindFlavor" value="Este ataque suelta una estela de luz brillante, dañando la vista de la víctima y obligándola a luchar a ciegas durante unos instantes."/>
	<entry name="Blinding" value="Ceguera"/>
	<entry name="BlindingDescription" value="Reduce la puntería de la infantería o criatura monstruosa objetivo."/>
	<entry name="BloodBlessing" value="Bendición de la Sangre"/>
	<entry name="BloodBlessingDescription" value="Aumenta los ataques."/>
	<entry name="BloodBlessingFlavor" value="<string name='Features/SkullAltarFlavor'/>"/>
	<entry name="BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="BolsterDefencesDescription" value="Aumenta la reducción en el daño a distancia."/>
	<entry name="BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="BoltWeapon" value="Arma bólter"/>
	<entry name="BoltWeaponFlavor" value="<string name='Weapons/BoltFlavor'/>"/>
	<entry name="BolterDrill" value="Diestros con el bólter"/>
	<entry name="BolterDrillDescription" value="Aumenta la puntería."/>
	<entry name="BolterDrillFlavor" value="<string name='Actions/BolterDrillFlavor'/>"/>
	<entry name="Bomb" value="Bomba"/>
	<entry name="BombDescription" value="Puntería fija."/>
	<entry name="Bosspole" value="Palo del jefe"/>
	<entry name="BosspoleDescription" value="Reduce la pérdida de puntos en base a la cantidad de unidades aliadas en el área."/>
	<entry name="BosspoleFlavor" value="Los Noblez lucen a menudo su palo trofeo mostrando que es mejor no acercarse a ellos. Un Noble suele encontrar útil un Palo de jefe para romper cabezas e imponer un poco de orden en el fragor de la batalla."/>
	<entry name="BringItDown" value="“¡Hazlo caer!”"/>
	<entry name="BringItDownDescription" value="Aumenta la penetración en blindaje."/>
	<entry name="Broken" value="Rota"/>
	<entry name="BrokenDescription" value="Reduce la puntería y aumenta el daño sufrido."/>
	<entry name="BruteShield" value="Escudo burdo"/>
	<entry name="BruteShieldDescription" value="Aumenta el daño y la reducción de daño."/>
	<entry name="BruteShieldFlavor" value="Estos escudos se parecen más a broqueles resistentes y eléctricos. Suelen llevarlos algunos Ogretes, sirviendo en combate tanto en defensa como arma de mano."/>
	<entry name="Bulky" value="Voluminoso"/>
	<entry name="BulkyDescription" value="Necesita un hueco de carga adicional en un transporte."/>
	<entry name="BulkyFlavor" value="Esta criatura es tan voluminosa que ocupa una cantidad enorme de espacio en cualquier vehículo o edificio en el que entre."/>	
	<entry name="CamoNetting" value="Red de camuflaje"/>
	<entry name="CamoNettingDescription" value="Aumenta la reducción de daño por ataque a distancia."/>
	<entry name="CamoNettingFlavor" value="Ya sea raras capas de camuflaje o simples mallas tejidas con flora local, la red de camuflaje ayuda a ocultar un vehículo de ojos curiosos.."/>
	<entry name="Capturable" value="Capturable"/>
	<entry name="CapturableDescription" value="La unidad puede ser capturada por otra unidad adyacente."/>
	<entry name="CeramitePlating" value="Placas de ceramita"/>
	<entry name="CeramitePlatingDescription" value="Aumenta el blindaje."/>
	<entry name="CeramitePlatingFlavor" value="Estas placas están tres veces bendecidas por los tecnosacerdotres del Capítulo y ungidas con los siete ungüentos sagrados de protección térmica contra las extremas condiciones de la entrada orbital. Estas precauciones también sirven para reducir el poderío de ciertas armas, absorbiendo y dispersando las más extremas temperaturas y emisiones de microondas."/>
	<entry name="Chaff" value="Señuelos"/>
	<entry name="ChaffDescription" value="Aumenta la reducción del daño a distancia."/>
 	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="Occulum arcano"/>
 	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Bendición de Caos que aumenta la precisión."/>
 	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="Un ojo inyectado en sangre empuja a través de la carne."/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGlory" value="Aura de Gloria Oscura"/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGloryDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGloryFlavor" value="Algunos campeones del Caos están tan bendecidos por su deidad protectora que están protegidos sobrenaturalmente del daño. Pueden estar rodeados por una poderosa y crepitante burbuja de energía psíquica, o bien los proyectiles dirigidos hacia ellos pueden ser desviados extrañamente justo antes de impactar. En cualquier caso, es evidente que los Dioses Oscuros los protegen."/>
 	<entry name="ChaosSpaceMarines/BlastDamage" value="Recubrimiento Daemonbone"/>
 	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Aumenta la penetración de armadura."/>
 	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="Los herreros de la disformidad solo pueden haber atrapado horrores menores en las envolturas de estos proyectiles explosivos, pero su furia en el punto de impacto no es menor, ya que se rompen y regresan a la deformación, se enfurecen y arrebatan a cualquier cosa que esté cerca."/>
	<entry name="ChaosSpaceMarines/Bloated" value="Abotardago"/>
 	<entry name="ChaosSpaceMarines/BloatedDescription" value="Restaura puntos de vida."/>
 	<entry name="ChaosSpaceMarines/BloatedFlavor" value="Aunque las enfermedades malditas de Nurgle pudren al hombre tanto física como espiritualmente, también imbuyen a sus sujetos en descomposición de la carne con una resistencia inhumana. A tales formas abotargadas, propagar su plaga a menudo puede traer pequeñas bendiciones de la plaga de Dios."/>
	<entry name="ChaosSpaceMarines/BloodForTheBloodGod" value="Sangre para el Dios de la Sangre"/>
	<entry name="ChaosSpaceMarines/BloodForTheBloodGodDescription" value="Aumenta los ataques."/>
 	<entry name="ChaosSpaceMarines/BloodRage" value="Ira sanguinaria"/>
 	<entry name="ChaosSpaceMarines/BloodRageDescription" value="Aumenta los ataques cuerpo a cuerpo y el movimiento."/>
 	<entry name="ChaosSpaceMarines/BloodRageFlavor" value="El Bruto Infernal se lanza contra los enemigos cercanos, conduciendo su maldita prisión Dreadnought más allá de sus antiguos límites de seguridad, a una conducta completamente demoníaca."/> <!-- Hellbrute Crazed result. -->
 	<entry name="ChaosSpaceMarines/BoltDamage" value="Warpborne Bolts"/>
 	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Aumenta la penetración de armadura."/>
 	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="Al cruzar los Inferno Bolts aumentados con prometio del Imperio y los Inferno Bolts de los Mil Hijos de la Legión Traicionera, el Warpborne Bolt salta a través del espacio disforme para desencadenar un fuego químico sobrecalentado sobre sus objetivos."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="Llamada de los Dioses Oscuros"/>
 	<entry name="ChaosSpaceMarines/CityTier2Description" value="Aumenta el radio de adquisición de cuadrícula."/>
 	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="La gente del Imperio escuchará cualquier mentira, creerá cualquier historia, que les diga que no están condenados. De esa manera, tristemente, está la perdición, y las puertas del Caos están abiertas de par en par a los perdidos y condenados, al menos, en la entrada."/>
 	<entry name="ChaosSpaceMarines/CityTier3" value="Nexo de la Fatalidad"/>
 	<entry name="ChaosSpaceMarines/CityTier3Description" value="Aumenta el radio de adquisición de cuadrícula."/>
 	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="Los adoradores del caos se han despojado de sus pretensiones. Donde antes atraían a las almas por sus debilidades o por la seducción de demonios, ahora lo hacen por látigos y cadenas. Grupos de esclavistas y partidas de guerra cazan en el interior de su ciudad por vidas para sacrificarlas. Sólo los más fuertes pueden escapar de su alcance; Los débiles son harina para el molino del Caos."/>
 	<entry name="ChaosSpaceMarines/Crazed" value="Loco"/>
 	<entry name="ChaosSpaceMarines/CrazedDescription" value="Si la unidad se dañó en el turno anterior, gana uno de los siguientes rasgos al azar durante un turno: Disparos furiosos, Furia creciente, Ira Sanguinaria."/>
 	<entry name="ChaosSpaceMarines/CrazedFlavor" value="Todos los Helbrutes son monstruosidades psicóticas y peligrosas, un veterano de La Guerra Eterna que está enloquecido por un entierro torturado. Así que atacar a un Helbrute puede parecer racional, pero si no lo destruyes por completo, solo conseguirás volverlo más enojado y más loco."/> <!-- Hellbrute. -->
 	<entry name="ChaosSpaceMarines/ChampionOfChaos" value="Campeón del Caos"/>
 	<entry name="ChaosSpaceMarines/ChampionOfChaosDescription" value="Matar a una unidad enemiga podría recompensar a esta unidad con un Don del Caos o transformar un no-héroe en un Engendro del Caos o un Príncipe Demonio."/>
 	<entry name="ChaosSpaceMarines/ChampionOfChaosFlavor" value="No es inusual que los poderes del Caos otorguen bendiciones extrañas y mutaciones a aquellos que matan en su nombre. No todos estos beneficios son beneficiosos: los oscuros son tan inconstantes como inescrutables, e incluso sus seguidores más ardientes son poco más que peones en un juego celestial."/>
 	<entry name="ChaosSpaceMarines/CrystallineBody" value="Cuerpo cristalino"/>
 	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Bendición del Caos que aumenta los puntos de golpe."/>
 	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="La carne del campeón se transforma en diamante."/>
 	<entry name="ChaosSpaceMarines/CultistSacrifice" value="<string name='Actions/ChaosSpaceMarines/CultistSacrifice'/>"/>
 	<entry name="ChaosSpaceMarines/CultistSacrificeDescription" value="Aumenta la tasa de crecimiento."/>
 	<entry name="ChaosSpaceMarines/CultistSacrificeFlavor" value="<string name='Actions/ChaosSpaceMarines/CultistSacrificeFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Daemonforge" value="<string name='Actions/ChaosSpaceMarines/Daemonforge'/>"/>
 	<entry name="ChaosSpaceMarines/DaemonforgeDescription" value="Aumenta el daño y la penetración de blindaje."/>
 	<entry name="ChaosSpaceMarines/DaemonforgeFlavor" value="<string name='Actions/ChaosSpaceMarines/DaemonforgeFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/DarkGlory" value="<string name='Actions/ChaosSpaceMarines/DarkGlory'/>"/>
 	<entry name="ChaosSpaceMarines/DarkGloryDescription" value="<string name='Actions/ChaosSpaceMarines/DarkGloryDescription'/>"/>
 	<entry name="ChaosSpaceMarines/DarkGloryFlavor" value="<string name='Actions/ChaosSpaceMarines/DarkGloryFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/DevourerOfSouls" value="Devorador de Almas"/>
 	<entry name="ChaosSpaceMarines/DevourerOfSoulsDescription" value="Restaura los puntos de golpe cada turno y cada vez que esta unidad mata a una unidad enemiga."/>
 	<entry name="ChaosSpaceMarines/DevourerOfSoulsFlavor" value="Los Trepadores Venenosos son quizás los trabajos más sofisticados de los Herreros de la Disformidad, Motores Demoníacos tipo araña que anhelan la energía empírica y consumirán otras entidades demoníacas para conseguirla, o incluso a los seres vivos."/> <!-- Venomcrawler -->
 	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
 	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Evita que la unidad realice ataques de vigilancia."/>
 	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/FireFrenzy" value="Disparos furiosos"/>
 	<entry name="ChaosSpaceMarines/FireFrenzyDescription" value="Previene el movimiento y aumenta los ataques a distancia."/>
 	<entry name="ChaosSpaceMarines/FireFrenzyFlavor" value="En respuesta al despreciable fuego de los mortales, los Hellbrute descargan sus armas implacablemente, ciegos ante cualquier cosa que no sea el ensordecedor rugido de sus municiones demoníacas."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/DeferredAbsolution" value="<string name='Actions/ChaosSpaceMarines/DeferredAbsolution'/>"/>
	<entry name="ChaosSpaceMarines/DeferredAbsolutionDescription" value="Aumenta la reducción al daño invulnerable."/>
	<entry name="ChaosSpaceMarines/DeferredAbsolutionFlavor" value="<string name='Actions/ChaosSpaceMarines/DeferredAbsolutionFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/GiftOfMutation" value="Regalo de Mutación"/>
 	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="Al comienzo del siguiente turno, la unidad gana una nueva Bendición del Caos desbloqueada al azar y finaliza este rasgo."/>
 	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="Los Dioses Oscuros han otorgado a su campeón una recompensa maligna, que es tan probable que sea un accesorio de hoja afilada como una lengua colocada de manera incómoda."/>
 	<entry name="ChaosSpaceMarines/GraspingPseudopods" value="Seudópodos prensiles"/>
 	<entry name="ChaosSpaceMarines/GraspingPseudopodsDescription" value="Aumenta los ataques cuerpo a cuerpo."/>
 	<entry name="ChaosSpaceMarines/GraspingPseudopodsFlavor" value="De la forma retorcida del engendro vienen tentáculos carnosos, agitándose y balbuceando a los oponentes cercanos. Demasiado cerca y se envuelven reflexivamente alrededor de un objetivo, atrayéndolo a las fauces y dientes que tiene el engendro."/> <!-- MutatedBeyondReason result. -->
 	<entry name="ChaosSpaceMarines/IchorBlood" value="<string name='Actions/ChaosSpaceMarines/IchorBlood'/>"/>
 	<entry name="ChaosSpaceMarines/IchorBloodDescription" value="<string name='Actions/ChaosSpaceMarines/IchorBloodDescription'/>"/>
 	<entry name="ChaosSpaceMarines/IchorBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/IchorBloodFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="Aumenta la reducción del daño cuerpo a cuerpo cuando es atacado por enemigos temerosos."/>
 	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="Aumenta la reducción de daño."/>
 	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="Aumenta el daño."/>
 	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/InfernalFusillade" value="<string name='Actions/ChaosSpaceMarines/InfernalFusillade'/>"/>
	<entry name="ChaosSpaceMarines/InfernalFusilladeDescription" value="Aumenta los ataques con armas de bólter."/>
	<entry name="ChaosSpaceMarines/InfernalFusilladeFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalFusillade'/>"/>
 	<entry name="ChaosSpaceMarines/InfernalIndustry" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustry'/>"/>
 	<entry name="ChaosSpaceMarines/InfernalIndustryDescription" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustryDescription'/>"/>
 	<entry name="ChaosSpaceMarines/InfernalIndustryFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustryFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/InfernalPower" value="<string name='Actions/ChaosSpaceMarines/InfernalPower'/>"/>
 	<entry name="ChaosSpaceMarines/InfernalPowerDescription" value="Aumenta la precisión y el daño."/>
 	<entry name="ChaosSpaceMarines/InfernalPowerFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalPowerFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/LasDamage" value="Energías Prohibidas"/>
 	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Incrementa la penetración en armadura."/>
 	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="Ir más allá de las tecnologías prohibidas del Imperio ha permitido a Mechanicum Oscuros desbloquear capacidades de armas más letales y más horribles."/>
 	<entry name="ChaosSpaceMarines/LasherTendrils" value="<string name='Actions/ChaosSpaceMarines/LasherTendrils'/>"/>
 	<entry name="ChaosSpaceMarines/LasherTendrilsDescription" value="Disminuye los ataques cuerpo a cuerpo."/>
 	<entry name="ChaosSpaceMarines/LasherTendrilsFlavor" value="<string name='Actions/ChaosSpaceMarines/LasherTendrilsFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/LoathsomeAura" value="<string name='Actions/ChaosSpaceMarines/LoathsomeAura'/>"/>
	<entry name="ChaosSpaceMarines/LoathsomeAuraDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="ChaosSpaceMarines/LoathsomeAuraFlavor" value="<string name='Actions/ChaosSpaceMarines/LoathsomeAuraFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MalevolentLocus" value="<string name='Actions/ChaosSpaceMarines/MalevolentLocus'/>"/>
	<entry name="ChaosSpaceMarines/MalevolentLocusDescription" value="Provoca daño cada turno."/>
	<entry name="ChaosSpaceMarines/MalevolentLocusFlavor" value="<string name='Actions/ChaosSpaceMarines/MalevolentLocusFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MaliciousVolleys" value="Salvas Maliciosas"/>
	<entry name="ChaosSpaceMarines/MaliciousVolleysDescription" value="Si la unidad permanece estacionaria, dispara su arma de Fuego Rápido con ataques aumentados a todos los alcances."/>
	<entry name="ChaosSpaceMarines/MaliciousVolleysFlavor" value="Para un Marine Herético, su bólter es mucho más que un arma, es un instrumento de su ira y portador de muerte para sus enemigos."/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="Aumenta los ataques."/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="Aumenta los puntos de golpe."/>
 	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="Aumenta el movimiento."/>
 	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="Aumenta la reducción de daño."/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Mechanoid" value="Mecanoide"/>
 	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Bendición del Caos que aumenta la armadura."/>
 	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="La carne del campeón se adhiere a su armadura."/>
 	<entry name="ChaosSpaceMarines/MeleeDamage" value="Armas vinculadas"/>
 	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Aumenta la penetración de armadura."/>
 	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="Es normal que los soldados crezcan cerca de un arma confiable, pero los marines de las Legiones Traidoras han ido más lejos. A lo largo de los siglos, las tropas de los Adeptus Astartes se convirtieron literalmente en sus armas, convirtiéndose en uno con sus armas y cuchillas, aumentando exponencialmente su efectividad."/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerror" value="Terror De Múltiples Patas"/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerrorDescription" value="Aumenta los ataques de Pisoteo."/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerrorFlavor" value="El titánico peso del hierro y el bronce que conforman las partes no-Empíreas del Escorpión de Bronce reposan sobre seis miembros letalmente afilados. Cuando avanza escurriéndose, con inesperada y enorme velocidad, aplasta con facilidad a los enemigos más pequeños."/>	
	<entry name="ChaosSpaceMarines/MutatedBeyondReason" value="Mutaciones inconcebibles"/>
 	<entry name="ChaosSpaceMarines/MutatedBeyondReasonDescription" value="Al comienzo de cada turno, la unidad gana una de las siguientes mutaciones aleatorias durante un turno: Seudópodos prensiles, Armadura subcutánea, Hemorragia tóxica."/>
 	<entry name="ChaosSpaceMarines/MutatedBeyondReasonFlavor" value="Cuando caen del favor de su dios, los Adoradores del Caos son bendecidos y maldecidos en igual medida, convirtiéndose en el asqueroso Engendro del Caos: masas de mutación que se retuercen, brotando sin cesar nuevos rasgos al capricho de su dios."/>
 	<entry name="ChaosSpaceMarines/Possession" value="<string name='Actions/ChaosSpaceMarines/Possession'/>"/>
 	<entry name="ChaosSpaceMarines/PossessionDescription" value="<string name='Actions/ChaosSpaceMarines/PossessionDescription'/>"/>
 	<entry name="ChaosSpaceMarines/PossessionFlavor" value="<string name='Actions/ChaosSpaceMarines/PossessionFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/PrinceOfChaos" value="<string name='Actions/ChaosSpaceMarines/PrinceOfChaos'/>"/>
	<entry name="ChaosSpaceMarines/PrinceOfChaosDescription" value="Aumenta la precisión."/>
	<entry name="ChaosSpaceMarines/PrinceOfChaosFlavor" value="<string name='Actions/ChaosSpaceMarines/PrinceOfChaosFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergy" value="<string name='Actions/ChaosSpaceMarines/ReservoirOfDaemonicEnergy'/>"/>
 	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergyDescription" value="Reduce el tiempo de reutilización de Incursión."/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergyFlavor" value="<string name='Actions/ChaosSpaceMarines/ReservoirOfDaemonicEnergyFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="Aumenta la tasa de producción."/>
 	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="Aumenta la tasa de producción e investigación."/>
 	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="Aumenta la producción de alimentos y la tasa de crecimiento."/>
 	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="Aumenta la influencia y la tasa de lealtad."/>
 	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/RisingFury" value="Furia creciente"/>
 	<entry name="ChaosSpaceMarines/RisingFuryDescription" value="Aumenta los ataques cuerpo a cuerpo."/>
 	<entry name="ChaosSpaceMarines/RisingFuryFlavor" value="La ira del Helbrute lo sobrepasa, lanzándolo en un frenesí de golpes contra cualquier cosa cercana."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGod" value="Runas del Dios de la Sangre"/>
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGodDescription" value="Devuelve el daño recibido por ataques de fuego brujo."/>
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGodFlavor" value="Hay relucientes runas, danzando como llamas, retorciéndose en la superficie, inscritas en el Escorpión de Bronce por los adeptos del Mechanicum Oscuro. Los Psíquicos lo bastante insensatos como para intentar un asalto se toparán con sus mentes azotadas por la eterna furia de Khorne."/>	
 	<entry name="ChaosSpaceMarines/ShatterDefences" value="<string name='Actions/ChaosSpaceMarines/ShatterDefences'/>"/>
 	<entry name="ChaosSpaceMarines/ShatterDefencesDescription" value="Disminuye la reducción de daños a distancia."/>
 	<entry name="ChaosSpaceMarines/ShatterDefencesFlavor" value="<string name='Actions/ChaosSpaceMarines/ShatterDefencesFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/SiegeCrawler" value="Trepadores de asedio"/>
 	<entry name="ChaosSpaceMarines/SiegeCrawlerDescription" value="Aumenta la penetración de armaduras contra fortificaciones."/>
 	<entry name="ChaosSpaceMarines/SiegeCrawlerFlavor" value="Los Maulerfiends carecen de armas a distancia de cualquier tipo, algo inusual para un vehículo de su tamaño. Sin embargo, su agilidad y sus diversas armas de corte significan que son una gran amenaza para las fortificaciones estáticas, capaces de explorarlas, encontrar un punto débil y explotarlo."/> <!-- Maulerfiend. -->
 	<entry name="ChaosSpaceMarines/SubcutaneousArmour" value="Armadura subcutánea"/>
 	<entry name="ChaosSpaceMarines/SubcutaneousArmourDescription" value="Aumenta la armadura."/>
 	<entry name="ChaosSpaceMarines/SubcutaneousArmourFlavor" value="La armadura de los Marines Espaciales, incluso tan antigua como la que poseen las Legiones Traidoras, se integra con la interfaz única de Caparazón Negro de un Adeptus Astartes. Pero a lo largo de los milenios, muchos Marines Espaciales del Caos se han vuelto uno con su armadura, con el Caparazón Negro extendiéndose por toda su carne y huesos."/> <!-- MutatedBeyondReason result. -->
 	<entry name="ChaosSpaceMarines/TemporalDistortion" value="Distorsión temporal"/>
 	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Bendición del Caos que aumenta el movimiento."/>
 	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="El tiempo se altera en torno al campeón."/>
 	<entry name="ChaosSpaceMarines/ToxicHaemorrhage" value="Hemorragia tóxica"/>
 	<entry name="ChaosSpaceMarines/ToxicHaemorrhageDescription" value="Aumenta el daño contra unidades de infantería y criaturas monstruosas."/>
 	<entry name="ChaosSpaceMarines/ToxicHaemorrhageFlavor" value="Brotando vapores viles y fluidos atroces, las entrañas malditas del Engendro del Caos pueden provocar la muerte de una criatura menos bendecida."/> <!-- MutatedBeyondReason result. -->
 	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="Veteranos de la Guerra Eterna"/>
 	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Reduce la pérdida de moral, y aumenta la precisión del cuerpo a cuerpo contra unidades de la facción de los Marines Espaciales."/>
 	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="Muchas facciones de los Marines Espaciales del Caos han estado atrapadas en una guerra constante y dura contra el Imperio del Hombre durante siglos, si no milenios. El odio ardiente que sienten por sus hermanos leales ha tenido tiempo de desvanecerse, ahora eclipsando todas las demás emociones. Esto se aplica, sobre todo, a las nueve Legiones Traidoras originales que acudieron al Estandarte de Horus hace diez milenios y continúan su guerra contra sus hermanos leales hasta el día de hoy."/>
 	<entry name="ChaosSpaceMarines/WarpFrenzy" value="Furia disforme"/>
 	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Bendición del Caos que aumenta los ataques."/>
 	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="El campeón se consume con rabia."/>
 	<entry name="ChaosSpaceMarines/WorthyOffering" value="<string name='Actions/ChaosSpaceMarines/WorthyOffering'/>"/>
 	<entry name="ChaosSpaceMarines/WorthyOfferingDescription" value="<string name='Actions/ChaosSpaceMarines/WorthyOfferingDescription'/>"/>
 	<entry name="ChaosSpaceMarines/WorthyOfferingFlavor" value="<string name='Actions/ChaosSpaceMarines/WorthyOfferingFlavor'/>"/>
	<entry name="ChapterUnity" value="Unidad del Capítulo"/>
	<entry name="ChapterUnityDescription" value="Aumenta la generación de lealtad."/>
	<entry name="ChapterUnityFlavor" value="Las reliquias atesoradas en la Gran Sala recuerdan a los Adeptus Astartes de los héroes del pasado, y que algún día sus armaduras, armas y huesos pueden ser venerados aquí."/>
	<entry name="City" value="Ciudad"/>
	<entry name="CityDescription" value="Aumenta la reducción del daño y la velocidad de curación para las unidades aliadas. Reduce el coste de movimiento para las unidades. También aumenta la reducción del daño a distancia de unidades de infantería."/>
	<entry name="ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="ClusterMinesDescription" value="Produce daño al entrar en una casilla."/>
	<entry name="ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="CombatShield" value="Escudo de combate"/>
	<entry name="CombatShieldDescription" value="Aumenta la reducción del daño."/>
	<entry name="CombatShieldFlavor" value="Un escudo de combate es la versión ligera del escudo de tormenta adaptado al avambrazo del portador, dejando la mano libre para portar otra arma mientras le permite protegerse de ataques enemigos."/>
	<entry name="CompendiumFlavor" value="Si los guerreros del 41º milenio fueran idénticos, estas guerras serían cuestión puramente de números—y la humanidad habría ganado hace mucho tiempo. En su lugar, cada raza, tipo de tropa y soldado tiene sus propias características, que los hacen ideales para determinadas situaciones—y terribles para otras."/>
	<entry name="Concussion" value="Conmoción"/>
	<entry name="ConcussionDescription" value="Reduce la precisión."/>
	<entry name="Concussive" value="Contusión"/>
	<entry name="ConcussiveDescription" value="Reduce temporalmente la precisión de la unidad de infantería o criatura monstruosa objetivo."/>
	<entry name="ConcussiveFlavor" value="Algunas armas están diseñadas para dejar a cualquier enemigo que consiga sobrevivir desorientado y fácil de rematar."/>
	<entry name="ConvergentTargeting" value="Puntería convergente"/>
	<entry name="ConvergentTargetingDescription" value="Aumenta la precisión cuando hay adyacente un Cañón Tormenta aliado."/>
	<entry name="CultAmbush" value="Culto a la emboscada"/>
	<entry name="CultAmbushDescription" value="Aumenta la puntería de los ataques de reacción."/>
	<entry name="CultAmbushFlavor" value="El culto Genestealer planea meticulosamente cada ataque, sus estrategias aprovechan todas las ventajas cuando se producen."/>
	<entry name="CurseOfTheWalkingPox" value="Maldición de la Viruela Andante"/>
	<entry name="CurseOfTheWalkingPoxDescription" value="Convierte daño en curación para esta unidad."/>
	<entry name="CurseOfTheWalkingPoxFlavor" value="La rígida sonrisa que adorna a cada Plagabundo esconde el tormento que sufren sus almas, atrapadas dentro de sus mutados, muertos cuerpos que luchan por Nurgle, infectando a más inocentes con la Plaga Andante al pasar."/>	
	<entry name="Daemon" value="Demonio"/>
	<entry name="DaemonDescription" value="Aumenta la reducción del daño."/>
	<entry name="DaemonFlavor" value="Las criaturas de la disformidad son muchas y nauseabundas, con una variedad infinita, pero hay algunas características que todas comparten."/>
	<entry name="Damaged" value="Dañado"/>
	<entry name="DamagedDescription" value="Disminuye la puntería."/>
	<entry name="Deathshriek" value="Engendro del terror"/>
	<entry name="DeathshriekDescription" value="Daña al atacante al morir."/>
	<entry name="DeathshriekFlavor" value="En el momento de la muerte de una Umbra, todos los que la rodean reciben una visión de la criatura de antaño que fue despedazada y arrojada a la urdimbre, mientras una voz, horrorosa y seductora, susurra una maldición… “LINGER…”"/>
	<entry name="DeedsOfGlory" value="Hazañas de gloria"/>
	<entry name="DeedsOfGloryDescription" value="<string name='Actions/DeedsOfGloryDescription'/>"/>
	<entry name="DestroyerWeapon" value="Armas destructoras"/>
	<entry name="DestroyerWeaponDescription" value="Aumenta el daño."/>
	<entry name="DevastatorDoctrine" value="Doctrina devastadora"/>
	<entry name="DevastatorDoctrineDescription" value="Aumenta la precisión."/>
	<entry name="DevastatorDoctrineFlavor" value="<string name='Actions/DevastatorDoctrineFlavor'/>"/>
	<entry name="Discipline" value="Disciplina"/>
	<entry name="DisciplineDescription" value="Aumenta la precisión."/>
	<entry name="DisciplineFlavor" value="<string name='Actions/AuraOfDisciplineFlavor'/>"/>
	<entry name="DistortScythe" value="Guadaña de la distorsión"/>
	<entry name="DistortScytheDescription" value="Aumenta el daño."/>
	<entry name="DistortScytheFlavor" value="<string name='Weapons/DistortionFlavor'/>"/>
	<entry name="DogmaAstrates" value="Dogma Astartes"/>
	<entry name="DogmaAstratesDescription" value="<string name='Actions/DogmaAstratesDescription'/>"/>
	<entry name="DogmaAstratesFlavor" value="<string name='Actions/DogmaAstratesFlavor'/>"/>
	<entry name="DozerBlade" value="Pala excavadora"/>
	<entry name="DozerBladeDescription" value="Niega la penalización de movimiento en terreno difícil"/>
	<entry name="DozerBladeFlavor" value="Las Palas excavadoras son palas pesadas, cuchillas, arietes o recogedores, usados para despejar obstáculos del camino."/>
	<entry name="Drukhari/AncientEvil" value="<string name='Actions/Drukhari/AncientEvil'/>"/>
	<entry name="Drukhari/AncientEvilDescription" value="La unidad atacada pierde moral."/>
	<entry name="Drukhari/AncientEvilFlavor" value="<string name='Actions/Drukhari/AncientEvilFlavor'/>"/>
	<entry name="Drukhari/AssaultWeaponBonus" value="Diseños aberrantes"/>
	<entry name="Drukhari/AssaultWeaponBonusDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Drukhari/AssaultWeaponBonusFlavor" value="Los maestros armeros de las fundiciones Cabalitas tienen acceso a variantes de armas que normalmente se considerarían imposibles o totalmente antiéticas. Lo que, por supuesto, está simplemente basado en lo que un Arconte en concreto esté dispuesto a pagar."/>
	<entry name="Drukhari/BetrayalCulture" value="Cultura de la Traición"/>
	<entry name="Drukhari/BetrayalCultureDescription" value="Las ciudades Drukhari tienen menor lealtad por defecto. Obtienen lealtad adicional según reunen Influencia."/>
	<entry name="Drukhari/BetrayalCultureFlavor" value="Para los depravados Drukhari, es normal que un niño traicione a sus padres por una eternidad de tortura simplemente por obtener una pizca de ventaja. No ha confianza en una ciudad Drukhari, sólo poder. Cuanto más poderoso seas, más gente querrá unirse a ti."/>
	<entry name="Drukhari/BetrayalCultureUpgrade" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgrade'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgradeDescription'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgradeDescription'/>"/>
	<entry name="Drukhari/BladeArtists" value="Artistas del Filo"/>
	<entry name="Drukhari/BladeArtistsDescription" value="Aumenta la penetración de armadura en combate cuerpo a cuerpo."/>
	<entry name="Drukhari/BladeArtistsFlavor" value="Todo ciudadano de Commorragh aprende desde bien joven el valor de un buen filo, y todos son hábiles en su uso, tanto los esgrimidos por sus crueles manos como los que adornan sus armaduras."/>
	<entry name="Drukhari/BladeWhip" value="Látigo Filo"/> 
	<entry name="Drukhari/BladeWhipDescription" value="Aumenta la precisión."/>
	<entry name="Drukhari/BladeWhipFlavor" value="Sólo los Laceradores de los cultos Brujas utilizan regularmente la hoja segmentada conocida como Flagelos Encadenados, pero su habilidad con ella el legendaria. Los ataques que parecen seguro que fallarán se curvan para conectar, cambiando entre látigo y filos a voluntad."/>
	<entry name="Drukhari/BloodDancer" value="<string name='Actions/Drukhari/BloodDancer'/>"/>
	<entry name="Drukhari/BloodDancerDescription" value="<string name='Actions/Drukhari/BloodDancerDescription'/>"/>
	<entry name="Drukhari/BloodDancerFlavor" value="<string name='Actions/Drukhari/BloodDancerFlavor'/>"/>
	<entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
	<entry name="Drukhari/BonusResourcesDescription" value="Aumenta la producción de los edificios."/>
	<entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
	<entry name="Drukhari/BridesOfDeath" value="<string name='Actions/Drukhari/BridesOfDeath'/>"/>
	<entry name="Drukhari/BridesOfDeathDescription" value="Aumenta el daño en combate cuerpo a cuerpo."/>
	<entry name="Drukhari/BridesOfDeathFlavor" value="<string name='Actions/Drukhari/BridesOfDeathFlavor'/>"/>
	<entry name="Drukhari/CityTier2" value="Hinchado por el Pecado"/>
	<entry name="Drukhari/CityTier2Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Drukhari/CityTier2Flavor" value="Según los incursores Drukhari se acostumbran a un planeta, su base de incursión se amplía, atrayendo a más de los pobladores más viles de la galaxia, y son capaces de apoyar a una población más amplia de los malvados herederos de los Aeldari. Los barrios bajos y los jardines de tortura de la ciudad se expanden a lo ancho y hacia abajo…"/>
	<entry name="Drukhari/CityTier3" value="Dominio de la Desesperación"/>
	<entry name="Drukhari/CityTier3Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Drukhari/CityTier3Flavor" value="Los Arcontes y Dracontes de este reino pirata se han vuelto tan ricos que han establecido palacios para sí mismos, sabiendo bien que no pueden quedarse, pues La Sedienta consumirá sus almas completamente. Por supuesto, mientras están aquí, necesitan un flujo constante de cautivos para saciar su inhumana lujuria y evitar la podredumbre de sus almas."/>
	<entry name="Drukhari/CombatDrugs" value="Drogas de Combate"/>
	<entry name="Drukhari/CombatDrugsDescription" value="Aumenta varias habilidades de combate."/>
	<entry name="Drukhari/CombatDrugsFlavor" value="Aunque reducen drásticamente la esperanza de vida de sus consumidores, los estimulantes químicos se usan ampliamente para mejorar el desempeño en combate."/>
	<entry name="Drukhari/CorsairOutposts" value="Puestos Avanzados Corsarios"/>
	<entry name="Drukhari/CorsairOutpostsDescription" value="Las ciudades Drukhari tienen un crecimiento menor por defecto. Los puestos avanzados controlados proporcionan crecimiento adicional."/>
	<entry name="Drukhari/CorsairOutpostsFlavor" value="Aunque estos pueblos se parecen a las ciudades de las razas rebaño, los Drukhari no permanecerán mucho tiempo en ellos a menos que sepan que hay riquezas cerca. Pero las bases incursoras más exitosas atraerán más Drukhari, ansiosos de tener su parte de asesinatos y dolor."/>
	<entry name="Drukhari/CorsairOutpostsUpgrade" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgrade'/>"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgradeDescription'/>"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgradeDescription'/>"/>
	<entry name="Drukhari/Dodge" value="Esquiva"/>
	<entry name="Drukhari/DodgeDescription" value="Aumenta la reducción de daño en combate cuerpo a cuerpo."/>
	<entry name="Drukhari/DodgeFlavor" value="Los diversos guerreros – conocidos como los Hekatarii – de los Cultos de Brujas disponen de un diverso abanico de herramientas con las que mutilar, atrapar, cortar y empalar. Llevan poca armadura; su defensa es la velocidad y la agilidad, mostrando su arrogancia al esquivar los torpes golpes del enemigo."/>
	<entry name="Drukhari/EnergyBuildingBonus" value="Colector Ilmaea Relajado"/>
	<entry name="Drukhari/EnergyBuildingBonusDescription" value="Aumenta la producción de energía."/>
	<entry name="Drukhari/EnergyBuildingBonusFlavor" value="Esta central de energía sólo accede a una pequeñísima fracción del poder de una de las Ilmaeas Drukhari—los Soles Negros capturados que rodean Commorragh. Relajar la seguridad de los colectores un pelín implica aumentar el poder considerablemente—al mismo tiempo aumentando de manera tremenda la posibilidad de que la Ilmaea falle y consuma Gladius Prime de un plumazo. Es un riesgo que los casi-inmortales Drukhari aceptan alegremente."/>
	<entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
	<entry name="Drukhari/EnhancedAethersailsDescription" value="<string name='Actions/Drukhari/EnhancedAethersailsDescription'/>"/>
	<entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
	<entry name="Drukhari/Flickerfield" value="Campo Parpadeante"/>
	<entry name="Drukhari/FlickerfieldDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="Drukhari/FlickerfieldFlavor" value="Los Campos Parpadeantes son escudo de fuerza óptica muy avanzados, que hacen que el vehículo que los monta parezca parpadear dentro y fuera de la realidad."/>
	<entry name="Drukhari/GhostplateArmour" value="Armadura Fantasmal"/>
	<entry name="Drukhari/GhostplateArmourDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="Drukhari/GhostplateArmourFlavor" value="Aquellos Eldar Oscuros que desean una gran cantidad de protección mientras mantienen un buen grado de movilidad visten armadura creada a partir de resinas endurecidas y rellena de gases más ligeros que el aire. La Armadura Fantasmal también incorpora tecnología de campos de fuerza menores para ofrecer una mejor protección al usuario."/>
	<entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
	<entry name="Drukhari/GrislyTrophiesDescription" value="Reduce la pérdida de moral."/>
	<entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
	<entry name="Drukhari/HeavyWeaponBonus" value="Artillería sobrepasada"/>
	<entry name="Drukhari/HeavyWeaponBonusDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Drukhari/HeavyWeaponBonusFlavor" value="“No esperéis un nuevo día, pequeños mortales. Los Drukhari os tienen ahora y nuestro control es inquebrantable. Os lo aseguro, todo lo que veréis a partir de ahora será la noche interminable y el tormento eterno.” – Gyrthineus Roche, Arconte de la última espada."/>
	<entry name="Drukhari/MasterOfPain" value="<string name='Actions/Drukhari/MasterOfPain'/>"/>
	<entry name="Drukhari/MasterOfPainDescription" value="Otorga reducción de daño adicional No Hay Dolor."/>
	<entry name="Drukhari/MasterOfPainFlavor" value="<string name='Actions/Drukhari/MasterOfPainFlavor'/>"/>
	<entry name="Drukhari/MeleeWeaponBonus" value="Estética de la Espada, perfeccionada."/>
	<entry name="Drukhari/MeleeWeaponBonusDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Drukhari/MeleeWeaponBonusFlavor" value="“El dolor es la única constante universal. El dolor lo es todo. Es la clave tanto para la creación como para la destrucción. Así, quien domina el dolor se convierte en dios.” – Urien Rakarth, maestro de los Haemonculi"/>
	<entry name="Drukhari/NightShields" value="Escudos Nocturnos"/>
	<entry name="Drukhari/NightShieldsDescription" value="Aumenta la reducción de daño a distancia."/>
	<entry name="Drukhari/NightShieldsFlavor" value="El vehículo está cubierto por un campo de desplazamiento de amplio espectro, envolviéndolo en una fría oscuridad. Los enemigos tienen problemas para apuntar contra el vehículo, pues está escondido en un manto de ondulantes sombras."/>
	<entry name="Drukhari/NoEscape" value="<string name='Actions/Drukhari/NoEscape'/>"/>
	<entry name="Drukhari/NoEscapeDescription" value="Reduce el movimiento."/>
	<entry name="Drukhari/NoEscapeFlavor" value="<string name='Actions/Drukhari/NoEscapeFlavor'/>"/>
	<entry name="Drukhari/Overlord" value="<string name='Actions/Drukhari/Overlord'/>"/>
	<entry name="Drukhari/OverlordDescription" value="Aument la precisión."/>
	<entry name="Drukhari/OverlordFlavor" value="<string name='Actions/Drukhari/OverlordFlavor'/>"/>
	<entry name="Drukhari/PowerFromPain" value="Poder desde el Dolor"/>
	<entry name="Drukhari/PowerFromPainDescription" value="Otorga bonificaciones en combate según la unidad sube de nivel. Aumenta la reducción de daño por encima de nivel 3, aumenta el daño en combate cuerpo a cuerpo a partir del nivel 6 y reduce la pérdida de moral a partir del nivel 10."/>
	<entry name="Drukhari/PowerFromPainFlavor" value="Al alimentarse de las almas de sus enemigos, los Drukhai se imbuyen de una fuerza sobrenatural, llegando a ser, con el tiempo, máquinas de matar."/>
	<entry name="Drukhari/RaidersTactics" value="Tácticas Incursoras"/>
	<entry name="Drukhari/RaidersTacticsDescription" value="Otorga rasgos de bonificación cuando esta unidad desembarca de un transporte."/>
	<entry name="Drukhari/RaidersTacticsFlavor" value="“¿Por qué nos desplazamos sobre estas elegantes naves? Para oir mejor los gritos de nuestras presas al alcanzarlas, para visualizar el miedo esculpido en sus rostros, para saborear el tentador toque de la sangre en el aire como un aperitivo antes del festín. Pero sobretodo, para que la matanza empiece cuanto antes.” – Dariaq Lenguafilo del Ojo Perforado."/>
	<entry name="Drukhari/RaidersTacticsDamage" value="Asalto Incursor"/>
	<entry name="Drukhari/RaidersTacticsDamageDescription" value="Aumenta el daño."/>
	<entry name="Drukhari/RaidersTacticsDamageFlavor" value="Milenios de depredación en el plano mortal significa que los Drukhari son depredadores inhumanos sin rival en la Historia. Esto es en parte por siglos de experiencia, en parte por evolución y en parte por las adaptaciones de los Hemónculos. Cuando emergen de sus naves, caen sobre sus enemigos como águilas en picado."/>
	<entry name="Drukhari/RaidersTacticsDamageReduction" value="Evasión Incursora"/>
	<entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="Los Drukhari viven para infligir dolor—y hay momentos cruciales en su existencia que lo ejemplifican. Cuando cae la guja de un íncubo; cuando una Bruja atormenta a su víctima en la arena; y el milisegundo de sorpresa cuando los incursores Drukhari emergen de sus naves…"/>
	<entry name="Drukhari/RaidersTacticsHealingRate" value="Preparación Incursora"/>
	<entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Aumenta la tasa de recuperación de puntos de vida cuando esta unidad embarca en un vehículo."/>
	<entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="“Amigos-así os llamo, aunque sé que me apuñalaríais en la espalda por un vistazo de Vect—amigos, estad preparados. El momento casi ha llegado, el rebaño no tiene ni idea de que llegamos. Pero en minutos descenderemos, azotaremos, desgarraremos, atormentaremos, asesinaremos y beberemos sus almas…con estilo, por supuesto. No somos salvajes.” – Gyrthineus Roche, Arconte de La Última Hoja"/>
	<entry name="Drukhari/Shadowfield" value="<string name='Actions/Drukhari/Shadowfield'/>"/>
	<entry name="Drukhari/ShadowfieldDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="Drukhari/ShadowfieldFlavor" value="<string name='Actions/Drukhari/ShadowfieldFlavor'/>"/>
	<entry name="Drukhari/SacrificeToKhaine" value="Sacrificio a Khaine"/>
	<entry name="Drukhari/SacrificeToKhaineDescription" value="Aumenta el daño."/>
	<entry name="Drukhari/SacrificeToKhaineFlavor" value="Los Drukhari no veneran a los viejos dioses Aeldari ni a los Antiguos, menos a uno—Khaela Mensha Khaine, dios de la muerte y la guerra. Sólo un puñado de Drukhari ofrecen sus respetos a Khaine y la mayoría son Íncubos, los reflejos oscuros de los Guerreros de la Senda Aeldari. Cada vez que matan con sus gujas rituales, es en honor a Khaine."/>
	<entry name="Drukhari/ShroudGate" value="Puerta Mortaja"/>
	<entry name="Drukhari/ShroudGateDescription" value="Aumenta la reducción de daño a distancia."/>
	<entry name="Drukhari/ShroudGateFlavor" value="“Fue inteligente montar la emboscada en la puerta de la telaraña, valiente pequeño humano. Calla, ahora. Pero no sabías cuándo ni dónde emergeríamos, y ese ha sido tu error. Para nosotros los Drukhari, las puertas son una conveniencia innecesaria…pero vuestro sufrimiento, me temo, es una necesidad.” – Gyrthineus Roche, Arconte de La Última Hoja."/>
	<entry name="Drukhari/SoulHunger" value="Hambre de Almas"/>
	<entry name="Drukhari/SoulHungerDescription" value="Otorga influencia al eliminar a un enemigo."/>
	<entry name="Drukhari/SoulHungerFlavor" value="El único sustento que refresca a un Drukhari—la única cosa que hace sus vidas inmortales sabrosas y soportables, mientras Slaanesh consume sus almas—es el sufrimiento de otros. Y aquellos Arcontes que pueden asegurar un suministro estable de víctimas y dolor reciben lealtad—o, mejor dicho, lo más parecido a la lealtad que un Drukhari puede ofrecer…"/>
	<entry name="Drukhari/SoulHungerLoyalty" value="Pan de Almas y Circo"/>
	<entry name="Drukhari/SoulHungerLoyaltyDescription" value="Aumenta la producción de lealtad."/>
	<entry name="Drukhari/SoulHungerLoyaltyFlavor" value="En un mundo tan vacío de lealtad, sale a cuenta tener de tu lado a las masas — ¿quién sabe cuándo necesitarás lanzar una horda contra tu rival, por ejemplo? Cuando un Arconte paga a un Culto de Brujas para que ofrezcan un espectáculo en la arena, o reparte comida, armas o víctimas a los desesperados de los bajos fondos de Commorragh, todo Drukhari entiende que no lo hace por compasión sino por cálculo."/>
	<entry name="Drukhari/SoulHungerOutposts" value="Diezmo del Alma"/>
	<entry name="Drukhari/SoulHungerOutpostsDescription" value="Aumenta la producción de recursos."/>
	<entry name="Drukhari/SoulHungerOutpostsFlavor" value="Por supuesto, todos los Cabalitas Drukhari están plenamente en deuda con su Arconte y no harán nada que les haga perder su favor…y, aún así, de algún modo, hay un vibrante mercado negro de recursos robados, especialmente cuanto más lejos de los asientos de poder. Así que cuando llega la orden de enviar sus diezmos, es sorprendentemente fácil de cumplir con ellos."/>
	<entry name="Drukhari/SoulHarvest" value="<string name='Actions/Drukhari/SoulHarvest'/>"/>
	<entry name="Drukhari/SoulHarvestDescription" value="Aumenta el daño y otorga influencia al eliminar a un enemigo."/>
	<entry name="Drukhari/SoulHarvestFlavor" value="<string name='Actions/Drukhari/SoulHarvestFlavor'/>"/>
	<entry name="Drukhari/FeastOfTorment" value="Feast of Torment"/>
	<entry name="Drukhari/FeastOfTormentDescription" value="Restores hitpoints each turn."/>
	<entry name="Drukhari/FeastOfTormentFlavor" value="Animar a los Drukhari a cometer actos de sadismo es innecesario, es algo natural en ellos. Sin embargo, aún temen a la ira del Arconte, así que darles permiso para torturar a los inocentes de Gladius Prime en vez de transportarlos todo el camino hasta Commorragh es a veces necesario. Como nota al margen, la sangre inocente tiene un notable efecto rejuvenecedor."/>
	<entry name="Drukhari/SoulFeast" value="Festival de Tormento"/>
	<entry name="Drukhari/SoulFeastDescription" value="Restaura puntos de vida cada turno."/>
	<entry name="Drukhari/SoulFeastFlavor" value="Animar a los Drukhari a cometer actos de sadismo es innecesario, es algo natural en ellos. Sin embargo, aún temen a la ira del Arconte, así que darles permiso para torturar a los inocentes de Gladius Prime en vez de transportarlos todo el camino hasta Commorragh es a veces necesario. Como nota al margen, la sangre inocente tiene un notable efecto rejuvenecedor."/>
	<entry name="Drukhari/SpiritProbe" value="<string name='Actions/Drukhari/SpiritProbe'/>"/>
	<entry name="Drukhari/SpiritProbeDescription" value="Aumenta la reducción de daño."/>
	<entry name="Drukhari/SpiritProbeFlavor" value="<string name='Actions/Drukhari/SpiritProbeFlavor'/>"/>
	<entry name="Drukhari/ToweringArrogance" value="<string name='Actions/Drukhari/ToweringArrogance'/>"/>
	<entry name="Drukhari/ToweringArroganceDescription" value="Disminuye la pérdida de moral."/>
	<entry name="Drukhari/ToweringArroganceFlavor" value="<string name='Actions/Drukhari/ToweringArroganceFlavor'/>"/>
	<entry name="Drukhari/WealthPlunder" value="Estudios de Agonía"/>
	<entry name="Drukhari/WealthPlunderDescription" value="Otorga investigación al eliminar a un enemigo."/>
	<entry name="Drukhari/WealthPlunderFlavor" value="“Cerca de la muerte, los mantengo. Los secretos que susurran. Las súplicas. Tácticas, inventos, conocimiento del otro lado, revelaciones…todo derramado. Para parar el dolor. Por qué debería pararlo, cuando es tan…productivo.” – Arkanic, Hemónculo de la Kábala de La Última Hoja"/>
	<entry name="Drukhari/WeaponRacks" value="Estantes de Armas"/>
	<entry name="Drukhari/WeaponRacksDescription" value="Otorga armas acopladas durante un turno a las unidades que desembarquen de este vehículo."/>
	<entry name="Drukhari/WeaponRacksFlavor" value="Algunos vehículos de los Eldar Oscuros tienen estantes de armas adicionales con armamento anti-persona. Esto permite a los ocupantes, literalmente, vaciar sus cargadores en grandes ráfagas antes de reemplazar sus armas por otras cargadas hasta los topes antes de desembarcar."/>
	<entry name="Drukhari/WhirlingDeath" value="<string name='Actions/Drukhari/WhirlingDeath'/>"/>
	<entry name="Drukhari/WhirlingDeathDescription" value="Impacta en todos los miembros de la unidad objetivo."/>
	<entry name="Drukhari/WhirlingDeathFlavor" value="<string name='Actions/Drukhari/WhirlingDeathFlavor'/>"/>
	<entry name="EavyArmour" value="Armadura pezada"/>
	<entry name="EavyArmourDescription" value="Aumenta el blindaje."/>
	<entry name="EavyArmourFlavor" value="Las armaduraz pezadaz orkas están hechas de chatarra, chapa y placas metálicas saqueadas de enemigos muertos. Aunque su montaje es dudoso, la armadura pezado proporciona una defensa solida al que lo lleva."/>
	<entry name="Eldar/AerobaticGrace" value="Gracia acrobática"/>
	<entry name="Eldar/AerobaticGraceDescription" value="Proporciona reducción del daño a distancia si la unidad no se ha movido este turno."/>
	<entry name="Eldar/AerobaticGraceFlavor" value="Mientras que los arlequines son los verdaderos maestros de la danza entre los Aeldari, los Lanzas Brillantes se acercan a ellos en su control de sus motos a reacción, pareciendo hacer piruetas entre las tormentas de fuego bólter mientras cargan contra su enemigo."/>
	<entry name="Eldar/AircraftBuildingBonus" value="Invocación de Kurnous"/>
	<entry name="Eldar/AircraftBuildingBonusDescription" value="Aumenta la producción de los puestos avanzados."/>
	<entry name="Eldar/AircraftBuildingBonusFlavor" value="Los Cazadores Carmesíes deben su lealtad a Kurnous, el Dios Aeldari de la Caza. Aunque lleva 60 millones de años muerto, cuando se invoca su nombre redoblan sus esfuerzos, tanto en el reclutamiento como en la construcción."/>
	<entry name="Eldar/AncientDoom" value="Condenación ancestral"/>
	<entry name="Eldar/AncientDoomDescription" value="Aumenta la puntería y el daño infligido a unidades del Caos."/>
	<entry name="Eldar/AncientDoomFlavor" value="Los Aeldari odian y temen a La Sedienta por encima de todo, ya que en Slaanesh ven su destino manifestarse."/>
	<entry name="Eldar/AssaultWeaponBonus" value="Munición sub-molecular"/>
	<entry name="Eldar/AssaultWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
	<entry name="Eldar/AssaultWeaponBonusFlavor" value="La munición única shuriken del Aeldari consiste en cuchillas giratorias monomoleculares disparadas a gran velocidad. Pero las moléculas son tan limitantes cuando hay estructuras más pequeñas y mortales que usar."/>
	<entry name="Eldar/AssuredDestruction" value="Destrucción asegurada"/>
	<entry name="Eldar/AssuredDestructionDescription" value="Aumenta el daño contra vehículos."/>
	<entry name="Eldar/AssuredDestructionFlavor" value="“Si un capitán es tan tonto como para permitir que cinco Aeldari con fusiles de fusión y varios milenios de experiencia en combate se acerquen a 20 metros de su tanque… bueno, francamente, merece ser un charco de ícor fundido en el suelo.”<br/>  — Comisario Gruber"/>
	<entry name="Eldar/AsuryaniArrivals" value="<string name='Actions/Eldar/AsuryaniArrivals'/>"/>
	<entry name="Eldar/AsuryaniArrivalsDescription" value="<string name='Actions/Eldar/AsuryaniArrivalsDescription'/>"/>
	<entry name="Eldar/AsuryaniArrivalsFlavor" value="<string name='Actions/Eldar/AsuryaniArrivalsFlavor'/>"/>
	<entry name="Eldar/AutarchsAssault" value="<string name='Actions/Eldar/AutarchsAssault'/>"/>
	<entry name="Eldar/AutarchsAssaultDescription" value="Aumenta el daño."/>
	<entry name="Eldar/AutarchsAssaultFlavor" value="<string name='Actions/Eldar/AutarchsAssaultFlavor'/>"/>
	<entry name="Eldar/AutarchsAssaultPassive" value="<string name='Actions/Eldar/AutarchsAssault'/>"/>
	<entry name="Eldar/BansheeMask" value="Máscara espectro"/>
	<entry name="Eldar/BansheeMaskDescription" value="<string name='Traits/InfiltrateDescription'/>"/>
	<entry name="Eldar/BansheeMaskFlavor" value="Amplifica el grito de batalla de los Aeldari, infligiendo parálisis psíquica."/>
	<entry name="Eldar/BattleFocus" value="Enfoque en la batalla"/>
	<entry name="Eldar/BattleFocusDescription" value="Las acciones no gastan movimiento."/>
	<entry name="Eldar/BattleFocusFlavor" value="Cuando los Asuryani se ponen sus máscaras de guerra, entran en un trance de batalla tan concentrado que fluyen por el campo de batalla como el azogue, matando a sus enemigos sin romper el paso."/>
	<entry name="Eldar/CityTier2" value="Punto de apoyo exodita"/>
	<entry name="Eldar/CityTier2Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Eldar/CityTier2Flavor" value="A pesar de los milenios que han pasado a la deriva de las estrellas, algunos exploradores de los Aeldari del mundo astronave han pasado el tiempo en los mundos exodita y los mundos vírgenes, y han aprendido a establecer vida en los planetas, cultivando alimentos y construyendo sus edificios de hueso espectral a la antigua usanza."/>
	<entry name="Eldar/CityTier3" value="Infraestructura exodita"/>
	<entry name="Eldar/CityTier3Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Eldar/CityTier3Flavor" value="Experimentados diseños de construcción exoditas están siendo empleados por los aedas óseos del mundo astronave. Sólo habiendo trabajado en los antiguos mundos astronave, la idea de construir una ciudad por primera vez es algo completamente nuevo para sus mentes."/>
	<entry name="Eldar/Command" value="<string name='Actions/Eldar/Command'/>"/>
	<entry name="Eldar/CommandDescription" value="Aumenta la puntería."/>
	<entry name="Eldar/CommandFlavor" value="<string name='Actions/Eldar/CommandFlavor'/>"/>
	<entry name="Eldar/ConstructionBuildingBonus" value="Convocación de aeda óseo"/>
	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Aumenta la producción."/>
	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="Los aedas óseos son raros y apreciados, más incluso entre los dispersos Aeldari—pero han venido en masa a la llamada del Vidente, para capturar este planeta único para su raza moribunda."/>
	<entry name="Eldar/CrackShot" value="Tirador excepcional"/>
	<entry name="Eldar/CrackShotDescription" value="Aumenta la puntería y la penetración de armadura."/>
	<entry name="Eldar/CrackShotFlavor" value="Los Dragones de Fuego son temidos, con razón, como cazadores de tanques y por encima su Exarca, quien lidera su escuadrón desde la vanguardia, inspirando a su escuadrón a más destrucción con sus propias hazañas devastadoras."/>
	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Actions/Eldar/CrystalTargetingMatrix'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixDescription" value="<string name='Actions/Eldar/CrystalTargetingMatrixDescription'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Actions/Eldar/CrystalTargetingMatrixFlavor'/>"/>
	<entry name="Eldar/Doom" value="<string name='Actions/Eldar/Doom'/>"/>
	<entry name="Eldar/DoomDescription" value="Aumenta el daño sufrido."/>
	<entry name="Eldar/DoomFlavor" value="<string name='Actions/Eldar/DoomFlavor'/>"/>
	<entry name="Eldar/ExpertHunter" value="Cazador experto"/>
	<entry name="Eldar/ExpertHunterDescription" value="Aumenta el daño contra criaturas monstruosas, vehículos y fortificaciones."/>
	<entry name="Eldar/ExpertHunterFlavor" value="El armamento de los lanzas brillantes puede ser de corto alcance pero sus lanzas estelares y lanzas láser son increíblemente poderosas, ya que se mueven alrededor de objetivos más grandes, buscando sus puntos débiles."/>
	<entry name="Eldar/FoodBuildingBonus" value="Cultivo exquisito"/>
	<entry name="Eldar/FoodBuildingBonusDescription" value="Aumenta la generación de comida."/>
	<entry name="Eldar/FoodBuildingBonusFlavor" value="Los Aeldari sienten tan intensamente que incluso sus alimentos básicos son obras de arte, cultivadas por artesanos que han pasado milenios perfeccionando su cocina. Con una amplia gama de ingredientes y condimentos, pueden hacer milagros con los alimentos aparentemente más simples."/>
	<entry name="Eldar/Forceshield" value="Escudo de energía"/>
	<entry name="Eldar/ForceshieldDescription" value="Aumenta la reducción del daño."/>
	<entry name="Eldar/ForceshieldFlavor" value="Estos poderosos proyectores de escudo pueden desviar casi cualquier golpe."/>
	<entry name="Eldar/HeavyWeaponBonus" value="Motor de Vaul"/>
	<entry name="Eldar/HeavyWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
	<entry name="Eldar/HeavyWeaponBonusFlavor" value="Mientras que el armamento de plasma de la humanidad es a menudo fatal para sus usuarios, los elegantes equivalentes de Aeldari no albergan tales amenazas. Y ahora un descubrimiento fortuito en la superficie de Gladius Prime ha permitido a los asedas óseos y a los exarcas mejorar su enfoque."/>
	<entry name="Eldar/HoloFields" value="Holopantalla"/>
	<entry name="Eldar/HoloFieldsDescription" value="Aumenta la reducción del daño si la unidad se ha movido este turno."/>
	<entry name="Eldar/HoloFieldsFlavor" value="Aprovechando la energía cinética para brillar y distorsionar la silueta del vehículo, las holopantallas impiden que el enemigo apunte con precisión a la nave cuando ésta se desplaza por el campo de batalla."/>
	<entry name="Eldar/InescapableAccuracy" value="Puntería Ineludible"/>
	<entry name="Eldar/InescapableAccuracyDescription" value="Aumenta la puntería contra motos, motos a reacción, voladores y vehículos que hayan movido este turno."/>
	<entry name="Eldar/InescapableAccuracyFlavor" value="Los eones de experiencia en combate heredados luchando contra sus parientes, los Drukhari, ha enseñado a los Segadores Siniestros Guerreros de la Senda de los Eldar a seguir incluso a sus esquivas motos a reacción, protegidas por holo-proyecciones. Alcanzar a esos objetivos con sus letales misiles Segador es simple para ellos—y todo parte de su emulación de Khaine en su rol de El Destructor."/>	
	<entry name="Eldar/InfantryBuildingBonus" value="Invocación de Asuryan"/>
	<entry name="Eldar/InfantryBuildingBonusDescription" value="Aumenta la generación de producción."/>
	<entry name="Eldar/InfantryBuildingBonusFlavor" value="Asurmen es el fundador de la senda del guerrero y el primero de los Señores Fénix, los creadores de los templos de la senda. Invocar su nombre seguramente traerá más guerreros a Gladius Prime, para tomar este planeta para los Aeldari."/>
	<entry name="Eldar/InfluenceBuildingBonus" value="Oráculo de Lileath"/>
	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Aumenta la generación de influencia."/>
	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="Muy pocos seres mortales han tenido la previsión de un Vidente—un poder quizás derivado de la diosa perdida de Aeldari, Lileath, que predijo la muerte de los dioses a manos del mortal Aeldari. Un oráculo que invoca su nombre atrae los ojos de todos los Aeldari sobre ti…"/>
	<entry name="Eldar/Jinx" value="Maldición"/>
	<entry name="Eldar/JinxDescription" value="Reduce el blindaje."/>
	<entry name="Eldar/JinxFlavor" value="<string name='Actions/Eldar/ProtectJinxFlavor'/>"/>
	<entry name="Eldar/KhaineAwakened" value="<string name='Actions/Eldar/KhaineAwakened'/>"/>
	<entry name="Eldar/KhaineAwakenedDescription" value="Aumenta el daño de los ataques cuerpo a cuerpo, reduce la perdida de moral y proporciona inmunidad al miedo y al enclavamiento."/>
	<entry name="Eldar/KhaineAwakenedFlavor" value="<string name='Actions/Eldar/KhaineAwakenedFlavor'/>"/>
	<entry name="Eldar/KhainesMight" value="<string name='Actions/Eldar/KhainesMight'/>"/>
	<entry name="Eldar/KhainesMightDescription" value="Aumenta los ataques cuerpo a cuerpo."/>
	<entry name="Eldar/KhainesMightFlavor" value="<string name='Actions/Eldar/KhainesMightFlavor'/>"/>
	<entry name="Eldar/LinkedFire" value="<string name='Actions/Eldar/LinkedFire'/>"/>
	<entry name="Eldar/LinkedFireDescription" value="Aumenta el daño y la penetración de blindaje del cañón prisma."/>
	<entry name="Eldar/LinkedFireFlavor" value="<string name='Actions/Eldar/LinkedFireFlavor'/>"/>
	<entry name="Eldar/MarksmansEye" value="Ojo de tirador"/>
	<entry name="Eldar/MarksmansEyeDescription" value="Aumenta la puntería."/>
	<entry name="Eldar/MarksmansEyeFlavor" value="Sólo un cazador carmesí consideraría que pilotar la nave Aeldari más rápida y maniobrable implica tan poco desafío, que también son capaces de apuntar y disparar tres armas distintas con diferentes velocidades de fuego al mismo tiempo."/>
	<entry name="Eldar/MeleeWeaponBonus" value="Planos sub-moleculares"/>
	<entry name="Eldar/MeleeWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
	<entry name="Eldar/MeleeWeaponBonusFlavor" value="Los filamentos y filos monomoleculares de los Aeldari son lo suficientemente letales por sí mismos, pero con la más mínima adición de tecnología de fase, atraviesan incluso la armadura más gruesa."/>
	<entry name="Eldar/MindWar" value="<string name='Actions/Eldar/MindWar'/>"/>
	<entry name="Eldar/MindWarDescription" value="Reduce la puntería."/>
	<entry name="Eldar/MindWarFlavor" value="<string name='Actions/Eldar/MindWarFlavor'/>"/>
	<entry name="Eldar/MoltenBody" value="Cuerpo ardiente"/>
	<entry name="Eldar/MoltenBodyDescription" value="Inmune a las llamas y a las armas de fusión."/>
	<entry name="Eldar/MoltenBodyFlavor" value="El Avatar de Khaine no es simplemente el último fragmento de un dios moribundo—más específicamente, es una construcción de hierro sobrecalentado con un núcleo que es literalmente lava, animado por el poder psíquico de los Aeldari y la ira eterna del dios. Calentarlo solo lo hace más furioso…"/>
	<entry name="Eldar/OreBuildingBonus" value="Extracción eficiente"/>
	<entry name="Eldar/OreBuildingBonusDescription" value="Aumenta la generación de mineral."/>
	<entry name="Eldar/OreBuildingBonusFlavor" value="Los Aeldari necesitan menos metales que otras razas, dado que casi todas sus estructuras y vehículos son energía psíquica hecha tangible, en forma de hueso espectral. Pero para la pequeña cantidad que necesitan, siempre hay mejoras que hacer."/>
	<entry name="Eldar/PowerField" value="Campo de energía"/>
	<entry name="Eldar/PowerFieldDescription" value="Aumenta la reducción del daño."/>
	<entry name="Eldar/PowerFieldFlavor" value="Los campos de energía redirigen una parte del suministro de energía del vehículo para proyectar un escudo brillante de protección alrededor del vehículo."/>
	<entry name="Eldar/Protect" value="Protección"/>
	<entry name="Eldar/ProtectDescription" value="Aumenta el blindaje."/>
	<entry name="Eldar/ProtectFlavor" value="<string name='Actions/Eldar/ProtectJinxFlavor'/>"/>
	<entry name="Eldar/ReaperRangefinder" value="Telémetro Segador"/>
	<entry name="Eldar/ReaperRangefinderDescription" value="Ignora las reducción de daño a distancia otorgadas por Jink."/>
	<entry name="Eldar/ReaperRangefinderFlavor" value="Montados en los cascos de los Segadores Siniestros se encuentran avanzados sistemas de puntería que calculan los parámetros en un abrir y cerrar de ojos."/>	
	<entry name="Eldar/RemnantsOfTheFall" value="Remanentes de la caída"/>
	<entry name="Eldar/RemnantsOfTheFallDescription" value="Reduce el ratio de crecimiento."/>
	<entry name="Eldar/RemnantsOfTheFallFlavor" value="Desde el nacimiento de Slaanesh, los Aeldari que quedan han luchado por sobrevivir en una galaxia hostil. Su larga vida significa que siempre tuvieron una baja tasa de fertilidad y una gestación lenta, por lo que luchan por reemplazar a los que caen en la batalla. En muchos mundos astronave, los muertos superan con creces a los vivos."/>
	<entry name="Eldar/ResearchBuildingBonus" value="Bibliotecarios negros"/>
	<entry name="Eldar/ResearchBuildingBonusDescription" value="Aumenta la generación de investigación."/>
	<entry name="Eldar/ResearchBuildingBonusFlavor" value="Los Videntes de la Biblioteca Negra nos enviaron en este encargo y ahora ofrecen su apoyo. Con el acceso a sus archivos, deberíamos ser capaces de redescubrir las tecnologías perdidas y mejorar más rápidamente nuestro punto de apoyo en este planeta."/>
	<entry name="Eldar/ReturnOfTheAeldari" value="<string name='Actions/Eldar/ReturnOfTheAeldari'/>"/>
	<entry name="Eldar/ReturnOfTheAeldariDescription" value="<string name='Actions/Eldar/ReturnOfTheAeldariDescription'/>"/>
	<entry name="Eldar/ReturnOfTheAeldariFlavor" value="<string name='Actions/Eldar/ReturnOfTheAeldariFlavor'/>"/>
	<entry name="Eldar/RuneArmour" value="Armadura rúnica"/>
	<entry name="Eldar/RuneArmourDescription" value="Aumenta la reducción del daño."/>
	<entry name="Eldar/RuneArmourFlavor" value="Los psíquicos Aeldari se hacen una elegante armadura decorada con runas de hueso espectral. Se dice que late a ritmo del latido del corazón del portador, la armadura rúnica ofrece protección contra los ataques tanto de naturaleza espiritual como física."/>
	<entry name="Eldar/Scattershield" value="Escudo dispersor"/>
	<entry name="Eldar/ScattershieldDescription" value="Aumenta la reducción del daño y ciega a la infantería y criaturas monstruosas atacantes en combate cuerpo a cuerpo."/>
	<entry name="Eldar/ScattershieldFlavor" value="Utilizados para proteger las preciosas construcciones de guerra de Aeldari, los escudos de dispersión son gigantescos generadores de escudos en forma de abanico que convierten la energía de los ataques entrantes en cegadores rayos de luz multicolor."/>
	<entry name="Eldar/SerpentShield" value="Escudo serpiente"/>
	<entry name="Eldar/SerpentShieldDescription" value="Aumenta el blindaje mientras el escudo de serpiente no esté en enfriamiento."/>
	<entry name="Eldar/SerpentShieldFlavor" value="<string name='Weapons/SerpentShieldFlavor'/>"/>
	<entry name="Eldar/Skyhunter" value="Cazador de los cielos"/>
	<entry name="Eldar/SkyhunterDescription" value="Aumenta la penetración de blindaje contra unidades voladoras."/>
	<entry name="Eldar/SkyhunterFlavor" value="El espíritu de un exarca perdido de Biel-Tan ha infundido a nuestros cazadores carmesí, revelando secretos de milenios de lucha contra los enemigos de los Aeldari. Su sabiduría redescubierta nos permitirá dominar los cielos como nunca antes."/>
	<entry name="Eldar/SpiritMark" value="<string name='Actions/Eldar/SpiritMark'/>"/>
	<entry name="Eldar/SpiritMarkDescription" value="Aumenta la puntería."/>
	<entry name="Eldar/SpiritMarkFlavor" value="<string name='Actions/Eldar/SpiritMarkFlavor'/>"/>
	<entry name="Eldar/SpiritPreservation" value="Preservación del espíritu"/>
	<entry name="Eldar/SpiritPreservationDescription" value="Otorga energía al morir."/>
	<entry name="Eldar/SpiritPreservationFlavor" value="Somos pocos en número y La Sedienta espera en la Disformidad, para consumir nuestras almas. Sin embargo, llevamos joyas espirituales para capturar nuestra esencia en el momento de la muerte; aunque todos los Aeldari se estremecen de disgusto al pensarlo, cuando se recuperan estos espíritus pueden ser usados para guiar nuestras máquinas de guerra."/>
	<entry name="Eldar/SpiritStones" value="Joyas espirituales"/>
	<entry name="Eldar/SpiritStonesDescription" value="Reduce la perdida de moral."/>
	<entry name="Eldar/SpiritStonesFlavor" value="Algunos vehículos Aeldari incorporan grandes joyas espirituales con un animus incorporado que puede controlar el vehículo si se desactiva."/>
	<entry name="Eldar/StarEngines" value="Motores estelares"/>
	<entry name="Eldar/StarEnginesDescription" value="Aumenta el movimiento."/>
	<entry name="Eldar/StarEnginesFlavor" value="Mientras que todos los vehículos Aeldari son rápidos y ágiles, los que montan motores estelares a menudo son capaces de moverse más rápido de lo que el ojo puede seguir. Las razas menores sólo pueden maravillarse de la fenomenal velocidad y maniobrabilidad de una nave tan equipada."/>
	<entry name="Eldar/TitanHoloFields" value="Holopantalla de Titán"/>
	<entry name="Eldar/TitanHoloFieldsDescription" value="<string name='Traits/Eldar/HoloFieldsDescription'/>"/>
	<entry name="Eldar/TitanHoloFieldsFlavor" value="“Estaba viendo el mejor espectáculo de luces de la Colmena, con chispas y bang-bang y brillantes y taaan psicodélicos… y luego me asombré y los Irregulares estaban muertos-muertos y ese tanque alienígena pasó junto a mí como si yo fuera plastilina.”<br/>  — Soldado Grande, único superviviente del Cuarto de Irregulares de Necromunda"/>
	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
	<entry name="Eldar/TranscendentBlissDescription" value="<string name='Actions/Eldar/TranscendentBlissDescription'/>"/>
	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
	<entry name="Eldar/VectorDancer" value="<string name='Actions/Eldar/VectorDancer'/>"/>
	<entry name="Eldar/VectorDancerDescription" value="<string name='Actions/Eldar/VectorDancerDescription'/>"/>
	<entry name="Eldar/VectorDancerFlavor" value="<string name='Actions/Eldar/VectorDancerFlavor'/>"/>
	<entry name="Eldar/VectoredEngines" value="<string name='Actions/Eldar/VectoredEngines'/>"/>
	<entry name="Eldar/VectoredEnginesDescription" value="<string name='Actions/Eldar/VectoredEnginesDescription'/>"/>
	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Actions/Eldar/VectoredEnginesFlavor'/>"/>
	<entry name="Eldar/VehicleBuildingBonus" value="Invocación de Vaul"/>
	<entry name="Eldar/VehicleBuildingBonusDescription" value="Aumenta la generación de producción."/>
	<entry name="Eldar/VehicleBuildingBonusFlavor" value="Una joya espiritual recuperada de Gladius resultó ser la de un antiguo aeda óseo, perdido aquí hace milenios. Con sus conocimientos sobre la estructura y los recursos del planeta, somos capaces de aumentar enormemente nuestra producción."/>
	<entry name="Enslaved" value="Esclavizado"/>
	<entry name="EnslavedDescription" value="Elimina al esclavizador para liberar a la unidad."/>
	<entry name="EreWeGo" value="¡Allá vamoz!"/>
	<entry name="EreWeGoDescription" value="Aumenta el movimiento."/>
	<entry name="EreWeGoFlavor" value="<string name='Actions/EreWeGoFlavor'/>"/>
	<entry name="ExtraInfantryArmour" value="Armadura de Infantería Extra"/>
	<entry name="ExtraInfantryArmourDescription" value="Aumenta la armadura."/>
	<entry name="ExtraMonstrousCreatureArmour" value="Armadura extra de criatura monstruosa"/>
	<entry name="ExtraMonstrousCreatureArmourDescription" value="Aumenta la armadura."/>
	<entry name="ExtraMonstrousCreatureArmourFlavor" value=""/>
	<entry name="ExtraVehicleArmour" value="Blindaje de vehículo extra"/>
	<entry name="ExtraVehicleArmourDescription" value="Aumenta el blindaje."/>
	<entry name="Fear" value="Miedo"/>
	<entry name="FearDescription" value="Disminuye la moral cada turno."/>
	<entry name="FearFlavor" value="<string name='Actions/AuraOfFearFlavor'/>"/>
	<entry name="Fearless" value="Sin miedo"/>
	<entry name="FearlessDescription" value="Reduce la pérdida de moral y otorga inmunidad al miedo y a quedarse clavado."/>
	<entry name="FearlessFlavor" value="Las tropas sin miedo nunca se rinden y rara vez hacen uso de la cobertura—incluso si es lo más sensato."/>
	<entry name="FeelNoPain" value="No hay dolor"/>
	<entry name="FeelNoPainDescription" value="Aumenta la reducción del daño."/>
	<entry name="FeelNoPainFlavor" value="Ya sea a través de la fuerza de voluntad, aumento biónico o hechicería, este guerrero puede luchar a pesar de las terribles heridas."/>
	<entry name="Flail" value="Flagelar"/>
 	<entry name="FlailDescription" value="Aumenta la reducción del daño cuerpo a cuerpo"/>
 	<entry name="FlailFlavor" value="Equipados con un flagelo, estos guerreros obstaculizan el ataque de los atacantes."/>
	<entry name="Flame" value="Llama"/>
	<entry name="FlameDescription" value="Classification."/>
	<entry name="Fleet" value="Ligereza de pies"/>
	<entry name="FleetDescription" value="Aumenta el movimiento."/>
	<entry name="FleetFlavor" value="Preternaturalmente ágiles, estos guerreros puedem cubrir grandes distancias más rápidamente que sus enemigos."/>
	<entry name="Fleshbane" value="Devoradora de carne"/>
	<entry name="FleshbaneDescription" value="Aumenta el daño contra unidades de infantería y criaturas monstruosas."/>
	<entry name="Flyer" value="Voladores"/>
	<entry name="FlyerDescription" value="Pueden volar sobre elevaciones, agua y unidades enemigas. Ignora la ZOC enemiga. Los voladores no pueden ser alcanzados por armas cuerpo a cuerpo, y son difíciles de alcanzar por armas terrestres que no sean antiáereas. Sin penalización por armamento pesado y artillería."/>
	<entry name="FlyerFlavor" value="El espacio aéreo sobre una batalla está atestado de actividad. Cazas y bombarderos surcan los cielos, luchando entre sí para proporcionar apoyo a las tropas de tierra."/>
	<entry name="Forest" value="Bosque"/>
	<entry name="ForestDescription" value="Aumenta la reducción del daño a distancia a unidades de infantería."/>
	<entry name="ForestFlavor" value="<string name='Features/ForestFlavor'/>"/>
	<entry name="ForestStealth" value="Forest Stealth"/>
	<entry name="ForestStealthDescription" value="Aumenta la reducción del daño de ataques a distancia en un bosque."/>
	<entry name="Fortification" value="Fortificación"/>
	<entry name="FortificationDescription" value="Unidad estacionaria y fortificada. Si es armada, extiende su control sobre los puestos avanzados adyacentes."/>
	<entry name="FullThrottle" value="“¡A todo gas!”"/>
	<entry name="FullThrottleDescription" value="Aumenta el movimiento."/>
	<entry name="FuelledByRage" value="Impulsado por la rabia"/>
	<entry name="FuelledByRageDescription" value="Aumenta los ataques cuando los puntos de vida de la unidad decrece."/>
	<entry name="FuriousCharge" value="Azalto rabiozo"/>
	<entry name="FuriousChargeDescription" value="Aumenta el daño cuerpo a cuerpo."/>
	<entry name="FuriousChargeFlavor" value="Algunos guerreros usan el ímpetu del ataque para alimentar su propia furia."/>
	<entry name="Gargantuan" value="Gargantuesca"/>
	<entry name="GargantuanDescription" value="Clasificación."/>
	<entry name="GargantuanFlavor" value="Las criaturas gargantuescas son de un inmenso tamaña que puede llevarse por delante ejércitos completos. Se imponen en el campo de batalla, haciendo temblar el suelo cuando avanzan hacia el enemigo, aplastando criaturas con sus pies mientras se mueven lentamente."/>
	<entry name="GargantuanCreature" value="<string name='Traits/Gargantuan'/>"/>
	<entry name="GargantuanCreatureDescription" value="<string name='Traits/GargantuanDescription'/>"/>
	<entry name="GargantuanCreatureFlavor" value="<string name='Traits/GargantuanFlavor'/>"/>
	<entry name="Gauss" value="Gauss"/>
	<entry name="GaussDescription" value="El daño mínimo se ajusta a los puntos de vida de la unidad objetivo."/>
	<entry name="GaussFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="GaussDamage" value="Desollador atómico"/>
	<entry name="GaussDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="GaussDamageFlavor" value="En una galaxia repleta de crueles, monstruosas o demenciales armas, la tecnología Gauss es particularmente temida. Una bala de bólter simplemente explotará dentro de ti, pero un arma Gauss te quemará vivo, átomo por átomo."/>
	<entry name="GetsHot" value="Sobrecalentamiento"/>
	<entry name="GetsHotDescription" value="Esta unidad pierde puntos de vida cada vez que dispara su arma."/>
 	<entry name="Graviton" value="Gravitón"/>
 	<entry name="GravitonDescription" value="Aumenta el daño según el blindaje de la unidad objetivo."/>
 	<entry name="GravitonFlavor" value="Algunas armas aplastan a los enemigos con su propio blindaje."/>
	<entry name="GetsHotFlavor" value="Algunas armas se nutren de energías inestables con riesgo de sobrecalentamiento por cada disparo—a menudo en detrimento del portador."/>
	<entry name="Grenade" value="Granada"/>
	<entry name="GrenadeDescription" value="Clasificación."/>
	<entry name="GrotRiggers" value="Kanijoz ayudantez"/>
	<entry name="GrotRiggersDescription" value="Restaura puntos de vida cada turno."/>
	<entry name="GrotRiggersFlavor" value="Ya sea reubicando kachivachez con arremalladoras, o salir a empujar, una tripulación de Kanijoz ayudantez puede ayudar a mantener un vehículo orko en el combate después de caerse a pedazos."/>
	<entry name="GroundAttack" value="Ataque de tierra"/>
	<entry name="GroundAttackDescription" value="Solo puede atacar unidades de tierra."/>
	<entry name="GunnersKillOnSight" value="“¡Artilleros, tirad a matar!”"/>
	<entry name="GunnersKillOnSightDescription" value="Aumenta los ataques a distancia."/>
	<entry name="HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="HammerOfWrathDescription" value="Aumenta el daño por armas que no sean bombas."/>
	<entry name="HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Hammerhand" value="<string name='Actions/Hammerhand'/>"/>
	<entry name="HammerhandDescription" value="<string name='Actions/HammerhandDescription'/>"/>
	<entry name="HammerhandFlavor" value="<string name='Actions/HammerhandFlavor'/>"/>
	<entry name="Hallucination" value="<string name='Actions/Hallucination'/>"/>
	<entry name="HallucinationDescription" value="Reduce los ataques."/>
	<entry name="HallucinationFlavor" value="<string name='Actions/HallucinationFlavor'/>"/>
	<entry name="HarvestResourceFeatures" value="Cosechar recursos de casilla"/>
	<entry name="HarvestResourceFeaturesDescription" value="Cosecha recursos de adyacentes recursos con características especiales."/>
	<entry name="Haywire" value="Disrupción"/>
	<entry name="HaywireDescription" value="Aumenta el daño e ignora la armadura contra vehículo y fortificaciones."/>
	<entry name="HaywireFlavor" value="Las armas disruptoras emiten poderosos pulsos electromagnéticos."/>
	<entry name="Headquarters" value="Unidad de mando"/>
	<entry name="HeadquartersDescription" value="Destruir las unidades de mando destruye la ciudad por completo."/>
	<entry name="HeavyWeapon" value="Pesado"/>
	<entry name="HeavyWeaponDescription" value="Reduce la precisión después de moverse."/>
	<entry name="HellstormTemplate" value="Plantilla Hellstorm"/>
	<entry name="HellstormTemplateDescription" value="<string name='Traits/BeamDescription'/>"/>
	<entry name="Hero" value="Héroe"/>
	<entry name="HeroDescription" value="Aumenta la reducción del daño del héroe."/>
	<entry name="HeroFlavor" value="¿Qué es ser un héroe? Muchos definirían a uno como un miembro inusualmente valiente, inteligente o fuerte de una especie. En un universo de guerra interminable, esto tristemente tiende a significar a alguien que es muy, muy bueno en causar la muerte de sus enemigos, y de alguna manera sobrevivir."/>
	<entry name="HighPower" value="Alta potencia"/>
	<entry name="HighPowerDescription" value="Se activa antes de moverse."/>
	<entry name="HitAndRun" value="Atacar y huir"/>
	<entry name="HitAndRunDescription" value="Ignora la zona de control enemiga."/>
	<entry name="HitAndRunFlavor" value="Algunas tropas emplean una posición de combate flexible, enfrentándose al enemigo cuerpo a cuerpo en un momento, antes de retirarse para atacar con fuerzas renovadas."/>
	<entry name="Homing" value="Guiado"/>
	<entry name="HomingDescription" value="No requiere línea de visión."/>
	<entry name="IgnoresCover" value="Ignora cobertura"/>
	<entry name="IgnoresCoverDescription" value="Ignora la reducción de ataque a distancia de la unidad objetivo."/>
	<entry name="IgnoresCoverFlavor" value="Este arma dispara munición que saca al enemigo de su escondite."/>
	<entry name="Illuminated" value="Iluminado"/>
	<entry name="IlluminatedDescription" value="Reduce la reducción de daño a distancia."/>
	<entry name="Immobilized" value="Inmovilizado"/>
	<entry name="ImmobilizedDescription" value="La unidad no se puede mover."/>
	<entry name="ImperialRuin" value="<string name='Features/ImperialRuin'/>"/>
	<entry name="ImperialRuinDescription" value="Aumenta la reducción del daño a distancia a la unidad de infantería."/>
	<entry name="ImperialRuinFlavor" value="<string name='Features/ImperialRuinFlavor'/>"/>
	<entry name="ImperialSplendour" value="Esplendor imperial"/>
	<entry name="ImperialSplendourDescription" value="Aumenta la influencia"/>
	<entry name="ImperialSplendourFlavor" value="Un gobernador planetario de éxito reflejara la riqueza de su reciente dominio con monumentos al Imperio—basílicas de plastihormigón y estatuas de Marines Espaciales, Primarcas y el Emperador."/>
	<entry name="Infiltrate" value="Infiltrar"/>
	<entry name="InfiltrateDescription" value="Evita que la unidad sea objetivo de ataques de fuego defensivo."/>
	<entry name="InfiltrateFlavor" value="Muchos ejércitos emplean tropas de reconocimiento, los cuales se ocultan durante días, esperando el momento preciso para golpear."/>
	<entry name="InstantDeath" value="Muerte instantánea"/>
	<entry name="InstantDeathDescription" value="Aumenta enormemente el daño contra unidades de infantería y criaturas monstruosas."/>
	<entry name="InstantDeathFlavor" value="Algunos golpes pueden matar a un enemigo directamente, sin importar lo resistente que sea."/>
	<entry name="Invulnerable" value="Invulnerable"/>
	<entry name="InvulnerableDescription" value="Esta unidad es invulnerable."/>
	<entry name="IronHalo" value="Aura de hierro"/>
	<entry name="IronHaloDescription" value="Aumenta la reducción del daño invulnerable"/>
	<entry name="IronHaloFlavor" value="El Aura de hierro es un honor otorgado a los comandantes de los Marines Espaciales y un símbolo de su valentía y sabiduría excepcional. Generalmente montado sobre la mochila o incorporado en un collar blindado, contiene un campo de energía que protege incluso contra las armas enemigas más potentes."/>
	<entry name="IronWill" value="Voluntad de hierro"/>
	<entry name="IronWillDescription" value="<string name='Actions/IronWillDescription'/>"/>
	<entry name="IronWillFlavor" value="<string name='Actions/IronWillFlavor'/>"/>
	<entry name="ItWillNotDie" value="Nunca muere"/>
	<entry name="ItWillNotDieDescription" value="Restaura puntos de vida cada turno."/>
	<entry name="ItWillNotDieFlavor" value="En rincones oscuros de la galaxia hay criaturas que sanan a una velocidad escalofriante."/>
	<entry name="JetPack" value="Retroreactor"/>
	<entry name="JetPackDescription" value="Esta unidad puede moverse sobre el agua ignorando la penalización al movimiento de los ríos y alambre de espino."/>
	<entry name="JetPackFlavor" value="Los equipos a reacción están diseñados para proporcionar plataformas de fuego estables en un medio para entrar en combate cercano."/>
	<entry name="Jetbike" value="moto a reacción"/>
	<entry name="JetbikeDescription" value="Esta unidad puede moverse sobre el agua ignorando la penalización al movimiento de los ríos y alambre de espino."/>
	<entry name="JetbikeFlavor" value="La tecnología para hacer gravíticos más fiables y rápidos es más difícil de lo que parece, y por muchos años solo los Aeldari la poseían. Recientemente, los Orkos han desarrollado su propia versión rudimentaria, el Kóptero, y los Necrones traen sus impredecibles Cuchillas de la Necrópolis."/>
	<entry name="Jink" value="<string name='Actions/Jink'/>"/>
	<entry name="JinkDescription" value="Aumenta la reducción del daño a distancia pero reduce la puntería"/>
	<entry name="JinkFlavor" value="<string name='Actions/JinkFlavor'/>"/>
	<entry name="Killshot" value="Killshot"/>
	<entry name="KillshotDescription" value="Aumenta el daño contra criaturas monstruosas, vehículos y fortificaciones cuando se está adyacente a un Predator aliado."/>
	<entry name="KillshotFlavor" value="Hay pocas cosas que puedan resistir el poder de un escuadrón de asalto Predator—un trío de Predators—sus artilleros derriban hasta criaturas xenos gigantescas o máquinas de guerra en una lluvia de fuego de sus cañones."/>
	<entry name="Lance" value="Lanza"/>
	<entry name="LanceDescription" value="Disminuye el blindaje de la unidad objetivo."/>
	<entry name="LanceFlavor" value="El terror de los comandantes de tanques, un arma lanza que dispare un rayo concentrado de energía que puede traspasar el blindaje, independientemente de su grosor."/>
	<entry name="LargeBlast" value="Área grande"/>
	<entry name="LargeBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="LargeBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="LastStand" value="Resistencia final"/>
	<entry name="LastStandDescription" value="Aumenta la moral."/>
	<entry name="LastStandFlavor" value="“Aquí estamos y aquí moriremos, inquebrantables e incolumnes. ¡Aunque la muerte misma venga por nosotros, mantendremos nuestro desafío hasta el final!”<br/>  — Capellán de los Grifos Aullantes Armand Titus"/>
	<entry name="LifeDrain" value="Drenar vida"/>
	<entry name="LifeDrainDescription" value="Aumenta el daño contra infantería y criaturas monstruosas."/>
	<entry name="LifeSteal" value="Robavidas"/>
	<entry name="LifeStealDescription" value="Convierte el daño en curación para esta y unidades de infantería y criaturas monstruosas adyacentes."/>
	<entry name="LinebreakerBombardment" value="Bombardeo rompelíneas"/>
	<entry name="LinebreakerBombardmentDescription" value="Ignora la reducción de daño a distancia del objetivo cuando esté adyacente de una unidad Vindicator aliada."/>
	<entry name="Luminagen" value="Luminógeno"/>
	<entry name="LuminagenDescription" value="Reduce temporalmente la reducción de daño de la unidad objetivo."/>
	<entry name="LocatorBeacon" value="Baliza localizadora"/>
	<entry name="LocatorBeaconDescription" value="Causa que el despliegue orbital no consume puntos de acción cuando se despliegue adyacente a esta unidad."/>
	<entry name="LocatorBeaconFlavor" value="Las balizas localizadoras a menudo son llevados por Motoristas exploradores o en Cápsulas de desembarco y proporcionan un paquete de señalización, comunicadores de amplio espectro, y rastreador de posición geográfica. Cuando se activa, las balizas cargan información detallada de la posición en las casillas tácticas, permitiendo precisión en los refuerzos de las fuerzas de reserva."/>
	<entry name="LowPower" value="Bajo poder"/>
	<entry name="LowPowerDescription" value="Se activa después de moverse."/>
	<entry name="MachineEmpathy" value="Empatía mecánica"/>
	<entry name="MachineEmpathyDescription" value="Restaura puntos de vida."/>
	<entry name="MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="MannedWeapon" value="Arma tripulada"/>
	<entry name="MannedWeaponDescription" value="Requiere que la carga se dispare."/>
	<entry name="MassiveBlast" value="Explosión masiva"/>
	<entry name="MassiveBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="MassiveBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="MasterCrafted" value="Artesanal"/>
	<entry name="MasterCraftedDescription" value="Aumenta la precisión."/>
	<entry name="MasterCraftedFlavor" value="Algunas armas son artefactos mantenidos con amor, fabricados con una habilidad ya perdida. Aunque la forma exacta de artesanía varía, siempre se ha considerado el pináculo del arte del armero."/>
	<entry name="Melee" value="Combate de melé o cuerpo a cuerpo"/>
	<entry name="MeleeDescription" value="No vigilar."/>
	<entry name="MeleeFlavor" value="<string name='Weapons/MeleeFlavor'/>"/>
	<entry name="Melta" value="Fusión"/>
	<entry name="MeltaDescription" value="Aumenta la penetración en blindaje a media distancia."/>
	<entry name="MeltaFlavor" value="<string name='Weapons/MeltaFlavor'/>"/>
	<entry name="MindControl" value="Control mental"/>
	<entry name="MindControlDescription" value="Reduce la moral cada turno."/>
	<entry name="Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="MisfortuneDescription" value="Aumenta el daño recibido."/>
	<entry name="MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="Missing" value="Perdido"/>
	<entry name="MobRule" value="La ley de la peña"/>
	<entry name="MobRuleDescription" value="Reduce la perdida de moral en base a la cantidad de unidades aliadas en el área."/>
	<entry name="MobRuleFlavor" value="Los Orkos son simplistas, criaturas bestiales que adoran luchar y relucir su confianza al poseer fuerza en números."/>
	<entry name="MobileCommand" value="Mando móvil"/>
	<entry name="MobileCommandDescription" value="Las habilidades pasivas funcionan para la carga embarcada en este transporte."/>
	<entry name="MobileCommandFlavor" value="Un vehículo de transporte está normalmente sellado, dejando las tropas ineficaces (pero protegidas) hasta llegar a su destino. A diferencia de las hendiduras para las armas, los equipamientos de comunicaciones no compromente la protección, y la variante de Mando del Quimera Imperial es la prueba, permitiendo a los comandantes continuar dirigiendo a sus fuerzas."/>
	<entry name="Monofilament" value="Monofilamento"/>
	<entry name="MonofilamentDescription" value="Escala loa puntería con el movimiento máximo de la unidad objetivo. Aumenta la penetración de blindaje."/>
	<entry name="MonofilamentFlavor" value="<string name='Weapons/MonofilamentFlavor'/>"/>
	<entry name="MonstrousCreature" value="Criatura monstruosa"/>
	<entry name="MonstrousCreatureDescription" value="Clasificación."/>
 	<entry name="MonstrousCreatureFlavor" value="Estos son gigantes enormes capaces de aplastar un tanque – como el Tiránido Carnifex, una criatura biodiseñada y evolucionada para convertirse en un ariete vivo."/>
	<entry name="MoraleSoak" value="Llenarse de valor"/>
	<entry name="MoraleSoakDescription" value="Reduce el daño de la moral del objetivo."/>
	<entry name="MoveThroughCover" value="Mueve a través de cobertura"/>
	<entry name="MoveThroughCoverDescription" value="Niega la penalización de movimiento en bosques y ruinas imperiales."/>
	<entry name="MoveThroughCoverFlavor" value="Algunos guerreros tienen la habilidad de moverse sobre terreno destruido y enredado."/>
	<entry name="Necrons/AircraftBuildingBonus" value="Orphic Starkness"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Aumenta la generación de producción."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="Esta extensión inhumana de piedra parece ahora doblarse, ocupando más espacio del que debe. Las máquinas voladoras Necrón se estiran y deslizan dentro y fuera de la propia existencia. ."/>
	<entry name="Necrons/AttackCityBonus" value="Apuntado estático"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Aumenta la precisión contra unidades en ciudades."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="La tecnología de los Necrones pueden haber surgido de antiguas tumbas polvorientas, pero pocas razas en los últimos 60 millones de años han igualado su destreza en manipular variables dimensionales. Ningún muro, búnker o rejas láser es una defensa contra tales armas.."/>
	<entry name="Necrons/BlastDamage" value="Apuntado disperso"/>
	<entry name="Necrons/BlastDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Necrons/BlastDamageFlavor" value="Los sistemas de apuntado humanos tiende a enfocarse en la precisión, reduciendo la dispersión de los disparos de un arma. Contraintutivamente, los sistemas de apuntado avanzados de los Necrones incrementan la dispersión del ataque del arma, de alguna forma incrementando la probabilidad por cada disparo de acertar."/>
	<entry name="Necrons/BloodyCrusade" value="<string name='Actions/Necrons/BloodyCrusade'/>"/>
	<entry name="Necrons/BloodyCrusadeDescription" value="Aumenta el daño."/>
	<entry name="Necrons/CityTier2" value="Excavación preeliminar"/>
	<entry name="Necrons/CityTier2Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Necrons/CityTier2Flavor" value="Muchas de las antiguas ciudades han sido descubiertos por excavadores esclavos, quienes ahora han recuperado las construcciones largamente enterradas."/>
	<entry name="Necrons/CityTier3" value="Ciudad desenterrada"/>
	<entry name="Necrons/CityTier3Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Necrons/CityTier3Flavor" value="La totalidad de la enterrada necrópolis ha sido revelada, resplandeciente en su increíble horror.."/>
	<entry name="Necrons/ConstructionBuildingBonus" value="Esclavos mejorados"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Aumenta la generación de producción."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="Pequeñas modificaciones para los cuerpos y las mentes de tus esclavos les permite dormir menos y trabajar más, hasta que sus frágiles cuerpos aguanten."/>
	<entry name="Necrons/CtanNecrodermis" value="Necrodermis C'tan"/>
	<entry name="Necrons/CtanNecrodermisDescription" value="Aumenta la reducción del daño."/>
	<entry name="Necrons/CtanNecrodermisBlast" value="Explosión de Necrodermis C'tan"/>
	<entry name="Necrons/CtanNecrodermisBlastDescription" value="Daña unidades cercanas cuando muere."/>
	<entry name="Necrons/DefensiveProtocols" value="<string name='Actions/Necrons/DefensiveProtocols'/>"/>
	<entry name="Necrons/DefensiveProtocolsDescription" value="Aumenta el blindaje."/>
	<entry name="Necrons/DestructionProtocols" value="<string name='Actions/Necrons/DestructionProtocols'/>"/>
	<entry name="Necrons/DestructionProtocolsDescription" value="Aumenta el daño recibido."/>
	<entry name="Necrons/Dynasty" value="<string name='Actions/Necrons/Dynasty'/>"/>
	<entry name="Necrons/DynastyDescription" value="<string name='Actions/Necrons/DynastyDescription'/>"/>
	<entry name="Necrons/EnergyBuildingBonus" value="Empyreal Accumulators"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Acumulador empíreo."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="Poco ha cambiado en el exterior de este sarcófago barroco—aunque en su interior, dispositivos de Criptecnólogos oscuros están estabilizando otro dispositivo más avanzado."/>
	<entry name="Necrons/EntropicStrike" value="Golpe entrópico"/>
	<entry name="Necrons/EntropicStrikeDescription" value="Escala el daño mínimo con los HP de las unidad objetivo."/>
	<entry name="Necrons/EternityGate" value="Puerta de la Eternidad"/>
	<entry name="Necrons/EternityGateDescription" value="Unidades de Necrones pueden teleportarse a las casillas adyacentes."/>
	<entry name="Necrons/EternityGateFlavor" value="Un Monolito puerta de la eternidad es un corredor dimensional entre el campo de batalla y una tumba, que permite a legiones de guerreros Necron cruzar grandes distancias y entrar en la batalla con un solo paso."/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="Aumenta la reducción de daño por fuego brujo."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GravityPulse" value="<string name='Actions/Necrons/GravityPulse'/>"/>
	<entry name="Necrons/GravityPulseDescription" value="Reduce el movimiento y provoca daño a las unidades motorizadas, gravíticas y voladoras a su alcance."/>
	<entry name="Necrons/GrowthBonus" value="Rituales de reanimación"/>
	<entry name="Necrons/GrowthBonusDescription" value="Aumenta la tasa de crecimiento."/>
	<entry name="Necrons/GrowthBonusFlavor" value="Mediante protocolos más eficientes para la recuperación de los Necrones ya enterrados desde sus propias tumbas se reduce la pérdida de los excavadores esclavos."/>
	<entry name="Necrons/HardwiredForDestruction" value="Ciberdestrucción"/>
	<entry name="Necrons/HardwiredForDestructionDescription" value="Aumenta la precisión."/>
	<entry name="Necrons/HardwiredForDestructionFlavor" value="Los Necrones del Culto Destructor son recolectores de vivos alimentados por el odio, obsesionados con la erradicación de todos los seres sintientes. Encargan a los Crypteks modificar sus cuerpos para convertirse en seres optimizados. En Gladius Prime, los recientemente surgidos Destructores Skorpekh lo demuestran a la perfección, lanzando impactos brutalmente precisos con sus grandes espadas."/>
	<entry name="Necrons/HuntersFromHyperspace" value="<string name='Actions/Necrons/HuntersFromHyperspace'/>"/>
	<entry name="Necrons/HuntersFromHyperspaceDescription" value="<string name='Actions/Necrons/HuntersFromHyperspaceDescription'/>"/>
	<entry name="Necrons/HuntersFromHyperspaceFlavor" value="<string name='Actions/Necrons/HuntersFromHyperspaceFlavor'/>"/>	
	<entry name="Necrons/HousingBuildingBonus" value="Compresión del refugio"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Aumenta el límite de población."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="Mediante la alteración de las limitaciones dimensionales de cada refugio, se pueden acomodar a más Necrones para su reparación o mantenimiento."/>
	<entry name="Necrons/ImmuneToNaturalLaw" value="Inmune a la Ley Natural"/>
	<entry name="Necrons/ImmuneToNaturalLawDescription" value="La unidad puede moverse sobre el agua, a través de unidades enemigas e ignora la penalización ríos y alambre de espino."/>
	<entry name="Necrons/InfantryBuildingBonus" value="Refinamientos del núcleo"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Aumenta la generación de producción."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="Con mejores herramientas y menos restricciones, los Criptecnólogos y Canoteks pueden recuperar una gran parte de los Necrones enterrados, y más rápido."/>
	<entry name="Necrons/InfluenceBuildingBonus" value="Inscripciones encantadoras"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Aumenta la influencia."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="¡Ay de cualquier mortal que mire estas inscripciones!—Sus bordes filosos parecen vacilar por el rabillo del ojo, llevando el miedo a los corazones humanos."/>
	<entry name="Necrons/InvasionBeams" value="Corrientes de invasión"/>
	<entry name="Necrons/InvasionBeamsDescription" value="Desembarcar no consume movimiento."/>
	<entry name="Necrons/JetCharge" value="<string name='Actions/Necrons/JetCharge'/>"/>
	<entry name="Necrons/JetChargeDescription" value="Aumenta la reducción del daño."/>
	<entry name="Necrons/LivingMetal" value="Metal viviente"/>
	<entry name="Necrons/LivingMetalDescription" value="Restaura los puntos de vida cada turno."/>
	<entry name="Necrons/LivingMetalFlavor" value="Es comprensible que los Necrones construyan sus vehículos a partir del mismo metal viviente del que están hechos sus propios cuerpos, teniendo en cuenta lo duros que son y que así es más fácil reparar el daño recibido en el campo."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="Pacto barroco"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Aumenta la lealtad."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="La mayor parte del relato del santuario oculto ha sido revelada, aunque la historia sigue terminando antes de que el rey Silencioso pueda vengarse de los dioses estelares que despojaron a su pueblo de sus almas."/>
	<entry name="Necrons/MeleeDamage" value="Cuchillas necrodérmicas"/>
	<entry name="Necrons/MeleeDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Necrons/MeleeDamageFlavor" value="Fabricadas con pedazos del metal viviente que usaron los Necrones, estas cuchillas atraviesan casi cualquier piel, hueso y blindaje sin inmutarse."/>
	> <entry name="Necrons/NanoscarabReanimationBeam" value="Rayo de Reanimación Nanoscarab"/>
	<entry name="Necrons/NanoscarabReanimationBeamDescription" value="Restaura los puntos de vida."/>
	<entry name="Necrons/NanoscarabReanimationBeamFlavor" value="<string name='Actions/Necrons/NanoscarabReanimationBeamFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="Nebuloscope"/>
	<entry name="Necrons/NebuloscopeDescription" value="Ignora la reducción del daño a distancia del enemigo."/>
	<entry name="Necrons/NebuloscopeFlavor" value="Este dispositivo arcano permite al piloto de una Cuchilla de la Necrópolis rastrear a su víctima a través de diferentes dimensiones, dejándole sin sitio donde esconderse."/>
	<entry name="Necrons/OreBuildingBonus" value="Esclavos de confianza"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Aumenta el mineral."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="Tener un poco de fe en las jóvenes razas de esclavos de los Antiguos y mejorar su lote puede parecer una locura, pero aumenta la productividad."/>
	<entry name="Necrons/Chronometron" value="<string name='Actions/Necrons/Chronometron'/>"/>
	<entry name="Necrons/ChronometronDescription" value="Aumenta la reducción del daño."/>
	<entry name="Necrons/ChronometronFlavor" value="<string name='Actions/Necrons/ChronometronFlavor'/>"/>
	<entry name="Necrons/PhaseShiftGenerator" value="<string name='Actions/Necrons/PhaseShiftGenerator'/>"/>
	<entry name="Necrons/PhaseShiftGeneratorDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="Necrons/PhaseShiftGeneratorFlavor" value="<string name='Actions/Necrons/PhaseShiftGeneratorFlavor'/>"/><entry name="Necrons/QuantumShieldingDescription" value="Aumenta el blindaje."/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Aumenta la reducción del daño invulnerable."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/ReanimationProtocols" value="Protocolos de reanimación"/>
	<entry name="Necrons/ReanimationProtocolsDescription" value="Restaura puntos de vida cada turno."/>
	<entry name="Necrons/ReanimationProtocolsFlavor" value="Los Necrones tienen sofisticados sistemas de auto-reparación que permiten regresar a los guerreros gravemente dañados al combate."/>
	<entry name="Necrons/Reaper" value="<string name='Actions/Necrons/Reaper'/>"/>
	<entry name="Necrons/ReaperFlavor" value="<string name='Actions/Necrons/ReaperFlavor'/>"/>
	<entry name="Necrons/ResearchBuildingBonus" value="Criptecnólogos Datastyles"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Aumenta la generación de investigación."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="Soñando con nuevas tecnologías es lo natural en los Criptecnólogos—y con Datastyles pueden acceder a información compartida sin riesgos de conexión directa mente a mente."/>
	<entry name="Necrons/ShieldVane" value="Escudos aspa"/>
	<entry name="Necrons/ShieldVaneDescription" value="Aumenta el blindaje."/>
	<entry name="Necrons/ShieldVaneFlavor" value="Las Cuchillas de la Necrópolis son desplegadas directamente en medio de las defensas de un mundo equipado normalmente con paneles blindados adicionales."/>
	<entry name="Necrons/SleepingSentry" value="<string name='Actions/Necrons/SleepingSentry'/>"/>
	<entry name="Necrons/SleepingSentryDescription" value="Aumenta la reducción del daño aunque impide que la unidad pueda moverse o realizar una acción."/>
	<entry name="Necrons/TargetRelayed" value="Objetivo retransmitido"/>
	<entry name="Necrons/TargetRelayedDescription" value="Aumenta la puntería a distancia de los necrones contra la unidad objetivo."/>
	<entry name="Necrons/TargetRelayedFlavor" value="<string name='Traits/Necrons/TargetingRelayFlavor'/>"/>
	<entry name="Necrons/TargetingRelay" value="Revelador de objetivos"/>
	<entry name="Necrons/TargetingRelayDescription" value="Aumenta temporalmente la precisión a distancia de los necrones contra la unidad objetivo."/>
	<entry name="Necrons/TargetingRelayFlavor" value="Los Acechantes de la Triarca pueden parecer frágiles, pero su único Escudo Cuántico los mantiene en una pieza, para que su revelador de objetivos pueda amplificar la potencia de fuego de los necrones cercanos."/>
	<entry name="Necrons/Technomancer" value="Tecnomante"/>
	<entry name="Necrons/TechnomancerDescription" value="Aumenta la reducción del daño"/>
	<entry name="Necrons/VehiclesBuildingBonus" value="Trabajadores canópticos"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Aumenta la generación de producción."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="Asignando constructores canópticos para mantener y reparar las durmientes máquinas de guerras, acelerando su restauración."/>
	<entry name="Necrons/VengeanceOfTheEnchained" value="Venganza del encadenado"/>
	<entry name="Necrons/VengeanceOfTheEnchainedDescription" value="Explosión de Muerte causa daño extra."/>
	<entry name="Necrons/WraithForm" value="Forma espectral"/>
	<entry name="Necrons/WraithFormDescription" value="Aumenta la reducción al daño invulnerable."/>
	<entry name="Necrons/WraithFormFlavor" value="Como dispositivo canóptico, el Espectro estaba destinado a mantener a los Necrones durmientes durante los milenios que durmieron y era capaz de desestabilizarse dimensionalmente para reparar los componentes internos que fallaban. Ahora que sus cargas se han despertado, utiliza la misma tecnología para revolotear por el campo de batalla mientras las municiones enemigas pasan inofensivamente a través de él."/>
	<entry name="Necrons/Wraithflight" value="Vuelo espectral"/>
	<entry name="Necrons/WraithflightDescription" value="<string name='Traits/Necrons/ImmuneToNaturalLawDescription'/>"/>
	<entry name="Necrons/WraithflightFlavor" value="Su matriz de desestabilización dimensional no sólo hace que los Espectros sean casi imposibles de matar, sino que también les permite atravesar cualquier obstáculo, ya sean ríos, alambre de espinos o incluso guerreros enemigos."/>
	<entry name="Necrons/WrithingWorldscape" value="<string name='Actions/Necrons/WrithingWorldscape'/>"/>
	<entry name="NoEscape" value="Sin escapatoria"/>
	<entry name="NoEscapeDescription" value="Aumenta el ataque contra unidades sin techo."/>
	<entry name="OpenTopped" value="Descubierto"/>
	<entry name="OpenToppedDescription" value="Clasificación."/>
	<entry name="OpenToppedFlavor" value="Algunos vehículos tienen poca armadura, haciéndolos mas vulnerables al daño por su construcción ligera. Sin embargo, estos vehículos son excelentes para el transporte de asalto, ya que sus pasajeros pueden desembarcar con mucha más facilidad"/>
	<entry name="OrationOfRestoration" value="Oración de Restauración"/>
	<entry name="OrationOfRestorationDescription" value="Restaura puntos de vida."/>
	<entry name="Ordnance" value="Artillería"/>
	<entry name="OrdnanceDescription" value="Aumenta la penetración en armadura contra vehículos. No puede usarse en infantería después de moverse."/>
	<entry name="OrkoidFungus" value="Hongos Orkos"/>
	<entry name="OrkoidFungusDescription" value="Restaura puntos de vida cada turno para unidades Orkas de tierra."/>
	<entry name="OrkoidFungusFlavor" value="Los orkos mantienen una relación simbiótica con los hongos que proliferan en sus planetas—son genéticamente iguales a ellos, Squigs y Grots, y todas estas especies tienen a surgir de ellos. Las unidades orkas desplegadas en combate son propensas a retirarse a estos campos para adquirir, literalmente, reclutas frescos."/>
	<entry name="OrkoidFungusBonusHealingRate" value="Bosque de hongos"/>
	<entry name="OrkoidFungusBonusHealingRateDescription" value="Restaura puntos de vida cada turno para las unidades Orkas de tierra."/>
	<entry name="OrkoidFungusBonusHealingRateFlavor" value="Una vez que los hongos orkos están bien establecidos por todo un planeta, pueden llegar a formar bosques—aunque, repleto de hongos inmensos, expulsando orkos bárbaros, Kanijos y Garrapatos."/>
	<entry name="OrkoidFungusFood" value="Brote Mushling"/>
	<entry name="OrkoidFungusFoodDescription" value="Aumenta la generación de comida."/>
	<entry name="OrkoidFungusFoodFlavor" value="Los pequeños Snotlings, como todo orkoide, son derivados de la misma estructura genética fúngica. Aunque a diferencia de otros orkoides, los Snotlings más perezosos pueden volver a la senda de los hongos y revertirse hasta convertirse en una gigantesca criatura mediohongo, una exquisitez para sus camaradas orkos."/>
	<entry name="Orks/BeastSnagga" value="Beast Snagga"/>
	<entry name="Orks/BeastSnaggaDescription" value="Aumenta la precisión contra vehículos y criaturas monstruosas, y aumenta la reducción de daño invulnerable."/>
	<entry name="Orks/BeastSnaggaFlavor" value="Al detectar las amenazas más grandes o peligrosas en el campo de batalla, además de los propios Orkos, los Beast Snaggas los persiguen con entusiasmo jubiloso."/>
	<entry name="Orks/BigBoss" value="<string name='Actions/Orks/BigBoss'/>"/>
	<entry name="Orks/BigBossDescription" value="Aumenta los puntos de vida."/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="<string name='Traits/AstraMilitarum/BlastDamageDescription'/>"/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="Boltz máz grandez"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Traits/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="Como la munición Kraken de los Marines Espaciales, esta modificación permite a este Akribillador munición de mayor calibre, permitiendo que el afortunado receptor se muestre con una arma mayor y que hace aún más daño."/>
	<entry name="Orks/BonusBeastsProduction" value="Látigos máz grandez"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="Aumenta la generación de producción de Runtherd Groundz."/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="“Vamos a necesitar un látigo más grande.”<br/>  — Runtherd Gark Snotsmeer, poco antes que un garrapato le pisara"/>
	<entry name="Orks/BonusColonizersProduction" value="Martillos de Chatarra"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="Aumenta la producción de Mek Bitz Yards."/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="Cuando no hay suficiente chatarra de menor tamaño para fabricar creaciones toscas, los Chapuzaz adquieren más inventando máquinas extrañas y maravillosas que destruyen ruinas y escombros de sus mundos conquistados."/>
	<entry name="Orks/BonusInfantryProduction" value="Máz dakka"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="Aumenta la generación de producción en Pile O' Dakkas."/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="“Da best shoota I eva made, dat iz. Loadza barrulz, so dat it's ded shooty. 'Sept dat wun, 'cos dat's da skorcha, dat's burny insted. Yeah, good an' propa. An' da bullitz is 'splosiv…dey goez boom inna fings wot you'z shootin.' An' dat button dere…dat's da best bit. Wot it duz, see, iz…iz…oh, zog. Nah, it's nuffin' boss. Nah, you'z don't need ta see wot dat button duz…'onist. Don't push it!”<br/>  — Last words, Nazdakka Boomsnik, Mek"/>
	<entry name="Orks/BonusVehiclesProduction" value="Chapuzaz"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="Aumenta la generación de producción de Kults ov Speed."/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="La mayoría de la tecnología orka no debería funcionar—suelen faltar componentes vitales o el artilugio es simplemente imposible. La actitud decidida de los orkos—enfocado en forma de energía ¡Waaagh! que les rodea—llena los vacíos. Es la razón por la que acelerar la producción de vehículos y armamento orko no tiene efecto en su calidad…"/>
	<entry name="Orks/BustHeads" value="<string name='Actions/Orks/BustHeads'/>"/>
	<entry name="Orks/BustHeadsDescription" value="Reduce la pérdida de moral."/>
	<entry name="Orks/BustHeadsFlavor" value="<string name='Actions/Orks/BustHeadsFlavor'/>"/>
	<entry name="Orks/ChannelMentalEmissions" value="<string name='Actions/Orks/ChannelMentalEmissions'/>"/>
	<entry name="Orks/ChannelMentalEmissionsDescription" value="Aumenta la generación de investigación."/>
	<entry name="Orks/CityEnergy" value="Kraklin Gubbinz"/>
	<entry name="Orks/CityEnergyDescription" value="Aumenta la generación de energía."/>
	<entry name="Orks/CityEnergyFlavor" value="Arcos de rayos verdes y rojos entre las intrincadas puntas de estos dispositivos de amplificación de potencia, friendo a cualquier pielverde que se acerque demasiado. Es un deporte para espectadores, pero a la tecnología le faltan componentes clave, por lo que no podría confirmar las afirmaciones de los mekánikos sobre el aumento de la producción de energía—salvo por la fe de los orkos en la realidad alterada que lo hace."/>
	<entry name="Orks/CityGrowth" value="Altavoces del Jefe"/>
	<entry name="Orks/CityGrowthDescription" value="Aumenta la tasa de crecimiento de la Kabaña de Chatarra."/>
	<entry name="Orks/CityGrowthFlavor" value="“Naw, No kiero tweeterz zogging. Kiero gorkabuferz… ¡ponlo al máximp!”<br/>  — Merfnik Orejas Sangrantes, Señor de la Guerra Goff y Rockero"/>
	<entry name="Orks/CityInfluence" value="Brillantes Grandes"/>
	<entry name="Orks/CityInfluenceDescription" value="Aumenta la generación de influencia."/>
	<entry name="Orks/CityInfluenceFlavor" value="“No zé ké zon, pero loz kiero.”<br/>  — Señor de la Guerra Flashgrub Nitwiz, observando las puertas de oro batido de la Basílica Imperialis en Catiline VII"/>
	<entry name="Orks/CityLoyalty" value="Palos de Trofeo"/>
	<entry name="Orks/CityLoyaltyDescription" value="Aumenta la generación de lealtad."/>
	<entry name="Orks/CityLoyaltyFlavor" value="No hay nada que eleve el vigor de un orko como la vista de sus enemigos (o sus partes restantes) colgado en lo alto para que todos lo vean."/>
	<entry name="Orks/CityPopulationLimit" value="Excavaciones Profundas"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Aumenta el límite de población."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="Usando los últimos artilugios del Chapuza, las chozas y forjas orkas pueden extenderse en las profundidades de la ciudad. Por supuesto, el colapso eventual arrastra a los orkos gritando hasta las profundidades, pero vale la pena por el espacio extra."/>
	<entry name="Orks/CityResearch" value="Habitaciones de trastos"/>
	<entry name="Orks/CityResearchDescription" value="Aumenta la generación de investigación."/>
	<entry name="Orks/CityResearchFlavor" value="Dado que la tecnología está conectada a los genes de los orkos normales, un Kaudillo inteligente se dará cuenta que dedicar espacio, mano de obra y tiempo para encerrar a sus Chapuzaz dentro de una pequeña habitación, repleta de chatarra, y ordenarles que obtengan un buenos Dakkas siempre da sus frutos."/>
	<entry name="Orks/CityTier2" value="Suburbios"/>
	<entry name="Orks/CityTier2Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Orks/CityTier2Flavor" value="A las afueras de cada fortaleza, ha surgido un suburbio de Gretchin, Garrapatos, Snotlings y orkos de baja categoría, mientras que los pérfidos hongos verdes se esparcen por todas partes, rehaciendo este mundo en la imagen genética de los orkos."/>
	<entry name="Orks/CityTier3" value="Chozas"/>
	<entry name="Orks/CityTier3Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Orks/CityTier3Flavor" value="La fama del Kaudillo se ha extendido por todo el planeta, con las energías invisibles del ¡Waaagh! recorriendo su paso. Donde sea que los orkos hayan surgido recientemente, sienten la irresistible llama de las ciudades del Kaudillo, que ahora están esparcidas por el paisaje en interminables suburbios y chozas."/>
	<entry name="Orks/CreateOrkoidFungusOnDeath" value="Descomposición rápida"/>
	<entry name="Orks/CreateOrkoidFungusOnDeathDescription" value="Permite a las unidades crear hongos orkos cuando mueren."/>
	<entry name="Orks/CreateOrkoidFungusOnDeathFlavor" value="“Figura 27: una sola 'espora' de la especie xenos #0451 'Orko'. Acólito, debes tener en cuenta la textura fungoide y las estructuras microhomunculares. Creemos que estas 'esporas' generan nuevos 'Orkos' en cualquier entorno suficientemente fértil. Cuando un Orko alcanza la madurez (normalmente 30 minutos después de la emergencia) emite estos constantemente—pero se liberan en masa en el momento de la muerte. No lo dejes caer.”<br/>  — Transcripciones de la conferencia, Grigomen “AmantedeOrkos” Delr, Comerciante poco honesto y Xenólogo amateur"/>
	<entry name="Orks/CyborkImplants" value="<string name='Actions/Orks/CyborkImplants'/>"/>
	<entry name="Orks/CyborkImplantsDescription" value="Aumenta el daño y la reducción del daño."/>
	<entry name="Orks/ExtraBitz" value="<string name='Actions/Orks/ExtraBitz'/>"/>
	<entry name="Orks/ExtraBitzDescription" value="Reduce la manutención de comida, mineral y energía."/>
	<entry name="Orks/ExperimentalProcedure" value="<string name='Actions/Orks/ExperimentalProcedure'/>"/>
	<entry name="Orks/ExperimentalProcedureDescription" value="Aumenta el daño y la reducción del daño."/>
	<entry name="Orks/Flyboss" value="Zuperartillero"/>
	<entry name="Orks/FlybossDescription" value="Aumenta la precisión a distancia contra voladores, motos a reacción y gravíticos."/>
	<entry name="Orks/FlybossFlavor" value="Los Zuperartilleroz son pilotos expertos que han sobrevivido a más combates aéreos de los que pueden contar (incluso usando los dedos de sus pies)."/>
	<entry name="Orks/Gitfinda" value="Gitfinda"/>
	<entry name="Orks/GitfindaDescription" value="Aumenta la puntaría a distancia si la unidad permanece sin moverse."/>
	<entry name="Orks/GitfindaFlavor" value="Estos pueden ser biónicos oculares muy trabajados, cascos de comunicaciones monoculares, grandes telescopios o sabe Morko qué más. La función de estos aparatos es mejorar la precisión del usuario a niveles normales."/>
	<entry name="Orks/GreenTide" value="Marea Verde"/>
	<entry name="Orks/GreenTideFlavor" value="Los orkos son muy difíciles de matar. Los que aparentemente mueren en el campo de batalla regresan a menudo en la siguiente, ya sean arreglados por un Matazanoz Loko o curados milagrosamente—más fuertes y resistentes por la experiencia."/>
	<entry name="Orks/GreenTideGrowth" value="<string name='Traits/Orks/GreenTide'/>"/>
	<entry name="Orks/GreenTideGrowthDescription" value="Aumenta la tasa de crecimiento."/>
	<entry name="Orks/GreenTideGrowthFlavor" value="<string name='Traits/Orks/GreenTideFlavor'/>"/>
	<entry name="Orks/GreenTideHealing" value="<string name='Traits/Orks/GreenTide'/>"/>
	<entry name="Orks/GreenTideHealingDescription" value="Aumenta la tasa de curación."/>
	<entry name="Orks/GreenTideHealingFlavor" value="<string name='Traits/Orks/GreenTideFlavor'/>"/>
	<entry name="Orks/GrotGunner" value="Kanijo artillero"/>
	<entry name="Orks/GrotGunnerDescription" value="Aumenta la precisión de los Akribilladorez Pezadoz y los Akribilladorez Pezadoz Akopladoz."/>
	<entry name="Orks/KustomForceField" value="<string name='Actions/Orks/KustomForceField'/>"/>
	<entry name="Orks/KustomForceFieldDescription" value="Aumenta la reducción de daño por ataque a distancia."/>
	<entry name="Orks/KustomForceFieldFlavor" value="<string name='Actions/Orks/KustomForceFieldFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="Rebanadoras más grandes"/>
	<entry name="Orks/MeleeDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Orks/MeleeDamageFlavor" value="Cuando los orkos piensan en mejoras, no piensan demasiado. Mega-armaduras, Giga-akribilladores… y rebanadoras más grandes. Si puedes llevarlo, puedes usarlo, y si puedes usarlo, puedes golpear a alguien con él."/>
	<entry name="Orks/MightMakesRight" value="La Fuerza da la Razón"/>
	<entry name="Orks/MightMakesRightDescription" value="Recibe influencia al atacar."/>
	<entry name="Orks/MightMakesRightFlavor" value="Los orkos solo se sienten vivos cuando están en un ¡Waaagh! y un ¡Waaagh! solo puedes prosperar a través del combate constante. Mientras los orkos estén luchando, el ¡Waaagh! sigue creciendo."/>
	<entry name="Orks/ProphetOfTheWaaagh" value="<string name='Actions/Orks/ProphetOfTheWaaagh'/>"/>
	<entry name="Orks/ProphetOfTheWaaaghDescription" value="Concede influencia basado en el mantenimiento cuando ataca."/>
	<entry name="Orks/Scavenger" value="Scavenger"/>
	<entry name="Orks/ScavengerDescription" value="Concede mineral al matar un enemigo."/>
	<entry name="Orks/ScavengerFlavor" value="Los orkos no son conocidos por su amor al trabajo, prefieren sentarse entra batallas sacando trozos de garrapatos de sus bocas y luchando entre si. Así que en lugar de extraer minerales del suelo, tienden a saquearlo de, bueno, alguna parte. O más bien lo hacen sus subordinados Kanijoz."/>
	<entry name="Orks/WarbikeTurboBoost" value="<string name='Actions/Orks/WarbikeTurboBoost'/>"/>
	<entry name="Orks/WarbikeTurboBoostDescription" value="<string name='Actions/Orks/WarbikeTurboBoostDescription'/>"/>
	<entry name="Orks/WarbikeTurboBoostFlavor" value="<string name='Actions/Orks/WarbikeTurboBoostFlavor'/>"/>
	<entry name="Orks/Warpath" value="<string name='Actions/Orks/Warpath'/>"/>
	<entry name="Orks/WarpathDescription" value="<string name='Actions/Orks/WarpathDescription'/>"/>
	<entry name="Orks/WarpathFlavor" value="<string name='Actions/Orks/WarpathFlavor'/>"/>
	<entry name="Orks/WingMissiles" value="<string name='Weapons/WingMissiles'/>"/>
	<entry name="Orks/WingMissilesDescription" value="Aumenta la precisión contra vehículos."/>
	<entry name="Outpost" value="Puesto Avanzado"/>
	<entry name="OutpostDescription" value="Aumenta reducción del daño y aumenta la tasa de curación para unidades aliadas. Aumenta la reducción del daño a distancia a las unidades de infantería."/>
	<entry name="Outflank" value="Flanquear"/>
	<entry name="OutflankDescription" value="Aumenta la precisión cuando está adyacente a una unidad aliada."/>
	<entry name="OutflankFlavor" value="La mejor forma de sorprender a un enemigo es golpear por donde menos lo espera."/>
	<entry name="Pinned" value="Suprimidos"/>
	<entry name="PinnedDescription" value="Reduce el movimiento y la precisión a distancia, aumenta la reducción del daño a distancia e impide que la unidad realice ataques de respuesta."/>
	<entry name="PinnedFlavor" value="Estar bajo fuego enemigo sin saber de dónde vienen los disparos, o tener una lluvia de proyectiles encima, puede reducir la moral de incluso el soldado más valiente, que se arrojará sobre cualquier cobertura que pueda encontrar."/>
	<entry name="Pinning" value="Supresión"/>
	<entry name="PinningDescription" value="Temporalmente reduce el movimiento de la unidad objetivo."/>
	<entry name="PinningFlavor" value="<string name='Traits/PinnedFlavor'/>"/>
	<entry name="Poisoned" value="Envenenado"/>
	<entry name="PoisonedDescription" value="Aumenta el daño contra la infantería y criaturas monstruosas."/>
	<entry name="PoisonedFlavor" value="Hay muchos venenos virulentos y letales en el futuro oscuro. Es muy sencillo adaptar tales toxinas para el uso en el campo de batalla. No importa si cubren la hoja de arma o la munición, o si son segregadas por monstruosidades extraterrestres, todas son letales."/>
	<entry name="PowerOfTheMachineSpirit" value="<string name='Actions/PowerOfTheMachineSpirit'/>"/>
	<entry name="PowerOfTheMachineSpiritDescription" value="<string name='Actions/PowerOfTheMachineSpiritDescription'/>"/>
	<entry name="PowerOfTheMachineSpiritFlavor" value="<string name='Actions/PowerOfTheMachineSpiritFlavor'/>"/>
	<entry name="PrecisionShots" value="Disparos de precisión"/>
	<entry name="PrecisionShotsDescription" value="Aumenta la puntería."/>
	<entry name="PreferredEnemy" value="Enemigo Predilecto"/>
	<entry name="PreferredEnemyDescription" value="Aumenta la precisión."/>
	<entry name="PreferredEnemyFlavor" value="Muchos de los guerreros de la galaxia entrenan duro para vencer a un enemigo en particular, permitiéndoles predecir los movimientos del enemigo y así, lanzar un disparo o ataque más efectivo."/>
	<entry name="PrimaryWeapon" value="Arma Primaria"/>
	<entry name="PrimaryWeaponDescription" value="Aumenta la penetración en armadura."/>
	<entry name="PsychicBlock" value="Bloqueo Psíquico"/>
	<entry name="PsychicBlockDescription" value="Bloquea daño y efectos del próximo ataque psíquico."/>
	<entry name="PsychicHood" value="Capucha Psíquica"/>
	<entry name="PsychicHoodDescription" value="Aumenta la reducción de daño por fuego brujo."/>
	<entry name="PsychicLash" value="Azote Psíquico"/>
	<entry name="PsychicLashDescription" value="Los ataques penetran todas las armaduras."/>
	<entry name="PsychicPower" value="Poder Psíquico"/>
	<entry name="PsychicPowerDescription" value="Clasificación."/>
	<entry name="PsychneueinInfest" value="Infestación Psychneuein"/>
	<entry name="PsychneueinInfestDescription" value="Permite a la unidad objetivo generar un Psychneuein cuando muera."/>
	<entry name="PsychneueinInfestation" value="Infestación Psychneuein"/>
	<entry name="PsychneueinInfestationDescription" value="Genera un Psychneuein al morir."/>
	<entry name="Psyker" value="Psíquicos"/>
	<entry name="PsykerDescription" value="Clasificación."/>
	<entry name="PsykerFlavor" value="Los psíquicos son místicos en los campos de batalla que pueden controlar los poderes de la Disformidad."/>
	<entry name="Rage" value="Furia"/>
	<entry name="RageDescription" value="Aumenta el ataque."/>
	<entry name="RageFlavor" value="La sed de sangre es una poderosa arma en el campo de batalla, incitando al guerrero a hacer pedazos a sus enemigos en un sinsentido (pero satisfactorio) frenesí."/>
	<entry name="Rampage" value="Devastación"/>
	<entry name="RampageDescription" value="Aumenta los ataques cuando hay más enemigos que aliados en celdas adyacentes."/>
	<entry name="RampageFlavor" value="Para algunos guerreros, ser superados en número no es causa de desesperación, sino una llamada para atacar a sus enemigos con un contraataque frenético."/>
	<entry name="RapidFire" value="Fuego Rápido"/>
	<entry name="RapidFireDescription" value="Dobla los ataques a medio alcance."/>
	<entry name="RecoveryGear" value="Recuperación de Equipo"/>
	<entry name="RecoveryGearDescription" value="Aumenta la tasa de curación."/>
	<entry name="RecoveryGearFlavor" value="Muchas tripulaciones cargan sus vehículos con una buena cantidad de herramientas, cables de remolcado y otros kits útiles que pueden marcar la diferencia entre sacar un vehículo inmovilizado o abandonarlo a su suerte."/>
	<entry name="RedPaintJob" value="Kapa de rojo korre máz"/>
	<entry name="RedPaintJobDescription" value="Aumenta el daño."/>
	<entry name="RedPaintJobFlavor" value="Los orkos creen que un vehículo pintado de rojo puede superar un vehículo similar que no lo está. Por extraño que parezca, no están equivocados."/>
	<entry name="RefractorField" value="Campo Refractor"/>
	<entry name="RefractorFieldDescription" value="Aumenta la reducción del daño."/>
	<entry name="RefractorFieldFlavor" value="Llevado a menudo por oficiales de alto rango y héroes imperiales, estos brillantes campos refractan los ataques de energía sobre su portador, apartando explosiones y ataques de espadas que de otro modo acabarían con él."/>
	<entry name="Regeneration" value="Regeneración"/>
	<entry name="RegenerationDescription" value="Restaura puntos de vida cada turno."/>
	<entry name="RegenerationFlavor" value="Algunas unidades pueden recuperarse de horrendas heridas y lesiones que deberían haberles matado."/>
	<entry name="Relentless" value="Implacable"/>
	<entry name="RelentlessDescription" value="Anula la penalización por armas pesadas, artillería y salvas."/>
	<entry name="RelentlessFlavor" value="Los guerreros implacables tienen brazos fuertes—nada puede detener su avance."/>
	<entry name="RelicPlating" value="Blindaje Reliquia"/>
	<entry name="RelicPlatingDescription" value="Aumenta la reducción de daño por fuego brujo."/>
	<entry name="RelicPlatingFlavor" value="Ocasionalmente, una tripulación logrará una unión empática con el espíritu máquina de su tanque. Cuando una tripulación como esta muere, sus restos suelen ser enterrados dentro de su vehículo, los espíritus permanecen protectores para rechazar los siniestras energías del vacío."/>
	<entry name="Rending" value="Acerada"/>
	<entry name="RendingDescription" value="Aumenta el daño y la penetración de blindaje."/>
	<entry name="RendingFlavor" value="Algunas armas pueden infligir golpes críticos contra los cuales ninguna armadura puede proteger."/>
	<entry name="RepulsorGrid" value="Red Repulsor"/>
	<entry name="RepulsorGridDescription" value="Aumenta la reducción del daño a distancia y devuelve del daño a distancia de las armas que no son blaters, de plantilla o fuego brujo al atacante."/>
	<entry name="RitesOfWar" value="Ritos de Guerra"/>
	<entry name="RitesOfWarDescription" value="Incremento del daño."/>
	<entry name="RitesOfWarFlavor" value="<string name='Actions/RitesOfWarFlavor'/>"/>
	<entry name="Rosarius" value="Rosarius"/>
	<entry name="RosariusDescription" value="Aumenta la reducción del daño."/>
	<entry name="RosariusFlavor" value="Los Capellanes de los Marines Espaciales llevan rosario como protección y símbolo de su oficio. Emite un campo de energía alrededor de su portador capaz de desviar golpes y disparos que destrozarían un búnker de ferrocemento. Se dice que cuanto más fuerte es la creencia del portador en el poder del Emperador, más fuerte será el campo de fuerza de un rosario."/>
	<entry name="RuinsStealth" value="Sigilo en Ruinas"/>
	<entry name="RuinsStealthDescription" value="Aumenta la reducción de daño a distancia cuando se está en ruinas imperiales."/>
	<entry name="Salvo" value="Salvo"/>
	<entry name="SalvoDescription" value="Media los ataques y alcance si la unidad se ha movido."/>
	<entry name="SavantInterlocution" value="Interlocución Erudita"/>
	<entry name="SavantInterlocutionDescription" value="Aumenta la precisión contra aeronaves y gravíticos cuando haya un Cazador adyacente."/>
	<entry name="SavantLock" value="Rastreo de Blanco"/>
	<entry name="SavantLockDescription" value="Aumenta la precisión contra aeronaves, moto a reacción y gravíticos."/>
	<entry name="SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="SeekerMissileDescription" value="Alcance reducido contra objetivos no captados por el marcador. No requiere línea de visión contra objetivos captados por el marcador."/>
	<entry name="SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Seeking" value="Buscadores"/>
	<entry name="SeekingDescription" value="Aumenta la precisión contra voladores."/>
	<entry name="Shaken" value="Conmocionado"/>
	<entry name="ShakenDescription" value="Reduce la precisión y aumenta el daño sufrido."/>
	<entry name="Shielded" value="Escudado"/>
	<entry name="ShieldedDescription" value="Aumenta la reducción de daño."/>
	<entry name="ShieldedFlavor" value="Algunos guerreros están protegidos por algo más que meras armaduras físicas. Pueden escudarse en campos de fuerza, envueltos en energías místicas o tener una constitución tal que pueden ignorar impactos que podrían penetrar tanques de batalla."/>
	<entry name="Shop" value="Tienda"/>
	<entry name="ShopDescription" value="Permite a las unidades héroe comprar y vender objetos."/>
	<entry name="Shred" value="Despedazar"/>
 	<entry name="ShredDescription" value="Aumenta el daño."/>
 	<entry name="ShredFlavor" value="Algunas armas y guerreros golpean en una ráfaga de golpes, desgarrando la carne en una serie de brutales ataques."/>
	<entry name="Shrouded" value="Oculto"/>
	<entry name="ShroudedDescription" value="Aumenta la reducción del daño a distancia."/>
	<entry name="ShroudedFlavor" value="La fuente de la oscuridad alrededor de estos guerreros no importa—solo un golpe de suerte tiene alguna posibilidad de perforar el velo que los oculta."/>
	<entry name="SiegeMasters" value="Maestros del Asedio"/>
	<entry name="SiegeMastersDescription" value="Aumenta el daño contra unidades enemigas en ciudades o fortificaciones."/>
	<entry name="SiegeShield" value="Escudo de Asedio"/>
	<entry name="SiegeShieldDescription" value="Niega la penalización por movimiento a través de terreno difícil."/>
	<entry name="SiegeShieldFlavor" value="Muchos Vindicators están equipados con enormes palas bulldozer, permitiéndoles apartar desechos del campo de batalla sin riesgos."/>
	<entry name="Signum" value="Signum"/>
	<entry name="SignumDescription" value="Aumenta la precisión a distancia."/>
	<entry name="SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SistersOfBattle/ActOfFaith" value="Acto de Fe"/>
	<entry name="SistersOfBattle/ActOfFaithDescription" value="Puede ejecutar Actos de Fe si la unidad no se encuentra rota o acobardada."/>
	<entry name="SistersOfBattle/ActOfFaithFlavor" value="Las Adepta Sororitas pueden aprovechar la fuente de su fe y pedir al Emperador que guíe sus acciones. Por lo que la absoluta creencia en el Credo Imperial permite a las Hermanas de Batalla ejecutar acciones que parecen imposibles en el campo de batalla. Pero no se puede confiar en los milagros como algo rutinario. En el corazón del Credo Imperial está la creencia de que el divino Emperador confía en que sus seguidores crearán su propia salvación, pero también que, si la situación es lo bastante difícil, Él intervendrá en apoyo de sus verdaderos siervos."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="Sarcófago Anacoreta"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Aumenta el blindaje."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="Para aquellas Arrepentidas que no sólo huyeron sino que traicionaron a sus Hermanas en combate, les espera un destino aún peor. Tras ser conectadas al crisol de sus Mortificadoras, se les entierra aún más tras gruesas paredes de adamantium. Este sarcófago protege a sus torturados cuerpos del fuego enemigo y de las hojas blandidas con desesperación, evitándoles el alivio de la muerte."/>
	<entry name="SistersOfBattle/AngelicVisage" value="Faz Angelical"/>
	<entry name="SistersOfBattle/AngelicVisageDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/AngelicVisageFlavor" value="Sonriendo beatíficamente, las Hermanas Céfiro esquivan los ataques de sus enemigos con fluida gracia antes de ejecutar su propio ataque, típicamente un tiro en la cabeza con su bólter o un tajo de su espada de energía."/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemed" value="Angustia de los No Redimidos"/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemedDescription" value="Daña al atacante cuerpo a cuerpo tras la muerte."/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemedFlavor" value="Las Mortificadoras abren un camino de destrucción con sus lanzallamas antes de cargar de cabeza contra sus enemigos, alimentadas por la culpa y el dolor, despreciando el peligro. Incluso al morir, atacan a sus enemigos, buscando la redención en cada momento."/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherine" value="Armadura de Santa Katherine"/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherineDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherineFlavor" value="Desde que se ungió a esta reverenciada armadura con un vial de sangre de Santa Katherine, se ha creído que tiene poderes sagrados de protección."/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="Descarga Furiosa"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="“El ataque correcto, cuidadosamente emplazado, puede derrotar al más poderoso de los enemigos. ¿Qué pasa, sin embargo, si no conocemos el ataque correcto? Pues que un asalto, abrumador y rudimentario, alcanzará el lugar justo, eventualmente…”<br/>  — Canonesa Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/AvengingZeal" value="Celo Vengador"/>
	<entry name="SistersOfBattle/AvengingZealDescription" value="Reduce la pérdida de moral."/>
	<entry name="SistersOfBattle/AvengingZealFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/BerserkKillingMachine" value="Máquina Asesina Bersérker"/>
	<entry name="SistersOfBattle/BerserkKillingMachineDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/BerserkKillingMachineFlavor" value="Atormentada por grupos de neuro-agonizadores que amplifican su autodesprecio y separada de las oraciones de sus Hermanas gracias a una pantalla acústica sobre su cabeza, la agonía espiritual de la desafortunada piloto alimenta al Mortificador. Asaltan a sus enemigos como aterradoras tropas de choque, antes de estrellarse contra ellos."/>
	<entry name="SistersOfBattle/BloodyResolution" value="Resolución Ensangrentada"/>
	<entry name="SistersOfBattle/BloodyResolutionDescription" value="Reduce la pérdida de moral."/>
	<entry name="SistersOfBattle/BloodyResolutionFlavor" value="<string name='Traits/SistersOfBattle/MartyrdomFlavor'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnance" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnance'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnanceDescription" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnanceDescription'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnanceFlavor" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnanceFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="Reclutamiento de Novicias"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Aumenta la tasa de crecimiento."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="La invasión Orka y subsiguiente guerra en Gladius Prime dejó muchos, muchos huérfanos. Enviar misionarios y predicadores a las tierras baldías para reclutar ejércitos para el Imperio aumentará el crecimiento de tus ciudades."/>
	<entry name="SistersOfBattle/CityTier2" value="Anexos de la Preceptoría"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="Dada la cantidad de refugiados que llegan a la Preceptoría desde las destrozadas colmenas y tierras baldías de Gladius Prime, la Canonesa Superior ha ordenado santificar nuevas tierras, atrayendo a los nativos al rebaño—quieran o no."/>
	<entry name="SistersOfBattle/CityTier3" value="Fortaleza Santuario"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="La Preceptoría es ya más una ciudad, parecida a las colmenas que una vez salpicaron la superficie del planeta. Bajo su masa se encuentra un suburbio, sobre el mismo agujas y capillas. Quizá sólo las Fortalezas-Monasterio de los Adeptus Astartes rivalizan en tamaño y adornos sagrados."/>
	<entry name="SistersOfBattle/DivineDeliverance" value="<string name='Actions/SistersOfBattle/DivineDeliverance'/>"/>
	<entry name="SistersOfBattle/DivineDeliveranceDescription" value="<string name='Actions/SistersOfBattle/DivineDeliveranceDescription'/>"/>
	<entry name="SistersOfBattle/DivineDeliveranceFlavor" value="<string name='Actions/SistersOfBattle/DivineDeliveranceFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="Cruzada Eterna"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="Aumenta la producción."/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="“Y no descansaremos, pues el ojo del Dios-Emperador está sobre nosotros. Él ve cada alma salvada y sabe que el número en peligro es mucho mayor. No podemos parar, no podemos ir más despacio—la nuestra es una cruzada eterna.”<br/>  — Canonesa Vandire, In Memoriam De Virtute"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Caza Experto"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Aumenta la punteria."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="En la mayoría de alas de ataque, un puñado de aeronaves es pilotado por pilotos veteranos. Estos ases de caza son enemigos realmente peligrosos, capaces de prever la reacción de su presa con precisión increíble. Pocos de estos hombre sobreviven más de una o dos campañas pues son enviados a las misiones más peligrosas donde sólo los más experimentados pilotos tendrían esperanza de vencer."/>
	<entry name="SistersOfBattle/FlankSpeed" value="Velocidad de Flanco"/>
	<entry name="SistersOfBattle/FlankSpeedDescription" value="Aumenta la velocidad de movimiento, pero impide emplear armas a distancia."/>
	<entry name="SistersOfBattle/FlankSpeedFlavor" value="Uno no debería cuestionar la nobleza del Questor Imperialis. Aparte de la fe, ninguno puede compararse con el Caballero-Lancero, que va a la batalla armado sólo con una lanza y su prodigiosa velocidad, rezando por llegar a las líneas enemigas. Es una buena adición a nuestras fuerzas.—Canonesa Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="Munición Mejorada"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="Sobre los proyectiles del Castigador, rociamos polvo de la sagrada Tierra, alzamos nuestras voces en oración, y nos arrodillamos frente al Santuario de Santa Katherine. Después añadimos un 25% más de explosivo a cada proyectil y en la punta ponemos adamantium.”<br/>  — Canonesa Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/IonGauntletShield" value="Escudo Iónico Guantelete"/>
	<entry name="SistersOfBattle/IonGauntletShieldDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/IonGauntletShieldFlavor" value="El generador de escudo iónico montado en el ensamblaje del guantelete derecho del Cerastus es más concentrado que el escudo iónico direccional montado en el Caballero Paladin, pero carece de su flexibilidad táctica."/>
	<entry name="SistersOfBattle/KeepersOfTheFaith" value="Guardianes De La Fe"/>
	<entry name="SistersOfBattle/KeepersOfTheFaithDescription" value="Evita la vigilancia, pero aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/KeepersOfTheFaithFlavor" value="La razón dice, dispara cuando veas al enemigo. La razón dice, cúbrete cuando el enemigo te dispare. La razón dice, muere cuando seas alcanzado. La razón no entiende nuestra fe ni cómo la mantenemos.”<br/>  — Canonesa Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/KnightParry" value="Esquiva Caballeresca"/>
	<entry name="SistersOfBattle/KnightParryDescription" value="Reduce la precisión en combate cuerpo a cuerpo."/>
	<entry name="SistersOfBattle/KnightParryFlavor" value="El generador de escudo iónico montado en el ensamblaje del guantelete derecho del Cerastus puede ser empleado para desviar el más poderoso de los golpes en combate cuerpo a cuerpo."/>
	<entry name="SistersOfBattle/LaudHailer" value="Alabanza"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="Esta unidad aún puede ejecutar actos de fe aunque esté acobardada."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Actions/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/LeadTheRighteous" value="<string name='Actions/SistersOfBattle/LeadTheRighteous'/>"/>
	<entry name="SistersOfBattle/LeadTheRighteousDescription" value="Aumenta la precisión."/>
	<entry name="SistersOfBattle/LeadTheRighteousFlavor" value="<string name='Actions/SistersOfBattle/LeadTheRighteousFlavor'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperor" value="<string name='Actions/SistersOfBattle/LightOfTheEmperor'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperorDescription" value="<string name='Actions/SistersOfBattle/LightOfTheEmperorDescription'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperorFlavor" value="<string name='Actions/SistersOfBattle/LightOfTheEmperorFlavor'/>"/>
	<entry name="SistersOfBattle/Martyrdom" value="Martirio"/>
	<entry name="SistersOfBattle/MartyrdomDescription" value="Al morir, reduce la pérdida de moral de todas las unidades aliadas del Adepta Sororitas."/>
	<entry name="SistersOfBattle/MartyrdomFlavor" value="Las Hermanas del Adepta Sororitas no se dejan llevar por la desesperación cuando ven caer a sus líderes. Al contrario, la sangre de estas heroínas martirizadas refuerza su resolución, inspirando su sacrificio a alcanzar mayores cotas de heroísmo."/>
	<entry name="SistersOfBattle/MartyrSpirit" value="Espíritu Mártir"/>
	<entry name="SistersOfBattle/MartyrSpiritDescription" value="Al morir, reduce la pérdida de moral de las unidades adyacentes con Escudo de Fe."/>
	<entry name="SistersOfBattle/MartyrSpiritFlavor" value="“La pérdida de nuestras Hermanas y tropas auxiliares nos entristeció—pero nos galvanizó para redoblar nuestros esfuerzos.”<br/>  — Rememoradora desconocida, Evangelio de la Telaraña"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Actions/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Actions/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="Hojas Benditas"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Aumenta la penetración de blindaje."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="Las armas del Adepta Sororitas difieren poco de aquellas empleadas por el Astra Militarum, menos en dos aspectos: la calidad de su mantenimiento; y las sagradas súplicas y oraciones que se hacen sobre ellas. Sólo esto último puede explicar los milagros que se producen en la batalla, donde esas hojas benditas encuentran las más pequeñas debilidades una y otra vez."/>
	<entry name="SistersOfBattle/MiraculousIntervention" value="<string name='Actions/SistersOfBattle/MiraculousIntervention'/>"/>
	<entry name="SistersOfBattle/MiraculousInterventionDescription" value="<string name='Actions/SistersOfBattle/MiraculousInterventionDescription'/>"/>
	<entry name="SistersOfBattle/MiraculousInterventionFlavor" value="<string name='Actions/SistersOfBattle/MiraculousInterventionFlavor'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="Movilización del Ministorum"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="Aumenta la producción de requisiciones."/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="Los ritos de la Eclesiarquía no son únicamente de carácter militar: existen oraciones y liturgias para todas las funciones del Imperio. Ser el foco de uno de esos ritos, sea Famulata o Pronatus o incluso el más humilde de los trabajadores, les conduce a los más grandes esfuerzos."/>
	<entry name="SistersOfBattle/OathOfFaith" value="Pacto de Fe"/>
	<entry name="SistersOfBattle/OathOfFaithDescription" value="Reduce la precisión y evita el uso del Escudo de Fe."/>
	<entry name="SistersOfBattle/OathOfFaithFlavor" value="“Todas hicimos un juramento, el de defender al Imperio, purgar al hereje y al alien. Nos dio apoyo, nos ayudó a través del dolor y del miedo. Pero cuando nuestras hermanas finalmente se rindieron y huyeron, nuestro juramento las persiguió…Si sobrevivieran a su huida, volverían como Hermanas Arrepentidas…”<br/>  — Rememoradora desconocida, Evangelio de la Telaraña"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="Armadura Dechado"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="Entre el arsenal sagrado de una Orden Militante, las antiguas Armaduras Dechado son consideradas como atuendos sagrados con voluntad propia. Sólo las Celestinas más dignas pueden controlar a su espíritu para emplearlas en batalla."/>
	<entry name="SistersOfBattle/Protected" value="Protegida"/>
	<entry name="SistersOfBattle/ProtectedDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/ProtectedFlavor" value="<string name='Actions/SistersOfBattle/BodyguardFlavor'/>"/>
	<entry name="SistersOfBattle/PsyShock" value="Choque Psíquico"/>
	<entry name="SistersOfBattle/PsyShockDescription" value="Aturde a los Psíquicos."/>
	<entry name="SistersOfBattle/PsyShockFlavor" value="<string name='Weapons/CondemnorBoltgunSilverStake'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="Recitaciones Purificadoras"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="Aumenta el daño."/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="“Nuestros enemigos nos llaman frías, inhumanas, sin sentimientos. Pero sentimos, nos lamentamos, nos enfurecemos. Dejemos que sientan nuestro calor—en nuestros lanzallamas, nuestras armas de fusión.”<br/>  — Canonesa Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/RagingFervour" value="Fervor Furioso"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="Aumenta el daño."/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="“Las armas en nuestras manos son instrumentos de nuestra fe. Cada proyectil es una oración lanzada al corazón del infiel, expandiendo la palabra por todo su ser.”<br/>  — Canonesa Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/RighteousJudgement" value="<string name='Actions/SistersOfBattle/RighteousJudgement'/>"/>
	<entry name="SistersOfBattle/RighteousJudgementDescription" value="<string name='Actions/SistersOfBattle/RighteousJudgementDescription'/>"/>
	<entry name="SistersOfBattle/RighteousJudgementFlavor" value="<string name='Actions/SistersOfBattle/RighteousJudgementFlavor'/>"/>
	<entry name="SistersOfBattle/SacresantShield" value="Escudo Sacrosanto"/>
	<entry name="SistersOfBattle/SacresantShieldDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/SacresantShieldFlavor" value="Las fieles Celestes Sacrosantas permanecen firmes ante los peores horrores de la galaxia. Hordas de mutantes y herejes rompen fútilmente contra sus muros de escudos plantados con justa ira."/>
	<entry name="SistersOfBattle/SaintlyBlessings" value="Santas Bendiciones"/>
	<entry name="SistersOfBattle/SaintlyBlessingsDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/SaintlyBlessingsFlavor" value="<string name='Actions/SistersOfBattle/SaintlyBlessingsFlavor'/>"/>
	<entry name="SistersOfBattle/ShieldOfFaith" value="Escudo de la Fe"/>
	<entry name="SistersOfBattle/ShieldOfFaithDescription" value="Aumenta la reducción de daño."/>
	<entry name="SistersOfBattle/ShieldOfFaithFlavor" value="Las miembros del Adepta Sororitas aprenden que la fe es un escudo más fuerte que cualquier armadura. Tal es el poder de su creencia en que el Emperador les protegerá que las Adeptas Sororitas pueden ignorar las más graves de las heridas y resistir la brujería de los hechiceros enemigos."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="Simulacrum Imperialis"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="Reduce el enfriamiento de los Actos de Fe."/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="Estos símbolos sagrados de la Eclesiarquía fueron una vez portados por uno de los muchos santos del Imperio, o quizá fueron forjados a partir de sus huesos. Son fuente de inspiración y fe y es un gran honor llevar una reliquia irremplazable como estas a la batalla."/>
	<entry name="SistersOfBattle/SisterSuperior" value="Hermana Superiora"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="Aumenta la moral de la infantería y de las unidades Armadura Dechado."/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="Son las Hermanas Superioras las que forman las verdaderas columnas de cada escuadra de Hermanas de Batalla. Hablan con la autoridad derivada de años de experiencia en combate y una fe suprema en el Dios-Emperador, estas notables oficiales aseguran que cada Hermana bajo su mando luche con todas sus habilidades y resistencia, maximizando así el impacto estratégico de la escuadra en sí."/>
	<entry name="SistersOfBattle/SolaceInAnguish" value="<string name='Actions/SistersOfBattle/SolaceInAnguish'/>"/>
	<entry name="SistersOfBattle/SolaceInAnguishDescription" value="<string name='Actions/SistersOfBattle/SolaceInAnguishDescription'/>"/>
	<entry name="SistersOfBattle/SolaceInAnguishFlavor" value="<string name='Actions/SistersOfBattle/SolaceInAnguishFlavor'/>"/>
	<entry name="SistersOfBattle/StirringRhetoric" value="<string name='Actions/SistersOfBattle/StirringRhetoric'/>"/>
	<entry name="SistersOfBattle/StirringRhetoricDescription" value="Aumenta el blindaje."/>
	<entry name="SistersOfBattle/StirringRhetoricFlavor" value="<string name='Actions/SistersOfBattle/StirringRhetoricFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithful" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithful'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithfulDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithfulDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheFaithfulFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithfulFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoic" value="<string name='Actions/SistersOfBattle/TaleOfTheStoic'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoicDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheStoicDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoicFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheStoicFlavor'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarrior" value="<string name='Actions/SistersOfBattle/TaleOfTheWarrior'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarriorDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheWarriorDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheWarriorFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheWarriorFlavor'/>"/>
	<entry name="SistersOfBattle/ThePassion" value="<string name='Actions/SistersOfBattle/ThePassion'/>"/>
	<entry name="SistersOfBattle/ThePassionDescription" value="Aumenta la precisión en combate cuerpo a cuerpo."/>
	<entry name="SistersOfBattle/ThePassionFlavor" value="<string name='Actions/SistersOfBattle/ThePassionFlavor'/>"/>
	<entry name="SistersOfBattle/UsedActOfFaith" value="Acto de Fe empleado"/>
	<entry name="SistersOfBattle/UsedActOfFaithDescription" value="Esta unidad ya ha empleado su acto de fe durante este turno."/>
	<entry name="SistersOfBattle/UsedActOfFaithFlavor" value="<string name='Traits/SistersOfBattle/UsedActOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/UsedSacredRite" value="Usado 1 Rito Sagrado"/>
	<entry name="SistersOfBattle/UsedSacredRite2" value="Usados 2 Ritos Sagrados"/>
	<entry name="SistersOfBattle/VengefulSpirit" value="Espíritu Vengativo"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="Devuelve el daño recibido al atacante."/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="“Y en la muerte, tenemos una última oportunidad—una liberación de la preocupación. Una vez muertas, no hay nada que retenga nuestra furia, ninguna necesidad de autopreservación. Podemos usar todo lo que somos, recibir todos los golpes que nos caigan, por una última oportunidad de impartir justicia.”<br/>  — Canonesa Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/WarHymn" value="<string name='Actions/SistersOfBattle/WarHymn'/>"/>
	<entry name="SistersOfBattle/WarHymnDescription" value="Aumenta los ataques."/>
	<entry name="SistersOfBattle/WarHymnFlavor" value="<string name='Actions/SistersOfBattle/WarHymnFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="Furia Mecanizada"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="Aumenta la producción."/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="“En aquellos últimos días, el fervor se propagó. Se realizó el Rito de Suministro, para pedir el máximo de los Espíritus-Máquina que hacían funcionar los Manufactora y los Hangares donde las antiguas máquinas de guerra se bendecían. No defraudaron.”<br/>  — Rememoradora desconocida, Evangelio de la Telaraña"/>
	<entry name="SkilledJink" value="<string name='Actions/SkilledJink'/>"/>
	<entry name="SkilledJinkDescription" value="<string name='Traits/JinkDescription'/>"/>
	<entry name="SkilledJinkFlavor" value="<string name='Actions/SkilledJinkFlavor'/>"/>
	<entry name="Skimmer" value="Gravítico"/>
	<entry name="SkimmerDescription" value="La unidad puede moverse sobre agua. Ignora la penalización por movimiento en ríos y alambre de espino. Reduce la penalización de movimiento en bosques y ruinas imperiales."/>
	<entry name="SkimmerFlavor" value="Algunos vehículos altamente avanzados están equipados con tracción antigravitatoria que les permite sobrevolar levemente sobre terreno difícil y tropas, convirtiéndolos en herramientas perfectas para el flanqueo sorpresa."/>
	<entry name="SkullAltar" value="<string name='Features/SkullAltar'/>"/>
	<entry name="SkullAltarDescription" value="Proporciona una recompensa a la unidad que entre en la casilla."/>
	<entry name="SkullAltarFlavor" value="<string name='Features/SkullAltarFlavor'/>"/>
	<entry name="SkullsForTheSkullThrone" value="¡Cráneos para el Trono de Cráneos!"/>
	<entry name="SkullsForTheSkullThroneDescription" value="Aumenta la reducción del daño."/>
	<entry name="SkullsForTheSkullThroneFlavor" value="Khorne solo respeta al asesino más fuerte y sin límites."/>	
	<entry name="Skyfire" value="Antiaéreo"/>
	<entry name="SkyfireDescription" value="Aumenta la puntería contra voladores. Reduce la puntería contra unidades terrestres que no sean gravíticas, motos a reacción o retrorreactores."/>
	<entry name="SkyfireFlavor" value="Las armas antiaéreas sobresalen en abatir aeronaves y gravíticos."/>
	<entry name="SlowAndPurposeful" value="Lento y sistemático"/>
 	<entry name="SlowAndPurposefulDescription" value="Niega la penalización por armas pesadas y artilleras."/>
 	<entry name="SlowAndPurposefulFlavor" value="Muchos guerreros son constantes pero seguros, tardan en avanzar pero no son menos letales."/>
	<entry name="Slowed" value="Ralentizado"/>
	<entry name="SlowedDescription" value="Reduce el movimiento."/>
	<entry name="Smash" value="Aplastar"/>
	<entry name="SmashDescription" value="Aumenta la penetracion en armadura del ataque cuerpo a cuerpo."/>
	<entry name="SmashFlavor" value="Para las criaturas más temidas, un solo golpe es suficiente para romper la armadura de un tanque o convertir a cualquier criatura viva en una pasta sangrienta."/>
	<entry name="SmokeScreen" value="Cortina de Humo"/>
	<entry name="SmokeScreenDescription" value="Aumenta la reducción del daño a distancia."/>
	<entry name="SmokeScreenFlavor" value="<string name='Actions/CreateSmokeScreenFlavor'/>"/>
	<entry name="Sniper" value="Francotirador"/>
	<entry name="SniperDescription" value="Aumenta el daño y la penetración de blindaje contra unidades de infantería y criaturas monstruosas."/>
	<entry name="SniperFlavor" value="Las armas de los francotiradores son instrumentos de precisión, usados para escoger los puntos débiles de un objetivo."/>
	<entry name="SonicBoom" value="Estámpido Sónico"/>
	<entry name="SonicBoomDescription" value="Aumenta el daño frente a unidades aéreas."/>
	<entry name="SoulBlaze" value="Abrasaalmas"/>
	<entry name="SoulBlazeDescription" value="Produce daño cada turno."/>
	<entry name="SoulBlazed" value="Alma abrasada"/>
	<entry name="SoulBlazedDescription" value="<string name='Traits/SoulBlazeDescription'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="<string name='Traits/AstraMilitarum/BlastDamageDescription'/>"/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Traits/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDiscipline" value="Bolter Discipline"/>
	<entry name="SpaceMarines/BolterDisciplineDescription" value="<string name='Traits/ChaosSpaceMarines/MaliciousVolleysDescription'/>"/>
	<entry name="SpaceMarines/BolterDisciplineFlavor" value="Para un Marine Espacial, el bólter es más que un arma: es un instrumento que muestra lo divino de la Humanidad, el portador de la muerte para sus enemigos."/>
	<entry name="SpaceMarines/CityTier2" value="Expansión de Fortaleza"/>
	<entry name="SpaceMarines/CityTier2Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="La segunda etapa en la construcción de la fortaleza ha comenzado. Con las instalaciones centrales establecidas, los tecnomarines están autorizados a expandir sus operaciones y crear una segunda línea de defensa."/>
	<entry name="SpaceMarines/CityTier3" value="Reducto Avanzado"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="La tercera etapa en la construcción de la fortaleza implica la creación de guarniciones y contrafuertes a lo largo de una línea de muros. Servidores y siervos ocupan sus vidas a la sombra de estas barreras, yendo de una tarea a otra."/>
	<entry name="SpaceMarines/CityTier4" value="Fortaleza Suprema"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="Con esta expansión, el Maestro Artífice declara completa la fortaleza. Ahora rivaliza en escala a una ciudad colmena. Dentro de sus inexpugnables paredes, da la sensación de que podrías estar en cualquier parte del Imperio. A las afueras, las tribus locales llegan desde enormes distancias para rendir tributo, mientras que sus mejores guerreros esperan ser aceptados para las pruebas letales que podrían llevarlos a ser modificados como un Marine Espacial."/>
	<entry name="SpaceMarines/CloseQuartersFirepower" value="Potencia de fuego en combate cerrado"/>
	<entry name="SpaceMarines/CloseQuartersFirepowerDescription" value="Aumenta la penetración de armadura a distancia."/>
	<entry name="SpaceMarines/CloseQuartersFirepowerFlavor" value="A distancias más cortas, la gama de armas diseñadas por Belisarius Cawl para sus marines Primaris es letalmente efectiva, lo que anima a los mejores del Emperador a acortar la distancia con sus enemigos."/>
	<entry name="SpaceMarines/DutyEternal" value="Deber eterno"/>
	<entry name="SpaceMarines/DutyEternalDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="SpaceMarines/DutyEternalFlavor" value="Todos los Adeptus Astartes poseen una voluntad indomable. Sin embargo, esos pocos Primaris a los que se les dio el privilegio de estar encerrados en un Dreadnought Redentor, después de su muerte efectiva, conocen una responsabilidad más profunda: la de estar a la altura de este honor único. Lucharán sin importar cuán graves sean las heridas o cuán severos los daños."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Escudo de Fortaleza"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Incrementa la reducción del daño invulnerable."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="Gladius Prime ya está demostrando ser mucho más hostil que los peores pronósticos del Adeptus Administratum. Con las defensas físicas de la Fortaleza bajo ataques casi constantes, los Tecnomarines aprueban la instalación de un escudo de vacío. Una vez construida, esta brillante cúpula es suficiente para deformar la potencia de fuego de batallones enteros (o incluso de un Caballero Imperial deshonesto) antes de colapsar."/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
 	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Traits/AstraMilitarum/LasDamageDescription'/>"/>
 	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="Maestría en Combate Cuerpo a Cuerpo"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="Ciertos capítulos del Adeptus Astartes—Lobos Espaciales, Bebedores de Sangre, Minotauros—son famosos por su dominio del combate cuerpo a cuerpo. Entablar combate incluso con el Explorador de menor rango de ese capítulo es como luchar contra generaciones de conocimiento y entrenamiento."/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
	<entry name="SpaceMarines/OmniscopeDescription" value="<string name='Actions/SpaceMarines/OmniscopeDescription'/>"/>
	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/RepulsorField" value="<string name='Actions/SpaceMarines/RepulsorField'/>"/>
	<entry name="SpaceMarines/RepulsorFieldDescription" value="Reduce el movimiento."/>
	<entry name="SpaceMarines/RepulsorFieldFlavor" value="<string name='Actions/SpaceMarines/RepulsorFieldFlavor'/>"/>
	<entry name="SpaceMarines/StormShield" value="Escudo de Tormenta"/>
	<entry name="SpaceMarines/StormShieldDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="SpaceMarines/StormShieldFlavor" value="Un escudo de tormenta es un escudo grande y sólido que tiene un generador de campo de energía incorporado. Aunque la mayor parte del escudo ofrece protección física, es el campo de energía el que es más impresionante, ya que es capaz de desviar casi cualquier ataque. Incluso los impactos que normalmente atravesarían la armadura Terminator son desviados con facilidad por las energías protectoras del escudo de tormenta."/>
	<entry name="SpaceMarines/SuppressiveBombardment" value="Bombardeo de Supresión"/>
	<entry name="SpaceMarines/SuppressiveBombardmentDescription" value="Aumenta el daño y fija a la infantería enemiga cuando esté adyacente a otro Whirlwind."/>
	<entry name="SpaceMarines/SuppressiveBombardmentFlavor" value="El Codex Astartes dicta que los Whirlwinds operan mejor en grandes grupos, desplegando un bombardeo que avanza y que no puede ser evitado, sólo soportado. Incluso a cobijo, las tropas ligeras y los vehículos son completamente devastados por la incesante lluvia de cohetes."/>	
	<entry name="SpaceMarines/Thunderstrike" value="Atronador"/>
	<entry name="SpaceMarines/ThunderstrikeDescription" value="Aumenta el daño a distancia de las unidades de Marines Espaciales contra objetivos atacados por esta unidad."/>
	<entry name="SpaceMarines/ThunderstrikeFlavor" value="Los Storm Speeders fuertemente armados diseñados por Belisarius Cawl están especializados contra ciertos objetivos, y el Atronador se centra en eliminar vehículos enemigos y objetivos clave. De hecho, un enemigo paralizado por sus armamentos se vuelve aún más vulnerable a los ataques posteriores de sus compañeros de batalla."/>
	<entry name="SpaceMarines/ThunderstrikeTarget" value="<string name='Traits/SpaceMarines/Thunderstrike'/>"/>
	<entry name="SpaceMarines/ThunderstrikeTargetDescription" value="Aumenta el daño a distancia de las unidades de Marines Espaciales contra esta unidad."/>
	<entry name="SpaceMarines/ThunderstrikeTargetFlavor" value="<string name='Traits/SpaceMarines/ThunderstrikeFlavor'/>"/>
	<entry name="SpaceSlip" value="<string name='Actions/SpaceSlip'/>"/>
	<entry name="SpaceSlipDescription" value="Aumenta la reducción del daño"/>
	<entry name="Stealth" value="Sigilo"/>
	<entry name="StealthDescription" value="Aumenta la reducción de daño a distancia."/>
	<entry name="StealthFlavor" value="Algunos guerreros son maestros del disfraz y el ocultamiento, capaces de desvanecerse en las ruinas de un campo de batalla hasta que estén listos para atacar."/>
	<entry name="StrafingRun" value="Acribillar"/>
	<entry name="StrafingRunDescription" value="Aumenta el rango de precisión contra unidades de tierra."/>
	<entry name="StrafingRunFlavor" value="Este vehículo esta diseñado como una máquina de ataque a tierra, la convergencia y distancia de dispersión de sus armas es la clave para maximizar la carnicería en sus enemigos."/>
	<entry name="Strikedown" value="Tumbar"/>
	<entry name="StrikedownDescription" value="Reduce temporalmente el movimiento de unidades de infantería objetivo."/>
	<entry name="StrikedownFlavor" value="Un poderoso golpe que puede tumbar incluso al más poderoso guerrero."/>
	<entry name="Stubborn" value="Tozudo"/>
	<entry name="StubbornDescription" value="Reduce la pérdida de moral."/>
	<entry name="StubbornFlavor" value="Muchos guerreros viven y mueren acorde a los principios de 'muerte antes del deshonor'. Rara vez estos guerreros dan un paso atrás ante el peligro."/>
	<entry name="Stunned" value="Aturdido"/>
	<entry name="StunnedDescription" value="Impide que la unidad actúe o se mueva."/>
	<entry name="Suicider" value="Suicida"/>
	<entry name="SuiciderDescription" value="Los enemigos no ganan experiencia cuando esta unidad se suicida."/>
	<entry name="Summon" value="Invocar"/>
	<entry name="SummonDescription" value="No puede hacer Vigilancia si la unidad es un andador."/>
	<entry name="SuperHeavy" value="Superpesado"/>
	<entry name="SuperHeavyDescription" value="Clasificación."/>
	<entry name="SuperHeavyFlavor" value="Grandes construcciones blindadas que llevan suficiente potencia de fuego para vaporizar, aplastar o incinerar un ejército entero."/>
	<entry name="Supersonic" value="Supersónico"/>
	<entry name="SupersonicDescription" value="Aumenta el movimiento."/>
	<entry name="SupersonicFlavor" value="Los vehículos supersonicas son extremadamente rápidos, incluso para los estándares de una aeronave, haciéndolos excepcionalmente móviles en combate."/>
	<entry name="Swarms" value="Enjambres"/>
	<entry name="SwarmsDescription" value="El daño sufrido se distribuye uniformemente entre el grupo de la unidad, pero el grupo sufre impactos adicionales contra armas explosivas y de plantilla."/>
	<entry name="SwarmsFlavor" value="Estas criaturas Estas criaturas son tan multitudinarias que no pueden eliminarse individualmente y tienen que ser combatidas en grupo."/>
	<entry name="Swiftstrike" value="Ataque rápido"/>
	<entry name="SwiftstrikeDescription" value="Aumenta los ataques."/>
	<entry name="TacticalDoctrine" value="Doctrina Táctica"/>
	<entry name="TacticalDoctrineDescription" value="Aumenta la precisión."/>
	<entry name="TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="Tank" value="Tanque"/>
	<entry name="TankDescription" value="Clasificación."/>
	<entry name="TankFlavor" value="Los tanques pueden usar su peso como arma, conduciendo directamente por encima de sus enemigos. Esto hace que a menudo la línea enemiga se desorganice, ya que tener un monstruo de metal aproximándose hacia ti tiene que ser perturbador para cualquiera."/>
	<entry name="TankHunters" value="Cazacarros"/>
	<entry name="TankHuntersDescription" value="Aumenta la penetración en armadura contra vehículos enemigos."/>
	<entry name="TankHuntersFlavor" value="Estos veteranos de la guerra blindada son capaces de identificar los puntos débiles de los vehículos enemigos y acabar con ellos apropiadamente."/>
	<entry name="Tau/AdvancedTargetingSystem" value="Sistema de Puntería Avanzado"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Aumenta la puntería a distancia."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="Un sistema de puntería avanzado que ayuda al artillero del vehículo identificando blancos de gran valor o peligro trazando planes de fuego para contrarrestarlos."/>
	<entry name="Tau/AutomatedRepairSystem" value="Sistema de Reparación Automática"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Repara cada turno puntos de daño."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="Minúsculos drones de mantenimiento revolotean en torno a sistemas dañados para repararlos en el fragor de la batalla."/>
	<entry name="Tau/BlacksunFilter" value="Filtro de Visión Nocturna"/>
	<entry name="Tau/BlacksunFilterDescription" value="Aumenta la visión."/>
	<entry name="Tau/BlacksunFilterFlavor" value="Este equipo de filtrado óptico permite a los sensores de los vehículos apuntar contra los enemigos con plena eficacia y rango incluso durante operaciones de combate nocturno."/>
	<entry name="Tau/BlastDamage" value="Explosión de Fase"/>
	<entry name="Tau/BlastDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Tau/BlastDamageFlavor" value="El armamento con efecto de área siempre ha sido mejor empleado para atacar grandes concentraciones de infantería que para combatir contra blancos blindados como vehículos—pero los ingenieros de la Casta de la Tierra han averiguado una forma de remediar esto. Teletransportando el componente energético de municiones explosivas e incendiarias pueden garantizar que al menos uno de estos efectos se colará entre cualquier escudo."/>
	<entry name="Tau/BoltDamage" value="Constructos Brachyuran"/>
	<entry name="Tau/BoltDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Tau/BoltDamageFlavor" value="Incluso ni los mejores técnicos de la Casta de la Tierra pueden ensamblar los componentes de sus armas de aceleración a mano. Pero confiando este trabajo a los minúsculos Brachyuran y proveyéndoles con herramientas miniaturizadas, pueden reducir la escala y la desviación fundamental de cualquier componente, posibilitando armamento aún más refinado."/>
	<entry name="Tau/BreakComposure" value="<string name='Actions/Tau/BreakComposure'/>"/>
	<entry name="Tau/BreakComposureDescription" value="Las unidades atacadas pierden moral."/>
	<entry name="Tau/BreakComposureFlavor" value="<string name='Actions/Tau/BreakComposureFlavor'/>"/>
	<entry name="Tau/CityTier2" value="Diseminación"/>
	<entry name="Tau/CityTier2Description" value="Aumenta el radio de obtención de casillas."/>
	<entry name="Tau/CityTier2Flavor" value="Una vez que una ciudad tiene un cierto tamaño, la Casta de Agua comienza una ofensiva diplomática para traer más civiles al Camino-bombardeándolos con propaganda adaptada de todas las formas, desde gotas de panfletos hasta transmisiones de video, radio y en altavoz."/>
	<entry name="Tau/CityTier3" value="Cimientos Anthrazod"/>
	<entry name="Tau/CityTier3Description" value="Aumenta el radio de obtención de casillas."/>
	<entry name="Tau/CityTier3Flavor" value="Los Anthrazods de piel gruesa son una raza de pensamiento lento normalmente empleada por los T'au para actividades peligrosas y pesadas como la minería de asteroides, pero dirigidos por los Demiurgo y la Casta Terrestre, pueden producir subestructuras de ciudades y túneles de transporte como nadie, conectando sectores distantes con facilidad."/>
	<entry name="Tau/ClusterFire" value="Fuego de Racimo"/>
	<entry name="Tau/ClusterFireDescription" value="Aumenta los ataques y el daño contra unidades que son motocicletas, motocicletas a reacción o muy voluminosas. Aumenta mucho los impactos y daño contra unidades que son criaturas monstruosas, vehículos o fortificaciones."/>
	<entry name="Tau/ClusterFireFlavor" value="Los Cañones de Inducción con Submunición exclusivos de R’Varna operan a alcances extremos, saturando el área objetivo con micro-descargas de plasma. Cuanto más grande el objetivo, más descargas de plasma impactan de manera cuasi-simultánea, destrozándolo en pequeños trocitos con facilidad."/>
	<entry name="Tau/CounterfireDefenceSystem" value="Sistemas de Defensa de Respuesta"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Aumenta la precisión de los disparos defensivos."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Actions/Tau/CounterfireDefenceSystemFlavor'/>"/>

	<entry name="Tau/DisruptionPod" value="Módulo Distorsionador"/>
	<entry name="Tau/DisruptionPodDescription" value="Aumenta la disminución del daño a distancia."/>
	<entry name="Tau/DisruptionPodFlavor" value="Un módulo distorsionador arroja imágenes distorsionadas, tanto en el espectro visual como magnético, haciendo difícil acertar al vehículo a distancia."/>
	<entry name="Tau/DroneController" value="Controlador de Drones"/>
	<entry name="Tau/DroneControllerDescription" value="Aumenta la precisión de drones adyacentes."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Actions/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/DroneControllerInRange" value="Controlador de Drones en Rango"/>
	<entry name="Tau/DroneControllerInRangeDescription" value="Aumenta la precisión."/>
	<entry name="Tau/DroneControllerInRangeFlavor" value="<string name='Actions/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/FieldAmplifierRelay" value="Relé de amplificador de campo"/>
	<entry name="Tau/FieldAmplifierRelayDescription" value="Aumenta la reducción de daño invulnerable cuando la unidad está protegida."/>
	<entry name="Tau/FieldAmplifierRelayFlavor" value="Tomando la forma de una unidad de mochila liviana, el relé amplificador de campo capta el campo de fuerza protector del Dron, lo extiende en un paraguas energizado sobre su portador y transmite la señal a otros relés dentro del alcance."/>
	<entry name="Tau/FireTeam" value="Equipo de Fuego"/>
	<entry name="Tau/FireTeamDescription" value="Aumenta la precisión a distancia cuando está adyacente a un vehículo o criatura monstruosa aliada."/>
	<entry name="Tau/FireTeamFlavor" value="Algunos sistemas de sensores de armaduras y tanques de batalla pueden ser enlazados en red para aumentar la eficacia cuando combaten formando equipos de fuego."/>
	<entry name="Tau/FlechetteDischarger" value="Descargador de Dardos"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Daña a atacantes de melé."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="Poderosos racimos de cargas reactivas son colocadas en los cascos de mucho vehículos T'au. Si el enemigo se aproxima, el racimo dispara dementes nubes de dardos a altas velocidades."/>
	<entry name="Tau/GhostkeelElectrowarfareSuite" value="Traje de Guerra electrónica Ghostkeel"/>
	<entry name="Tau/GhostkeelElectrowarfareSuiteDescription" value="Aumenta la reducción de daño de ataques a 2 casillas de distancia o más."/>
	<entry name="Tau/GhostkeelElectrowarfareSuiteFlavor" value="El equipo IA de guerra electrónica Ghostkeel rastrea agresivamente el espectro enemigo y neutraliza sus sensores, saturándolos con información falsa que hace casi imposible disparar a distancia en el campo de batalla."/>
	<entry name="Tau/HolophotonCountermeasures" value="<string name='Actions/Tau/HolophotonCountermeasures'/>"/>
	<entry name="Tau/HolophotonCountermeasuresDescription" value="Disminuye la puntería a distancia."/>
	<entry name="Tau/HolophotonCountermeasuresFlavor" value="<string name='Actions/Tau/HolophotonCountermeasuresFlavor'/>"/>
	<entry name="Tau/IntegratedShieldGenerator" value="Generador de Escudo Integrado"/>
	<entry name="Tau/IntegratedShieldGeneratorDescription" value="Aumenta la reducción de daño invulnerable y otorga una reducción de daño invulnerable a distancia mejorada."/>
	<entry name="Tau/IntegratedShieldGeneratorFlavor" value="A diferencia de la Cataclismo, la armadura de combate R’Varna está diseñada para apoyo de fuego a larga distancia y no para la movilidad, permitiendo que lleve un blindaje más pesado. De manera similar, su generador de escudo es efectivo contra asaltos cercanos, pero opera de manera óptima cuando el enemigo ataca a distancia."/>
	<entry name="Tau/Kauyon" value="<string name='Actions/Tau/Kauyon'/>"/>
	<entry name="Tau/KauyonDescription" value="Aumenta la reducción de daño a distancia."/>
	<entry name="Tau/KauyonFlavor" value="<string name='Actions/Tau/KauyonFlavor'/>"/>
	<entry name="Tau/LasDamage" value="Aceleradores de Mor'tonium"/>
	<entry name="Tau/LasDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Tau/LasDamageFlavor" value="Como bien ha aprendido la humanidad una y otra vez, a un alto coste, el uso de materiales radioactivos en el armamento siempre es peligroso. Es razonable asumir que los T’au son conscientes de ello, pero de todas maneras han elegido utilizar el recientemente descubierto Mor’tonium en su tecnología militar. Mediante al exposición al aire se desintegra rápidamente en un enorme estallido de energía iónica que los T’au aprovechan."/>
	<entry name="Tau/MobileDefencePlatform" value="Plataforma de defensa móvil"/>
	<entry name="Tau/MobileDefencePlatformDescription" value="Aumenta el movimiento mientra transporta carga."/>
	<entry name="Tau/Montka" value="<string name='Actions/Tau/Montka'/>"/>
	<entry name="Tau/MontkaDescription" value="Aumenta el daño contra unidades con menos del 50% de puntos de daño."/>
	<entry name="Tau/MontkaFlavor" value="<string name='Actions/Tau/MontkaFlavor'/>"/>
	<entry name="Tau/NetworkedMarkerlight" value="Red de Telemetría"/>
	<entry name="Tau/NetworkedMarkerlightDescription" value="Aumenta el la puntería a distancia e ignora la reducción del daño a distancia. Los ataques de la unidad no se benefician de o consumen el objetivo adquirido."/>
	<entry name="Tau/NetworkedMarkerlightFlavor" value="Esta telemetría está enlazada directamente con los sistemas de armas, permitiendo lanzar el armamento con precisión quirúrgica."/>
	<entry name="Tau/NovaBoost" value="<string name='Actions/Tau/NovaBoost'/>"/>
	<entry name="Tau/NovaBoostDescription" value="Aumenta el movimiento."/>
	<entry name="Tau/NovaBoostFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaElectromagneticShockwave" value="<string name='Actions/Tau/NovaElectromagneticShockwave'/>"/>
	<entry name="Tau/NovaElectromagneticShockwaveDescription" value="Alcanza a todos los miembros de la unidad objetivo."/>
	<entry name="Tau/NovaElectromagneticShockwaveFlavor" value="<string name='Actions/Tau/NovaElectromagneticShockwaveFlavor'/>"/>
	<entry name="Tau/NovaFire" value="<string name='Actions/Tau/NovaFire'/>"/>
	<entry name="Tau/NovaFireDescription" value="Aumenta los ataques."/>
	<entry name="Tau/NovaFireFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaShield" value="<string name='Actions/Tau/NovaShield'/>"/>
	<entry name="Tau/NovaShieldDescription" value="Aumenta la reducción de daño."/>
	<entry name="Tau/NovaShieldFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="Sistema de Defensa de Punto"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Aumenta el daño de los disparos defensivos contra unidades enemigas adyacentes a unidades amigas."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="Diseñado para proporcionar un apoyo de fuego superior a unidades de la Casta del Fuego, apunta y dispara automáticamente a enemigos que intentan un asalto."/>
	<entry name="Tau/PulseAccelerator" value="Acelerador de Inducción"/>
	<entry name="Tau/PulseAcceleratorDescription" value="Aumenta el rango de armas de pulso."/>
	<entry name="Tau/PulseAcceleratorFlavor" value="<string name='Actions/Tau/PulseAcceleratorFlavor'/>"/>
	<entry name="Tau/PulseBlaster" value="Pulse Blaster"/>
	<entry name="Tau/PulseBlasterDescription" value="Aumenta el daño y la penetración de armadura de los ataques de vigilancia."/>
	<entry name="Tau/PulseBlasterFlavor" value="Aunque los T'au temen, con razón, los combates a corta distancia, la necesidad de luchar en cascos espaciales o en laberínticos mundos colmena imperiales llevó al desarrollo del Pulse Blaster, coloquialmente llamado Pulse. Excepcionalmente, el Blaster pinta su objetivo con partículas cargadas negativamente un momento antes de disparar, para aumentar el efecto cuando golpea la carga útil de plasma al completo."/>
	<entry name="Tau/Rinyon" value="<string name='Actions/Tau/Rinyon'/>"/>
	<entry name="Tau/RinyonDescription" value="Aumenta la puntería contra unidades enemigas adyacentes a unidades amigas."/>
	<entry name="Tau/RinyonFlavor" value="<string name='Actions/Tau/RinyonFlavor'/>"/>
	<entry name="Tau/RiptideShieldGenerator" value="Generador de Escudos Cataclismo"/>
	<entry name="Tau/RiptideShieldGeneratorDescription" value="Aumenta la reducción de daño."/>
	<entry name="Tau/RiptideShieldGeneratorFlavor" value="En el interior del escudo ablativo de una Armadura Cataclismo se aloja un pequeño generador de campo cuya potencia puede ser incrementada desviando energía del reactor nova XV104."/>
	<entry name="Tau/Ripyka" value="<string name='Actions/Tau/Ripyka'/>"/>
	<entry name="Tau/RipykaDescription" value="<string name='Actions/Tau/RipykaDescription'/>"/>
	<entry name="Tau/RipykaFlavor" value="<string name='Actions/Tau/RipykaFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="Ripyka'va"/>
	<entry name="Tau/RipykaVaDescription" value="<string name='Actions/Tau/RipykaDescription'/>"/>
	<entry name="Tau/RipykaVaFlavor" value="La Comandante Coldflame fue famosa por su sutileza, su habilidad para incluir capa sobre capa de tácticas y de algún modo hacer inspirar a sus tropas para que abracen tal complejidad estratégico. Sin embargo, algunos rumorean que se comprometió tan profundamente con el Bien Supremo debido a un mero Guerrero de Fuego…"/>
	<entry name="Tau/SenseOfStone" value="<string name='Actions/Tau/SenseOfStone'/>"/>
	<entry name="Tau/SenseOfStoneDescription" value="Aumenta la reducción de daño."/>
	<entry name="Tau/SenseOfStoneFlavor" value="<string name='Actions/Tau/SenseOfStoneFlavor'/>"/>
	<entry name="Tau/ShieldGenerator" value="Shield Generator"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Aumenta la reducción de daño."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Actions/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StabilisingAnchors" value="Anclajes Estabilizadores"/>
	<entry name="Tau/StabilisingAnchorsDescription" value="Aumenta los ataques a distancia si la unidad no ha movido este turno."/>
	<entry name="Tau/StabilisingAnchorsFlavor" value="Los trajes Balistico Stormsurge KV128 son demasiado grandes como para montarles una unidad a reacción como a sus contrapartidas de Batalla. En lugar de ello, el científico de la Casta de la Tierra Bork'an los diseñó para anclarse sobre el terreno una vez desplegados, permitiéndoles divertir toda la energía del reactor a su poderoso armamento."/>
	<entry name="Tau/StimulantInjector" value="Sistema de Soporte Vital"/>
	<entry name="Tau/StimulantInjectorDescription" value="Aumenta la reducción de daño."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Actions/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/StormOfFire" value="<string name='Actions/Tau/StormOfFire'/>"/>
	<entry name="Tau/StormOfFireDescription" value="Aumenta los ataques."/>
	<entry name="Tau/StormOfFireFlavor" value="<string name='Actions/Tau/StormOfFireFlavor'/>"/>
	<entry name="Tau/SubvertCity" value="<string name='Actions/Tau/SubvertCity'/>"/>
	<entry name="Tau/SubvertCityDescription" value="Reduce la lealtad."/>
	<entry name="Tau/SubvertCityFlavor" value="<string name='Actions/Tau/SubvertCityFlavor'/>"/>
	<entry name="Tau/SupportSystems" value="Support Systems"/>
	<entry name="Tau/SupportSystemsDescription" value="Permite la instalación de sistemas de apoyo."/>
	<entry name="Tau/SupportSystemsFlavor" value="Los Trajes de Batalla T’au están diseñados para ser modulares, para que la Casta de la Tierra puedan personalizarlos fácilmente para cubrir diferentes situaciones de combate. Aunque cada traje solo tiene un número limitado de puntos de anclajes y disminuir la potencia de fuego de cualquier traje siempre resulta ser una difícil decisión."/>
	<entry name="Tau/SupportingFire" value="Fuego de Apoyo"/>
	<entry name="Tau/SupportingFireDescription" value="Aumenta el daño de los disparos defensivos contra unidades enemigas adyacentes a unidades aliadas."/>
	<entry name="Tau/SupportingFireFlavor" value="La doctrina de la Casta de Fuego, tal como aparece reflejada en el Código de Fuego, instruye a cada guerrero en la protección de sus camaradas. Utilizando zonas de fuego sobrepuestas, los equipos se proporcionan apoyo mutuo en el campo de batalla."/>
	<entry name="Tau/TargetAcquired" value="Blanco Adquirido"/>
	<entry name="Tau/TargetAcquiredDescription" value="Aumenta la puntería a distacia de los T'au contra la unidad. Reduce la reducción del daño a distancia de la unidad contra los T'au. Finaliza una vez la unidad es atacada por los T'au."/>
	<entry name="Tau/TargetAcquiredFlavor" value="Ninguna otra raza está tan comprometida con el trabajo en equipo como los T’au, y la Telemetría es un excelente ejemplo de ello. Es un láser de apuntado manual que están conectado con el sistema de información T’au, permitiendo que otras fuerzas T’au realicen ataques de precisión."/>
	<entry name="Tau/TidewallShieldline" value="Línea de Escudos"/>
	<entry name="Tau/TidewallShieldlineDescription" value="Devuelve daño a distancia de armas que no son de área, de plantilla o de fuego brujo contra el atacante."/>
	<entry name="Tau/TidewallShieldlineFlavor" value="La fortificación más utilizada por las fuerzas del Imperio T'au es la Línea de Escudos, un muro de energía tras el cual la infantería puede ponerse a cubierto. Mientras las descargas del enemigo chocan y rebotan, inocuas, contra el campo refractivo de la Línea, los Guerreros de Fuego a los que protege desatan un violento fuego de respuesta. Y aún peor para cualquier agresor que intente apartar a los T'au de su cobertura, este muro de fuerza puede redirigir energía cinética, devolviendo los proyectiles hacia las filas del enemigo."/>
	<entry name="Tau/TidewallShieldlineCity" value="<string name='Traits/Tau/TidewallShieldline'/>"/>
	<entry name="Tau/TidewallShieldlineCityDescription" value="<string name='Traits/Tau/TidewallShieldlineDescription'/>"/>
	<entry name="Tau/TidewallShieldlineCityFlavor" value="<string name='Traits/Tau/TidewallShieldlineFlavor'/>"/>
	<entry name="Tau/TidewallShieldlineOutpost" value="<string name='Traits/Tau/TidewallShieldline'/>"/>
	<entry name="Tau/TidewallShieldlineOutpostDescription" value="<string name='Traits/Tau/TidewallShieldlineDescription'/>"/>
	<entry name="Tau/TidewallShieldlineOutpostFlavor" value="<string name='Traits/Tau/TidewallShieldlineFlavor'/>"/>
	<entry name="Tau/Unity" value="<string name='Actions/Tau/Unity'/>"/>
	<entry name="Tau/UnityDescription" value="<string name='Actions/Tau/UnityDescription'/>"/>
	<entry name="Tau/UnityFlavor" value="<string name='Actions/Tau/UnityFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="En la Diversidad, Unidad"/>
	<entry name="Tau/UtopiaBonusDescription" value="Aumenta la bonificación de lealtad de nuevos tipos de edificios."/>
	<entry name="Tau/UtopiaBonusFlavor" value="La ciudad T’au ideal es un ejemplo para los demás, un alarde de equilibrio perfecto entre las varias castas de los T’au y con sus clientes y razas auxiliares, todos viviendo en armonía."/>
	<entry name="Tau/VectoredRetroThrusters" value="Retrocohetes Vectoriales"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Aumenta el movimiento he ignora la zona de control enemiga."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Actions/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="Sensor Antiaéreo"/>
	<entry name="Tau/VelocityTrackerDescription" value="Aumenta la precisión a distancia contra voladores."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Actions/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tau/VolleyFire" value="<string name='Actions/Tau/VolleyFire'/>"/>
	<entry name="Tau/VolleyFireDescription" value="Aumenta el ataque a distancia si la unidad no se ha movido."/>
	<entry name="Tau/VolleyFireFlavor" value="<string name='Actions/Tau/VolleyFireFlavor'/>"/>
	<entry name="Tau/ZephyrsGrace" value="<string name='Actions/Tau/ZephyrsGrace'/>"/>
	<entry name="Tau/ZephyrsGraceDescription" value="Increases the action points."/>
	<entry name="Tau/ZephyrsGraceFlavor" value="<string name='Actions/Tau/ZephyrsGraceFlavor'/>"/>
	<entry name="TelekineDome" value="Domo Telekinético"/>
	<entry name="TelekineDomeDescription" value="Aumenta la reducción del daño a distancia."/>
	<entry name="TelekineDomeFlavor" value="<string name='Actions/TelekineDomeFlavor'/>"/>
	<entry name="TeleportHomer" value="Baliza de Teleportación"/>
	<entry name="TeleportHomerDescription" value="Hace que el despliegue orbital no consuma puntos de acción cuando se despliegan Capellanes, Exterminadores de asalto y Exterminadores adyacentes a esta unidad."/>
	<entry name="TeleportHomerFlavor" value="Las balizas de teleportación emiten una poderosa señal que permite que los Cruceros de Asalto en órbita les fijen con su equipamiento de teleportación. Al hacer coincidir las coordenadas exactas de la señal, se reduce considerablemente el riesgo de fallar en el lugar deseado."/>
	<entry name="Template" value="Plantilla"/>
	<entry name="TemplateDescription" value="<string name='Traits/BeamDescription'/>"/>
	<entry name="TerminatorArmour" value="Armadura Exterminador"/>
	<entry name="TerminatorArmourDescription" value="Aumenta la reducción del daño."/>
	<entry name="TerminatorArmourFlavor" value="La armadura exterminador es la mejor protección que un Marine Espacial puede llevar. Incluso se dice que una armadura exterminador puede aguantar las impresionantes energías emitidas por el núcleo de un generador de plasma y esto, de hecho, era el propósito original de esta armadura."/>
	<entry name="Tesla" value="Tesla"/>
	<entry name="TeslaDescription" value="Aumenta el ataque."/>
	<entry name="TeslaFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="TeslaDamage" value="Descarga Viridiana"/>
	<entry name="TeslaDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="TeslaDamageFlavor" value="El luminoso rayo descargado por estas exóticas y antiguas armas Necrón chisporrotea con vida, retorciendo y cortando a cualquier objetivo que alcance, moviéndose como si tuviera vida propia."/>
	<entry name="TheFleshIsWeak" value="La Carne es Débil"/>
	<entry name="TheFleshIsWeakDescription" value="Aumenta la reducción del daño."/>
	<entry name="TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="TrainedSentinelPilots" value="Pilotos Sentinel Cualificados"/>
	<entry name="TrainedSentinelPilotsDescription" value="Aumenta el daño."/>
	<entry name="TrainedSentinelPilotsFlavor" value="Los Sentinels son usados en numerosos roles dentro del Imperio—desde elevadores usados para cargar aeronaves con armamento hasta exploradores. Algunos Sentinels están equipados con armaduras mejoradas y estabilizadores para llevar armamento más pesado. Esto cambia el rol del Sentinel de soporte a reconocimiento y a una plataforma de fuego pesado."/>
	<entry name="Traktor" value="Traktor"/>
	<entry name="TraktorDescription" value="Inmoviliza voladores enemigos."/>
	<entry name="Transport" value="Transporte"/>
	<entry name="TransportDescription" value="Permite el transporte de unidades de infantería y criaturas monstruosas."/>
	<entry name="TransportFlavor" value="Algunos pueden transportar a otros, proporcionando velocidad y protección. Por supuesto, si el transporte es destruido, los pasajeros mueren carbonizados en la explosión."/>
	<entry name="TurboBoost" value="Turbopropulsores"/>
	<entry name="TurboBoostDescription" value="Aumenta el movimiento."/>
	<entry name="Tusked" value="Cornamenta"/>
	<entry name="TuskedDescription" value="Aumenta el ataque de las armas cuerpo a cuerpo."/>
	<entry name="Tyranids/AcidBlood" value="Sangre ácida"/>
	<entry name="Tyranids/AcidBloodDescription" value="Los enemigos sufren daño cuando causan daño cuerpo a cuerpo."/>
	<entry name="Tyranids/AcidBloodFlavor" value="La sangre alienígena de ciertos tiránidos es tan corrosiva que puede corroer incluso la ceramita y disolver la carne en segundos."/>
	<entry name="Tyranids/AdaptiveBiology" value="<string name='Actions/Tyranids/AdaptiveBiology'/>"/>
	<entry name="Tyranids/AdaptiveBiologyDescription" value="Aumenta la reducción del daño."/>
	<entry name="Tyranids/AdaptiveBiologyFlavor" value="<string name='Actions/Tyranids/AdaptiveBiologyFlavor'/>"/>
	<entry name="Tyranids/AlphaWarrior" value="<string name='Actions/Tyranids/AlphaWarrior'/>"/>
	<entry name="Tyranids/AlphaWarriorDescription" value="Aumenta la precisión."/>
	<entry name="Tyranids/AlphaWarriorFlavor" value="<string name='Actions/Tyranids/AlphaWarriorFlavor'/>"/>
	<entry name="Tyranids/Biomorph" value="Biomorfo"/>
	<entry name="Tyranids/BiomorphDescription" value="Clasificación."/>
	<entry name="Tyranids/BiomorphDamage" value="Adaptación biomórfica"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value=""/>
	<entry name="Tyranids/BoundingLeap" value="Salto delimitador"/>
	<entry name="Tyranids/BoundingLeapDescription" value="Niega las penalizaciones de movimiento por ríos."/>
	<entry name="Tyranids/BroodProgenitor" value="<string name='Actions/Tyranids/BroodProgenitor'/>"/>
	<entry name="Tyranids/BroodProgenitorDescription" value="Aumenta los ataques."/>
	<entry name="Tyranids/BroodProgenitorFlavor" value="<string name='Actions/Tyranids/BroodProgenitorFlavor'/>"/>
	<entry name="Tyranids/ChameleonicSkin" value="Piel camaleónica"/>
	<entry name="Tyranids/ChameleonicSkinDescription" value="Permite ataques defensivos con armas de melé."/>
	<entry name="Tyranids/CityDamage" value="El Salón de las Arañas"/>
	<entry name="Tyranids/CityDamageDescription" value="Causa daño cada turno."/>
	<entry name="Tyranids/CityDamageFlavor" value="Entrar en una ciudad tiránida madura es caminar entre gigantes—gigantes hostiles y biológicamente diseñados, determinados a consumirte. La infantería de vellosidades vidriadas recubiertas de toxina, los valles salpicados de dientes que se cierran como fauces, y los esfínteres de tamaño inmenso se abren repentinamente, arrojando ejércitos incautos a las piscinas de recuperación ácidas."/>
	<entry name="Tyranids/CityGrowth" value="Expansión agresiva"/>
	<entry name="Tyranids/CityGrowthDescription" value="Aumenta la tasa de natalidad."/>
	<entry name="Tyranids/CityGrowthFlavor" value="Las ciudades tiránidas dan poco valor al tamaño, pues se centran en la productividad. Mientras una ciudad esté produciendo tropas de manera eficiente, no importa si ocupa terreno. Pero cuando una ciudad debe expandirse, puede hacerlo extremadamente rápido—desde esfínteres que lanzan tendones en forma de gancho, arrastrando las vainas de semillas hacia el paisaje, donde estallan como púas estranguladoras. En momentos la tierra está ocupada, gestando rápidamente una nueva estructura."/>
	<entry name="Tyranids/CityLoyalty" value="Dispersión de materia gris"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Aumenta la producción de lealtad."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="A lo largo del enjambre, desde los grandes organismos productores, hasta las criaturas de mantenimiento más pequeñas, hasta el propio sustrato, se dispersan pequeños paquetes de materia gris, lo que aumenta la facilidad con la que se pueden controlar las criaturas enjambre."/>
	<entry name="Tyranids/CityPopulationLimit" value="Orgánulos Biogénesis"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Aumenta el límite de población."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="Nada verdaderamente “vive” en una colmena de ciudad tiránida. Las criaturas 'orgánulos' sin sentido engendran y realizan funciones cruciales antes de ser re-asimiladas. Se pueden hacer modificaciones a la estructura de la ciudad, lo que permite que estos organismos se engendren y reabsorban en ventanas mucho más cortas, lo que permite que aumente su población general."/>
	<entry name="Tyranids/CityProduction" value="Propulsor de partos"/>
	<entry name="Tyranids/CityProductionDescription" value="Aumenta el rendimiento de producción."/>
	<entry name="Tyranids/CityProductionFlavor" value="Esperar a que los recién nacidos tiránidos abandonen sus respectivos edificios madre es relativamente ineficiente. Sin embargo, los genetistas han visto ciertos edificios adaptados que expulsan a la criatura recién nacida de su saco de parto con un impulso eléctrico convulsivo, lo que le permite reutilizarla rápidamente."/>
	<entry name="Tyranids/CityTier2" value="Dispersión de devoradores"/>
	<entry name="Tyranids/CityTier2Description" value="Aumenta el radio de adquisición de casillas."/>
	<entry name="Tyranids/CityTier2Flavor" value="A medida que la colmena madura, los devoradores ya no necesitan enjambres en un número tan grande para la seguridad. Al dispersarlos, la Mente Enjambre puede coordinar su comportamiento de recuperación en un rango más amplio."/>
	<entry name="Tyranids/DiffusionField" value="<string name='Actions/Tyranids/DiffusionField'/>"/>
	<entry name="Tyranids/DiffusionFieldDescription" value="Aumenta la reducción de daño a distancia."/>
	<entry name="Tyranids/DiffusionFieldFlavor" value="<string name='Actions/Tyranids/DiffusionFieldFlavor'/>"/>
	<entry name="Tyranids/Dominion" value="<string name='Actions/Tyranids/Dominion'/>"/>
	<entry name="Tyranids/DominionDescription" value="<string name='Actions/Tyranids/DominionDescription'/>"/>
	<entry name="Tyranids/DominionFlavor" value="<string name='Actions/Tyranids/DominionFlavor'/>"/>
	<entry name="Tyranids/ExploitWeaknesses" value="<string name='Actions/Tyranids/ExploitWeaknesses'/>"/>
	<entry name="Tyranids/ExploitWeaknessesDescription" value="Reduce la armadura."/>
	<entry name="Tyranids/ExploitWeaknessesFlavor" value="<string name='Actions/Tyranids/ExploitWeaknessesFlavor'/>"/>
	<entry name="Tyranids/FeederBeast" value="Bestia alimentadora"/>
	<entry name="Tyranids/FeederBeastDescription" value="El daño causado por esta unidad se convierte en curación."/>
	<entry name="Tyranids/GraspingTail" value="<string name='Actions/Tyranids/GraspingTail'/>"/>
	<entry name="Tyranids/GraspingTailDescription" value="Reduce los ataques."/>
	<entry name="Tyranids/GraspingTailFlavor" value="<string name='Actions/Tyranids/GraspingTailFlavor'/>"/>
	<entry name="Tyranids/HiveCommander" value="<string name='Actions/Tyranids/HiveCommander'/>"/>
	<entry name="Tyranids/HiveCommanderDescription" value="Aumenta el daño y la reducción de daño."/>
	<entry name="Tyranids/HiveCommanderFlavor" value="<string name='Actions/Tyranids/HiveCommanderFlavor'/>"/>
	<entry name="Tyranids/IndescribableHorror" value="<string name='Actions/Tyranids/IndescribableHorror'/>"/>
	<entry name="Tyranids/IndescribableHorrorDescription" value="Reduce la moral cada turno."/>
	<entry name="Tyranids/IndescribableHorrorFlavor" value="<string name='Actions/Tyranids/IndescribableHorrorFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="Instintos gantes"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Reduce el mantenimiento de biomasa."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="La Mente Enjambre generalmente mantiene un estrecho control sobre su enjambre, dirigiendo todo a excepción de las acciones inconscientes—pero al aflojar ese control, a los organismos más pequeños se les puede permitir solo la suficiente dependencia para alimentarse por sí mismos."/>
	<entry name="Tyranids/InstinctiveBehaviour" value="Comportamiento instintivo"/>
	<entry name="Tyranids/InstinctiveBehaviourDescription" value="<string name='Traits/Tyranids/InstinctiveBehaviourLurkDescription'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourLurkFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourFeed" value="Comportamiento instintivo (Alimentarse)"/>
	<entry name="Tyranids/InstinctiveBehaviourFeedDescription" value="A menos que esté controlado o coordinado por la voluntad dominante de la Mente Enjambre, muchos organismos tiránidos volverán a sus instintos más básicos."/>
	<entry name="Tyranids/InstinctiveBehaviourFeedFlavor" value="Si no están coordinadas o controladas por la voluntad de la mente enjambre, muchas criaturas tiránidas se reducen a sus instintos base."/>
	<entry name="Tyranids/InstinctiveBehaviourHunt" value="Comportamiento instintivo (Caza)"/>
	<entry name="Tyranids/InstinctiveBehaviourHuntDescription" value=""/>
	<entry name="Tyranids/InstinctiveBehaviourHuntFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourLurk" value="Comportamiento instintivo (Acechar)"/>
	<entry name="Tyranids/InstinctiveBehaviourLurkDescription" value="La unidad pierde la moral cada turno mientras no esté al alcance de una criatura sináptica."/>
	<entry name="Tyranids/InstinctiveBehaviourLurkFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride" value="Eliminar el comportamiento instintivo"/>
	<entry name="Tyranids/InstinctiveBehaviourOverrideDescription" value="Esta unidad ya no está sujeta al comportamiento instintivo."/>
	<entry name="Tyranids/InstinctiveBehaviourOverrideFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveFire" value="Fuego Instintivo"/>
	<entry name="Tyranids/InstinctiveFireDescription" value="Sólo puede tomar como objetivo a la unidad enemiga más cercana."/>
	<entry name="Tyranids/InstinctiveFireFlavor" value="El Tiranocito es desarrollado por la Flota Enjambre como un mecanismo de entrega, para desplegar sus tropas claves desde el espacio y acercarlas a la biomasa del enemigo. Pero, después de que su propósito inicial haya sido cumplido, el trabajo de la prácticamente estúpida criatura no acaba: flotando en el aire, continua lanzando ácido orgánico y azotando, con sus flexibles tentáculos, a cualquiera que se ponga a su alcance."/>	
	<entry name="Tyranids/LivingBatteringRam" value="<string name='Actions/Tyranids/LivingBatteringRam'/>"/>
	<entry name="Tyranids/LivingBatteringRamDescription" value="Aumenta el daño."/>
	<entry name="Tyranids/LivingBatteringRamFlavor" value="<string name='Actions/Tyranids/LivingBatteringRamFlavor'/>"/>
	<entry name="Tyranids/LongRangedDamage" value="Proliferación ocular"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="Atacar a los enemigos a distancia puede ser difícil con una simple visión binocular. Una adaptación sensata es aumentar la variedad y el número de sensores oculares disponibles para el organismo, para mejorar el ataque a todos los tipos de enemigos."/>
	<entry name="Tyranids/MassIncubation" value="<string name='Actions/Tyranids/MassIncubation'/>"/>
	<entry name="Tyranids/MassIncubationDescription" value="<string name='Actions/Tyranids/MassIncubationDescription'/>"/>
	<entry name="Tyranids/MassIncubationFlavor" value="<string name='Actions/Tyranids/MassIncubationFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="Simbiosis de las bioarmas"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="Cuando los tiránidos fueron encontrados por primera vez, particularmente sus precursores, los Zoats, su armamento era típicamente un rango de organismos separados. Con el paso del tiempo, los tiránidos y sus armas se han acercado."/>
	<entry name="Tyranids/Onslaught" value="<string name='Actions/Tyranids/Onslaught'/>"/>
	<entry name="Tyranids/OnslaughtDescription" value="Aumenta el movimiento."/>
	<entry name="Tyranids/OnslaughtFlavor" value="<string name='Actions/Tyranids/OnslaughtFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="Reduce la precisión."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation" value="Adaptación de presa"/>
	<entry name="Tyranids/PreyAdaptationDescription" value="La mente enjambre gana investigación cuando un Malóntropo gana experiencia por combate."/>
	<entry name="Tyranids/PreyAdaptationFlavor" value="El Malántropo no solo recopila nuevos datos genéticos de cadáveres caídos—sino que también es muy capaz de observar los patrones de comunicación de las criaturas entre ellas, ya sean padres o maestros, y retener eso para codificar en las criaturas de la Mente Enjambre."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="Canales de nacimiento"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Reduce el mantenimiento de influencia."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="Un simple cambio en la estructura de los grandes organismos que conforman el enjambre tiránida, los canales de nacimiento son algo así como líneas de producción biológica, permitiendo que muchas criaturas se produzcan en paralelo sin la supervisión directa de la Mente Enjambre."/>
	<entry name="Tyranids/PsychicBarrier" value="Barrera Psíquica"/>
	<entry name="Tyranids/PsychicBarrierDescription" value="Aumenta la reducción de daño invulnerable."/>
	<entry name="Tyranids/PsychicBarrierFlavor" value="Los horrores tiránidos del tamaño del Maleceptor no carecen de defensas inherentes. Pero más allá de su gran volumen y su resistente armadura, el tejido encefálico del Malceptor también proyecta una formidable barrera psíquica, capaz de vaporizar o desviar las balas y los rayos de energía que le disparan antes de que lo golpeen."/>
	<entry name="Tyranids/PsychicOverload" value="<string name='Actions/Tyranids/PsychicOverload'/>"/>
	<entry name="Tyranids/PsychicOverloadDescription" value="Golpes con mayor precisión."/>
	<entry name="Tyranids/PsychicOverloadFlavor" value="Los pseudópodos etéreos proyectados por el tejido encefálico de un Maleceptor son manifestaciones de la Sombra en la Disformidad misma, la presencia psíquica anuladora de la Mente Colmena. Si estos rozaran siquiera la conciencia de un oponente debilitado, Experimentarás directamente la horrorosa inmensidad de esa inmanencia y morirás rápida, horrible y explosivamente."/>
	<entry name="Tyranids/RakingStrike" value="Ataque en rastrillo"/>
	<entry name="Tyranids/RakingStrikeDescription" value="Aumenta los ataques cuerpo a cuerpo de los Aeróvoros del enjambre. Aumentan los ataques contra voladores también."/>
	<entry name="Tyranids/RakingStrikeFlavor" value="El alargamiento y el endurecimiento de las espuelas afiladas en la parte inferior de un Aerovoro del Enjambre le permiten realizar ataques letales de sobrevuelo contra los enemigos. Además, las puntas de las alas afiladas permiten al Aerovoro atacar con precisión a los pilotos enemigos."/>
	<entry name="Tyranids/RapaciousHunger" value="<string name='Actions/Tyranids/RapaciousHunger'/>"/>
	<entry name="Tyranids/RapaciousHungerDescription" value="Aumenta los ataques."/>
	<entry name="Tyranids/RapaciousHungerFlavor" value="<string name='Actions/Tyranids/RapaciousHungerFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="Arqueóvoros sinápticos"/>
	<entry name="Tyranids/Reclamation2Description" value="Reduce el coste de influencia."/>
	<entry name="Tyranids/Reclamation2Flavor" value="Las piscinas de recuperación pueden parecer el más simple de los organismos tiránidos—burbujas digestivas que degradan la biomasa—pero dada su función central en el ciclo de vida de los tiránidos, sin duda han visto generaciones de mejora en el diseño. Esta adaptación particular coloca control sináptico en el borde de cada grupo para gestionar la digestión con menos supervisión de la Mente Enjambre"/>
	<entry name="Tyranids/Reclamation3" value="Acelerantes de asimilación"/>
	<entry name="Tyranids/Reclamation3Description" value="Elimina el tiempo de recarga."/>
	<entry name="Tyranids/Reclamation3Flavor" value="La recuperación debe ser un proceso eficiente para servir mejor a la Mente Enjambre—cada punto básico de biomasa que se pierde podría significar una derrota. Esta adaptación hace que la recuperación sea un proceso continuo, que permite un alto porcentaje de recuperación de biomasa."/>
	<entry name="Tyranids/Regeneration" value="Regeneración"/>
	<entry name="Tyranids/RegenerationDescription" value="Cada turno restaura puntos de salud."/>
	<entry name="Tyranids/RegenerationFlavor" value="Algunos tiránidos pueden recuperarse de terribles heridas que deberían haberles matado."/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="Adaptación carroñera"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Reduce el mantenimiento en influencia."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="La Mente Enjambre generalmente mantiene un estrecho control sobre su enjambre, dirigiendo todo menos las acciones inconscientes—pero al perder ese control, los organismos más pequeños se les puede permitir la suficiente actividad como para alimentarse por sí mismos."/>
	<entry name="Tyranids/ScourgeOfTheBrood" value="<string name='Actions/Tyranids/ScourgeOfTheBrood'/>"/>
	<entry name="Tyranids/ScourgeOfTheBroodDescription" value="Aumenta	el daño."/>
	<entry name="Tyranids/ScourgeOfTheBroodFlavor" value="<string name='Actions/Tyranids/ScourgeOfTheBroodFlavor'/>"/>
	<entry name="Tyranids/ShadowInTheWarp" value="<string name='Actions/Tyranids/ShadowInTheWarp'/>"/>
	<entry name="Tyranids/ShadowInTheWarpDescription" value="Reduce la moral cada turno."/>
	<entry name="Tyranids/ShadowInTheWarpFlavor" value="<string name='Actions/Tyranids/ShadowInTheWarpFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="Colmena gigabora"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Aumenta la penetración de armadura."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="El nido Borer, único y repugnante, se compone de un nido compacto de escarabajos Borer, insectoides ácidos que se lanzan en frenesí desde el arma. Enjambres Gigaborer contienen menos escarabajos, mucho más grandes, simplemente permitiendo más daño."/>
	<entry name="Tyranids/SingularPurpose" value="<string name='Actions/Tyranids/SingularPurpose'/>"/>
	<entry name="Tyranids/SingularPurposeDescription" value="Aumenta la precisión y el daño contra las unidades enemigas marcadas como objetivos."/>
	<entry name="Tyranids/SingularPurposeFlavor" value="<string name='Actions/Tyranids/SingularPurposeFlavor'/>"/>
	<entry name="Tyranids/SingularPurposeTarget" value="Objetivo Singular"/>
	<entry name="Tyranids/SingularPurposeTargetDescription" value="Los Emisarios Norn tienen mayor precisión y provocan más daño al atacar a esta unidad."/>
	<entry name="Tyranids/SingularPurposeTargetFlavor" value="<string name='Actions/Tyranids/SingularPurposeFlavor'/>"/>
	<entry name="Tyranids/SporeCloud" value="<string name='Actions/Tyranids/SporeCloud'/>"/>
	<entry name="Tyranids/SporeCloudDescription" value="Aumenta la reducción del daño a distancia."/>
	<entry name="Tyranids/SporeCloudFlavor" value="<string name='Actions/Tyranids/SporeCloudFlavor'/>"/>
	<entry name="Tyranids/SymbioticTargeting" value="Elección de blancos simbiótica"/>
	<entry name="Tyranids/SymbioticTargetingDescription" value="Aumenta la precisión de los ataques de disparo si la unidad se mantiene estacionaria."/>
	<entry name="Tyranids/SynapseLink" value="Enlace sináptico"/>
	<entry name="Tyranids/SynapseLinkDescription" value="La unidad no puede ser conmocionada o rota, es inmune al miedo y no sufre de comportamiento instintivo."/>
	<entry name="Tyranids/SynapseLinkFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/SynapticBacklash" value="Synaptic Backlash"/>
	<entry name="Tyranids/SynapticBacklashDescription" value="Los Termagantes en el área sufren daño cuando el Tervigón muere."/>
	<entry name="Tyranids/SynapticBacklashFlavor" value="Aunque los tiránidos no se tienen afecto entre sí—observarán con calma mientras sus compañeros de nido mueren o caminan hacia la piscina de recuperación—el trauma psíquico que padecen las crías de Tervigón en el momento de su muerte es extraño, especialmente dada la aparente incapacidad de la Mente Enjambre para eliminarlo. Tal vez sea un legado de cualquier raza tiempor desaparecida en la que se basaron los datos genéticos de los Tervigón, y un símbolo que la Mente Enjambre rara vez innova, si no que plagia."/>
	<entry name="Tyranids/ToxinSacs" value="Sacos de toxinas"/>
 	<entry name="Tyranids/ToxinSacsDescription" value="Aumenta el daño de las armas cuerpo a cuerpo contra unidades de infantería y criaturas monstruosas."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="Estas glándulas parasitarias liberan fluidos viles que cubren las garras de los tiránidos, así como sus colmillos y talones con venenos aliens."/>
	<entry name="Tyranids/Tunnel2" value="Muros Echiurean"/>
	<entry name="Tyranids/Tunnel2Description" value="Aumenta los puntos de vida."/>
	<entry name="Tyranids/Tunnel2Flavor" value="Los muros de la progenie se cultivan rápidamente, lo que significa que tienden a ser criaturas endebles, para los tiránidos. Los genetistas han observado adaptaciones que crecen más lentamente, pero que acumulan masa muscular con el tiempo, y en general son más resistentes a la acción del enemigo."/>
	<entry name="Tyranids/UnnaturalResilience" value="Resistencia Antinatural"/>
	<entry name="Tyranids/UnnaturalResilienceDescription" value="Aumenta la reducción de daño invulnerable y otorga reducción de daño adicional por insensibilidad."/>
	<entry name="Tyranids/UnnaturalResilienceFlavor" value="Imbuido de propósito por la Reina Norn, el Emisario se adapta instantáneamente a heridas que paralizarían a otra criatura, ignorando la muerte para perseguir su objetivo."/>
	<entry name="Tyranids/VehiclesUpkeep" value="Instintos de megante"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Reduce el mantenimiento de biomasa."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="La Mente Enjambre generalmente mantiene un estrecho control sobre su enjambre, dirigiendo todo menos las acciones inconscientes, excepto por sus criaturas alfa—pero perder ese control, los organismos más grandes pueden dirigir o preceder a los organismos más pequeños, lo que les permite sobrevivir sin control directo."/>
	<entry name="Tyranids/WarpField" value="Campo de la disformidad"/>
	<entry name="Tyranids/WarpFieldDescription" value="Aumenta la reducción del daño."/>
	<entry name="Tyranids/WarpFieldFlavor" value="Los Zoantropos son vitales para que la mente enjambre pueda dominar todo su potencial psíquico y son creados con un especial sentido de autopreservación. Por lo tanto, instintivamente proyectan un muro psíquico para protegerse – un escudo mental que es invisible excepto por el ligero brillo que se produce cuando es impactado por armas enemigas, sin hacer daño."/>
	<entry name="TwinLinked" value="Acoplado"/>
	<entry name="TwinLinkedDescription" value="Aumenta la precisión."/>
	<entry name="TwinLinkedFlavor" value="Estas armas tienen injertadas el mismo sistema de puntería para una mejor puntería."/>
	<entry name="Uncommon" value="Poco común"/>
 	<entry name="UncommonDescription" value="Clasificación."/>
	<entry name="UnendingHorde" value="Horda Sin Fin"/>
	<entry name="UnendingHordeDescription" value="Incrementa la reducción de daño."/>
	<entry name="UnendingHordeFlavor" value="Como los zombis de la ficción de la antigua Terra, los Plagabundos no se detienen hasta que son completamente destruidos. Su carne muerta no siente nada, por lo que los ataques que tumbarían a un ser humano normal no son más que inconvenientes mientras avanzan riendo y gruñendo…"/>
	<entry name="Unique" value="Único"/>
	<entry name="UniqueDescription" value="Limta la cantidad de unidades de este tipo."/>
	<entry name="Unwieldy" value="Aparatoso"/>
	<entry name="UnwieldyDescription" value="Reduce la precisión."/>
	<entry name="UnwieldyFlavor" value="Este arma es tan grande y aparatosa, que los ataques rápidos son imposibles de lograr."/>
	<entry name="VectoredAfterburners" value="Postquemadores Vectoriales"/>
	<entry name="VectoredAfterburnersDescription" value="Aumenta el movimiento y la reducción del daño a distancia."/>
	<entry name="Vehicle" value="Vehículo"/>
	<entry name="VehicleDescription" value="Anula la penalización por armas pesadas y artillería. Aumenta la penalización de movimiento en bosques y ruinas imperiales."/>
	<entry name="VehicleFlavor" value="La guerra no se trata de un duelo entre soldados vivos, también es una poderosa guerra entre máquinas de guerras y tanques."/>
	<entry name="VeryBulky" value="Muy Voluminoso"/>
	<entry name="VeryBulkyDescription" value="Requiere dos huecos de carga adicionales en un transporte."/>	
	<entry name="VeryBulkyFlavor" value="<string name='Traits/BulkyFlavor'/>"/>	
	<entry name="VoidShield" value="Escudo de Vacío"/>
	<entry name="VoidShieldDescription" value="Aumenta la reducción de daño por ataque a distancia."/>
	<entry name="VoidShieldFlavor" value="<string name='Actions/AstraMilitarum/ProjectedVoidShieldFlavor'/>"/>
	<entry name="VoxCaster" value="Vocoemisor"/>
	<entry name="VoxCasterDescription" value="Reduce la perdida de moral."/>
	<entry name="VoxCasterFlavor" value="Un Vocoemisor es un fiable sistema de comunicaciones conectado la red de mando táctico por un conjunto de rayos."/>
	<entry name="Waaagh" value="¡Waaagh!"/>
	<entry name="WaaaghDescription" value="Aumenta los ataques."/>
	<entry name="WaaaghFlavor" value="El ¡Waaagh! es jolgorio, una cruzada, un inmenso poder psíquico, un aura de creencia tangible y, quizá, los propios dioses orkos, todo unido en un rugido desgarrador de placentera agresión que empuja a los orkos a través de la galaxia hacia el combate. Es esencial para ser un orko."/>
	<entry name="Walker" value="Bípode"/>
	<entry name="WalkerDescription" value="<string name='Traits/DozerBladeDescription'/>"/>
	<entry name="Weakened" value="Debilitado"/>
	<entry name="WeakenedDescription" value="Reduce el movimiento y el daño."/>
	<entry name="WireWeed" value="<string name='Features/WireWeed'/>"/>
	<entry name="WireWeedDescription" value="Realiza daño cada turno."/>
	<entry name="WireWeedFlavor" value="<string name='Features/WireWeedFlavor'/>"/>
	<entry name="Witchfire" value="Fuego Brujo"/>
	<entry name="WitchfireDescription" value="Clasificación."/>
	<entry name="Zzap" value="Zzap"/>
	<entry name="ZzapFlavor" value="El kañón zzap dispara proyectiles de luz inestables. Tienen el poder de atravesar sobre el casco de incluso el más pesado vehículo enemigo en medio de una lluvia de chispas, pero tiene la tendencia a sobrecargarse y electrocutar al artillero que lo opera."/>
	<entry name="Zealot" value="Fanático"/>
	<entry name="ZealotDescription" value="Reduce la pérdida de moral, aumenta el daño de melé y proporciona inmunidad al miedo y a quedarse clavada."/>
	<entry name="ZealotFlavor" value="Los Fanáticos luchan independientemente de las bajas o del terror de la guerra, están conducidos por sus convicciones."/>
</language>
