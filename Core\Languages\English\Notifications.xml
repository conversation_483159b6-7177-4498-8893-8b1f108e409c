<?xml version="1.0" encoding="utf-8"?>
<language>
	<!-- %1% = subject, %2% = object -->
	<entry name="CityGrownSummary" value="%1% grown to %2% population."/>
	<entry name="FactionDefeated" value="Faction Defeated"/>
	<entry name="FactionDiscovered" value="Faction Discovered"/>
	<entry name="FactionDiscoveredSummary" value="%1% discovered."/>
	<entry name="FeatureExploredSummary" value="%1% provided %2%."/>
	<entry name="FeatureTypeDiscoveredSummary" value="%1% discovered."/>
	<entry name="LordOfSkullsAppearedSummary" value="%1% appeared."/>
	<entry name="LordOfSkullsDisappearedSummary" value="%1% disappeared."/>
	<entry name="PlayerLost" value="Defeat!"/>
	<entry name="PlayerLostSummary" value="%1% · %2% %3% defeated!"/>
	<entry name="PlayerWon" value="Victory!"/>
	<entry name="PlayerWonSummary" value="%1% · %2% %3% victorious!"/>
	<entry name="PlayerWonElimination" value="<string name='Notifications/PlayerWon'/>"/>
	<entry name="PlayerWonEliminationSummary" value="<string name='Notifications/PlayerWonSummary'/>"/>
	<entry name="PlayerWonQuest" value="<string name='Notifications/PlayerWon'/>"/>
	<entry name="PlayerWonQuestSummary" value="<string name='Notifications/PlayerWonSummary'/>"/>
	<entry name="ProductionCompletedSummary" value="%1% produced %2%."/>
	<entry name="QuestAdded" value="Quest Added"/>
	<entry name="QuestCompleted" value="Quest Completed"/>
	<entry name="QuestUpdated" value="Quest Updated"/>
	<entry name="RegionDiscoveredSummary" value="%1% discovered."/>
	<entry name="ResearchCompleted" value="Research Completed"/>
	<entry name="TileAcquiredSummary" value="%1% acquired tile."/>
	<entry name="TileCapturedSummary" value="%1% captured %2%."/>
	<entry name="TileClearedSummary" value="%1% cleared %2%."/>
	<entry name="UnitAttackedSummary" value="%1% attacked<br/>%2%."/>
	<entry name="UnitCapturedSummary" value="%1% captured<br/>%2%."/>
	<entry name="UnitKilledSummary" value="%1% killed<br/>%2%."/>
	<entry name="UnitGainedTraitSummary" value="%1% gained %2%."/>
	<entry name="UnitTransformedSummary" value="%1% transformed into %2%."/>
	<entry name="UnitTypeDiscoveredSummary" value="%1% discovered."/>
	<entry name="UnitUsedActionOnSummary" value="%1% used %2% on %3%."/>
</language>
