<?xml version="1.0" encoding="utf-8"?>
<world:worldHUD extends="HUD" showEffect="FadeIn" hideEffect="FadeOut">
	<container layout.direction="TopToBottom" preferredSize="FillParent FillParent">
		<world:topBar name="topBar" preferredSize="FillParent 31"/>
		<container name="panelsContainer" preferredSize="FillParent FillParent"
			weights="FillAll 1">
			<container name="leftPanelsContainer" layout="Absolute" preferredSize="444 FillParent">
				<container layout.direction="TopToBottom" preferredSize="382 FillParent">
					<world:cityNameContainer preferredSize="FillParent WrapContent"/>
					<world:cityResourcesPanel name="cityResourcesPanel"
						preferredSize="FillParent WrapContent"/>
					<world:cityBuildingsPanel name="cityBuildingsPanel"
						preferredSize="FillParent FillParent" weights="FillAll 1"/>
				</container>
				<container layout.alignment="TopLeft" layout.collapseInvisible="1" preferredSize="444 FillParent">
					<world:playerActionsPanel name="playerActionsPanel"
						preferredSize="FillParent WrapContent"/>
						<!-- preferredSize="WrapContent WrapContent" minSize="222 0" maxSize="444 0"/> -->
					<world:unclaimedItemsPanel name="unclaimedItemsPanel"
						preferredSize="FillParent WrapContent"/>
						<!-- preferredSize="WrapContent WrapContent" minSize="222 0" maxSize="444 0"/> -->
				</container>
				<container layout.alignment="BottomLeft" preferredSize="FillParent FillParent">
					<world:battlePanel name="battlePanel"
						preferredSize="WrapContent WrapContent"/>
					<world:selectionPanel name="selectionPanel"
						preferredSize="FillParent 282"/>
				</container>
			</container>
			<container layout="Relative" layout.alignment="BottomLeft"
				layout.collapseInvisible="1" preferredSize="WrapContent FillParent">
				<container layout="Relative" layout.alignment="BottomLeft"
					layout.collapseInvisible="1" preferredSize="FillParent FillParent" margin="-62 0">
					<world:cityActionsPanel name="cityActionsPanel"
						preferredSize="206 WrapContent"/>
				</container>
				<world:itemsPanel name="itemsPanel" preferredSize="156 WrapContent"/>
				<world:cargoPanel name="cargoPanel" preferredSize="180 WrapContent"/>
			</container>
			<container layout="Relative" layout.alignment="BottomRight" 
				layout.collapseInvisible="1" preferredSize="FillParent FillParent" weights="1 FillAll">
				<chatContainer name="chatContainer" preferredSize="356 210"/>
				<world:debugPanel name="debugPanel" preferredSize="420 350"/>
				<world:mixerPanel name="mixerPanel" preferredSize="420 460"/>
			</container>
			<container layout.alignment="BottomRight" layout.direction="TopToBottom"
				layout.collapseInvisible="1" preferredSize="360 FillParent">
				<container layout.alignment="TopRight"
						layout.collapseInvisible="1" 
						preferredSize="FillParent WrapContent">
					<imageButton name="markTileButton" hint="<string name='GUI/MarkTileHint'/>" image.texture="Icons/MarkTile"
							unpressedTexture="GUI/ShadowedButton"
							pressedTexture="GUI/ShadowedButton" showEffect="FadeIn" hideEffect="FadeOut"/>
					<imageButton name="statsButton" hint="<style name='Heading'/><string name='GUI/Stats'/> [<control name='Controls/ToggleStatsPanel'/>]" image.texture="Icons/Stats"
							unpressedTexture="GUI/ShadowedButton"
							pressedTexture="GUI/ShadowedButton" showEffect="FadeIn" hideEffect="FadeOut" control="Controls/ToggleStatsPanel"/>
					<imageButton name="researchButton" hint="<style name='Heading'/><string name='GUI/Research'/> [<control name='Controls/OpenResearchScreen'/>]" image.texture="Icons/Research"
							unpressedTexture="GUI/ShadowedButton"
							pressedTexture="GUI/ShadowedButton" showEffect="FadeIn" hideEffect="FadeOut" control="Controls/OpenResearchScreen"/>
					<imageButton name="questsButton" hint="<style name='Heading'/><string name='GUI/Quests'/> [<control name='Controls/OpenQuestsScreen'/>]" image.texture="Icons/Quests"
							unpressedTexture="GUI/ShadowedButton"
							pressedTexture="GUI/ShadowedButton" showEffect="FadeIn" hideEffect="FadeOut" control="Controls/OpenQuestsScreen"/>
					<imageButton name="compendiumButton" hint="<style name='Heading'/><string name='GUI/Compendium'/> [<control name='Controls/OpenCompendiumScreen'/>]" image.texture="Icons/Compendium"
							unpressedTexture="GUI/ShadowedButton"
							pressedTexture="GUI/ShadowedButton" showEffect="FadeIn" hideEffect="FadeOut" control="Controls/OpenCompendiumScreen"/>
				</container>
				<world:researchingTechnologyContainer
					name="researchingTechnologyContainer" preferredSize="FillParent WrapContent" pressedSound="Interface/Press"/>
				<world:questList name="questList" preferredSize="FillParent FillParent"
					weights="FillAll 1"/>
				<world:notificationList name="notificationList"
					preferredSize="FillParent FillParent" weights="FillAll 2"/>
				<container layout.gap="0 4" layout.direction="TopToBottom"
					preferredSize="FillParent WrapContent">
					<container preferredSize="FillParent 44">
						<world:endTurnButton name="endTurnButton"
							preferredSize="FillParent FillParent" weights="1 FillAll"/>
						<world:forceEndTurnButton name="forceEndTurnButton"
							preferredSize="44 44"/>
					</container>
					<world:minimapPanel name="minimapPanel" preferredSize="FillParent 280"/>
				</container>
			</container>
		</container>
	</container>
	<world:overlay name="overlay" preferredSize="FillParent FillParent"/>
	<container layout="Relative" layout.alignment="TopCenter" preferredSize="FillParent FillParent" stayOnTop="1">
		<contentContainer surface.texture="GUI/ShadowedSurface" name="playersReadyContainer" preferredSize="WrapContent WrapContent" margin="0 34; 0 0" content.margin="2 2"/>
		<container layout.alignment="BottomCenter" preferredSize="FillParent 200" name="processingContainer" visible="0" showEffect="FadeInProcessing" hideEffect="FadeOutProcessing">
			<label name="processingLabel" alignment="BottomCenter" preferredSize="FillParent WrapContent" style="<style name='ShadowedMenuTitle'/>" interactive="0"/>
			<image texture="GUI/HeadlineBar" color="GUI/HeadlineBar" interactive="0"/>
		</container>
		<container layout="Relative" layout.alignment="BottomCenter" preferredSize="FillParent 300">
			<label name="statusLabel" alignment="BottomCenter" preferredSize="FillParent WrapContent" style="<style name='ShadowedMenuTitle'/>" visible="0" interactive="0" effect="Blink" showEffect="FadeInLeft" hideEffect="FadeOutRight"/>
		</container>
		<container layout="Relative" layout.alignment="BottomCenter" preferredSize="FillParent 350">
			<label name="messageLabel" alignment="BottomCenter" preferredSize="FillParent WrapContent" style="<style name='ShadowedMenuTitle'/>" visible="0" interactive="0" hideEffect="Message"/>
		</container>
		<container layout="Relative" layout.alignment="MiddleCenter" preferredSize="FillParent FillParent">
			<world:reportPanel name="reportPanel"/>
			<world:statsPanel name="statsPanel"/>
		</container>
	</container>
</world:worldHUD>
