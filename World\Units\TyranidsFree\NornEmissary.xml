<?xml version="1.0" encoding="utf-8"?>
<unit dlc="Supplement16">
	<model>
		<unit mesh="Units/Tyranids/NornEmissary"
				material="Units/Tyranids/NornEmissary"
				idleAnimation="Units/Tyranids/NornEmissaryIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				scale="1 1 1"
				bloodBone="Chest"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="ScythingTalons">
			<model>
				<weapon fireInterval="10.0"/>
			</model>
		</weapon>
		<weapon name="RendingClaws">
			<model>
				<weapon fireInterval="10.0"/>
			</model>
		</weapon>
		<weapon name="PsychicTendrilNeuroblast" slotName="PsychicTendrilNeuroblast" enabled="0">
			<model>
				<beamWeapon muzzleBone="Head"/>
			</model>	
		</weapon>
		<weapon name="PsychicTendrilNeurolance" slotName="PsychicTendrilNeurolance" enabled="0">
			<model>
				<beamWeapon muzzleBone="Head"/>
			</model>	
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="6.0"/> <!-- %biomassUpkeep base tier=9 factor=1 -->
				<biomassCost base="120.0"/> <!-- %biomassCost base tier=9 factor=1 -->
				<hitpointsMax base="36.0"/> <!-- %hitpointsMax base toughness=6 wounds=6 -->
				<meleeAccuracy base="10"/> <!-- %meleeAccuracy base weaponSkill=5 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="3"/> <!-- %strengthDamage base strength=6 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=10 -->
				<movementMax base="3"/>
				<productionCost base="54.0"/> <!-- %productionCost base tier=9 factor=1 -->
				<rangedAccuracy base="10"/> <!-- %rangedAccuracy base ballisticSkill=5 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseSupportScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/NornEmissaryAttack"
						chargeAnimation="Units/Tyranids/NornEmissaryCharge"
						meleeAnimation="Units/Tyranids/NornEmissaryMelee"
						meleeBeginSwing="0.1"/>
			</model>
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/NornEmissaryDie0"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="1.35"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/NornEmissaryMove"
						sound="Units/Tyranids/CarnifexMove"
						soundCount="2"/>
			</model>
		</move>
		<genericUnitAbility name="Tyranids/SynapseCreature" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/SynapseLink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<genericUnitAbility name="Tyranids/ShadowInTheWarp" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1" excludeRadius="0">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/ShadowInTheWarp"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<useWeapon cooldown="2"
				psychicPower="1"
				weaponSlotName="PsychicTendrilNeuroblast">
			<model>
				<action animation="Units/Tyranids/NornEmissaryAbility"
						beginFire="0.4"
						endFire="2.5"/>
			</model>
			<sharedCooldowns>
				<action name="UseWeapon/PsychicTendrilNeurolance"/>
			</sharedCooldowns>
		</useWeapon>
		<useWeapon cooldown="2"
				psychicPower="1"
				weaponSlotName="PsychicTendrilNeurolance">
			<model>
				<action animation="Units/Tyranids/NornEmissaryAbility"
						beginFire="0.4"
						endFire="2.5"/>
			</model>
			<sharedCooldowns>
				<action name="UseWeapon/PsychicTendrilNeuroblast"/>
			</sharedCooldowns>
		</useWeapon>
		<singularPurpose cooldown="10"
				consumedActionPoints="0" 
				consumedMovement="0"
				requiredActionPoints="0"
				name="Tyranids/SingularPurpose"
				icon="Traits/Tyranids/SingularPurpose">
			<model>
				<action animation="Units/Tyranids/NornEmissaryAbility"
						sound="Actions/SingularPurpose"/>
			</model>
			<beginTargets>
				<target rangeMax="-1">
					<conditions>
						<unit>
							<enemy/>
							<noTrait name="Tyranids/SingularPurpose"/>
						</unit>
					</conditions>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait duration="10" name="Tyranids/SingularPurpose"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</target>
			</beginTargets>
		</singularPurpose>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				requiredActionPoints="0"
				requiredUpgrade="Tyranids/HammerOfWrath">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="HammerOfWrath" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>
	</actions>
	<traits>
		<trait name="Fearless"/>
		<trait name="MoveThroughCover"/>
		<trait name="Smash"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="Psyker"/>
		<trait name="Tyranids/UnnaturalResilience"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="MonstrousCreature"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<!-- <trait name="Relentless"/> -->
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
	</traits>
</unit>