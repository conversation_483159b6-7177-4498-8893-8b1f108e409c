<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="CompendiumFlavor" value="The 41st millenium is an unwelcoming place for all species, whether human or xenos—these tips should help you get your military campaign off the ground."/>
	<entry name="Tip0" value="Camera"/>
	<entry name="Tip0Description" value="Welcome to Gladius! To move the camera, position your mouse over terrain, click and hold the left mouse button, and move your mouse. Once you are comfortable with your surroundings, click OK."/>
	<entry name="Tip1" value="Colonization"/>
	<entry name="Tip1Description" value="This unit allows you to build a city that will be the base for your faction on Gladius. To found your first city, click the “<icon height='20' texture='Icons/Actions/FoundCity'/> Found City” button in the selection panel in the bottom left then select an empty tile. Note that this action will consume the unit."/>
	<entry name="Tip2" value="Colonization—Space Marines"/>
	<entry name="Tip2Description" value="Space Marines deploy most of their crucial infrastructure from orbit. To found your Fortress, which is the backbone of your chapter on Gladius and elsewhere, click the “<icon height='20' texture='Icons/Actions/DeployCity'/> Deploy City” button in the operations panel in the top left and select an empty tile in sight. Note that Space Marines only have one city."/>
	<entry name="Tip3" value="End Turn Button"/>
	<entry name="Tip3Description" value="Take your time to explore the interface. Every element has a hint to provide you with extra information. Whenever you are unsure what to do next, refer to the red task button on the right side of the screen. It will help you focus on the next task before you end your turn."/>
	<entry name="Tip4" value="Exploration"/>
	<entry name="Tip4Description" value="Gladius is full of unknown treasure and vicious alien creatures. As you explore, you will receive discovery notifications that can be opened to reveal more information. Some terrain can be explored for rewards, while other features might hurt your unit. Tread carefully—the planet's life can be ruthless, but if you leave it alone it may spare you."/>
	<entry name="Tip5" value="Movement"/>
	<entry name="Tip5Description" value="Move the selected unit by pressing the right mouse button over a destination tile. Holding the button down will display which path the unit will take. Each unit can only be moved once per turn."/>
	<entry name="Tip6" value="Healing"/>
	<entry name="Tip6Description" value="Your unit has taken <icon height='20' texture='Icons/Attributes/Damage'/> damage and is more likely to die in the next fight. To recover his <icon height='20' texture='Icons/Attributes/Hitpoints'/> hitpoints, he must stay still and not participate in combat. Click the “<icon height='20' texture='Icons/Actions/Heal'/> Hold Position Until Healed” action to wait until he is back at full strength. You can accelerate the healing rate by moving the unit into a friendly city tile."/>
	<entry name="Tip7" value="Quests & Victory"/>
	<entry name="Tip7Description" value="Quests unfold the story of your faction on Gladius Prime. Completing each stage of a quest will provide you with a reward, and completing the last story quest will result in a complete victory. However, that is not the only path—eliminating all rival factions is another way to victory."/>
	<entry name="Tip8" value="Attacking"/>
	<entry name="Tip8Description" value="Hovering the cursor over enemies with a unit selected will display important information about the defender and the weapons used, as well as the expected casualties for the battle. Note that not every weapon can fire at all ranges—unusable weapons are greyed out. Units can only participate in combat once per turn, after which the icon above the unit becomes transparent to indicate it has no action points remaining."/>
	<entry name="Tip9" value="Morale"/>
	<entry name="Tip9Description" value="Your unit has taken a substantial hit to <icon height='20' texture='Icons/Attributes/Morale'/> morale from internal struggles such as the loss of nearby allies. This will negatively impact its performance in combat. You can either retreat or kill some nearby xenos to recover."/>
	<entry name="Tip10" value="Units"/>
	<entry name="Tip10Description" value="Every unit has a variety of statistics and attributes, traits, abilities and weapons, each with their own ranges and effects. Some units might only have <icon height='20' texture='Icons/Traits/Melee'/> melee weapons, but <icon height='20' texture='Icons/Traits/MoveThroughCover'/> quickly move through rough terrain, while others might have <icon height='20' texture='Icons/Actions/AuraOfFear'/> fear auras or unleash powerful <icon height='20' texture='Icons/Traits/Barrage'/> artillery blasts. Move your mouse over the icons and buttons in the selection panel to find out more about the selected unit."/>
	<entry name="Tip11" value="Heroes"/>
	<entry name="Tip11Description" value="This is a powerful <icon height='20' texture='Icons/Traits/Hero'/> hero unit that can manifest extraordinary abilities to vanquish his foes or protect his allies. Choose your first ability by pressing “<icon height='20' texture='Icons/Actions/LevelUp'/> Level Up” and selecting your desired ability. Some abilities might be passive, or have a longer or shorter <icon height='20' texture='Icons/Attributes/Cooldown'/> cooldown. Most abilities can, however, be leveled up multiple times to increase their power."/>
	<entry name="Tip12" value="Items"/>
	<entry name="Tip12Description" value="Hero items can be found from exploration, received from quest rewards or bought at the Jokaero Trader Encampments. Items provide either a passive bonus or give the unit a new ability that can be performed once or multiple times as a free action. Before being able to claim an item you will have to recruit and select a hero unit."/> <!-- Generally don't stack, traits also generally don't. -->
	<entry name="Tip13" value="Open Item Shop Screen"/>
	<entry name="Tip13Description" value="Your hero is standing on an <icon height='20' texture='Icons/Actions/Shop'/> item shop. To open it, click on the “<icon height='20' texture='Icons/Actions/Shop'/> Shop” button."/> 
	<entry name="Tip14" value="Item Shop Screen"/>
	<entry name="Tip14Description" value="In the <icon height='20' texture='Icons/Actions/Shop'/> item shop you can trade <icon height='20' texture='Icons/Attributes/Influence'/> influence in return for items. Simply click on the items you wish to buy or sell. Note that many common items have a limited number of <icon height='20' texture='Icons/Attributes/Uses'/> uses."/>
	<entry name="Tip15" value="Cities"/>
	<entry name="Tip15Description" value="To open the city screen, you can click on the city name or click on the headquarters twice. You can also select the city's defenses by clicking on the headquarters itself or on the icon above the city. All cities have limited firepower and will defend themselves when attacked. Make sure to protect your city at all costs—if you lose all your cities you are defeated!"/>
	<entry name="Tip16" value="City Buildings"/>
	<entry name="Tip16Description" value="Here, buildings are listed with their available actions. Note that all buildings of the same type are grouped together and can only perform one action at a time. Although multiple actions can be queued up, the required resources will be immediately subtracted from your global pool."/>
	<entry name="Tip17" value="Research Screen"/>
	<entry name="Tip17Description" value="The research screen presents your faction's unique technology tree. Begin by selecting a technology to research from Tier 1. You must research two technologies from a tier to unlock the subsequent tier. You can speed up research by constructing additional <icon height='20' texture='Icons/Buildings/AstraMilitarum/Research'/> research buildings in your cities. Note that all progress is lost when you change the currently researching technology."/> <!-- , while shift-clicking will queue up the research -->
	<entry name="Tip18" value="City Resources"/>
	<entry name="Tip18Description" value="Cities gather resources required to fuel your war machinery. The city screen displays all the resources related to the city. Hover your mouse over each resource to find out more."/>
	<entry name="Tip19" value="Acquire Tile"/>
	<entry name="Tip19Description" value="Before you can construct additional buildings, you have to claim more tiles. Go ahead and click “<icon height='20' texture='Icons/Actions/AcquireTile'/> Acquire Tile” and select a tile with 1 or more <icon height='20' texture='Icons/Attributes/BuildingSlots'/> building slots adjacent to your city borders. Acquiring a tile will take a few turns and cost some <icon height='20' texture='Icons/Attributes/Influence'/> influence."/>
	<entry name="Tip20" value="Acquired Resource Tile"/>
	<entry name="Tip20Description" value="Your city has acquired a special resource tile with no <icon height='20' texture='Icons/Attributes/BuildingSlots'/> building slots. While other tiles need to be exploited by constructing buildings on them, special resource tiles grant a city-wide increase to your resource output."/>
	<entry name="Tip21" value="Construct Resource Building"/>
	<entry name="Tip21Description" value="To keep your war machinery in operation you will have to construct specialized resource buildings. Build a resource building like you built the infantry building. The tile on which the building is placed may modify the building's resource output—look for matching resource icons. It's important to not run out of resources otherwise your buildings will begin working at lower efficiency."/>
	<entry name="Tip22" value="Construct Infantry Building"/>
	<entry name="Tip22Description" value="There are free <icon height='20' texture='Icons/Attributes/BuildingSlots'/> building slots in the city: an opportune time to make plans to expand your army. Begin by constructing an infantry production building. Click the blinking building button and select a city tile with an empty <icon height='20' texture='Icons/Attributes/BuildingSlots'/> building slot to start construction there. This will take several turns to complete."/>
	<entry name="Tip23" value="Produce Infantry"/>
	<entry name="Tip23Description" value="You can now produce units. Queue up as many as you like, but watch your resources. Since this is a separate building type, other buildings can keep producing at the same time. If you construct additional infantry production buildings, infantry will complete training twice as fast."/>
	<entry name="Tip24" value="Population"/>
	<entry name="Tip24Description" value="Every building except the headquarters requires one <icon height='20' texture='Icons/Attributes/PopulationLimit'/> population to operate. You'll have to research and construct <icon height='20' texture='Icons/Buildings/AstraMilitarum/Housing'/> housing to increase your population <icon height='20' texture='Icons/Attributes/Growth'/> growth and limit. If you run into trouble due to a shortage of population or other resources, you can disable some buildings to let the rest operate at full efficiency by clicking the <icon height='20' texture='Icons/DisableBuilding'/> button next to each building group."/>
	<entry name="Tip25" value="Loyalty"/>
	<entry name="Tip25Description" value="If your city's <icon height='20' texture='Icons/Attributes/Loyalty'/> loyalty drops below 0, it will negatively impact building efficiency. You will want to construct some buildings that provide loyalty to keep it positive and get an efficiency boost across the whole city."/>
	<entry name="Tip26" value="Global Pooled Resources"/>
	<entry name="Tip26Description" value="Vital resources like <icon height='20' texture='Icons/Attributes/Food'/> food, <icon height='20' texture='Icons/Attributes/Ore'/> ore, <icon height='20' texture='Icons/Attributes/Energy'/> energy and <icon height='20' texture='Icons/Attributes/Influence'/> influence are accumulated and shared globally. Every city provides and consumes these resources from the same pool, allowing you to fully specialize cities and even stock up on resources for harsher times."/>
	<entry name="Tip27" value="City Edicts"/>
	<entry name="Tip27Description" value="Edicts are city-wide effects that can be issued by expending influence. Multiple can be active at a time, but each activation lasts only a limited amount of time. Timing these correctly can help you prevail on Gladius."/>
	<entry name="Tip28" value="Artefacts"/>
	<entry name="Tip28Description" value="Something has attracted objects with extraordinary powers to Gladius, and the surface is littered with Old One tech of all sizes and kinds. While some artefact items may be bought, bigger artefact units must be captured from the tentacles of Enslavers. Once all adjacent units holding control of an artefact have been killed, the artefact can be captured by right-clicking on it with a unit selected."/>
	<entry name="Tip29" value="Operations"/>
	<entry name="Tip29Description" value="Space Marines are able to issue strategic and tactical operations by expending influence. One such operation is the deployment of a Fortress of Redemption—a stationary unit that secures adjacent special resource features. Placement of Fortresses of Redemption is key for Space Marines to gather resources beyond their city's reach."/>
	<entry name="Tip30" value="Stationary Unit"/>
	<entry name="Tip30Description" value="Some units cannot move but can still perform actions. If they have weapons, like the city headquarters, they can attack. To perform an attack move your mouse over enemies and right-click."/>
	<entry name="Tip31" value="Ruins of Vaul"/>
	<entry name="Tip31Description" value="There are Ruins of Vaul in sight. These ruins provide rewards for the first player exploring them. Some even provide powerful ancient items that can be later equipped by heroes to make them stronger or excel at specific encounters."/>
	<entry name="Tip32" value="Enslaved"/>
	<entry name="Tip32Description" value="Your unit has been taken over by Enslavers! Kill the Enslaver unit to release your troops from their grasp!"/>
	<entry name="Tip33" value="Heavy Weapon"/>
	<entry name="Tip33Description" value="This infantry unit has a heavy weapon equipped. Heavy weapons carried by infantry suffer an accuracy penalty if the unit has moved."/>
	<entry name="Tip34" value="Rapid Fire"/>
	<entry name="Tip34Description" value="This unit has a rapid fire weapon equipped. Units with rapid fire weapons are able to cause much more damage at close range, but can still fire from further away. It might be worth moving closer before attacking."/>
	<entry name="Tip35" value="Powerful Creatures"/>
	<entry name="Tip35Description" value="There are some frighteningly powerful creatures on Gladius Prime. It would be wise to gather a sizable force before engaging these beasts."/>
	<entry name="Tip36" value="Gladius Compendium"/>
	<entry name="Tip36Description" value="The Compendium stores a collection of useful game information. You can read up on factions, buildings, units, re-read tips and more. You can open it by clicking the question mark in the top right corner of the screen."/>
	<entry name="Tip37" value="Out of Resources"/>
	<entry name="Tip37Description" value="You are low on resources! Move your mouse over the resources on the top left of the screen to find out why. Perhaps it would help to disable a building, construct a building or two, or establish a new city."/>
	<entry name="Tip38" value="Colonization—Necrons"/>
	<entry name="Tip38Description" value="Canoptek Spyders allow you to erect a city that will form the backbone of your dynasty on Gladius. To found your first city, click the “<icon height='20' texture='Icons/Actions/FoundCity'/> Found City” button in the selection panel in the bottom left and select an empty tile with a Necron Tomb. Note that this action will consume the unit."/>
	<entry name="Tip39" value="Clear Tile"/>
	<entry name="Tip39Description" value="You've acquired a tile with <icon height='20' texture='Icons/Features/WireWeed'/> Wire Weed on it. This razor-sharp weed damages units and prevents construction of buildings. To begin clearing the tile, click the “<icon height='20' texture='Icons/Actions/ClearTile'/> Clear Tile” action and select the tile with the Wire Weed."/>
	<entry name="Tip40" value="Weak Units"/>
	<entry name="Tip40Description" value="Units like Guardsmen are very weak by themselves. With such vulnerable units, it's advisable to keep multiple units close together so they can assist each other—especially when venturing into unexplored territory."/>
	<entry name="Tip41" value="Good Luck!"/>
	<entry name="Tip41Description" value="You are now able to produce new units, maintain your economy and understand the basic concepts of the game. More tips and suggestions will be shown throughout your interactions, but you will be left to explore the world of Gladius Prime and discover its mysteries alone. Survival is no birthright, but a prize wrested from an uncaring galaxy by forgotten heroes."/>
	<entry name="Tip42" value="Claim Item"/>
	<entry name="Tip42Description" value="To claim an item and transfer it to your hero click on it in the unclaimed items panel in the top left. After claiming an item, you can use it at will, just like abilities. You can also transfer it to another hero by dragging it out. Each hero can only have a limited number of items at a time."/>
	<entry name="Tip43" value="Terrain"/>
	<entry name="Tip43Description" value="Terrain affects units in a variety of ways. Forests and Imperial ruins have an increased movement cost for ground units and make it harder to see and fire across. Height differences may also prevent your units from having a clear line of fire. Tiles marked with red indicate enemies that can be attacked from your current position."/> <!-- Click OK and hover over a tile marked with a red outline. -->
	<entry name="Tip44" value="Unit Abilities"/>
	<entry name="Tip44Description" value="Some units have extra abilities that can be utilized to gain an edge in combat. Use an ability by clicking its button and selecting a target with the left mouse button. Using an ability may consume the unit's action point and movement, preventing the unit from performing any further movement or attacks this turn."/>
	<entry name="Tip45" value="Overwatch"/>
	<entry name="Tip45Description" value="Units with action points remaining that have ranged weapons will automatically perform <icon height='20' texture='Icons/Actions/Overwatch'/> overwatch attacks when an enemy unit enters their attack range unless the enemy unit has <icon height='20' texture='Icons/Traits/Infiltrate'/> infiltrate."/>
	<entry name="Tip46" value="Outposts"/>
	<entry name="Tip46Description" value="Tiles with special resource features can be captured by moving a unit on them. This will construct an <icon height='20' texture='Icons/Features/Outpost'/> outpost, providing you with a small but important bonus to your resource output and giving the unit stationed there increased damage reduction and healing rate. Defend your outposts or they will be taken over by other factions or the Gladius wildlife."/>
	<entry name="Tip47" value="Transports: Embarking"/>
	<entry name="Tip47Description" value="This unit is able to carry infantry units as cargo. To embark, move the unit you wish to carry onto the transport's tile. Embarked units will normally not be able to disembark in the turn they have embarked."/>
	<entry name="Tip48" value="Transports: Disembarking"/>
	<entry name="Tip48Description" value="You have a unit embarked on this transport. To disembark it, click on the cargo to select it and then right-click the destination tile. You can also rearrange cargo by dragging it into different slots."/>
	<entry name="Tip49" value="Instinctive Behaviour"/>
	<entry name="Tip49Description" value="Many Tyranid units will suffer from instinctive behaviour while not in range of a synapse creature or a Tyranid city. Termagants have <icon height='20' texture='Icons/Traits/Tyranids/InstinctiveBehaviourLurk'/> instinctive behaviour which causes them to lose morale each turn. You can use the unit's <icon height='20' texture='Icons/Actions/Tyranids/InstinctiveBehaviourOverride'/> override instinctive behaviour ability to temporarily fend off the effects."/>
	<entry name="Tip50" value="Reclamation"/>
	<entry name="Tip50Description" value="Tyranid units in cities can be reclaimed to regain biomass and accelerate a building's production. Click “<icon height='20' texture='Icons/Actions/Tyranids/ReclaimUnit'/> Reclaim Unit” to show each unit's reclaim value and select a target. The reclamation process is instantaneous and can be executed in parallel with production."/>
	<entry name="Tip51" value="Feed Upon The Planet"/>
	<entry name="Tip51Description" value="Tyranid cities consume all tile <icon height='20' texture='Icons/Attributes/Biomass'/> biomass when acquiring a tile. This biomass is added to your pool of resources and is then forever depleted from the tile. <icon height='20' texture='Icons/Units/Tyranids/Malanthrope'/> Malanthropes, which are at the core of any Tyranid economy, are able to consume tiles across all of Gladius, even in faraway, remote locations."/>
	<entry name="Tip52" value="Unholy Rites"/>
	<entry name="Tip52Description" value="Unholy Rites are powerful city-wide effects that can be activated by expending population. Coordinating the rites together with sacrificing Chaos Cultists to increase population growth is a key element to getting the most out of your economy."/>	
	<entry name="Tip53" value="Champions of Chaos"/>
	<entry name="Tip53Description" value="Champions of Chaos have a chance to transform into Chaos Spawn or a mighty Daemon Prince when they kill an enemy. Additionally, after Chaos Boons are researched, each Champion of Chaos can gain permanent buffs. Be careful who you deal final blows with."/>
	<entry name="Tip54" value="Marks of Chaos"/>
	<entry name="Tip54Description" value="Infantry units can be marked by one of the Dark Gods to increase the unit's powers. Only a single mark can ever exist on a unit, and it can never be removed. Later in the game, Icons of Chaos can be used to increase the unit's favor with the Dark Gods even further."/>
	<entry name="Tip55" value="Mutated Beyond Reason"/>
	<entry name="Tip55Description" value="A Chaos Spawn may have several sets of limbs, crab claws or tentacles, armour that bulges beneath the skin, tattered and useless wings, clumps of eye-stalks that wave like grass in a wind, a sinewy prehensile neck or a gaping maw of needle-thin teeth. And each of those can change at a whim, granting the hideous creature different properties each turn. Other Chaos entities can be similarly manic, or simply at the whims of the unpredictable Ruinous Powers."/>
	<entry name="Tip56" value="Unleashed Wildlife"/>
	<entry name="Tip56Description" value="Neutral units may not attack you as you move around them, but attacking them will surely make them angry."/>
	
<!-- Zone of control: trigger when an enemy is 2 away, after attacking and healing has been shown. -->
<!-- Set rally point (when unit count above 3?). -->
<!-- Waaagh! -->
<!-- Rapid Rise -->
<!-- Necrodermis Repair -->
<!-- Rename units, rename city. -->
<!-- New Game Screen tips. -->
<!-- Query Space Marines to deploy city before ending turn—end turn button. -->

<!-- Flyers. -->
<!-- Hero death—resurrect, items when hero below 50%. -->
</language>
