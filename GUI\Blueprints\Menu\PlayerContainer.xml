<?xml version="1.0" encoding="utf-8"?>
<menu:playerContainer extends="ContentContainer" content.layout="Absolute" preferredSize="180 265" content.margin="0 0">
	<image name="factionImage" preferredSize="FillParent FillParent"/>
	<container layout.alignment="BottomRight" layout.gap="0 0" layout.direction="TopToBottom" preferredSize="FillParent FillParent">
		<contentContainer name="colorContainer" content.margin="0 0" content.layout.collapseInvisible="1" content.layout.gap="0 0" preferredSize="FillParent 28" surface.texture="GUI/ColoredSurface" surface.color="0 0 0 0.5" surface.padding="" surface.delta="">
			<menu:playerContainerDropList name="colorDropList" preferredSize="28 FillParent" list.preferredSize="44 WrapContent"/>
			<textBox maxLength="50" name="nameTextBox" content.margin="0 0" label.style="<style name='ShadowedHeading'/>" preferredSize="FillParent FillParent" weights="1 FillAll" surface.color="0 0 0 0"/>
			<playerStatusContainer name="statusContainer" preferredSize="WrapContent FillParent"/>
		</container>
		<menu:playerContainerDropList name="factionDropList" preferredSize="FillParent 28" list.maxSize="0 600"/>
		<container name="selectFactionContainer" preferredSize="FillParent FillParent" weights="FillAll 1" pressedSound="Interface/Press"/>
		<container layout.gap="0 0" layout.direction="TopToBottom" layout.collapseInvisible="1" preferredSize="FillParent WrapContent">
			<menu:playerContainerDropList name="teamDropList" preferredSize="FillParent 28">
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team0Hint'/>" name="0" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team0'/>"/>
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team1'/>" name="1" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team1'/>"/>
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team2'/>" name="2" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team2'/>"/>
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team3'/>" name="3" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team3'/>"/>
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team4'/>" name="4" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team4'/>"/>
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team5'/>" name="5" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team5'/>"/>
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team6'/>" name="6" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team6'/>"/>
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team7'/>" name="7" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team7'/>"/>
				<labeledListItem hint="<string name='GUI/SelectTeamHint'/><br/><br/><string name='GUI/Team8'/>" name="8" label.style="<style name='ShadowedHeading'/>" label.caption="<string name='GUI/Team8'/>"/>
			</menu:playerContainerDropList>
			<menu:playerContainerDropList name="difficultyDropList" preferredSize="FillParent 28"/>
		</container>
	</container>
</menu:playerContainer>
