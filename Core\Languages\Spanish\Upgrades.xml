<?xml version="1.0" encoding="utf-8"?> 
<language>

	<!-- Buildings -->
 	<entry name="AdeptusMechanicus/Aircraft" value="<string name='Buildings/AdeptusMechanicus/Aircraft'/>"/>
 	<entry name="AdeptusMechanicus/AircraftDescription" value="<string name='Buildings/AdeptusMechanicus/AircraftDescription'/>"/>
 	<entry name="AdeptusMechanicus/AircraftFlavor" value="<string name='Buildings/AdeptusMechanicus/AircraftFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Construction" value="<string name='Buildings/AdeptusMechanicus/Construction'/>"/>
 	<entry name="AdeptusMechanicus/ConstructionDescription" value="<string name='Buildings/AdeptusMechanicus/ConstructionDescription'/>"/>
 	<entry name="AdeptusMechanicus/ConstructionFlavor" value="<string name='Buildings/AdeptusMechanicus/ConstructionFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Heroes" value="<string name='Buildings/AdeptusMechanicus/Heroes'/>"/>
 	<entry name="AdeptusMechanicus/HeroesDescription" value="<string name='Buildings/AdeptusMechanicus/HeroesDescription'/>"/>
 	<entry name="AdeptusMechanicus/HeroesFlavor" value="<string name='Buildings/AdeptusMechanicus/HeroesFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Housing" value="<string name='Buildings/AdeptusMechanicus/Housing'/>"/>
 	<entry name="AdeptusMechanicus/HousingDescription" value="<string name='Buildings/AdeptusMechanicus/HousingDescription'/>"/>
 	<entry name="AdeptusMechanicus/HousingFlavor" value="<string name='Buildings/AdeptusMechanicus/HousingFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Loyalty" value="<string name='Buildings/AdeptusMechanicus/Loyalty'/>"/>
 	<entry name="AdeptusMechanicus/LoyaltyDescription" value="<string name='Buildings/AdeptusMechanicus/LoyaltyDescription'/>"/>
 	<entry name="AdeptusMechanicus/LoyaltyFlavor" value="<string name='Buildings/AdeptusMechanicus/LoyaltyFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Vehicles" value="<string name='Buildings/AdeptusMechanicus/Vehicles'/>"/>
 	<entry name="AdeptusMechanicus/VehiclesDescription" value="<string name='Buildings/AdeptusMechanicus/VehiclesDescription'/>"/>
 	<entry name="AdeptusMechanicus/VehiclesFlavor" value="<string name='Buildings/AdeptusMechanicus/VehiclesFlavor'/>"/>
	<entry name="AstraMilitarum/Aircraft" value="<string name='Buildings/AstraMilitarum/Aircraft'/>"/>
	<entry name="AstraMilitarum/AircraftDescription" value="<string name='Buildings/AstraMilitarum/AircraftDescription'/>"/>
	<entry name="AstraMilitarum/AircraftFlavor" value="<string name='Buildings/AstraMilitarum/AircraftFlavor'/>"/>
	<entry name="AstraMilitarum/Construction" value="<string name='Buildings/AstraMilitarum/Construction'/>"/>
	<entry name="AstraMilitarum/ConstructionDescription" value="<string name='Buildings/AstraMilitarum/ConstructionDescription'/>"/>
	<entry name="AstraMilitarum/ConstructionFlavor" value="<string name='Buildings/AstraMilitarum/ConstructionFlavor'/>"/>
	<entry name="AstraMilitarum/Heroes" value="<string name='Buildings/AstraMilitarum/Heroes'/>"/>
	<entry name="AstraMilitarum/HeroesDescription" value="<string name='Buildings/AstraMilitarum/HeroesDescription'/>"/>
	<entry name="AstraMilitarum/HeroesFlavor" value="<string name='Buildings/AstraMilitarum/HeroesFlavor'/>"/>
	<entry name="AstraMilitarum/Housing" value="<string name='Buildings/AstraMilitarum/Housing'/>"/>
	<entry name="AstraMilitarum/HousingDescription" value="<string name='Buildings/AstraMilitarum/HousingDescription'/>"/>
	<entry name="AstraMilitarum/HousingFlavor" value="<string name='Buildings/AstraMilitarum/HousingFlavor'/>"/>
	<entry name="AstraMilitarum/Loyalty" value="<string name='Buildings/AstraMilitarum/Loyalty'/>"/>
	<entry name="AstraMilitarum/LoyaltyDescription" value="<string name='Buildings/AstraMilitarum/LoyaltyDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyFlavor" value="<string name='Buildings/AstraMilitarum/LoyaltyFlavor'/>"/>
	<entry name="AstraMilitarum/Psykers" value="<string name='Buildings/AstraMilitarum/Psykers'/>"/>
	<entry name="AstraMilitarum/PsykersDescription" value="<string name='Buildings/AstraMilitarum/PsykersDescription'/>"/>
	<entry name="AstraMilitarum/PsykersFlavor" value="<string name='Buildings/AstraMilitarum/PsykersFlavor'/>"/>
	<entry name="AstraMilitarum/Upgrades" value="<string name='Buildings/AstraMilitarum/Upgrades'/>"/>
	<entry name="AstraMilitarum/UpgradesDescription" value="<string name='Buildings/AstraMilitarum/UpgradesDescription'/>"/>
	<entry name="AstraMilitarum/UpgradesFlavor" value="<string name='Buildings/AstraMilitarum/UpgradesFlavor'/>"/>
	<entry name="AstraMilitarum/Vehicles" value="<string name='Buildings/AstraMilitarum/Vehicles'/>"/>
	<entry name="AstraMilitarum/VehiclesDescription" value="<string name='Buildings/AstraMilitarum/VehiclesDescription'/>"/>
	<entry name="AstraMilitarum/VehiclesFlavor" value="<string name='Buildings/AstraMilitarum/VehiclesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Aircraft" value="<string name='Buildings/ChaosSpaceMarines/Aircraft'/>"/>
 	<entry name="ChaosSpaceMarines/AircraftDescription" value="<string name='Buildings/ChaosSpaceMarines/AircraftDescription'/>"/>
 	<entry name="ChaosSpaceMarines/AircraftFlavor" value="<string name='Buildings/ChaosSpaceMarines/AircraftFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Construction" value="<string name='Buildings/ChaosSpaceMarines/Construction'/>"/>
 	<entry name="ChaosSpaceMarines/ConstructionDescription" value="<string name='Buildings/ChaosSpaceMarines/ConstructionDescription'/>"/>
 	<entry name="ChaosSpaceMarines/ConstructionFlavor" value="<string name='Buildings/ChaosSpaceMarines/ConstructionFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Heroes" value="<string name='Buildings/ChaosSpaceMarines/Heroes'/>"/>
 	<entry name="ChaosSpaceMarines/HeroesDescription" value="<string name='Buildings/ChaosSpaceMarines/HeroesDescription'/>"/>
 	<entry name="ChaosSpaceMarines/HeroesFlavor" value="<string name='Buildings/ChaosSpaceMarines/HeroesFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Housing" value="<string name='Buildings/ChaosSpaceMarines/Housing'/>"/>
 	<entry name="ChaosSpaceMarines/HousingDescription" value="<string name='Buildings/ChaosSpaceMarines/HousingDescription'/>"/>
 	<entry name="ChaosSpaceMarines/HousingFlavor" value="<string name='Buildings/ChaosSpaceMarines/HousingFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Infantry" value="<string name='Buildings/ChaosSpaceMarines/Infantry'/>"/>
 	<entry name="ChaosSpaceMarines/InfantryDescription" value="<string name='Buildings/ChaosSpaceMarines/InfantryDescription'/>"/>
 	<entry name="ChaosSpaceMarines/InfantryFlavor" value="<string name='Buildings/ChaosSpaceMarines/InfantryFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Loyalty" value="<string name='Buildings/ChaosSpaceMarines/Loyalty'/>"/>
 	<entry name="ChaosSpaceMarines/LoyaltyDescription" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyDescription'/>"/>
 	<entry name="ChaosSpaceMarines/LoyaltyFlavor" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Vehicles" value="<string name='Buildings/ChaosSpaceMarines/Vehicles'/>"/>
 	<entry name="ChaosSpaceMarines/VehiclesDescription" value="<string name='Buildings/ChaosSpaceMarines/VehiclesDescription'/>"/>
 	<entry name="ChaosSpaceMarines/VehiclesFlavor" value="<string name='Buildings/ChaosSpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Drukhari/Aircraft" value="<string name='Buildings/Drukhari/Aircraft'/>"/>
	<entry name="Drukhari/AircraftDescription" value="<string name='Buildings/Drukhari/AircraftDescription'/>"/>
	<entry name="Drukhari/AircraftFlavor" value="<string name='Buildings/Drukhari/AircraftFlavor'/>"/>
	<entry name="Drukhari/Construction" value="<string name='Buildings/Drukhari/Construction'/>"/>
	<entry name="Drukhari/ConstructionDescription" value="<string name='Buildings/Drukhari/ConstructionDescription'/>"/>
	<entry name="Drukhari/ConstructionFlavor" value="<string name='Buildings/Drukhari/ConstructionFlavor'/>"/>
	<entry name="Drukhari/Heroes" value="<string name='Buildings/Drukhari/Heroes'/>"/>
	<entry name="Drukhari/HeroesDescription" value="<string name='Buildings/Drukhari/HeroesDescription'/>"/>
	<entry name="Drukhari/HeroesFlavor" value="<string name='Buildings/Drukhari/HeroesFlavor'/>"/>
	<entry name="Drukhari/Housing" value="<string name='Buildings/Drukhari/Housing'/>"/>
	<entry name="Drukhari/HousingDescription" value="<string name='Buildings/Drukhari/HousingDescription'/>"/>
	<entry name="Drukhari/HousingFlavor" value="<string name='Buildings/Drukhari/HousingFlavor'/>"/>
	<entry name="Drukhari/Loyalty" value="<string name='Buildings/Drukhari/Loyalty'/>"/>
	<entry name="Drukhari/LoyaltyDescription" value="<string name='Buildings/Drukhari/LoyaltyDescription'/>"/>
	<entry name="Drukhari/LoyaltyFlavor" value="<string name='Buildings/Drukhari/LoyaltyFlavor'/>"/>
	<entry name="Drukhari/Vehicles" value="<string name='Buildings/Drukhari/Vehicles'/>"/>
	<entry name="Drukhari/VehiclesDescription" value="<string name='Buildings/Drukhari/VehiclesDescription'/>"/>
	<entry name="Drukhari/VehiclesFlavor" value="<string name='Buildings/Drukhari/VehiclesFlavor'/>"/>
	<entry name="Eldar/Aircraft" value="<string name='Buildings/Eldar/Aircraft'/>"/>
	<entry name="Eldar/AircraftDescription" value="<string name='Buildings/Eldar/AircraftDescription'/>"/>
	<entry name="Eldar/AircraftFlavor" value="<string name='Buildings/Eldar/AircraftFlavor'/>"/>
	<entry name="Eldar/Construction" value="<string name='Buildings/Eldar/Construction'/>"/>
	<entry name="Eldar/ConstructionDescription" value="<string name='Buildings/Eldar/ConstructionDescription'/>"/>
	<entry name="Eldar/ConstructionFlavor" value="<string name='Buildings/Eldar/ConstructionFlavor'/>"/>
	<entry name="Eldar/Heroes" value="<string name='Buildings/Eldar/Heroes'/>"/>
	<entry name="Eldar/HeroesDescription" value="<string name='Buildings/Eldar/HeroesDescription'/>"/>
	<entry name="Eldar/HeroesFlavor" value="<string name='Buildings/Eldar/HeroesFlavor'/>"/>
	<entry name="Eldar/Housing" value="<string name='Buildings/Eldar/Housing'/>"/>
	<entry name="Eldar/HousingDescription" value="<string name='Buildings/Eldar/HousingDescription'/>"/>
	<entry name="Eldar/HousingFlavor" value="<string name='Buildings/Eldar/HousingFlavor'/>"/>
	<entry name="Eldar/Infantry" value="<string name='Buildings/Eldar/Infantry'/>"/>
	<entry name="Eldar/InfantryDescription" value="<string name='Buildings/Eldar/InfantryDescription'/>"/>
	<entry name="Eldar/InfantryFlavor" value="<string name='Buildings/Eldar/InfantryFlavor'/>"/>
	<entry name="Eldar/Loyalty" value="<string name='Buildings/Eldar/Loyalty'/>"/>
	<entry name="Eldar/LoyaltyDescription" value="<string name='Buildings/Eldar/LoyaltyDescription'/>"/>
	<entry name="Eldar/LoyaltyFlavor" value="<string name='Buildings/Eldar/LoyaltyFlavor'/>"/>
	<entry name="Eldar/Vehicles" value="<string name='Buildings/Eldar/Vehicles'/>"/>
	<entry name="Eldar/VehiclesDescription" value="<string name='Buildings/Eldar/VehiclesDescription'/>"/>
	<entry name="Eldar/VehiclesFlavor" value="<string name='Buildings/Eldar/VehiclesFlavor'/>"/>
	<entry name="Necrons/Aircraft" value="<string name='Buildings/Necrons/Aircraft'/>"/>
	<entry name="Necrons/AircraftDescription" value="<string name='Buildings/Necrons/AircraftDescription'/>"/>
	<entry name="Necrons/AircraftFlavor" value="<string name='Buildings/Necrons/AircraftFlavor'/>"/>
	<entry name="Necrons/Construction" value="<string name='Buildings/Necrons/Construction'/>"/>
	<entry name="Necrons/ConstructionDescription" value="<string name='Buildings/Necrons/ConstructionDescription'/>"/>
	<entry name="Necrons/ConstructionFlavor" value="<string name='Buildings/Necrons/ConstructionFlavor'/>"/>
	<entry name="Necrons/Heroes" value="<string name='Buildings/Necrons/Heroes'/>"/>
	<entry name="Necrons/HeroesDescription" value="<string name='Buildings/Necrons/HeroesDescription'/>"/>
	<entry name="Necrons/HeroesFlavor" value="<string name='Buildings/Necrons/HeroesFlavor'/>"/>
	<entry name="Necrons/Housing" value="<string name='Buildings/Necrons/Housing'/>"/>
	<entry name="Necrons/HousingDescription" value="<string name='Buildings/Necrons/HousingDescription'/>"/>
	<entry name="Necrons/HousingFlavor" value="<string name='Buildings/Necrons/HousingFlavor'/>"/>
	<entry name="Necrons/Loyalty" value="<string name='Buildings/Necrons/Loyalty'/>"/>
	<entry name="Necrons/LoyaltyDescription" value="<string name='Buildings/Necrons/LoyaltyDescription'/>"/>
	<entry name="Necrons/LoyaltyFlavor" value="<string name='Buildings/Necrons/LoyaltyFlavor'/>"/>
	<entry name="Necrons/Vehicles" value="<string name='Buildings/Necrons/Vehicles'/>"/>
	<entry name="Necrons/VehiclesDescription" value="<string name='Buildings/Necrons/VehiclesDescription'/>"/>
	<entry name="Necrons/VehiclesFlavor" value="<string name='Buildings/Necrons/VehiclesFlavor'/>"/>
	<entry name="Orks/Beasts" value="<string name='Buildings/Orks/Beasts'/>"/>
	<entry name="Orks/BeastsDescription" value="<string name='Buildings/Orks/BeastsDescription'/>"/>
	<entry name="Orks/BeastsFlavor" value="<string name='Buildings/Orks/BeastsFlavor'/>"/>
	<entry name="Orks/Colonizers" value="<string name='Buildings/Orks/Colonizers'/>"/>
	<entry name="Orks/ColonizersDescription" value="<string name='Buildings/Orks/ColonizersDescription'/>"/>
	<entry name="Orks/ColonizersFlavor" value="<string name='Buildings/Orks/ColonizersFlavor'/>"/>
	<entry name="Orks/Construction" value="<string name='Buildings/Orks/Construction'/>"/>
	<entry name="Orks/ConstructionDescription" value="<string name='Buildings/Orks/ConstructionDescription'/>"/>
	<entry name="Orks/ConstructionFlavor" value="<string name='Buildings/Orks/ConstructionFlavor'/>"/>
	<entry name="Orks/Heroes" value="<string name='Buildings/Orks/Heroes'/>"/>
	<entry name="Orks/HeroesDescription" value="<string name='Buildings/Orks/HeroesDescription'/>"/>
	<entry name="Orks/HeroesFlavor" value="<string name='Buildings/Orks/HeroesFlavor'/>"/>
	<entry name="Orks/Housing" value="<string name='Buildings/Orks/Housing'/>"/>
	<entry name="Orks/HousingDescription" value="<string name='Buildings/Orks/HousingDescription'/>"/>
	<entry name="Orks/HousingFlavor" value="<string name='Buildings/Orks/HousingFlavor'/>"/>
	<entry name="Orks/Loyalty" value="<string name='Buildings/Orks/Loyalty'/>"/>
	<entry name="Orks/LoyaltyDescription" value="<string name='Buildings/Orks/LoyaltyDescription'/>"/>
	<entry name="Orks/LoyaltyFlavor" value="<string name='Buildings/Orks/LoyaltyFlavor'/>"/>
	<entry name="Orks/Vehicles" value="<string name='Buildings/Orks/Vehicles'/>"/>
	<entry name="Orks/VehiclesDescription" value="<string name='Buildings/Orks/VehiclesDescription'/>"/>
	<entry name="Orks/VehiclesFlavor" value="<string name='Buildings/Orks/VehiclesFlavor'/>"/>
	<entry name="SistersOfBattle/Auxiliaries" value="<string name='Buildings/SistersOfBattle/Auxiliaries'/>"/>
	<entry name="SistersOfBattle/AuxiliariesDescription" value="<string name='Buildings/SistersOfBattle/AuxiliariesDescription'/>"/>
	<entry name="SistersOfBattle/AuxiliariesFlavor" value="<string name='Buildings/SistersOfBattle/AuxiliariesFlavor'/>"/>
	<entry name="SistersOfBattle/Construction" value="<string name='Buildings/SistersOfBattle/Construction'/>"/>
	<entry name="SistersOfBattle/ConstructionDescription" value="<string name='Buildings/SistersOfBattle/ConstructionDescription'/>"/>
	<entry name="SistersOfBattle/ConstructionFlavor" value="<string name='Buildings/SistersOfBattle/ConstructionFlavor'/>"/>
	<entry name="SistersOfBattle/Heroes" value="<string name='Buildings/SistersOfBattle/Heroes'/>"/>
	<entry name="SistersOfBattle/HeroesDescription" value="<string name='Buildings/SistersOfBattle/HeroesDescription'/>"/>
	<entry name="SistersOfBattle/HeroesFlavor" value="<string name='Buildings/SistersOfBattle/HeroesFlavor'/>"/>
	<entry name="SistersOfBattle/Housing" value="<string name='Buildings/SistersOfBattle/Housing'/>"/>
	<entry name="SistersOfBattle/HousingDescription" value="<string name='Buildings/SistersOfBattle/HousingDescription'/>"/>
	<entry name="SistersOfBattle/HousingFlavor" value="<string name='Buildings/SistersOfBattle/HousingFlavor'/>"/>
	<entry name="SistersOfBattle/Loyalty" value="<string name='Buildings/SistersOfBattle/Loyalty'/>"/>
	<entry name="SistersOfBattle/LoyaltyDescription" value="<string name='Buildings/SistersOfBattle/LoyaltyDescription'/>"/>
	<entry name="SistersOfBattle/LoyaltyFlavor" value="<string name='Buildings/SistersOfBattle/LoyaltyFlavor'/>"/>
	<entry name="SistersOfBattle/Vehicles" value="<string name='Buildings/SistersOfBattle/Vehicles'/>"/>
	<entry name="SistersOfBattle/VehiclesDescription" value="<string name='Buildings/SistersOfBattle/VehiclesDescription'/>"/>
	<entry name="SistersOfBattle/VehiclesFlavor" value="<string name='Buildings/SistersOfBattle/VehiclesFlavor'/>"/>
	<entry name="SpaceMarines/Aircraft" value="<string name='Buildings/SpaceMarines/Aircraft'/>"/>
	<entry name="SpaceMarines/AircraftDescription" value="<string name='Buildings/SpaceMarines/AircraftDescription'/>"/>
	<entry name="SpaceMarines/AircraftFlavor" value="<string name='Buildings/SpaceMarines/AircraftFlavor'/>"/>
	<entry name="SpaceMarines/Construction" value="<string name='Buildings/SpaceMarines/Construction'/>"/>
	<entry name="SpaceMarines/ConstructionDescription" value="<string name='Buildings/SpaceMarines/ConstructionDescription'/>"/>
	<entry name="SpaceMarines/ConstructionFlavor" value="<string name='Buildings/SpaceMarines/ConstructionFlavor'/>"/>
	<entry name="SpaceMarines/GeneseedBunker" value="<string name='Buildings/SpaceMarines/GeneseedBunker'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerDescription" value="<string name='Buildings/SpaceMarines/GeneseedBunkerDescription'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerFlavor" value="<string name='Buildings/SpaceMarines/GeneseedBunkerFlavor'/>"/>
	<entry name="SpaceMarines/Heroes" value="<string name='Buildings/SpaceMarines/Heroes'/>"/>
	<entry name="SpaceMarines/HeroesDescription" value="<string name='Buildings/SpaceMarines/HeroesDescription'/>"/>
	<entry name="SpaceMarines/HeroesFlavor" value="<string name='Buildings/SpaceMarines/HeroesFlavor'/>"/>
	<entry name="SpaceMarines/Housing" value="<string name='Buildings/SpaceMarines/Housing'/>"/>
	<entry name="SpaceMarines/HousingDescription" value="<string name='Buildings/SpaceMarines/HousingDescription'/>"/>
	<entry name="SpaceMarines/HousingFlavor" value="<string name='Buildings/SpaceMarines/HousingFlavor'/>"/>
	<entry name="SpaceMarines/Loyalty" value="<string name='Buildings/SpaceMarines/Loyalty'/>"/>
	<entry name="SpaceMarines/LoyaltyDescription" value="<string name='Buildings/SpaceMarines/LoyaltyDescription'/>"/>
	<entry name="SpaceMarines/LoyaltyFlavor" value="<string name='Buildings/SpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="SpaceMarines/Vehicles" value="<string name='Buildings/SpaceMarines/Vehicles'/>"/>
	<entry name="SpaceMarines/VehiclesDescription" value="<string name='Buildings/SpaceMarines/VehiclesDescription'/>"/>
	<entry name="SpaceMarines/VehiclesFlavor" value="<string name='Buildings/SpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Tau/Aircraft" value="<string name='Buildings/Tau/Aircraft'/>"/>
	<entry name="Tau/AircraftDescription" value="<string name='Buildings/Tau/AircraftDescription'/>"/>
	<entry name="Tau/AircraftFlavor" value="<string name='Buildings/Tau/AircraftFlavor'/>"/>
	<entry name="Tau/Construction" value="<string name='Buildings/Tau/Construction'/>"/>
	<entry name="Tau/ConstructionDescription" value="<string name='Buildings/Tau/ConstructionDescription'/>"/>
	<entry name="Tau/ConstructionFlavor" value="<string name='Buildings/Tau/ConstructionFlavor'/>"/>
	<entry name="Tau/Heroes" value="<string name='Buildings/Tau/Heroes'/>"/>
	<entry name="Tau/HeroesDescription" value="<string name='Buildings/Tau/HeroesDescription'/>"/>
	<entry name="Tau/HeroesFlavor" value="<string name='Buildings/Tau/HeroesFlavor'/>"/>
	<entry name="Tau/Housing" value="<string name='Buildings/Tau/Housing'/>"/>
	<entry name="Tau/HousingDescription" value="<string name='Buildings/Tau/HousingDescription'/>"/>
	<entry name="Tau/HousingFlavor" value="<string name='Buildings/Tau/HousingFlavor'/>"/>
	<entry name="Tau/Loyalty" value="<string name='Buildings/Tau/Loyalty'/>"/>
	<entry name="Tau/LoyaltyDescription" value="<string name='Buildings/Tau/LoyaltyDescription'/>"/>
	<entry name="Tau/LoyaltyFlavor" value="<string name='Buildings/Tau/LoyaltyFlavor'/>"/>
	<entry name="Tau/MonstrousCreatures" value="<string name='Buildings/Tau/MonstrousCreatures'/>"/>
	<entry name="Tau/MonstrousCreaturesDescription" value="<string name='Buildings/Tau/MonstrousCreaturesDescription'/>"/>
	<entry name="Tau/MonstrousCreaturesFlavor" value="<string name='Buildings/Tau/MonstrousCreaturesFlavor'/>"/>
	<entry name="Tau/Vehicles" value="<string name='Buildings/Tau/Vehicles'/>"/>
	<entry name="Tau/VehiclesDescription" value="<string name='Buildings/Tau/VehiclesDescription'/>"/>
	<entry name="Tau/VehiclesFlavor" value="<string name='Buildings/Tau/VehiclesFlavor'/>"/>
	<entry name="Tyranids/Aircraft" value="<string name='Buildings/Tyranids/Aircraft'/>"/>
	<entry name="Tyranids/AircraftDescription" value="<string name='Buildings/Tyranids/AircraftDescription'/>"/>
	<entry name="Tyranids/AircraftFlavor" value="<string name='Buildings/Tyranids/AircraftFlavor'/>"/>
	<entry name="Tyranids/Construction" value="<string name='Buildings/Tyranids/Construction'/>"/>
	<entry name="Tyranids/ConstructionDescription" value="<string name='Buildings/Tyranids/ConstructionDescription'/>"/>
	<entry name="Tyranids/ConstructionFlavor" value="<string name='Buildings/Tyranids/ConstructionFlavor'/>"/>
	<entry name="Tyranids/Heroes" value="<string name='Buildings/Tyranids/Heroes'/>"/>
	<entry name="Tyranids/HeroesDescription" value="<string name='Buildings/Tyranids/HeroesDescription'/>"/>
	<entry name="Tyranids/HeroesFlavor" value="<string name='Buildings/Tyranids/HeroesFlavor'/>"/>
	<entry name="Tyranids/Housing" value="<string name='Buildings/Tyranids/Housing'/>"/>
	<entry name="Tyranids/HousingDescription" value="<string name='Buildings/Tyranids/HousingDescription'/>"/>
	<entry name="Tyranids/HousingFlavor" value="<string name='Buildings/Tyranids/HousingFlavor'/>"/>
	<entry name="Tyranids/Loyalty" value="<string name='Buildings/Tyranids/Loyalty'/>"/>
	<entry name="Tyranids/LoyaltyDescription" value="<string name='Buildings/Tyranids/LoyaltyDescription'/>"/>
	<entry name="Tyranids/LoyaltyFlavor" value="<string name='Buildings/Tyranids/LoyaltyFlavor'/>"/>
	<entry name="Tyranids/Thropes" value="<string name='Buildings/Tyranids/Thropes'/>"/>
	<entry name="Tyranids/ThropesDescription" value="<string name='Buildings/Tyranids/ThropesDescription'/>"/>
	<entry name="Tyranids/ThropesFlavor" value="<string name='Buildings/Tyranids/ThropesFlavor'/>"/>
	<entry name="Tyranids/Vehicles" value="<string name='Buildings/Tyranids/Vehicles'/>"/>
	<entry name="Tyranids/VehiclesDescription" value="<string name='Buildings/Tyranids/VehiclesDescription'/>"/>
	<entry name="Tyranids/VehiclesFlavor" value="<string name='Buildings/Tyranids/VehiclesFlavor'/>"/>
	
	<!-- Edicts -->
	<entry name="AstraMilitarum/AircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictDescription" value="<string name='Actions/AstraMilitarumAircraftProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/DefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarum/DefenseEdictDescription" value="<string name='Actions/AstraMilitarumDefenseEdictDescription'/>"/>
	<entry name="AstraMilitarum/DefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarum/EnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarum/EnergyEdictDescription" value="<string name='Actions/AstraMilitarumEnergyEdictDescription'/>"/>
	<entry name="AstraMilitarum/EnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/FoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarum/FoodEdictDescription" value="<string name='Actions/AstraMilitarumFoodEdictDescription'/>"/>
	<entry name="AstraMilitarum/FoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarum/GrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarum/GrowthEdictDescription" value="<string name='Actions/AstraMilitarumGrowthEdictDescription'/>"/>
	<entry name="AstraMilitarum/GrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictDescription" value="<string name='Actions/AstraMilitarumInfantryProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictDescription" value="<string name='Actions/AstraMilitarumLoyaltyEdictDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/OreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarum/OreEdictDescription" value="<string name='Actions/AstraMilitarumOreEdictDescription'/>"/>
	<entry name="AstraMilitarum/OreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictDescription" value="<string name='Actions/AstraMilitarumPsykerProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/ResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarum/ResearchEdictDescription" value="<string name='Actions/AstraMilitarumResearchEdictDescription'/>"/>
	<entry name="AstraMilitarum/ResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictDescription" value="<string name='Actions/AstraMilitarumVehicleProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>
		
	<!-- Units -->
 	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptor'/>"/>
 	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorDescription" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorDescription'/>"/>
 	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorFlavor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorFlavor'/>"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriest'/>"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriestDescription" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestDescription'/>"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriestFlavor" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestFlavor'/>"/>
 	<entry name="AdeptusMechanicus/KastelanRobot" value="<string name='Units/Neutral/KastelanRobot'/>"/>
 	<entry name="AdeptusMechanicus/KastelanRobotDescription" value="<string name='Units/Neutral/KastelanRobotDescription'/>"/>
 	<entry name="AdeptusMechanicus/KastelanRobotFlavor" value="<string name='Units/Neutral/KastelanRobotFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacher" value="<string name='Units/AdeptusMechanicus/KataphronBreacher'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherDescription" value="<string name='Units/AdeptusMechanicus/KataphronBreacherDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherFlavor" value="<string name='Units/AdeptusMechanicus/KataphronBreacherFlavor'/>"/>	
 	<entry name="AdeptusMechanicus/KataphronDestroyer" value="<string name='Units/AdeptusMechanicus/KataphronDestroyer'/>"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyerDescription" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerDescription'/>"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyerFlavor" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerFlavor'/>"/>
 	<entry name="AdeptusMechanicus/KnightCrusader" value="<string name='Units/AdeptusMechanicus/KnightCrusader'/>"/>
 	<entry name="AdeptusMechanicus/KnightCrusaderDescription" value="<string name='Units/AdeptusMechanicus/KnightCrusaderDescription'/>"/>
 	<entry name="AdeptusMechanicus/KnightCrusaderFlavor" value="<string name='Units/AdeptusMechanicus/KnightCrusaderFlavor'/>"/>
 	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawler'/>"/>
 	<entry name="AdeptusMechanicus/OnagerDunecrawlerDescription" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerDescription'/>"/>
 	<entry name="AdeptusMechanicus/OnagerDunecrawlerFlavor" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerFlavor'/>"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizor'/>"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizorDescription" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorDescription'/>"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizorFlavor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhound'/>"/>
 	<entry name="AdeptusMechanicus/SerberysSulphurhoundDescription" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundDescription'/>"/>
 	<entry name="AdeptusMechanicus/SerberysSulphurhoundFlavor" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SicarianInfiltrator" value="<string name='Units/AdeptusMechanicus/SicarianInfiltrator'/>"/>
 	<entry name="AdeptusMechanicus/SicarianInfiltratorDescription" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorDescription'/>"/>
 	<entry name="AdeptusMechanicus/SicarianInfiltratorFlavor" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker" value="<string name='Units/AdeptusMechanicus/SicarianRuststalker'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerDescription" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerDescription'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerFlavor" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal" value="<string name='Units/AdeptusMechanicus/SkitariiMarshal'/>"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshalDescription" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalDescription'/>"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshalFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SkitariiRanger" value="<string name='Units/AdeptusMechanicus/SkitariiRanger'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerDescription" value="<string name='Units/AdeptusMechanicus/SkitariiRangerDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiRangerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegrator'/>"/>
 	<entry name="AdeptusMechanicus/SkorpiusDisintegratorDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorDescription'/>"/>
 	<entry name="AdeptusMechanicus/SkorpiusDisintegratorFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SkorpiusDunerider" value="<string name='Units/AdeptusMechanicus/SkorpiusDunerider'/>"/>
 	<entry name="AdeptusMechanicus/SkorpiusDuneriderDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderDescription'/>"/>
 	<entry name="AdeptusMechanicus/SkorpiusDuneriderFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderFlavor'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoon" value="<string name='Units/AdeptusMechanicus/SydonianDragoon'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoonDescription" value="<string name='Units/AdeptusMechanicus/SydonianDragoonDescription'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoonFlavor" value="<string name='Units/AdeptusMechanicus/SydonianDragoonFlavor'/>"/>
 	<entry name="AdeptusMechanicus/TechPriestDominus" value="<string name='Units/AdeptusMechanicus/TechPriestDominus'/>"/>
 	<entry name="AdeptusMechanicus/TechPriestDominusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestDominusDescription'/>"/>
 	<entry name="AdeptusMechanicus/TechPriestDominusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestDominusFlavor'/>"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus" value="<string name='Units/AdeptusMechanicus/TechPriestManipulus'/>"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusDescription'/>"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusFlavor'/>"/>	
	<entry name="AstraMilitarum/AttilanRoughRider" value="<string name='Units/AstraMilitarum/AttilanRoughRider'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderDescription" value="<string name='Units/AstraMilitarum/AttilanRoughRiderDescription'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderFlavor" value="<string name='Units/AstraMilitarum/AttilanRoughRiderFlavor'/>"/>
	<entry name="AstraMilitarum/Baneblade" value="<string name='Units/AstraMilitarum/Baneblade'/>"/>
	<entry name="AstraMilitarum/BanebladeDescription" value="<string name='Units/AstraMilitarum/BanebladeDescription'/>"/>
	<entry name="AstraMilitarum/BanebladeFlavor" value="<string name='Units/AstraMilitarum/BanebladeFlavor'/>"/>
	<entry name="AstraMilitarum/Basilisk" value="<string name='Units/AstraMilitarum/Basilisk'/>"/>
	<entry name="AstraMilitarum/BasiliskDescription" value="<string name='Units/AstraMilitarum/BasiliskDescription'/>"/>
	<entry name="AstraMilitarum/BasiliskFlavor" value="<string name='Units/AstraMilitarum/BasiliskFlavor'/>"/>
	<entry name="AstraMilitarum/Bullgryn" value="<string name='Units/AstraMilitarum/Bullgryn'/>"/>
	<entry name="AstraMilitarum/BullgrynDescription" value="<string name='Units/AstraMilitarum/BullgrynDescription'/>"/>
	<entry name="AstraMilitarum/BullgrynFlavor" value="<string name='Units/AstraMilitarum/BullgrynFlavor'/>"/>
	<entry name="AstraMilitarum/Chimera" value="<string name='Units/AstraMilitarum/Chimera'/>"/>
	<entry name="AstraMilitarum/ChimeraDescription" value="<string name='Units/AstraMilitarum/ChimeraDescription'/>"/>
	<entry name="AstraMilitarum/ChimeraFlavor" value="<string name='Units/AstraMilitarum/ChimeraFlavor'/>"/>
	<entry name="AstraMilitarum/DevilDog" value="<string name='Units/AstraMilitarum/DevilDog'/>"/>
	<entry name="AstraMilitarum/DevilDogDescription" value="<string name='Units/AstraMilitarum/DevilDogDescription'/>"/>
	<entry name="AstraMilitarum/DevilDogFlavor" value="<string name='Units/AstraMilitarum/DevilDogFlavor'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery" value="<string name='Units/AstraMilitarum/FieldOrdnanceBattery'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryDescription" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryDescription'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryFlavor" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryFlavor'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquad'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadDescription" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadDescription'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadFlavor" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadFlavor'/>"/>
	<entry name="AstraMilitarum/Hydra" value="<string name='Units/AstraMilitarum/Hydra'/>"/>
	<entry name="AstraMilitarum/HydraDescription" value="<string name='Units/AstraMilitarum/HydraDescription'/>"/>
	<entry name="AstraMilitarum/HydraFlavor" value="<string name='Units/AstraMilitarum/HydraFlavor'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTank" value="<string name='Units/AstraMilitarum/LemanRussBattleTank'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankDescription" value="<string name='Units/AstraMilitarum/LemanRussBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankFlavor" value="<string name='Units/AstraMilitarum/LemanRussBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/LordCommissar" value="<string name='Units/AstraMilitarum/LordCommissar'/>"/>
	<entry name="AstraMilitarum/LordCommissarDescription" value="<string name='Units/AstraMilitarum/LordCommissarDescription'/>"/>
	<entry name="AstraMilitarum/LordCommissarFlavor" value="<string name='Units/AstraMilitarum/LordCommissarFlavor'/>"/>
	<entry name="AstraMilitarum/MarauderBomber" value="<string name='Units/AstraMilitarum/MarauderBomber'/>"/>
	<entry name="AstraMilitarum/MarauderBomberDescription" value="<string name='Units/AstraMilitarum/MarauderBomberDescription'/>"/>
	<entry name="AstraMilitarum/MarauderBomberFlavor" value="<string name='Units/AstraMilitarum/MarauderBomberFlavor'/>"/>
	<entry name="AstraMilitarum/PrimarisPsyker" value="<string name='Units/AstraMilitarum/PrimarisPsyker'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerDescription" value="<string name='Units/AstraMilitarum/PrimarisPsykerDescription'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerFlavor" value="<string name='Units/AstraMilitarum/PrimarisPsykerFlavor'/>"/>
	<entry name="AstraMilitarum/Ratling" value="<string name='Units/AstraMilitarum/Ratling'/>"/>
	<entry name="AstraMilitarum/RatlingDescription" value="<string name='Units/AstraMilitarum/RatlingDescription'/>"/>
	<entry name="AstraMilitarum/RatlingFlavor" value="<string name='Units/AstraMilitarum/RatlingFlavor'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTank" value="<string name='Units/AstraMilitarum/RogalDornBattleTank'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTankDescription" value="<string name='Units/AstraMilitarum/RogalDornBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTankFlavor" value="<string name='Units/AstraMilitarum/RogalDornBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/ScoutSentinel" value="<string name='Units/AstraMilitarum/ScoutSentinel'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelDescription" value="<string name='Units/AstraMilitarum/ScoutSentinelDescription'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelFlavor" value="<string name='Units/AstraMilitarum/ScoutSentinelFlavor'/>"/>
	<entry name="AstraMilitarum/TankCommander" value="<string name='Units/AstraMilitarum/TankCommander'/>"/>
	<entry name="AstraMilitarum/TankCommanderDescription" value="<string name='Units/AstraMilitarum/TankCommanderDescription'/>"/>
	<entry name="AstraMilitarum/TankCommanderFlavor" value="<string name='Units/AstraMilitarum/TankCommanderFlavor'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseer" value="<string name='Units/AstraMilitarum/TechpriestEnginseer'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerDescription" value="<string name='Units/AstraMilitarum/TechpriestEnginseerDescription'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerFlavor" value="<string name='Units/AstraMilitarum/TechpriestEnginseerFlavor'/>"/>
	<entry name="AstraMilitarum/TempestusScion" value="<string name='Units/AstraMilitarum/TempestusScion'/>"/>
	<entry name="AstraMilitarum/TempestusScionDescription" value="<string name='Units/AstraMilitarum/TempestusScionDescription'/>"/>
	<entry name="AstraMilitarum/TempestusScionFlavor" value="<string name='Units/AstraMilitarum/TempestusScionFlavor'/>"/>
	<entry name="AstraMilitarum/Thunderbolt" value="<string name='Units/AstraMilitarum/Thunderbolt'/>"/>
	<entry name="AstraMilitarum/ThunderboltDescription" value="<string name='Units/AstraMilitarum/ThunderboltDescription'/>"/>
	<entry name="AstraMilitarum/ThunderboltFlavor" value="<string name='Units/AstraMilitarum/ThunderboltFlavor'/>"/>
	<entry name="AstraMilitarum/Valkyrie" value="<string name='Units/AstraMilitarum/Valkyrie'/>"/>
	<entry name="AstraMilitarum/ValkyrieDescription" value="<string name='Units/AstraMilitarum/ValkyrieDescription'/>"/>
	<entry name="AstraMilitarum/ValkyrieFlavor" value="<string name='Units/AstraMilitarum/ValkyrieFlavor'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsyker" value="<string name='Units/AstraMilitarum/WyrdvanePsyker'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerDescription" value="<string name='Units/AstraMilitarum/WyrdvanePsykerDescription'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerFlavor" value="<string name='Units/AstraMilitarum/WyrdvanePsykerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaider'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderDescription" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn" value="<string name='Units/ChaosSpaceMarines/ChaosSpawn'/>"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawnDescription" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnDescription'/>"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawnFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="<string name='Units/ChaosSpaceMarines/ChaosTerminator'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorDescription" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince" value="<string name='Units/ChaosSpaceMarines/DaemonPrince'/>"/>
 	<entry name="ChaosSpaceMarines/DaemonPrinceDescription" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceDescription'/>"/>
 	<entry name="ChaosSpaceMarines/DaemonPrinceFlavor" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DarkDisciple" value="<string name='Units/ChaosSpaceMarines/DarkDisciple'/>"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleDescription" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleDescription'/>"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleFlavor" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Defiler" value="<string name='Units/ChaosSpaceMarines/Defiler'/>"/>
 	<entry name="ChaosSpaceMarines/DefilerDescription" value="<string name='Units/ChaosSpaceMarines/DefilerDescription'/>"/>
 	<entry name="ChaosSpaceMarines/DefilerFlavor" value="<string name='Units/ChaosSpaceMarines/DefilerFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Forgefiend" value="<string name='Units/ChaosSpaceMarines/Forgefiend'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendDescription" value="<string name='Units/ChaosSpaceMarines/ForgefiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendFlavor" value="<string name='Units/ChaosSpaceMarines/ForgefiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Havoc" value="<string name='Units/ChaosSpaceMarines/Havoc'/>"/>
 	<entry name="ChaosSpaceMarines/HavocDescription" value="<string name='Units/ChaosSpaceMarines/HavocDescription'/>"/>
 	<entry name="ChaosSpaceMarines/HavocFlavor" value="<string name='Units/ChaosSpaceMarines/HavocFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Helbrute" value="<string name='Units/ChaosSpaceMarines/Helbrute'/>"/>
 	<entry name="ChaosSpaceMarines/HelbruteDescription" value="<string name='Units/ChaosSpaceMarines/HelbruteDescription'/>"/>
 	<entry name="ChaosSpaceMarines/HelbruteFlavor" value="<string name='Units/ChaosSpaceMarines/HelbruteFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerker'/>"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerkerDescription" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerDescription'/>"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerkerFlavor" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession" value="<string name='Units/ChaosSpaceMarines/MasterOfPossession'/>"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossessionDescription" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionDescription'/>"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossessionFlavor" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Maulerfiend" value="<string name='Units/ChaosSpaceMarines/Maulerfiend'/>"/>
 	<entry name="ChaosSpaceMarines/MaulerfiendDescription" value="<string name='Units/ChaosSpaceMarines/MaulerfiendDescription'/>"/>
 	<entry name="ChaosSpaceMarines/MaulerfiendFlavor" value="<string name='Units/ChaosSpaceMarines/MaulerfiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrown" value="<string name='Units/ChaosSpaceMarines/NoctilithCrown'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrownDescription" value="Otorga a los Cultistas del Caos la habilidad de construir fortificaciones."/>
	<entry name="ChaosSpaceMarines/NoctilithCrownFlavor" value="<string name='Units/ChaosSpaceMarines/NoctilithCrownFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Obliterator" value="<string name='Units/ChaosSpaceMarines/Obliterator'/>"/>
 	<entry name="ChaosSpaceMarines/ObliteratorDescription" value="<string name='Units/ChaosSpaceMarines/ObliteratorDescription'/>"/>
 	<entry name="ChaosSpaceMarines/ObliteratorFlavor" value="<string name='Units/ChaosSpaceMarines/ObliteratorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarine" value="<string name='Units/ChaosSpaceMarines/RubricMarine'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineDescription" value="<string name='Units/ChaosSpaceMarines/RubricMarineDescription'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineFlavor" value="<string name='Units/ChaosSpaceMarines/RubricMarineFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Venomcrawler" value="<string name='Units/ChaosSpaceMarines/Venomcrawler'/>"/>
 	<entry name="ChaosSpaceMarines/VenomcrawlerDescription" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerDescription'/>"/>
 	<entry name="ChaosSpaceMarines/VenomcrawlerFlavor" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Warpsmith" value="<string name='Units/ChaosSpaceMarines/Warpsmith'/>"/>
 	<entry name="ChaosSpaceMarines/WarpsmithDescription" value="<string name='Units/ChaosSpaceMarines/WarpsmithDescription'/>"/>
 	<entry name="ChaosSpaceMarines/WarpsmithFlavor" value="<string name='Units/ChaosSpaceMarines/WarpsmithFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/WarpTalon" value="<string name='Units/ChaosSpaceMarines/WarpTalon'/>"/>
 	<entry name="ChaosSpaceMarines/WarpTalonDescription" value="<string name='Units/ChaosSpaceMarines/WarpTalonDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonFlavor" value="<string name='Units/ChaosSpaceMarines/WarpTalonFlavor'/>"/>
	<entry name="Drukhari/Cronos" value="<string name='Units/Drukhari/Cronos'/>"/>
	<entry name="Drukhari/CronosDescription" value="<string name='Units/Drukhari/CronosDescription'/>"/>
	<entry name="Drukhari/CronosFlavor" value="<string name='Units/Drukhari/CronosFlavor'/>"/>
	<entry name="Drukhari/Haemonculus" value="<string name='Units/Drukhari/Haemonculus'/>"/>
	<entry name="Drukhari/HaemonculusDescription" value="<string name='Units/Drukhari/HaemonculusDescription'/>"/>
	<entry name="Drukhari/HaemonculusFlavor" value="<string name='Units/Drukhari/HaemonculusFlavor'/>"/>
	<entry name="Drukhari/Hellion" value="<string name='Units/Drukhari/Hellion'/>"/>
	<entry name="Drukhari/HellionDescription" value="<string name='Units/Drukhari/HellionDescription'/>"/>
	<entry name="Drukhari/HellionFlavor" value="<string name='Units/Drukhari/HellionFlavor'/>"/>
	<entry name="Drukhari/Incubi" value="<string name='Units/Drukhari/Incubi'/>"/>
	<entry name="Drukhari/IncubiDescription" value="<string name='Units/Drukhari/IncubiDescription'/>"/>
	<entry name="Drukhari/IncubiFlavor" value="<string name='Units/Drukhari/IncubiFlavor'/>"/>
	<entry name="Drukhari/KabaliteTrueborn" value="<string name='Units/Drukhari/KabaliteTrueborn'/>"/>
	<entry name="Drukhari/KabaliteTruebornDescription" value="<string name='Units/Drukhari/KabaliteTruebornDescription'/>"/>
	<entry name="Drukhari/KabaliteTruebornFlavor" value="<string name='Units/Drukhari/KabaliteTruebornFlavor'/>"/>
	<entry name="Drukhari/Mandrake" value="<string name='Units/Drukhari/Mandrake'/>"/>
	<entry name="Drukhari/MandrakeDescription" value="<string name='Units/Drukhari/MandrakeDescription'/>"/>
	<entry name="Drukhari/MandrakeFlavor" value="<string name='Units/Drukhari/MandrakeFlavor'/>"/>
	<entry name="Drukhari/Raider" value="<string name='Units/Drukhari/Raider'/>"/>
	<entry name="Drukhari/RaiderDescription" value="<string name='Units/Drukhari/RaiderDescription'/>"/>
	<entry name="Drukhari/RaiderFlavor" value="<string name='Units/Drukhari/RaiderFlavor'/>"/>
	<entry name="Drukhari/Ravager" value="<string name='Units/Drukhari/Ravager'/>"/>
	<entry name="Drukhari/RavagerDescription" value="<string name='Units/Drukhari/RavagerDescription'/>"/>
	<entry name="Drukhari/RavagerFlavor" value="<string name='Units/Drukhari/RavagerFlavor'/>"/>
	<entry name="Drukhari/Reaver" value="<string name='Units/Drukhari/Reaver'/>"/>
	<entry name="Drukhari/ReaverDescription" value="<string name='Units/Drukhari/ReaverDescription'/>"/>
	<entry name="Drukhari/ReaverFlavor" value="<string name='Units/Drukhari/ReaverFlavor'/>"/>
	<entry name="Drukhari/Scourge" value="<string name='Units/Drukhari/Scourge'/>"/>
	<entry name="Drukhari/ScourgeDescription" value="<string name='Units/Drukhari/ScourgeDescription'/>"/>
	<entry name="Drukhari/ScourgeFlavor" value="<string name='Units/Drukhari/ScourgeFlavor'/>"/>
	<entry name="Drukhari/Succubus" value="<string name='Units/Drukhari/Succubus'/>"/>
	<entry name="Drukhari/SuccubusDescription" value="<string name='Units/Drukhari/SuccubusDescription'/>"/>
	<entry name="Drukhari/SuccubusFlavor" value="<string name='Units/Drukhari/SuccubusFlavor'/>"/>
	<entry name="Drukhari/Talos" value="<string name='Units/Drukhari/Talos'/>"/>
	<entry name="Drukhari/TalosDescription" value="<string name='Units/Drukhari/TalosDescription'/>"/>
	<entry name="Drukhari/TalosFlavor" value="<string name='Units/Drukhari/TalosFlavor'/>"/>
	<entry name="Drukhari/Tantalus" value="<string name='Units/Drukhari/Tantalus'/>"/>
	<entry name="Drukhari/TantalusDescription" value="<string name='Units/Drukhari/TantalusDescription'/>"/>
	<entry name="Drukhari/TantalusFlavor" value="<string name='Units/Drukhari/TantalusFlavor'/>"/>
	<entry name="Drukhari/VoidravenBomber" value="<string name='Units/Drukhari/VoidravenBomber'/>"/>
	<entry name="Drukhari/VoidravenBomberDescription" value="<string name='Units/Drukhari/VoidravenBomberDescription'/>"/>
	<entry name="Drukhari/VoidravenBomberFlavor" value="<string name='Units/Drukhari/VoidravenBomberFlavor'/>"/>
	<entry name="Drukhari/Wrack" value="<string name='Units/Drukhari/Wrack'/>"/>
	<entry name="Drukhari/WrackDescription" value="<string name='Units/Drukhari/WrackDescription'/>"/>
	<entry name="Drukhari/WrackFlavor" value="<string name='Units/Drukhari/WrackFlavor'/>"/>
	<entry name="Drukhari/Wyche" value="<string name='Units/Drukhari/Wyche'/>"/>
	<entry name="Drukhari/WycheDescription" value="<string name='Units/Drukhari/WycheDescription'/>"/>
	<entry name="Drukhari/WycheFlavor" value="<string name='Units/Drukhari/WycheFlavor'/>"/>
	<entry name="Eldar/AvatarOfKhaine" value="<string name='Units/Eldar/AvatarOfKhaine'/>"/>
	<entry name="Eldar/AvatarOfKhaineDescription" value="<string name='Units/Eldar/AvatarOfKhaineDescription'/>"/>
	<entry name="Eldar/AvatarOfKhaineFlavor" value="<string name='Units/Eldar/AvatarOfKhaineFlavor'/>"/>
	<entry name="Eldar/DarkReaper" value="<string name='Units/Eldar/DarkReaper'/>"/>
	<entry name="Eldar/DarkReaperDescription" value="<string name='Units/Eldar/DarkReaperDescription'/>"/>
	<entry name="Eldar/DarkReaperFlavor" value="<string name='Units/Eldar/DarkReaperFlavor'/>"/>
	<entry name="Eldar/FarseerSkyrunner" value="<string name='Units/Eldar/FarseerSkyrunner'/>"/>
	<entry name="Eldar/FarseerSkyrunnerDescription" value="<string name='Units/Eldar/FarseerSkyrunnerDescription'/>"/>
	<entry name="Eldar/FarseerSkyrunnerFlavor" value="<string name='Units/Eldar/FarseerSkyrunnerFlavor'/>"/>
	<entry name="Eldar/FireDragon" value="<string name='Units/Eldar/FireDragon'/>"/>
	<entry name="Eldar/FireDragonDescription" value="<string name='Units/Eldar/FireDragonDescription'/>"/>
	<entry name="Eldar/FireDragonFlavor" value="<string name='Units/Eldar/FireDragonFlavor'/>"/>
	<entry name="Eldar/FirePrism" value="<string name='Units/Eldar/FirePrism'/>"/>
	<entry name="Eldar/FirePrismDescription" value="<string name='Units/Eldar/FirePrismDescription'/>"/>
	<entry name="Eldar/FirePrismFlavor" value="<string name='Units/Eldar/FirePrismFlavor'/>"/>
	<entry name="Eldar/Hornet" value="<string name='Units/Eldar/Hornet'/>"/>
	<entry name="Eldar/HornetDescription" value="<string name='Units/Eldar/HornetDescription'/>"/>
	<entry name="Eldar/HornetFlavor" value="<string name='Units/Eldar/HornetFlavor'/>"/>
	<entry name="Eldar/HemlockWraithfighter" value="<string name='Units/Eldar/HemlockWraithfighter'/>"/>
	<entry name="Eldar/HemlockWraithfighterDescription" value="<string name='Units/Eldar/HemlockWraithfighterDescription'/>"/>
	<entry name="Eldar/HemlockWraithfighterFlavor" value="<string name='Units/Eldar/HemlockWraithfighterFlavor'/>"/>
	<entry name="Eldar/Hornet" value="<string name='Units/Eldar/Hornet'/>"/>
	<entry name="Eldar/HornetDescription" value="<string name='Units/Eldar/HornetDescription'/>"/>
	<entry name="Eldar/HornetFlavor" value="<string name='Units/Eldar/HornetFlavor'/>"/>
	<entry name="Eldar/HowlingBanshee" value="<string name='Units/Eldar/HowlingBanshee'/>"/>
	<entry name="Eldar/HowlingBansheeDescription" value="<string name='Units/Eldar/HowlingBansheeDescription'/>"/>
	<entry name="Eldar/HowlingBansheeFlavor" value="<string name='Units/Eldar/HowlingBansheeFlavor'/>"/>
	<entry name="Eldar/Ranger" value="<string name='Units/Eldar/Ranger'/>"/>
	<entry name="Eldar/RangerDescription" value="<string name='Units/Eldar/RangerDescription'/>"/>
	<entry name="Eldar/RangerFlavor" value="<string name='Units/Eldar/RangerFlavor'/>"/>
	<entry name="Eldar/Scorpion" value="<string name='Units/Eldar/Scorpion'/>"/>
	<entry name="Eldar/ScorpionDescription" value="<string name='Units/Eldar/ScorpionDescription'/>"/>
	<entry name="Eldar/ScorpionFlavor" value="<string name='Units/Eldar/ScorpionFlavor'/>"/>
	<entry name="Eldar/Spiritseer" value="<string name='Units/Eldar/Spiritseer'/>"/>
	<entry name="Eldar/SpiritseerDescription" value="<string name='Units/Eldar/SpiritseerDescription'/>"/>
	<entry name="Eldar/SpiritseerFlavor" value="<string name='Units/Eldar/SpiritseerFlavor'/>"/>
	<entry name="Eldar/VaulsWrathSupportBattery" value="<string name='Units/Eldar/VaulsWrathSupportBattery'/>"/>
	<entry name="Eldar/VaulsWrathSupportBatteryDescription" value="<string name='Units/Eldar/VaulsWrathSupportBatteryDescription'/>"/>
	<entry name="Eldar/VaulsWrathSupportBatteryFlavor" value="<string name='Units/Eldar/VaulsWrathSupportBatteryFlavor'/>"/>
	<entry name="Eldar/Vyper" value="<string name='Units/Eldar/Vyper'/>"/>
	<entry name="Eldar/VyperDescription" value="<string name='Units/Eldar/VyperDescription'/>"/>
	<entry name="Eldar/VyperFlavor" value="<string name='Units/Eldar/VyperFlavor'/>"/>
	<entry name="Eldar/WarWalker" value="<string name='Units/Eldar/WarWalker'/>"/>
	<entry name="Eldar/WarWalkerDescription" value="<string name='Units/Eldar/WarWalkerDescription'/>"/>
	<entry name="Eldar/WarWalkerFlavor" value="<string name='Units/Eldar/WarWalkerFlavor'/>"/>
	<entry name="Eldar/Warlock" value="<string name='Units/Eldar/Warlock'/>"/>
	<entry name="Eldar/WarlockDescription" value="<string name='Units/Eldar/WarlockDescription'/>"/>
	<entry name="Eldar/WarlockFlavor" value="<string name='Units/Eldar/WarlockFlavor'/>"/>
	<entry name="Eldar/WaveSerpent" value="<string name='Units/Eldar/WaveSerpent'/>"/>
	<entry name="Eldar/WaveSerpentDescription" value="<string name='Units/Eldar/WaveSerpentDescription'/>"/>
	<entry name="Eldar/WaveSerpentFlavor" value="<string name='Units/Eldar/WaveSerpentFlavor'/>"/>
	<entry name="Eldar/Wraithblade" value="<string name='Units/Eldar/Wraithblade'/>"/>
	<entry name="Eldar/WraithbladeDescription" value="<string name='Units/Eldar/WraithbladeDescription'/>"/>
	<entry name="Eldar/WraithbladeFlavor" value="<string name='Units/Eldar/WraithbladeFlavor'/>"/>
	<entry name="Eldar/Wraithknight" value="<string name='Units/Eldar/Wraithknight'/>"/>
	<entry name="Eldar/WraithknightDescription" value="<string name='Units/Eldar/WraithknightDescription'/>"/>
	<entry name="Eldar/WraithknightFlavor" value="<string name='Units/Eldar/WraithknightFlavor'/>"/>
	<entry name="Eldar/Wraithlord" value="<string name='Units/Eldar/Wraithlord'/>"/>
	<entry name="Eldar/WraithlordDescription" value="<string name='Units/Eldar/WraithlordDescription'/>"/>
	<entry name="Eldar/WraithlordFlavor" value="<string name='Units/Eldar/WraithlordFlavor'/>"/>
 	<entry name="Necrons/AnnihilationBarge" value="<string name='Units/Necrons/AnnihilationBarge'/>"/>
	<entry name="Necrons/AnnihilationBargeDescription" value="<string name='Units/Necrons/AnnihilationBargeDescription'/>"/>
	<entry name="Necrons/AnnihilationBargeFlavor" value="<string name='Units/Necrons/AnnihilationBargeFlavor'/>"/>
	<entry name="Necrons/CanoptekReanimator" value="<string name='Units/Necrons/CanoptekReanimator'/>"/>
	<entry name="Necrons/CanoptekReanimatorDescription" value="<string name='Units/Necrons/CanoptekReanimatorDescription'/>"/>
	<entry name="Necrons/CanoptekReanimatorFlavor" value="<string name='Units/Necrons/CanoptekReanimatorFlavor'/>"/>
	<entry name="Necrons/CanoptekSpyder" value="<string name='Units/Necrons/CanoptekSpyder'/>"/>
	<entry name="Necrons/CanoptekSpyderDescription" value="<string name='Units/Necrons/CanoptekSpyderDescription'/>"/>
	<entry name="Necrons/CanoptekSpyderFlavor" value="<string name='Units/Necrons/CanoptekSpyderFlavor'/>"/>
	<entry name="Necrons/CanoptekWraith" value="<string name='Units/Necrons/CanoptekWraith'/>"/>
	<entry name="Necrons/CanoptekWraithDescription" value="<string name='Units/Necrons/CanoptekWraithDescription'/>"/>
	<entry name="Necrons/CanoptekWraithFlavor" value="<string name='Units/Necrons/CanoptekWraithFlavor'/>"/>
	<entry name="Necrons/Cryptek" value="<string name='Units/Necrons/Cryptek'/>"/>
	<entry name="Necrons/CryptekDescription" value="<string name='Units/Necrons/CryptekDescription'/>"/>
	<entry name="Necrons/CryptekFlavor" value="<string name='Units/Necrons/CryptekFlavor'/>"/>
	<entry name="Necrons/Deathmark" value="<string name='Units/Necrons/Deathmark'/>"/>
	<entry name="Necrons/DeathmarkDescription" value="<string name='Units/Necrons/DeathmarkDescription'/>"/>
	<entry name="Necrons/DeathmarkFlavor" value="<string name='Units/Necrons/DeathmarkFlavor'/>"/>
	<entry name="Necrons/DestroyerLord" value="<string name='Units/Necrons/DestroyerLord'/>"/>
	<entry name="Necrons/DestroyerLordDescription" value="<string name='Units/Necrons/DestroyerLordDescription'/>"/>
	<entry name="Necrons/DestroyerLordFlavor" value="<string name='Units/Necrons/DestroyerLordFlavor'/>"/>
	<entry name="Necrons/DoomScythe" value="<string name='Units/Necrons/DoomScythe'/>"/>
	<entry name="Necrons/DoomScytheDescription" value="<string name='Units/Necrons/DoomScytheDescription'/>"/>
	<entry name="Necrons/DoomScytheFlavor" value="<string name='Units/Necrons/DoomScytheFlavor'/>"/>
	<entry name="Necrons/DoomsdayArk" value="<string name='Units/Necrons/DoomsdayArk'/>"/>
	<entry name="Necrons/DoomsdayArkDescription" value="<string name='Units/Necrons/DoomsdayArkDescription'/>"/>
	<entry name="Necrons/DoomsdayArkFlavor" value="<string name='Units/Necrons/DoomsdayArkFlavor'/>"/>
	<entry name="Necrons/FlayedOne" value="<string name='Units/Necrons/FlayedOne'/>"/>
	<entry name="Necrons/FlayedOneDescription" value="<string name='Units/Necrons/FlayedOneDescription'/>"/>
	<entry name="Necrons/FlayedOneFlavor" value="<string name='Units/Necrons/FlayedOneFlavor'/>"/>
	<entry name="Necrons/GhostArk" value="<string name='Units/Necrons/GhostArk'/>"/>
	<entry name="Necrons/GhostArkDescription" value="<string name='Units/Necrons/GhostArkDescription'/>"/>
	<entry name="Necrons/GhostArkFlavor" value="<string name='Units/Necrons/GhostArkFlavor'/>"/>
	<entry name="Necrons/HeavyDestroyer" value="<string name='Units/Necrons/HeavyDestroyer'/>"/>
	<entry name="Necrons/HeavyDestroyerDescription" value="<string name='Units/Necrons/HeavyDestroyerDescription'/>"/>
	<entry name="Necrons/HeavyDestroyerFlavor" value="<string name='Units/Necrons/HeavyDestroyerFlavor'/>"/>
	<entry name="Necrons/Immortal" value="<string name='Units/Necrons/Immortal'/>"/>
	<entry name="Necrons/ImmortalDescription" value="<string name='Units/Necrons/ImmortalDescription'/>"/>
	<entry name="Necrons/ImmortalFlavor" value="<string name='Units/Necrons/ImmortalFlavor'/>"/>
	<entry name="Necrons/Lord" value="<string name='Units/Necrons/Lord'/>"/>
	<entry name="Necrons/LordDescription" value="<string name='Units/Necrons/LordDescription'/>"/>
	<entry name="Necrons/LordFlavor" value="<string name='Units/Necrons/LordFlavor'/>"/>
	<entry name="Necrons/Monolith" value="<string name='Units/Necrons/Monolith'/>"/>
	<entry name="Necrons/MonolithDescription" value="<string name='Units/Necrons/MonolithDescription'/>"/>
	<entry name="Necrons/MonolithFlavor" value="<string name='Units/Necrons/MonolithFlavor'/>"/>
	<entry name="Necrons/NightScythe" value="<string name='Units/Necrons/NightScythe'/>"/>
	<entry name="Necrons/NightScytheDescription" value="<string name='Units/Necrons/NightScytheDescription'/>"/>
	<entry name="Necrons/NightScytheFlavor" value="<string name='Units/Necrons/NightScytheFlavor'/>"/>
	<entry name="Necrons/Obelisk" value="<string name='Units/Necrons/Obelisk'/>"/>
	<entry name="Necrons/ObeliskDescription" value="<string name='Units/Necrons/ObeliskDescription'/>"/>
	<entry name="Necrons/ObeliskFlavor" value="<string name='Units/Necrons/ObeliskFlavor'/>"/>
	<entry name="Necrons/SkorpekhDestroyer" value="<string name='Units/Necrons/SkorpekhDestroyer'/>"/>
	<entry name="Necrons/SkorpekhDestroyerDescription" value="<string name='Units/Necrons/SkorpekhDestroyerDescription'/>"/>
	<entry name="Necrons/SkorpekhDestroyerFlavor" value="<string name='Units/Necrons/SkorpekhDestroyerFlavor'/>"/>
	<entry name="Necrons/TombBlade" value="<string name='Units/Necrons/TombBlade'/>"/>
	<entry name="Necrons/TombBladeDescription" value="<string name='Units/Necrons/TombBladeDescription'/>"/>
	<entry name="Necrons/TombBladeFlavor" value="<string name='Units/Necrons/TombBladeFlavor'/>"/>
	<entry name="Necrons/TranscendentCtan" value="<string name='Units/Necrons/TranscendentCtan'/>"/>
	<entry name="Necrons/TranscendentCtanDescription" value="<string name='Units/Necrons/TranscendentCtanDescription'/>"/>
	<entry name="Necrons/TranscendentCtanFlavor" value="<string name='Units/Necrons/TranscendentCtanFlavor'/>"/>
	<entry name="Necrons/TriarchPraetorian" value="<string name='Units/Necrons/TriarchPraetorian'/>"/>
	<entry name="Necrons/TriarchPraetorianDescription" value="<string name='Units/Necrons/TriarchPraetorianDescription'/>"/>
	<entry name="Necrons/TriarchPraetorianFlavor" value="<string name='Units/Necrons/TriarchPraetorianFlavor'/>"/>
	<entry name="Necrons/TriarchStalker" value="<string name='Units/Necrons/TriarchStalker'/>"/>
	<entry name="Necrons/TriarchStalkerDescription" value="<string name='Units/Necrons/TriarchStalkerDescription'/>"/>
	<entry name="Necrons/TriarchStalkerFlavor" value="<string name='Units/Necrons/TriarchStalkerFlavor'/>"/>
	<entry name="Orks/Battlewagon" value="<string name='Units/Orks/Battlewagon'/>"/>
	<entry name="Orks/BattlewagonDescription" value="<string name='Units/Orks/BattlewagonDescription'/>"/>
	<entry name="Orks/BattlewagonFlavor" value="<string name='Units/Orks/BattlewagonFlavor'/>"/>
	<entry name="Orks/BigMek" value="<string name='Units/Orks/BigMek'/>"/>
	<entry name="Orks/BigMekDescription" value="<string name='Units/Orks/BigMekDescription'/>"/>
	<entry name="Orks/BigMekFlavor" value="<string name='Units/Orks/BigMekFlavor'/>"/>
	<entry name="Orks/BurnaBommer" value="<string name='Units/Orks/BurnaBommer'/>"/>
	<entry name="Orks/BurnaBommerDescription" value="<string name='Units/Orks/BurnaBommerDescription'/>"/>
	<entry name="Orks/BurnaBommerFlavor" value="<string name='Units/Orks/BurnaBommerFlavor'/>"/>
	<entry name="Orks/BurnaBoy" value="<string name='Units/Orks/BurnaBoy'/>"/>
	<entry name="Orks/BurnaBoyDescription" value="<string name='Units/Orks/BurnaBoyDescription'/>"/>
	<entry name="Orks/BurnaBoyFlavor" value="<string name='Units/Orks/BurnaBoyFlavor'/>"/>
	<entry name="Orks/Dakkajet" value="<string name='Units/Orks/Dakkajet'/>"/>
	<entry name="Orks/DakkajetDescription" value="<string name='Units/Orks/DakkajetDescription'/>"/>
	<entry name="Orks/DakkajetFlavor" value="<string name='Units/Orks/DakkajetFlavor'/>"/>
	<entry name="Orks/DeffDread" value="<string name='Units/Orks/DeffDread'/>"/>
	<entry name="Orks/DeffDreadDescription" value="<string name='Units/Orks/DeffDreadDescription'/>"/>
	<entry name="Orks/DeffDreadFlavor" value="<string name='Units/Orks/DeffDreadFlavor'/>"/>
	<entry name="Orks/Deffkopta" value="<string name='Units/Orks/Deffkopta'/>"/>
	<entry name="Orks/DeffkoptaDescription" value="<string name='Units/Orks/DeffkoptaDescription'/>"/>
	<entry name="Orks/DeffkoptaFlavor" value="<string name='Units/Orks/DeffkoptaFlavor'/>"/>
	<entry name="Orks/FlashGitz" value="<string name='Units/Orks/FlashGitz'/>"/>
	<entry name="Orks/FlashGitzDescription" value="<string name='Units/Orks/FlashGitzDescription'/>"/>
	<entry name="Orks/FlashGitzFlavor" value="<string name='Units/Orks/FlashGitzFlavor'/>"/>
	<entry name="Orks/GargantuanSquiggoth" value="<string name='Units/Orks/GargantuanSquiggoth'/>"/>
	<entry name="Orks/GargantuanSquiggothDescription" value="<string name='Units/Orks/GargantuanSquiggothDescription'/>"/>
	<entry name="Orks/GargantuanSquiggothFlavor" value="<string name='Units/Orks/GargantuanSquiggothFlavor'/>"/>
	<entry name="Orks/Gorkanaut" value="<string name='Units/Orks/Gorkanaut'/>"/>
	<entry name="Orks/GorkanautDescription" value="<string name='Units/Orks/GorkanautDescription'/>"/>
	<entry name="Orks/GorkanautFlavor" value="<string name='Units/Orks/GorkanautFlavor'/>"/>
	<entry name="Orks/KillBursta" value="<string name='Units/Orks/KillBursta'/>"/>
	<entry name="Orks/KillBurstaDescription" value="<string name='Units/Orks/KillBurstaDescription'/>"/>
	<entry name="Orks/KillBurstaFlavor" value="<string name='Units/Orks/KillBurstaFlavor'/>"/>
	<entry name="Orks/KillaKan" value="<string name='Units/Orks/KillaKan'/>"/>
	<entry name="Orks/KillaKanDescription" value="<string name='Units/Orks/KillaKanDescription'/>"/>
	<entry name="Orks/KillaKanFlavor" value="<string name='Units/Orks/KillaKanFlavor'/>"/>
	<entry name="Orks/Meganob" value="<string name='Units/Orks/Meganob'/>"/>
	<entry name="Orks/MeganobDescription" value="<string name='Units/Orks/MeganobDescription'/>"/>
	<entry name="Orks/MeganobFlavor" value="<string name='Units/Orks/MeganobFlavor'/>"/>
	<entry name="Orks/MegatrakkScrapjet" value="<string name='Units/Orks/MegatrakkScrapjet'/>"/>
	<entry name="Orks/MegatrakkScrapjetDescription" value="<string name='Units/Orks/MegatrakkScrapjetDescription'/>"/>
	<entry name="Orks/MegatrakkScrapjetFlavor" value="<string name='Units/Orks/MegatrakkScrapjetFlavor'/>"/>
	<entry name="Orks/Mek" value="<string name='Units/Orks/Mek'/>"/>
	<entry name="Orks/MekDescription" value="<string name='Units/Orks/MekDescription'/>"/>
	<entry name="Orks/MekFlavor" value="<string name='Units/Orks/MekFlavor'/>"/>
	<entry name="Orks/MekGun" value="<string name='Units/Orks/MekGun'/>"/>
	<entry name="Orks/MekGunDescription" value="<string name='Units/Orks/MekGunDescription'/>"/>
	<entry name="Orks/MekGunFlavor" value="<string name='Units/Orks/MekGunFlavor'/>"/>
	<entry name="Orks/Painboy" value="<string name='Units/Orks/Painboy'/>"/>
	<entry name="Orks/PainboyDescription" value="<string name='Units/Orks/PainboyDescription'/>"/>
	<entry name="Orks/PainboyFlavor" value="<string name='Units/Orks/PainboyFlavor'/>"/>
	<entry name="Orks/SquighogBoy" value="<string name='Units/Orks/SquighogBoy'/>"/>
	<entry name="Orks/SquighogBoyDescription" value="<string name='Units/Orks/SquighogBoyDescription'/>"/>
	<entry name="Orks/SquighogBoyFlavor" value="<string name='Units/Orks/SquighogBoyFlavor'/>"/>
	<entry name="Orks/Tankbusta" value="<string name='Units/Orks/Tankbusta'/>"/>
	<entry name="Orks/TankbustaDescription" value="<string name='Units/Orks/TankbustaDescription'/>"/>
	<entry name="Orks/TankbustaFlavor" value="<string name='Units/Orks/TankbustaFlavor'/>"/>
	<entry name="Orks/Warbiker" value="<string name='Units/Orks/Warbiker'/>"/>
	<entry name="Orks/WarbikerDescription" value="<string name='Units/Orks/WarbikerDescription'/>"/>
	<entry name="Orks/WarbikerFlavor" value="<string name='Units/Orks/WarbikerFlavor'/>"/>
	<entry name="Orks/Warboss" value="<string name='Units/Orks/Warboss'/>"/>
	<entry name="Orks/WarbossDescription" value="<string name='Units/Orks/WarbossDescription'/>"/>
	<entry name="Orks/WarbossFlavor" value="<string name='Units/Orks/WarbossFlavor'/>"/>
	<entry name="Orks/Warbuggy" value="<string name='Units/Orks/Warbuggy'/>"/>
	<entry name="Orks/WarbuggyDescription" value="<string name='Units/Orks/WarbuggyDescription'/>"/>
	<entry name="Orks/WarbuggyFlavor" value="<string name='Units/Orks/WarbuggyFlavor'/>"/>
	<entry name="Orks/Weirdboy" value="<string name='Units/Orks/Weirdboy'/>"/>
	<entry name="Orks/WeirdboyDescription" value="<string name='Units/Orks/WeirdboyDescription'/>"/>
	<entry name="Orks/WeirdboyFlavor" value="<string name='Units/Orks/WeirdboyFlavor'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellant" value="<string name='Units/SistersOfBattle/ArcoFlagellant'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellantDescription" value="<string name='Units/SistersOfBattle/ArcoFlagellantDescription'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellantFlavor" value="<string name='Units/SistersOfBattle/ArcoFlagellantFlavor'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter" value="<string name='Units/SistersOfBattle/AvengerStrikeFighter'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterDescription" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterDescription'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterFlavor" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterFlavor'/>"/>
	<entry name="SistersOfBattle/Castigator" value="<string name='Units/SistersOfBattle/Castigator'/>"/>
	<entry name="SistersOfBattle/CastigatorDescription" value="<string name='Units/SistersOfBattle/CastigatorDescription'/>"/>
	<entry name="SistersOfBattle/CastigatorFlavor" value="<string name='Units/SistersOfBattle/CastigatorFlavor'/>"/>
	<entry name="SistersOfBattle/CelestianSacresant" value="<string name='Units/SistersOfBattle/CelestianSacresant'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantDescription" value="<string name='Units/SistersOfBattle/CelestianSacresantDescription'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantFlavor" value="<string name='Units/SistersOfBattle/CelestianSacresantFlavor'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancer" value="<string name='Units/SistersOfBattle/CerastusKnightLancer'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerDescription" value="<string name='Units/SistersOfBattle/CerastusKnightLancerDescription'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerFlavor" value="<string name='Units/SistersOfBattle/CerastusKnightLancerFlavor'/>"/>
	<entry name="SistersOfBattle/Dialogus" value="<string name='Units/SistersOfBattle/Dialogus'/>"/>
	<entry name="SistersOfBattle/DialogusDescription" value="<string name='Units/SistersOfBattle/DialogusDescription'/>"/>
	<entry name="SistersOfBattle/DialogusFlavor" value="<string name='Units/SistersOfBattle/DialogusFlavor'/>"/>
	<entry name="SistersOfBattle/Dominion" value="<string name='Units/SistersOfBattle/Dominion'/>"/>
	<entry name="SistersOfBattle/DominionDescription" value="<string name='Units/SistersOfBattle/DominionDescription'/>"/>
	<entry name="SistersOfBattle/DominionFlavor" value="<string name='Units/SistersOfBattle/DominionFlavor'/>"/>
	<entry name="SistersOfBattle/Exorcist" value="<string name='Units/SistersOfBattle/Exorcist'/>"/>
	<entry name="SistersOfBattle/ExorcistDescription" value="<string name='Units/SistersOfBattle/ExorcistDescription'/>"/>
	<entry name="SistersOfBattle/ExorcistFlavor" value="<string name='Units/SistersOfBattle/ExorcistFlavor'/>"/>
	<entry name="SistersOfBattle/Headquarters" value="<string name='Buildings/SistersOfBattle/Headquarters'/>"/>
	<entry name="SistersOfBattle/HeadquartersDescription" value="<string name='Units/SistersOfBattle/HeadquartersDescription'/>"/>
	<entry name="SistersOfBattle/HeadquartersFlavor" value="<string name='Buildings/SistersOfBattle/HeadquartersFlavor'/>"/>
	<entry name="SistersOfBattle/Hospitaller" value="<string name='Units/SistersOfBattle/Hospitaller'/>"/>
	<entry name="SistersOfBattle/HospitallerDescription" value="<string name='Units/SistersOfBattle/HospitallerDescription'/>"/>
	<entry name="SistersOfBattle/HospitallerFlavor" value="<string name='Units/SistersOfBattle/HospitallerFlavor'/>"/>
	<entry name="SistersOfBattle/Imagifier" value="<string name='Units/SistersOfBattle/Imagifier'/>"/>
	<entry name="SistersOfBattle/ImagifierDescription" value="<string name='Units/SistersOfBattle/ImagifierDescription'/>"/>
	<entry name="SistersOfBattle/ImagifierFlavor" value="<string name='Units/SistersOfBattle/ImagifierFlavor'/>"/>	
	<entry name="SistersOfBattle/Mortifier" value="<string name='Units/SistersOfBattle/Mortifier'/>"/>
	<entry name="SistersOfBattle/MortifierDescription" value="<string name='Units/SistersOfBattle/MortifierDescription'/>"/>
	<entry name="SistersOfBattle/MortifierFlavor" value="<string name='Units/SistersOfBattle/MortifierFlavor'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="<string name='Units/SistersOfBattle/ParagonWarsuit'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="<string name='Units/SistersOfBattle/ParagonWarsuitDescription'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="<string name='Units/SistersOfBattle/ParagonWarsuitFlavor'/>"/>
	<entry name="SistersOfBattle/Retributor" value="<string name='Units/SistersOfBattle/Retributor'/>"/>
	<entry name="SistersOfBattle/RetributorDescription" value="<string name='Units/SistersOfBattle/RetributorDescription'/>"/>
	<entry name="SistersOfBattle/RetributorFlavor" value="<string name='Units/SistersOfBattle/RetributorFlavor'/>"/>
	<entry name="SistersOfBattle/SaintCelestine" value="<string name='Units/SistersOfBattle/SaintCelestine'/>"/>
	<entry name="SistersOfBattle/SaintCelestineDescription" value="<string name='Units/SistersOfBattle/SaintCelestineDescription'/>"/>
	<entry name="SistersOfBattle/SaintCelestineFlavor" value="<string name='Units/SistersOfBattle/SaintCelestineFlavor'/>"/>
	<entry name="SistersOfBattle/SisterRepentia" value="<string name='Units/SistersOfBattle/SisterRepentia'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaDescription" value="<string name='Units/SistersOfBattle/SisterRepentiaDescription'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaFlavor" value="<string name='Units/SistersOfBattle/SisterRepentiaFlavor'/>"/>
	<entry name="SistersOfBattle/Zephyrim" value="<string name='Units/SistersOfBattle/Zephyrim'/>"/>
	<entry name="SistersOfBattle/ZephyrimDescription" value="<string name='Units/SistersOfBattle/ZephyrimDescription'/>"/>
	<entry name="SistersOfBattle/ZephyrimFlavor" value="<string name='Units/SistersOfBattle/ZephyrimFlavor'/>"/>
	<entry name="SpaceMarines/Apothecary" value="<string name='Units/SpaceMarines/Apothecary'/>"/>
	<entry name="SpaceMarines/ApothecaryDescription" value="<string name='Units/SpaceMarines/ApothecaryDescription'/>"/>
	<entry name="SpaceMarines/ApothecaryFlavor" value="<string name='Units/SpaceMarines/ApothecaryFlavor'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannon" value="<string name='Units/SpaceMarines/AquilaMacroCannon'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonDescription" value="<string name='Units/SpaceMarines/AquilaMacroCannonDescription'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonFlavor" value="<string name='Units/SpaceMarines/AquilaMacroCannonFlavor'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="<string name='Units/SpaceMarines/AssaultSpaceMarine'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineDescription" value="<string name='Units/SpaceMarines/AssaultSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineFlavor" value="<string name='Units/SpaceMarines/AssaultSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/AssaultTerminator" value="<string name='Units/SpaceMarines/AssaultTerminator'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorDescription" value="<string name='Units/SpaceMarines/AssaultTerminatorDescription'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorFlavor" value="<string name='Units/SpaceMarines/AssaultTerminatorFlavor'/>"/>
	<entry name="SpaceMarines/Captain" value="<string name='Units/SpaceMarines/Captain'/>"/>
	<entry name="SpaceMarines/CaptainDescription" value="<string name='Units/SpaceMarines/CaptainDescription'/>"/>
	<entry name="SpaceMarines/CaptainFlavor" value="<string name='Units/SpaceMarines/CaptainFlavor'/>"/>
	<entry name="SpaceMarines/Chaplain" value="<string name='Units/SpaceMarines/Chaplain'/>"/>
	<entry name="SpaceMarines/ChaplainDescription" value="<string name='Units/SpaceMarines/ChaplainDescription'/>"/>
	<entry name="SpaceMarines/ChaplainFlavor" value="<string name='Units/SpaceMarines/ChaplainFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorCenturion" value="<string name='Units/SpaceMarines/DevastatorCenturion'/>"/>
	<entry name="SpaceMarines/DevastatorCenturionDescription" value="<string name='Units/SpaceMarines/DevastatorCenturionDescription'/>"/>
	<entry name="SpaceMarines/DevastatorCenturionFlavor" value="<string name='Units/SpaceMarines/DevastatorCenturionFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="<string name='Units/SpaceMarines/DevastatorSpaceMarine'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineDescription" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineFlavor" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/Dreadnought" value="<string name='Units/SpaceMarines/Dreadnought'/>"/>
	<entry name="SpaceMarines/DreadnoughtDescription" value="<string name='Units/SpaceMarines/DreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/DreadnoughtFlavor" value="<string name='Units/SpaceMarines/DreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Hunter" value="<string name='Units/SpaceMarines/Hunter'/>"/>
	<entry name="SpaceMarines/HunterDescription" value="<string name='Units/SpaceMarines/HunterDescription'/>"/>
	<entry name="SpaceMarines/HunterFlavor" value="<string name='Units/SpaceMarines/HunterFlavor'/>"/>
	<entry name="SpaceMarines/LandRaider" value="<string name='Units/SpaceMarines/LandRaider'/>"/>
	<entry name="SpaceMarines/LandRaiderDescription" value="<string name='Units/SpaceMarines/LandRaiderDescription'/>"/>
	<entry name="SpaceMarines/LandRaiderFlavor" value="<string name='Units/SpaceMarines/LandRaiderFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeeder" value="<string name='Units/SpaceMarines/LandSpeeder'/>"/>
	<entry name="SpaceMarines/LandSpeederDescription" value="<string name='Units/SpaceMarines/LandSpeederDescription'/>"/>
	<entry name="SpaceMarines/LandSpeederFlavor" value="<string name='Units/SpaceMarines/LandSpeederFlavor'/>"/>
	<entry name="SpaceMarines/Librarian" value="<string name='Units/SpaceMarines/Librarian'/>"/>
	<entry name="SpaceMarines/LibrarianDescription" value="<string name='Units/SpaceMarines/LibrarianDescription'/>"/>
	<entry name="SpaceMarines/LibrarianFlavor" value="<string name='Units/SpaceMarines/LibrarianFlavor'/>"/>
	<entry name="SpaceMarines/Predator" value="<string name='Units/SpaceMarines/Predator'/>"/>
	<entry name="SpaceMarines/PredatorDescription" value="<string name='Units/SpaceMarines/PredatorDescription'/>"/>
	<entry name="SpaceMarines/PredatorFlavor" value="<string name='Units/SpaceMarines/PredatorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressor" value="<string name='Units/SpaceMarines/PrimarisAggressor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressorDescription" value="<string name='Units/SpaceMarines/PrimarisAggressorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisAggressorFlavor" value="<string name='Units/SpaceMarines/PrimarisAggressorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisHellblaster" value="<string name='Units/SpaceMarines/PrimarisHellblaster'/>"/>
	<entry name="SpaceMarines/PrimarisHellblasterDescription" value="<string name='Units/SpaceMarines/PrimarisHellblasterDescription'/>"/>
	<entry name="SpaceMarines/PrimarisHellblasterFlavor" value="<string name='Units/SpaceMarines/PrimarisHellblasterFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisInceptor" value="<string name='Units/SpaceMarines/PrimarisInceptor'/>"/>
	<entry name="SpaceMarines/PrimarisInceptorDescription" value="<string name='Units/SpaceMarines/PrimarisInceptorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisInceptorFlavor" value="<string name='Units/SpaceMarines/PrimarisInceptorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessor" value="<string name='Units/SpaceMarines/PrimarisIntercessor'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessorDescription" value="<string name='Units/SpaceMarines/PrimarisIntercessorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessorFlavor" value="<string name='Units/SpaceMarines/PrimarisIntercessorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATV" value="<string name='Units/SpaceMarines/PrimarisInvaderATV'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATVDescription" value="<string name='Units/SpaceMarines/PrimarisInvaderATVDescription'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATVFlavor" value="<string name='Units/SpaceMarines/PrimarisInvaderATVFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutioner'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerDescription" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerDescription'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerFlavor" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerFlavor'/>"/>
	<entry name="SpaceMarines/Razorback" value="<string name='Units/SpaceMarines/Razorback'/>"/>
	<entry name="SpaceMarines/RazorbackDescription" value="<string name='Units/SpaceMarines/RazorbackDescription'/>"/>
	<entry name="SpaceMarines/RazorbackFlavor" value="<string name='Units/SpaceMarines/RazorbackFlavor'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnought" value="<string name='Units/SpaceMarines/RedemptorDreadnought'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtDescription" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtFlavor" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Scout" value="<string name='Units/SpaceMarines/Scout'/>"/>
	<entry name="SpaceMarines/ScoutDescription" value="<string name='Units/SpaceMarines/ScoutDescription'/>"/>
	<entry name="SpaceMarines/ScoutFlavor" value="<string name='Units/SpaceMarines/ScoutFlavor'/>"/>
	<entry name="SpaceMarines/ScoutBiker" value="<string name='Units/SpaceMarines/ScoutBiker'/>"/>
	<entry name="SpaceMarines/ScoutBikerDescription" value="<string name='Units/SpaceMarines/ScoutBikerDescription'/>"/>
	<entry name="SpaceMarines/ScoutBikerFlavor" value="<string name='Units/SpaceMarines/ScoutBikerFlavor'/>"/>
	<entry name="SpaceMarines/StormravenGunship" value="<string name='Units/SpaceMarines/StormravenGunship'/>"/>
	<entry name="SpaceMarines/StormravenGunshipDescription" value="<string name='Units/SpaceMarines/StormravenGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormravenGunshipFlavor" value="<string name='Units/SpaceMarines/StormravenGunshipFlavor'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike" value="<string name='Units/SpaceMarines/StormSpeederThunderstrike'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeDescription" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeDescription'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeFlavor" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeFlavor'/>"/>
	<entry name="SpaceMarines/StormtalonGunship" value="<string name='Units/SpaceMarines/StormtalonGunship'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipDescription" value="<string name='Units/SpaceMarines/StormtalonGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipFlavor" value="<string name='Units/SpaceMarines/StormtalonGunshipFlavor'/>"/>
	<entry name="SpaceMarines/Terminator" value="<string name='Units/SpaceMarines/Terminator'/>"/>
	<entry name="SpaceMarines/TerminatorDescription" value="<string name='Units/SpaceMarines/TerminatorDescription'/>"/>
	<entry name="SpaceMarines/TerminatorFlavor" value="<string name='Units/SpaceMarines/TerminatorFlavor'/>"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="<string name='Units/SpaceMarines/ThunderfireCannon'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonDescription" value="<string name='Units/SpaceMarines/ThunderfireCannonDescription'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonFlavor" value="<string name='Units/SpaceMarines/ThunderfireCannonFlavor'/>"/>
	<entry name="SpaceMarines/Vindicator" value="<string name='Units/SpaceMarines/Vindicator'/>"/>
	<entry name="SpaceMarines/VindicatorDescription" value="<string name='Units/SpaceMarines/VindicatorDescription'/>"/>
	<entry name="SpaceMarines/VindicatorFlavor" value="<string name='Units/SpaceMarines/VindicatorFlavor'/>"/>
	<entry name="SpaceMarines/Whirlwind" value="<string name='Units/SpaceMarines/Whirlwind'/>"/>
	<entry name="SpaceMarines/WhirlwindDescription" value="<string name='Units/SpaceMarines/WhirlwindDescription'/>"/>
	<entry name="SpaceMarines/WhirlwindFlavor" value="<string name='Units/SpaceMarines/WhirlwindFlavor'/>"/>	
	<entry name="Tau/BroadsideBattlesuit" value="<string name='Units/Tau/BroadsideBattlesuit'/>"/>
	<entry name="Tau/BroadsideBattlesuitDescription" value="<string name='Units/Tau/BroadsideBattlesuitDescription'/>"/>
	<entry name="Tau/BroadsideBattlesuitFlavor" value="<string name='Units/Tau/BroadsideBattlesuitFlavor'/>"/>
	<entry name="Tau/BuilderDrone" value="<string name='Units/Tau/BuilderDrone'/>"/>
	<entry name="Tau/BuilderDroneDescription" value="<string name='Units/Tau/BuilderDroneDescription'/>"/>
	<entry name="Tau/BuilderDroneFlavor" value="<string name='Units/Tau/BuilderDroneFlavor'/>"/>
	<entry name="Tau/Commander" value="<string name='Units/Tau/Commander'/>"/>
	<entry name="Tau/CommanderDescription" value="<string name='Units/Tau/CommanderDescription'/>"/>
	<entry name="Tau/CommanderFlavor" value="<string name='Units/Tau/CommanderFlavor'/>"/>
	<entry name="Tau/CrisisBattlesuit" value="<string name='Units/Tau/CrisisBattlesuit'/>"/>
	<entry name="Tau/CrisisBattlesuitDescription" value="<string name='Units/Tau/CrisisBattlesuitDescription'/>"/>
	<entry name="Tau/CrisisBattlesuitFlavor" value="<string name='Units/Tau/CrisisBattlesuitFlavor'/>"/>
	<entry name="Tau/Devilfish" value="<string name='Units/Tau/Devilfish'/>"/>
	<entry name="Tau/DevilfishDescription" value="<string name='Units/Tau/DevilfishDescription'/>"/>
	<entry name="Tau/DevilfishFlavor" value="<string name='Units/Tau/DevilfishFlavor'/>"/>
	<entry name="Tau/Ethereal" value="<string name='Units/Tau/Ethereal'/>"/>
	<entry name="Tau/EtherealDescription" value="<string name='Units/Tau/EtherealDescription'/>"/>
	<entry name="Tau/EtherealFlavor" value="<string name='Units/Tau/EtherealFlavor'/>"/>
	<entry name="Tau/FireWarriorBreacher" value="<string name='Units/Tau/FireWarriorBreacher'/>"/>
	<entry name="Tau/FireWarriorBreacherDescription" value="<string name='Units/Tau/FireWarriorBreacherDescription'/>"/>
	<entry name="Tau/FireWarriorBreacherFlavor" value="<string name='Units/Tau/FireWarriorBreacherFlavor'/>"/>
	<entry name="Tau/GunDrone" value="<string name='Units/Tau/GunDrone'/>"/>
	<entry name="Tau/GunDroneDescription" value="Proporciona a los Guerreros de Fuego, armaduras XV8 Crisis, armaduras mimética XV25, armaduras XV88 -Apocalipsis-, Filoardiente, Etéreo y al Comandante la habilidad de desplegar temporalmente drones básicos de combate."/>
	<entry name="Tau/GunDroneFlavor" value="<string name='Units/Tau/GunDroneFlavor'/>"/>
	<entry name="Tau/HammerheadGunship" value="<string name='Units/Tau/HammerheadGunship'/>"/>
	<entry name="Tau/HammerheadGunshipDescription" value="<string name='Units/Tau/HammerheadGunshipDescription'/>"/>
	<entry name="Tau/HammerheadGunshipFlavor" value="<string name='Units/Tau/HammerheadGunshipFlavor'/>"/>
	<entry name="Tau/KrootoxRider" value="<string name='Units/Tau/KrootoxRider'/>"/>
	<entry name="Tau/KrootoxRiderDescription" value="<string name='Units/Tau/KrootoxRiderDescription'/>"/>
	<entry name="Tau/KrootoxRiderFlavor" value="<string name='Units/Tau/KrootoxRiderFlavor'/>"/>
	<entry name="Tau/MarkerDrone" value="<string name='Units/Tau/MarkerDrone'/>"/>
	<entry name="Tau/MarkerDroneDescription" value="Proporciona a los Guerreros de Fuego, armaduras XV8 Crisis, armaduras mimética XV25, armaduras XV88 -Apocalipsis-, Filoardiente, Etéreo y al Comandante la habilidad de desplegar temporalmente drones que señalan al enemigo para que sufra más daño."/>
	<entry name="Tau/MarkerDroneFlavor" value="<string name='Units/Tau/MarkerDroneFlavor'/>"/>
	<entry name="Tau/Pathfinder" value="<string name='Units/Tau/Pathfinder'/>"/>
	<entry name="Tau/PathfinderDescription" value="<string name='Units/Tau/PathfinderDescription'/>"/>
	<entry name="Tau/PathfinderFlavor" value="<string name='Units/Tau/PathfinderFlavor'/>"/>
	<entry name="Tau/RiptideBattlesuit" value="<string name='Units/Tau/RiptideBattlesuit'/>"/>
	<entry name="Tau/RiptideBattlesuitDescription" value="<string name='Units/Tau/RiptideBattlesuitDescription'/>"/>
	<entry name="Tau/RiptideBattlesuitFlavor" value="<string name='Units/Tau/RiptideBattlesuitFlavor'/>"/>
	<entry name="Tau/RVarnaBattlesuit" value="<string name='Units/Tau/RVarnaBattlesuit'/>"/>
	<entry name="Tau/RVarnaBattlesuitDescription" value="<string name='Units/Tau/RVarnaBattlesuitDescription'/>"/>
	<entry name="Tau/RVarnaBattlesuitFlavor" value="<string name='Units/Tau/RVarnaBattlesuitFlavor'/>"/>
	<entry name="Tau/ShieldDrone" value="<string name='Units/Tau/ShieldDrone'/>"/>
	<entry name="Tau/ShieldDroneDescription" value="Proporciona a los Guerreros de Fuego, armaduras XV8 Crisis, armaduras mimética XV25, armaduras XV88 -Apocalipsis-, Filoardiente, Etéreo y al Comandante la habilidad de desplegar temporalmente drones que protegen a los aliados."/>
	<entry name="Tau/ShieldDroneFlavor" value="<string name='Units/Tau/ShieldDroneFlavor'/>"/>
	<entry name="Tau/SkyRayGunship" value="<string name='Units/Tau/SkyRayGunship'/>"/>
	<entry name="Tau/SkyRayGunshipDescription" value="<string name='Units/Tau/SkyRayGunshipDescription'/>"/>
	<entry name="Tau/SkyRayGunshipFlavor" value="<string name='Units/Tau/SkyRayGunshipFlavor'/>"/>
	<entry name="Tau/StealthBattlesuit" value="<string name='Units/Tau/StealthBattlesuit'/>"/>
	<entry name="Tau/StealthBattlesuitDescription" value="<string name='Units/Tau/StealthBattlesuitDescription'/>"/>
	<entry name="Tau/StealthBattlesuitFlavor" value="<string name='Units/Tau/StealthBattlesuitFlavor'/>"/>
	<entry name="Tau/Stormsurge" value="<string name='Units/Tau/Stormsurge'/>"/>
	<entry name="Tau/StormsurgeDescription" value="<string name='Units/Tau/StormsurgeDescription'/>"/>
	<entry name="Tau/StormsurgeFlavor" value="<string name='Units/Tau/StormsurgeFlavor'/>"/>
	<entry name="Tau/SunSharkBomber" value="<string name='Units/Tau/SunSharkBomber'/>"/>
	<entry name="Tau/SunSharkBomberDescription" value="<string name='Units/Tau/SunSharkBomberDescription'/>"/>
	<entry name="Tau/SunSharkBomberFlavor" value="<string name='Units/Tau/SunSharkBomberFlavor'/>"/>
	<entry name="Tau/TidewallGunrig" value="<string name='Units/Tau/TidewallGunrig'/>"/>
	<entry name="Tau/TidewallGunrigDescription" value="Otorga a los drones constructores la capacidad de construir una fortificación fuertemente armada que puede ser movida transportando tropas."/>
	<entry name="Tau/TidewallGunrigFlavor" value="<string name='Units/Tau/TidewallGunrigFlavor'/>"/>
	<entry name="Tau/TigerShark" value="<string name='Units/Tau/TigerShark'/>"/>
	<entry name="Tau/TigerSharkDescription" value="<string name='Units/Tau/TigerSharkDescription'/>"/>
	<entry name="Tau/TigerSharkFlavor" value="<string name='Units/Tau/TigerSharkFlavor'/>"/>
	<entry name="Tyranids/Biovore" value="<string name='Units/Tyranids/Biovore'/>"/>
	<entry name="Tyranids/BiovoreDescription" value="<string name='Units/Tyranids/BiovoreDescription'/>"/>
	<entry name="Tyranids/BiovoreFlavor" value="<string name='Units/Tyranids/BiovoreFlavor'/>"/>
	<entry name="Tyranids/Carnifex" value="<string name='Units/Tyranids/Carnifex'/>"/>
	<entry name="Tyranids/CarnifexDescription" value="<string name='Units/Tyranids/CarnifexDescription'/>"/>
	<entry name="Tyranids/CarnifexFlavor" value="<string name='Units/Tyranids/CarnifexFlavor'/>"/>
	<entry name="Tyranids/Exocrine" value="<string name='Units/Tyranids/Exocrine'/>"/>
	<entry name="Tyranids/ExocrineDescription" value="<string name='Units/Tyranids/ExocrineDescription'/>"/>
	<entry name="Tyranids/ExocrineFlavor" value="<string name='Units/Tyranids/ExocrineFlavor'/>"/>
	<entry name="Tyranids/Gargoyle" value="<string name='Units/Tyranids/Gargoyle'/>"/>
	<entry name="Tyranids/GargoyleDescription" value="<string name='Units/Tyranids/GargoyleDescription'/>"/>
	<entry name="Tyranids/GargoyleFlavor" value="<string name='Units/Tyranids/GargoyleFlavor'/>"/>
	<entry name="Tyranids/Haruspex" value="<string name='Units/Tyranids/Haruspex'/>"/>
	<entry name="Tyranids/HaruspexDescription" value="<string name='Units/Tyranids/HaruspexDescription'/>"/>
	<entry name="Tyranids/HaruspexFlavor" value="<string name='Units/Tyranids/HaruspexFlavor'/>"/>
	<entry name="Tyranids/HiveGuard" value="<string name='Units/Tyranids/HiveGuard'/>"/>
	<entry name="Tyranids/HiveGuardDescription" value="<string name='Units/Tyranids/HiveGuardDescription'/>"/>
	<entry name="Tyranids/HiveGuardFlavor" value="<string name='Units/Tyranids/HiveGuardFlavor'/>"/>
	<entry name="Tyranids/HiveTyrant" value="<string name='Units/Tyranids/HiveTyrant'/>"/>
	<entry name="Tyranids/HiveTyrantDescription" value="<string name='Units/Tyranids/HiveTyrantDescription'/>"/>
	<entry name="Tyranids/HiveTyrantFlavor" value="<string name='Units/Tyranids/HiveTyrantFlavor'/>"/>
	<entry name="Tyranids/Hormagaunt" value="<string name='Units/Tyranids/Hormagaunt'/>"/>
	<entry name="Tyranids/HormagauntDescription" value="<string name='Units/Tyranids/HormagauntDescription'/>"/>
	<entry name="Tyranids/HormagauntFlavor" value="<string name='Units/Tyranids/HormagauntFlavor'/>"/>
	<entry name="Tyranids/Lictor" value="<string name='Units/Tyranids/Lictor'/>"/>
	<entry name="Tyranids/LictorDescription" value="<string name='Units/Tyranids/LictorDescription'/>"/>
	<entry name="Tyranids/LictorFlavor" value="<string name='Units/Tyranids/LictorFlavor'/>"/>
	<entry name="Tyranids/NornEmissary" value="<string name='Units/Tyranids/NornEmissary'/>"/>
	<entry name="Tyranids/NornEmissaryDescription" value="<string name='Units/Tyranids/NornEmissaryDescription'/>"/>
	<entry name="Tyranids/NornEmissaryFlavor" value="<string name='Units/Tyranids/NornEmissaryFlavor'/>"/>
	<entry name="Tyranids/Ravener" value="<string name='Units/Tyranids/Ravener'/>"/>
	<entry name="Tyranids/RavenerDescription" value="<string name='Units/Tyranids/RavenerDescription'/>"/>
	<entry name="Tyranids/RavenerFlavor" value="<string name='Units/Tyranids/RavenerFlavor'/>"/>
	<entry name="Tyranids/ScythedHierodule" value="<string name='Units/Tyranids/ScythedHierodule'/>"/>
	<entry name="Tyranids/ScythedHieroduleDescription" value="<string name='Units/Tyranids/ScythedHieroduleDescription'/>"/>
	<entry name="Tyranids/ScythedHieroduleFlavor" value="<string name='Units/Tyranids/ScythedHieroduleFlavor'/>"/>
	<entry name="Tyranids/Tervigon" value="<string name='Units/Tyranids/Tervigon'/>"/>
	<entry name="Tyranids/TervigonDescription" value="<string name='Units/Tyranids/TervigonDescription'/>"/>
	<entry name="Tyranids/TervigonFlavor" value="<string name='Units/Tyranids/TervigonFlavor'/>"/>
	<entry name="Tyranids/Trygon" value="<string name='Units/Tyranids/Trygon'/>"/>
	<entry name="Tyranids/TrygonDescription" value="<string name='Units/Tyranids/TrygonDescription'/>"/>
	<entry name="Tyranids/TrygonFlavor" value="<string name='Units/Tyranids/TrygonFlavor'/>"/>
	<entry name="Tyranids/Tyrannofex" value="<string name='Units/Tyranids/Tyrannofex'/>"/>
	<entry name="Tyranids/TyrannofexDescription" value="<string name='Units/Tyranids/TyrannofexDescription'/>"/>
	<entry name="Tyranids/TyrannofexFlavor" value="<string name='Units/Tyranids/TyrannofexFlavor'/>"/>
	<entry name="Tyranids/Venomthrope" value="<string name='Units/Tyranids/Venomthrope'/>"/>
	<entry name="Tyranids/VenomthropeDescription" value="<string name='Units/Tyranids/VenomthropeDescription'/>"/>
	<entry name="Tyranids/VenomthropeFlavor" value="<string name='Units/Tyranids/VenomthropeFlavor'/>"/>
	<entry name="Tyranids/Warrior" value="<string name='Units/Tyranids/Warrior'/>"/>
	<entry name="Tyranids/WarriorDescription" value="<string name='Units/Tyranids/WarriorDescription'/>"/>
	<entry name="Tyranids/WarriorFlavor" value="<string name='Units/Tyranids/WarriorFlavor'/>"/>
	<entry name="Tyranids/Zoanthrope" value="<string name='Units/Tyranids/Zoanthrope'/>"/>
	<entry name="Tyranids/ZoanthropeDescription" value="<string name='Units/Tyranids/ZoanthropeDescription'/>"/>
	<entry name="Tyranids/ZoanthropeFlavor" value="<string name='Units/Tyranids/ZoanthropeFlavor'/>"/>
	<entry name="Tyranids/Maleceptor" value="<string name='Units/Tyranids/Maleceptor'/>"/>
	<entry name="Tyranids/MaleceptorDescription" value="<string name='Units/Tyranids/MaleceptorDescription'/>"/>
	<entry name="Tyranids/MaleceptorFlavor" value="<string name='Units/Tyranids/MaleceptorFlavor'/>"/>
	
	<!-- Other -->
	<entry name="SmokeLauncher" value="Descargadores de humo"/>
	<entry name="SmokeLauncherDescription" value="Otorga a los vehículos terrestres la habilidad de lanzar cortinas de humo que aumentan la reducción de daño por ataques a distancia."/>
	<entry name="SmokeLauncherFlavor" value="Algunos vehículos tienen pequeños lanzadores montados que llevan contenedores de humo. Estos son usados para oscurecer el vehículo detras de cortinas de humo, lo que le permite cruzar zonas abiertas con mayor seguridad—aunque lo hace a costa de disparar sus propias armas."/>
 	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegration'/>"/>
 	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Aumenta la producción de investigación del edificio por cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegrationFlavor'/>"/>
 	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
 	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeDescription'/>"/>
 	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonus'/>"/>
 	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Aumenta la penetración de armadura de las armas de asalto."/>
 	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonusFlavor'/>"/>
 	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
 	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceDescription'/>"/>
 	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
 	<entry name="AdeptusMechanicus/BlessedConduits" value="Conductos Benditos"/>
 	<entry name="AdeptusMechanicus/BlessedConduitsDescription" value="Reduce el enfriamiento de la Sobrecarga de Energía."/>
 	<entry name="AdeptusMechanicus/BlessedConduitsFlavor" value="“Y cuando al fin llegó al vehículo, percibió la angustia del motor en él e inmediatamente pulsó la runa, y todo estuvo bien. A continuación el motor arrancó y se llenó de fuerza…“<br/>—Señor de los Motores, 16º Tomo, Versículo 2001"/>
 	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
 	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Habilidad para los Zancoférreos Ballistarius, Surcadunas Skorpius, Trepadunas Onagros y Desintegrador Skorpius que reduce la pérdida de moral de las unidades del Adeptus Mechanicus adyacentes."/>
 	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
 	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
 	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeDescription'/>"/>
 	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
 	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistDescription'/>"/>
 	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
 	<entry name="AdeptusMechanicus/CityTier2" value="<string name='Traits/AdeptusMechanicus/CityTier2'/>"/>
 	<entry name="AdeptusMechanicus/CityTier2Description" value="<string name='Traits/AdeptusMechanicus/CityTier2Description'/>"/>
 	<entry name="AdeptusMechanicus/CityTier2Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier2Flavor'/>"/>
 	<entry name="AdeptusMechanicus/CityTier3" value="<string name='Traits/AdeptusMechanicus/CityTier3'/>"/>
 	<entry name="AdeptusMechanicus/CityTier3Description" value="<string name='Traits/AdeptusMechanicus/CityTier3Description'/>"/>
 	<entry name="AdeptusMechanicus/CityTier3Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier3Flavor'/>"/>
 	<entry name="AdeptusMechanicus/CognisHeavyStubber" value="<string name='Weapons/CognisHeavyStubber'/>"/>
 	<entry name="AdeptusMechanicus/CognisHeavyStubberDescription" value="Otorga una Ametralladora Pesada Cognis a los Trepadunas Onagro."/>
 	<entry name="AdeptusMechanicus/CognisHeavyStubberFlavor" value="<string name='Weapons/CognisHeavyStubberFlavor'/>"/>
 	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Traits/AdeptusMechanicus/CommandUplink'/>"/>
 	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Habilidad del Arqueóptero Transvector y del Arqueóptero Estratorraptor que reduce la pérdida de moral de las unidades del Adeptus Mechanicus adyacentes."/>
 	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Traits/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
 	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeDescription'/>"/>
 	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/EnhancedDataTether" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTether'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Aumenta la moral de la Vanguardia Skitarii y los Rangers Skitarii."/>
 	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTetherFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ExtraInfantryArmour" value="Protocolos Graianos"/>
 	<entry name="AdeptusMechanicus/ExtraInfantryArmourDescription" value="Aumenta la armadura de la infantería."/>
 	<entry name="AdeptusMechanicus/ExtraInfantryArmourFlavor" value="La integración de los protocolos Graianos en los circuitos de mando de un Skitarii mejora aún más su ya de por sí tremenda supervivencia. El Mundo Forja de Graia es conocido por negarse a rendirse jamás, dado que su lógica es simplemente irrefutable."/>
 	<entry name="AdeptusMechanicus/ExtraVehicleArmour" value="Ablación Agripinana"/>
 	<entry name="AdeptusMechanicus/ExtraVehicleArmourDescription" value="Aumenta la armadura de los vehículos."/>
 	<entry name="AdeptusMechanicus/ExtraVehicleArmourFlavor" value="El vivir cerca del Ojo del Terror tras la caída de Cadia ha transformado al Adeptus Mechanicus de Agripinaa en expertos en defensa, particularmente cuando se trata de mantener operativos a sus vehículos bajo el fuego."/>
 	<entry name="AdeptusMechanicus/FidorumVossPrime" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrime'/>"/>
 	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Aumenta la producción de influencia del edificio por cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrimeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrathDescription" value="Otorga a los Zancoférreos Ballistarius, Electrosacerdotes Fulguritas, Trepadunas Onagros, Tecnorrapaces Pteraxii y Caballeros Cruzados la habilidad de llevar a cabo ataques más devastadores."/>
 	<entry name="AdeptusMechanicus/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
 	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonus'/>"/>
 	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Aumenta la penetración de armadura de las armas pesadas."/>
 	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonusFlavor'/>"/>
 	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiah'/>"/>
 	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Aumenta la producción de lealtad del edificio por cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiahFlavor'/>"/>
 	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
 	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulDescription'/>"/>
 	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
 	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
 	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightDescription'/>"/>
 	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
 	<entry name="AdeptusMechanicus/LitanyOfTheElectromancer" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancer'/>"/>
 	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerDescription" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerDescription'/>"/>
 	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerFlavor" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerFlavor'/>"/>
 	<entry name="AdeptusMechanicus/LucianSpecialisation" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisation'/>"/>
 	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationDescription'/>"/>
 	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationFlavor'/>"/>
 	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonus'/>"/>
 	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Aumenta la penetración de armadura de las armas cuerpo a cuerpo."/>
 	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonusFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Omnispex" value="<string name='Traits/AdeptusMechanicus/Omnispex'/>"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="Permite a la Vanguardia Skitarii y los Rangers Skitarii ignorar la reducción de daño por ataque a distancia."/>
 	<entry name="AdeptusMechanicus/OmnispexFlavor" value="<string name='Traits/AdeptusMechanicus/OmnispexFlavor'/>"/>
 	<entry name="AdeptusMechanicus/OptateRestrictions" value="<string name='Traits/AdeptusMechanicus/OptateRestrictions'/>"/>
 	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Aumenta el límite de población de los Templos Hab."/>
 	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="<string name='Traits/AdeptusMechanicus/OptateRestrictionsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
 	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeDescription'/>"/>
 	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocols'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Aumenta la tasa de crecimiento de las ciudades"/>
 	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocolsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
 	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmDescription'/>"/>
 	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SolarReflectors" value="<string name='Traits/AdeptusMechanicus/SolarReflectors'/>"/>
 	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Aumenta la producción de energía del edificio por cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="<string name='Traits/AdeptusMechanicus/SolarReflectorsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeams'/>"/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Aumenta la producción de comida de un edificio por cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeamsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/StygianEnlightenment" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenment'/>"/>
 	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentDescription'/>"/>
 	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentFlavor'/>"/>
 	<entry name="AdeptusMechanicus/TerranGeneralism" value="<string name='Traits/AdeptusMechanicus/TerranGeneralism'/>"/>
 	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismDescription'/>"/>
 	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismFlavor'/>"/> 
 	<entry name="AdeptusMechanicus/ThermoExchangeEfficiency" value="Eficiencia del Intercambiador Térmico"/>
 	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyDescription" value="Aumenta el bonus de producción de la Sobrecarga de Energía."/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyFlavor" value="Paradójicamente, ralentizar las entradas del Intercambiador de Calor y alimentar el plasma a través de un segundo juego de acoplamientos térmicos y condensadores permite alargar el lapso de tiempo en el cual puede operar más allá de sus objetivos nominales. O, al menos, reduce el número de vidas perdidas y reparaciones necesarias."/>
 	<entry name="AdeptusMechanicus/TriplexNecessity" value="<string name='Traits/AdeptusMechanicus/TriplexNecessity'/>"/>
 	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Aumenta la producción de minerales del edificio por cada edificio en una casilla adyacente."/>
 	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="<string name='Traits/AdeptusMechanicus/TriplexNecessityFlavor'/>"/>
 	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannon" value="<string name='Weapons/TwinLinkedIcarusAutocannon'/>"/>
 	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonDescription" value="Otorga a los Caballeros Cruzados un Cañón Automático Gemelo Ícaro."/>
 	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonFlavor" value="<string name='Weapons/TwinLinkedIcarusAutocannonFlavor'/>"/>
 	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
 	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Aumenta la pérdida de moral de las unidades enemigas adyacentes a Infiltradores Sicarianos."/>
 	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/XenariteAcceptance" value="Aceptación Xenarita"/>
> 	<entry name="AdeptusMechanicus/XenariteAcceptanceDescription" value="Aumenta la cantidad plana de investigación obtenida de las Ruinas de Vaul."/>
 	<entry name="AdeptusMechanicus/XenariteAcceptanceFlavor" value="Parece que los tecnosacerdotes Xenaritas de Stygies VIII se están saliendo con la suya también en Gladius Prime, posicionando unidades de observación y drones en cada puesto avanzado para entender mejor a los Xenos. Las aplicaciones prácticas en forma de investigación saltan a la vista…"/>
	<entry name="AstraMilitarum/AdditionalHeavyBolters" value="Bólteres pesados adicionales"/>
 	<entry name="AstraMilitarum/AdditionalHeavyBoltersDescription" value="Otorga Bólters Pesados extra a los tanques Baneblade, Leman Russ, Rogal Dorn y a las Valkyries."/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachine" value="<string name='Actions/AwakenTheMachine'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachineDescription" value="Permite a los Tecnosacerdote Visioingenieros tener la habilidad de aumentar el daño de vehículos aliados."/>
	<entry name="AstraMilitarum/AwakenTheMachineFlavor" value="<string name='Actions/AwakenTheMachineFlavor'/>"/>
	<entry name="AstraMilitarum/BanebladeLascannon" value="Baneblade con cañones láser"/>
	<entry name="AstraMilitarum/BanebladeLascannonDescription" value="Arma a los Baneblades con más cañones láser."/>
	<entry name="AstraMilitarum/BanebladeLascannonFlavor" value="<string name='Weapons/LascannonFlavor'/>"/>
	<entry name="AstraMilitarum/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Aumenta la penetración de blindaje de granadas, misiles y armas de área."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Aumenta la penetración de blindaje de las armas bólter."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BruteShield" value="<string name='Traits/BruteShield'/>"/>
	<entry name="AstraMilitarum/BruteShieldDescription" value="Aumenta el daño y la reducción del daño de los Bullgryns."/>
	<entry name="AstraMilitarum/BruteShieldFlavor" value="<string name='Traits/BruteShieldFlavor'/>"/>
	<entry name="AstraMilitarum/CamoNetting" value="<string name='Traits/CamoNetting'/>"/>
	<entry name="AstraMilitarum/CamoNettingDescription" value="Aumenta la reducción de daño por ataque a distancia de los vehículos terrestres."/>
	<entry name="AstraMilitarum/CamoNettingFlavor" value="<string name='Traits/CamoNettingFlavor'/>"/>
	<entry name="AstraMilitarum/ChaffLauncher" value="Lanzador de Chaff"/>
	<entry name="AstraMilitarum/ChaffLauncherDescription" value="Proporciona a los caza Thunderbolt y a los bombarderos Marauder la habilidad de lanzar chaffs que aumenta la reducción del daño a distancia."/>
	<entry name="AstraMilitarum/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="AstraMilitarum/CityTier2" value="<string name='Traits/AstraMilitarum/CityTier2'/>"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Aumenta el radio de adquisición de casillas de las ciudades."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="<string name='Traits/AstraMilitarum/CityTier2Flavor'/>"/>
	<entry name="AstraMilitarum/CityTier3" value="<string name='Traits/AstraMilitarum/CityTier3'/>"/>
	<entry name="AstraMilitarum/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="<string name='Traits/AstraMilitarum/CityTier3Flavor'/>"/>
	<entry name="AstraMilitarum/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="AstraMilitarum/DozerBladeDescription" value="Permite a los tanques moverse sin penalización sobre terreno difícil."/>
	<entry name="AstraMilitarum/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmourDescription" value="Aumenta la armadura de la infantería."/>
	<entry name="AstraMilitarum/ExtraInfantryArmourFlavor" value="La mayoría de la infantería en el 41º milenio está equipada con una armadura antibalas o su equivalente. Si un comandante quiere que sus tropas tengan más que una pequeña posibilidad de supervivencia, los equiparán con algo más cercano a la armadura de caparazón derivada del armaplas Astra Militarum."/>
	<entry name="AstraMilitarum/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="AstraMilitarum/ExtraVehicleArmourDescription" value="Aumenta la armadura de los vehículos."/>
	<entry name="AstraMilitarum/ExtraVehicleArmourFlavor" value="Agregar placas de blindaje adicionales a los tanques podría estar a la altura de la herejía para los tecnosacerdotes, pero para los soldados del Astra Militarum es la norma. Placas ablativas o blindajes personalizados para tratar con armas particulares no son desconocidas."/>
	<entry name="AstraMilitarum/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
 	<entry name="AstraMilitarum/FragGrenadeDescription" value="Otorga la habilidad de lanzar granadas de fragmentación anti-infantería a los Bullgryns, Guardias Imperiales, Escuadras de Armas Pesadas, Comisarios Mayores, Psíquicos Primaris, Tecnosacerdotes Visioingenieros y Vástagos Tempestus."/>
	<entry name="AstraMilitarum/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AstraMilitarum/HammerOfWrathDescription" value="Proporciona a los Bullgryns y a los Centinelas Exploradores la habilidad de realizar más ataques devastadores."/>
	<entry name="AstraMilitarum/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissileDescription" value="Proporciona a los vehículos de tierra la habilidad de disparar misiles cazador-asesino."/>
	<entry name="AstraMilitarum/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialSplendour" value="<string name='Traits/ImperialSplendour'/>"/>
	<entry name="AstraMilitarum/ImperialSplendourDescription" value="Aumenta la influencia de las ciudades del Astra Militarum."/>
	<entry name="AstraMilitarum/ImperialSplendourFlavor" value="<string name='Traits/ImperialSplendourFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpoint" value="<string name='Units/AstraMilitarum/ImperialStrongpoint'/>"/>
 	<entry name="AstraMilitarum/ImperialStrongpointDescription" value="Otorga la habilidad de construir fortificaciones con Bólters Pesados a los tecnosacerdotes visioingenieros."/>
	<entry name="AstraMilitarum/ImperialStrongpointFlavor" value="<string name='Units/AstraMilitarum/ImperialStrongpointFlavor'/>"/>
	<entry name="AstraMilitarum/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
 	<entry name="AstraMilitarum/KrakGrenadeDescription" value="Otorga la habilidad de lanzar granadas anti-blindaje a los Guardias Imperiales, Escuadras de Armas Pesadas, Comisarios Mayores, Psíquicos Primaris, Tecnosacerdotes Visioingenieros y Vástagos Tempestus."/>
	<entry name="AstraMilitarum/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Aumenta la penetración de blindaje de las armas."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="AstraMilitarum/MediPack" value="<string name='Actions/MediPack'/>"/>
	<entry name="AstraMilitarum/MediPackDescription" value="Proporciona a la Guardia Imperial la habilidad de curarse a si mismos durante el combate."/>
	<entry name="AstraMilitarum/MediPackFlavor" value="<string name='Actions/MediPackFlavor'/>"/>
	<entry name="AstraMilitarum/Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="AstraMilitarum/MisfortuneDescription" value="Proporciona a los Psíquicos de Batalla la habilidad de maldecir unidades enemigas para que reciban más daño."/>
	<entry name="AstraMilitarum/MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="AstraMilitarum/RecoveryGear" value="<string name='Traits/RecoveryGear'/>"/>
	<entry name="AstraMilitarum/RecoveryGearDescription" value="Aumenta el nivel de curación para los vehículos de tierra."/>
	<entry name="AstraMilitarum/RecoveryGearFlavor" value="<string name='Traits/RecoveryGearFlavor'/>"/>
	<entry name="AstraMilitarum/RelicPlating" value="<string name='Traits/RelicPlating'/>"/>
	<entry name="AstraMilitarum/RelicPlatingDescription" value="Aumenta la reducción de daño por fuego brujo de los vehículos terrestres."/>
	<entry name="AstraMilitarum/RelicPlatingFlavor" value="<string name='Traits/RelicPlatingFlavor'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissile" value="<string name='Weapons/SkystrikeMissile'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissileDescription" value="Proporciona a los caza Thunderbolts la habilidad de disparar misiles Skystrike."/>
	<entry name="AstraMilitarum/SkystrikeMissileFlavor" value="<string name='Weapons/SkystrikeMissileFlavor'/>"/>
	<entry name="AstraMilitarum/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilots" value="<string name='Traits/TrainedSentinelPilots'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsDescription" value="Aumenta el daño de los Centinelas Exploradores."/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsFlavor" value="<string name='Traits/TrainedSentinelPilotsFlavor'/>"/>
	<entry name="AstraMilitarum/VoidShieldGenerator" value="<string name='Units/AstraMilitarum/VoidShieldGenerator'/>"/>
	<entry name="AstraMilitarum/VoidShieldGeneratorDescription" value="Otorga a los Tecnosacerdote Visioingenieros la habilidad de construir generadores de escudos que aumentan la reducción de daño por ataques a distancia a las unidades dentro del alcance."/>
	<entry name="AstraMilitarum/VoidShieldGeneratorFlavor" value="<string name='Units/AstraMilitarum/VoidShieldGeneratorFlavor'/>"/>
	<entry name="AstraMilitarum/VoxCaster" value="<string name='Traits/VoxCaster'/>"/>
	<entry name="AstraMilitarum/VoxCasterDescription" value="Reduce la pérdida de moral de la Guardia Imperial."/>
	<entry name="AstraMilitarum/VoxCasterFlavor" value="<string name='Traits/VoxCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculum'/>"/>
 	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Otorga a los Campeones del Caos la oportunidad de ganar precisión cuando matas a un enemigo (Bendición del Caos)."/>
 	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculumFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/BlastDamage" value="<string name='Traits/ChaosSpaceMarines/BlastDamage'/>"/>
 	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Aumenta la penetración de blindaje de las armas bólter."/>
 	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BlastDamageFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Bloated" value="<string name='Traits/ChaosSpaceMarines/Bloated'/>"/>
 	<entry name="ChaosSpaceMarines/BloatedDescription" value="Otorga a los Campeones del Caos la oportunidad de restaurar sus puntos de golpe al matar a un enemigo."/>
 	<entry name="ChaosSpaceMarines/BloatedFlavor" value="<string name='Traits/ChaosSpaceMarines/BloatedFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/BoltDamage" value="<string name='Traits/ChaosSpaceMarines/BoltDamage'/>"/>
 	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Aumenta el daño de las armas de rayo."/>
 	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BoltDamageFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/ChaosRising" value="El Alzamiento del Caos"/>
 	<entry name="ChaosSpaceMarines/ChaosRisingDescription" value="Reduce el coste de asentamiento de nuevas ciudades."/>
 	<entry name="ChaosSpaceMarines/ChaosRisingFlavor" value="La población imperial de Gladius sabe poco del Caos, pero en el infierno de las invasiones Xenos, su fe en el lejano Emperador se están poniendo en duda. Es fácil para sus adoradores y apóstoles dirigir sus mentes a los dioses y a su perdición."/>
 	<entry name="ChaosSpaceMarines/CityTier2" value="<string name='Traits/ChaosSpaceMarines/CityTier2'/>"/>
 	<entry name="ChaosSpaceMarines/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
 	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier2Flavor'/>"/>
 	<entry name="ChaosSpaceMarines/CityTier3" value="<string name='Traits/ChaosSpaceMarines/CityTier3'/>"/>
 	<entry name="ChaosSpaceMarines/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
 	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier3Flavor'/>"/>
 	<entry name="ChaosSpaceMarines/CrystallineBody" value="<string name='Traits/ChaosSpaceMarines/CrystallineBody'/>"/>
 	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Otorga a los Campeones del Caos la oportunidad de ganar puntos de golpe al matar a un enemigo (Bendición del Caos)."/>
 	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="<string name='Traits/ChaosSpaceMarines/CrystallineBodyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
 	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Otorga a los rhinos, profanadores y land raiders del Caos la capacidad de prevenir ataques de disparos defensivos."/>
 	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
 	<entry name="ChaosSpaceMarines/ExtraInfantryArmourDescription" value="Aumenta la armadura de la infantería."/>
 	<entry name="ChaosSpaceMarines/ExtraInfantryArmourFlavor" value="La mayoría de la infantería en el 41º milenio está equipada con una armadura antibalas o su equivalente. Si un comandante quiere que sus tropas tengan más que una pequeña posibilidad de supervivencia, los equiparán con algo más cercano a la armadura de caparazón derivada del armaplas Astra Militarum."/>
 	<entry name="ChaosSpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
 	<entry name="ChaosSpaceMarines/ExtraVehicleArmourDescription" value="Aumenta la armadura de los vehículos."/>
 	<entry name="ChaosSpaceMarines/ExtraVehicleArmourFlavor" value="Agregar placas de blindaje adicionales a los tanques podría ser considerado una herejía para los Sacerdotes de la Tecnología, pero para los soldados del Astra Militarum es la norma. Placas ablativas o armaduras personalizadas para tratar con armas particulares no son desconocidas."/>
 	<entry name="ChaosSpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
 	<entry name="ChaosSpaceMarines/FragGrenadeDescription" value="Otorga a los Señores del Caos, Marines espaciales del Caos, Aniquiladores, Berserkers de Khorne , Señores de la Posesión y Herreros Disformes la habilidad de lanzar granadas anti-infantería."/>
 	<entry name="ChaosSpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/GiftOfMutation" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutation'/>"/>
 	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="Otorga a los Grants Chaos Lord, Chaos Space Marines, Chaos Terminators, Havocs, Khorne Berzerkers, Master of Possession, Warp Talons y Warpsmith una Bendición del Caos desbloqueada al azar."/>
 	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutationFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrathDescription" value="Otorga a los Príncipe Demonio, Profanadores, Diablos de la Forja, Grandes Escorpiones de Bronce, Brutos Infernales, Diablos Despedazadores, Trepadores Ponzoñosos y Espolones de la Disformidad la habilidad de realizar ataques más devastadores."/>
 	<entry name="ChaosSpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/HavocLauncher" value="<string name='Weapons/HavocLauncher'/>"/>
 	<entry name="ChaosSpaceMarines/HavocLauncherDescription" value="Otorga a los rhinos y land raiders del Caos un arma explosiva de rango medio."/>
 	<entry name="ChaosSpaceMarines/HavocLauncherFlavor" value="<string name='Weapons/HavocLauncherFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairDescription'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessDescription'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfFlame" value="<string name='Actions/ChaosSpaceMarines/IconOfFlame'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfFlameDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameDescription'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfFlameFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfVengeance" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeance'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfVengeanceDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceDescription'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfVengeanceFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathDescription'/>"/>
 	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
 	<entry name="ChaosSpaceMarines/KrakGrenadeDescription" value="Otorga al Señor del Caos, Marines Espaciales del Caos, Aniquiladores del Caos, Bersérkers de Khorne, Señor de la Posesión y Herrero de la Disformidad la habilidad de lanzar granadas antitanque."/>
 	<entry name="ChaosSpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/LasDamage" value="<string name='Traits/ChaosSpaceMarines/LasDamage'/>"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Aumenta la penetración de blindaje de las armas de plasma."/>
 	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/LasDamageFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneDescription'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleDescription'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshDescription'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchDescription'/>"/>
 	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/Mechanoid" value="<string name='Traits/ChaosSpaceMarines/Mechanoid'/>"/>
 	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Otorga a los Campeones del Caos la oportunidad de ganar una armadura incrementada de forma permanente al matar a un enemigo (Bendición del Caos)."/>
 	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="<string name='Traits/ChaosSpaceMarines/MechanoidFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MeleeDamage" value="<string name='Traits/ChaosSpaceMarines/MeleeDamage'/>"/>
 	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Aumenta la penetración de blindaje de las armas de melé."/>
 	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/MeleeDamageFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
 	<entry name="ChaosSpaceMarines/MeltaBombDescription" value="Otorga al Señor del Caos, Marines Espaciales del Caos, Aniquiladores del Caos y Bersérkers de Khorne la habilidad de desplegar una bomba de fusión que es altamente efectiva contra vehículos pesados y fortificacionesfications."/>
 	<entry name="ChaosSpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/OurLivesForTheGods" value="Nuestras vidas para los dioses"/>
 	<entry name="ChaosSpaceMarines/OurLivesForTheGodsDescription" value="Aumenta la tasa de crecimiento del Sacrificio Cultista."/>
 	<entry name="ChaosSpaceMarines/OurLivesForTheGodsFlavor" value="Tantos yacen muertos en las espadas de los adoradores en Gladius que cada muerte no tiene sentido, está vacía, es superficial. Pero también aumenta el efecto de su sacrificio, ya que arrastran a este mundo hacia la dimensión del infierno caído de los Dioses Oscuros, y se vuelve más fácil para los bombardeos oscuros romper la barrera con el Immaterium."/>
 	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodDescription'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeDescription'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceDescription'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureDescription'/>"/>
 	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
 	<entry name="ChaosSpaceMarines/SmokeLauncherDescription" value="Otorga a los rhinos, profanadores y land raiders del Caos la habilidad de lanzar una cortina de humo que aumenta la reducción de daño por ataques a distancia."/>
 	<entry name="ChaosSpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/TemporalDistortion" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortion'/>"/>
 	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Otorga a los Campeones del Caos la oportunidad de ganar un movimiento permanentemente al matar a un enemigo (Bendición del Caos)."/>
 	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortionFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWar'/>"/>
 	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Reduce la pérdida de moral y aumenta la precisión cuerpo a cuerpo de la infantería contra las unidades de los marines espaciales."/>
 	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWarFlavor'/>"/>
 	<entry name="ChaosSpaceMarines/WarpflameGargoyles" value="Gárgolas de llamas disformes"/>
 	<entry name="ChaosSpaceMarines/WarpflameGargoylesDescription" value="Otorga daño a lo largo del tiempo a las armas de los rhinos, profanadores y land raiders del caos."/>
 	<entry name="ChaosSpaceMarines/WarpflameGargoylesFlavor" value="Las bocas de las armas de este vehículo parpadean con un fuego extraño."/>
 	<entry name="ChaosSpaceMarines/WarpFrenzy" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzy'/>"/>
 	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Otorga a los Campeones del Caos la oportunidad encadenar ataques cada vez más fuertes al matar a un enemigo (Bendición del Caos)."/>
 	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzyFlavor'/>"/>
	<entry name="CompendiumFlavor" value="Cada facción expande sus ciudades de forma diferente. A veces, es un Kaudillo Orko uniéndose a sus Chikoz para, literalmente, levantar y mover sus muros más allá, o quizá un Líder Necrón ordenando a sus esclavos excavar más en sus antiguas tumbas de la superficie del planeta. Sin embargo, lo hacen, es necesario para hacer hueco a más construcciones que mejoren las habilidades de la facción."/>
	<entry name="Drukhari/AssaultWeaponBonus" value="<string name='Traits/Drukhari/AssaultWeaponBonus'/>"/>
	<entry name="Drukhari/AssaultWeaponBonusDescription" value="<string name='Upgrades/Eldar/AssaultWeaponBonusDescription'/>"/>
	<entry name="Drukhari/AssaultWeaponBonusFlavor" value="<string name='Traits/Drukhari/AssaultWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgrade" value="Asesinatos Dirigidos"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="Aumenta la lealtad de las ciudades Drukhari."/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="La muerte es algo natural en las calles de una ciudad Drukhari, donde los cadáveres yacen desplomados en cada esquina, esperando que los Ur-Ghuls se los lleven…pero las muertes de los Dracontes y los nobles Cabalitas son más raras. O lo eran hasta que los Íncubos del Arconte descubrieron otro complot. Ahora, incluso el más mínimo indicio de deslealtad resultará en otro cuerpo noble en una púa sobre los dormitorios del Arconte…"/>
	<entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
	<entry name="Drukhari/BonusResourcesDescription" value="<string name='Actions/Drukhari/BonusResourcesDescription'/>"/>
	<entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
	<entry name="Drukhari/CityTier2" value="<string name='Traits/Drukhari/CityTier2'/>"/>
	<entry name="Drukhari/CityTier2Description" value="<string name='Traits/Drukhari/CityTier2Description'/>"/>
	<entry name="Drukhari/CityTier2Flavor" value="<string name='Traits/Drukhari/CityTier2Flavor'/>"/>
	<entry name="Drukhari/CityTier3" value="<string name='Traits/Drukhari/CityTier3'/>"/>
	<entry name="Drukhari/CityTier3Description" value="<string name='Traits/Drukhari/CityTier3Description'/>"/>
	<entry name="Drukhari/CityTier3Flavor" value="<string name='Traits/Drukhari/CityTier3Flavor'/>"/>
	<entry name="Drukhari/CombatDrugsUpgrade" value="Comerciante del Culto del Frío"/>
	<entry name="Drukhari/CombatDrugsUpgradeDescription" value="Otorga a todas las unidades de infantería Drukhari la habilidad de emplear drogas de combate."/>
	<entry name="Drukhari/CombatDrugsUpgradeFlavor" value="Aunque las drogas de combate son de uso generalizado en toda la sociedad Drukhari, son los Cultos de Brujas los más felices usándolas, a pesar de su efecto nocivo en su fisiología y esperanza de vida. Tener un enlace con un Culto de Brujas con acceso a su enorme almacén de compuestos horripilantes es una gran ayuda para una Kábala que realiza incursiones en el espacio real."/>
	<entry name="Drukhari/CorsairOutpostsUpgrade" value="Subidón del Alma"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="Aumenta el crecimiento por defecto de las ciudades Drukhari."/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="Los rumores se están extendiendo como una plaga en Comorragh: sobre la riqueza de Gladius Prime; sobre sus billones de humanos sufriendo atrapados bajo la Tormenta Disforme; y sobre el extraño núcleo de hueso espectral del planeta, lleno de almas Aeldari perdidas…cierto o no, los Drukhari vienen. Pero ¿quién podría haber iniciado esos rumores…?"/>
	<entry name="Drukhari/EnergyBuildingBonus" value="<string name='Traits/Drukhari/EnergyBuildingBonus'/>"/>
	<entry name="Drukhari/EnergyBuildingBonusDescription" value="Aumenta la producción de energía de los edificios productores de energía."/>
	<entry name="Drukhari/EnergyBuildingBonusFlavor" value="<string name='Traits/Drukhari/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
	<entry name="Drukhari/EnhancedAethersailsDescription" value="Otorga a Incursores, Devastadores y Tantalus la habilidad de aumentar su movimiento."/>
	<entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
	<entry name="Drukhari/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Drukhari/ExtraInfantryArmourDescription" value="Incremente la armadura de la infantería."/>
	<entry name="Drukhari/ExtraInfantryArmourFlavor" value="Equipar a todo un ejército con Armadura Fantasmal sería ruinoso. Pero para unos pocos favorecidos, el Arconte se ha estirado. Fabricada con extraños materiales resinosos y llena de cavidades con un gas más ligero que el aire, la Armadura Fantasmal proporciona una protección considerable sin pesar casi nada."/>
	<entry name="Drukhari/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Drukhari/ExtraVehicleArmourDescription" value="<string name='Traits/ExtraVehicleArmourDescription'/>"/>
	<entry name="Drukhari/ExtraVehicleArmourFlavor" value="Ya estén equipados con Campos Parpadeantes o Escudos Nocturnos, los vehículos de los Drukhari son difíciles de detectar y más difíciles de apuntar. Integrar Armadura Fantasmal en las máquinas más preciadas es costoso, pero reduce su peso y aumenta su velocidad, reduciendo aún más la probabilidad de que puedan ser alcanzados."/>
	<entry name="Drukhari/FeastOfTorment" value="<string name='Traits/Drukhari/FeastOfTorment'/>"/>
	<entry name="Drukhari/FeastOfTormentDescription" value="<string name='Actions/Drukhari/FeastOfTormentDescription'/>"/>
	<entry name="Drukhari/FeastOfTormentFlavor" value="<string name='Traits/Drukhari/FeastOfTormentFlavor'/>"/>
	<entry name="Drukhari/FieldRepairs" value="<string name='Actions/Drukhari/FieldRepairs'/>"/>
	<entry name="Drukhari/FieldRepairsDescription" value="Otorga a todos los vehículos Drukhari la habilidad de recuperar puntos de vida."/>
	<entry name="Drukhari/FieldRepairsFlavor" value="<string name='Actions/Drukhari/FieldRepairsFlavor'/>"/>
	<entry name="Drukhari/GraveLotus" value="Loto de la Tumba"/>
	<entry name="Drukhari/GraveLotusDescription" value="Las drogas de combate otorgan daño cuerpo a cuerpo adicional."/>
	<entry name="Drukhari/GraveLotusFlavor" value="En el Huerto del Diablo, los ruidosos jardines colgantes de Loto de la Tumba brotan de un mosaico de muertos. Un hongo de color púrpura intenso, el loto roba la fuerza que se desvanece de los recién muertos para favorecer su propio crecimiento. Los Cultos de Brujas lo roban a su vez, bebiendo el loto en forma líquida para aumentar sus propios poderes físicos."/>
	<entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
	<entry name="Drukhari/GrislyTrophiesDescription" value="Otorga a Ponzoñas, Incursores, Devastadores y Tantalus un aura que reduce la pérdida de moral de las unidades Drukhari aliadas adyacentes."/>
	<entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
	<entry name="Drukhari/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Drukhari/HammerOfWrathDescription" value="Otorga a Cronos, Infernales, Guadañas y Azotes la habilidad de realizar ataques más devastadores."/>
	<entry name="Drukhari/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Drukhari/HaywireGrenade" value="<string name='Weapons/HaywireGrenade'/>"/>
	<entry name="Drukhari/HaywireGrenadeDescription" value="Otorga a Guerreros Cabalitas, Súcubos y Brujas la habilidad de lanzar granadas anti-vehículo."/>
	<entry name="Drukhari/HaywireGrenadeFlavor" value="<string name='Weapons/HaywireGrenadeFlavor'/>"/>
	<entry name="Drukhari/HeavyWeaponBonus" value="<string name='Traits/Drukhari/HeavyWeaponBonus'/>"/>
	<entry name="Drukhari/HeavyWeaponBonusDescription" value="Aumenta la penetración de armadura de las armas pesadas."/>
	<entry name="Drukhari/HeavyWeaponBonusFlavor" value="<string name='Traits/Drukhari/HeavyWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/Hypex" value="Velocet"/>
	<entry name="Drukhari/HypexDescription" value="Drogas de combate que permiten moverse a través de cobertura."/>
	<entry name="Drukhari/HypexFlavor" value="Capturar un Psiconeuein es una misión peligrosa, pero quien lo haga puede venderlo a los Cultos de Brujas por un alto precio. La droga Velocet, cuando se destila de los fluidos cerebrales de la criatura insectoide, aumenta la ya afilada velocidad de reacción de los Drukhari a niveles realmente sorprendentes."/>
	<entry name="Drukhari/MeleeWeaponBonus" value="<string name='Traits/Drukhari/MeleeWeaponBonus'/>"/>
	<entry name="Drukhari/MeleeWeaponBonusDescription" value="Aumenta la penetración de armadura de las armas cuerpo a cuerpo."/>
	<entry name="Drukhari/MeleeWeaponBonusFlavor" value="<string name='Traits/Drukhari/MeleeWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/NightShields" value="<string name='Traits/Drukhari/NightShields'/>"/>
	<entry name="Drukhari/NightShieldsDescription" value="Aumenta la reducción de daño a distancia de Incursores, Devastadores, Cazas Estilete, Tantalus y Bombarderos Cuervo."/>
	<entry name="Drukhari/NightShieldsFlavor" value="<string name='Traits/Drukhari/NightShieldsFlavor'/>"/>
	<entry name="Drukhari/Painbringer" value="Vigorizadora"/>
	<entry name="Drukhari/PainbringerDescription" value="Drogas de combate que otorgan reducción de daño No Hay Dolow."/>
	<entry name="Drukhari/PainbringerFlavor" value="Sólo el duque exiliado Sliscus puede reclamar un suministro constante de Vigorizadora. Uno de los elixires aumentativos más raros, endurece la piel del bebedor hasta convertirla en una funda flexible tan resistente como el cuero curado. El proceso es extremadamente agonizante, aunque sus defensores consideran que el dolor es un precio trivial a pagar."/>
	<entry name="Drukhari/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
	<entry name="Drukhari/PlasmaGrenadeDescription" value="Otorga a Arcontes, Azotes, Brujas y Súcubos la habilidad de lanzar granadas versátiles."/>
	<entry name="Drukhari/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
	<entry name="Drukhari/RaiderFortress" value="<string name='Actions/Drukhari/RaiderFortress'/>"/>
	<entry name="Drukhari/RaiderFortressDescription" value="Otorga la habilidad de fundar nuevas ciudades en Portales a la Telaraña capturados."/>
	<entry name="Drukhari/RaiderFortressFlavor" value="<string name='Actions/Drukhari/RaiderFortressFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsDamage" value="<string name='Traits/Drukhari/RaidersTacticsDamage'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageDescription" value="Aumenta el daño provocado por las unidades de infantería Drukhari cuando desembarcan de un transporte."/>
	<entry name="Drukhari/RaidersTacticsDamageFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageReduction" value="<string name='Traits/Drukhari/RaidersTacticsDamageReduction'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Aumenta la reducción de daño invulnerable de las unidades de infantería Drukhari cuando desembarcan de un transporte."/>
	<entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageReductionFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsHealingRate" value="<string name='Traits/Drukhari/RaidersTacticsHealingRate'/>"/>
	<entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Aumenta el ritmo de curación de las unidades de infantería embarcadas en un transporte."/>
	<entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="<string name='Traits/Drukhari/RaidersTacticsHealingRateFlavor'/>"/>
	<entry name="Drukhari/SacrificeToKhaine" value="<string name='Traits/Drukhari/SacrificeToKhaine'/>"/>
	<entry name="Drukhari/SacrificeToKhaineDescription" value="<string name='Actions/Drukhari/SacrificeToKhaineDescription'/>"/>
	<entry name="Drukhari/SacrificeToKhaineFlavor" value="<string name='Traits/Drukhari/SacrificeToKhaineFlavor'/>"/>
	<entry name="Drukhari/ShroudGate" value="<string name='Traits/Drukhari/ShroudGate'/>"/>
	<entry name="Drukhari/ShroudGateDescription" value="Aumenta la reducción de daño a distancia de las unidades que pasen a través de una puerta o portal a la Telaraña."/>
	<entry name="Drukhari/ShroudGateFlavor" value="<string name='Traits/Drukhari/ShroudGateFlavor'/>"/>
	<entry name="Drukhari/SoulHungerCost" value="Lugar del Alma"/>
	<entry name="Drukhari/SoulHungerCostDescription" value="Reduce el coste de las habilidades Hambre de Almas."/>
	<entry name="Drukhari/SoulHungerCostFlavor" value="A medida que la incursión de los Drukhari en el Espacio Real se enraiza más, las conexiones entre este mundo y Commorragh se vuelven cada vez más numerosas. Incluso cuando está sellado físicamente, todavía es plausible que la Kábala extraiga poder de estas conexiones y alimente vida robada a través de ellas."/>
	<entry name="Drukhari/SoulHungerLoyalty" value="<string name='Traits/Drukhari/SoulHungerLoyalty'/>"/>
	<entry name="Drukhari/SoulHungerLoyaltyDescription" value="<string name='Actions/Drukhari/SoulHungerLoyaltyDescription'/>"/>
	<entry name="Drukhari/SoulHungerLoyaltyFlavor" value="<string name='Traits/Drukhari/SoulHungerLoyaltyFlavor'/>"/>
	<entry name="Drukhari/SoulHungerOutposts" value="<string name='Traits/Drukhari/SoulHungerOutposts'/>"/>
	<entry name="Drukhari/SoulHungerOutpostsDescription" value="<string name='Actions/Drukhari/SoulHungerOutpostsDescription'/>"/>
	<entry name="Drukhari/SoulHungerOutpostsFlavor" value="<string name='Traits/Drukhari/SoulHungerOutpostsFlavor'/>"/>
	<entry name="Drukhari/SoulHungerUpgrade" value="Sádicos del Alma"/>
	<entry name="Drukhari/SoulHungerUpgradeDescription" value="Aumenta la influencia obtenida por las unidades Drukhari cuando eliminan a un enemigo."/>
	<entry name="Drukhari/SoulHungerUpgradeFlavor" value="“Debes drogarlos. Mata a los guardias. Abre la puerta. Despiértalos con cuidado. Llévalos hacia la libertad. Dales la esperanza de escapar. Comienza la persecución en serio. Falsea tu muerte. Muéstrales una salida. Desvela entonces la farsa. Y devuélvelos a la cámara de tortura, sonriendo maliciosamente. Sin tocarlos, los has imbuido de un rico dolor espiritual que nunca desaparecerá.” – Gyrthineus Roche, Arconte de La Última Hoja"/>
	<entry name="Drukhari/SoulFeast" value="<string name='Traits/Drukhari/SoulFeast'/>"/>
	<entry name="Drukhari/SoulFeastDescription" value="<string name='Traits/Drukhari/SoulFeastDescription'/>"/>
	<entry name="Drukhari/SoulFeastFlavor" value="<string name='Traits/Drukhari/SoulFeastFlavor'/>"/>
	<entry name="Drukhari/SoulShelling" value="<string name='Actions/Drukhari/SoulShelling'/>"/>
	<entry name="Drukhari/SoulShellingDescription" value="<string name='Actions/Drukhari/SoulShellingDescription'/>"/>
	<entry name="Drukhari/SoulShellingFlavor" value="<string name='Actions/Drukhari/SoulShellingFlavor'/>"/>
	<entry name="Drukhari/Splintermind" value="Partemente"/>
	<entry name="Drukhari/SplintermindDescription" value="Drogas de combate que otorgan reducción de pérdida de moral."/>
	<entry name="Drukhari/SplintermindFlavor" value="La Partemente está hecha de los restos de cristal molidos de un Vidente Eldar muerto. Aunque no garantiza la presciencia, esta sustancia parecida al polvo permite a quienes la toman pensar en varias direcciones al mismo tiempo, una habilidad de un valor incalculable, ya que la confusión de la batalla pasa factura incluso al plan de batalla más riguroso."/>
	<entry name="Drukhari/TormentGrenadeLaunchers" value="<string name='Weapons/TormentGrenadeLaunchers'/>"/>
	<entry name="Drukhari/TormentGrenadeLaunchersDescription" value="Otorga a Incursores, Devastadores, y Tantalus Lanzagranadas Tormento."/>
	<entry name="Drukhari/TormentGrenadeLaunchersFlavor" value="<string name='Weapons/TormentGrenadeLaunchersFlavor'/>"/>
	<entry name="Drukhari/WealthPlunder" value="<string name='Actions/Drukhari/WealthPlunder'/>"/>
	<entry name="Drukhari/WealthPlunderDescription" value="<string name='Actions/Drukhari/WealthPlunderDescription'/>"/>
	<entry name="Drukhari/WealthPlunderFlavor" value="<string name='Actions/Drukhari/WealthPlunderFlavor'/>"/>
	<entry name="Drukhari/WeaponRacks" value="<string name='Traits/Drukhari/WeaponRacks'/>"/>
	<entry name="Drukhari/WeaponRacksDescription" value="Otorga armas acopladas a las armas a distancia de las unidades que desembarquen de un Incursor o Tantalus."/>
	<entry name="Drukhari/WeaponRacksFlavor" value="<string name='Traits/Drukhari/WeaponRacksFlavor'/>"/>
	<entry name="Drukhari/WebwayTravelAction" value="Irrupción en el Espacio Real"/>
	<entry name="Drukhari/WebwayTravelActionDescription" value="Elimina el coste de acción por Viaje en la Telaraña."/>
	<entry name="Drukhari/WebwayTravelActionFlavor" value="Aunque pasar a través de una puerta de la Telaraña es simple en sí mismo, coordinar los movimientos de un gran ejército o formación puede hacerlos vulnerables. Para los Drukhari, esto se resuelve mediante la creación de puertas temporales alrededor de cualquier portal permanente, lo que permite que todo el grupo se mueva rápidamente a la vez."/>
	<entry name="Eldar/AircraftBuildingBonus" value="<string name='Traits/Eldar/AircraftBuildingBonus'/>"/>
	<entry name="Eldar/AircraftBuildingBonusDescription" value="Aumenta la generación de producción del portal espiral."/>
	<entry name="Eldar/AircraftBuildingBonusFlavor" value="<string name='Traits/Eldar/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Eldar/AssaultWeaponBonus" value="<string name='Traits/Eldar/AssaultWeaponBonus'/>"/>
	<entry name="Eldar/AssaultWeaponBonusDescription" value="Aumenta la penentración de blindaje de las armas de asalto."/>
	<entry name="Eldar/AssaultWeaponBonusFlavor" value="<string name='Traits/Eldar/AssaultWeaponBonusFlavor'/>"/>
	<entry name="Eldar/AsuryaniArrivalsBonus" value="Invocación de Vidente"/>
	<entry name="Eldar/AsuryaniArrivalsBonusDescription" value="Reduce el costes de las llegadas Asuryani."/>
	<entry name="Eldar/AsuryaniArrivalsBonusFlavor" value="“Mi palabra es más que un mero consejo: es una imprecación filtrada a través de la conciencia de nuestra muerte narrada, esforzándose por traer una pizca de orden al universo desordenado. Nuestra raza lo entiende y escucha mi palabra, no como la de un dictador sino como la de un estudiante atento.”<br/>  — Vidente Kataimon of Malantai"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2" value="Promesa del mundo astronave"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Description" value="Reduce el enfriamiento de las llegadas Asuryani."/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Flavor" value="“Cuando los Brujos y los Videntes de múltiples mundos astronave comparten una visión—una visión literal del futuro—entonces ellos y sus mundos astronave trabajan como uno solo. Despojarán sus propios salones de vida y espíritu para cumplir su promesa a ese futuro.”<br/>  — Transcipciones, Grigomen Delr, comerciante contrabandista y xenólogo amateur"/>
	<entry name="Eldar/CityTier2" value="<string name='Traits/Eldar/CityTier2'/>"/>
	<entry name="Eldar/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Eldar/CityTier2Flavor" value="<string name='Traits/Eldar/CityTier2Flavor'/>"/>
	<entry name="Eldar/CityTier3" value="<string name='Traits/Eldar/CityTier3'/>"/>
	<entry name="Eldar/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Eldar/CityTier3Flavor" value="<string name='Traits/Eldar/CityTier3Flavor'/>"/>
	<entry name="Eldar/CleansingFlame" value="<string name='Actions/CleansingFlame'/>"/>
	<entry name="Eldar/CleansingFlameDescription" value="Otorga a los Brujos la habilidad de lanzar llamas psíquicas al rojo vivo sobre los enemigos adyacentes."/>
	<entry name="Eldar/CleansingFlameFlavor" value="<string name='Actions/CleansingFlameFlavor'/>"/>
	<entry name="Eldar/ConstructionBuildingBonus" value="<string name='Traits/Eldar/ConstructionBuildingBonus'/>"/>
	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Aumenta la generación de producción de las capillas de aedas óseos."/>
	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="<string name='Traits/Eldar/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Eldar/CrackShot" value="<string name='Traits/Eldar/CrackShot'/>"/>
	<entry name="Eldar/CrackShotDescription" value="Aumenta la puntería y penetración de blindaje de los dragones llameantes."/>
	<entry name="Eldar/CrackShotFlavor" value="<string name='Traits/Eldar/CrackShotFlavor'/>"/>
	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Traits/Eldar/CrystalTargetingMatrix'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixDescription" value="Otorga a los Prisma, Hornets, Serpientes Eldar y Bípodes de Combate la habilidad de aumentar temporalmente su puntería."/>
	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Traits/Eldar/CrystalTargetingMatrixFlavor'/>"/>
	<entry name="Eldar/Dominate" value="<string name='Actions/Dominate'/>"/>
	<entry name="Eldar/DominateDescription" value="Proporciona a los cazas espectrales de Hemlock la habilidad de aturdir a unidades enemigas que no sean vehículos ni fortalezas."/>
	<entry name="Eldar/DominateFlavor" value="<string name='Actions/DominateFlavor'/>"/>
	<entry name="Eldar/ExpertHunter" value="<string name='Traits/Eldar/ExpertHunter'/>"/>
	<entry name="Eldar/ExpertHunterDescription" value="Aumenta el daño de las lanza bruja contra criaturas monstruosas, vehículos y fortificaciones."/>
	<entry name="Eldar/ExpertHunterFlavor" value="<string name='Traits/Eldar/ExpertHunterFlavor'/>"/>
	<entry name="Eldar/ExtraInfantryArmour" value="Armadura de malla laberíntica"/>
	<entry name="Eldar/ExtraInfantryArmourDescription" value="Increases the armour of infantry."/>
	<entry name="Eldar/ExtraInfantryArmourFlavor" value="La armadura de malla de termoplástico del Aeldari no está diseñada con un enfoque únicamente de defensa, sino que también permite al usuario moverse tan libremente como si no la llevara. Sin embargo, al invadir aún más sus complejos protocolos de disipación, es posible aumentar su resistencia sin perder la movilidad."/>
	<entry name="Eldar/ExtraVehicleArmour" value="Infusión de hueso espectral"/>
	<entry name="Eldar/ExtraVehicleArmourDescription" value="Aumenta el blidaje de los vehículos."/>
	<entry name="Eldar/ExtraVehicleArmourFlavor" value="Todos los vehículos y estructuras de Aeldari son literalmente ‘cantados’ en la existencia, por los aedas óseos cuyo comando de rango vocal y psíquico es la contrapartida constructiva del grito de guerra del espectro aullante. Sin embargo, con los recursos de Gladius Prime y la alteración de sus armonías internas, los aedas óseos pueden tejer un mayor poder en sus creaciones, aumentando aún más su resistencia."/>
	<entry name="Eldar/FoodBuildingBonus" value="<string name='Traits/Eldar/FoodBuildingBonus'/>"/>
	<entry name="Eldar/FoodBuildingBonusDescription" value="Aumenta la generación de comida de los jardines de Isha."/>
	<entry name="Eldar/FoodBuildingBonusFlavor" value="<string name='Traits/Eldar/FoodBuildingBonusFlavor'/>"/>
	<entry name="Eldar/GhostwalkMatrix" value="Matriz de senda espiritual"/>
	<entry name="Eldar/GhostwalkMatrixDescription" value="Permite a los Prisma, Hornets, Serpientes Eldar y Bípodes de Combate mover a través de cobertura."/>
	<entry name="Eldar/GhostwalkMatrixFlavor" value="Una matriz de senda espiritual uriliza el conocimeinto y la sabiduría contenida en una joya espiritual para guiar al vehículo en su camino."/>
	<entry name="Eldar/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Eldar/HammerOfWrathDescription" value="Otorga a los lanzas brillantes, videntes, avatar de Khaine, bípodes de combate y caballeros espectrales la habilidad de llevar a cabo ataques más devastadores."/>
	<entry name="Eldar/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Eldar/HeavyWeaponBonus" value="<string name='Traits/Eldar/HeavyWeaponBonus'/>"/>
	<entry name="Eldar/HeavyWeaponBonusDescription" value="Aumenta la penetración de armadura de las armas pesadas."/>
	<entry name="Eldar/HeavyWeaponBonusFlavor" value="<string name='Traits/Eldar/HeavyWeaponBonusFlavor'/>"/>
	<entry name="Eldar/HoloFields" value="<string name='Traits/Eldar/HoloFields'/>"/>
	<entry name="Eldar/HoloFieldsDescription" value="Otorga a los Prisma, Hornets y Serpientes Eldar reducción de daño invulnerable después de haber movido."/>
	<entry name="Eldar/HoloFieldsFlavor" value="<string name='Traits/Eldar/HoloFieldsFlavor'/>"/>
	<entry name="Eldar/InfantryBuildingBonus" value="<string name='Traits/Eldar/InfantryBuildingBonus'/>"/>
	<entry name="Eldar/InfantryBuildingBonusDescription" value="Aumenta la generación de producción de los crisoles de Asuryan."/>
	<entry name="Eldar/InfantryBuildingBonusFlavor" value="<string name='Traits/Eldar/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Eldar/InfluenceBuildingBonus" value="<string name='Traits/Eldar/InfluenceBuildingBonus'/>"/>
	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Aumenta la generación de influencia de las cúpulas de los videntes."/>
	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="<string name='Traits/Eldar/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Eldar/MarksmansEye" value="<string name='Traits/Eldar/MarksmansEye'/>"/>
	<entry name="Eldar/MarksmansEyeDescription" value="Aumenta la puntería de los cazadores carmesí."/>
	<entry name="Eldar/MarksmansEyeFlavor" value="<string name='Traits/Eldar/MarksmansEyeFlavor'/>"/>
	<entry name="Eldar/MeleeWeaponBonus" value="<string name='Traits/Eldar/MeleeWeaponBonus'/>"/>
	<entry name="Eldar/MeleeWeaponBonusDescription" value="Aumenta la penetración de blindaje de las armas cuerpo a cuerpo."/>
	<entry name="Eldar/MeleeWeaponBonusFlavor" value="<string name='Traits/Eldar/MeleeWeaponBonusFlavor'/>"/>
	<entry name="Eldar/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="Eldar/MeltaBombDescription" value="Proporciona a los dragones llameantes la habilidad de desplegar una bomba de fusión que es muy efectiva contra vehículos pesados y fortificaciones."/>
	<entry name="Eldar/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="Eldar/OreBuildingBonus" value="<string name='Traits/Eldar/OreBuildingBonus'/>"/>
	<entry name="Eldar/OreBuildingBonusDescription" value="Aumenta la generación de mineral del Altmarls de Vaul."/>
	<entry name="Eldar/OreBuildingBonusFlavor" value="<string name='Traits/Eldar/OreBuildingBonusFlavor'/>"/>
	<entry name="Eldar/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
	<entry name="Eldar/PlasmaGrenadeDescription" value="Proporciona a los guardianes y autarcas la habilidad de lanzar versátiles granadas."/>
	<entry name="Eldar/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
	<entry name="Eldar/ResearchBuildingBonus" value="<string name='Traits/Eldar/ResearchBuildingBonus'/>"/>
	<entry name="Eldar/ResearchBuildingBonusDescription" value="Aumenta la generación de investigación de los osarios de espíritus."/>
	<entry name="Eldar/ResearchBuildingBonusFlavor" value="<string name='Traits/Eldar/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Eldar/SpiritPreservationBonus" value="Montaje de Escutcheon"/>
	<entry name="Eldar/SpiritPreservationBonusDescription" value="Aumenta la ganancia de energía cuando las unidades Aeldari mueren."/>
	<entry name="Eldar/SpiritPreservationBonusFlavor" value="Desde tiempos inmemoriales, hemos montado nuestras joyas espirituales en simples carcasas, confiando en la habilidad de sus dueños para preservarlas. Sin embargo, las antiguas estructuras de la Telaraña que hemos encontrado en Gladius Prime demuestran que se puede construir un complejo escudo en cualquier carcasa de hueso espectral, permitiéndonos arrebatar un mayor número de nuestros guerreros muertos de las fauces de La Que Espera."/>
	<entry name="Eldar/SpiritStones" value="<string name='Traits/Eldar/SpiritStones'/>"/>
> 	<entry name="Eldar/SpiritStonesDescription" value="Reduce la pérdida de moral de los Prisma, Hornets, Serpientes Eldar y Bípodes de Combate."/>
	<entry name="Eldar/SpiritStonesFlavor" value="<string name='Traits/Eldar/SpiritStonesFlavor'/>"/>
	<entry name="Eldar/StarEngines" value="<string name='Traits/Eldar/StarEngines'/>"/>
<entry name="Eldar/StarEnginesDescription" value="Aumenta el movimiento de los Prisma, Hornets, Escorpiones, Serpientes Eldar y Bípodes de Combate."/>
	<entry name="Eldar/StarEnginesFlavor" value="<string name='Traits/Eldar/StarEnginesFlavor'/>"/>
	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
	<entry name="Eldar/TranscendentBlissDescription" value="Proporciona a las ciudades la habilidad de gastar influencia para aumentar temporalmente la lealtad."/>
	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
	<entry name="Eldar/TranscendentBlissBonus" value="El sino de los Aeldari"/>
	<entry name="Eldar/TranscendentBlissBonusDescription" value="Aumenta la lealtad ganada por la dicha transcendental."/>
	<entry name="Eldar/TranscendentBlissBonusFlavor" value="“Para una mente Aeldari sin trabas no hay ni cordura ni locura, sino simplemente una ola de existencia perfecta realizada por su propio impulso salvaje.”<br/>  — Ralamine Mung, Ordo Xenos"/>
	<entry name="Eldar/VectoredEngines" value="<string name='Traits/Eldar/VectoredEngines'/>"/>
	<entry name="Eldar/VectoredEnginesDescription" value="Otorga a los Prisma, Hornets, Escorpiones, Serpientes Eldar y Bípodes de Combate la habilidad de incrementar temporalmente su blindaje frente a ataques a distancia."/>
	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Traits/Eldar/VectoredEnginesFlavor'/>"/>
	<entry name="Eldar/VehicleBuildingBonus" value="<string name='Traits/Eldar/VehicleBuildingBonus'/>"/>
	<entry name="Eldar/VehicleBuildingBonusDescription" value="Aumenta la generación de producción del Gran Acceso."/>
	<entry name="Eldar/VehicleBuildingBonusFlavor" value="<string name='Traits/Eldar/VehicleBuildingBonusFlavor'/>"/>
	<entry name="Eldar/WarShout" value="<string name='Actions/Eldar/WarShout'/>"/>
	<entry name="Eldar/WarShoutDescription" value="Proporciona a los espectros aullantes la habilidad de desmoralizar a los enemigos adyacentes."/>
	<entry name="Eldar/WarShoutFlavor" value="<string name='Actions/Eldar/WarShoutFlavor'/>"/>
	<entry name="Eldar/WebwayGateBonus" value="Cartografía de la Telaraña"/>
	<entry name="Eldar/WebwayGateBonusDescription" value="Elimina el costes de activación de las puertas de la Telaraña."/>
	<entry name="Eldar/WebwayGateBonusFlavor" value="Sólo los arlequines y los custodios de la Biblioteca Negra saben realmente algo de la disposición transdimensional de la Telaraña. Pero al regalarnos una parte de ese conocimiento, que sólo pertenece a este mundo, los bibliotecarios se han asegurado de que podamos reabrir estas antiguas puertas con facilidad."/>
	<entry name="Eldar/WebwayGateBonus2" value="Constricción de la Telaraña"/>
	<entry name="Eldar/WebwayGateBonus2Description" value="Elimina el coste de activación para viajar en la Telaraña."/>
	<entry name="Eldar/WebwayGateBonus2Flavor" value="Habiendo recibido orientación de la Biblioteca Negra sobre el diseño único de Gladius, los guardianes de almas han planeado los caminos más cortos entre dos puertas cualesquiera, haciendo el camino entre ellos tan libre de fricción como cualquier otro paso que podamos dar."/>
	<entry name="Eldar/WebwayRedoubt" value="Reducto de la Telaraña"/>
	<entry name="Eldar/WebwayRedoubtDescription" value="Otorga la habilidad de fundar nuevas ciudades en las Puertas de la Telaraña reclamadas."/>
	<entry name="Eldar/WebwayRedoubtFlavor" value="<string name='Actions/Eldar/WebwayRedoubtFlavor'/>"/>
	<entry name="Eldar/WraithknightStarcannon" value="Cañones estelares de los caballeros espectrales"/>
	<entry name="Eldar/WraithknightStarcannonDescription" value="Otorga dos cañones estelares a los caballeros espectrales."/>
	<entry name="Eldar/WraithknightStarcannonFlavor" value="<string name='Weapons/StarcannonFlavor'/>"/>
	<entry name="Necrons/AircraftBuildingBonus" value="<string name='Traits/Necrons/AircraftBuildingBonus'/>"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Aumenta la producción del Pavimento sin Nombre"/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="<string name='Traits/Necrons/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Necrons/AttackCityBonus" value="<string name='Traits/Necrons/AttackCityBonus'/>"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Aumenta la precisión contra unidades enemigas en ciudades."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="<string name='Traits/Necrons/AttackCityBonusFlavor'/>"/>
	<entry name="Necrons/BlastDamage" value="<string name='Traits/Necrons/BlastDamage'/>"/>
	<entry name="Necrons/BlastDamageDescription" value="Aumenta la penetración de blindaje de las armas de área y de las armas de plantilla."/>
	<entry name="Necrons/BlastDamageFlavor" value="<string name='Traits/Necrons/BlastDamageFlavor'/>"/>
	<entry name="Necrons/CityDefenseBonus" value="Obstáculos inefables"/>
	<entry name="Necrons/CityDefenseBonusDescription" value="Aumenta la reducción del daño de las unidades en ciudades."/>
	<entry name="Necrons/CityDefenseBonusFlavor" value="Los ataques realizados a los defensores de las Ciudades Tumba fracasan misteriosamente—los ataques con energía se desvanecen, los disparos con armas Gravitón se desvían, y los proyectiles parecen arrebatados del cielo por los muros Cyclopean."/>
	<entry name="Necrons/CityTier2" value="<string name='Traits/Necrons/CityTier2'/>"/>
	<entry name="Necrons/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier2Flavor" value="<string name='Traits/Necrons/CityTier2Flavor'/>"/>
	<entry name="Necrons/CityTier3" value="<string name='Traits/Necrons/CityTier3'/>"/>
	<entry name="Necrons/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier3Flavor" value="<string name='Traits/Necrons/CityTier3Flavor'/>"/>
	<entry name="Necrons/ConstructionBuildingBonus" value="<string name='Traits/Necrons/ConstructionBuildingBonus'/>"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Aumenta la producción de los Esclavos Mastabas."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="<string name='Traits/Necrons/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor" value="<string name='Actions/Necrons/DimensionalCorridor'/>"/>
	<entry name="Necrons/DimensionalCorridorDescription" value="Proporciona a la infantería la habilidad de teleportarse a las ciudades y Monolitos."/>
	<entry name="Necrons/DimensionalCorridorFlavor" value="<string name='Actions/Necrons/DimensionalCorridorFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor2" value="Estabilidad Dimensional"/>
	<entry name="Necrons/DimensionalCorridor2Description" value="Reduce el coste del Corredor Dimensional."/>
	<entry name="Necrons/DimensionalCorridor2Flavor" value="Aprovechar el poder de la tecnología abandonada de los Antiguos ha permitido a los Necrones estabilizar su tecnología de recuperación, reduciendo en gran medida el coste del tránsito de los guerreros de la Puerta de la eternidad."/>
	<entry name="Necrons/DimensionalCorridor3" value="Sanción Dimensional"/>
	<entry name="Necrons/DimensionalCorridor3Description" value="Elimina la acción y el coste de movimiento del Corredor Dimensional."/>
	<entry name="Necrons/DimensionalCorridor3Flavor" value="La astuta autorización de Criptecnólogos ha permitido a los Necrones ajustar su tecnología de resurrección, incrementando considerablemente su adquisición cuando transitan."/>
	<entry name="Necrons/EnergyBuildingBonus" value="<string name='Traits/Necrons/EnergyBuildingBonus'/>"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Aumenta la producción de energía de los Núcleos de Energía"/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="<string name='Traits/Necrons/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Necrons/ExtraInfantryArmourDescription" value="Aumenta la armadura de la infantería."/>
	<entry name="Necrons/ExtraInfantryArmourFlavor" value="Incluso después de milenios de guerra y sueño, los Criptecnólogos necrón no han perdido su deseo de innovación. Haciendo pequeñas adaptaciones a su necrodermis, pueden aumentar la capacidad de supervivencia de sus tropas contra las armas de hoy."/>
	<entry name="Necrons/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Necrons/ExtraVehicleArmourDescription" value="Aumenta el blindaje de los vehículos y unidades de Canópticos."/>
	<entry name="Necrons/ExtraVehicleArmourFlavor" value="Para un observador externo, estos vehículos necrón son idénticos a sus predecesores. Para un Criptecnólogo o quizás incluso un Aeldari erudito, pueden ver que el material del vehículo ha aumentado de alguna manera su resistencia, sin afectar su densidad o peso."/>
	<entry name="Necrons/GaussDamage" value="<string name='Traits/GaussDamage'/>"/>
	<entry name="Necrons/GaussDamageDescription" value="Aumenta la penetración de blindaje de las armas gauss."/>
	<entry name="Necrons/GaussDamageFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="Necrons/GaussPylon" value="<string name='Units/Necrons/GaussPylon'/>"/>
	<entry name="Necrons/GaussPylonDescription" value="Otorga a las ciudades la habilidad de levantar poderosos Pilones Gauss."/>
	<entry name="Necrons/GaussPylonFlavor" value="<string name='Units/Necrons/GaussPylonFlavor'/>"/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="Aumenta la reducción de daño de las Arañas Canópticas y unidades aliadas adyacentes frente al fuego brujo."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GrowthBonus" value="<string name='Traits/Necrons/GrowthBonus'/>"/>
	<entry name="Necrons/GrowthBonusDescription" value="Aumenta la tasa de crecimiento de las ciudades Necrones."/>
	<entry name="Necrons/GrowthBonusFlavor" value="<string name='Traits/Necrons/GrowthBonusFlavor'/>"/>
	<entry name="Necrons/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Necrons/HammerOfWrathDescription" value="Otorga a los Líderes Destructores, Cuchillas de la Necrópolis, Espías Canópticos, Trascendente C'tan , Pretorianos de la Triarca y Acechantes de la Triarca la capacidad de realizar ataques más devastadores."/>
	<entry name="Necrons/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Necrons/HousingBuildingBonus" value="<string name='Traits/Necrons/HousingBuildingBonus'/>"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Aumenta el límite de población de los Refugios."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="<string name='Traits/Necrons/HousingBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfantryBuildingBonus" value="<string name='Traits/Necrons/InfantryBuildingBonus'/>"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Aumenta la producción de los Núcleos de Convocación."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="<string name='Traits/Necrons/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfluenceBuildingBonus" value="<string name='Traits/Necrons/InfluenceBuildingBonus'/>"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Aumenta la generación de influencia de Stelae."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="<string name='Traits/Necrons/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Necrons/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="Necrons/LivingMetal2" value="Formas inmortales"/>
	<entry name="Necrons/LivingMetal2Description" value="Aumenta la curación del Metal Viviente."/>
	<entry name="Necrons/LivingMetal2Flavor" value="En un sentido abstruso, las máquinas Necron recuerdan su gloria anterior y se esfuerzan eternamente por recuperarla, haciendo fluir su metal viviente rápidamente para reparar cualquier daño."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="<string name='Traits/Necrons/LoyaltyBuildingBonus'/>"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Aumenta la generación de lealtad del Santuario Barroco."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="<string name='Traits/Necrons/LoyaltyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/MeleeDamage" value="<string name='Traits/Necrons/MeleeDamage'/>"/>
	<entry name="Necrons/MeleeDamageDescription" value="Aumenta la penetración de blindajede las armas cuerpo a cuerpo."/>
	<entry name="Necrons/MeleeDamageFlavor" value="<string name='Traits/Necrons/MeleeDamageFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="<string name='Traits/Necrons/Nebuloscope'/>"/>
	<entry name="Necrons/NebuloscopeDescription" value="Permite a las Cuchillas de la Necrópolis para ignorar la reducción."/>
	<entry name="Necrons/NebuloscopeFlavor" value="<string name='Traits/Necrons/NebuloscopeFlavor'/>"/>
	<entry name="Necrons/NecrodermisRepair2" value="Recrecimiento acelerado"/>
	<entry name="Necrons/NecrodermisRepair2Description" value="Aumenta la restauración de puntos de vida de la Reparación Necrodermis."/>
	<entry name="Necrons/NecrodermisRepair2Flavor" value="Los Criptecnólogos han mejorado la estructura nanomecánica de los cuerpos metálicos de los Necrones, permitiéndoles recuperarse de casi cualquier daño."/>
	<entry name="Necrons/NecrodermisRepair3" value="Dire Necrodermis"/>
	<entry name="Necrons/NecrodermisRepair3Description" value="Elimina el enfriamiento de la Reparación Necrodermis."/>
	<entry name="Necrons/NecrodermisRepair3Flavor" value="El metal viviente que constituye los vehículos y el cuerpo Necrón se retuerce constantemente, reconstruyéndose a si mismo, momento a momento."/>
	<entry name="Necrons/OreBuildingBonus" value="<string name='Traits/Necrons/OreBuildingBonus'/>"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Aumenta la producción de metal de las Canteras de Al-Khemic"/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="<string name='Traits/Necrons/OreBuildingBonusFlavor'/>"/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Otorga a las Plataformas de Aniquilación, Arcas Fantasma, Arcas del Exterminio y Acechantes de la Triarca reducción de daño invulnerable que pasa por un periodo de enfriamiento al inicio del siguiente turno si recibe daño."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/RapidRiseBonus" value="La orden del Líder"/>
	<entry name="Necrons/RapidRiseBonusDescription" value="Reduce el coste del Acenso Rápido"/>
	<entry name="Necrons/RapidRiseBonusFlavor" value="El Líder fortalece sus protocólos patrón-cliente, así sus ordenes se llevan a cabo con mas rapidez y menos pensamiento—y menos voluntad propia."/>
	<entry name="Necrons/ReanimationProtocols2" value="Protocolos de Reanimación Eficiente"/>
	<entry name="Necrons/ReanimationProtocols2Description" value="Aumenta la curación de los Protocolos de Reanimación."/>
	<entry name="Necrons/ReanimationProtocols2Flavor" value="Con estas reparaciones derivadas de sistemas Criptecnólogos, es más probable que los Necrones ignores daño aparentemente letal."/>
	<entry name="Necrons/ResearchBuildingBonus" value="<string name='Traits/Necrons/ResearchBuildingBonus'/>"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Aumenta la generación de investigación de los Archivos Prohibidos."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="<string name='Traits/Necrons/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ScarabHive" value="<string name='Actions/Necrons/ScarabHive'/>"/>
	<entry name="Necrons/ScarabHiveDescription" value="Proporciona a los Espías canópticos la habilidad de construir Escarabajos canópticos."/>
	<entry name="Necrons/ScarabHiveFlavor" value="<string name='Actions/Necrons/ScarabHiveFlavor'/>"/>
	<entry name="Necrons/SeismicAssault" value="Asalto sístimo"/>
	<entry name="Necrons/SeismicAssaultDescription" value="Proporciona a los Fragmentos de C'tan y la Cripta Teseráptica la habilidad de realizar devastadores ataques."/>
	<entry name="Necrons/SeismicAssaultFlavor" value="<string name='Weapons/SeismicAssaultTranscendentFlavor'/>"/>
	<entry name="Necrons/ShieldVane" value="<string name='Traits/Necrons/ShieldVane'/>"/>
	<entry name="Necrons/ShieldVaneDescription" value="Aumenta la armadura de las Cuchillas de la Necrópolis."/>
	<entry name="Necrons/ShieldVaneFlavor" value="<string name='Traits/Necrons/ShieldVaneFlavor'/>"/>
	<entry name="Necrons/TeslaDamage" value="<string name='Traits/TeslaDamage'/>"/>
	<entry name="Necrons/TeslaDamageDescription" value="Aumenta la penetración de blindaje de las armas tesla."/>
	<entry name="Necrons/TeslaDamageFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="Necrons/TheBoundCoalescent" value="<string name='Actions/Necrons/TheBoundCoalescent'/>"/>
	<entry name="Necrons/TheBoundCoalescentDescription" value="Proporciona a los Fragmentos de C'tan la habilidad de unirse con Obeliscos para crear la Cripta Teseráptica."/>
	<entry name="Necrons/TheBoundCoalescentFlavor" value="<string name='Actions/Necrons/TheBoundCoalescentFlavor'/>"/>
	<entry name="Necrons/VehiclesBuildingBonus" value="<string name='Traits/Necrons/VehiclesBuildingBonus'/>"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Aumenta la generación de producción de los Templos Hipóstilas."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="<string name='Traits/Necrons/VehiclesBuildingBonusFlavor'/>"/>
	<entry name="Orks/AmmoRunt" value="<string name='Actions/AmmoRunt'/>"/>
	<entry name="Orks/AmmoRuntDescription" value="Otorga a los Mekánikoz, Tipejoz Vacilonez y Despachurradorez la habilidad de aumentar su precisión a distancia."/>
	<entry name="Orks/AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Orks/BattlewagonBigShootas" value="Karro de Guerra con Akribilladorez Pezadoz"/>
	<entry name="Orks/BattlewagonBigShootasDescription" value="Proporciona a los Karroz de Guerra Akribilladorez Pedazoz"/>
	<entry name="Orks/BattlewagonBigShootasFlavor" value="<string name='Weapons/BigShootaFlavor'/>"/>
	<entry name="Orks/BattlewagonRokkitLaunchas" value="Karroz de Guerra con Lanzakohetez"/>
	<entry name="Orks/BattlewagonRokkitLaunchasDescription" value="Proporciona a los Kaaroz de Guerra Lanzakohetez."/>
	<entry name="Orks/BattlewagonRokkitLaunchasFlavor" value="<string name='Weapons/RokkitLaunchaFlavor'/>"/>
	<entry name="Orks/Bigbomm" value="<string name='Weapons/Bigbomm'/>"/>
	<entry name="Orks/BigbommDescription" value="Proporciona a los Kópteros la habilidad de lanzar bombas anti-infantería."/>
	<entry name="Orks/BigbommFlavor" value="<string name='Weapons/BigbommFlavor'/>"/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/Orks/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="Aumenta el daño de las granadas, misiles y armas de área."/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/Orks/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="<string name='Traits/Orks/BoltDamage'/>"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="<string name='Traits/Orks/BoltDamageFlavor'/>"/>
	<entry name="Orks/BonusBeastsProduction" value="<string name='Traits/Orks/BonusBeastsProduction'/>"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="<string name='Traits/Orks/BonusBeastsProductionDescription'/>"/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="<string name='Traits/Orks/BonusBeastsProductionFlavor'/>"/>
	<entry name="Orks/BonusColonizersProduction" value="<string name='Traits/Orks/BonusColonizersProduction'/>"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="<string name='Traits/Orks/BonusColonizersProductionDescription'/>"/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="<string name='Traits/Orks/BonusColonizersProductionFlavor'/>"/>
	<entry name="Orks/BonusInfantryProduction" value="<string name='Traits/Orks/BonusInfantryProduction'/>"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="<string name='Traits/Orks/BonusInfantryProductionDescription'/>"/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="<string name='Traits/Orks/BonusInfantryProductionFlavor'/>"/>
	<entry name="Orks/BonusVehiclesProduction" value="<string name='Traits/Orks/BonusVehiclesProduction'/>"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="<string name='Traits/Orks/BonusVehiclesProductionDescription'/>"/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="<string name='Traits/Orks/BonusVehiclesProductionFlavor'/>"/>
	<entry name="Orks/Bosspole" value="Palo del jefe"/>
	<entry name="Orks/BosspoleDescription" value="Reduce significativamente la perdida de moral al usar la Ley de la Peña para los Mekánikos, Guerreros Orkos, Meganoblez, Petatankez y Kaudilloz."/>
	<entry name="Orks/BosspoleFlavor" value="Los Noblez Orkos a menudo lucen un palo como trofeo que muestra que no se debe tontear con ellos. Un Noblez con un Palo de jefe a menudo encuentra que es útil para restablecer el orden machando algunas cabezas en el calor de la batalla."/>
	<entry name="Orks/CityEnergy" value="<string name='Traits/Orks/CityEnergy'/>"/>
 	<entry name="Orks/CityGrowthDescription" value="<string name='Traits/Orks/CityGrowthDescription'/>"/>
	<entry name="Orks/CityEnergyDescription" value="Aumenta la generación de energía de las ciudades orkas."/>
	<entry name="Orks/CityEnergyFlavor" value="<string name='Traits/Orks/CityEnergyFlavor'/>"/>
	<entry name="Orks/CityGrowth" value="<string name='Traits/Orks/CityGrowth'/>"/>
	<entry name="Orks/CityGrowthDescription" value="Aumenta la tasa de crecimiento de las ciudades Orkas."/>
	<entry name="Orks/CityGrowthFlavor" value="<string name='Traits/Orks/CityGrowthFlavor'/>"/>
	<entry name="Orks/CityInfluence" value="<string name='Traits/Orks/CityInfluence'/>"/>
	<entry name="Orks/CityInfluenceDescription" value="Aumenta la generación de influencia en ciudades Orkas."/>
	<entry name="Orks/CityInfluenceFlavor" value="<string name='Traits/Orks/CityInfluenceFlavor'/>"/>
	<entry name="Orks/CityLoyalty" value="<string name='Traits/Orks/CityLoyalty'/>"/>
	<entry name="Orks/CityLoyaltyDescription" value="Aumenta la lealtad de las ciudades Orkas."/>
	<entry name="Orks/CityLoyaltyFlavor" value="<string name='Traits/Orks/CityLoyaltyFlavor'/>"/>
	<entry name="Orks/CityPopulationLimit" value="<string name='Traits/Orks/CityPopulationLimit'/>"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Aumenta el límite de población de las ciudades Orkas."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="<string name='Traits/Orks/CityPopulationLimitFlavor'/>"/>
	<entry name="Orks/CityResearch" value="<string name='Traits/Orks/CityResearch'/>"/>
	<entry name="Orks/CityResearchDescription" value="Aumenta la generación de investigación de ciudades Orkas."/>
	<entry name="Orks/CityResearchFlavor" value="<string name='Traits/Orks/CityResearchFlavor'/>"/>
	<entry name="Orks/CityTier2" value="<string name='Traits/Orks/CityTier2'/>"/>
	<entry name="Orks/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier2Flavor" value="<string name='Traits/Orks/CityTier2Flavor'/>"/>
	<entry name="Orks/CityTier3" value="<string name='Traits/Orks/CityTier3'/>"/>
	<entry name="Orks/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier3Flavor" value="<string name='Traits/Orks/CityTier3Flavor'/>"/>
 	<entry name="Orks/CreatePermanentOrkoidFungusOnDeath" value="Descomposición permanente"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathDescription" value="Causa que las unidades creen hongos orkoides permanentes cuando mueran."/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathFlavor" value="<string name='Traits/Orks/CreateOrkoidFungusOnDeathFlavor'/>"/>
	<entry name="Orks/DakkajetSupaShoota" value="Dakkajet Superakribillador"/>
	<entry name="Orks/DakkajetSupaShootaDescription" value="Proporciona a los Dakkajets un Superakribillador."/>
	<entry name="Orks/DakkajetSupaShootaFlavor" value="<string name='Weapons/TwinLinkedSupaShootaFlavor'/>"/>
	<entry name="Orks/EavyArmour" value="<string name='Traits/EavyArmour'/>"/>
	<entry name="Orks/EavyArmourDescription" value="Aumenta el blindaje de Chicoz and Kaudillos."/>
	<entry name="Orks/EavyArmourFlavor" value="<string name='Traits/EavyArmourFlavor'/>"/>
	<entry name="Orks/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Orks/ExtraInfantryArmourDescription" value="Aumenta la armadura de la infantería."/>
	<entry name="Orks/ExtraInfantryArmourFlavor" value="Para cualquier otra raza, la armadura orka parece ridícula: grandes bultos de gruesas placas de metal, unidas por cables, clavos y aparatos sin sentido, pintadas de oro por mekánikoz y kanijoz. Pero con un poco de confianza orka, funciona estupendamente."/>
	<entry name="Orks/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Orks/ExtraVehicleArmourDescription" value="Aumenta la armadura de vehículos."/>
	<entry name="Orks/ExtraVehicleArmourFlavor" value="Cuando un mekániko se enfrenta a un Kaudillo enojado porque sus buggies y karroz de guerra siguen explotando, rápidamente ordenará a sus kanijoz que desentierren un poco de chatarra y colocan armadura adicional en cualquier lugar que se ajuste. Él sabe que los Lokos de la Velozidad pronto lo despegarán, después de todo."/>
	<entry name="Orks/Flyboss" value="<string name='Traits/Orks/Flyboss'/>"/>
	<entry name="Orks/FlybossDescription" value="Aumenta la precisión a distancia de los Dakkajets contra voladores, motocicletas a reacción y gravíticos."/>
	<entry name="Orks/FlybossFlavor" value="<string name='Traits/Orks/FlybossFlavor'/>"/>
	<entry name="Orks/GrabbinKlaw" value="<string name='Actions/GrabbinKlaw'/>"/>
	<entry name="Orks/GrabbinKlawDescription" value="Proporciona a los Karroz de Guerra la habilidad de inmovilizar a vehículos enemigos de tierra adyacentes"/>
	<entry name="Orks/GrabbinKlawFlavor" value="<string name='Actions/GrabbinKlawFlavor'/>"/>
	<entry name="Orks/GrotRiggers" value="Ayudantez"/>
	<entry name="Orks/GrotRiggersDescription" value="Proporciona a las Orugas, Lataz Azezinaz, Karroz de Guerra, Dreadnought Orkoz, Gorkanauts y Kill Burstaz regeneración pasiva de puntos de vida."/>
	<entry name="Orks/GrotRiggersFlavor" value="<string name='Traits/GrotRiggersFlavor'/>"/>
	<entry name="Orks/HealingRate" value="Marea Verde"/>
	<entry name="Orks/HealingRateDescription" value="Aumenta la tasa de curación de las unidades."/>
	<entry name="Orks/HealingRateFlavor" value="A través de la cooperación de un equipo de Matazanos Lokoz y Kaporalez, tu Kaudillo ha prescrito una nueva, y máz Orka, dieta para hacer a los chikoz más grandes y fuertes rápidamente. No importa si el frío aceite de garrapatos para el desayuno o un garrapato comecaras para la cena tiene efectos fisiológicos—los chikoz creen que sí, y eso los hace funcionar."/>
	<entry name="Orks/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Orks/HammerOfWrathDescription" value="Proporciona a los Dreadnought Orkoz, Kópteros, Garrapatos mamut, Gorkanauts, Lataz Azezinaz y Motoriztaz la habilidad para realizar ataques más devastadores."/>
	<entry name="Orks/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="<string name='Traits/Orks/MeleeDamage'/>"/>
	<entry name="Orks/MeleeDamageDescription" value="Aumenta la penetración de blindaje de las armas cuerpo a cuerpo."/>
	<entry name="Orks/MeleeDamageFlavor" value="<string name='Traits/Orks/MeleeDamageFlavor'/>"/>
	<entry name="Orks/MightMakesRight2" value="¡Teme a loz Orkoz!"/>
	<entry name="Orks/MightMakesRight2Description" value="Aumenta la ganancia de influencia al realizar daño."/>
	<entry name="Orks/MightMakesRight2Flavor" value="Cuando un ¡Waaagh! is lo suficientemente poderoso, tiene algo que como un efecto multiplicador, con cada golpe mejora el campo psíquico, en una especia de circuito de retroalimentación Orka."/>
	<entry name="Orks/OrkoidFungusBonusHealingRate" value="<string name='Traits/OrkoidFungusBonusHealingRate'/>"/>
	<entry name="Orks/OrkoidFungusBonusHealingRateDescription" value="Aumenta la curación de los hongos orkos."/>
	<entry name="Orks/OrkoidFungusBonusHealingRateFlavor" value="<string name='Traits/OrkoidFungusBonusHealingRateFlavor'/>"/>
	<entry name="Orks/OrkoidFungusFood" value="<string name='Traits/OrkoidFungusFood'/>"/>
	<entry name="Orks/OrkoidFungusFoodDescription" value="Aumenta la generación de comida en casillas con hongos orkos."/>
	<entry name="Orks/OrkoidFungusFoodFlavor" value="<string name='Traits/OrkoidFungusFoodFlavor'/>"/>
	<entry name="Orks/RedPaintJob" value="Trabajo de Pintura Roja"/>
<entry name="Orks/RedPaintJobDescription" value="Aumenta el daño de las Orugas, Kocherreactorez, Karroz de Guerra, Dakkajets y Bombaderos-Chicharradores."/>
	<entry name="Orks/RedPaintJobFlavor" value="<string name='Traits/RedPaintJobFlavor'/>"/>
	<entry name="Orks/Scavenger2" value="Kanijoz Chatarreroz"/>
	<entry name="Orks/Scavenger2Description" value="Aumenta la cantidad de mineral saqueado cuando matas a una unidad enemiga."/>
	<entry name="Orks/Scavenger2Flavor" value="Un bien organizado Kaporal habrá entrenado sus Kanijoz para recolectar y organizar chatarra para los Orkos, también evitar los casquillos y municiones más peligrosas."/>
	<entry name="Orks/SkorchaMissile" value="<string name='Weapons/SkorchaMissile'/>"/>
	<entry name="Orks/SkorchaMissileDescription" value="Proporciona a los Bombarderoz-Achicharradorez la habilidad para lanzar misiles anti-infantería que ignoran cobertura."/>
	<entry name="Orks/SkorchaMissileFlavor" value="<string name='Weapons/SkorchaMissileFlavor'/>"/>
	<entry name="Orks/Stikkbomb" value="<string name='Weapons/Stikkbomb'/>"/>
	<entry name="Orks/StikkbombDescription" value="Proporciona a la infantería la habilidad de tirar granadas anti-infantería."/>
	<entry name="Orks/StikkbombFlavor" value="<string name='Weapons/StikkbombFlavor'/>"/>
	<entry name="Orks/TankbustaBomb" value="<string name='Weapons/TankbustaBomb'/>"/>
	<entry name="Orks/TankbustaBombDescription" value="Proporciona a los Petatankez la habilidad de lanzar bombas anti-armadura."/>
	<entry name="Orks/TankbustaBombFlavor" value="<string name='Weapons/TankbustaBombFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolter" value="Bólter Pesado Adicional"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterDescription" value="Otorga a los Immolators, Exorcistas y Castigadores un Bólter Pesado adicional."/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalSacredRite" value="Fe Doblada"/>
	<entry name="SistersOfBattle/AdditionalSacredRiteDescription" value="Se pueden activar simultáneamente dos Ritos Sagrados."/>
	<entry name="SistersOfBattle/AdditionalSacredRiteFlavor" value="“Antaño, nuestros coros rituales eran singulares-solos o armonías cantadas con voces sagradas. Hoy en día, cantamos nuestros himnos conjuntamente, para amplificar sus temáticas duales, para que puedan alabar al Dios-Emperador de todas las maneras posibles.”<br/>  — Rememoradora desconocida, Evangelio de la Telaraña"/>
	<entry name="SistersOfBattle/AircraftsMissiles" value="Misiles de Aeronaves"/>
	<entry name="SistersOfBattle/AircraftsMissilesDescription" value="Otorga Misiles Skystrike a los Cazas Lightning y Misiles Hellstrike a los Cazabombarderos Avenger."/>
	<entry name="SistersOfBattle/AircraftsMissilesFlavor" value="Estos misiles permiten a las aeronaves atacar de manera efectiva a una variedad de objetivos; misiles Hellstrike en misiones contra vehículos blindados, misiles Skystrike para la fuerza aérea enemiga."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagus'/>"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Aumenta el blindaje de los Mortificadores."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagusFlavor'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonus'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Aumenta la penetración de blindaje de las armas de asalto."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/AvengeTheMartyrs" value="Vengad a las Mártires"/>
	<entry name="SistersOfBattle/AvengeTheMartyrsDescription" value="Aumenta la reducción de moral por Celo Vengativo."/>
	<entry name="SistersOfBattle/AvengeTheMartyrsFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/ChaffLauncher" value="Lanzador de Chaff"/>
	<entry name="SistersOfBattle/ChaffLauncherDescription" value="Otorga, a los Cazas Lightning y a los Cazabombarderos Avenger, la habilidad de lanzar chaff, que aumenta la reducción de daño de largo alcance."/>
	<entry name="SistersOfBattle/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfane" value="<string name='Actions/SistersOfBattle/ChaseTheProfane'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneDescription" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneDescription'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneFlavor" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="<string name='Traits/SistersOfBattle/CityGrowth'/>"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Aumenta la tasa de crecimiento de las ciudades."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="<string name='Traits/SistersOfBattle/CityGrowthFlavor'/>"/>
	<entry name="SistersOfBattle/CityTier2" value="<string name='Traits/SistersOfBattle/CityTier2'/>"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Aumenta el radio de adquisición de casillas de la ciudad principal."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="<string name='Traits/SistersOfBattle/CityTier2Flavor'/>"/>
	<entry name="SistersOfBattle/CityTier3" value="<string name='Traits/SistersOfBattle/CityTier3'/>"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Aumenta el radio de adquisición de casillas de la ciudad principal."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="<string name='Traits/SistersOfBattle/CityTier3Flavor'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaith" value="<string name='Actions/SistersOfBattle/ConvictionOfFaith'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithDescription" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithFlavor" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SistersOfBattle/DozerBladeDescription" value="Reduce la penalización al movimiento de los Immolators, Exorcistas y Castigadores en bosques y ruinas imperiales."/>
	<entry name="SistersOfBattle/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="<string name='Actions/SistersOfBattle/EternalCrusade'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="<string name='Actions/SistersOfBattle/EternalCrusadeDescription'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="<string name='Actions/SistersOfBattle/EternalCrusadeFlavor'/>"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Cazas Expertos"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Aumenta la puntería de los Cazas Lightning y Cazabombarderos Avenger."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="<string name='Traits/SistersOfBattle/ExpertFightersFlavor'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmourDescription" value="Aumenta el blindaje de la infantería."/>
	<entry name="SistersOfBattle/ExtraInfantryArmourFlavor" value="Construida con gruesas placas de ceramita, la servoarmadura empleada por las Adepta Sororitas se basa en los mismos sistemas arcaicos de sus primos los Adeptus Astartes. Proporciona el mismo grado de protección blindada, pero carece de los más avanzados sistemas de apoyo y de mejora de la fuerza, pues las Hermanas de Batalla no poseen la habilidad de los Marines Espaciales de interactuar directamente con su propia armadura."/>
	<entry name="SistersOfBattle/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SistersOfBattle/ExtraVehicleArmourDescription" value="Aumenta el blindaje de los vehículos."/>
	<entry name="SistersOfBattle/ExtraVehicleArmourFlavor" value="Los vehículos del Adepta Sororitas son demostraciones de fe, así que es crucial mantener y proteger los expuestos artefactos que portan. Así de protegidos, estos vehículos cubrirán más fácilmente el avance de las Órdenes Militantes del Adepta Sororitas."/>
	<entry name="SistersOfBattle/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SistersOfBattle/FragGrenadeDescription" value="Otorga a las Hermanas de Batalla, Canonesas, Celestes Sacrosantas, Dialogantes, Dominias, Hospitalarias, Imagificadoras, Armaduras Dechado, Vengadoras, Santa Celestine, Hermanas Arrepentidas y Céfiros la habilidad de lanzar granadas anti-infantería."/>
	<entry name="SistersOfBattle/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/HammerOfWrath" value="<string name='Traits/HammerOfWrath'/>"/>
	<entry name="SistersOfBattle/HammerOfWrathDescription" value="Otorga a los Caballeros-Lanceros Cerastus, Mortificadores, Armaduras Dechado, Santa Celestine y Céfiros la habilidad de ejectuar ataques más devastadores."/>
	<entry name="SistersOfBattle/HammerOfWrathFlavor" value="<string name='Traits/HammerOfWrathFlavor'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonus'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Aumenta la penetración de blindaje de las armas pesadas."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SistersOfBattle/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SistersOfBattle/KrakGrenadeDescription" value="Otorga a las Hermanas de Batalla, Canonesas, Dialogantes, Dominias, Hospitalarias, Imagificadoras, Vengadoras, Hermanas Arrepentidas y Céfiros la habilidad de lanzar granadas anti-blindaje."/>
	<entry name="SistersOfBattle/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/LaudHailer" value="<string name='Traits/SistersOfBattle/LaudHailer'/>"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="Otorga a los Castigadores, Exorcistas e Immolators un aura que permite a las unidades adyacentes que estén acobardadas el realizar Actos de Fe."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Traits/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Traits/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Otorga la reducción de daño No Sienten Dolor a las unidades de infantería adyacentes a una unidad de Hospitalarias."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Traits/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonus'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Aumenta la penetración de blindaje de las armas cuerpo a cuerpo."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SistersOfBattle/MeltaBombDescription" value="Otorga a las Hermanas de Batalla, Canonesas, Dialogantes, Dominias, Hospitalarias, Vengadoras, Hermanas Arrepentidas y Céfiros la habilidad de desplegar una bomba de fusión que es altamente efectiva contra vehículos pesados y fortificaciones."/>
	<entry name="SistersOfBattle/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SistersOfBattle/MinistorumIndoctrination" value="Indoctrinación del Ministorum"/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationDescription" value="Otorga Espíritu Mártir a los Cazas Lightning, Cazabombarderos Avenger y Caballeros-Lanceros Cerastus."/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationFlavor" value="“A través de una larga exposición a nuestros rituales y oraciones, nuestros aliados de fortuna—la Armada Imperial y el puñado de Caballeros-Lanceros Imperiales que sobrevivió—empezaron a tomar parte en nuestros ritos y a interiorizar nuestras creencias también. La fe, por lo que parece, es contagiosa.”<br/>  — Rememoradora desconocida, Evangelio de la Telaraña"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisation'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationDescription'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationFlavor'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="<string name='Actions/SistersOfBattle/PurifyingRecitations'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsDescription'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsFlavor'/>"/>
	<entry name="SistersOfBattle/RagingFervour" value="<string name='Actions/SistersOfBattle/RagingFervour'/>"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="<string name='Actions/SistersOfBattle/RagingFervourDescription'/>"/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="<string name='Actions/SistersOfBattle/RagingFervourFlavor'/>"/>
	<entry name="SistersOfBattle/RitualizedCeremonies" value="Ceremonias Ritualizadas"/>
	<entry name="SistersOfBattle/RitualizedCeremoniesDescription" value="Reduce el coste de los Ritos Sagrados."/>
	<entry name="SistersOfBattle/RitualizedCeremoniesFlavor" value="“Al principio, rezábamos de camino, en momentos de paz en ruinas o cuevas. Con el tiempo, según se estabilizaba el conflicto en una guerra interminable y demoledoras, nuestros rituales y oraciones también se estabilizaron, para formar un régimen y una rutina.”<br/>  — Rememoradora desconocida, Evangelio de la Telaraña"/>
	<entry name="SistersOfBattle/SacralVigor" value="<string name='Actions/SistersOfBattle/SacralVigor'/>"/>
	<entry name="SistersOfBattle/SacralVigorDescription" value="<string name='Actions/SistersOfBattle/SacralVigorDescription'/>"/>
	<entry name="SistersOfBattle/SacralVigorFlavor" value="<string name='Actions/SistersOfBattle/SacralVigorFlavor'/>"/>
	<entry name="SistersOfBattle/SanctifiedWorld" value="Mundo Santificado"/>
	<entry name="SistersOfBattle/SanctifiedWorldDescription" value="Aumenta el bonus de lealtad otorgado por el Convento de Fe."/>
	<entry name="SistersOfBattle/SanctifiedWorldFlavor" value="La mayoría de Órdenes extienden la iluminación del Dios-Emperador en mundos muy lejos de sus santuarios principales, estableciendo misiones lejanas y capillas subsidiarias para extender la influencia de su Orden y de la Eclesiarquía."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="<string name='Traits/SistersOfBattle/SimulacrumImperialis'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisDescription'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisFlavor'/>"/>
	<entry name="SistersOfBattle/SisterSuperior" value="<string name='Traits/SistersOfBattle/SisterSuperior'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="<string name='Traits/SistersOfBattle/SisterSuperiorDescription'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="<string name='Traits/SistersOfBattle/SisterSuperiorFlavor'/>"/>
	<entry name="SistersOfBattle/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SistersOfBattle/UniversalCatechism" value="Catecismo Universal"/>
	<entry name="SistersOfBattle/UniversalCatechismDescription" value="Otorga Escudo de Fe a Cazas Lightning, Cazabombarderos Avenger y Caballeros-Lanceros Cerastus."/>
	<entry name="SistersOfBattle/UniversalCatechismFlavor" value="Las tropas Imperiales requisadas para las Guerras de Fe habitualmente acaban rezando codo con codo con las hermanas del Adepta Sororitas antes de cada batalla, buscando guía en sus convicciones."/>
	<entry name="SistersOfBattle/VengefulSpirit" value="<string name='Actions/SistersOfBattle/VengefulSpirit'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="<string name='Actions/SistersOfBattle/VengefulSpiritDescription'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="<string name='Actions/SistersOfBattle/VengefulSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrder" value="Voto de la Orden Militante"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderDescription" value="Las unidades mantienen su Escudo de Fe aunque sean rotas."/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderFlavor" value="<string name='Traits/SistersOfBattle/ShieldOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="<string name='Actions/SistersOfBattle/WarmachinesWrath'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="<string name='Actions/SistersOfBattle/WarmachinesWrathDescription'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="<string name='Actions/SistersOfBattle/WarmachinesWrathFlavor'/>"/>
	<entry name="SpaceMarines/AssaultDoctrine" value="<string name='Traits/AssaultDoctrine'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineDescription" value="<string name='Actions/AssaultDoctrineDescription'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineFlavor" value="<string name='Traits/AssaultDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/SpaceMarines/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="Aumenta la penetración de blindaje de granadas, misiles and armas de área."/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/SpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="SpaceMarines/BolsterDefencesDescription" value="Otorga al Cañón Thunderfire la habilidad de aumentar la reducción de daño frente a ataques a distancia para la casilla objetivo."/>
	<entry name="SpaceMarines/BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/SpaceMarines/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/SpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDrill" value="<string name='Traits/BolterDrill'/>"/>
	<entry name="SpaceMarines/BolterDrillDescription" value="<string name='Actions/BolterDrillDescription'/>"/>
	<entry name="SpaceMarines/BolterDrillFlavor" value="<string name='Traits/BolterDrillFlavor'/>"/>
	<entry name="SpaceMarines/CeramitePlating" value="<string name='Traits/CeramitePlating'/>"/>
	<entry name="SpaceMarines/CeramitePlatingDescription" value="Aumenta el blindaje de las Stormraven y las Cañoneras Stormtalon."/>
	<entry name="SpaceMarines/CeramitePlatingFlavor" value="<string name='Traits/CeramitePlatingFlavor'/>"/>
	<entry name="SpaceMarines/ChapterUnity" value="<string name='Traits/ChapterUnity'/>"/>
	<entry name="SpaceMarines/ChapterUnityDescription" value="Aumenta la generación de lealtad de la Gran Sala."/>
	<entry name="SpaceMarines/ChapterUnityFlavor" value="<string name='Traits/ChapterUnityFlavor'/>"/>
	<entry name="SpaceMarines/CityTier2" value="<string name='Traits/SpaceMarines/CityTier2'/>"/>
	<entry name="SpaceMarines/CityTier2Description" value="Aumenta el radio de adquisición de casillas de las ciudades."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="<string name='Traits/SpaceMarines/CityTier2Flavor'/>"/>
	<entry name="SpaceMarines/CityTier3" value="<string name='Traits/SpaceMarines/CityTier3'/>"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="<string name='Traits/SpaceMarines/CityTier3Flavor'/>"/>
	<entry name="SpaceMarines/CityTier4" value="<string name='Traits/SpaceMarines/CityTier4'/>"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="<string name='Traits/SpaceMarines/CityTier4Flavor'/>"/>
	<entry name="SpaceMarines/ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="SpaceMarines/ClusterMinesDescription" value="Proporciona a los Exploradores motorizados la habilidad de colocar minas de racimo."/>
	<entry name="SpaceMarines/ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="SpaceMarines/CombatShield" value="<string name='Traits/CombatShield'/>"/>
	<entry name="SpaceMarines/CombatShieldDescription" value="Aumenta reduccion del daño de los Marines Espaciales de Asalto."/>
	<entry name="SpaceMarines/CombatShieldFlavor" value="<string name='Traits/CombatShieldFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrine" value="<string name='Traits/DevastatorDoctrine'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineDescription" value="<string name='Actions/DevastatorDoctrineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineFlavor" value="<string name='Traits/DevastatorDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SpaceMarines/DozerBladeDescription" value="Reduce la penalización de movimiento para los Cazadores, Predators, Razorbacks y Whirlwinds en bosques y ruinas imperiales."/>
	<entry name="SpaceMarines/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourFlavor" value="Si bien los sellos de pureza bien pueden mantener a un Adeptus Astartes en el campo de batalla durante más tiempo, la fe no detiene todos los proyectiles. Al actualizar a versiones posteriores de su armadura de poder, la infantería de los marines espaciales mejora sustancialmente su capacidad de supervivencia."/>
	<entry name="SpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraVehicleArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourFlavor" value="Cuando se han difundido y se han hecho las bendiciones adecuadas, un Tecnomarine puede hacer algunos cambios menores en sus cargos, en línea con el Codex Astartes, por supuesto, para mejorar la capacidad de supervivencia."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Escudo de Fortaleza."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Garantiza que la Fortaleza de la Redención no tenga reducción de daño invulnerable."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="<string name='Traits/SpaceMarines/FortressOfRedemptionDamageReductionFlavor'/>"/>	
	<entry name="SpaceMarines/FortressOfRedemptionMissileSilo" value="Silo de Misiles de la Fortaleza de la Redención."/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloDescription" value="Proporciona a la Fortaleza de la Redención un silo de misiles krakstorm."/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloFlavor" value="<string name='Weapons/KrakstormMissileSiloFlavor'/>"/>
	<entry name="SpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SpaceMarines/FragGrenadeDescription" value="Proporciona a los Apotecarios, Marines Espaciales de Asalto, Capitanes, Marines Espaciales Devastadores, Bibliotecarios, Exploradores, Exploradores en Motocicleta, Marines Espaciales Tácticos y Cañones Tormenta la habilidad de lanzar granadas anti-infantería."/>
	<entry name="SpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="SpaceMarines/HammerOfWrathDescription" value="Proporciona a los Marines Espaciales, Dreadnoughts y a los Exploradores motorizados la habilidad de realizar ataques más devastadores."/>
	<entry name="SpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="SpaceMarines/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SpaceMarines/HurricaneBolter" value="Bólter Huracán"/>
	<entry name="SpaceMarines/HurricaneBolterDescription" value="Proporciona a las Cañoneras Stormraven un Bólter Huracán."/>
	<entry name="SpaceMarines/HurricaneBolterFlavor" value="Usado por primera vez por el Capítulo de los Templarios Negros, los bólteres huracán combinan la furiosa potencia de fuego de multiples armas bólter unidas para producir una tormenta de proyectiles fulminantes."/>
	<entry name="SpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SpaceMarines/KrakGrenadeDescription" value="Proporciona a los Apotecarios, Marines Espaciales de Asalto, Capitanes, Marines Espaciales Devastadores, Bibliotecarios, Exploradores, Exploradores en Motocicleta, Marines Espaciales Tácticos y Cañones Tormenta la habilidad de lanzar granadas anti-blindaje."/>
	<entry name="SpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMelta" value="Land Speeder <string name='Weapons/MultiMelta'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaDescription" value="Grants Land Speeders a multi-meta Proporciona a los Land Speeders un multi-melta."/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaFlavor" value="<string name='Weapons/MultiMeltaFlavor'/>"/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/SpaceMarines/LasDamage'/>"/>
	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Upgrades/AstraMilitarum/LasDamageDescription'/>"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/SpaceMarines/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/LastStand" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LastStandDescription" value="Aumenta la moral de todas las unidades Marines Espaciales."/>
	<entry name="SpaceMarines/LastStandFlavor" value="<string name='Traits/LastStandFlavor'/>"/>
	<entry name="SpaceMarines/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LocatorBeacon" value="<string name='Traits/LocatorBeacon'/>"/>
	<entry name="SpaceMarines/LocatorBeaconDescription" value="Causa que un despliegue orbital no consuma puntos de acción cuando se despliegue adyacentemente de Exploradores motorizados o Cañoneras Stormraven."/>
	<entry name="SpaceMarines/LocatorBeaconFlavor" value="<string name='Traits/LocatorBeaconFlavor'/>"/>
	<entry name="SpaceMarines/MachineEmpathy" value="<string name='Traits/MachineEmpathy'/>"/>
	<entry name="SpaceMarines/MachineEmpathyDescription" value="<string name='Actions/MachineEmpathyDescription'/>"/>
	<entry name="SpaceMarines/MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="<string name='Traits/SpaceMarines/MeleeDamage'/>"/>
 	<entry name="SpaceMarines/MeleeDamageDescription" value="Aumenta la penetración de blindaje de las armas cuerpo a cuerpo."/>
 	<entry name="SpaceMarines/MeleeDamageFlavor" value="<string name='Traits/SpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SpaceMarines/MeltaBombDescription" value="Proporciona a los Marines Espaciales Tácticos, Marines Espaciales de Asalto, Marines Espaciales Devastadores, Exploradores y Exploradores en Motocicleta la habilidad de desplegar una bomba melta que es muy eficaz contra vehículos pesados y fortificaciones."/>
	<entry name="SpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
	<entry name="SpaceMarines/OmniscopeDescription" value="Otorga a los Devastadores Centurión la habilidad de ignorar la reducción del daño a distancia del objetivo."/>
	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalBombardment" value="<string name='Actions/OrbitalBombardment'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentDescription" value="<string name='Actions/OrbitalBombardmentDescription'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentFlavor" value="<string name='Actions/OrbitalBombardmentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalDeployment" value="<string name='Actions/OrbitalDeployment'/>"/>
	<entry name="SpaceMarines/OrbitalDeploymentDescription" value="Proporciona a las unidades la habilidad de desplegarse en cualquier punto del campo de batalla por cápsulas de desembarco."/>
	<entry name="SpaceMarines/OrbitalDeploymentFlavor" value="<string name='Actions/OrbitalDeploymentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalScan" value="<string name='Actions/OrbitalScan'/>"/>
	<entry name="SpaceMarines/OrbitalScanDescription" value="<string name='Actions/OrbitalScanDescription'/>"/>
	<entry name="SpaceMarines/OrbitalScanFlavor" value="<string name='Actions/OrbitalScanFlavor'/>"/>
	<entry name="SpaceMarines/PredatorLascannon" value="Bólters Pesados Adicionales"/>
 	<entry name="SpaceMarines/PredatorLascannonDescription" value="Otorga Bólters Pesados adicionales a las Fortalezas de Redención, Predators y Macro-Cañones Aquila."/>
	<entry name="SpaceMarines/PredatorLascannonFlavor" value="<string name='Weapons/LascannonFlavor'/>"/>
	<entry name="SpaceMarines/SiegeMasters" value="<string name='Traits/SiegeMasters'/>"/>
	<entry name="SpaceMarines/SiegeMastersDescription" value="<string name='Actions/SiegeMastersDescription'/>"/>
	<entry name="SpaceMarines/SiegeMastersFlavor" value="<string name='Actions/SiegeMastersFlavor'/>"/>
	<entry name="SpaceMarines/SiegeShield" value="<string name='Traits/SiegeShield'/>"/>
	<entry name="SpaceMarines/SiegeShieldDescription" value="Aumenta la armadura de los Vindicators y les permite moverse por terreno difícil sin penalización."/>
	<entry name="SpaceMarines/SiegeShieldFlavor" value="<string name='Traits/SiegeShieldFlavor'/>"/>
	<entry name="SpaceMarines/Signum" value="<string name='Actions/Signum'/>"/>
	<entry name="SpaceMarines/SignumDescription" value="Proporciona a los Marines Espaciales Devastadores la habilidad de negar la penalización por armas pesadas, artillería y de salva."/>
	<entry name="SpaceMarines/SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SpaceMarines/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SpaceMarines/TacticalDoctrine" value="<string name='Traits/TacticalDoctrine'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineDescription" value="<string name='Actions/TacticalDoctrineDescription'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/TeleportHomer" value="<string name='Traits/TeleportHomer'/>"/>
	<entry name="SpaceMarines/TeleportHomerDescription" value="Causa que el despliegue orbital no consuma puntos de acción cuando se desplieguen Capellanes, Exterminadores de Asalto y Exterminadores adyacentes a Marines Espaciales Tácticos o Exploradores."/>
	<entry name="SpaceMarines/TeleportHomerFlavor" value="<string name='Traits/TeleportHomerFlavor'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeak" value="<string name='Traits/TheFleshIsWeak'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakDescription" value="<string name='Actions/TheFleshIsWeakDescription'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="Tau/AdvancedTargetingSystem" value="<string name='Traits/Tau/AdvancedTargetingSystem'/>"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Sistema de soporte de Armadura y actualización de vehículos que aumenta la precisión a distancia."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="<string name='Traits/Tau/AdvancedTargetingSystemFlavor'/>"/>
	<entry name="Tau/AutomatedRepairSystem" value="<string name='Traits/Tau/AutomatedRepairSystem'/>"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Otorga restauración de puntos de vida a los vehículos cada turno."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="<string name='Traits/Tau/AutomatedRepairSystemFlavor'/>"/>
	<entry name="Tau/BlacksunFilter" value="<string name='Traits/Tau/BlacksunFilter'/>"/>
	<entry name="Tau/BlacksunFilterDescription" value="Aumenta la distancia de visión de los vehículos, Etéreos, Comandante, Rastreadores Tau y Armaduras de Combate."/>
	<entry name="Tau/BlacksunFilterFlavor" value="<string name='Traits/Tau/BlacksunFilterFlavor'/>"/>
	<entry name="Tau/BlastDamage" value="<string name='Traits/Tau/BlastDamage'/>"/>
	<entry name="Tau/BlastDamageDescription" value="Aumenta la penetración de armadura de las armas de llamas y de misiles."/>
	<entry name="Tau/BlastDamageFlavor" value="<string name='Traits/Tau/BlastDamageFlavor'/>"/>
	<entry name="Tau/BoltDamage" value="<string name='Traits/Tau/BoltDamage'/>"/>
	<entry name="Tau/BoltDamageDescription" value="Aumenta la penetración de blindaje de estallido y armas aceleradoras y rotativas."/>
	<entry name="Tau/BoltDamageFlavor" value="<string name='Traits/Tau/BoltDamageFlavor'/>"/>
	<entry name="Tau/BondingKnifeRitual" value="<string name='Actions/Tau/BondingKnifeRitual'/>"/>
	<entry name="Tau/BondingKnifeRitualDescription" value="Otorga a los Guerreros del Fuego, Rastreadores, Armaduras Miméticas XV25, Armaduras XV8 Crisis, Armaduras XV88 Apocalipsis, Armaduras Quilla Fantasma XV95 y Armaduras Cataclismo XV104 la capacidad de restaurar su moral."/>
	<entry name="Tau/BondingKnifeRitualFlavor" value="<string name='Actions/Tau/BondingKnifeRitualFlavor'/>"/>
	<entry name="Tau/CityTier2" value="<string name='Traits/Tau/CityTier2'/>"/>
	<entry name="Tau/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier2Flavor" value="<string name='Traits/Tau/CityTier2Flavor'/>"/>
	<entry name="Tau/CityTier3" value="<string name='Traits/Tau/CityTier3'/>"/>
	<entry name="Tau/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier3Flavor" value="<string name='Traits/Tau/CityTier3Flavor'/>"/>
	<entry name="Tau/CounterfireDefenceSystem" value="<string name='Traits/Tau/CounterfireDefenceSystem'/>"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Sistema de soporte de armadura que aumenta la precisión de los ataques defensivos."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Traits/Tau/CounterfireDefenceSystemFlavor'/>"/>
	<entry name="Tau/DisruptionPod" value="<string name='Traits/Tau/DisruptionPod'/>"/>
	<entry name="Tau/DisruptionPodDescription" value="Aumenta la reducción de daño a distancia de los vehículos."/>
	<entry name="Tau/DisruptionPodFlavor" value="<string name='Traits/Tau/DisruptionPodFlavor'/>"/>
	<entry name="Tau/DroneController" value="<string name='Traits/Tau/DroneController'/>"/>
	<entry name="Tau/DroneControllerDescription" value="Sistema de soporte de armadura que aumenta la precisión de los drones adyacentes."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Traits/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/EMPGrenade" value="<string name='Weapons/EMPGrenade'/>"/>
	<entry name="Tau/EMPGrenadeDescription" value="Otorga a Guerreros del Fuego y Rastreadores la capacidad de lanzar granadas anticarro."/>
	<entry name="Tau/EMPGrenadeFlavor" value="<string name='Weapons/EMPGrenadeFlavor'/>"/>
	<entry name="Tau/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tau/ExtraInfantryArmourDescription" value="Aumenta el blindaje de infantería y criaturas monstruosas."/>
	<entry name="Tau/ExtraInfantryArmourFlavor" value="Seguir estudiando el caparazón quitinoso de nuestros camaradas Thraxian ha revelado mejoras estructurales que podemos utilizar en la composición de nuestras armaduras y Cuerpos."/>
	<entry name="Tau/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Tau/ExtraVehicleArmourDescription" value="Aumenta el blindaje de los vehículos."/>
	<entry name="Tau/ExtraVehicleArmourFlavor" value="A diferencia de los inactivos Technosacerdotes de Marte, la Casta de la Tierra está innovando y creando sin cesar. Fio’tak es el material que es el pico actual de su creatividad, una aleación de metal nanocristalina dura, ultradensa y utilizada con moderación en sus mejores creaciones."/>
	<entry name="Tau/FlechetteDischarger" value="<string name='Traits/Tau/FlechetteDischarger'/>"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Otorga a los vehículos la capacidad de dañar a los atacantes cuerpo a cuerpo."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="<string name='Traits/Tau/FlechetteDischargerFlavor'/>"/>
	<entry name="Tau/ForTheGreaterGoodBonus" value="Charpactin Ambassaspores"/>
	<entry name="Tau/ForTheGreaterGoodBonusDescription" value="Reduce el coste de influencia de 'Por el Bien Supremo'."/>
	<entry name="Tau/ForTheGreaterGoodBonusFlavor" value="Cuando nuestros diplomáticos de la Casta del Agua están en el campo, a veces les resulta útil llevar a nuestros fungosos aliados de Charpactin, cuyas intermitentes comunicaciones ultravioletas tienen un efecto sedante, casi hipnótico, en casi todas las razas."/>
	<entry name="Tau/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tau/HammerOfWrathDescription" value="Otorga a Armaduras XV95 Quilla Fantasma, Armaduras XV104 Cataclismo, Armaduras XV107 R'Varna y KV128 Stormsurge la capacidad de realizar ataques más devastadores."/>
	<entry name="Tau/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tau/LasDamage" value="<string name='Traits/Tau/LasDamage'/>"/>
	<entry name="Tau/LasDamageDescription" value="Aumenta la penetración de blindaje de las armas de fusión, iones, plasma e inducción."/>
	<entry name="Tau/LasDamageFlavor" value="<string name='Traits/Tau/LasDamageFlavor'/>"/>
	<entry name="Tau/PhotonGrenade" value="<string name='Weapons/PhotonGrenade'/>"/>
	<entry name="Tau/PhotonGrenadeDescription" value="Otorga a Guerreros del Fuego, Rastreadores y Filoardientes la capacidad de lanzar granadas cegadoras."/>
	<entry name="Tau/PhotonGrenadeFlavor" value="<string name='Weapons/PhotonGrenadeFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="<string name='Traits/Tau/PointDefenceTargetingRelay'/>"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Otorga a los vehículos un mayor daño defensivo contra unidades enemigas adyacentes a otras unidades amigas."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="<string name='Traits/Tau/PointDefenceTargetingRelayFlavor'/>"/>
	<entry name="Tau/PurchaseEnergy" value="<string name='Actions/Tau/PurchaseEnergy'/>"/>
	<entry name="Tau/PurchaseEnergyDescription" value="Otorga la capacidad de comprar energía con influencia."/>
	<entry name="Tau/PurchaseEnergyFlavor" value="<string name='Actions/Tau/PurchaseEnergyFlavor'/>"/>
	<entry name="Tau/PurchaseFood" value="<string name='Actions/Tau/PurchaseFood'/>"/>
	<entry name="Tau/PurchaseFoodDescription" value="Otorga la capacidad de comprar alimentos con influencia."/>
	<entry name="Tau/PurchaseFoodFlavor" value="<string name='Actions/Tau/PurchaseFoodFlavor'/>"/>
	<entry name="Tau/PurchaseOre" value="<string name='Actions/Tau/PurchaseOre'/>"/>
	<entry name="Tau/PurchaseOreDescription" value="Otorga la capacidad de comprar minerales con influencia."/>
	<entry name="Tau/PurchaseOreFlavor" value="<string name='Actions/Tau/PurchaseOreFlavor'/>"/>
	<entry name="Tau/PurchasePopulation" value="<string name='Actions/Tau/PurchasePopulation'/>"/>
	<entry name="Tau/PurchasePopulationDescription" value="Otorga la capacidad de comprar población con influencia."/>
	<entry name="Tau/PurchasePopulationFlavor" value="<string name='Actions/Tau/PurchasePopulationFlavor'/>"/>
	<entry name="Tau/PurchaseResearch" value="<string name='Actions/Tau/PurchaseResearch'/>"/>
	<entry name="Tau/PurchaseResearchDescription" value="Otorga la capacidad de comprar investigación con influencia."/>
	<entry name="Tau/PurchaseResearchFlavor" value="<string name='Actions/Tau/PurchaseResearchFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="<string name='Traits/Tau/RipykaVa'/>"/>
	<entry name="Tau/RipykaVaDescription" value="Reduce el tiempo de reutilización de las Metastrategias del Comandante."/>
	<entry name="Tau/RipykaVaFlavor" value="<string name='Traits/Tau/RipykaVaFlavor'/>"/>
	<entry name="Tau/SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="Tau/SeekerMissileDescription" value="Otorga a vehículos y Armaduras Apocalipsis XV88 un arma de misiles."/>
	<entry name="Tau/SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Tau/SensorSpines" value="multisensores"/>
	<entry name="Tau/SensorSpinesDescription" value="Otorga a los vehículos que se muevan a través de cobertura."/>
	<entry name="Tau/SensorSpinesFlavor" value="Los multisensores se utilizan para alimentar datos de un sistema avanzado de control de vuelo de seguimiento terrestre, trazando cursos seguros a través de terrenos traicioneros evitando trampas y minas que podrían estar ocultas a la vista."/>
	<entry name="Tau/ShieldGenerator" value="<string name='Traits/Tau/ShieldGenerator'/>"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Sistema de apoyo de Armadura que otorga una reducción de daño invulnerable."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Traits/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StimulantInjector" value="<string name='Traits/Tau/StimulantInjector'/>"/>
	<entry name="Tau/StimulantInjectorDescription" value="Sistema de soporte de Armadura que aumenta la reducción de daño sin dolor."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Traits/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/SubversionBonus" value="Investigación sediciosa"/>
	<entry name="Tau/SubversionBonusDescription" value="Aumenta la penalización de lealtad de Subvertir Ciudad."/>
	<entry name="Tau/SubversionBonusFlavor" value="Mientras realizaba una investigación opositora, los T'au encontraron la frase -piensa como tu enemigo- en un libro de texto militar humano. La Casta del Agua se lo ha tomado muy en serio e investigan a fondo las necesidades insatisfechas y los deseos de un asentamiento determinado, ya sea haciendo añicos a los esclavos Necron o a los chicos Orkos hambrientos de garrapatos, antes de fomentar una rebelión."/>
	<entry name="Tau/TacticalSupportTurret" value="<string name='Weapons/TacticalSupportTurret'/>"/>
	<entry name="Tau/TacticalSupportTurretDescription" value="Otorga a los Guerreros del Fuego un arma adicional cuando están parados."/>
	<entry name="Tau/TacticalSupportTurretFlavor" value="<string name='Weapons/TacticalSupportTurretFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="<string name='Traits/Tau/UtopiaBonus'/>"/>
	<entry name="Tau/UtopiaBonusDescription" value="<string name='Traits/Tau/UtopiaBonusDescription'/>"/>
	<entry name="Tau/UtopiaBonusFlavor" value="<string name='Traits/Tau/UtopiaBonusFlavor'/>"/>
	<entry name="Tau/VectoredRetroThrusters" value="<string name='Traits/Tau/VectoredRetroThrusters'/>"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Sistema de apoyo de Armadura que aumenta el movimiento y permite a la unidad ignorar la zona de control enemiga."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Traits/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="<string name='Traits/Tau/VelocityTracker'/>"/>
	<entry name="Tau/VelocityTrackerDescription" value="Sistema de soporte de Armadura que aumenta la precisión a distancia contra voladores."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Traits/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tyranids/AcidBlood" value="Sangre ácida"/>
	<entry name="Tyranids/AcidBloodDescription" value="Proporciona a las monstruosas criaturas tiránidas la cualidad de dañar a todos los atacantes cuerpo a cuerpo."/>
	<entry name="Tyranids/AcidBloodFlavor" value="<string name='Traits/Tyranids/AcidBloodFlavor'/>"/>
	<entry name="Tyranids/AdrenalGlands" value="Glándulas de adrenalina"/>
	<entry name="Tyranids/AdrenalGlandsDescription" value="Proporciona a las unidades aumento del movimiento e incremento del daño cuerpo a cuerpo."/>
	<entry name="Tyranids/AdrenalGlandsFlavor" value="Las glándulas suprarrenales saturan el cuerpo de su huésped con sustancias químicas que estimulan el metabolismo de la criatura a un estado de frenesí hiperactivo."/>
	<entry name="Tyranids/BiomorphDamage" value="<string name='Traits/Tyranids/BiomorphDamage'/>"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Aumenta la penetración de blindaje de los biomorfos."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="<string name='Traits/Tyranids/BiomorphDamageFlavor'/>"/>
	<entry name="Tyranids/BioPlasma" value="Bioplasma de Cárnifex"/>
	<entry name="Tyranids/BioPlasmaDescription" value="Proporciona a los Cárnifex un arma a distancia adicional."/>
	<entry name="Tyranids/BioPlasmaFlavor" value="<string name='Weapons/BioPlasmaFlavor'/>"/>
	<entry name="Tyranids/BoneMace" value="Maza ósea de Cárnifex"/>
	<entry name="Tyranids/BoneMaceDescription" value="Proporciona a los Cárnifex un arma cuerpo a cuerpo adicional."/>
	<entry name="Tyranids/BoneMaceFlavor" value="<string name='Weapons/BoneMaceFlavor'/>"/>
	<entry name="Tyranids/CityCost" value="Malántropos metamórficos"/>
	<entry name="Tyranids/CityCostDescription" value="Reduce el coste de fundar nuevas ciudades."/>
	<entry name="Tyranids/CityCostFlavor" value="El papel de los Malátropos en la fundación de nuevas colmenas tiránidas ha sido poco estudiado por los genetistas imperiales, ya que en la mayoría de las circunstancias están muertos o huyendo cuando se funda la colmena. Sin embargo, se cree que transportan la semilla de la ciudad a nuevas ubicaciones cuando se requiere una localización de cría. En ocasiones se han observado Malántropos´ inusuales, con estructuras físicas especializadas que les permiten realizar esta tarea de manera más eficiente."/>
	<entry name="Tyranids/CityDamage" value="<string name='Traits/Tyranids/CityDamage'/>"/>
	<entry name="Tyranids/CityDamageDescription" value="Las unidades enemigas en las ciudades tiránidas sufren daño cada turno."/>
	<entry name="Tyranids/CityDamageFlavor" value="<string name='Traits/Tyranids/CityDamageFlavor'/>"/>
	<entry name="Tyranids/CityGrowth" value="<string name='Traits/Tyranids/CityGrowth'/>"/>
	<entry name="Tyranids/CityGrowthDescription" value="Aumenta la tasa de crecimiento en las ciudades tiránidas."/>
	<entry name="Tyranids/CityGrowthFlavor" value="<string name='Traits/Tyranids/CityGrowthFlavor'/>"/>
	<entry name="Tyranids/CityLoyalty" value="<string name='Traits/Tyranids/CityLoyalty'/>"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Reduce la penalización de lealtad por tener múltiples ciudades."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="<string name='Traits/Tyranids/CityLoyaltyFlavor'/>"/>
	<entry name="Tyranids/CityPopulationLimit" value="<string name='Traits/Tyranids/CityPopulationLimit'/>"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Aumenta el límite de población de las ciudades tiránidas."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="<string name='Traits/Tyranids/CityPopulationLimitFlavor'/>"/>
	<entry name="Tyranids/CityProduction" value="<string name='Traits/Tyranids/CityProduction'/>"/>
	<entry name="Tyranids/CityProductionDescription" value="Incremetna la producción de las ciudades tiránidas."/>
	<entry name="Tyranids/CityProductionFlavor" value="<string name='Traits/Tyranids/CityProductionFlavor'/>"/>
	<entry name="Tyranids/CityTier2" value="<string name='Traits/Tyranids/CityTier2'/>"/>
	<entry name="Tyranids/CityTier2Description" value="Aumenta el radio de adquisición de casillas de la ciudad."/>
	<entry name="Tyranids/CityTier2Flavor" value="<string name='Traits/Tyranids/CityTier2Flavor'/>"/>
	<entry name="Tyranids/ConsumeTile2" value="Digestión eficiente"/>
	<entry name="Tyranids/ConsumeTile2Description" value="Disminuye el coste de influencia para consumir casillas."/>
	<entry name="Tyranids/ConsumeTile2Flavor" value="Si bien las mandíbulas de Malántropo o Devorador no tienen problemas para abrirse paso a través de la carne, el hueso o incluso el plastiacero, se ven disminuidas por la necesidad de consumir las enormes cantidades de tierra y roca que la Mente Enjambre exige a escala y velocidad. Se presume que se han desplegado organismos especializados para consumirlos—aunque, de nuevo, ninguno ha sobrevivido para informarlos."/>
	<entry name="Tyranids/Deathspitter" value="Escupemuerte de Mántifex"/>
	<entry name="Tyranids/DeathspitterDescription" value="Proporciona a los"/>
	<entry name="Tyranids/DeathspitterFlavor" value="<string name='Weapons/DeathspitterFlavor'/>"/>
	<entry name="Tyranids/DesiccatorLarvae" value="<string name='Weapons/DesiccatorLarvae'/>"/>
	<entry name="Tyranids/DesiccatorLarvaeDescription" value="Proporciona a los Tiranos, Tervigones y Tiránofex un arma de plantilla."/>
	<entry name="Tyranids/DesiccatorLarvaeFlavor" value="<string name='Weapons/DesiccatorLarvaeFlavor'/>"/>
	<entry name="Tyranids/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourFlavor" value="La Mente Enjambre es una mentalidad frugal. ¿Por qué sacrificar recursos armando tropas que serán reclamadas de todos modos? Solo cuando la apuesta tiene sentido, invierte en quitina extra gruesa y hueso para su fauna."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmour" value="<string name='Traits/ExtraMonstrousCreatureArmour'/>"/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourDescription" value="Aumenta el blindaje de las criaturas monstruosas."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourFlavor" value="A través de una combinación de caparazones endurecidos, campos disruptores y terminaciones nerviosas muertas, la Mente Enjambre puede alterar fácilmente la resistencia de sus criaturas más grandes."/>
	<entry name="Tyranids/FleshHooks" value="<string name='Weapons/FleshHooks'/>"/>
	<entry name="Tyranids/FleshHooksDescription" value="Otorga a los Guerreros Tiránidos, Tiránidos Prime y Lictores un arma a distancia adicional."/>
	<entry name="Tyranids/FleshHooksFlavor" value="<string name='Weapons/FleshHooksFlavor'/>"/>
	<entry name="Tyranids/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tyranids/HammerOfWrathDescription" value="Otorga la habilidad de realizar ataques más devastadores a los Exocrinos, Gárgolas, Haruspexes, Aerovoros del Enjambre, Tiranos del Enjambre, Hierodulos Guadaña, Tervigones, Trigones, Tiranocitos y Tiranofexes."/>
	<entry name="Tyranids/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="<string name='Traits/Tyranids/InfantryUpkeep'/>"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Reduce el coste de biomasa por el mantenimiento de la infantería."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="<string name='Traits/Tyranids/InfantryUpkeepFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2" value="Apressant salvaje"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Description" value="Reduce el coste de anular el comportamiento instintivo."/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Flavor" value="Mantener el control de sus tropas es una de las principales preocupaciones de la Mente Enjambre y una que trata de superar mediante diversas adaptaciones, como sus criaturas sinapsis. Una más simple es reducir el salvajismo natural de sus unidades cuando funcionan de forma salvaje, por lo que requieren menos esfuerzo psíquico para volver a tener el control."/>
	<entry name="Tyranids/LongRangedDamage" value="<string name='Traits/Tyranids/LongRangedDamage'/>"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Aumenta la penetración de blindaje de armas de largo alcance."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="<string name='Traits/Tyranids/LongRangedDamageFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="<string name='Traits/Tyranids/MeleeDamage'/>"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Aumenta la penetración de blindaje de las armas cuerpo a cuerpo."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="<string name='Traits/Tyranids/MeleeDamageFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="Proporciona a los Zoántropos la capacidad de maldecir a las unidades enemigas disminuyendo su puntería."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation2" value="Gustarflagelli malantrópico"/>
	<entry name="Tyranids/PreyAdaptation2Description" value="Aumenta la investigación ganada cuando los enemigos mueren en el área de un Malántropo."/>
	<entry name="Tyranids/PreyAdaptation2Flavor" value="Una adaptación inusual de la forma malantrópica, los gustarflagelli son zarcillos delgados de filamento que rodean a un Malántropo como una nube carnosa que se retuerce. Se presume que permiten a los Malántropos recopilar y retener más datos genéticos de enemigos recientemente fallecidos."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="<string name='Traits/Tyranids/ProductionBuildingUpkeep'/>"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Reduce el coste de influencia por el mantenimiento de los edificios de producción."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ProductionBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="<string name='Traits/Tyranids/Reclamation2'/>"/>
	<entry name="Tyranids/Reclamation2Description" value="Reduce el coste de influencia de una reclamación."/>
	<entry name="Tyranids/Reclamation2Flavor" value="<string name='Traits/Tyranids/Reclamation2Flavor'/>"/>
	<entry name="Tyranids/Reclamation3" value="<string name='Traits/Tyranids/Reclamation3'/>"/>
	<entry name="Tyranids/Reclamation3Description" value="Elimina el tiempo de enfriamiento de una reclamación."/>
	<entry name="Tyranids/Reclamation3Flavor" value="<string name='Traits/Tyranids/Reclamation3Flavor'/>"/>
	<entry name="Tyranids/Regeneration" value="Regeneración"/> 
	<entry name="Tyranids/RegenerationDescription" value="Proporciona a las criaturas monstruosas tiránidas y tiránidos prime recuperación de puntos de vida cada turno."/>
	<entry name="Tyranids/RegenerationFlavor" value="<string name='Traits/RegenerationFlavor'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="<string name='Traits/Tyranids/ResourceBuildingUpkeep'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Reduce el coste de influencia por el mantenimiento de los edificios de recursos."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ResourceBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="<string name='Traits/Tyranids/ShortRangedDamage'/>"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Aumenta la penetración de blindaje de las armas de corto alcance."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="<string name='Traits/Tyranids/ShortRangedDamageFlavor'/>"/>
	<entry name="Tyranids/StingerSalvo" value="<string name='Weapons/StingerSalvo'/>"/>
	<entry name="Tyranids/StingerSalvoDescription" value="Otorga a los Aerovoros del Enjambre un arma a distancia adicional."/>
	<entry name="Tyranids/StingerSalvoFlavor" value="<string name='Weapons/StingerSalvoFlavor'/>"/>
	<entry name="Tyranids/ThresherScythe" value="<string name='Weapons/ThresherScythe'/>"/>
	<entry name="Tyranids/ThresherScytheDescription" value="Proporciona a los Exocrinos y Harúspex un arma cuerpo a cuerpo adicional."/>
	<entry name="Tyranids/ThresherScytheFlavor" value="<string name='Weapons/ThresherScytheFlavor'/>"/>
	<entry name="Tyranids/ToxinSacs" value="<string name='Traits/Tyranids/ToxinSacs'/>"/>
	<entry name="Tyranids/ToxinSacsDescription" value="Aumenta el daño de las armas cuerpo a cuerpo contra la infantería y criaturas monstruosas."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="<string name='Traits/Tyranids/ToxinSacsFlavor'/>"/>
	<entry name="Tyranids/Toxinspike" value="Púa tóxica de Trigón"/>
	<entry name="Tyranids/ToxinspikeDescription" value="Proporciona a los Trigones un arma cuerpo a cuerpo adicional."/>
	<entry name="Tyranids/ToxinspikeFlavor" value="<string name='Weapons/ToxinspikeFlavor'/>"/>
	<entry name="Tyranids/Tunnel2" value="<string name='Traits/Tyranids/Tunnel2'/>"/>
	<entry name="Tyranids/Tunnel2Description" value="Aumenta los puntos de vida de la progenie."/>
	<entry name="Tyranids/Tunnel2Flavor" value="<string name='Traits/Tyranids/Tunnel2Flavor'/>"/>
	<entry name="Tyranids/VehiclesUpkeep" value="<string name='Traits/Tyranids/VehiclesUpkeep'/>"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Reduce el biomasa necesario para le mantenimiento de las criaturas monstruosas."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="<string name='Traits/Tyranids/VehiclesUpkeepFlavor'/>"/>
	
	<entry name="Missing" value="Falta"/>
</language>
