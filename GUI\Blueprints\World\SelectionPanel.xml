<?xml version="1.0" encoding="utf-8"?>
<world:selectionPanel extends="Panel" content.layout.direction="TopToBottom" showEffect="FadeInBottom" hideEffect="FadeOutBottom">
	<label name="attributesLabel" alignment="TopCenter" preferredSize="FillParent 28"/>
	<container preferredSize="FillParent FillParent" weights="FillAll 1">
		<world:selectionPortrait name="portrait" preferredSize="FillParent FillParent" weights="1 FillAll"/>
		<container name="rightSideContainer" layout.direction="TopToBottom" preferredSize="236 FillParent">
			<container name="weaponsContainer" preferredSize="FillParent 28" layout="CompressedFlow" layout.alignment="TopCenter" layout.gap="8 0"/>
			<container name="traitsContainer" preferredSize="FillParent 28" layout="CompressedFlow" layout.alignment="TopCenter" layout.gap="8 0"/>
			<scrollableContainer name="actionsContainer" preferredSize="FillParent 140" scrollableContent.layout.direction="LeftToRight" visibleContentContainer.content.margin="0 0" scrollBarMargin="0 0"/>
		</container>
	</container>
</world:selectionPanel>
