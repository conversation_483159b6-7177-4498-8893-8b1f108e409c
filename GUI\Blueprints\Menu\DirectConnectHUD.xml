<?xml version="1.0" encoding="utf-8"?>
<menu:lobbyHUD extends="HUD" layout="Flow" layout.alignment="MiddleCenter" layout.gap="0 0" layout.direction="TopToBottom" preferredSize="1240 720">
	<label caption="<style name='ShadowedMenuTitle'/><string name='GUI/DirectConnect'/>"/>
	<image texture="GUI/HeadlineBar" color="GUI/HeadlineBar"/>
	<component preferredSize="FillParent 30"/>
	<container layout.alignment="MiddleCenter" layout.gap="20 20" preferredSize="600 540" weights="FillAll 1">
		<container layout.gap="20 20" preferredSize="FillParent 30">
			<label alignment="MiddleRight" style="<style name='ShadowedMenuHeading'/>" caption="<string name='Settings/Network/RemoteAddress'/>:" preferredSize="FillParent FillParent" weights="1 FillAll"/>
			<textBox surface.texture="GUI/ShadowedSurface" name="remoteAddressTextBox" preferredSize="FillParent FillParent" weights="2 FillAll"/>
		</container>
		<container layout.gap="20 20" preferredSize="FillParent 30">
			<label alignment="MiddleRight" style="<style name='ShadowedMenuHeading'/>" caption="<string name='Settings/Network/RemotePort'/>:" preferredSize="FillParent FillParent" weights="1 FillAll"/>
			<textBox surface.texture="GUI/ShadowedSurface" name="remotePortTextBox" preferredSize="FillParent FillParent" weights="2 FillAll"/>
		</container>
		<container layout.gap="20 20" preferredSize="FillParent 30">
			<label alignment="MiddleRight" style="<style name='ShadowedMenuHeading'/>" caption="<string name='Settings/Network/ListenPort'/>:" preferredSize="FillParent FillParent" weights="1 FillAll"/>
			<textBox surface.texture="GUI/ShadowedSurface" name="listenPortTextBox" preferredSize="FillParent FillParent" weights="2 FillAll"/>
		</container>
	</container>
	<component preferredSize="FillParent 30"/>
	<container layout.alignment="TopCenter" layout.gap="40 40" preferredSize="FillParent WrapContent">
		<navigationButton name="cancelButton" label.caption="<string name='GUI/Back'/>" control="Controls/Cancel"/>
		<navigationButton name="hostButton" label.caption="<string name='GUI/Host'/>"/>
		<navigationOKButton name="connectButton" label.caption="<string name='GUI/Connect'/>" control=""/>
	</container>
</menu:mainMenuHUD>
