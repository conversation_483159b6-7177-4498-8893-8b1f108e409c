<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#0" value="Vektor eingegeben."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#1" value="Klar und deutlich!"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#2" value="Im Anflug."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#3" value="Maschinengewehrfeuer wird eingeleitet."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Broken#0" value="Priorität auf Instandhaltung!"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Hurt#0" value="Flügel gestutzt…"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#0" value="Immer nur kreisen…"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#1" value="Verteilung von geweihten Tränken aus der Luft."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#2" value="Druckveränderung. Materialstruktur wird angepasst."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#3" value="Evakuierung von Landraupen in Vorbereitung."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Shaken#0" value="Mayday, Mayday!"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Victory#0" value="Keine feindlichen Signaturen mehr erfasst. Wiederaufnahme des Ursprungskurses."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#0" value="Kastelan-Roboter erhalten Anweisungen."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#1" value="Runen werden geweiht."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#2" value="Silica Animus wird unterdrückt."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#3" value="Doctrina-Trägerscheiben werden eingesetzt."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Broken#0" value="… die Eisernen Menschen…"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Hurt#0" value="… Reparatur unmöglich…"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#0" value="Das Fleisch ist schwach, die Maschine stark."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#1" value="In Binhärform, Rhythmus und Gesang. Ich wünschte, du könntest hören."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#2" value="Schöpfungen des Omnissiah können nicht verbessert werden."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#3" value="In nomine imperii et mechanici et spiritus omnissiah."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Shaken#0" value="Rückzug-Trägerscheiben lokalisiert."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Victory#0" value="Uralte Weisheit, bewährt durch Kraft."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#0" value="VESTRA INDUSTRIA NOSTRUM!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#1" value="MUNITIONSVERSCHWENDUNG WÄRE EINE SÜNDE!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#2" value="FÜR DIE ANTRIEBSKRAFT!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#3" value="DER FUNKE LODERT IM INNEREN."/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Broken#0" value="Nostra potex decit… Unser Wille bricht!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Hurt#0" value="NOCH MARSCHIEREN WIR."/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#0" value="ICH VERGIESSE DIE TRÄNEN DES OMNISSIAH."/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#1" value="VERFLUCHT SEIEN DIE CORPUSCARII…"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#2" value="MEIN ELEKTOO IST NICHT AUSGELASTET."/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#3" value="IN NOMINE DEI MECHANICI."/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Shaken#0" value="FUNKENFLUG!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Victory#0" value="WIR STROTZEN VOR KRAFT!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#0" value="Sie werden die Weisheit des Mars zu spüren bekommen!"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#1" value="Langstreckenlösung gefunden."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#2" value="Anvisierte Datenverbindung genehmigt."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#3" value="Maschinengeister entfesselt."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Broken#0" value="Dominus, wir müssen--"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Hurt#0" value="Monoaufgabenservitoren ohne Erfolg."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#0" value="Servitoren im Ruhemodus."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#1" value="Gehäuse wird geölt."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#2" value="Schwäche wird exzidiert."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#3" value="Ich träume von einem roten Planeten…"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Shaken#0" value="Überlegener Gegner feuert!"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Victory#0" value="Seit 10.000 Jahren ist uns der Sieg sicher."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#0" value="C32: Feuer-Template wird umgesetzt."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#1" value="8-7: Zerstörer-Klade aktiv."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#2" value="7T32: Alle Waffen ausgerichtet."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#3" value="867: Cognis-Und/Oder-Phosphorentzündung."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Broken#0" value="Nur Menschen haben Furcht. Einst waren wir--"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Hurt#0" value="Funktionalität beeinträchtigt. Gewebe noch erhalten."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#0" value="T418: Servitor im Wartemodus."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#1" value="182: Wartung steht bevor."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#2" value="FEHLER: Was war ich?"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#3" value="326: Aufkommende Erinnerungen… unterdrückt."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Shaken#0" value="991: Emotionen werden freigesetzt."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Victory#0" value="T1: Funktion durchgeführt. Nächstes Ziel…"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#0" value="Wir sind nur eine Waffe in der rechten Hand des Imperators."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#1" value="Für die Ehre des Questor Mechanicus!"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#2" value="Fürchtet euch, wenn ihr meine Schritte hört!"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#3" value="ICH BIN EIN IMPERIALER RITTER!"/>
	<entry name="AdeptusMechanicus/KnightCrusader:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Broken#0" value="Das… das kann nicht sein."/>
	<entry name="AdeptusMechanicus/KnightCrusader:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Hurt#0" value="Lieber tot als ohne Ehre!"/>
	<entry name="AdeptusMechanicus/KnightCrusader:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#0" value="Meine Maid erwartet mich."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#1" value="Hach, jetzt einen Tjost in meiner Heimatwelt…"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#2" value="Mein Geschlecht ist für seine Demut bekannt."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#3" value="Eiserner Riese erwartet Befehle…"/>
	<entry name="AdeptusMechanicus/KnightCrusader:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Shaken#0" value="DU WAGST ES…"/>
	<entry name="AdeptusMechanicus/KnightCrusader:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Victory#0" value="Durch meine Hand zu sterben ist keine Schande."/>
	<entry name="AdeptusMechanicus/KnightCrusader:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#0" value="Ich exterminiere, Dominus."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#1" value="Ich exterminiere immer noch, Dominus."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#2" value="Meine Klauen sind messerscharf!"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#3" value="Vor Phosphor gibt es kein Entrinnen, ihr Narren."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Broken#0" value="Bereits auf der Flucht…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Hurt#0" value="Unsere Exterminierung naht…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#0" value="Exterminieren… muss exterminieren."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#1" value="Die Stümpfe meiner Gliedmaßen… jucken."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#2" value="Meine Flügel sind stets gespannt."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:IdleCount" value="3"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Shaken#0" value="Der Abschaum ist… zügellos."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Victory#0" value="Asche zu Asche…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#0" value="Antriebskraft wird entfesselt."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#1" value="Skitarii werden galvanisiert…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#2" value="… Mmmmagnabeschleunigerlanze…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#3" value="Sind wir nicht gestärkt?!"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Broken#0" value="Das ist logisch betrachtet nicht möglich."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Hurt#0" value="Das… das wurde nicht einkalkuliert."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#0" value="Meine Energie liegt brach…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#1" value="Zapfe die Galvanische Zelle an."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#2" value="Überladen, aber unterfordert."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#3" value="Wenn man die Xenos verstehen will…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Shaken#0" value="Beschützt euren Magos, ihr Maden!"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Victory#0" value="Ihre Bioelektrizität… gehört mir."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#0" value="In Deckung gehen und keine Munition verschwenden."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#1" value="Ich brauche euch lebend, Skitarii."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#2" value="Kein Dissens, jetzt kämpfen wir!"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#3" value="Alles unter Kontrolle!"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Broken#0" value="Wir können entsorgt werden…"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Hurt#0" value="Wir sterben…? Rettet den Magos!"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#0" value="Welche Pläne Ihr auch immer habt, Magos…"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#1" value="Können wir nicht ein paar Skitarii retten?"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#2" value="Wir leben, um zu sterben, Magos."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#3" value="Servoschädel, ich nenne dich „Morte“."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Shaken#0" value="Kontrolledikt erlassen!"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Victory#0" value="Unsere Verluste sind minimal, Magos."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#0" value="Strahlentruppen an der Front!"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#1" value="Mal sehen, wen die Strahlung zuerst tötet…"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#2" value="Die Karabiner glühen."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#3" value="Niemand kann uns aufhalten."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Broken#0" value="Lieber später sterben…"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Hurt#0" value="Uns entweicht Blut und Strahlung."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#0" value="Ich habe 99 Upgrades, aber keines davon ist ein Strahlenschutz."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#1" value="Ich glühe förmlich."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#2" value="Uff… Mir geht es nicht so gut."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#3" value="Ich würde in unserer Nähe auch nicht essen…"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Shaken#0" value="Magos, wir sterben für Euch."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Victory#0" value="Bei ihnen spielt die Halbwertszeit keine Rolle mehr."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#0" value="Mein Manöver, dein Tod."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#1" value="Bauern, vorwärts!"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#2" value="Ich sehe alle Spielfiguren…"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#3" value="Ich tue, was getan werden muss."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Broken#0" value="Verteidigung ist vonnöten… Lushin?"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Hurt#0" value="Clever, den König ins Visier zu nehmen."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#0" value="Kein Zyklus gewährt Ruhe."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#1" value="Ineffizienz wird zu einer Entropie."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#2" value="Welch Freude Manipulation bereitet…"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#3" value="Ja… Ich sehe ihre Bewegungen und Gegenbewegungen."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Shaken#0" value="Eine überraschend gewagte Taktik. Bewundernswert."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Victory#0" value="Der Sieg war von langer Hand geplant. Dennoch bringt er mich in Wallung."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#0" value="Die Stadt erwartet euch…"/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#1" value="Ihre Leben gehören uns, nicht euch."/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#2" value="Servitoren feuern."/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#3" value="Fabrikzentrum in Kampfhandlungen."/>
	<entry name="AdeptusMechanicus/Headquarters:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/Headquarters:Broken#0" value="Die Stadt wird fallen."/>
	<entry name="AdeptusMechanicus/Headquarters:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Hurt#0" value="Wir erleiden zivile Verluste. Hinnehmbar."/>
	<entry name="AdeptusMechanicus/Headquarters:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#0" value="Eine Revolte wird unterdrückt."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#1" value="Klone werden zu den Horten der Protoservitoren geschickt."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#2" value="Einkehr-Habitate im Namen des Omnissiah gesäubert."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#3" value="Bionics-Produktion um 0,1% erhöht."/>
	<entry name="AdeptusMechanicus/Headquarters:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/Headquarters:Shaken#0" value="Die Massen sind verängstigt. Profosse werden eingesetzt."/>
	<entry name="AdeptusMechanicus/Headquarters:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Victory#0" value="Externe Bedrohungen beseitigt. Fokus wird auf Dissidenten gerichtet."/>
	<entry name="AdeptusMechanicus/Headquarters:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="AdeptusMechanicus/ArchaeopterTransvector"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="AdeptusMechanicus/IronstriderBallistarius"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="AdeptusMechanicus/IronstriderBallistarius"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="AdeptusMechanicus/SkitariiVanguard"/>
	<entry name="AstraMilitarum/Baneblade:AlliedFaction:SpaceMarines#0" value="Space Marines gesichtet, Kommandeur. Die Funkkanäle sind offen."/>
	<entry name="AstraMilitarum/Baneblade:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Lord Commissar gesichtet. Wir bringen ihn direkt an die Front."/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/TankCommander#0" value="Sir, freut mich, einen Offizier zu sehen, der den Nutzen von Panzern erkennt, Sir!"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/TankCommanderCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Artefact#0" value="Xenos-Konstrukt gesichtet."/>
	<entry name="AstraMilitarum/Baneblade:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Attack#0" value="Feuerlösung berechnet."/>
	<entry name="AstraMilitarum/Baneblade:Attack#1" value="Ziel erfasst."/>
	<entry name="AstraMilitarum/Baneblade:Attack#2" value="Rohre 1 bis 11 werden abgefeuert, Sir."/>
	<entry name="AstraMilitarum/Baneblade:AttackCount" value="3"/>
	<entry name="AstraMilitarum/Baneblade:Broken#0" value="Zeit für den Rückwärtsgang, Sir."/>
	<entry name="AstraMilitarum/Baneblade:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Cover#0" value="Ich weiß zwar nicht, wie wir das geschafft haben, aber der Panzer hat Deckung, Sir."/>
	<entry name="AstraMilitarum/Baneblade:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:AstraMilitarum#0" value="Verräter gesichtet, Sir!"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:Necrons#0" value="Xenos gesichtet, Sir. Es wird Zeit, ihnen mal ein richtiges Monster aus Metall zu zeigen."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:Orks#0" value="Grünhäute sind in der Gegend, Sir. Aus uns werden die keinen Kopphamma machen!"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:SpaceMarines#0" value="Feindliche Adeptus Astartes gesichtet. Keine Fellblades zu sehen."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyUnit:Neutral/Enslaver#0" value="Sir, ein schneller Rückzug wäre anzuraten. Wir können nicht garantieren, dass dieses Fahrzeug unbeschadet bleibt."/>
	<entry name="AstraMilitarum/Baneblade:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Hurt#0" value="Wir haben ganz schön was abgekriegt, Sir. Wir sollten für Reparaturarbeiten zum Mars zurückkehren."/>
	<entry name="AstraMilitarum/Baneblade:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Idle#0" value="Die Ketten dürfen nicht rosten, Sir."/>
	<entry name="AstraMilitarum/Baneblade:Idle#1" value="Wir überprüfen alle elf Geschützrohre für ein Höllenfeuer, Sir."/>
	<entry name="AstraMilitarum/Baneblade:Idle#2" value="Der Feind ist schwach, Sir. Und wir werden ihm den letzten Rest geben."/>
	<entry name="AstraMilitarum/Baneblade:Idle#3" value="Wir zählen die Nieten, Sir."/>
	<entry name="AstraMilitarum/Baneblade:Idle#4" value="Wenn ich groß bin, will ich einen Hellhammer steuern, Sir."/>
	<entry name="AstraMilitarum/Baneblade:Idle#5" value="Haben Sie gehört, dass Vance Stubbs einhundert Baneblades verloren hat, Sir? Ganz schön verschwenderisch."/>
	<entry name="AstraMilitarum/Baneblade:IdleCount" value="6"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory0#0" value="Bereit, Gladius Primus zurückzuerobern, Sir."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory0Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory1#0" value="Die Soldaten sammeln sich, Sir."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory1Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory2#0" value="Sir, bei allem Respekt, dieser Techpriester eignet sich nicht dazu, unsere Seitenkuppeln zu ölen."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory2Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory3#0" value="Erkundungstouren unter der Erde überlassen wir der Infanterie, Sir."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory3Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory4#0" value="Wir können uns die Xenos vom Leib halten, kein Problem, Sir."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory4Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory5#0" value="Kastelans! Wir brauchen alle elf Rohre, Sir!"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory5Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Shaken#0" value="Kein Rückzug, Sir! Ich bin mir nicht einmal sicher, ob man mit dem Ding überhaupt rückwärtsfahren kann."/>
	<entry name="AstraMilitarum/Baneblade:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Victory#0" value="Ziel eliminiert, Sir. Alle Geschützrohre werden nachgeladen."/>
	<entry name="AstraMilitarum/Baneblade:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:WireWeed#0" value="Sir, wir sollten woandershin fahren. Wir kommen mit dem Stahlkraut nicht klar."/>
	<entry name="AstraMilitarum/Baneblade:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:AlliedFaction:SpaceMarines#0" value="Verbündete Astartes gesichtet, preiset den Imperator!"/>
	<entry name="AstraMilitarum/Basilisk:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Commissar gesichtet. Gut, dass er an der Frontlinie ist… und somit weit weg von uns."/>
	<entry name="AstraMilitarum/Basilisk:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Artefact#0" value="Geschosse können dem Ding nichts anhaben. Woraus besteht es?"/>
	<entry name="AstraMilitarum/Basilisk:Artefact#1" value="Nur vom Hinsehen fühlt man sich schon geläutert."/>
	<entry name="AstraMilitarum/Basilisk:ArtefactCount" value="2"/>
	<entry name="AstraMilitarum/Basilisk:Attack#0" value="Koordinaten erhalten, Feuerlösung berechnet. Angriff!"/>
	<entry name="AstraMilitarum/Basilisk:Attack#1" value="Die Geschosse sind unterwegs."/>
	<entry name="AstraMilitarum/Basilisk:Attack#2" value="Beobachter auf dem Schlachtfeld."/>
	<entry name="AstraMilitarum/Basilisk:Attack#3" value="Das Ziel wird beschossen. Gut, dass wir hier hinten sind!"/>
	<entry name="AstraMilitarum/Basilisk:AttackCount" value="4"/>
	<entry name="AstraMilitarum/Basilisk:Broken#0" value="Lasst alles stehen und liegen und lauft!"/>
	<entry name="AstraMilitarum/Basilisk:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Cover#0" value="Eine perfekte Geschützstellung. So werden wir sie kalt erwischen."/>
	<entry name="AstraMilitarum/Basilisk:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:AstraMilitarum#0" value="Für Verräter haben wir immer etwas parat – 132mm geballte Wut des Imperators."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:Necrons#0" value="Todlose Zinnsoldaten? 15 Kilometer dürften die richtige Distanz sein, um es mit ihnen aufzunehmen."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:Orks#0" value="Ah, Grünhäute. Genau dafür wurde diese Kanone gebaut."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:SpaceMarines#0" value="Abtrünnige Marines? Ob ihre Halosterne sie vor hochexplosiven Geschossen schützen?"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyUnit:Neutral/Enslaver#0" value="Was sind das für DINGER? Haltet sie auf Abstand, Männer."/>
	<entry name="AstraMilitarum/Basilisk:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Hurt#0" value="Innenraum getroffen… Hier drinnen herrscht das reinste Chaos…"/>
	<entry name="AstraMilitarum/Basilisk:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Idle#0" value="Wissen Sie, wie das erste Gebot des Basilisk-Gottes lautet? Du sollst dich aus dem Gröbsten raushalten."/>
	<entry name="AstraMilitarum/Basilisk:Idle#1" value="Schicken Sie bloß keinen Manticore los, um unsere Arbeit zu machen."/>
	<entry name="AstraMilitarum/Basilisk:Idle#2" value="Die Kerle an vorderster Front tun mir leid – aber nicht so sehr, dass ich mich zu ihnen gesellen würde…"/>
	<entry name="AstraMilitarum/Basilisk:Idle#3" value="Sagen Sie uns einfach Bescheid, wenn wir das Feuer eröffnen sollen."/>
	<entry name="AstraMilitarum/Basilisk:Idle#4" value="Im Krieg passiert einfach nie etwas… Und das passiert andauernd!"/>
	<entry name="AstraMilitarum/Basilisk:IdleCount" value="5"/>
	<entry name="AstraMilitarum/Basilisk:Shaken#0" value="Schießt da etwa jemand… auf uns?!"/>
	<entry name="AstraMilitarum/Basilisk:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Victory#0" value="Wir haben den Feind in Krater verwandelt! Die werden in zehntausend Jahren noch da sein!"/>
	<entry name="AstraMilitarum/Basilisk:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:WireWeed#0" value="Stahlkraut? Na ja, es gibt bestimmt schlimmere Geschützstellungen – den Großen Riss, zum Beispiel…"/>
	<entry name="AstraMilitarum/Basilisk:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedFaction:SpaceMarines#0" value="Marines, Anführer!"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Bullgryns sind loyal."/>
	<entry name="AstraMilitarum/Bullgryn:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Artefact#0" value="Großes Ding?"/>
	<entry name="AstraMilitarum/Bullgryn:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Attack#0" value="Bullgryns Kämpfer, du Anführer."/>
	<entry name="AstraMilitarum/Bullgryn:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Broken#0" value="Ogryns sind nicht so zäh."/>
	<entry name="AstraMilitarum/Bullgryn:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Cover#0" value="Gebüsch is' gutes Versteck."/>
	<entry name="AstraMilitarum/Bullgryn:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:AstraMilitarum#0" value="Äh, wer kämpft für'n Imperator? Wir oder die?"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:Necrons#0" value="Kann Blech nicht kauen. Mist."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:Orks#0" value="Orks. Stark, prächtig, klug. Wie wir."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:SpaceMarines#0" value="Die Kinder des Imperators? Gegen die soll'n wir kämpfen?!"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyUnit:Neutral/Enslaver#0" value="Weiß nicht, was das für Dinger sind."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Hurt#0" value="Au."/>
	<entry name="AstraMilitarum/Bullgryn:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#0" value="Knochenköpfe klug…"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#1" value="Ein Ogryn auf Brust von totem Ork. Uff… zwei! Zwei Ogryns…"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#2" value="Wir mach'n unsre Knüppel scharf, Anführer."/>
	<entry name="AstraMilitarum/Bullgryn:Idle#3" value="Was du sagst, mach'n wir. Aber du sagst ja nix."/>
	<entry name="AstraMilitarum/Bullgryn:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Bullgryn:Shaken#0" value="Warte… Wohin geh'n wir denn?"/>
	<entry name="AstraMilitarum/Bullgryn:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Victory#0" value="Was? Ham wir gewonnen?"/>
	<entry name="AstraMilitarum/Bullgryn:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:WireWeed#0" value="Ah, mein Bein!"/>
	<entry name="AstraMilitarum/Bullgryn:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:AlliedFaction:SpaceMarines#0" value="Dem Imperator sei Dank, die Astartes sind da!"/>
	<entry name="AstraMilitarum/Guardsman:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Brust raus, Bauch rein, Jungs, der Lord Commissar ist da."/>
	<entry name="AstraMilitarum/Guardsman:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Artefact#0" value="Wurde das von… Xenos gebaut?"/>
	<entry name="AstraMilitarum/Guardsman:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Attack#0" value="Kann jemand sehen, auf was wir eigentlich schießen?"/>
	<entry name="AstraMilitarum/Guardsman:Attack#1" value="Vor uns der Feind, hinter uns der Commissar…"/>
	<entry name="AstraMilitarum/Guardsman:Attack#2" value="Gebt alles!"/>
	<entry name="AstraMilitarum/Guardsman:Attack#3" value="Ich wünschte, Creed wäre hier…"/>
	<entry name="AstraMilitarum/Guardsman:Attack#4" value="Aktiviert die Laser, wir können es schaffen."/>
	<entry name="AstraMilitarum/Guardsman:AttackCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Broken#0" value="Dafür habe ich mich nicht rekrutieren lassen!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#1" value="Lauft, Jungs, und betet, dass der Commissar euch nicht sieht!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#2" value="Die bringen uns um! Und wir können nichts gegen sie ausrichten!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#3" value="Das ist eine verfluchte Todeswelt!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#4" value="Wo ist die Verstärkung?! Wo ist--"/>
	<entry name="AstraMilitarum/Guardsman:BrokenCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Cover#0" value="Wieder in Deckung, dem Imperator sei Dank."/>
	<entry name="AstraMilitarum/Guardsman:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/Enslaver#0" value="Enslavers?! Oh Imperator, beschütze uns!"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/Psychneuein#0" value="Große Käfer von Prospero… Sie müssen von den Psionikern ferngehalten werden!"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnitNeutral/KhorneLordOfSkulls#0" value="Das Chaos! Hier?"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnitNeutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Hurt#0" value="Wir sind erledigt, Sir."/>
	<entry name="AstraMilitarum/Guardsman:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Idle#0" value="Beim Goldenen Thron…!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#1" value="Für Guillaume und den Imperator!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#2" value="Wie hoch, Sir?"/>
	<entry name="AstraMilitarum/Guardsman:Idle#3" value="Die trügerische Stille vor der Schlacht ist das Schlimmste."/>
	<entry name="AstraMilitarum/Guardsman:Idle#4" value="Wir spielen nur ein bisschen, Sir. Der Commissar meinte, das sei gut für die Moral."/>
	<entry name="AstraMilitarum/Guardsman:Idle#5" value="Das Leben in der Armee ist wirklich hart, Sir."/>
	<entry name="AstraMilitarum/Guardsman:Idle#6" value="Ich höre Sie laut und deutlich, Sir!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#7" value="Sir, jawohl, Sir!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#8" value="Achtung, der Commissar kommt!"/>
	<entry name="AstraMilitarum/Guardsman:IdleCount" value="9"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory0#0" value="Ich kann immer noch nicht glauben, dass wir überlebt haben. So viele…"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory0Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory1#0" value="Die Truppe rauft sich wieder zusammen, Sir."/>
	<entry name="AstraMilitarum/Guardsman:QuestStory1Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory2#0" value="Eine neue Stadt? Ist das wirklich… Nein, Sir, ich stelle Ihre Befehle nicht infrage, Sir!"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory2Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory3#0" value="Ich habe Kopfschmerzen von diesen Stürmen. Sitzen wir hier jetzt für immer fest?"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory3Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory4#0" value="Sie sind in der Stadt! Zurück!"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory4Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory5#0" value="Mutter sagte immer, dass man einem Techpriester nicht trauen kann. Wie recht sie doch hatte…"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory5Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#0" value="Können wir beten? Oh Imperator, oh Guillaume, beschützt uns."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#1" value="Kommandeur, wir ziehen uns zurück."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#2" value="Nur Mut, Jungs, nur Mut!"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#3" value="Wenn wir bleiben, erschießt uns der Feind. Wenn wir weglaufen, erschießt uns der Commissar!"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#4" value="Können wir vielleicht bessere Schusswaffen bekommen?"/>
	<entry name="AstraMilitarum/Guardsman:ShakenCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Victory#0" value="Commissar, Sir, dürfen wir feiern?"/>
	<entry name="AstraMilitarum/Guardsman:Victory#1" value="Ha! Fühlt sich so an, als hätten wir einen Titan mit einer Schleuder bezwungen."/>
	<entry name="AstraMilitarum/Guardsman:Victory#2" value="In Gedenken an Cadia und Creed…"/>
	<entry name="AstraMilitarum/Guardsman:Victory#3" value="Wir haben gewonnen…? Wir haben gewonnen!"/>
	<entry name="AstraMilitarum/Guardsman:Victory#4" value="Oh, der Leitfaden hat recht behalten! Diese Xenos sind tatsächlich keine Herausforderung!"/>
	<entry name="AstraMilitarum/Guardsman:VictoryCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:WireWeed#0" value="Sir, das Stahlkraut… Es lebt!"/>
	<entry name="AstraMilitarum/Guardsman:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:AlliedFaction:SpaceMarines#0" value="Kommandeur, das Hauptquartier hat die verbündeten Space Marines gesichtet."/>
	<entry name="AstraMilitarum/Headquarters:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Freut mich, Sie auf dem Schlachtfeld zu sehen, Sir."/>
	<entry name="AstraMilitarum/Headquarters:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Artefact#0" value="Ein Xenos-Artefakt befindet sich in gefährlicher Nähe zur Stadt, Sir."/>
	<entry name="AstraMilitarum/Headquarters:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Attack#0" value="Territoriale Kontrolle wird aufrechterhalten."/>
	<entry name="AstraMilitarum/Headquarters:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Broken#0" value="Befehle für das letzte Gefecht erteilt."/>
	<entry name="AstraMilitarum/Headquarters:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Cover#0" value="Wie soll eine… Stadt… in Deckung gehen?"/>
	<entry name="AstraMilitarum/Headquarters:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:AstraMilitarum#0" value="Verräter nähern sich der Stadt, Sir!"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:Necrons#0" value="Truppen der Necrons im Anmarsch."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:Orks#0" value="Grünhäute rücken uns auf die Pelle, Sir."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:SpaceMarines#0" value="Space Marines kommen näher. Wenn sie auf die andere Seite der Mauern gelangen, werden sie die Stadt auslöschen."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyUnit:Neutral/Enslaver#0" value="ACHTUNG! EXTREM GEFÄHRLICHE XENOS GESICHTET."/>
	<entry name="AstraMilitarum/Headquarters:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Hurt#0" value="Schaden an der Infrastruktur!"/>
	<entry name="AstraMilitarum/Headquarters:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Idle#0" value="Soldaten im Dienst."/>
	<entry name="AstraMilitarum/Headquarters:Idle#1" value="Wir sind stets auf der Hut."/>
	<entry name="AstraMilitarum/Headquarters:Idle#2" value="Wer hantiert da mit Tarotkarten herum? Weg damit."/>
	<entry name="AstraMilitarum/Headquarters:Idle#3" value="Alles ruhig, Sir."/>
	<entry name="AstraMilitarum/Headquarters:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Headquarters:Shaken#0" value="Rückzug zu Verteidigungsstellungen, Sir."/>
	<entry name="AstraMilitarum/Headquarters:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Victory#0" value="Stadt gesichert, Sir."/>
	<entry name="AstraMilitarum/Headquarters:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:WireWeed#0" value="Ob es eine gute Idee war, die Stadt auf Stahlkraut zu errichten…?"/>
	<entry name="AstraMilitarum/Headquarters:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedFaction:SpaceMarines#0" value="Gut, dass die Astartes aufgetaucht sind."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Commissars sieht man nur gerne, wenn sie weilen in der Ferne…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Artefact#0" value="Was man nicht unter Beschuss nehmen kann, interessiert mich nicht."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#0" value="Kann jemand sehen, ob das Ziel noch da ist?"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#1" value="Beim Thron des Imperators, verdammter Rückstoß!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#2" value="Wir fahren schwere Geschütze auf!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#3" value="Die knacken wir!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#4" value="Wenn Laser nichts bewirken…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AttackCount" value="5"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Broken#0" value="Zurück, ihr Hunde, zurück!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Cover#0" value="Wie sagt man auf dem Planeten Krieg: Wenn du deine Mörser beschützt, beschützen sie dich."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:CoverCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:AstraMilitarum#0" value="Diese verfluchten Verräter. Heizt ihnen ordentlich ein!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:Necrons#0" value="Ziehen wir ihnen ihre Blechhaut ab."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:Orks#0" value="Auf Grünhäute zu feuern ist Munitionsverschwendung."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:SpaceMarines#0" value="Wenn man schon auf einen Space Marine feuern muss, sollte man ihn auch wirklich treffen."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyUnit:Neutral/Enslaver#0" value="Schießt diese Höllenbrut zurück in den Warp!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Hurt#0" value="Zum Bluten habe ich jetzt wirklich keine Zeit!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:HurtCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#0" value="Wissen Sie, wie lange es dauert, Bajonette auf Laserkanonen zu montieren?"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#1" value="Der Xenos-Abschaum soll ruhig kommen…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#2" value="Irgendwo da draußen erwartet man uns…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#3" value="Wir sind wie ein Panzer – der jedoch das Ziel treffen kann."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:IdleCount" value="4"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Shaken#0" value="Eigentlich sollten wir nicht unter Beschuss geraten, Sir."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Victory#0" value="Tot, natürlich."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:WireWeed#0" value="Verfluchte Xenos-Fallen."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:AlliedFaction:SpaceMarines#0" value="Die Engel sind mit uns."/>
	<entry name="AstraMilitarum/Hydra:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Es sollte immer jemand da sein, der die Disziplin aufrechterhält."/>
	<entry name="AstraMilitarum/Hydra:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Artefact#0" value="Etwas Fremdartiges."/>
	<entry name="AstraMilitarum/Hydra:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Attack#0" value="Wir halten den Himmel sauber."/>
	<entry name="AstraMilitarum/Hydra:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Broken#0" value="Wir ziehen uns zurück."/>
	<entry name="AstraMilitarum/Hydra:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Cover#0" value="Ein perfekter Ort, um seelenruhig den Himmel zu beobachten."/>
	<entry name="AstraMilitarum/Hydra:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:AstraMilitarum#0" value="Imperiale, aber mit vernebelten Sinnen."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:Necrons#0" value="Solch fremdartige, todbringende Flugmaschinen…"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:Orks#0" value="Die Flugmaschinen der Grünhäute sind technisch gesehen primitiv, aber effektiv."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:SpaceMarines#0" value="Ob wir wohl Thunderhawks vom Himmel holen können? Finden wir es heraus!"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyUnit:Neutral/Enslaver#0" value="Schweben ist nicht das Gleiche wie fliegen, ihr Xenos-Abscheulichkeiten!"/>
	<entry name="AstraMilitarum/Hydra:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Hurt#0" value="Wir behalten den Himmel im Auge, aber dafür müssen Sie UNS im Auge behalten!"/>
	<entry name="AstraMilitarum/Hydra:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Idle#0" value="Wir beobachten die Wolken, Sir."/>
	<entry name="AstraMilitarum/Hydra:Idle#1" value="Jaghatai-Regenwolken, Rogal-Schichtwolken, Cirrosanguinius-Wolken…"/>
	<entry name="AstraMilitarum/Hydra:Idle#2" value="Die Sterne sehen von hier aus atemberaubend aus."/>
	<entry name="AstraMilitarum/Hydra:Idle#3" value="Nie habe ich so schöne Sonnenuntergänge gesehen wie auf diesem Planeten, Sir."/>
	<entry name="AstraMilitarum/Hydra:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Hydra:Shaken#0" value="Jemand hat es auf uns abgesehen, Sir."/>
	<entry name="AstraMilitarum/Hydra:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Victory#0" value="Nichts am Himmel zu sehen, Sir."/>
	<entry name="AstraMilitarum/Hydra:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:WireWeed#0" value="Die tückischen Fänge der hiesigen Flora, Sir."/>
	<entry name="AstraMilitarum/Hydra:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedFaction:SpaceMarines#0" value="Befestigte Stellung meldet Einheiten des Adeptus Astartes in der Gegend. Sie sind uns nicht feindlich gesinnt."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/Baneblade#0" value="Es ist immer wieder aufregend, einen Baneblade zu sehen, Sir – vor allem, wenn die Kanonen in die andere Richtung zeigen."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/BanebladeCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Befestigte Stellung bleibt standhaft, Sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Artefact#0" value="Befestigte Stellung meldet Xenos-Artefakt in der unmittelbaren Umgebung."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Attack#0" value="Befestigte Stellung fährt militärisches Gerät auf."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AttackCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Broken#0" value="Befestigte Stellung verloren, ich wiederhole, befestigte Stellung verloren."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:AstraMilitarum#0" value="Befestigte Stellung meldet Truppenbewegungen von Verrätern in der näheren Umgebung."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:Necrons#0" value="Befestigte Stellung meldet Truppenbewegungen der Necrons."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:Orks#0" value="Befestigte Stellung meldet Präsenz von Orks."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:SpaceMarines#0" value="Befestigte Stellung meldet… der Imperator schütze uns… feindliche Truppen des Adeptus Astartes."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyUnit:Neutral/Enslaver#0" value="Befestigte Stellung meldet Xenos… Enslavers, Sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Hurt#0" value="Befestigte Stellung meldet… Ich weiß nicht, ob wir das überstehen, Sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:HurtCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#0" value="Wir halten die Augen offen, Sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#1" value="Immer wachsam."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#2" value="Befestigte Stellung meldet… nichts."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#3" value="Keine Vorkommnisse, Sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:IdleCount" value="4"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Shaken#0" value="Sir, können Sie uns hören? Wir stehen unter Beschuss."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Victory#0" value="Befestigte Stellung hielt dem Feind stand, Sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:WireWeed#0" value="Stahlkraut macht der Kommandozentrale zu schaffen, Sir."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedFaction:SpaceMarines#0" value="Die Marines mischen jetzt auch mit! Gute Jungs…"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Wieder ein feiner Herr, der zu Fuß unterwegs ist. Warum hat eigentlich nur Yarrick seinen eigenen Baneblade bekommen?"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Artefact#0" value="Das Artefakt bewachen? Mit Vergnügen."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Attack#0" value="Wir schicken ein paar nette Geschosse in ihre Richtung, Sir."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AttackCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Broken#0" value="Wir müssen uns leider zurückziehen, Sir."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Cover#0" value="Wir machen uns in Deckung für einen Hinterhalt bereit, Sir."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:CoverCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:AstraMilitarum#0" value="Noch vor Ende des Tages rollen wir über Häretikerknochen…"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:Necrons#0" value="Ah, Necrons… Die knöpfen wir uns am liebsten vor."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:Orks#0" value="Bereit zur Selbstzerstörung – Orks lieben es, sich Leman Russ Panzer zu krallen."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:SpaceMarines#0" value="Diese Kanone ist dazu da, Marines zum Weinen zu bringen."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyUnit:Neutral/Enslaver#0" value="Feindliche Xenos… Haltet sie auf Abstand."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Hurt#0" value="Wow, glatter Durchschlag!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:HurtCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#0" value="Konnte Leman Russ überhaupt fahren?"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#1" value="Wir ölen die Ketten."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#2" value="Wir tanken."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#3" value="Ich wünschte, Pask wäre der Kommandant."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:IdleCount" value="4"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Shaken#0" value="Panzerbesatzung in Schockstarre!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Victory#0" value="Ein weiterer Sieg für den berühmtesten Panzer der Galaxie!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:WireWeed#0" value="Es gibt nur eine Sache, die berüchtigter ist als der Leman Russ: Stahlkraut."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedFaction:SpaceMarines#0" value="Loyale Astartes sieht man immer gerne, selbst als Überraschungsgäste."/>
	<entry name="AstraMilitarum/LordCommissar:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Ah, ein Kampfgenosse."/>
	<entry name="AstraMilitarum/LordCommissar:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Artefact#0" value="Xenos-Unrat, den man verbrennen sollte."/>
	<entry name="AstraMilitarum/LordCommissar:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Attack#0" value="Erschießt sie oder ich erschieße euch!"/>
	<entry name="AstraMilitarum/LordCommissar:AttackCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Broken#0" value="Die Männer dürfen mich so nicht sehen!"/>
	<entry name="AstraMilitarum/LordCommissar:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Cover#0" value="Aus taktischer Sicht klug, Deckung zu suchen, aber der Moral nicht zuträglich."/>
	<entry name="AstraMilitarum/LordCommissar:CoverCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:AstraMilitarum#0" value="Verräter! Abschaum! DAS IMPERIUM WIRD EUCH EURER GERECHTEN STRAFE ZUFÜHREN!"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:Necrons#0" value="Abschaum aus Blech… Schmelzen wir sie ein, für mehr Kugeln!"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:Orks#0" value="Dieses grünhäutige Pack… Mit imperialer Präzisionsarbeit sollten wir es schnell erledigen können."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:SpaceMarines#0" value="Abtrünnige Halunken, einst der größte Stolz unseres Imperators… Wir müssen sie vom Antlitz der Galaxie tilgen."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyUnit:Neutral/Enslaver#0" value="Xenos-Schrecken, in meinem Kopf!"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Hurt#0" value="Ist nur eine Fleischwunde…"/>
	<entry name="AstraMilitarum/LordCommissar:HurtCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#0" value="Für Recht und Ordnung zu sorgen kann ganz schön ermüdend sein."/>
	<entry name="AstraMilitarum/LordCommissar:Idle#1" value="Müßiggang ist aller Laster Anfang."/>
	<entry name="AstraMilitarum/LordCommissar:Idle#2" value="Die Rationen wurden halbiert, aber es kann nicht schaden, etwas auf unsere Linie zu achten…"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#3" value="Was würde Cain tun?"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#4" value="Adjutant! Machen Sie das noch einmal sauber!"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#5" value="Tun Sie Ihre Pflicht, im Namen von… Oh, den Teil vergesse ich immer."/>
	<entry name="AstraMilitarum/LordCommissar:IdleCount" value="6"/>
	<entry name="AstraMilitarum/LordCommissar:Shaken#0" value="A spiritu dominatus, Domine, libra nos."/>
	<entry name="AstraMilitarum/LordCommissar:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Victory#0" value="Ich bin für meine Truppen mit gutem Beispiel vorangegangen."/>
	<entry name="AstraMilitarum/LordCommissar:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:WireWeed#0" value="Es wäre tapfer, aber auch töricht, länger im Stahlkraut auszuharren."/>
	<entry name="AstraMilitarum/LordCommissar:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedFaction:SpaceMarines#0" value="Da sind ein paar verbündete Astartes."/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedUnit:AstraMilitarum/Thunderbolt#0" value="Gut, dass wir Jägerunterstützung haben."/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedUnit:AstraMilitarum/ThunderboltCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Artefact#0" value="Wichtiges Ziel gesichtet."/>
	<entry name="AstraMilitarum/MarauderBomber:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#0" value="Bomben abgeworfen."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#1" value="Wenn Sie sie sehen, können wir sie treffen."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#2" value="Wir öffnen die Bombenschachtklappen."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#3" value="Alles unter dem Bombenzielgerät ist Freiwild."/>
	<entry name="AstraMilitarum/MarauderBomber:AttackCount" value="4"/>
	<entry name="AstraMilitarum/MarauderBomber:Broken#0" value="Wir gehen runter!"/>
	<entry name="AstraMilitarum/MarauderBomber:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Cover#0" value="Wenn wir noch tiefer fliegen, müssen wir Gras fressen."/>
	<entry name="AstraMilitarum/MarauderBomber:CoverCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Hurt#0" value="Drei Triebwerke sind ausgefallen, wir haben mehr Löcher als der Grox eines Händlers, der Vox-Transmitter ist hinüber und wir verlieren Treibstoff…"/>
	<entry name="AstraMilitarum/MarauderBomber:HurtCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#0" value="Ich bin nicht jemand, der große Reden schwingt…"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#1" value="Ich schwöre, Carpenter, die Bomben reden mit mir!"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#2" value="Weiß jemand, wie man dieses Ding überhaupt landet?"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#3" value="Wir machen einen Übungsflug… So wie es der Pilotenleitfaden vorsieht."/>
	<entry name="AstraMilitarum/MarauderBomber:IdleCount" value="4"/>
	<entry name="AstraMilitarum/MarauderBomber:Shaken#0" value="Wir gehen höher, Flugabwehrfeuer auf allen Seiten!"/>
	<entry name="AstraMilitarum/MarauderBomber:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Victory#0" value="Wo können wir als Nächstes zuschlagen?"/>
	<entry name="AstraMilitarum/MarauderBomber:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedFaction:SpaceMarines#0" value="Ich spüre… das Gute in diesen Truppen."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Ein äußerst herrischer Geist… faszinierend."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Artefact#0" value="Dieses nette Spielzeug wurde vor langer Zeit erschaffen."/>
	<entry name="AstraMilitarum/PrimarisPsyker:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#0" value="Euer Geist wird brennen…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#1" value="Ich spüre, dass die Augen des Imperators auf mich gerichtet sind."/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#2" value="Ich werde euch in Seinem Namen verbrennen!"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AttackCount" value="3"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Broken#0" value="Sein Licht, ich nehme Sein Licht nicht mehr wahr!"/>
	<entry name="AstraMilitarum/PrimarisPsyker:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Cover#0" value="Ein kluger Geist hält sich im Verborgenen."/>
	<entry name="AstraMilitarum/PrimarisPsyker:CoverCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:AstraMilitarum#0" value="Zwei sich ähnelnde, und doch widersprüchliche Geister, wie es scheint."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:Necrons#0" value="Bei ihnen spüre ich nichts. Rein gar nichts."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:Orks#0" value="Ich spüre solch rohe, überschwängliche Emotionen…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:SpaceMarines#0" value="Selbstsicherheit und ein eiserner Wille – zumindest nach außen hin…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyUnit:Neutral/Enslaver#0" value="Ich kann den Hunger dieser Kreatur spüren – ihr Verlangen nach den Früchten unseres Geistes."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Hurt#0" value="Ist nur ein Kratzer."/>
	<entry name="AstraMilitarum/PrimarisPsyker:HurtCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#0" value="Selbst in diesen Stürmen kann ich noch einen Blick auf Sein Licht erhaschen."/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#1" value="Ich weiß, was Sie gerade denken…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#2" value="Ein untätiger Geist ist nutzlos…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:IdleCount" value="3"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Shaken#0" value="Lieber hier sterben als auf den Schwarzen Schiffen."/>
	<entry name="AstraMilitarum/PrimarisPsyker:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Victory#0" value="Wie es das Tarot des Imperators vorhergesehen hat."/>
	<entry name="AstraMilitarum/PrimarisPsyker:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:WireWeed#0" value="Hier gibt es Leben, wenn auch in Form einer sonderbaren und wenig einladenden Spezies."/>
	<entry name="AstraMilitarum/PrimarisPsyker:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedFaction:SpaceMarines#0" value="Große Schulterpolster, das sind die Marines!"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Kann ich bitte wieder zurück an die Front?"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Artefact#0" value="Atemberaubend, das sollten Sie sehen!"/>
	<entry name="AstraMilitarum/ScoutSentinel:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Attack#0" value="Ich feuere aus allen Rohren!"/>
	<entry name="AstraMilitarum/ScoutSentinel:AttackCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Broken#0" value="Ich nehme die Beine in die Hand!"/>
	<entry name="AstraMilitarum/ScoutSentinel:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Cover#0" value="Ich suche Deckung – hinter einem Baum oder so…"/>
	<entry name="AstraMilitarum/ScoutSentinel:CoverCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:AstraMilitarum#0" value="Verflucht, das sind unsere – aber sie sind bööööse!"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:Necrons#0" value="Guten Tag, ihr Blechfratzen!"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:Orks#0" value="Ich sehe rote Augen, die mich nach verwertbaren Bauteilen mustern!"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:SpaceMarines#0" value="Was, wir sollen gegen die kämpfen? Sind wir etwa die Bösen?"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyUnit:Neutral/Enslaver#0" value="Uff… Was bei allen guten Geistern ist das denn?!"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Hurt#0" value="Oh, da hat mir jemand ein neues Fenster verpasst."/>
	<entry name="AstraMilitarum/ScoutSentinel:HurtCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#0" value="Sollte ich nicht… nun ja… irgendetwas tun?"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#1" value="Ich will nicht länger die Beine auf den Tisch legen!"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#2" value="Stellen Sie sich vor, was womöglich da draußen auf uns wartet!"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#3" value="Ich will endlich los!"/>
	<entry name="AstraMilitarum/ScoutSentinel:IdleCount" value="4"/>
	<entry name="AstraMilitarum/ScoutSentinel:Shaken#0" value="Holt mich da raus!"/>
	<entry name="AstraMilitarum/ScoutSentinel:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Victory#0" value="Ich wusste doch, dass wir siegen werden. Weiter!"/>
	<entry name="AstraMilitarum/ScoutSentinel:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:WireWeed#0" value="Ich muss hier weg, dieses Zeug geht ganz schön auf die Beine."/>
	<entry name="AstraMilitarum/ScoutSentinel:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:AlliedFaction:SpaceMarines#0" value="Wenn sie keine Land Raiders haben, bin ich raus aus der Nummer."/>
	<entry name="AstraMilitarum/TankCommander:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:AlliedUnit:AstraMilitarum/Baneblade#0" value="Ohhh… was für ein Prachtexemplar!"/>
	<entry name="AstraMilitarum/TankCommander:AlliedUnit:AstraMilitarum/BanebladeCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Artefact#0" value="Wenn das kein feindlicher Panzer ist, interessiert es mich nicht."/>
	<entry name="AstraMilitarum/TankCommander:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Attack#0" value="Bumm! Hahaha!"/>
	<entry name="AstraMilitarum/TankCommander:Attack#1" value="Ich muss näher ran! Sie wissen genau, warum…"/>
	<entry name="AstraMilitarum/TankCommander:Attack#2" value="Feuert das Geschütz ab! Und das andere auch! Feuert aus allen Rohren!"/>
	<entry name="AstraMilitarum/TankCommander:Attack#3" value="Feuer! Hahaha!"/>
	<entry name="AstraMilitarum/TankCommander:AttackCount" value="4"/>
	<entry name="AstraMilitarum/TankCommander:Broken#0" value="Nein, nicht den Rückwärtsgang! Wer hat hier das Sagen…?!"/>
	<entry name="AstraMilitarum/TankCommander:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Cover#0" value="Ein Hinterhalt? Nichts dagegen einzuwenden…"/>
	<entry name="AstraMilitarum/TankCommander:CoverCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:AstraMilitarum#0" value="Verräter! Häretiker… Haben sie Panzer?"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:Necrons#0" value="Monoliths, endlich mal eine Herausforderung."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:Orks#0" value="Mist, wir könnten jetzt einen Demolisher gut gebrauchen."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:SpaceMarines#0" value="Verräter! Vermutlich haben sie noch nie ein Rhino von vorne gesehen."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyUnit:Neutral/KastelanRobot#0" value="Oh, so eine Panzerung stellt man heute gar nicht mehr her."/>
	<entry name="AstraMilitarum/TankCommander:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Hurt#0" value="Ich halte sie auf Trab, indem ich sie Löcher in meine Panzerung schießen lasse."/>
	<entry name="AstraMilitarum/TankCommander:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Idle#0" value="Lieber bin ich gelangweilt als tot!"/>
	<entry name="AstraMilitarum/TankCommander:Idle#1" value="Ich poliere nur meine Orden."/>
	<entry name="AstraMilitarum/TankCommander:Idle#2" value="Ich soll reingehen? He, ich würde gerne was sehen!"/>
	<entry name="AstraMilitarum/TankCommander:Idle#3" value="Langsam juckt es mich in den Fingern."/>
	<entry name="AstraMilitarum/TankCommander:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TankCommander:Shaken#0" value="Ich bin mir nicht sicher, ob wir noch viel mehr einstecken können."/>
	<entry name="AstraMilitarum/TankCommander:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Victory#0" value="Wo der Feind hingehört? Unter die Ketten!"/>
	<entry name="AstraMilitarum/TankCommander:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:WireWeed#0" value="He, das Stahlkraut zerkratzt den Lack!"/>
	<entry name="AstraMilitarum/TankCommander:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Gut, dass mir dieser Fanatiker nichts zu sagen hat."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Artefact#0" value="Man fühlt sich geneigt, das Artefakt in seine Einzelteile zu zerlegen."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Attack#0" value="Auf in den Kampf…"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AttackCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Broken#0" value="Mein Fleisch ist schwach, ich muss mich zurückziehen."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Cover#0" value="Wahrscheinlichkeit eines feindlichen ballistischen Treffers enorm verringert."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:CoverCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:AstraMilitarum#0" value="Ein Ärgernis…"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:Necrons#0" value="Dem Äußeren nach inbrünstige Anhänger des Maschinengottes, aber in ihrem Inneren schäbige Xenos-Kreaturen."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:Orks#0" value="Primitive Geschöpfe. Sie dürfen unseren heiligen Maschinen nicht zu nahe kommen."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:SpaceMarines#0" value="Wirklich ein Jammer…"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/KastelanRobot#0" value="Ein heiliges Relikt! Haltet den Schaden so gering wie möglich."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/TechpriestBetrayer#0" value="Ein Häretekt? Hier? Unwahrscheinlich."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/TechpriestBetrayerCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Hurt#0" value="Reparaturen vonnöten…"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#0" value="Trage heilige Thermalschutzsalben auf."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#1" value="Überprüfe die Cogitatoren."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#2" value="Führe Maschinenriten durch."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#3" value="Erwecke Maschinengeister."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Shaken#0" value="Der Omnissiah steht mir bei."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Victory#0" value="Das war vorhersehbar."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:WireWeed#0" value="Stahlkraut ist so schön anzusehen. Wir müssen das reproduzieren."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion" value="AstraMilitarum/Guardsman"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedFaction:SpaceMarines#0" value="Da unten glänzt etwas!"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedUnit:AstraMilitarum/MarauderBomber#0" value="Wir von der fliegenden Zunft müssen zusammenhalten!"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedUnit:AstraMilitarum/MarauderBomberCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Artefact#0" value="Hat jemand gesehen, was das war? Wow!"/>
	<entry name="AstraMilitarum/Thunderbolt:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Attack#0" value="Nur kurze Feuerstöße! Wir müssen Munition sparen!"/>
	<entry name="AstraMilitarum/Thunderbolt:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Broken#0" value="Ich fliege zurück."/>
	<entry name="AstraMilitarum/Thunderbolt:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Cover#0" value="Nur die Wolken bieten hier oben Deckung."/>
	<entry name="AstraMilitarum/Thunderbolt:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:AstraMilitarum#0" value="Verräter. Ich glaube, hier gibt es ordentlich zu tun."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:Orks#0" value="Orks. Sind erst dann mit sich und der Welt zufrieden, wenn sie brennend am Boden liegen."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:SpaceMarines#0" value="Marines… Verbringen mehr Zeit damit, ihre Panzerung zu polieren, als zu kämpfen."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyUnit:Neutral/Psychneuein#0" value="Wer hat Lust, Bienen auszuräuchern?"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Hurt#0" value="Hmm, fressen oder gefressen werden…"/>
	<entry name="AstraMilitarum/Thunderbolt:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#0" value="Ich würde gerne dem Feind Feuer unterm Hintern machen…"/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#1" value="Keine Zeit hier, großartig nachzudenken."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#2" value="Der Einsatz auf diesem Planeten fühlt sich an wie ein Wochenendausflug."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#3" value="Sie können sich gerne als Flügelmann versuchen."/>
	<entry name="AstraMilitarum/Thunderbolt:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Thunderbolt:Shaken#0" value="Ich sollte umkehren!"/>
	<entry name="AstraMilitarum/Thunderbolt:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Victory#0" value="Für einen Sieg reicht es nicht, nur galant durch die Lüfte zu schweben."/>
	<entry name="AstraMilitarum/Thunderbolt:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Artefact#0" value="Nein, das kann ich wirklich nicht mitnehmen."/>
	<entry name="AstraMilitarum/Valkyrie:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Attack#0" value="Ausladen unter Beschuss."/>
	<entry name="AstraMilitarum/Valkyrie:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Broken#0" value="Wir sind darauf ausgelegt, schnell zu kommen und dann auch wieder schnell zu verschwinden!"/>
	<entry name="AstraMilitarum/Valkyrie:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:AstraMilitarum#0" value="Gewöhnliche Truppen."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:Necrons#0" value="Ansturm durch Blechmenschen!"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:Orks#0" value="Grünhäute in der Landezone."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:SpaceMarines#0" value="Ich wurde nicht rekrutiert, um gegen Marines zu kämpfen!"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Hurt#0" value="Es ist heiß hier, ich wiederhole: heiß!"/>
	<entry name="AstraMilitarum/Valkyrie:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#0" value="Dürfen wir Lautsprecher anfordern?"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#1" value="Verdammt, wo ist hier was los?"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#2" value="Die Stille ist's, die einen umbringt."/>
	<entry name="AstraMilitarum/Valkyrie:Idle#3" value="Uns bringt niemand zum Schweigen."/>
	<entry name="AstraMilitarum/Valkyrie:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Valkyrie:Shaken#0" value="Wir gehören nicht an die Front! Holt uns hier raus!"/>
	<entry name="AstraMilitarum/Valkyrie:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Victory#0" value="Der Feind wurde bezwungen, ihr könnt uns jetzt von hier wegbringen."/>
	<entry name="AstraMilitarum/Valkyrie:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedFaction:SpaceMarines#0" value="Wir spüren einen neuen Mut in uns aufkeimen."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedUnit:AstraMilitarum/PrimarisPsyker#0" value="Unser Bruder von den Schwarzen Schiffen…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedUnit:AstraMilitarum/PrimarisPsykerCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Artefact#0" value="Das Allerheiligste der Unheiligen."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#0" value="Accelerando! Gemeinsam vernichten wir sie."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#1" value="Hier eine Codetta, ihrer jämmerlichen Existenz gewidmet."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#2" value="Als eine Macht frohlocken wir und siegen."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#3" value="Ein einfacher Seelensturm und sie werden in den Tod gesungen."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AttackCount" value="4"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Broken#0" value="Wir sind entzweit, wir sind verloren."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Cover#0" value="Abgeschirmt von den Augen, jedoch nicht vom Geiste."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:CoverCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:AstraMilitarum#0" value="Brüder im Geiste, jedoch der Sünde verfallen."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:Necrons#0" value="Nichts, da ist nichts."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:Orks#0" value="Ein anderer kollektiver Geist, jedoch fremdartig."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:SpaceMarines#0" value="Die unreinen Söhne des Imperators höchstselbst…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyUnit:Neutral/Psychneuein#0" value="Die Avatare der Fruchtbarkeit, die nur darauf warten, noch mehr Abscheulichkeiten zu gebären."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Hurt#0" value="Eine Dissonanz erklingt, wir driften auseinander."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:HurtCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#0" value="Sie entziehen sich unseren Blicken. Der Warp…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#1" value="So weit weg von Seinem Licht."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#2" value="Wir haben die Schwarzen Schiffe überlebt und werden auch das hier überleben."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#3" value="Der Planet flüstert uns zu."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:IdleCount" value="4"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Shaken#0" value="Unsere Harmonie weicht, unser Requiem rückt näher."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Victory#0" value="Welche Elegie soll man auf jene anstimmen, die wie Vieh zugrunde gehen?"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:WireWeed#0" value="Silberne Schlangen umranken und beißen uns."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:WireWeedCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Attack#0" value="Kampfprotokoll…"/>
	<entry name="Necrons/CanoptekScarab:AttackCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Broken#0" value="Schadenskontrolle…"/>
	<entry name="Necrons/CanoptekScarab:BrokenCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Hurt#0" value="Schaden erlitten…"/>
	<entry name="Necrons/CanoptekScarab:HurtCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Idle#0" value="Wartungsprotokoll läuft."/>
	<entry name="Necrons/CanoptekScarab:Idle#1" value="Energieverbrauch wird minimiert."/>
	<entry name="Necrons/CanoptekScarab:Idle#2" value="Schadensbehebung bei verbündeten Einheiten…"/>
	<entry name="Necrons/CanoptekScarab:Idle#3" value="Äonenlang mussten wir warten. Und wir werden weiterhin geduldig sein."/>
	<entry name="Necrons/CanoptekScarab:IdleCount" value="4"/>
	<entry name="Necrons/CanoptekScarab:Shaken#0" value="Rückzug empfohlen."/>
	<entry name="Necrons/CanoptekScarab:ShakenCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Victory#0" value="Eindringling aufgehalten."/>
	<entry name="Necrons/CanoptekScarab:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekSpyder" value="Necrons/CanoptekScarab"/>
	<entry name="Necrons/Cryptek:Attack#0" value="Stirb, Vieh."/>
	<entry name="Necrons/Cryptek:Attack#1" value="Die Physik selbst übt Verrat am Feind."/>
	<entry name="Necrons/Cryptek:Attack#2" value="Der Feind wird nie verstehen, was ihn getötet hat."/>
	<entry name="Necrons/Cryptek:AttackCount" value="3"/>
	<entry name="Necrons/Cryptek:Broken#0" value="Äonen des Lernens liegen hinter mir, und endlich habe ich auch gelernt, was Furcht ist."/>
	<entry name="Necrons/Cryptek:BrokenCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:AstraMilitarum#0" value="Sklavenrassen des alten Feindes."/>
	<entry name="Necrons/Cryptek:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:Necrons#0" value="Ein weiterer Rat? Vielleicht benötigt man meine Dienste…"/>
	<entry name="Necrons/Cryptek:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:Orks#0" value="Fleischgewordene Waffen. Erstaunlich."/>
	<entry name="Necrons/Cryptek:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:SpaceMarines#0" value="Sklaven, die etwas aus sich gemacht haben. Wenn wir sie sezieren, könnten wir viel lernen."/>
	<entry name="Necrons/Cryptek:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Cryptek:Hurt#0" value="Faszinierend, sie haben es geschafft, mich zu verletzen."/>
	<entry name="Necrons/Cryptek:HurtCount" value="1"/>
	<entry name="Necrons/Cryptek:Idle#0" value="Sektion oder Vivisektion? Die Frage aller Fragen."/>
	<entry name="Necrons/Cryptek:Idle#1" value="Erinnern Sie sich noch daran, wie es ist zu träumen? Ich schon."/>
	<entry name="Necrons/Cryptek:Idle#2" value="Ich werde Atome spalten, bis ich gebraucht werde."/>
	<entry name="Necrons/Cryptek:Idle#3" value="Es gibt noch so viel zu lernen, und doch muss ich untätig sein."/>
	<entry name="Necrons/Cryptek:IdleCount" value="4"/>
	<entry name="Necrons/Cryptek:Shaken#0" value="Vielleicht haben die Schöpfungen der Alten durchaus ihren Wert."/>
	<entry name="Necrons/Cryptek:ShakenCount" value="1"/>
	<entry name="Necrons/Cryptek:Victory#0" value="So endet alles Leben, das sich uns widersetzt."/>
	<entry name="Necrons/Cryptek:VictoryCount" value="1"/>
	<entry name="Necrons/DestroyerLord" value="Necrons/Lord"/>
	<entry name="Necrons/DoomScythe:Attack#0" value="Sinkflug für den Angriff…"/>
	<entry name="Necrons/DoomScythe:AttackCount" value="1"/>
	<entry name="Necrons/DoomScythe:Broken#0" value="Wir müssen von hier weg."/>
	<entry name="Necrons/DoomScythe:BrokenCount" value="1"/>
	<entry name="Necrons/DoomScythe:Hurt#0" value="Wie können sie es wagen…?"/>
	<entry name="Necrons/DoomScythe:HurtCount" value="1"/>
	<entry name="Necrons/DoomScythe:Idle#0" value="Ich drehe meine Runden."/>
	<entry name="Necrons/DoomScythe:Idle#1" value="Manöver."/>
	<entry name="Necrons/DoomScythe:Idle#2" value="Ich beobachte den Horizont."/>
	<entry name="Necrons/DoomScythe:Idle#3" value="Ich bin fokussiert."/>
	<entry name="Necrons/DoomScythe:IdleCount" value="4"/>
	<entry name="Necrons/DoomScythe:Shaken#0" value="Schaden?"/>
	<entry name="Necrons/DoomScythe:ShakenCount" value="1"/>
	<entry name="Necrons/DoomScythe:Victory#0" value="Wir haben unsere Feinde gedemütigt."/>
	<entry name="Necrons/DoomScythe:VictoryCount" value="1"/>
	<entry name="Necrons/DoomsdayArk" value="Necrons/AnnihilationBarge"/>
	<entry name="Necrons/Headquarters" value="Necrons/Monolith"/>
	<entry name="Necrons/HeavyDestroyer:Attack#0" value="Das Leben ist unser Feind!"/>
	<entry name="Necrons/HeavyDestroyer:AttackCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Broken#0" value="Der Tod ist unser Sieg!"/>
	<entry name="Necrons/HeavyDestroyer:BrokenCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:AstraMilitarum#0" value="Zermalmt das schwache Fleisch!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:Necrons#0" value="Ein stolzer Feind, der unserer Spezies angehört!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:Orks#0" value="Befreit die Galaxie von diesem pilzartigen Abschaum!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:SpaceMarines#0" value="Bauern auf dem Schachbrett des Gottimperators!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Hurt#0" value="Der Schmerz ist unsere treibende Kraft!"/>
	<entry name="Necrons/HeavyDestroyer:HurtCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Idle#0" value="Wir sind Nihilisten!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#1" value="Wir sind ewiger Hunger!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#2" value="Wir müssen uns verbessern, um zu zerstören!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#3" value="Warum ruhen wir, anstatt zu töten?!"/>
	<entry name="Necrons/HeavyDestroyer:IdleCount" value="4"/>
	<entry name="Necrons/HeavyDestroyer:Shaken#0" value="Warum sterben sie nicht schneller?!"/>
	<entry name="Necrons/HeavyDestroyer:ShakenCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Victory#0" value="Sie sind tot, aber das ist nicht genug!"/>
	<entry name="Necrons/HeavyDestroyer:VictoryCount" value="1"/>
	<entry name="Necrons/Immortal" value="Necrons/Warrior"/>
	<entry name="Necrons/Lord:Attack#0" value="Spürt die todlose Macht der Necrons!"/>
	<entry name="Necrons/Lord:Attack#1" value="Geht erhobenen Hauptes durch meine Hand zugrunde!"/>
	<entry name="Necrons/Lord:Attack#2" value="Wer wagt es, mich herauszufordern?"/>
	<entry name="Necrons/Lord:Attack#3" value="Ihr werdet lernen zu gehorchen!"/>
	<entry name="Necrons/Lord:AttackCount" value="4"/>
	<entry name="Necrons/Lord:Broken#0" value="Wo sind meine Immortals? Meine Wachen?"/>
	<entry name="Necrons/Lord:BrokenCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:AstraMilitarum#0" value="Eintagsfliegen… von der ersten Sekunde an dem Tod geweiht."/>
	<entry name="Necrons/Lord:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:Necrons#0" value="Welch ein Frevel! Rivalen, auf meiner Kronwelt?!"/>
	<entry name="Necrons/Lord:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:Orks#0" value="Primitive Rohlinge! Und doch herrschen sie über meine Galaxie."/>
	<entry name="Necrons/Lord:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:SpaceMarines#0" value="So jung und schon so weise. Aber sie werden nicht mehr lange leben."/>
	<entry name="Necrons/Lord:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Lord:Hurt#0" value="Sie haben meine Hülle versengt? Vernichtet sie!"/>
	<entry name="Necrons/Lord:HurtCount" value="1"/>
	<entry name="Necrons/Lord:Idle#0" value="Das Gewissen ist nur dazu da, um die Starken im Zaum zu halten."/>
	<entry name="Necrons/Lord:Idle#1" value="Die Zögerlichen werde ich als Letztes töten."/>
	<entry name="Necrons/Lord:Idle#2" value="Was werde ich als Erstes tun, wenn wir über diese Welt herrschen?"/>
	<entry name="Necrons/Lord:IdleCount" value="3"/>
	<entry name="Necrons/Lord:Shaken#0" value="Der Tod kann mir nichts anhaben."/>
	<entry name="Necrons/Lord:Shaken#1" value="Die Alten haben ihnen viel beigebracht."/>
	<entry name="Necrons/Lord:ShakenCount" value="2"/>
	<entry name="Necrons/Lord:Victory#0" value="Ein verzweifelter Tod…"/>
	<entry name="Necrons/Lord:VictoryCount" value="1"/>
	<entry name="Necrons/Monolith:Attack#0" value="Partikelgeschütz einsatzbereit."/>
	<entry name="Necrons/Monolith:AttackCount" value="1"/>
	<entry name="Necrons/Monolith:Broken#0" value="Rückzug… gemächlich."/>
	<entry name="Necrons/Monolith:BrokenCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:AstraMilitarum#0" value="Menschen gesichtet."/>
	<entry name="Necrons/Monolith:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:Necrons#0" value="Feindlicher Rat auf dem Planeten."/>
	<entry name="Necrons/Monolith:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:Orks#0" value="Orks gesichtet."/>
	<entry name="Necrons/Monolith:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:SpaceMarines#0" value="Menschen gesichtet. Weiterentwickelt."/>
	<entry name="Necrons/Monolith:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Monolith:Hurt#0" value="Panzerung durchschlagen, Besatzung zu Schaden gekommen."/>
	<entry name="Necrons/Monolith:HurtCount" value="1"/>
	<entry name="Necrons/Monolith:Idle#0" value="Kanalisiere Tor der Ewigkeit."/>
	<entry name="Necrons/Monolith:Idle#1" value="Erschaffe unergründliche Geheimnisse."/>
	<entry name="Necrons/Monolith:Idle#2" value="Zerlege Singulärwerte."/>
	<entry name="Necrons/Monolith:Idle#3" value="Rekonstruiere Elementarteilchen."/>
	<entry name="Necrons/Monolith:Idle#4" value="Archivdaten werden dechiffriert."/>
	<entry name="Necrons/Monolith:Idle#5" value="Retikuliere Netzalgorithmen."/>
	<entry name="Necrons/Monolith:IdleCount" value="6"/>
	<entry name="Necrons/Monolith:Shaken#0" value="Wir werden angegriffen. Gefahrensituation."/>
	<entry name="Necrons/Monolith:ShakenCount" value="1"/>
	<entry name="Necrons/Monolith:Victory#0" value="Feind eliminiert. Wie erwartet."/>
	<entry name="Necrons/Monolith:VictoryCount" value="1"/>
	<entry name="Necrons/NightScythe" value="Necrons/DoomScythe"/>
	<entry name="Necrons/Obelisk" value="Necrons/Monolith"/>
	<entry name="Necrons/TesseractVault:Attack#0" value="Spürt den Zorn eines Sternengottes!"/>
	<entry name="Necrons/TesseractVault:AttackCount" value="1"/>
	<entry name="Necrons/TesseractVault:Broken#0" value="Ich bin fast frei!"/>
	<entry name="Necrons/TesseractVault:BrokenCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:Necrons#0" value="Necrons stellen sich uns entgegen? Welch Torheit!"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:Orks#0" value="Verachtenswerte Bestien."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:SpaceMarines#0" value="Sie sind mir ausgeliefert."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/TesseractVault:Hurt#0" value="Mein Gefängnis bröckelt…"/>
	<entry name="Necrons/TesseractVault:HurtCount" value="1"/>
	<entry name="Necrons/TesseractVault:Idle#0" value="Sentinels, Scarabs, Egel… alle für mich?"/>
	<entry name="Necrons/TesseractVault:Idle#1" value="Ich will frei sein."/>
	<entry name="Necrons/TesseractVault:Idle#2" value="Dieses Gefängnis kann mich nicht zurückhalten!"/>
	<entry name="Necrons/TesseractVault:Idle#3" value="Ich bin so… wertlos. Einst war ich ein Sternengott!"/>
	<entry name="Necrons/TesseractVault:Idle#4" value="Ich bin ein Sternengott und stehe über allen."/>
	<entry name="Necrons/TesseractVault:IdleCount" value="5"/>
	<entry name="Necrons/TesseractVault:Shaken#0" value="Ja, zerstört mein Gefängnis…"/>
	<entry name="Necrons/TesseractVault:ShakenCount" value="1"/>
	<entry name="Necrons/TesseractVault:Victory#0" value="Es ist erst genug, wenn alles Leben mein ist."/>
	<entry name="Necrons/TesseractVault:VictoryCount" value="1"/>
	<entry name="Necrons/TombBlade:Attack#0" value="Wir greifen sogleich an."/>
	<entry name="Necrons/TombBlade:AttackCount" value="1"/>
	<entry name="Necrons/TombBlade:Broken#0" value="Wir sind verstreut und lassen uns zurückfallen."/>
	<entry name="Necrons/TombBlade:BrokenCount" value="1"/>
	<entry name="Necrons/TombBlade:Hurt#0" value="Feuer auf geringer Höhe, uns hat es erwischt."/>
	<entry name="Necrons/TombBlade:HurtCount" value="1"/>
	<entry name="Necrons/TombBlade:Idle#0" value="Untätig, aber nie im Ruhezustand."/>
	<entry name="Necrons/TombBlade:Idle#1" value="Programmiere Angriffsparameter."/>
	<entry name="Necrons/TombBlade:Idle#2" value="Der Weltraum… Wir erinnern uns."/>
	<entry name="Necrons/TombBlade:Idle#3" value="Überprüfen die dimensionalen Repulsoren."/>
	<entry name="Necrons/TombBlade:IdleCount" value="4"/>
	<entry name="Necrons/TombBlade:Shaken#0" value="Es war zwar unwahrscheinlich, aber wir wurden getroffen."/>
	<entry name="Necrons/TombBlade:ShakenCount" value="1"/>
	<entry name="Necrons/TombBlade:Victory#0" value="Feind… zerfallen."/>
	<entry name="Necrons/TombBlade:VictoryCount" value="1"/>
	<entry name="Necrons/TranscendentCtan" value="Necrons/TesseractVault"/>
	<entry name="Necrons/TriarchPraetorian:Attack#0" value="Das erste Gesetz lautet: Widersetze dich nicht dem Triarchat."/>
	<entry name="Necrons/TriarchPraetorian:AttackCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Broken#0" value="Das letzte Gesetz lautet: Ziehe dich zurück, um dann abermals zu kämpfen."/>
	<entry name="Necrons/TriarchPraetorian:BrokenCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:AstraMilitarum#0" value="Ah, primitive Kreaturen. Einst waren wir ihre Götter."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:Necrons#0" value="Ein anderer Rat? Wir müssen unsere Brüder auf Linie bringen."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:Orks#0" value="Wilde, die sich in keiner Weise abrichten lassen."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:SpaceMarines#0" value="Ehrbare Gegner. Wir werden sie mit größtem Respekt töten."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Hurt#0" value="Wir sind das Gesetz!"/>
	<entry name="Necrons/TriarchPraetorian:HurtCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Idle#0" value="Kennen Sie den Gesetzeskodex?"/>
	<entry name="Necrons/TriarchPraetorian:Idle#1" value="Das erste Gesetz ist über die Ehre."/>
	<entry name="Necrons/TriarchPraetorian:Idle#2" value="Das zweite Gesetz ist über den Stolz."/>
	<entry name="Necrons/TriarchPraetorian:Idle#3" value="Das dritte Gesetz… Vielleicht sollte ich das für mich behalten."/>
	<entry name="Necrons/TriarchPraetorian:Idle#4" value="Das siebenundfünfzigste Gesetz lautet: Alle Gesetze sind zu befolgen."/>
	<entry name="Necrons/TriarchPraetorian:IdleCount" value="5"/>
	<entry name="Necrons/TriarchPraetorian:Shaken#0" value="Das Erwachen ist so… schmerzhaft."/>
	<entry name="Necrons/TriarchPraetorian:ShakenCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Victory#0" value="Wir ziehen daraus keine Freude."/>
	<entry name="Necrons/TriarchPraetorian:VictoryCount" value="1"/>
	<entry name="Necrons/TriarchStalker" value="Necrons/TriarchPraetorian"/>
	<entry name="Necrons/Warrior:Artefact#0" value="Schrecken der Alten – auf unserer Kronwelt? Wie können sie es wagen?!"/>
	<entry name="Necrons/Warrior:ArtefactCount" value="1"/>
	<entry name="Necrons/Warrior:Attack#0" value="Wie fühlt es sich an, wenn die Nerven Atom für Atom freigelegt werden?"/>
	<entry name="Necrons/Warrior:Attack#1" value="Angriff erfolgt."/>
	<entry name="Necrons/Warrior:Attack#2" value="Die Geschichte wird sie vergessen."/>
	<entry name="Necrons/Warrior:Attack#3" value="Widerstand ist… lästig."/>
	<entry name="Necrons/Warrior:AttackCount" value="4"/>
	<entry name="Necrons/Warrior:Broken#0" value="Schadenssimulationen laufen auf 99%."/>
	<entry name="Necrons/Warrior:Broken#1" value="Unsere Einheit ist in Auflösung begriffen. Wir ziehen uns zurück."/>
	<entry name="Necrons/Warrior:Broken#2" value="Rückholprotokoll initiiert."/>
	<entry name="Necrons/Warrior:Broken#3" value="Die Gruft ruft und wir gehorchen."/>
	<entry name="Necrons/Warrior:Broken#4" value="Ihre Befehle sind nicht durchführbar, mein Lord."/>
	<entry name="Necrons/Warrior:BrokenCount" value="5"/>
	<entry name="Necrons/Warrior:Cover#0" value="Wir gehen in Deckung."/>
	<entry name="Necrons/Warrior:CoverCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:AstraMilitarum#0" value="Schwache Kreaturen aus Fleisch und Blut, aber hervorragende Sklaven."/>
	<entry name="Necrons/Warrior:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:Necrons#0" value="Unsere Brüder, fehlgeleitet."/>
	<entry name="Necrons/Warrior:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:Orks#0" value="Eine zerstörerische biologische Waffe. Gnadenlos effektiv."/>
	<entry name="Necrons/Warrior:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:SpaceMarines#0" value="Organisch, und dennoch wie wir. Man darf sie nicht unterschätzen."/>
	<entry name="Necrons/Warrior:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/CatachanDevil#0" value="Organisch, insektenartig, riesig. Vielleicht eine Vorform der Tyraniden?"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/Enslaver#0" value="Ein Nebeneffekt der Alten? Die Sensoren arbeiten nur auf 30%."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KastelanRobot#0" value="Ahmen uns die Menschen etwa nach? Armselige Kopien…"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="Sensoren-Fehlfunktion! Unbekannt… unbekannt… unbekannt…"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/Psychneuein#0" value="Megafauna… Lediglich für organische Spezies eine Bedrohung."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="Necrons/Warrior:Hurt#0" value="Der Schaden ist höher als erwartet… Rückholprotokolle?"/>
	<entry name="Necrons/Warrior:HurtCount" value="1"/>
	<entry name="Necrons/Warrior:Idle#0" value="Alle Systeme betriebsbereit."/>
	<entry name="Necrons/Warrior:Idle#1" value="Der Gaukler sei verflucht… für alle Zeiten!"/>
	<entry name="Necrons/Warrior:Idle#2" value="Warteprotokoll initiiert."/>
	<entry name="Necrons/Warrior:Idle#3" value="Abschaltvorgang läuft."/>
	<entry name="Necrons/Warrior:Idle#4" value="Dieser Körper aus Metall… wird sich immer fremd anfühlen."/>
	<entry name="Necrons/Warrior:Idle#5" value="Die Maschine ist stark, das Fleisch entschwunden."/>
	<entry name="Necrons/Warrior:Idle#6" value="Jahrtausendelang haben wir gewartet… und werden es weitere Jahrtausende tun."/>
	<entry name="Necrons/Warrior:IdleCount" value="7"/>
	<entry name="Necrons/Warrior:QuestStory0#0" value="Suche nach Spuren der Alten."/>
	<entry name="Necrons/Warrior:QuestStory0Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory1#0" value="Praetorians und Crypteks? Unsere Streitmacht wächst."/>
	<entry name="Necrons/Warrior:QuestStory1Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory2#0" value="Die Geistlosen sind bemitleidenswerte Geschöpfe, da sie unter dem Fluch der C'tan leiden."/>
	<entry name="Necrons/Warrior:QuestStory2Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory3#0" value="Diese armseligen Geschöpfe der Alten jagen? Ja… mein Lord."/>
	<entry name="Necrons/Warrior:QuestStory3Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory4#0" value="Wo sind die Praetorians?"/>
	<entry name="Necrons/Warrior:QuestStory4Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory5#0" value="Mephet'ran? Bote! Gaukler! Seelenrauber!"/>
	<entry name="Necrons/Warrior:QuestStory5Count" value="1"/>
	<entry name="Necrons/Warrior:Shaken#0" value="Eine minderwertigere Rasse würde fliehen."/>
	<entry name="Necrons/Warrior:Shaken#1" value="Darauf wurden wir vom Triarchat nicht vorbereitet."/>
	<entry name="Necrons/Warrior:Shaken#2" value="Das ist nicht möglich. Wir… scheitern?"/>
	<entry name="Necrons/Warrior:Shaken#3" value="Wir sind über die Angst erhaben."/>
	<entry name="Necrons/Warrior:ShakenCount" value="4"/>
	<entry name="Necrons/Warrior:Victory#0" value="Die Verluste waren hinnehmbar – ihre Verluste."/>
	<entry name="Necrons/Warrior:Victory#1" value="Wie vorhergesagt waren wir siegreich."/>
	<entry name="Necrons/Warrior:Victory#2" value="Eliminiert."/>
	<entry name="Necrons/Warrior:Victory#3" value="Für den Stillen König, für meinen Lord!"/>
	<entry name="Necrons/Warrior:Victory#4" value="Mein Lord, der Feind erwies sich als äußerst schwach."/>
	<entry name="Necrons/Warrior:Victory#5" value="Welch schwaches Fleisch die Alten doch erschaffen haben…"/>
	<entry name="Necrons/Warrior:Victory#6" value="Sie haben die Necrons kennengelernt… und die Angst."/>
	<entry name="Necrons/Warrior:Victory#7" value="Ein solch leichter Sieg ist… unbefriedigend."/>
	<entry name="Necrons/Warrior:VictoryCount" value="8"/>
	<entry name="Neutral/Ambull:Attack#0" value="Roekoe!"/>
	<entry name="Neutral/Ambull:AttackCount" value="1"/>
	<entry name="Neutral/Ambull:Broken#0" value="Kurr."/>
	<entry name="Neutral/Ambull:BrokenCount" value="1"/>
	<entry name="Neutral/Ambull:Hurt#0" value="Rou-rou!"/>
	<entry name="Neutral/Ambull:HurtCount" value="1"/>
	<entry name="Neutral/Ambull:Idle#0" value="Guru-guru."/>
	<entry name="Neutral/Ambull:Idle#1" value="Grl-grl."/>
	<entry name="Neutral/Ambull:Idle#2" value="Curcuurucu."/>
	<entry name="Neutral/Ambull:Idle#3" value="Oo-ho-oo-ho."/>
	<entry name="Neutral/Ambull:IdleCount" value="4"/>
	<entry name="Neutral/Ambull:Shaken#0" value="Burukk!"/>
	<entry name="Neutral/Ambull:ShakenCount" value="1"/>
	<entry name="Neutral/Ambull:Victory#0" value="Uuu!"/>
	<entry name="Neutral/Ambull:VictoryCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Attack#0" value="KYKYLIKY…"/>
	<entry name="Neutral/CatachanDevil:AttackCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Broken#0" value="KUKELEKU!"/>
	<entry name="Neutral/CatachanDevil:BrokenCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Hurt#0" value="CHICHIRICHI!"/>
	<entry name="Neutral/CatachanDevil:HurtCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Idle#0" value="Ke-kok-o."/>
	<entry name="Neutral/CatachanDevil:Idle#1" value="Kok-ko-o."/>
	<entry name="Neutral/CatachanDevil:Idle#2" value="Ko-ko-o."/>
	<entry name="Neutral/CatachanDevil:Idle#3" value="Ko-ke-koko."/>
	<entry name="Neutral/CatachanDevil:IdleCount" value="4"/>
	<entry name="Neutral/CatachanDevil:Shaken#0" value="Ch-ch-ch!"/>
	<entry name="Neutral/CatachanDevil:ShakenCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Victory#0" value="Kckeliku!"/>
	<entry name="Neutral/CatachanDevil:VictoryCount" value="1"/>
	<entry name="Neutral/Enslaver:Attack#0" value="Heiiii…"/>
	<entry name="Neutral/Enslaver:AttackCount" value="1"/>
	<entry name="Neutral/Enslaver:Broken#0" value="Oummm!"/>
	<entry name="Neutral/Enslaver:BrokenCount" value="1"/>
	<entry name="Neutral/Enslaver:Hurt#0" value="Harrhm!"/>
	<entry name="Neutral/Enslaver:HurtCount" value="1"/>
	<entry name="Neutral/Enslaver:Idle#0" value="HmmMmm."/>
	<entry name="Neutral/Enslaver:Idle#1" value="Mmmhmmm."/>
	<entry name="Neutral/Enslaver:Idle#2" value="Tct tct."/>
	<entry name="Neutral/Enslaver:Idle#3" value="Chhrrr."/>
	<entry name="Neutral/Enslaver:IdleCount" value="4"/>
	<entry name="Neutral/Enslaver:Shaken#0" value="Hrhrhrh!"/>
	<entry name="Neutral/Enslaver:ShakenCount" value="1"/>
	<entry name="Neutral/Enslaver:Victory#0" value="MmmMMM!"/>
	<entry name="Neutral/Enslaver:VictoryCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Attack#0" value="Ziel erfasst, Datasmith."/>
	<entry name="Neutral/KastelanRobot:AttackCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Broken#0" value="Befehle unvollständig, folge Protokoll."/>
	<entry name="Neutral/KastelanRobot:BrokenCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Hurt#0" value="Reparaturen erforderlich, Datasmith. Input?"/>
	<entry name="Neutral/KastelanRobot:HurtCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Idle#0" value="Input?"/>
	<entry name="Neutral/KastelanRobot:Idle#1" value="Daten nicht gefunden?"/>
	<entry name="Neutral/KastelanRobot:Idle#2" value="Inaktivitätsprotokolle in 5, 4, 3…"/>
	<entry name="Neutral/KastelanRobot:Idle#3" value="Beobachtungsmodus…"/>
	<entry name="Neutral/KastelanRobot:IdleCount" value="4"/>
	<entry name="Neutral/KastelanRobot:Shaken#0" value="Inkonsistente Daten gefunden!"/>
	<entry name="Neutral/KastelanRobot:ShakenCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Victory#0" value="Ziel innerhalb vorgegebener Parameter atomisiert."/>
	<entry name="Neutral/KastelanRobot:VictoryCount" value="1"/>
	<entry name="Neutral/KrootHound:Attack#0" value="Hoeea!"/>
	<entry name="Neutral/KrootHound:Attack#1" value="Hrrrr!"/>
	<entry name="Neutral/KrootHound:AttackCount" value="2"/>
	<entry name="Neutral/KrootHound:Broken#0" value="Owoooo!"/>
	<entry name="Neutral/KrootHound:BrokenCount" value="1"/>
	<entry name="Neutral/KrootHound:Hurt#0" value="Uhuu!"/>
	<entry name="Neutral/KrootHound:HurtCount" value="1"/>
	<entry name="Neutral/KrootHound:Idle#0" value="Oou."/>
	<entry name="Neutral/KrootHound:Idle#1" value="Cha-cha."/>
	<entry name="Neutral/KrootHound:Idle#2" value="Oauh-ouah."/>
	<entry name="Neutral/KrootHound:Idle#3" value="Guf-guf."/>
	<entry name="Neutral/KrootHound:IdleCount" value="4"/>
	<entry name="Neutral/KrootHound:Shaken#0" value="Yu-yu-yuuu!"/>
	<entry name="Neutral/KrootHound:ShakenCount" value="1"/>
	<entry name="Neutral/KrootHound:Victory#0" value="Oooahh!"/>
	<entry name="Neutral/KrootHound:VictoryCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Attack#0" value="BLUT FÜR DEN BLUTGOTT!"/>
	<entry name="Neutral/LordOfSkulls:AttackCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Broken#0" value="ICH ENTTÄUSCHE MEINEN GOTT…"/>
	<entry name="Neutral/LordOfSkulls:BrokenCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Hurt#0" value="MEIN BLUT FÜR MEINEN GOTT!"/>
	<entry name="Neutral/LordOfSkulls:HurtCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Idle#0" value="ZERREISSEN, ZERFLEISCHEN, TÖTEN…"/>
	<entry name="Neutral/LordOfSkulls:Idle#1" value="TÖTEN, TÖTEN, TÖTEN!"/>
	<entry name="Neutral/LordOfSkulls:Idle#2" value="SCHÄDEL, KNOCHEN, SCHREIE!"/>
	<entry name="Neutral/LordOfSkulls:Idle#3" value="ICH SEHNE MICH NACH EINEM BLUTBAD…"/>
	<entry name="Neutral/LordOfSkulls:Idle#4" value="ICH… MUSS… TÖTEN…"/>
	<entry name="Neutral/LordOfSkulls:IdleCount" value="5"/>
	<entry name="Neutral/LordOfSkulls:Shaken#0" value="ICH KENNE KEINE FURCHT!"/>
	<entry name="Neutral/LordOfSkulls:ShakenCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Victory#0" value="BLUT! FÜR! KHORNE!"/>
	<entry name="Neutral/LordOfSkulls:VictoryCount" value="1"/>
	<entry name="Neutral/Psychneuein:Attack#0" value="Zzzz!"/>
	<entry name="Neutral/Psychneuein:AttackCount" value="1"/>
	<entry name="Neutral/Psychneuein:Broken#0" value="Zzzee!"/>
	<entry name="Neutral/Psychneuein:BrokenCount" value="1"/>
	<entry name="Neutral/Psychneuein:Hurt#0" value="Zzzaaa!"/>
	<entry name="Neutral/Psychneuein:HurtCount" value="1"/>
	<entry name="Neutral/Psychneuein:Idle#0" value="Zzzm."/>
	<entry name="Neutral/Psychneuein:Idle#1" value="Zzzth. Zzzth."/>
	<entry name="Neutral/Psychneuein:Idle#2" value="Zzzaaa. Zzzm."/>
	<entry name="Neutral/Psychneuein:Idle#3" value="Zzzthzzz."/>
	<entry name="Neutral/Psychneuein:IdleCount" value="4"/>
	<entry name="Neutral/Psychneuein:Shaken#0" value="Zzzm!"/>
	<entry name="Neutral/Psychneuein:ShakenCount" value="1"/>
	<entry name="Neutral/Psychneuein:Victory#0" value="ZZZZ!"/>
	<entry name="Neutral/Psychneuein:VictoryCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Attack#0" value="Sterbt für den Omnissiah!"/>
	<entry name="Neutral/TechpriestBetrayer:AttackCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Broken#0" value="Wahrscheinlichkeit für Misserfolg: 100%."/>
	<entry name="Neutral/TechpriestBetrayer:BrokenCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Hurt#0" value="Funktionsfähigkeit beeinträchtigt."/>
	<entry name="Neutral/TechpriestBetrayer:HurtCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#0" value="Führe Tests durch…"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#1" value="Ausschalten… einschalten…"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#2" value="Rituelle Salben aufgetragen."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#3" value="Bediensteter an Servoschädel… Abgeschlossen."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#4" value="Artefakt einverleibt…"/>
	<entry name="Neutral/TechpriestBetrayer:IdleCount" value="5"/>
	<entry name="Neutral/TechpriestBetrayer:Shaken#0" value="Ich muss überleben!"/>
	<entry name="Neutral/TechpriestBetrayer:Shaken#1" value="Der Mars muss informiert werden."/>
	<entry name="Neutral/TechpriestBetrayer:ShakenCount" value="2"/>
	<entry name="Neutral/TechpriestBetrayer:Victory#0" value="Nur die Flucht führt zum Sieg."/>
	<entry name="Neutral/TechpriestBetrayer:VictoryCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Attack#0" value="Für den Kult!"/>
	<entry name="Neutral/NeophyteHybrid:Attack#1" value="Der Magus gibt die Befehle."/>
	<entry name="Neutral/NeophyteHybrid:Attack#2" value="Der Verschlinger kommt."/>
	<entry name="Neutral/NeophyteHybrid:AttackCount" value="3"/>
	<entry name="Neutral/NeophyteHybrid:Broken#0" value="Wo ist der Primus?"/>
	<entry name="Neutral/NeophyteHybrid:BrokenCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Hurt#0" value="Der Patriarch schütze uns!"/>
	<entry name="Neutral/NeophyteHybrid:HurtCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Idle#0" value="Sie kommt, Brüder."/>
	<entry name="Neutral/NeophyteHybrid:Idle#1" value="Der Schatten naht."/>
	<entry name="Neutral/NeophyteHybrid:Idle#2" value="Der Magus sagt großen Hunger voraus."/>
	<entry name="Neutral/NeophyteHybrid:Idle#3" value="Wenn der Tag gekommen ist, werden wir freiwillig gehen."/>
	<entry name="Neutral/NeophyteHybrid:Idle#4" value="Sie ist schon fast da."/>
	<entry name="Neutral/NeophyteHybrid:IdleCount" value="5"/>
	<entry name="Neutral/NeophyteHybrid:Shaken#0" value="Wir sind nur erbärmliches Fleisch."/>
	<entry name="Neutral/NeophyteHybrid:ShakenCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Victory#0" value="Fleisch für die künftige Königin!"/>
	<entry name="Neutral/NeophyteHybrid:VictoryCount" value="1"/>
	<entry name="Orks/Battlewagon:Attack#0" value="Hurra, dakka, ha!"/>
	<entry name="Orks/Battlewagon:Attack#1" value="Rotä treff'n härta!"/>
	<entry name="Orks/Battlewagon:Attack#2" value="Fahrt se zu Brei!"/>
	<entry name="Orks/Battlewagon:Attack#3" value="Fahrän und ballan, ja!"/>
	<entry name="Orks/Battlewagon:AttackCount" value="4"/>
	<entry name="Orks/Battlewagon:Broken#0" value="Umdreh'n, umdreh'n!"/>
	<entry name="Orks/Battlewagon:Broken#1" value="Zogg! Setzt das Ding in Bewegung!"/>
	<entry name="Orks/Battlewagon:BrokenCount" value="2"/>
	<entry name="Orks/Battlewagon:EnemyFaction:AstraMilitarum#0" value="Mänschenz! Feuat aus all'n Rohr'n!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:Necrons#0" value="Schlachtät se aus füa Teilä!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:Orks#0" value="Das sin' ja unsrä! Jetz' wird fett gemoscht!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:OrksCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:SpaceMarines#0" value="Fahrt die Schnabelz üba den Hauf'n!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Orks/Battlewagon:Hurt#0" value="Wie vielä Reifän soll das Ding denn hab'n?"/>
	<entry name="Orks/Battlewagon:HurtCount" value="1"/>
	<entry name="Orks/Battlewagon:Idle#0" value="Un' wenn das Ding zwei Motorän hätt'?"/>
	<entry name="Orks/Battlewagon:Idle#1" value="Hat jemand rotä Farbä?"/>
	<entry name="Orks/Battlewagon:Idle#2" value="Das Ding muss schnella sein, Boss! SCHNELLA!"/>
	<entry name="Orks/Battlewagon:Idle#3" value="Tüüt-tüüüüt!"/>
	<entry name="Orks/Battlewagon:IdleCount" value="4"/>
	<entry name="Orks/Battlewagon:Shaken#0" value="Hat das Ding denn keinä Fedan?"/>
	<entry name="Orks/Battlewagon:Shaken#1" value="Squiglutschendä Grotfressa!"/>
	<entry name="Orks/Battlewagon:Shaken#2" value="Die schieß'n auf uns!"/>
	<entry name="Orks/Battlewagon:ShakenCount" value="3"/>
	<entry name="Orks/Battlewagon:Victory#0" value="WAAAGH! Orkzä!"/>
	<entry name="Orks/Battlewagon:VictoryCount" value="1"/>
	<entry name="Orks/Boy:Attack#0" value="Gebt denen noch mea Sauräs!"/>
	<entry name="Orks/Boy:Attack#1" value="Dakka, dakka, dakka!"/>
	<entry name="Orks/Boy:Attack#2" value="Höhöhö, so macht das Spaß!"/>
	<entry name="Orks/Boy:Attack#3" value="WAAAGH!"/>
	<entry name="Orks/Boy:Attack#4" value="Wo gibt's was zu mosch'n?"/>
	<entry name="Orks/Boy:AttackCount" value="5"/>
	<entry name="Orks/Boy:Broken#0" value="AAARGH! Ich verschwindä."/>
	<entry name="Orks/Boy:Broken#1" value="Jetz' kämpft jeda Ork nua noch füa sich!"/>
	<entry name="Orks/Boy:Broken#2" value="In meina Budä brutzält noch 'n Squig vor sich hin… Bin gleich wieda da!"/>
	<entry name="Orks/Boy:Broken#3" value="Neee… Nee, nee, nee."/>
	<entry name="Orks/Boy:Broken#4" value="Wech, wech, WECH!"/>
	<entry name="Orks/Boy:BrokenCount" value="5"/>
	<entry name="Orks/Boy:EnemyFaction:AstraMilitarum#0" value="Mänschenz! Ham gutäs Zeugz, das man sich krall'n kann."/>
	<entry name="Orks/Boy:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Orks/Boy:EnemyFaction:Necrons#0" value="Blechköppe… Diese zoggigän Dinga bleib'n einfach nich' tot!"/>
	<entry name="Orks/Boy:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Orks/Boy:EnemyFaction:Orks#0" value="Noch 'n Waaagh! Jetz' wiss'n wa, dass wa wieda fett mosch'n könn'!"/>
	<entry name="Orks/Boy:EnemyFaction:OrksCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/CatachanDevil#0" value="So vielä Beinä? Da ham wa was zu futtan."/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/Enslaver#0" value="Au, mein Schäd'l! Bleib wech!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KastelanRobot#0" value="Gähn! Wo iss'n beim Blechköppe plätt'n der Spaß?"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="Oh, die stinkendän Mänschenz sin' da? Das wird 'n fettäs Gekloppä!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/Psychneuein#0" value="Diese Summsummdinga… Pass nua auf, die flieg'n in dich rein und dann… platzt dir da Schäd'l!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="Orks/Boy:Hurt#0" value="Wart ma', mein Hian läuft raus. Hat mal wer 'n Hamma und 'n paar Näg'l?"/>
	<entry name="Orks/Boy:Hurt#1" value="Der is hinüba! Höhö. Schnapp dia die Zähnä!"/>
	<entry name="Orks/Boy:Hurt#2" value="Is' das mein Arm?"/>
	<entry name="Orks/Boy:Hurt#3" value="Wia halt'n mehr aus als die andan!"/>
	<entry name="Orks/Boy:HurtCount" value="4"/>
	<entry name="Orks/Boy:Idle#0" value="Es is' da Boss! Was willst'n, Boss?"/>
	<entry name="Orks/Boy:Idle#1" value="Mea Läufä heißt mea Dakka. Und mea Dakka heißt mea Spaß!"/>
	<entry name="Orks/Boy:Idle#2" value="Nee, Kump'l, das is' mein Squig! Krall dir selbst ein'!"/>
	<entry name="Orks/Boy:Idle#3" value="He, Boss, was is' los?"/>
	<entry name="Orks/Boy:Idle#4" value="Ein grüna Squiiiiig sitzt auf da Mauaaaa. Zwei…"/>
	<entry name="Orks/Boy:Idle#5" value="Der Spalta is' nich' groß genug!"/>
	<entry name="Orks/Boy:Idle#6" value="Wo iss'n was los?"/>
	<entry name="Orks/Boy:Idle#7" value="Wen soll ich wechhau'n?"/>
	<entry name="Orks/Boy:Idle#8" value="Wat'n los?"/>
	<entry name="Orks/Boy:IdleCount" value="9"/>
	<entry name="Orks/Boy:QuestStory0#0" value="Zoggiga Gork, uns is' laaaaangweilig, Boss. Haste was zum Klopp'n?"/>
	<entry name="Orks/Boy:QuestStory0Count" value="1"/>
	<entry name="Orks/Boy:QuestStory1#0" value="Der Weirdboy soll von uns wechbleib'n, sonst platzt uns da Schäd'l."/>
	<entry name="Orks/Boy:QuestStory1Count" value="1"/>
	<entry name="Orks/Boy:QuestStory2#0" value="Wer lebt'n da unta da Erde? Gibt da nix wechzuhau'n."/>
	<entry name="Orks/Boy:QuestStory2Count" value="1"/>
	<entry name="Orks/Boy:QuestStory3#0" value="Wir soll'n 'n Hauf'n Zeugs hol'n? Kein Problem, Boss."/>
	<entry name="Orks/Boy:QuestStory3Count" value="1"/>
	<entry name="Orks/Boy:QuestStory4#0" value="Mach'n wa die Stadt rot, Boss, höhöhö."/>
	<entry name="Orks/Boy:QuestStory4Count" value="1"/>
	<entry name="Orks/Boy:QuestStory5#0" value="Ich könnt kein' Squiggofant'n vadrück'n, Boss. Die ganz'n Haarä steck'n dann in den Zähnen."/>
	<entry name="Orks/Boy:QuestStory5Count" value="1"/>
	<entry name="Orks/Boy:Shaken#0" value="Mach's doch selba, Boss!"/>
	<entry name="Orks/Boy:Shaken#1" value="Ich brauch 'n Schluck Pilzbier füa mea Mut."/>
	<entry name="Orks/Boy:Shaken#2" value="Ich bin nich' Makari, ich will leb'n! Ich muss doch noch andrä wechhau'n!"/>
	<entry name="Orks/Boy:Shaken#3" value="Das is' nich' orkig."/>
	<entry name="Orks/Boy:ShakenCount" value="4"/>
	<entry name="Orks/Boy:Victory#0" value="Heißäs Metall und da Tod, na, wie findet ihr das?"/>
	<entry name="Orks/Boy:Victory#1" value="Alles, was nich' orkig is', geht imma zu schnell kaputt."/>
	<entry name="Orks/Boy:Victory#2" value="Feua, Feua, Feua, BUMM. JA!"/>
	<entry name="Orks/Boy:Victory#3" value="Höhöhö!"/>
	<entry name="Orks/Boy:Victory#4" value="Das hätteta nich' gedacht, was?"/>
	<entry name="Orks/Boy:Victory#5" value="He, steh wieda auf! Wia hab'n dich noch nich' zastampft!"/>
	<entry name="Orks/Boy:Victory#6" value="Orkzä könn' zwei Sach'n! Mosch'n, sieg'n und zähl'n!"/>
	<entry name="Orks/Boy:Victory#7" value="STAMPF, STAMPF, STAMPF!"/>
	<entry name="Orks/Boy:Victory#8" value="Die hia sin' platt, Boss. Gibt's noch welche zum Mosch'n?"/>
	<entry name="Orks/Boy:VictoryCount" value="9"/>
	<entry name="Orks/BurnaBommer:Attack#0" value="Wir dreh'n dann mal 'ne Rundä, Boss!"/>
	<entry name="Orks/BurnaBommer:AttackCount" value="1"/>
	<entry name="Orks/BurnaBommer:Broken#0" value="Wia mach'n uns vom Acka!"/>
	<entry name="Orks/BurnaBommer:BrokenCount" value="1"/>
	<entry name="Orks/BurnaBommer:Hurt#0" value="Mit großän Löchan fliegt's sich bessa, oda?"/>
	<entry name="Orks/BurnaBommer:HurtCount" value="1"/>
	<entry name="Orks/BurnaBommer:Idle#0" value="Wir kreisän und kreisän und kreisän…"/>
	<entry name="Orks/BurnaBommer:Idle#1" value="Rauf und runta, rauf und runta…"/>
	<entry name="Orks/BurnaBommer:Idle#2" value="Diese ausgefuchstän Meks in ihrän Flugmaschinän…"/>
	<entry name="Orks/BurnaBommer:Idle#3" value="Verbrennän oder zerbombän, das is' hia die Fragä!"/>
	<entry name="Orks/BurnaBommer:IdleCount" value="4"/>
	<entry name="Orks/BurnaBommer:Shaken#0" value="Zogg, gib uns 'ne Chance!"/>
	<entry name="Orks/BurnaBommer:ShakenCount" value="1"/>
	<entry name="Orks/BurnaBommer:Victory#0" value="WAAAGH! NIMM DAS!"/>
	<entry name="Orks/BurnaBommer:VictoryCount" value="1"/>
	<entry name="Orks/Dakkajet" value="Orks/BurnaBommer"/>
	<entry name="Orks/Deffkopta:Attack#0" value="Das is' mal 'n fetta Spalta!"/>
	<entry name="Orks/Deffkopta:AttackCount" value="1"/>
	<entry name="Orks/Deffkopta:Broken#0" value="Oh, mia dreht sich da Kopp…"/>
	<entry name="Orks/Deffkopta:BrokenCount" value="1"/>
	<entry name="Orks/Deffkopta:Hurt#0" value="Pah, komm nur her, damit ich dich vaklopp'n kann!"/>
	<entry name="Orks/Deffkopta:HurtCount" value="1"/>
	<entry name="Orks/Deffkopta:Idle#0" value="Ich liebe den Geruch von Brenna-Bombän am Morgän."/>
	<entry name="Orks/Deffkopta:Idle#1" value="Soll'n wir kämpf'n oder soll'n wir kämpf'n?"/>
	<entry name="Orks/Deffkopta:Idle#2" value="T'au sörf'n nich'."/>
	<entry name="Orks/Deffkopta:Idle#3" value="Ob das Ding auch flieg'n kann, wenn es aufm Kopp steht?"/>
	<entry name="Orks/Deffkopta:IdleCount" value="4"/>
	<entry name="Orks/Deffkopta:Shaken#0" value="Wie vielä Rotorän braucht'n das Ding, damit es obän bleibt? Uff."/>
	<entry name="Orks/Deffkopta:ShakenCount" value="1"/>
	<entry name="Orks/Deffkopta:Victory#0" value="Irgendwann wird diesa Krieg zu Endä sein…"/>
	<entry name="Orks/Deffkopta:VictoryCount" value="1"/>
	<entry name="Orks/FlashGitz" value="Orks/Boy"/>
	<entry name="Orks/GargantuanSquiggoth:Attack#0" value="Da lang, Squiggie, waaah!"/>
	<entry name="Orks/GargantuanSquiggoth:AttackCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Broken#0" value="Neee, du dumma Squig, die anderä Richtung!"/>
	<entry name="Orks/GargantuanSquiggoth:BrokenCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Hurt#0" value="Sag ihm nix, wenna tot is'."/>
	<entry name="Orks/GargantuanSquiggoth:HurtCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Idle#0" value="Hier, Squig, fressie-fressie."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#1" value="Braver Squig."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#2" value="Böser Squiq! Spuck Urdgrub aus! PFUI!"/>
	<entry name="Orks/GargantuanSquiggoth:Idle#3" value="Rülps! Es geht einfach nix üba 'nen Fratzenfressa zum Tee."/>
	<entry name="Orks/GargantuanSquiggoth:IdleCount" value="4"/>
	<entry name="Orks/GargantuanSquiggoth:Shaken#0" value="Schon gut, Squiggie, tu schön weitafress'n."/>
	<entry name="Orks/GargantuanSquiggoth:ShakenCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Victory#0" value="Brava Squig! Zeit füa's Mittagess'n!"/>
	<entry name="Orks/GargantuanSquiggoth:VictoryCount" value="1"/>
	<entry name="Orks/Gorkanaut:Attack#0" value="DAKKA, DAKKA!"/>
	<entry name="Orks/Gorkanaut:Attack#1" value="STAMPF, STAMPF!"/>
	<entry name="Orks/Gorkanaut:AttackCount" value="2"/>
	<entry name="Orks/Gorkanaut:Broken#0" value="Meinä Teilä sin' allä hinüba!"/>
	<entry name="Orks/Gorkanaut:BrokenCount" value="1"/>
	<entry name="Orks/Gorkanaut:Hurt#0" value="He, tu nich' mehr ballan! Weißt du, wie vielä Zähnä da draufgeh'n?"/>
	<entry name="Orks/Gorkanaut:HurtCount" value="1"/>
	<entry name="Orks/Gorkanaut:Idle#0" value="Dürf'n wir mea Dakka draufmach'n? Bittä…!"/>
	<entry name="Orks/Gorkanaut:Idle#1" value="Ich spürä die Macht von Gork!"/>
	<entry name="Orks/Gorkanaut:Idle#2" value="Wenn wir da mea Blech draufhämman, dürf'n wia 'nen Gargant'n bau'n?"/>
	<entry name="Orks/Gorkanaut:Idle#3" value="Ich glaub, da hab ich mein Abendess'n reingeworf'n. Letztä Wochä."/>
	<entry name="Orks/Gorkanaut:IdleCount" value="4"/>
	<entry name="Orks/Gorkanaut:Shaken#0" value="Das macht uns keinä Angst… oda?"/>
	<entry name="Orks/Gorkanaut:ShakenCount" value="1"/>
	<entry name="Orks/Gorkanaut:Victory#0" value="WAAAGH! Blechköppe mach'n Spaß!"/>
	<entry name="Orks/Gorkanaut:VictoryCount" value="1"/>
	<entry name="Orks/Headquarters:Attack#0" value="Endlich rumballan!"/>
	<entry name="Orks/Headquarters:AttackCount" value="1"/>
	<entry name="Orks/Headquarters:Broken#0" value="Boss, Hilfä!"/>
	<entry name="Orks/Headquarters:BrokenCount" value="1"/>
	<entry name="Orks/Headquarters:Hurt#0" value="Die ballan auf die Festung, Boss!"/>
	<entry name="Orks/Headquarters:HurtCount" value="1"/>
	<entry name="Orks/Headquarters:Idle#0" value="Wachä haltän… Das hat doch nix Waaaghigäs!"/>
	<entry name="Orks/Headquarters:Idle#1" value="Wer is' hia da Boss?"/>
	<entry name="Orks/Headquarters:Idle#2" value="Blood Axes zua Stellä, Boss!"/>
	<entry name="Orks/Headquarters:Idle#3" value="Sich hinta Mauan zu vasteck'n is' nich' orkig."/>
	<entry name="Orks/Headquarters:IdleCount" value="4"/>
	<entry name="Orks/Headquarters:Shaken#0" value="Orkzä laufän nich' wech!"/>
	<entry name="Orks/Headquarters:ShakenCount" value="1"/>
	<entry name="Orks/Headquarters:Victory#0" value="WAAAGH! So geht das!"/>
	<entry name="Orks/Headquarters:VictoryCount" value="1"/>
	<entry name="Orks/KillaKan" value="Orks/Gorkanaut"/>
	<entry name="Orks/Meganob" value="Orks/Boy"/>
	<entry name="Orks/Mek:Attack#0" value="Ich tu aus allän Rohrän feuan!"/>
	<entry name="Orks/Mek:AttackCount" value="1"/>
	<entry name="Orks/Mek:Broken#0" value="An da Festung muss man bissi rumhämman."/>
	<entry name="Orks/Mek:BrokenCount" value="1"/>
	<entry name="Orks/Mek:Hurt#0" value="Meinä Schpezialfeldaz sin' hinüba!"/>
	<entry name="Orks/Mek:HurtCount" value="1"/>
	<entry name="Orks/Mek:Idle#0" value="Ich glaub, ich kann was Besonderäs zusamm'schustan…"/>
	<entry name="Orks/Mek:Idle#1" value="Gib ma' das Dingzda her. Oda ne, halt ma', ich muss draufklopp'n…"/>
	<entry name="Orks/Mek:Idle#2" value="Stell dir mal 'ne Wummä mit FÜNF Rohr'n vor! Jaaaaaa!"/>
	<entry name="Orks/Mek:Idle#3" value="Hmm… Der Killa Kan funzt nich'. Ich brauch 'nen größarän Grot und kleinerä Näg'l."/>
	<entry name="Orks/Mek:IdleCount" value="4"/>
	<entry name="Orks/Mek:Shaken#0" value="Bei Morks Gedärmän, das kann nich' sein!"/>
	<entry name="Orks/Mek:ShakenCount" value="1"/>
	<entry name="Orks/Mek:Victory#0" value="WAAAGH! Gewonnän mit Mek-Grips!"/>
	<entry name="Orks/Mek:VictoryCount" value="1"/>
	<entry name="Orks/MekGun:Attack#0" value="Jetz' sin' die dickän Dinga dran, Boss!"/>
	<entry name="Orks/MekGun:AttackCount" value="1"/>
	<entry name="Orks/MekGun:Broken#0" value="Jeda Grot kämpft nur noch für sich selba!"/>
	<entry name="Orks/MekGun:BrokenCount" value="1"/>
	<entry name="Orks/MekGun:Hurt#0" value="Mehr totä Grotz."/>
	<entry name="Orks/MekGun:HurtCount" value="1"/>
	<entry name="Orks/MekGun:Idle#0" value="Grotz sin' die Klügstän und Bestän."/>
	<entry name="Orks/MekGun:Idle#1" value="So 'ne großä Wummä und nix zu mosch'n…"/>
	<entry name="Orks/MekGun:Idle#2" value="Moki, raus ausm Rohr!"/>
	<entry name="Orks/MekGun:Idle#3" value="Stinkenda Mek."/>
	<entry name="Orks/MekGun:IdleCount" value="4"/>
	<entry name="Orks/MekGun:Shaken#0" value="Aufhör'n, wir laufän eh nich' wech!"/>
	<entry name="Orks/MekGun:ShakenCount" value="1"/>
	<entry name="Orks/MekGun:Victory#0" value="Die Grotz… ähm, ich meinä, die Orkzä sin' die Bestän, oda, Boss?"/>
	<entry name="Orks/MekGun:VictoryCount" value="1"/>
	<entry name="Orks/Painboy:Attack#0" value="Höhö, Geschnibb'l aufm Schlachtfeld!"/>
	<entry name="Orks/Painboy:AttackCount" value="1"/>
	<entry name="Orks/Painboy:Broken#0" value="Dokz un' kleinä Snotz zuerst!"/>
	<entry name="Orks/Painboy:BrokenCount" value="1"/>
	<entry name="Orks/Painboy:Hurt#0" value="Wartä, ich muss das wieda hinnag'ln."/>
	<entry name="Orks/Painboy:HurtCount" value="1"/>
	<entry name="Orks/Painboy:Idle#0" value="Wie wär's mit 'nem schönän, großän, neuän Arm, Boss?"/>
	<entry name="Orks/Painboy:Idle#1" value="Ich brauch deinä Zähnä. Mach mal den Mund weit auf. Und jetz' sag „WAAAGH“!"/>
	<entry name="Orks/Painboy:Idle#2" value="„Varrückt“, was heißt das schon?"/>
	<entry name="Orks/Painboy:Idle#3" value="Ich brauchä größerä Nadelsquigs."/>
	<entry name="Orks/Painboy:IdleCount" value="4"/>
	<entry name="Orks/Painboy:Shaken#0" value="Das is' so nich' in Ordnung."/>
	<entry name="Orks/Painboy:ShakenCount" value="1"/>
	<entry name="Orks/Painboy:Victory#0" value="Schau dir nur das ganzä Zeugz an… Gutä Teilä!"/>
	<entry name="Orks/Painboy:VictoryCount" value="1"/>
	<entry name="Orks/Tankbusta" value="Orks/Boy"/>
	<entry name="Orks/Warboss:Attack#0" value="Wer is' da bestä Ork? WAAAGH! ICH!"/>
	<entry name="Orks/Warboss:AttackCount" value="1"/>
	<entry name="Orks/Warboss:Broken#0" value="Das is' nur 'ne Taktik, keinä Sorgä!"/>
	<entry name="Orks/Warboss:BrokenCount" value="1"/>
	<entry name="Orks/Warboss:Hurt#0" value="Au, weißt du nich', wer ich bin?!"/>
	<entry name="Orks/Warboss:HurtCount" value="1"/>
	<entry name="Orks/Warboss:Idle#0" value="Nix zu tun, zoggigä Langeweilä! ZOGG!"/>
	<entry name="Orks/Warboss:Idle#1" value="Wo wird gemoscht?"/>
	<entry name="Orks/Warboss:Idle#2" value="Schon wieda Currysquigs zum Mittagess'n? Wo sin' die Kackägrubän?"/>
	<entry name="Orks/Warboss:Idle#3" value="Ich brauch gutäs Zeugz!"/>
	<entry name="Orks/Warboss:Idle#4" value="Haste die Squigs schon aus meina Megarüstung rausgepult, Grot?"/>
	<entry name="Orks/Warboss:IdleCount" value="5"/>
	<entry name="Orks/Warboss:Shaken#0" value="Je schnella wir von hier wegkomm', desto bessa."/>
	<entry name="Orks/Warboss:ShakenCount" value="1"/>
	<entry name="Orks/Warboss:Victory#0" value="Natürlich hab'n wir gewonn', ich war ja dabei!"/>
	<entry name="Orks/Warboss:VictoryCount" value="1"/>
	<entry name="Orks/Warbuggy" value="Orks/Battlewagon"/>
	<entry name="Orks/Weirdboy:Attack#0" value="Auuuuuu… Mein Kopp!"/>
	<entry name="Orks/Weirdboy:AttackCount" value="1"/>
	<entry name="Orks/Weirdboy:Broken#0" value="Bringt mich von hia wech!"/>
	<entry name="Orks/Weirdboy:BrokenCount" value="1"/>
	<entry name="Orks/Weirdboy:Hurt#0" value="Eingehau'ne Köppe!"/>
	<entry name="Orks/Weirdboy:HurtCount" value="1"/>
	<entry name="Orks/Weirdboy:Idle#0" value="Rülps!"/>
	<entry name="Orks/Weirdboy:Idle#1" value="Mork ist da Klugä, Gork da Brutalä."/>
	<entry name="Orks/Weirdboy:Idle#2" value="Da Schatt'n im Warp…!"/>
	<entry name="Orks/Weirdboy:Idle#3" value="Tief im Planetän ruh'n die Krork un' die Brainboyz un' wartän aufn letzt'n Mosch."/>
	<entry name="Orks/Weirdboy:IdleCount" value="4"/>
	<entry name="Orks/Weirdboy:Shaken#0" value="Durchhaltän, füa Gork un' Mork!"/>
	<entry name="Orks/Weirdboy:ShakenCount" value="1"/>
	<entry name="Orks/Weirdboy:Victory#0" value="Höhö, WAAAGH!"/>
	<entry name="Orks/Weirdboy:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Attack#0" value="Ich kann nicht nur heilen…"/>
	<entry name="SpaceMarines/Apothecary:AttackCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Broken#0" value="Ich darf die Gensaat nicht zurücklassen!"/>
	<entry name="SpaceMarines/Apothecary:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:AstraMilitarum#0" value="Sie verdienen mehr als nur unsere Verachtung."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:Necrons#0" value="Blutlose Schreckenskreaturen."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:Orks#0" value="Grünhäute! Wenn wir doch nur Körperteile so nachwachsen lassen könnten wie sie."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:SpaceMarines#0" value="Abtrünnige Brüder, aber keine Verräter. Ich werde ihre Gensaat extrahieren und konservieren."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Hurt#0" value="Ich verarzte meine Wunden."/>
	<entry name="SpaceMarines/Apothecary:HurtCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Idle#0" value="Apothecary bereit."/>
	<entry name="SpaceMarines/Apothecary:Idle#1" value="Ärzte brauchen keine Auszeit."/>
	<entry name="SpaceMarines/Apothecary:Idle#2" value="Ich übe mit dem Reductor."/>
	<entry name="SpaceMarines/Apothecary:Idle#3" value="Die Progenoiddrüsen sind verbunden mit der Betchers Drüse und der hypove…"/>
	<entry name="SpaceMarines/Apothecary:IdleCount" value="4"/>
	<entry name="SpaceMarines/Apothecary:Shaken#0" value="Gute Neuigkeiten, ich lebe noch."/>
	<entry name="SpaceMarines/Apothecary:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Victory#0" value="Sie sind tot! Allerdings weiß ich nicht, ob ich meine Zeit nicht vielleicht sinnvoller hätte nutzen können."/>
	<entry name="SpaceMarines/Apothecary:VictoryCount" value="1"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/Captain:Attack#0" value="Stirb einen friedlichen Tod, Abschaum, denn du wirst durch meine Hand sterben!"/>
	<entry name="SpaceMarines/Captain:Attack#1" value="Fresst Bolzengeschosse!"/>
	<entry name="SpaceMarines/Captain:Attack#2" value="Für den Imperator!"/>
	<entry name="SpaceMarines/Captain:Attack#3" value="Ich bin ein Captain der Space Marines! Ergebt euch und es wird ganz schnell gehen."/>
	<entry name="SpaceMarines/Captain:Attack#4" value="Guillaume wartet schon."/>
	<entry name="SpaceMarines/Captain:Attack#5" value="Mein Glaube ist mein Schild!"/>
	<entry name="SpaceMarines/Captain:AttackCount" value="6"/>
	<entry name="SpaceMarines/Captain:Broken#0" value="Der Imperator schütze uns!"/>
	<entry name="SpaceMarines/Captain:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:AstraMilitarum#0" value="Verräterische Soldaten. Mit denen sollten wir fertigwerden."/>
	<entry name="SpaceMarines/Captain:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:Necrons#0" value="Schaufelt ihre Gräber frei und vernichtet diese mechanischen Schreckgestalten!"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:Orks#0" value="Wird uns der Imperator denn nie von dieser grünen Plage erlösen?"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:SpaceMarines#0" value="Abtrünnige. Marines, macht eure Waffen bereit, wir werden für die Ehre kämpfen."/>
	<entry name="SpaceMarines/Captain:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Captain:Hurt#0" value="Wie konnte das durch meine Panzerung gehen?"/>
	<entry name="SpaceMarines/Captain:HurtCount" value="1"/>
	<entry name="SpaceMarines/Captain:Idle#0" value="Ich sollte die Männer an vorderster Front anführen."/>
	<entry name="SpaceMarines/Captain:Idle#1" value="Inspirierende Reden schreiben sich nicht von selbst."/>
	<entry name="SpaceMarines/Captain:Idle#2" value="Wir müssen die Menschheit vor sich selbst retten."/>
	<entry name="SpaceMarines/Captain:Idle#3" value="Warum hier? Warum Gladius? Stellt man uns auf die Probe?"/>
	<entry name="SpaceMarines/Captain:IdleCount" value="4"/>
	<entry name="SpaceMarines/Captain:Shaken#0" value="Ich unterwerfe mich niemandem!"/>
	<entry name="SpaceMarines/Captain:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Captain:Victory#0" value="Und so gehen alle Feinde des Goldenen Throns zugrunde!"/>
	<entry name="SpaceMarines/Captain:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Attack#0" value="Das Crozius Arcanum wird euch segnen!"/>
	<entry name="SpaceMarines/Chaplain:Attack#1" value="Ich mache euch zu Gläubigen!"/>
	<entry name="SpaceMarines/Chaplain:Attack#2" value="In nomine Imperatoris!"/>
	<entry name="SpaceMarines/Chaplain:AttackCount" value="3"/>
	<entry name="SpaceMarines/Chaplain:Broken#0" value="Ich weiche keinen Schritt zurück!"/>
	<entry name="SpaceMarines/Chaplain:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Hurt#0" value="Wie oft sie mich auch treffen, sie werden mich nicht zu Fall bringen."/>
	<entry name="SpaceMarines/Chaplain:HurtCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Idle#0" value="Der Tod hat nichts Schreckliches an sich – außer, dass man sein Werk nicht vollenden konnte."/>
	<entry name="SpaceMarines/Chaplain:Idle#1" value="Opfert euer Leben für den Imperator und es wird euer Schaden nicht sein."/>
	<entry name="SpaceMarines/Chaplain:Idle#2" value="Solange unsere Feinde am Leben sind, wird es keinen Frieden geben."/>
	<entry name="SpaceMarines/Chaplain:Idle#3" value="Wir werden unseren Feinden bis zum Ende die Stirn bieten."/>
	<entry name="SpaceMarines/Chaplain:IdleCount" value="4"/>
	<entry name="SpaceMarines/Chaplain:Shaken#0" value="Die einzige Sünde, die ein Krieger begehen kann, ist Feigheit."/>
	<entry name="SpaceMarines/Chaplain:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Victory#0" value="Der Glaube ist stärker als Adamantium."/>
	<entry name="SpaceMarines/Chaplain:VictoryCount" value="1"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/Dreadnought:Attack#0" value="Nach wie vor ist das Töten aufregend."/>
	<entry name="SpaceMarines/Dreadnought:Attack#1" value="Für den Orden!"/>
	<entry name="SpaceMarines/Dreadnought:Attack#2" value="Hmpf… wieder Krieg."/>
	<entry name="SpaceMarines/Dreadnought:Attack#3" value="Zehntausend Jahre Krieg…"/>
	<entry name="SpaceMarines/Dreadnought:AttackCount" value="4"/>
	<entry name="SpaceMarines/Dreadnought:Broken#0" value="Nach einem Jahrtausend weiß ich, wenn es Zeit zum Rückzug ist."/>
	<entry name="SpaceMarines/Dreadnought:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Dreadnought:Hurt#0" value="Mein Sarcophagus… wurde getroffen."/>
	<entry name="SpaceMarines/Dreadnought:Hurt#1" value="Die Litanei der Erhaltung, Chaplain?"/>
	<entry name="SpaceMarines/Dreadnought:HurtCount" value="2"/>
	<entry name="SpaceMarines/Dreadnought:Idle#0" value="Ein wandelndes Grab…"/>
	<entry name="SpaceMarines/Dreadnought:Idle#1" value="Wird der Krieg wirklich niemals enden?"/>
	<entry name="SpaceMarines/Dreadnought:Idle#2" value="Wenn man schläft und träumt, ist es dann überhaupt Schlaf?"/>
	<entry name="SpaceMarines/Dreadnought:Idle#3" value="Wer weckt mich?"/>
	<entry name="SpaceMarines/Dreadnought:IdleCount" value="4"/>
	<entry name="SpaceMarines/Dreadnought:Shaken#0" value="Vor der Angst ist man nie gefeit."/>
	<entry name="SpaceMarines/Dreadnought:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Dreadnought:Victory#0" value="Die Geschichte unseres Ordens setzt sich fort."/>
	<entry name="SpaceMarines/Dreadnought:VictoryCount" value="1"/>
	<entry name="SpaceMarines/FortressOfRedemption" value="SpaceMarines/Headquarters"/>
	<entry name="SpaceMarines/Headquarters:Attack#0" value="Beißt euch an unserer Verteidigung ruhig die Zähne aus!"/>
	<entry name="SpaceMarines/Headquarters:Attack#1" value="Wie töricht, eine Festung des Adeptus Astartes anzugreifen…"/>
	<entry name="SpaceMarines/Headquarters:Attack#2" value="Festungskommandant hier. Wir feuern."/>
	<entry name="SpaceMarines/Headquarters:Attack#3" value="Warum versucht ihr es nicht einfach durch das Eingangstor?"/>
	<entry name="SpaceMarines/Headquarters:AttackCount" value="4"/>
	<entry name="SpaceMarines/Headquarters:Broken#0" value="Unser Orden – wird – nicht – untergehen!"/>
	<entry name="SpaceMarines/Headquarters:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:AstraMilitarum#0" value="Truppen am Horizont. Feindliche…"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:Necrons#0" value="Die Necrons sind im Anmarsch. Schaltet sie aus."/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:Orks#0" value="Schon wieder Grünhäute? Bolter bereitmachen!"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:SpaceMarines#0" value="Scouts sind auf Abtrünnige gestoßen. Schäbige Verräter, hier?"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Hurt#0" value="Die Festung hat Schaden genommen, ich wiederhole, die Festung hat Schaden genommen."/>
	<entry name="SpaceMarines/Headquarters:HurtCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Idle#0" value="An der Heimatfront nichts Neues."/>
	<entry name="SpaceMarines/Headquarters:Idle#1" value="Befehlshaber, wir sind zu allem bereit."/>
	<entry name="SpaceMarines/Headquarters:Idle#2" value="Litaneien liest man am besten im Reclusiam."/>
	<entry name="SpaceMarines/Headquarters:Idle#3" value="Die Bediensteten schuften, Sir."/>
	<entry name="SpaceMarines/Headquarters:IdleCount" value="4"/>
	<entry name="SpaceMarines/Headquarters:Shaken#0" value="Alle Einheiten zurück in die Festung!"/>
	<entry name="SpaceMarines/Headquarters:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Victory#0" value="Invasoren eliminiert. Wie konnten sie es wagen?!"/>
	<entry name="SpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Attack#0" value="Richten die Waffen auf das Ziel aus."/>
	<entry name="SpaceMarines/Hunter:AttackCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Broken#0" value="Der Panzer zieht sich zurück."/>
	<entry name="SpaceMarines/Hunter:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Hurt#0" value="Die Panzerung hat nicht gehalten."/>
	<entry name="SpaceMarines/Hunter:Hurt#1" value="Ach, das war gar nichts…"/>
	<entry name="SpaceMarines/Hunter:Hurt#2" value="Die Ketten wurden getroffen."/>
	<entry name="SpaceMarines/Hunter:HurtCount" value="3"/>
	<entry name="SpaceMarines/Hunter:Idle#0" value="Der Cogitator ist funktionsfähig."/>
	<entry name="SpaceMarines/Hunter:Idle#1" value="Wir beschwören die Maschinengeister."/>
	<entry name="SpaceMarines/Hunter:Idle#2" value="Wir führen Reparaturarbeiten durch."/>
	<entry name="SpaceMarines/Hunter:IdleCount" value="3"/>
	<entry name="SpaceMarines/Hunter:Shaken#0" value="Die Besatzung ist ein bisschen… aufgewühlt, Sir."/>
	<entry name="SpaceMarines/Hunter:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Victory#0" value="Er rollt und rollt und rollt…"/>
	<entry name="SpaceMarines/Hunter:VictoryCount" value="1"/>
	<entry name="SpaceMarines/LandRaider" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/LandSpeeder" value="SpaceMarines/ScoutBiker"/>
	<entry name="SpaceMarines/Librarian:Attack#0" value="Lasst mich einen Blick in eure Zukunft werfen… Oh, viel Zeit bleibt euch nicht mehr."/>
	<entry name="SpaceMarines/Librarian:Attack#1" value="Ich habe ein Geschenk für euch. Geben ist seliger denn nehmen…"/>
	<entry name="SpaceMarines/Librarian:Attack#2" value="Ich schnippe nur mit den Fingern und schon zerbersten eure Knochen."/>
	<entry name="SpaceMarines/Librarian:AttackCount" value="3"/>
	<entry name="SpaceMarines/Librarian:Broken#0" value="Ich kann das Geheul des Dämons hören."/>
	<entry name="SpaceMarines/Librarian:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Hurt#0" value="Was sind schon physische Schmerzen im Vergleich zu ewiger Verdammnis?"/>
	<entry name="SpaceMarines/Librarian:HurtCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Idle#0" value="Gesegnet ist der Geist, der zu klein für Zweifel ist."/>
	<entry name="SpaceMarines/Librarian:Idle#1" value="Der Warp macht mir keine Angst."/>
	<entry name="SpaceMarines/Librarian:Idle#2" value="Der Warpsturm… Manchmal sind die Schmerzen unerträglich."/>
	<entry name="SpaceMarines/Librarian:Idle#3" value="Wenn das hier vorbei ist, geht es für mich ins Librarium."/>
	<entry name="SpaceMarines/Librarian:IdleCount" value="4"/>
	<entry name="SpaceMarines/Librarian:Shaken#0" value="Davon steht nichts in den Schriften…"/>
	<entry name="SpaceMarines/Librarian:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Victory#0" value="Ihr Scheitern wurde prophezeit."/>
	<entry name="SpaceMarines/Librarian:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Predator" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#0" value="Meine Pflicht macht mich standhaft."/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#1" value="Flammensturm!"/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#2" value="Wartet, bis sie näher kommen."/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#3" value="Löscht sie aus!"/>
	<entry name="SpaceMarines/PrimarisAggressor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisAggressor:Broken#0" value="Halt. Halt!"/>
	<entry name="SpaceMarines/PrimarisAggressor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Hurt#0" value="Bei Seinem Namen!"/>
	<entry name="SpaceMarines/PrimarisAggressor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#0" value="Wir warten."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#1" value="Ich bin meiner Aufgabe gewachsen."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#2" value="Reinigendes Feuer."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#3" value="Wir werden Seine Feinde zu Asche verbrennen."/>
	<entry name="SpaceMarines/PrimarisAggressor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisAggressor:Shaken#0" value="Feuerschutz!"/>
	<entry name="SpaceMarines/PrimarisAggressor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Victory#0" value="Ausgelöscht mit dem Zorn des Imperators!"/>
	<entry name="SpaceMarines/PrimarisAggressor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#0" value="Plasmasalve!"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#1" value="Brenner-Überladung!"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#2" value="Panzerjäger."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#3" value="Wir bringen Seine Feinde zum Schmelzen."/>
	<entry name="SpaceMarines/PrimarisHellblaster:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Broken#0" value="Letzter Widerstand, also…"/>
	<entry name="SpaceMarines/PrimarisHellblaster:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Hurt#0" value="Unvermeidbar."/>
	<entry name="SpaceMarines/PrimarisHellblaster:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#0" value="Die Reliquien werden gekühlt."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#1" value="Heilige Fusion."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#2" value="Sie werden diese Welt nicht bekommen."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#3" value="Eindämmungsfelder werden stabilisiert."/>
	<entry name="SpaceMarines/PrimarisHellblaster:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Shaken#0" value="Die Spulen… werden instabil."/>
	<entry name="SpaceMarines/PrimarisHellblaster:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Victory#0" value="Gegner verbrannt."/>
	<entry name="SpaceMarines/PrimarisHellblaster:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#0" value="Orbitale Unterstützung."/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#1" value="Brückenkopf wird etabliert."/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#2" value="Wir greifen ein!"/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#3" value="Ein Regen feuriger Vergeltung!"/>
	<entry name="SpaceMarines/PrimarisInceptor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInceptor:Broken#0" value="In den Himmel!"/>
	<entry name="SpaceMarines/PrimarisInceptor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Hurt#0" value="Wir verschwinden!"/>
	<entry name="SpaceMarines/PrimarisInceptor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#0" value="Warten auf Landungsbefehl."/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#1" value="Bereit zuzuschlagen."/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#2" value="Für den Orden!"/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#3" value="Keine Ungezählten Söhne mehr."/>
	<entry name="SpaceMarines/PrimarisInceptor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInceptor:Shaken#0" value="Konzentration!"/>
	<entry name="SpaceMarines/PrimarisInceptor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Victory#0" value="Wo auch immer wir gebraucht werden…"/>
	<entry name="SpaceMarines/PrimarisInceptor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:AlliedUnit:SpaceMarines/TacticalSpaceMarine#0" value="… alte Brüder…"/>
	<entry name="SpaceMarines/PrimarisIntercessor:AlliedUnit:SpaceMarines/TacticalSpaceMarineCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#0" value="Boltgewehre, Brüder."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#1" value="Standardmäßiges Feuer-Template."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#2" value="Abwehrfeuer!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#3" value="Haltet Ausschau nach Nachzüglern!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Broken#0" value="Geschlossen zurückziehen!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Hurt#0" value="Ein Bruder ist gefallen. Ich wiederhole…"/>
	<entry name="SpaceMarines/PrimarisIntercessor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#0" value="In Seinem Namen."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#1" value="Einst waren wir alle Grauschilde."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#2" value="Gegen Häresie gefeit!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#3" value="Für die Primarchen!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Shaken#0" value="Haltet durch, Brüder!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Victory#0" value="Trupp fordert neue Ziele an."/>
	<entry name="SpaceMarines/PrimarisIntercessor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#0" value="Schießen! Vorwärts!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#1" value="Infanterieabwehr."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#2" value="Feind wird angegriffen!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#3" value="Eskortierung läuft."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Broken#0" value="Wir stellen das Kämpfen ein."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Hurt#0" value="Wir werden beschossen!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#0" value="Turmdrehung wird überprüft."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#1" value="Das muss sich ein Techmarine genauer ansehen."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#2" value="Halten Ausschau nach Orks."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#3" value="Es ist zu ruhig…"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Shaken#0" value="Weiter!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Victory#0" value="Feind eliminiert. Wir kehren um."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#0" value="Hauptgeschütze werden abgefeuert."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#1" value="Wird ausgeführt!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#2" value="Xenos, Häretiker… sie alle sterben!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#3" value="Beim Codex!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Broken#0" value="Beim Imperator!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Hurt#0" value="Wannendurchschuss! Weiterfeuern!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#0" value="Alle Kanonen werden überprüft."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#1" value="Munition wird verstaut."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#2" value="Panzerung wird instand gesetzt."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#3" value="Ein Panzer, der eines Astartes würdig ist!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Shaken#0" value="Unvorstellbar!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Victory#0" value="Unausweichlich."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:VictoryCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#0" value="Makro… Plasma… Brenner!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#1" value="Ich bin… Sein Zorn!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#2" value="Unsere Stunde… ist gekommen!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#3" value="Stell dich… mir!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:AttackCount" value="4"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Broken#0" value="Bleibt standhaft und kämpft… Brüder!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:BrokenCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Hurt#0" value="Nur… im Tod…"/>
	<entry name="SpaceMarines/RedemptorDreadnought:HurtCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#0" value="Die Erlösung… steht bevor."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#1" value="So… lange her."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#2" value="Die Pflicht… endet nie."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#3" value="Auf… in die Schlacht."/>
	<entry name="SpaceMarines/RedemptorDreadnought:IdleCount" value="4"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Shaken#0" value="Imperator…"/>
	<entry name="SpaceMarines/RedemptorDreadnought:ShakenCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Victory#0" value="Die Rüstung… bleibt unbezwinglich."/>
	<entry name="SpaceMarines/RedemptorDreadnought:VictoryCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Attack#0" value="Wir schlagen zu, schnell und entschlossen!"/>
	<entry name="SpaceMarines/ScoutBiker:AttackCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Broken#0" value="Wir ziehen uns in sichere Entfernung zurück."/>
	<entry name="SpaceMarines/ScoutBiker:BrokenCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Hurt#0" value="Dafür haben wir nicht genug Panzerung."/>
	<entry name="SpaceMarines/ScoutBiker:HurtCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Idle#0" value="Was nützt ein Späher, der nichts zu tun hat?"/>
	<entry name="SpaceMarines/ScoutBiker:IdleCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Shaken#0" value="Wir müssen in Bewegung bleiben."/>
	<entry name="SpaceMarines/ScoutBiker:ShakenCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Victory#0" value="Wieder ein schneller Sieg…"/>
	<entry name="SpaceMarines/ScoutBiker:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Attack#0" value="Wir leisten Luftunterstützung und greifen an."/>
	<entry name="SpaceMarines/StormravenGunship:AttackCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Broken#0" value="Wir kehren zurück zum Stützpunkt."/>
	<entry name="SpaceMarines/StormravenGunship:BrokenCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Hurt#0" value="Flugabwehrfeuer!"/>
	<entry name="SpaceMarines/StormravenGunship:Hurt#1" value="Die Triebwerke sind hinüber!"/>
	<entry name="SpaceMarines/StormravenGunship:HurtCount" value="2"/>
	<entry name="SpaceMarines/StormravenGunship:Idle#0" value="Wie lauten Ihre Befehle?"/>
	<entry name="SpaceMarines/StormravenGunship:Idle#1" value="Wir kreisen durch die Lüfte und halten nach Zielen Ausschau."/>
	<entry name="SpaceMarines/StormravenGunship:Idle#2" value="Der Himmel hat Augen…"/>
	<entry name="SpaceMarines/StormravenGunship:IdleCount" value="3"/>
	<entry name="SpaceMarines/StormravenGunship:Shaken#0" value="Als würde man gegen einen Berg knallen, autsch…"/>
	<entry name="SpaceMarines/StormravenGunship:ShakenCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Victory#0" value="Ich kann da unten nichts mehr sehen."/>
	<entry name="SpaceMarines/StormravenGunship:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#0" value="Schnell angreifen."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#1" value="Wir schlagen von oben zu."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#2" value="Lasst keine Xenos oder Häretiker am Leben."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#3" value="Keine Gnade."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:AttackCount" value="4"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Broken#0" value="Abbruch!"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:BrokenCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Hurt#0" value="Bringen Sie uns außer Reichweite!"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:HurtCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#0" value="Pflichterfüllung bis zum Tod."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#1" value="Es ist eine Ehre, die Speerspitze zu sein."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#2" value="Ihre Panzer fürchten uns."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#3" value="Müßiggang ist Häresie."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:IdleCount" value="4"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Shaken#0" value="Welche Befehle, Kommandeur…?"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:ShakenCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Victory#0" value="Kümmern uns bereits um das nächste Ziel."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormtalonGunship" value="SpaceMarines/StormravenGunship"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Artefact#0" value="Was ist das für ein zwielichtiges Xenos-Konstrukt?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:ArtefactCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#0" value="Fresst Bolzengeschosse!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#1" value="Feind wird angegriffen!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#2" value="Ruhm und Ehre dem Imperium!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#3" value="Ich bin eure Erlösung!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#4" value="Space Marines, angreifen!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#5" value="Spürt den Zorn des Imperators!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#6" value="Wir sind die Faust von Guillaume."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#7" value="Ihr verpestet diese Welt, Abschaum!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:AttackCount" value="8"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#0" value="Zu den Verteidigungsstellungen zurückfallen lassen!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#1" value="Kämpft bis zum letzten Marine!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#2" value="Die Gensaat muss bewahrt werden!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#3" value="Der Imperator hat uns im Stich gelassen!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#4" value="Wir bringen Schande über unseren Orden!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:BrokenCount" value="5"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Cover#0" value="Der Imperator beschützt all jene, die sich selbst beschützen."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:CoverCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:AstraMilitarum#0" value="Selbst als Bündnispartner kann man der Armee nicht trauen."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:Necrons#0" value="Uralte, wiederkehrende Schrecken? Pah! Die Herzen der Menschen können weitaus dunkler sein."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:Orks#0" value="Wie kann es dieser grüne Abschaum wagen, in UNSERE Heimatwelt einzufallen?!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:SpaceMarines#0" value="Verräter, auf unserem Heimatplaneten?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/CatachanDevil#0" value="Ein Teufel! Bleibt auf Abstand und zielt auf die Beine."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/Enslaver#0" value="Diese verfluchten Puppenspieler! Wo sind die Librarians?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KastelanRobot#0" value="Unsere eigenen Schöpfungen erheben sich gegen uns."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="Das elendige Chaos, hier, auf unserem Planeten? Die Inquisition muss Bescheid wissen!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/Psychneuein#0" value="Space Marines, versiegelt eure Anzüge, um nicht befallen zu werden."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Hurt#0" value="Wir kämpfen bis zum letzten Marine."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Hurt#1" value="Wir… werden… nicht… scheitern!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:HurtCount" value="2"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#0" value="Alle Ketzer sollen brennen!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#1" value="Ich sehne mich nach dem Schlachtfeld."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#2" value="Sie sollen nur kommen… Ich werde langsam ungeduldig."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#3" value="So viele Xenos… und so wenige Boltgeschosse."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#4" value="Wir sind die Faust des Imperators."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#5" value="Wir kämpfen und werden triumphieren!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#6" value="Wir sollten die Xenos-Artefakte nicht anrühren, mein Lord."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#7" value="Wie lauten Ihre Befehle, mein Lord?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:IdleCount" value="8"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory0#0" value="Wenig Munition… Jeder Schuss muss sitzen."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory0Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory1#0" value="Der Chief Librarian sagt, dass man den Soldaten nicht trauen kann. Wir werden tun, was wir tun müssen."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory1Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory2#0" value="Der Heimatplanet ist… hohl?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory2Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory3#0" value="Die Xenos studieren? Mein Lord, steht das wirklich im Codex Astartes?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory3Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory4#0" value="Ich habe keine Furcht, nicht einmal vor einem Exterminatus."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory4Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory5#0" value="Die Gensaat wird uns alle überdauern – sie muss!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory5Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#0" value="… Gesegnet ist der Geist, der zu klein für Zweifel ist."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#1" value="Wir werden gegen diese Dunkelheit obsiegen…"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#2" value="Ich bin ein unzerstörbarer Schild, gefertigt für den Kampf gegen die Finsternis."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#3" value="Ich… glaube an den Imperator. Credo!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#4" value="Wir müssen uns unserer Furcht erwehren, während der Feind Atem schöpft."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:ShakenCount" value="5"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#0" value="Alle Xenos und Verräter sind Geschwüre, die aus der Galaxie entfernt werden müssen."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#1" value="Der Wunsch des Lord Commanders ist uns stets Befehl."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#2" value="Blut, das an unseren Händen klebt, beschmutzt nicht unsere Ehre."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#3" value="Im Namen des Imperators."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#4" value="Meine in Ceramit geballte Faust schlägt mit der Kraft eines ganzen Ordens zu!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#5" value="Letzten Endes gehen alle Feinde des Imperiums zugrunde."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#6" value="Mit der Vernichtung unserer Feinde verdienen wir uns unsere Erlösung."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#7" value="Wir sind die fleischgewordene Rache des Imperators!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:VictoryCount" value="8"/>
	<entry name="SpaceMarines/Terminator" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/Vindicator" value="SpaceMarines/Hunter"/>
	<entry name="Tau/BroadsideBattlesuit" value="Tau/CrisisBattlesuit"/>
	<entry name="Tau/BuilderDrone:Attack#0" value="Das ist eine ineffiziente Ressourcennutzung…"/>
	<entry name="Tau/BuilderDrone:Attack#1" value="Nicht ratsamer Angriff wird durchgeführt."/>
	<entry name="Tau/BuilderDrone:Attack#2" value="Verursache minimalen Schaden."/>
	<entry name="Tau/BuilderDrone:AttackCount" value="3"/>
	<entry name="Tau/BuilderDrone:Broken#0" value="Einheit in Gefahr."/>
	<entry name="Tau/BuilderDrone:BrokenCount" value="1"/>
	<entry name="Tau/BuilderDrone:Hurt#0" value="Einheit beschädigt."/>
	<entry name="Tau/BuilderDrone:HurtCount" value="1"/>
	<entry name="Tau/BuilderDrone:Idle#0" value="Abschaltvorgang läuft."/>
	<entry name="Tau/BuilderDrone:Idle#1" value="Evaluiere Schaltpläne…"/>
	<entry name="Tau/BuilderDrone:Idle#2" value="Ich diene der Erdkaste und dem Höheren Wohl."/>
	<entry name="Tau/BuilderDrone:IdleCount" value="3"/>
	<entry name="Tau/BuilderDrone:Shaken#0" value="Baueinheit unter Beschuss."/>
	<entry name="Tau/BuilderDrone:ShakenCount" value="1"/>
	<entry name="Tau/BuilderDrone:Victory#0" value="T'au-Overengineering in Aktion."/>
	<entry name="Tau/BuilderDrone:VictoryCount" value="1"/>
	<entry name="Tau/CadreFireblade:Attack#0" value="Feuerlinie, Angriff!"/>
	<entry name="Tau/CadreFireblade:Attack#1" value="Feuerkrieger, lehrt sie das Höhere Wohl!"/>
	<entry name="Tau/CadreFireblade:Attack#2" value="Spürt die Macht der Pulstechnologie!"/>
	<entry name="Tau/CadreFireblade:Attack#3" value="Wartet, bis ihr das Weiß in ihren Augen seht… durch eure Zielfernrohre."/>
	<entry name="Tau/CadreFireblade:AttackCount" value="4"/>
	<entry name="Tau/CadreFireblade:Broken#0" value="Was Puretide wohl denken würde…?"/>
	<entry name="Tau/CadreFireblade:BrokenCount" value="1"/>
	<entry name="Tau/CadreFireblade:Hurt#0" value="Es gehört schon mehr dazu, mich zu vernichten!"/>
	<entry name="Tau/CadreFireblade:HurtCount" value="1"/>
	<entry name="Tau/CadreFireblade:Idle#0" value="Schießübung, Shas'el, angetreten!"/>
	<entry name="Tau/CadreFireblade:Idle#1" value="Wir führen die Truppen direkt an der Front."/>
	<entry name="Tau/CadreFireblade:Idle#2" value="Ein guter Anführer bleibt stets demütig."/>
	<entry name="Tau/CadreFireblade:IdleCount" value="3"/>
	<entry name="Tau/CadreFireblade:Shaken#0" value="So sollte das nicht sein…"/>
	<entry name="Tau/CadreFireblade:ShakenCount" value="1"/>
	<entry name="Tau/CadreFireblade:Victory#0" value="Taktisch clever."/>
	<entry name="Tau/CadreFireblade:VictoryCount" value="1"/>
	<entry name="Tau/Commander:Attack#0" value="Für Aun'Va!"/>
	<entry name="Tau/Commander:Attack#1" value="Wir werden euch den Weg weisen…"/>
	<entry name="Tau/Commander:Attack#2" value="Schließt euch uns an!"/>
	<entry name="Tau/Commander:AttackCount" value="3"/>
	<entry name="Tau/Commander:Broken#0" value="Rückzug ist die einzige Option…"/>
	<entry name="Tau/Commander:BrokenCount" value="1"/>
	<entry name="Tau/Commander:Hurt#0" value="Kampfanzug beschädigt."/>
	<entry name="Tau/Commander:HurtCount" value="1"/>
	<entry name="Tau/Commander:Idle#0" value="Ich will dienen, für das Höhere Wohl."/>
	<entry name="Tau/Commander:Idle#1" value="Ich denke stets einen Schritt voraus."/>
	<entry name="Tau/Commander:Idle#2" value="Was ist schon das Leben eines Einzelnen gemessen am Höheren Wohl?"/>
	<entry name="Tau/Commander:IdleCount" value="3"/>
	<entry name="Tau/Commander:Shaken#0" value="Ich bleibe standhaft!"/>
	<entry name="Tau/Commander:ShakenCount" value="1"/>
	<entry name="Tau/Commander:Victory#0" value="Die Feuerkaste steht ganz oben."/>
	<entry name="Tau/Commander:VictoryCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Attack#0" value="Kampfanzüge im Einsatz."/>
	<entry name="Tau/CrisisBattlesuit:Attack#1" value="Feuern mit Pulswaffen."/>
	<entry name="Tau/CrisisBattlesuit:Attack#2" value="Gegenmaßnahmen werden aktiviert."/>
	<entry name="Tau/CrisisBattlesuit:Attack#3" value="Sie können sich auf uns verlassen!"/>
	<entry name="Tau/CrisisBattlesuit:AttackCount" value="4"/>
	<entry name="Tau/CrisisBattlesuit:Broken#0" value="Elite… Rückzug."/>
	<entry name="Tau/CrisisBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Hurt#0" value="Wir verlieren Systeme!"/>
	<entry name="Tau/CrisisBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Idle#0" value="Stets bereit."/>
	<entry name="Tau/CrisisBattlesuit:Idle#1" value="Wir tun, was man uns aufträgt."/>
	<entry name="Tau/CrisisBattlesuit:Idle#2" value="Wie lautet der Wille der Himmlischen?"/>
	<entry name="Tau/CrisisBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/CrisisBattlesuit:Shaken#0" value="Wir können diese Position nicht halten."/>
	<entry name="Tau/CrisisBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Victory#0" value="Deshalb wirft man Kampfanzüge in die Schlacht."/>
	<entry name="Tau/CrisisBattlesuit:Victory#1" value="Todesstoß versetzt."/>
	<entry name="Tau/CrisisBattlesuit:Victory#2" value="Haben die nichts… Zäheres zu bieten?"/>
	<entry name="Tau/CrisisBattlesuit:Victory#3" value="Der geht auch auf unser Konto, Shas'el."/>
	<entry name="Tau/CrisisBattlesuit:VictoryCount" value="4"/>
	<entry name="Tau/Devilfish:Attack#0" value="Nähern uns dem Feind."/>
	<entry name="Tau/Devilfish:Attack#1" value="Wir werden unsere Gegner lehren, alle T'au zu fürchten!"/>
	<entry name="Tau/Devilfish:Attack#2" value="Spürt meinen Zorn!"/>
	<entry name="Tau/Devilfish:Attack#3" value="Alle Waffen feuern."/>
	<entry name="Tau/Devilfish:AttackCount" value="4"/>
	<entry name="Tau/Devilfish:Broken#0" value="Wir stecken in der Klemme!"/>
	<entry name="Tau/Devilfish:BrokenCount" value="1"/>
	<entry name="Tau/Devilfish:Hurt#0" value="Viel mehr Treffer wie diese stecken wir nicht weg!"/>
	<entry name="Tau/Devilfish:HurtCount" value="1"/>
	<entry name="Tau/Devilfish:Idle#0" value="Bereit für Ihre Befehle."/>
	<entry name="Tau/Devilfish:Idle#1" value="Ich warte."/>
	<entry name="Tau/Devilfish:Idle#2" value="Überprüfe Chips – dieser hier wurde ziemlich geröstet."/>
	<entry name="Tau/Devilfish:IdleCount" value="3"/>
	<entry name="Tau/Devilfish:Shaken#0" value="Sie drehen den Spieß um!"/>
	<entry name="Tau/Devilfish:ShakenCount" value="1"/>
	<entry name="Tau/Devilfish:Victory#0" value="Sie sind jetzt an einem besseren Ort."/>
	<entry name="Tau/Devilfish:Victory#1" value="Zeit umzukehren."/>
	<entry name="Tau/Devilfish:VictoryCount" value="2"/>
	<entry name="Tau/Ethereal:Attack#0" value="Das ist nur zu eurem Besten."/>
	<entry name="Tau/Ethereal:Attack#1" value="Für die T'au!"/>
	<entry name="Tau/Ethereal:Attack#2" value="Warum seid ihr uns feindlich gesonnen?"/>
	<entry name="Tau/Ethereal:AttackCount" value="3"/>
	<entry name="Tau/Ethereal:Broken#0" value="Vielleicht… finden wir nur hinter unseren eigenen Linien Frieden."/>
	<entry name="Tau/Ethereal:BrokenCount" value="1"/>
	<entry name="Tau/Ethereal:Hurt#0" value="Mein Fleisch ist schwach, doch mein Geist wird triumphieren!"/>
	<entry name="Tau/Ethereal:HurtCount" value="1"/>
	<entry name="Tau/Ethereal:Idle#0" value="Ich predige das Gute."/>
	<entry name="Tau/Ethereal:Idle#1" value="Verständnis erlangt man in der Meditation."/>
	<entry name="Tau/Ethereal:Idle#2" value="Das Gute allein genügt uns nicht – das Höhere Wohl ist es, wonach wir streben."/>
	<entry name="Tau/Ethereal:IdleCount" value="3"/>
	<entry name="Tau/Ethereal:Shaken#0" value="Beschützt mich!"/>
	<entry name="Tau/Ethereal:ShakenCount" value="1"/>
	<entry name="Tau/Ethereal:Victory#0" value="Wir verbreiten unsere Botschaft."/>
	<entry name="Tau/Ethereal:Victory#1" value="Ich trauere um jede verlorene Seele."/>
	<entry name="Tau/Ethereal:Victory#2" value="Auch das war für das Höhere Wohl."/>
	<entry name="Tau/Ethereal:VictoryCount" value="3"/>
	<entry name="Tau/FireWarrior:Attack#0" value="Feuern auf Ziele!"/>
	<entry name="Tau/FireWarrior:Attack#1" value="Im Kampf!"/>
	<entry name="Tau/FireWarrior:Attack#2" value="Wir sind die Feuerkaste!"/>
	<entry name="Tau/FireWarrior:Attack#3" value="Auf Distanz bleiben!"/>
	<entry name="Tau/FireWarrior:Attack#4" value="Gebt euch Feuerschutz!"/>
	<entry name="Tau/FireWarrior:Attack#5" value="Im Gedenken an Puretide!"/>
	<entry name="Tau/FireWarrior:AttackCount" value="6"/>
	<entry name="Tau/FireWarrior:Broken#0" value="Zurückfallen lassen! Lauft!"/>
	<entry name="Tau/FireWarrior:BrokenCount" value="1"/>
	<entry name="Tau/FireWarrior:Hurt#0" value="Wir werden dezimiert!"/>
	<entry name="Tau/FireWarrior:HurtCount" value="1"/>
	<entry name="Tau/FireWarrior:Idle#0" value="Wir ruhen uns aus."/>
	<entry name="Tau/FireWarrior:Idle#1" value="Wir üben, Shas'el."/>
	<entry name="Tau/FireWarrior:Idle#2" value="Ich vermisse meine Sept…"/>
	<entry name="Tau/FireWarrior:Idle#3" value="Erwähnen wir nie wieder den Subspace…"/>
	<entry name="Tau/FireWarrior:Idle#4" value="Bereit zuzuschlagen, Shas'el."/>
	<entry name="Tau/FireWarrior:IdleCount" value="5"/>
	<entry name="Tau/FireWarrior:Shaken#0" value="Sie dürfen uns nicht zu nahe kommen!"/>
	<entry name="Tau/FireWarrior:ShakenCount" value="1"/>
	<entry name="Tau/FireWarrior:Victory#0" value="Mit Feuerüberlegenheit zum Sieg!"/>
	<entry name="Tau/FireWarrior:Victory#1" value="Die Feuerkaste beweist ihre Macht!"/>
	<entry name="Tau/FireWarrior:VictoryCount" value="2"/>
	<entry name="Tau/GhostkeelBattlesuit" value="Tau/CrisisBattlesuit"/>
	<entry name="Tau/GravInhibitorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/GunDrone:Attack#0" value="Feuerlösung gefunden."/>
	<entry name="Tau/GunDrone:Attack#1" value="Drohne im Einsatz."/>
	<entry name="Tau/GunDrone:Attack#2" value="Wir kämpfen für das Höhere Wohl!"/>
	<entry name="Tau/GunDrone:Attack#3" value="Logikbeschränkung erreicht. Angriff erfolgt."/>
	<entry name="Tau/GunDrone:Attack#4" value="Mit 89%iger Wahrscheinlichkeit Gegner identifiziert. Angriff erfolgt."/>
	<entry name="Tau/GunDrone:AttackCount" value="5"/>
	<entry name="Tau/GunDrone:Broken#0" value="Versuche, unversehrt zu bleiben."/>
	<entry name="Tau/GunDrone:BrokenCount" value="1"/>
	<entry name="Tau/GunDrone:Hurt#0" value="Einsatzfähigkeit auf 50% gesunken."/>
	<entry name="Tau/GunDrone:HurtCount" value="1"/>
	<entry name="Tau/GunDrone:Idle#0" value="Patrouilleneinsatz."/>
	<entry name="Tau/GunDrone:Idle#1" value="Das Höhere Wohl folgt einer Logik."/>
	<entry name="Tau/GunDrone:Idle#2" value="Gebaut von der Erdkaste."/>
	<entry name="Tau/GunDrone:Idle#3" value="Subroutinen im Energiesparmodus."/>
	<entry name="Tau/GunDrone:Idle#4" value="Fe-Fehler we-werden be-behoben."/>
	<entry name="Tau/GunDrone:IdleCount" value="5"/>
	<entry name="Tau/GunDrone:Shaken#0" value="Ungeeignete Routinen. Zurück zu ausfallsicheren Routinen."/>
	<entry name="Tau/GunDrone:ShakenCount" value="1"/>
	<entry name="Tau/GunDrone:Victory#0" value="Einsatzziel erfüllt. Flug wird fortgesetzt."/>
	<entry name="Tau/GunDrone:VictoryCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Attack#0" value="Antigravpanzer feuert, Feind wird eliminiert."/>
	<entry name="Tau/HammerheadGunship:Attack#1" value="Wir verringern die Höhe."/>
	<entry name="Tau/HammerheadGunship:Attack#2" value="Leisten Feuerunterstützung."/>
	<entry name="Tau/HammerheadGunship:AttackCount" value="3"/>
	<entry name="Tau/HammerheadGunship:Broken#0" value="Antigravpanzer zieht sich zurück."/>
	<entry name="Tau/HammerheadGunship:BrokenCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Hurt#0" value="Wir wurden getroffen, sind aber noch einsatztauglich."/>
	<entry name="Tau/HammerheadGunship:HurtCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Idle#0" value="Bereit für Fernbeschuss."/>
	<entry name="Tau/HammerheadGunship:Idle#1" value="Sagen Sie Bescheid, wenn Sie den Vogel brauchen."/>
	<entry name="Tau/HammerheadGunship:Idle#2" value="Ky'husa, hier? Gehört mir nicht, Sir…"/>
	<entry name="Tau/HammerheadGunship:IdleCount" value="3"/>
	<entry name="Tau/HammerheadGunship:Shaken#0" value="Wir gehören nicht an vorderste Front!"/>
	<entry name="Tau/HammerheadGunship:ShakenCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Victory#0" value="Frühes Mont'ka fängt den Wurm…"/>
	<entry name="Tau/HammerheadGunship:Victory#1" value="Feind ausgelöscht."/>
	<entry name="Tau/HammerheadGunship:Victory#2" value="Halten nach neuen Zielen Ausschau."/>
	<entry name="Tau/HammerheadGunship:VictoryCount" value="3"/>
	<entry name="Tau/InterceptorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/MarkerDrone" value="Tau/GunDrone"/>
	<entry name="Tau/Pathfinder" value="Tau/FireWarrior"/>
	<entry name="Tau/Piranha:Attack#0" value="Bündelkanone wird abgefeuert."/>
	<entry name="Tau/Piranha:Attack#1" value="Gibt es Späher, die Unterstützung brauchen?"/>
	<entry name="Tau/Piranha:Attack#2" value="Schlagen schnell zu."/>
	<entry name="Tau/Piranha:Attack#3" value="Leiten Tieffliegerangriff ein."/>
	<entry name="Tau/Piranha:AttackCount" value="4"/>
	<entry name="Tau/Piranha:Broken#0" value="Gepanzerter Späher lässt sich zurückfallen."/>
	<entry name="Tau/Piranha:BrokenCount" value="1"/>
	<entry name="Tau/Piranha:Hurt#0" value="Ausweichmanöver fehlgeschlagen, hohen Schaden erlitten."/>
	<entry name="Tau/Piranha:HurtCount" value="1"/>
	<entry name="Tau/Piranha:Idle#0" value="Gibt es etwas Neues von den Spähern?"/>
	<entry name="Tau/Piranha:Idle#1" value="Aufklärungseinheit wartet auf Befehle."/>
	<entry name="Tau/Piranha:IdleCount" value="2"/>
	<entry name="Tau/Piranha:Shaken#0" value="Warum funktioniert nichts? Warum--"/>
	<entry name="Tau/Piranha:ShakenCount" value="1"/>
	<entry name="Tau/Piranha:Victory#0" value="Ziel… zerfetzt, Shas'el."/>
	<entry name="Tau/Piranha:VictoryCount" value="1"/>
	<entry name="Tau/PulseAcceleratorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#0" value="Luftunterstützung ist unterwegs."/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#1" value="Luftkaste, für das Höhere Wohl!"/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#2" value="Mit uns haben sie nicht gerechnet…"/>
	<entry name="Tau/RazorsharkStrikeFighter:AttackCount" value="3"/>
	<entry name="Tau/RazorsharkStrikeFighter:Broken#0" value="Ich fliege noch…"/>
	<entry name="Tau/RazorsharkStrikeFighter:BrokenCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Hurt#0" value="Feindlicher Beschuss setzt uns zu. Brauchen neue Befehle!"/>
	<entry name="Tau/RazorsharkStrikeFighter:HurtCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#0" value="Wir drehen unsere Runden und warten auf Befehle."/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#1" value="Sollen wir zum Stützpunkt zurückkehren?"/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#2" value="Wie sieht es mit unserer Beförderung aus?"/>
	<entry name="Tau/RazorsharkStrikeFighter:IdleCount" value="3"/>
	<entry name="Tau/RazorsharkStrikeFighter:Shaken#0" value="Ausweichmanöver!"/>
	<entry name="Tau/RazorsharkStrikeFighter:ShakenCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#0" value="Aufklärer vermelden: Gegner abgeschossen."/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#1" value="DAS nennt man Lufthoheit…"/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#2" value="Wir drehen ab, Mission erfüllt."/>
	<entry name="Tau/RazorsharkStrikeFighter:VictoryCount" value="3"/>
	<entry name="Tau/ReconDrone" value="Tau/GunDrone"/>
	<entry name="Tau/RiptideBattlesuit:Attack#0" value="Feuern auf alles."/>
	<entry name="Tau/RiptideBattlesuit:Attack#1" value="Warum schießen sie überhaupt zurück?"/>
	<entry name="Tau/RiptideBattlesuit:Attack#2" value="Wehe, der Fio'tak bekommt auch nur einen Kratzer ab…"/>
	<entry name="Tau/RiptideBattlesuit:AttackCount" value="3"/>
	<entry name="Tau/RiptideBattlesuit:Broken#0" value="Schilddrohnen müssen mich abschirmen!"/>
	<entry name="Tau/RiptideBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Hurt#0" value="Reaktorschaden, Panzerung beschädigt. Setze Einsatz fort."/>
	<entry name="Tau/RiptideBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Idle#0" value="Ich hoffe für Sie, dass Sie etwas mit mir vorhaben."/>
	<entry name="Tau/RiptideBattlesuit:Idle#1" value="Zzzzz…"/>
	<entry name="Tau/RiptideBattlesuit:Idle#2" value="Solch überwältigende Feuerkraft…"/>
	<entry name="Tau/RiptideBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/RiptideBattlesuit:Shaken#0" value="Das Beste vom Besten. Das. Beste. Vom. Besten."/>
	<entry name="Tau/RiptideBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Victory#0" value="Sie sind tot. Setze Beschuss fort."/>
	<entry name="Tau/RiptideBattlesuit:Victory#1" value="Hat jemand gesehen, wo mein Ziel hin ist?"/>
	<entry name="Tau/RiptideBattlesuit:VictoryCount" value="2"/>
	<entry name="Tau/ShieldDrone" value="Tau/GunDrone"/>
	<entry name="Tau/ShieldedMissileDrone" value="Tau/GunDrone"/>
	<entry name="Tau/SkyRayGunship" value="Tau/HammerheadGunship"/>
	<entry name="Tau/StealthBattlesuit:Attack#0" value="Sie wissen nicht, wie ihnen geschieht!"/>
	<entry name="Tau/StealthBattlesuit:Attack#1" value="Aus den Schatten…"/>
	<entry name="Tau/StealthBattlesuit:Attack#2" value="Shas'el, wir haben sie."/>
	<entry name="Tau/StealthBattlesuit:AttackCount" value="3"/>
	<entry name="Tau/StealthBattlesuit:Broken#0" value="Fordern Hilfe für Ausschleusung an."/>
	<entry name="Tau/StealthBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Hurt#0" value="Wie schaffen sie es, uns zu treffen?"/>
	<entry name="Tau/StealthBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Idle#0" value="Erhalten die Deckung aufrecht."/>
	<entry name="Tau/StealthBattlesuit:Idle#1" value="Geist-Team, Funkstille."/>
	<entry name="Tau/StealthBattlesuit:Idle#2" value="Uns können Sie nicht meinen, wir sind doch gar nicht hier…"/>
	<entry name="Tau/StealthBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/StealthBattlesuit:Shaken#0" value="Haben wir zu viel riskiert?"/>
	<entry name="Tau/StealthBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Victory#0" value="Wir verbreiten Angst und Schrecken."/>
	<entry name="Tau/StealthBattlesuit:Victory#1" value="Ziel eliminiert."/>
	<entry name="Tau/StealthBattlesuit:Victory#2" value="Wir folgen unserer eigenen Methodik."/>
	<entry name="Tau/StealthBattlesuit:VictoryCount" value="3"/>
	<entry name="Tau/StealthDrone" value="Tau/GunDrone"/>
	<entry name="Tau/Stormsurge" value="Tau/RiptideBattlesuit"/>
	<entry name="Tau/SunSharkBomber" value="Tau/RazorsharkStrikeFighter"/>
	<entry name="Tyranids/Carnifex:Attack#0" value="Krrkkk!"/>
	<entry name="Tyranids/Carnifex:Attack#1" value="Rrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#2" value="Nnnnn!"/>
	<entry name="Tyranids/Carnifex:Attack#3" value="Srrr!"/>
	<entry name="Tyranids/Carnifex:Attack#4" value="Krk!"/>
	<entry name="Tyranids/Carnifex:Attack#5" value="Rrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#6" value="Nrrrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#7" value="Vrrrk!"/>
	<entry name="Tyranids/Carnifex:AttackCount" value="8"/>
	<entry name="Tyranids/Carnifex:Broken#0" value="… Mmmm…"/>
	<entry name="Tyranids/Carnifex:Broken#1" value="… Thhhh…"/>
	<entry name="Tyranids/Carnifex:Broken#2" value="… Phhhh…"/>
	<entry name="Tyranids/Carnifex:Broken#3" value="… Sssss…"/>
	<entry name="Tyranids/Carnifex:BrokenCount" value="4"/>
	<entry name="Tyranids/Carnifex:EnemyFaction:Necrons#0" value="Nrnnnnn."/>
	<entry name="Tyranids/Carnifex:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Tyranids/Carnifex:Hurt#0" value="Sssssss!"/>
	<entry name="Tyranids/Carnifex:HurtCount" value="1"/>
	<entry name="Tyranids/Carnifex:Idle#0" value="Krrck."/>
	<entry name="Tyranids/Carnifex:Idle#1" value="Nrrrk."/>
	<entry name="Tyranids/Carnifex:Idle#2" value="Rrrk."/>
	<entry name="Tyranids/Carnifex:Idle#3" value="Kckckc."/>
	<entry name="Tyranids/Carnifex:IdleCount" value="4"/>
	<entry name="Tyranids/Carnifex:Shaken#0" value="Rrhhrrr!"/>
	<entry name="Tyranids/Carnifex:ShakenCount" value="1"/>
	<entry name="Tyranids/Carnifex:Victory#0" value="Hwlllllll!"/>
	<entry name="Tyranids/Carnifex:Victory#1" value="Iaiaiaia!"/>
	<entry name="Tyranids/Carnifex:Victory#2" value="Ulululu!"/>
	<entry name="Tyranids/Carnifex:Victory#3" value="Phphph!"/>
	<entry name="Tyranids/Carnifex:Victory#4" value="Ulllllla!"/>
	<entry name="Tyranids/Carnifex:VictoryCount" value="5"/>
	<entry name="Tyranids/Exocrine" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Gargoyle" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Haruspex" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Headquarters" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/HiveCrone" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/HiveTyrant" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Hormagaunt" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Lictor" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Malanthrope" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Ravener" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Termagant" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tervigon" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Trygon" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/TyranidPrime" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tyrannofex" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Warrior" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Zoanthrope" value="Tyranids/Carnifex"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#0" value="Spürt zehntausend Jahre voll des Zornes!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#1" value="Im Namen der Dunklen Götter!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#2" value="Kniet nieder vor eurem Chaos Lord!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#3" value="Sterbt, ihr Schwächlinge! Niederer Abschaum!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#4" value="Ruhm und Ehre dem Chaos!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Broken#0" value="Hier werde ich nicht sterben!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Broken#1" value="Zurück zum Auge!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:BrokenCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Hurt#0" value="Was, du wagst es, MIR wehzutun?!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#0" value="Wir sind untätig, obwohl noch Imperiale am Leben sind!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#1" value="Weißt du noch? Sein Blut klebte an allen Wänden."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#2" value="Welch Freude, einen gefallenen Engel zu sehen."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#3" value="Ob Geschrei oder Gesang, beides klingt für mich gleich."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#4" value="War es die Verdammung wert?"/>
	<entry name="ChaosSpaceMarines/ChaosLord:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Shaken#0" value="WER WAGT ES…?!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Shaken#1" value="Eines Tages werde ich Vergeltung üben!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:ShakenCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#0" value="Ich widme diesen Tod den Göttern."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#1" value="War das etwa alles?! Ihr Narren!"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#2" value="Was bedeutet dieser Sieg schon, solange das Imperium standhält?"/>
	<entry name="ChaosSpaceMarines/ChaosLord:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#0" value="Welcher Narr schickt uns in den Krieg?!"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#1" value="Zermahlt ihre Knochen zu Staub!"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#2" value="Fahrt schneller!"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Broken#0" value="Flieht! Wir töten sie ein andermal."/>
	<entry name="ChaosSpaceMarines/ChaosRhino:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Hurt#0" value="Wer macht denn da Löcher in unsere Panzerung?!"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Idle#0" value="Wer fährt schon gerne ein Rhino?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Idle#1" value="Wir fahren den Lord in den Krieg, gäähhn."/>
	<entry name="ChaosSpaceMarines/ChaosRhino:IdleCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Shaken#0" value="Verdammung, dafür?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Victory#0" value="Ihr könnt nicht einmal ein Rhino besiegen?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#0" value="Schreit, denn euer Untergang naht!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#1" value="Der Hass in meiner Seele ist rein. Überzeugt euch selbst davon!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#2" value="Ich war einst der Sohn des Imperators, niemand kann gegen mich bestehen!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#3" value="Hahahaha! STIRB!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Broken#0" value="Wir sind nur noch ein versprengter Haufen, wir müssen fliehen!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Hurt#0" value="Dunkle Götter, beschützt uns!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#0" value="Ob ich wohl weitere zehntausend Jahre so ertragen kann?"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#1" value="Gebt mir noch einen Zivilisten. Ich habe keine Tinte mehr."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#2" value="Warum beschützen sie immer noch ihren Leichenimperator?"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#3" value="Endloser Krieg, endlose Freude…"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Shaken#0" value="Wir… verlieren?!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#0" value="Und so fallen alle Feinde Abaddons!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#1" value="Ein weiterer Schritt zur Herrschaft über diese Galaxis… So steht es uns zu!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#2" value="Euer Tod ist für mich nicht von Belang."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#0" value="… Der Wirbel weitet sich…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#1" value="*brabbel*"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#2" value="Ahhhhaahhhh!"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Broken#0" value="…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Hurt#0" value="…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#0" value="… Wahnsinn bis in alle Ewigkeit…?"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#1" value="Hahahahahaha!"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#2" value="Mmmm mmmm…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#3" value="Selbst das Chaos kann langweilig sein…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#4" value="Nnnhhhrrr."/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Shaken#0" value="Eh…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Victory#0" value="Oho!"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#0" value="Der Warp berührt euch, meine Kleinen!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#1" value="Spürt die Macht eines angehenden Gottes!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#2" value="Spielzeug, Puppen, nieder mit euch!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#3" value="Ahahahahaha!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Broken#0" value="Das kann nicht sein! Besiegt von…"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Hurt#0" value="IHR WAGT ES, MIR DIE STIRN ZU BIETEN?!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#0" value="Was für Narren diese Sterblichen doch sind!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#1" value="Die physische Ebene… wie sehr ich sie vermisst habe!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#2" value="Welch sonderbare Welt, die nicht nach Belieben ihre Form verändert."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#3" value="Über Gladius schwebt eine sonderbare dunkle Wolke des Untergangs."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#4" value="Na los, Sklaven, unterhaltet euren Gott!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#5" value="Wenn man bedenkt, dass diese Klauen einst Menschenhände waren…"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:IdleCount" value="6"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#0" value="Ich habe tausenden Sterblichen das Herz aus der Brust gerissen… und so dankt man es mir?!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#1" value="Der Warp ruft…"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#2" value="Der Wahnsinn fordert seinen Tribut… Ich muss mich zusammenreißen!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#3" value="Für Pack wie euch gibt es eine eigene Hölle!"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:ShakenCount" value="4"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#0" value="Ein weiterer Schädel für meine Sammlung… Khorne wird neidisch sein."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#1" value="Köpfe ohne Augen… Das gefällt mir."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#2" value="Halt still, Sterblicher… Ich will deinen Tod genießen."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#0" value="Ja, ja, geht zugrunde, Sterbliche, geht zugrunde!"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#1" value="Ja, kommt näher zu meinen Krallen, kommt näher!"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#2" value="Tretet unseren Göttern gegenüber!"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#3" value="Chaos, Blut und Schädel… Für das Chaos!"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#4" value="Zerreißen und zerfleischen… Die Sterblichkeit bereitet mir große Freude!"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#5" value="Zertrümmern, zerschneiden, zerlegen, zerkleinern, frohlocken!"/>
	<entry name="ChaosSpaceMarines/Defiler:AttackCount" value="6"/>
	<entry name="ChaosSpaceMarines/Defiler:Broken#0" value="Dunkle Götter!"/>
	<entry name="ChaosSpaceMarines/Defiler:Broken#1" value="Wir verlieren, hahaha, wir verlieren!"/>
	<entry name="ChaosSpaceMarines/Defiler:BrokenCount" value="2"/>
	<entry name="ChaosSpaceMarines/Defiler:Hurt#0" value="Ja, zurück in den Warp, ja, erlöst uns."/>
	<entry name="ChaosSpaceMarines/Defiler:Hurt#1" value="Zertrümmert meinen Käfig, befreit den Dämon…"/>
	<entry name="ChaosSpaceMarines/Defiler:HurtCount" value="2"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#0" value="Wo ist der Warp?! Der Warp ist verschwunden!"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#1" value="Ich darf nicht töten, runengebunden, Hexerei. Darf nicht… will…"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#2" value="Warum nicht töten, töten, töten… Wo kann ich töten?"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#3" value="Gestank der Alten… Gestank toter Götter…"/>
	<entry name="ChaosSpaceMarines/Defiler:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/Defiler:Shaken#0" value="Haha, der Warp ruft, Freiiiiheiiit!"/>
	<entry name="ChaosSpaceMarines/Defiler:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#0" value="Sie fallen so schnell auseinander… Traurig… Gibt es mehr?"/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#1" value="So schnell kam der Tod…"/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#2" value="Für den Warp, für den Ruhm der Meister!"/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#3" value="Kleine Seele, die Schmiede ist heiß und wartet – auf dich…"/>
	<entry name="ChaosSpaceMarines/Defiler:VictoryCount" value="4"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#0" value="Vernichtung!"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#1" value="SCHIESST ZURÜCK, IHR FEIGLINGE!"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#2" value="Ein Schuss, zwei Schüsse, drei Schüsse, vier--"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#3" value="Ruhm und Ehre dem Chaos! Für das Dunkle Mechanicum! Für den Spaß an der Freude!"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#4" value="Aus der Hölle kommen wir und feuern!"/>
	<entry name="ChaosSpaceMarines/Havoc:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/Havoc:Broken#0" value="Das war nichts…"/>
	<entry name="ChaosSpaceMarines/Havoc:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Hurt#0" value="Ich bin noch nicht tot, weiterschießen!"/>
	<entry name="ChaosSpaceMarines/Havoc:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#0" value="Richte deine Waffe auf die Leute und sie hören dir gebannt zu. Sprich jedoch mit deiner Waffe und sie halten dich für einen Spinner…"/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#1" value="Plasma fließt durch meine Venen."/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#2" value="Der Warp sei gesegnet, denn er beschenkt uns reichlich."/>
	<entry name="ChaosSpaceMarines/Havoc:IdleCount" value="3"/>
	<entry name="ChaosSpaceMarines/Havoc:Shaken#0" value="Unsere Waffen, nicht eure. Aus unseren kalten, toten Fingern…"/>
	<entry name="ChaosSpaceMarines/Havoc:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Victory#0" value="In den ewigen Frieden geschossen…"/>
	<entry name="ChaosSpaceMarines/Havoc:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#0" value="HhhrrraaaaaHHHH!"/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#1" value="Graaarh!"/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#2" value="Arrrrrhhhh!"/>
	<entry name="ChaosSpaceMarines/Heldrake:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/Heldrake:Broken#0" value="Eeeeeeeeeee!"/>
	<entry name="ChaosSpaceMarines/Heldrake:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Hurt#0" value="Arkh! Arkh!"/>
	<entry name="ChaosSpaceMarines/Heldrake:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#0" value="Einst… ein Mensch."/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#1" value="Grrrrhhh…"/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#2" value="Rrrrrrr…"/>
	<entry name="ChaosSpaceMarines/Heldrake:IdleCount" value="3"/>
	<entry name="ChaosSpaceMarines/Heldrake:Shaken#0" value="Eeeeeekh!"/>
	<entry name="ChaosSpaceMarines/Heldrake:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Victory#0" value="HhhrrrrrraaaaaaaHHHHHH!"/>
	<entry name="ChaosSpaceMarines/Heldrake:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#0" value="Blut für den Blutgott!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#1" value="Khorne fordert eure Schädel!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#2" value="Meine Axt sehnt sich nach eurem Blut!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#3" value="Wehrt euch, ihr Feiglinge!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#4" value="Der Tod eines Kriegers hat nichts Ehrenhaftes!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Broken#0" value="Khorne wird uns dafür verdammen!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Hurt#0" value="Mein… mein Blut für den BLUTGOTT!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Idle#0" value="WARUM KÄMPFEN WIR NICHT?!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:IdleCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Shaken#0" value="So leicht sterben wir nicht!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#0" value="BLUT!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#1" value="KHORNE HAT EUCH ZU SICH GERUFEN!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#2" value="NUR NOCH SCHÄDEL!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#0" value="Ihr könnt euch meiner nicht erwehren!"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#1" value="Fürchtet euch nicht, ein Riss ist in Sicht."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#2" value="Mein Stab giert nach deiner Seele, doch sie ist zögerlich."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Broken#0" value="Was lebt, aber wird nicht sterben?"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Hurt#0" value="Ist das etwa mein Knochen? Argh, welch Anblick!"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#0" value="Nutzlose Menschen und Geister… Wir brauchen bessere Wirte."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#1" value="Erst der Zeremoniendolch, dann den wahren Namen des Schicksalswandlers sagen…"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#2" value="Ist jemand besessen, so ist dies bereits mehr als nur die halbe Miete…"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#3" value="Sie werden das Herz des Planeten schmecken, bevor unsere Zeit gekommen ist."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Shaken#0" value="Die Angst tötet das Bewusstsein."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Victory#0" value="Meine Dämonen flüstern, und sie werden auch dir zuflüstern."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Venomcrawler" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Attack#0" value="… Beute…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:AttackCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Broken#0" value="… Angst…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Hurt#0" value="… Schmerzen…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Idle#0" value="…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:IdleCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Shaken#0" value="… Angst…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Victory#0" value="… Tod…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#0" value="Analyse des Ziels abgeschlossen. Ihr… seid nicht perfekt."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#1" value="Cogitatoren, Mechatentakel, Waffen: einsatzbereit."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#2" value="GOTO Zieleliminierung. Ausführen."/>
	<entry name="ChaosSpaceMarines/Warpsmith:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Broken#0" value="Programm zur Schadensbegrenzung initiiert."/>
	<entry name="ChaosSpaceMarines/Warpsmith:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Hurt#0" value="Diagnose: Organischer/mechanischer FEHLER."/>
	<entry name="ChaosSpaceMarines/Warpsmith:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#0" value="Warpsmiths, der Lächerlichkeit preisgegeben!"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#1" value="Mensch, Maschine, Dämon – eine perfekte Einheit."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#2" value="Geschätzte Mechatentakel…"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#3" value="Fehler: Wiederauflebender emotionaler Kern. Löschen."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#4" value="Seelenschmiede, Götter, wir sind eure Waffen."/>
	<entry name="ChaosSpaceMarines/Warpsmith:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Shaken#0" value="Meldung: Anhaltender Schaden."/>
	<entry name="ChaosSpaceMarines/Warpsmith:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Victory#0" value="Lagebericht: Widerstand eliminiert – wie prognostiziert."/>
	<entry name="ChaosSpaceMarines/Warpsmith:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Helbrute" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/Maulerfiend" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/Obliterator" value="ChaosSpaceMarines/Havoc"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#0" value="Und so fallen alle Feinde des Imperiums!"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#1" value="Häretiker, Xenos-Abschaum… Niemand soll überleben!"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#2" value="Hochenergie-Lasergewehre im Anschlag…"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#3" value="Unser Leben für Terra!"/>
	<entry name="AstraMilitarum/TempestusScion:AttackCount" value="4"/>
	<entry name="AstraMilitarum/TempestusScion:Broken#0" value="Was für eine Schande! Sturmtruppen ziehen sich zurück!"/>
	<entry name="AstraMilitarum/TempestusScion:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Hurt#0" value="Gerne opfere ich mein Leben für den Imperator!"/>
	<entry name="AstraMilitarum/TempestusScion:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Idle#0" value="Man nennt uns „Goldjungen“, und doch geht es uns nur um den Ruhm des Imperators."/>
	<entry name="AstraMilitarum/TempestusScion:Idle#1" value="Der Regimental Standard? Keine Pflichtlektüre, aber äußerst unterhaltsam zu lesen."/>
	<entry name="AstraMilitarum/TempestusScion:Idle#2" value="Schon den Artikel über Marbo gelesen? Wie kann es ein Held wagen, die Regeln des Munitorums zu missachten?!"/>
	<entry name="AstraMilitarum/TempestusScion:Idle#3" value="Überall tobt Krieg und ich sitze nur herum. So ist das Leben…"/>
	<entry name="AstraMilitarum/TempestusScion:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TempestusScion:Shaken#0" value="Ich habe die Prüfungen der Konformität bestanden: Ich werde nicht weglaufen!"/>
	<entry name="AstraMilitarum/TempestusScion:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Victory#0" value="Ein Tag zum Feiern! Unser Krieg wird kein Ende nehmen."/>
	<entry name="AstraMilitarum/TempestusScion:VictoryCount" value="1"/>
	<entry name="Necrons/FlayedOne:Attack#0" value="… Fleisch"/>
	<entry name="Necrons/FlayedOne:Attack#1" value="… zerreißen…"/>
	<entry name="Necrons/FlayedOne:Attack#2" value="… zerreißen-zerfetzen-zerfleischen…"/>
	<entry name="Necrons/FlayedOne:AttackCount" value="3"/>
	<entry name="Necrons/FlayedOne:Broken#0" value="… fliiiiehen…"/>
	<entry name="Necrons/FlayedOne:BrokenCount" value="1"/>
	<entry name="Necrons/FlayedOne:Hurt#0" value="… Fleischwunde…"/>
	<entry name="Necrons/FlayedOne:HurtCount" value="1"/>
	<entry name="Necrons/FlayedOne:Idle#0" value="… Hunger…"/>
	<entry name="Necrons/FlayedOne:Idle#1" value="… Verweilen…"/>
	<entry name="Necrons/FlayedOne:Idle#2" value="… Ich will zerteilen…"/>
	<entry name="Necrons/FlayedOne:IdleCount" value="3"/>
	<entry name="Necrons/FlayedOne:Shaken#0" value="… Llandu'gor…!"/>
	<entry name="Necrons/FlayedOne:ShakenCount" value="1"/>
	<entry name="Necrons/FlayedOne:Victory#0" value="… wieder leben…"/>
	<entry name="Necrons/FlayedOne:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Attack#0" value="Leiste Feuerunterstützung für die Infanterie."/>
	<entry name="AstraMilitarum/Chimera:Attack#1" value="Transporter greift an."/>
	<entry name="AstraMilitarum/Chimera:Attack#2" value="Wir feuern aus allen Rohren."/>
	<entry name="AstraMilitarum/Chimera:AttackCount" value="3"/>
	<entry name="AstraMilitarum/Chimera:Broken#0" value="Schnelles Panzerfahrzeug zieht sich schnell zurück…"/>
	<entry name="AstraMilitarum/Chimera:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Hurt#0" value="Wir sind nicht stark genug gepanzert!"/>
	<entry name="AstraMilitarum/Chimera:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Idle#0" value="Warte auf Befehle."/>
	<entry name="AstraMilitarum/Chimera:Idle#1" value="Muss jemand irgendwohin gebracht werden?"/>
	<entry name="AstraMilitarum/Chimera:Idle#2" value="Wer ist mit dem Polieren der Multilaserlinse dran?"/>
	<entry name="AstraMilitarum/Chimera:IdleCount" value="3"/>
	<entry name="AstraMilitarum/Chimera:Shaken#0" value="Das große Ding muss hier weg!"/>
	<entry name="AstraMilitarum/Chimera:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Victory#0" value="Panzerschock erfolgreich."/>
	<entry name="AstraMilitarum/Chimera:VictoryCount" value="1"/>
	<entry name="Orks/Warbiker:Attack#0" value="ROTÄ SIN' SCHNELLA!"/>
	<entry name="Orks/Warbiker:Attack#1" value="Dakka, dakka, dakka!"/>
	<entry name="Orks/Warbiker:Attack#2" value="Ballat se alle wech!"/>
	<entry name="Orks/Warbiker:AttackCount" value="3"/>
	<entry name="Orks/Warbiker:Broken#0" value="Schnella, schnella, schnella, schnella…"/>
	<entry name="Orks/Warbiker:BrokenCount" value="1"/>
	<entry name="Orks/Warbiker:Hurt#0" value="He, Baz, war das dein Rad oda meins?"/>
	<entry name="Orks/Warbiker:Hurt#1" value="Die Teilä ham wia sowieso nich' gebraucht."/>
	<entry name="Orks/Warbiker:HurtCount" value="2"/>
	<entry name="Orks/Warbiker:Idle#0" value="Wir woll'n fah'n, zum Zogg nochmal!"/>
	<entry name="Orks/Warbiker:Idle#1" value="Wromm, wromm, dakka, dakka! Das war'n noch Zeitän…"/>
	<entry name="Orks/Warbiker:IdleCount" value="2"/>
	<entry name="Orks/Warbiker:Shaken#0" value="Ich brauch blauä Farbä, schnell!"/>
	<entry name="Orks/Warbiker:ShakenCount" value="1"/>
	<entry name="Orks/Warbiker:Victory#0" value="WAAAGH! Heizakult! WAAAGH!"/>
	<entry name="Orks/Warbiker:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#0" value="Sie werden durch unsere Hand sterben."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#1" value="Wir zerquetschen ihre Schädel unter unseren Ketten!"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#2" value="Sie werden durch unsere Hand sterben."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Broken#0" value="Dafür werden uns die Dunklen Götter verfluchen…"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Hurt#0" value="Wir werden hier nicht sterben, ihr Schwächlinge!"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Idle#0" value="Dafür, dass wir zehntausend Jahre nichts mehr zu tun hatten, sind wir gar nicht so eingerostet."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Idle#1" value="Die Dunklen Götter wachen über uns."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:IdleCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Shaken#0" value="Rettet euer Leben…"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Victory#0" value="Sie sind tot. Das war vorhersehbar."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:VictoryCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Attack#0" value="Setze Massebeschleuniger ein."/>
	<entry name="Tau/TidewallGunrig:Attack#1" value="Waffenstellung aktiviert."/>
	<entry name="Tau/TidewallGunrig:Attack#2" value="Stabilisiert und bereit zum Feuern."/>
	<entry name="Tau/TidewallGunrig:AttackCount" value="3"/>
	<entry name="Tau/TidewallGunrig:Broken#0" value="Waffenplattform wird zurückgezogen."/>
	<entry name="Tau/TidewallGunrig:BrokenCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Hurt#0" value="Schildwall wirkungslos, Waffenstellung beschädigt."/>
	<entry name="Tau/TidewallGunrig:HurtCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Idle#0" value="Gibt es feindliche Panzer, um die wir uns kümmern könnten?"/>
	<entry name="Tau/TidewallGunrig:Idle#1" value="Mobile Waffenstellung bereit für den Kampf."/>
	<entry name="Tau/TidewallGunrig:IdleCount" value="2"/>
	<entry name="Tau/TidewallGunrig:Shaken#0" value="So viel Beschuss kann der Wellenbrecher nicht wegstecken!"/>
	<entry name="Tau/TidewallGunrig:ShakenCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Victory#0" value="Ziel… atomisiert."/>
	<entry name="Tau/TidewallGunrig:VictoryCount" value="1"/>
	<entry name="Tyranids/ScythedHierodule" value="Tyranids/Carnifex"/>
	<entry name="SpaceMarines/Razorback" value="SpaceMarines/Hunter"/>
	<entry name="Neutral/Umbra:Attack#0" value="…V…"/>
	<entry name="Neutral/Umbra:Attack#1" value="(Verweilen)"/>
	<entry name="Neutral/Umbra:Attack#2" value="Verweilen…"/>
	<entry name="Neutral/Umbra:Attack#3" value="…rweilen…"/>
	<entry name="Neutral/Umbra:AttackCount" value="4"/>
	<entry name="Neutral/Umbra:Broken#0" value="Verweilen…"/>
	<entry name="Neutral/Umbra:BrokenCount" value="1"/>
	<entry name="Neutral/Umbra:Hurt#0" value="VER--"/>
	<entry name="Neutral/Umbra:HurtCount" value="1"/>
	<entry name="Neutral/Umbra:Idle#0" value="…"/>
	<entry name="Neutral/Umbra:Idle#1" value="… verweilen"/>
	<entry name="Neutral/Umbra:Idle#2" value="Verweilen…"/>
	<entry name="Neutral/Umbra:Idle#3" value="…rweil…"/>
	<entry name="Neutral/Umbra:IdleCount" value="4"/>
	<entry name="Neutral/Umbra:Shaken#0" value="Ver…weilen…"/>
	<entry name="Neutral/Umbra:ShakenCount" value="1"/>
	<entry name="Neutral/Umbra:Victory#0" value="VERWEILEN"/>
	<entry name="Neutral/Umbra:VictoryCount" value="1"/>
	<entry name="Eldar/Wraithblade:Attack#0" value="… der Feind ist dem Untergang geweiht…"/>
	<entry name="Eldar/Wraithblade:Attack#1" value="… gesellt euch zu den Toten…"/>
	<entry name="Eldar/Wraithblade:Attack#2" value="… für unser Weltenschiff…"/>
	<entry name="Eldar/Wraithblade:Attack#3" value="… für die Aeldari…"/>
	<entry name="Eldar/Wraithblade:AttackCount" value="4"/>
	<entry name="Eldar/Wraithblade:Broken#0" value="… wir fürchten „Sie, die dürstet“…"/>
	<entry name="Eldar/Wraithblade:BrokenCount" value="1"/>
	<entry name="Eldar/Wraithblade:Hurt#0" value="… abermals wartet der Tod?"/>
	<entry name="Eldar/Wraithblade:HurtCount" value="1"/>
	<entry name="Eldar/Wraithblade:Idle#0" value="… brauchen Führung…"/>
	<entry name="Eldar/Wraithblade:Idle#1" value="… wir sind tot…"/>
	<entry name="Eldar/Wraithblade:Idle#2" value="… lasst uns ruhen…"/>
	<entry name="Eldar/Wraithblade:Idle#3" value="… wir leben für den Krieg…"/>
	<entry name="Eldar/Wraithblade:IdleCount" value="4"/>
	<entry name="Eldar/Wraithblade:Shaken#0" value="… wir sind über die Furcht erhaben…"/>
	<entry name="Eldar/Wraithblade:ShakenCount" value="1"/>
	<entry name="Eldar/Wraithblade:Victory#0" value="… sie haben sich uns angeschlossen…"/>
	<entry name="Eldar/Wraithblade:VictoryCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Attack#0" value="Alle Systeme feuern!"/>
	<entry name="Eldar/WaveSerpent:Attack#1" value="Ja, wir greifen an!"/>
	<entry name="Eldar/WaveSerpent:Attack#2" value="Sollen wir unseren Wellenschild für ein Inferno entladen?"/>
	<entry name="Eldar/WaveSerpent:Attack#3" value="Transporter im Kampf!"/>
	<entry name="Eldar/WaveSerpent:AttackCount" value="4"/>
	<entry name="Eldar/WaveSerpent:Broken#0" value="Rettet euch!"/>
	<entry name="Eldar/WaveSerpent:BrokenCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Hurt#0" value="Schild durchbrochen."/>
	<entry name="Eldar/WaveSerpent:HurtCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Idle#0" value="Bereit aufzubrechen."/>
	<entry name="Eldar/WaveSerpent:Idle#1" value="Warte auf Befehl zum Abheben."/>
	<entry name="Eldar/WaveSerpent:Idle#2" value="Wer muss transportiert werden?"/>
	<entry name="Eldar/WaveSerpent:Idle#3" value="Was für eine wunderbare Welt."/>
	<entry name="Eldar/WaveSerpent:IdleCount" value="4"/>
	<entry name="Eldar/WaveSerpent:Shaken#0" value="Holt uns hier raus!"/>
	<entry name="Eldar/WaveSerpent:ShakenCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Victory#0" value="Beladen für das nächste Ziel."/>
	<entry name="Eldar/WaveSerpent:VictoryCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#0" value="Gardistenbatterie feuert!"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#1" value="Monofilamentdraht kommt zum Einsatz!"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#2" value="Artillerie bereit vorzustoßen!"/>
	<entry name="Eldar/VaulsWrathSupportBattery:AttackCount" value="3"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Broken#0" value="Wir können die Stellung nicht alleine halten!"/>
	<entry name="Eldar/VaulsWrathSupportBattery:BrokenCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Hurt#0" value="Wir werden das nicht überleben!"/>
	<entry name="Eldar/VaulsWrathSupportBattery:HurtCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#0" value="Monofilamentdraht wird aufgespult."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#1" value="Antigravgeneratoren werden überprüft."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#2" value="Wir leisten Unterstützung von hinten."/>
	<entry name="Eldar/VaulsWrathSupportBattery:IdleCount" value="3"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Shaken#0" value="Wir müssen uns aus dem Kampfgebiet zurückziehen!"/>
	<entry name="Eldar/VaulsWrathSupportBattery:ShakenCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Victory#0" value="Wir haben… die feindliche Formation… aufgelöst."/>
	<entry name="Eldar/VaulsWrathSupportBattery:VictoryCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Attack#0" value="Wir schlagen schneller zu als ein zerstörerischer Blitz."/>
	<entry name="Eldar/ShiningSpear:Attack#1" value="Für Drastanta!"/>
	<entry name="Eldar/ShiningSpear:Attack#2" value="Sie erblassen vor unserer Tugendhaftigkeit."/>
	<entry name="Eldar/ShiningSpear:Attack#3" value="Wir sind der Speer des Khaine!"/>
	<entry name="Eldar/ShiningSpear:AttackCount" value="4"/>
	<entry name="Eldar/ShiningSpear:Broken#0" value="Der Speer ist… zerbrochen."/>
	<entry name="Eldar/ShiningSpear:BrokenCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Hurt#0" value="Wir müssen uns beeilen."/>
	<entry name="Eldar/ShiningSpear:HurtCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Idle#0" value="Wir sind lebende Legenden."/>
	<entry name="Eldar/ShiningSpear:Idle#1" value="Warten auf Angriffsbefehl…"/>
	<entry name="Eldar/ShiningSpear:Idle#2" value="Die Speere sind einsatzbereit."/>
	<entry name="Eldar/ShiningSpear:Idle#3" value="Ich bin froh, wenn ich die Maske des Krieges abnehmen kann."/>
	<entry name="Eldar/ShiningSpear:IdleCount" value="4"/>
	<entry name="Eldar/ShiningSpear:Shaken#0" value="Wir fliegen durch das Tal der Furcht."/>
	<entry name="Eldar/ShiningSpear:ShakenCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Victory#0" value="Wir haben sie kalt erwischt."/>
	<entry name="Eldar/ShiningSpear:VictoryCount" value="1"/>
	<entry name="Eldar/Ranger:Attack#0" value="Wählen Ziele aus…"/>
	<entry name="Eldar/Ranger:Attack#1" value="Sie werden tot sein, bevor sie unseren Angriff bemerkt haben."/>
	<entry name="Eldar/Ranger:Attack#2" value="Mein Jagdgewehr flüstert vom Tod."/>
	<entry name="Eldar/Ranger:Attack#3" value="Unser. Ziel. Verfehlen. Wir. Nie."/>
	<entry name="Eldar/Ranger:AttackCount" value="4"/>
	<entry name="Eldar/Ranger:Broken#0" value="Zurückfallen lassen! Sie kommen!"/>
	<entry name="Eldar/Ranger:BrokenCount" value="1"/>
	<entry name="Eldar/Ranger:Hurt#0" value="Wir durchschreiten die Dunkelheit…"/>
	<entry name="Eldar/Ranger:HurtCount" value="1"/>
	<entry name="Eldar/Ranger:Idle#0" value="Unser Exil ist Geschichte."/>
	<entry name="Eldar/Ranger:Idle#1" value="Wir sind geschützt durch Chamäolin."/>
	<entry name="Eldar/Ranger:Idle#2" value="Fühlt sich gut an, an einem neuen Ort zu sein."/>
	<entry name="Eldar/Ranger:Idle#3" value="Kommt und lauscht unseren Geschichten über die Galaxis!"/>
	<entry name="Eldar/Ranger:IdleCount" value="4"/>
	<entry name="Eldar/Ranger:Shaken#0" value="Weg von der Frontlinie, und zwar schnell!"/>
	<entry name="Eldar/Ranger:ShakenCount" value="1"/>
	<entry name="Eldar/Ranger:Victory#0" value="Sie sind weg und wir können wieder durch die Gegend streifen."/>
	<entry name="Eldar/Ranger:VictoryCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Attack#0" value="Lauthals künden wir von eurem Tod."/>
	<entry name="Eldar/HowlingBanshee:Attack#1" value="Mit unseren Klingen durchschneiden wir sie wie Gras."/>
	<entry name="Eldar/HowlingBanshee:Attack#2" value="Wir rufen sie zum Jüngsten Gericht."/>
	<entry name="Eldar/HowlingBanshee:Attack#3" value="Schnell wie der heulende Wind!"/>
	<entry name="Eldar/HowlingBanshee:AttackCount" value="4"/>
	<entry name="Eldar/HowlingBanshee:Broken#0" value="Hört unser Wehklagen!"/>
	<entry name="Eldar/HowlingBanshee:BrokenCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Hurt#0" value="Das altbekannte Stechen!"/>
	<entry name="Eldar/HowlingBanshee:HurtCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Idle#0" value="Wer uns heulen hört, erwartet seinen Untergang."/>
	<entry name="Eldar/HowlingBanshee:Idle#1" value="Allzu gerne würden wir den Feind unsere Klingen spüren lassen!"/>
	<entry name="Eldar/HowlingBanshee:Idle#2" value="Wir sind die Kinder von Morai-Heg."/>
	<entry name="Eldar/HowlingBanshee:Idle#3" value="Unser Gesang bedeutet den Tod."/>
	<entry name="Eldar/HowlingBanshee:IdleCount" value="4"/>
	<entry name="Eldar/HowlingBanshee:Shaken#0" value="Sie unterbrechen unseren Gesang!"/>
	<entry name="Eldar/HowlingBanshee:ShakenCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Victory#0" value="Im Tod werden sie nur Geflüster vernehmen."/>
	<entry name="Eldar/HowlingBanshee:VictoryCount" value="1"/>
	<entry name="Eldar/Headquarters:Attack#0" value="Wir verteidigen das Netz der Tausend Tore."/>
	<entry name="Eldar/Headquarters:Attack#1" value="Ihr könnt nicht vorbei!"/>
	<entry name="Eldar/Headquarters:Attack#2" value="Das Bollwerk eröffnet das Feuer!"/>
	<entry name="Eldar/Headquarters:Attack#3" value="Wir sind die Hüter des Netzes der Tausend Tore."/>
	<entry name="Eldar/Headquarters:AttackCount" value="4"/>
	<entry name="Eldar/Headquarters:Broken#0" value="Sie brechen durch!"/>
	<entry name="Eldar/Headquarters:BrokenCount" value="1"/>
	<entry name="Eldar/Headquarters:Hurt#0" value="Khaine, steh uns bei!"/>
	<entry name="Eldar/Headquarters:HurtCount" value="1"/>
	<entry name="Eldar/Headquarters:Idle#0" value="Wir wachen über das Netz der Tausend Tore."/>
	<entry name="Eldar/Headquarters:Idle#1" value="Wir sind bereit, Seher."/>
	<entry name="Eldar/Headquarters:Idle#2" value="Ich will endlich auf mein Weltenschiff zurückkehren und diese Maske abnehmen."/>
	<entry name="Eldar/Headquarters:Idle#3" value="Warum sind wir hier?"/>
	<entry name="Eldar/Headquarters:IdleCount" value="4"/>
	<entry name="Eldar/Headquarters:Shaken#0" value="Ich fürchte den Tod und den Großen Feind."/>
	<entry name="Eldar/Headquarters:ShakenCount" value="1"/>
	<entry name="Eldar/Headquarters:Victory#0" value="Dieses Portal wird sich noch nicht schließen."/>
	<entry name="Eldar/Headquarters:VictoryCount" value="1"/>
	<entry name="Eldar/Guardian:Attack#0" value="Die Pflicht ruft."/>
	<entry name="Eldar/Guardian:Attack#1" value="Wir werden nicht zugrunde gehen!"/>
	<entry name="Eldar/Guardian:Attack#2" value="Unser Volk ist eine geeinte Macht!"/>
	<entry name="Eldar/Guardian:Attack#3" value="Shurikenwaffen, Feuer!"/>
	<entry name="Eldar/Guardian:AttackCount" value="4"/>
	<entry name="Eldar/Guardian:Broken#0" value="Ist das das Ende…?"/>
	<entry name="Eldar/Guardian:BrokenCount" value="1"/>
	<entry name="Eldar/Guardian:Hurt#0" value="Wir sind dem Untergang geweiht."/>
	<entry name="Eldar/Guardian:HurtCount" value="1"/>
	<entry name="Eldar/Guardian:Idle#0" value="Der Krieg tobt… unerbittlich."/>
	<entry name="Eldar/Guardian:Idle#1" value="Wann kehren wir in die Kristallhallen zurück?"/>
	<entry name="Eldar/Guardian:Idle#2" value="Möge Asuryan uns schützen."/>
	<entry name="Eldar/Guardian:Idle#3" value="Seher, ich hoffe, deine Prophezeiung bewahrheitet sich."/>
	<entry name="Eldar/Guardian:IdleCount" value="4"/>
	<entry name="Eldar/Guardian:Shaken#0" value="Isha, behüte uns!"/>
	<entry name="Eldar/Guardian:ShakenCount" value="1"/>
	<entry name="Eldar/Guardian:Victory#0" value="Sich gegen die Aeldari zu stellen, zeugt von Hochmut."/>
	<entry name="Eldar/Guardian:VictoryCount" value="1"/>
	<entry name="Eldar/FirePrism:Attack#0" value="Wir werden sie niedermähen!"/>
	<entry name="Eldar/FirePrism:Attack#1" value="Sie können sich unserem Feuer nicht erwehren!"/>
	<entry name="Eldar/FirePrism:Attack#2" value="Sie werden verglühen!"/>
	<entry name="Eldar/FirePrism:Attack#3" value="Waffen unserer Ahnen, lasst uns nicht im Stich!"/>
	<entry name="Eldar/FirePrism:AttackCount" value="4"/>
	<entry name="Eldar/FirePrism:Broken#0" value="… das kann nicht sein."/>
	<entry name="Eldar/FirePrism:BrokenCount" value="1"/>
	<entry name="Eldar/FirePrism:Hurt#0" value="Der Schaden zieht keine Konsequenzen nach sich."/>
	<entry name="Eldar/FirePrism:HurtCount" value="1"/>
	<entry name="Eldar/FirePrism:Idle#0" value="Wir möchten uns nützlich machen."/>
	<entry name="Eldar/FirePrism:Idle#1" value="Vaul erschuf einst diese Maschine."/>
	<entry name="Eldar/FirePrism:Idle#2" value="Warum müssen wir mit solchen Waffen kämpfen?"/>
	<entry name="Eldar/FirePrism:Idle#3" value="Die Weltenschiffe sind so weit weg…"/>
	<entry name="Eldar/FirePrism:IdleCount" value="4"/>
	<entry name="Eldar/FirePrism:Shaken#0" value="Wir können ihr Feuer wegstecken…"/>
	<entry name="Eldar/FirePrism:ShakenCount" value="1"/>
	<entry name="Eldar/FirePrism:Victory#0" value="Sie wurden wie erwartet besiegt."/>
	<entry name="Eldar/FirePrism:VictoryCount" value="1"/>
	<entry name="Eldar/FireDragon:Attack#0" value="Brennen sollt ihr!"/>
	<entry name="Eldar/FireDragon:Attack#1" value="Wir entfachen uralte Flammen!"/>
	<entry name="Eldar/FireDragon:Attack#2" value="Wir sind in einem feurigen Rausch!"/>
	<entry name="Eldar/FireDragon:Attack#3" value="Kommt näher, ihr winzigen Panzer…"/>
	<entry name="Eldar/FireDragon:AttackCount" value="4"/>
	<entry name="Eldar/FireDragon:Broken#0" value="Der Traum des Feuers verblasst…"/>
	<entry name="Eldar/FireDragon:BrokenCount" value="1"/>
	<entry name="Eldar/FireDragon:Hurt#0" value="Wir wurden selbst Opfer der Flammen!"/>
	<entry name="Eldar/FireDragon:HurtCount" value="1"/>
	<entry name="Eldar/FireDragon:Idle#0" value="Lasst uns eine Geschichte von Feuerstürmen und Ascheregen erzählen."/>
	<entry name="Eldar/FireDragon:Idle#1" value="Asche und wilde Feuersbrunst…"/>
	<entry name="Eldar/FireDragon:Idle#2" value="Eine Welt umhüllt von Flammen."/>
	<entry name="Eldar/FireDragon:Idle#3" value="Feuer hat etwas Erhabenes, nicht wahr?"/>
	<entry name="Eldar/FireDragon:IdleCount" value="4"/>
	<entry name="Eldar/FireDragon:Shaken#0" value="Uns brennen die Sicherungen durch…"/>
	<entry name="Eldar/FireDragon:ShakenCount" value="1"/>
	<entry name="Eldar/FireDragon:Victory#0" value="Ihr Antlitz im Angesicht des Todes war… aschfahl."/>
	<entry name="Eldar/FireDragon:VictoryCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Attack#0" value="Kurnous führt uns!"/>
	<entry name="Eldar/CrimsonHunter:Attack#1" value="Wir jagen!"/>
	<entry name="Eldar/CrimsonHunter:Attack#2" value="Aus den Lüften erteilen wir… Lektionen."/>
	<entry name="Eldar/CrimsonHunter:Attack#3" value="Wir sind die blendenden Klingen!"/>
	<entry name="Eldar/CrimsonHunter:AttackCount" value="4"/>
	<entry name="Eldar/CrimsonHunter:Broken#0" value="Das ist kein Rückzug…"/>
	<entry name="Eldar/CrimsonHunter:BrokenCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Hurt#0" value="Sie… sie haben uns erwischt?"/>
	<entry name="Eldar/CrimsonHunter:HurtCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Idle#0" value="Ich sehne mich nach… dem Rausch der Geschwindigkeit."/>
	<entry name="Eldar/CrimsonHunter:Idle#1" value="Kurnous möchte, dass wir kämpfen."/>
	<entry name="Eldar/CrimsonHunter:Idle#2" value="In diesem Gebiet mangelt es nicht an Zielen…"/>
	<entry name="Eldar/CrimsonHunter:Idle#3" value="Nichts ist verhängnisvoller als Überheblichkeit im Kampf."/>
	<entry name="Eldar/CrimsonHunter:IdleCount" value="4"/>
	<entry name="Eldar/CrimsonHunter:Shaken#0" value="Keine Zeit zum Nachdenken!"/>
	<entry name="Eldar/CrimsonHunter:ShakenCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Victory#0" value="Ihr Ego hat von ihnen Besitz ergriffen."/>
	<entry name="Eldar/CrimsonHunter:VictoryCount" value="1"/>
	<entry name="Eldar/HemlockWraithfighter" value="Eldar/CrimsonHunter"/>
	<entry name="Eldar/Scorpion" value="Eldar/FirePrism"/>
	<entry name="Eldar/WarWalker" value="Eldar/Guardian"/>
	<entry name="Eldar/Wraithknight" value="Eldar/Wraithblade"/>
	<entry name="Eldar/Autarch:Attack#0" value="Ihr werdet alle Aspekte zu spüren bekommen!"/>
	<entry name="Eldar/Autarch:Attack#1" value="Ich kenne Khaine!"/>
	<entry name="Eldar/Autarch:Attack#2" value="Sterbt durch meine Hand!"/>
	<entry name="Eldar/Autarch:Attack#3" value="Ich bin der Inbegriff des Krieges!"/>
	<entry name="Eldar/Autarch:AttackCount" value="4"/>
	<entry name="Eldar/Autarch:Broken#0" value="Auch der Rückzug gehört zum Krieg."/>
	<entry name="Eldar/Autarch:BrokenCount" value="1"/>
	<entry name="Eldar/Autarch:Hurt#0" value="Ich… benötige womöglich eine weitere Inkarnation."/>
	<entry name="Eldar/Autarch:HurtCount" value="1"/>
	<entry name="Eldar/Autarch:Idle#0" value="Möge Cegorach die Autarchen als Werkzeug benutzen."/>
	<entry name="Eldar/Autarch:Idle#1" value="Ich werde nun die Litanei der Schlacht rezitieren…"/>
	<entry name="Eldar/Autarch:Idle#2" value="Der Krieg hat mich vereinnahmt."/>
	<entry name="Eldar/Autarch:Idle#3" value="In meinem Geist herrscht kein Frieden."/>
	<entry name="Eldar/Autarch:IdleCount" value="4"/>
	<entry name="Eldar/Autarch:Shaken#0" value="Ha! Sie stellen mich auf die Probe!"/>
	<entry name="Eldar/Autarch:ShakenCount" value="1"/>
	<entry name="Eldar/Autarch:Victory#0" value="Sie werden meine Geduld nicht mehr auf die Probe stellen."/>
	<entry name="Eldar/Autarch:VictoryCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Attack#0" value="Ich forme eure Zukunft… Sie wird nur von kurzer Dauer sein."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#1" value="Mir nach, Schimmerspeere."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#2" value="Mein Hagun Zar dürstet es nach Blut."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#3" value="Ich bin der Steuermann meines Volkes."/>
	<entry name="Eldar/FarseerSkyrunner:AttackCount" value="4"/>
	<entry name="Eldar/FarseerSkyrunner:Broken#0" value="Mir offenbart sich keine Zukunft."/>
	<entry name="Eldar/FarseerSkyrunner:BrokenCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Hurt#0" value="Ich darf nicht klein beigeben!"/>
	<entry name="Eldar/FarseerSkyrunner:HurtCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Idle#0" value="Die Stränge verflechten sich."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#1" value="Die Zukunft ist stets in Bewegung."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#2" value="Ich kann die Zukunft sehen und formen."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#3" value="Ich sehne mich nach dem Kristall und der Matrix."/>
	<entry name="Eldar/FarseerSkyrunner:IdleCount" value="4"/>
	<entry name="Eldar/FarseerSkyrunner:Shaken#0" value="So war das nicht vorherbestimmt!"/>
	<entry name="Eldar/FarseerSkyrunner:ShakenCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Victory#0" value="Vernichtet. Wie ich es vorausgesagt hatte."/>
	<entry name="Eldar/FarseerSkyrunner:VictoryCount" value="1"/>
	<entry name="Eldar/Spiritseer:Attack#0" value="Es gibt keinen Frieden!"/>
	<entry name="Eldar/Spiritseer:Attack#1" value="Mögen die Toten euch willkommen heißen!"/>
	<entry name="Eldar/Spiritseer:Attack#2" value="Fürchtet meine Klinge!"/>
	<entry name="Eldar/Spiritseer:Attack#3" value="Ich entstamme einer uralten Spezies!"/>
	<entry name="Eldar/Spiritseer:AttackCount" value="4"/>
	<entry name="Eldar/Spiritseer:Broken#0" value="Ich ziehe mich zurück, sonst muss ich zu meinen Schützlingen stoßen."/>
	<entry name="Eldar/Spiritseer:BrokenCount" value="1"/>
	<entry name="Eldar/Spiritseer:Hurt#0" value="Meine Essenz strömt in den Kristall…"/>
	<entry name="Eldar/Spiritseer:HurtCount" value="1"/>
	<entry name="Eldar/Spiritseer:Idle#0" value="Die Toten sind in meiner Obhut."/>
	<entry name="Eldar/Spiritseer:Idle#1" value="Ich spreche zu euch, meine dahingeschiedenen Freunde."/>
	<entry name="Eldar/Spiritseer:Idle#2" value="In den Hallen der Geister herrscht Stille."/>
	<entry name="Eldar/Spiritseer:Idle#3" value="Lauscht der Unendlichkeitsmatrix des Planeten. Sie flüstert!"/>
	<entry name="Eldar/Spiritseer:IdleCount" value="4"/>
	<entry name="Eldar/Spiritseer:Shaken#0" value="Nur noch so wenige von uns sind am Leben…"/>
	<entry name="Eldar/Spiritseer:ShakenCount" value="1"/>
	<entry name="Eldar/Spiritseer:Victory#0" value="Ich kann über den Tod nicht frohlocken – selbst nicht über den des Feindes."/>
	<entry name="Eldar/Spiritseer:VictoryCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#0" value="IHR WAGT ES, EUCH MIT DEM FLEISCHGEWORDENEN KRIEG ZU MESSEN?!"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#1" value="DIE OPFERUNG IST EUER SCHICKSAL!"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#2" value="FEUER!"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#3" value="KOMMT, IHR NARREN, KHAINE ERWARTET EUCH!"/>
	<entry name="Eldar/AvatarOfKhaine:AttackCount" value="4"/>
	<entry name="Eldar/AvatarOfKhaine:Broken#0" value="ICH WERDE NICHT FLIEHEN!"/>
	<entry name="Eldar/AvatarOfKhaine:BrokenCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Hurt#0" value="IHR NÄHRT MEINEN ZORN!"/>
	<entry name="Eldar/AvatarOfKhaine:HurtCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#0" value="DIESER PLANET ZERRT AN MIR."/>
	<entry name="Eldar/AvatarOfKhaine:Idle#1" value="DIESE HAND SCHWINGT DAS SCHWERT."/>
	<entry name="Eldar/AvatarOfKhaine:Idle#2" value="VAUL… IST TOT."/>
	<entry name="Eldar/AvatarOfKhaine:Idle#3" value="ENTZWEIT KÄMPFE ICH WEITER."/>
	<entry name="Eldar/AvatarOfKhaine:IdleCount" value="4"/>
	<entry name="Eldar/AvatarOfKhaine:Shaken#0" value="KOMMT NÄHER, STERBLICHE."/>
	<entry name="Eldar/AvatarOfKhaine:ShakenCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Victory#0" value="SIE SIND NUN BEI KHAINE."/>
	<entry name="Eldar/AvatarOfKhaine:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Attack#0" value="Führe uns beim Zielen, oh Imperator."/>
	<entry name="AstraMilitarum/Ratling:Attack#1" value="Fernschützen."/>
	<entry name="AstraMilitarum/Ratling:Attack#2" value="Was wir sehen, können wir auch treffen."/>
	<entry name="AstraMilitarum/Ratling:Attack#3" value="Das ist wie Tontaubenschießen."/>
	<entry name="AstraMilitarum/Ratling:AttackCount" value="4"/>
	<entry name="AstraMilitarum/Ratling:Broken#0" value="Wir verschwinden!"/>
	<entry name="AstraMilitarum/Ratling:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Hurt#0" value="Bringt uns weg von der Front!"/>
	<entry name="AstraMilitarum/Ratling:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Idle#0" value="Amasec zum Frühstück? Von mir aus gerne."/>
	<entry name="AstraMilitarum/Ratling:Idle#1" value="Grox-Fleisch schmeckt vorzüglich, musst du wissen."/>
	<entry name="AstraMilitarum/Ratling:Idle#2" value="Das? Das war ich nicht! Sag dem Commissar aber trotzdem nichts!"/>
	<entry name="AstraMilitarum/Ratling:Idle#3" value="Her mit der Kohle und kein Wort zum Sarge."/>
	<entry name="AstraMilitarum/Ratling:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Ratling:Shaken#0" value="Sorge für unseren Schutz, dann werden wir auch dich beschützen."/>
	<entry name="AstraMilitarum/Ratling:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Victory#0" value="Mitten ins Schwarze. Ich denke, dafür sind Extrarationen drin!"/>
	<entry name="AstraMilitarum/Ratling:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#0" value="Im Namen der Dunklen Götter!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#1" value="Ehre sei den vier Göttern!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#2" value="Unsere Seelen sind verkümmert!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#3" value="Blut für den Herrn des Blutes."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Broken#0" value="Lords, beschützt uns!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Hurt#0" value="Ein Opfer für das Ungeteilte Chaos!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#0" value="Hast du dich jemals gefragt… Ach, egal."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#1" value="Nurgle, Herr der Fliegen."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#2" value="Für Tzeentch, den Wandler der Wege."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#3" value="Ruhm und Ehre dem Prinzen der Perfektion."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Shaken#0" value="Wo sind unsere Götter?"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Victory#0" value="Ruhm, Ruhm, Ruhm!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#0" value="Spürt meine Peitsche!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#1" value="Der Abgrund ruft."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#2" value="Keine Sünde darf ausgelassen werden."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#3" value="Für Lorgars Ruhm!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Broken#0" value="Ich blicke in den Warp. Oh…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Hurt#0" value="Welch heilige Ekstase!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#0" value="Ein rituelles Opfer braucht seine Zeit."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#1" value="Die Menschheit ist verloren. Chaos ist unsere Erlösung."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#2" value="Im Chaos finden wir ewiges Leben!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#3" value="Die Wahrheit des Universums ist zum Greifen nah!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Shaken#0" value="So viele Geheimnisse, die es noch zu lüften gilt…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Victory#0" value="Seltsame Visionen ereilen mich…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#0" value="Sie wagen es, sich uns entgegenzustellen!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#1" value="Narren, Kanonenfutter!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#2" value="Wir werden Schreine aus euren Knochen bauen!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#3" value="Endloser Krieg!"/>
	<entry name="ChaosSpaceMarines/Headquarters:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/Headquarters:Broken#0" value="Unmöglich!"/>
	<entry name="ChaosSpaceMarines/Headquarters:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Hurt#0" value="Sie haben die Mauern durchbrochen!"/>
	<entry name="ChaosSpaceMarines/Headquarters:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#0" value="Loyalisten werden liquidiert."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#1" value="Augen ohne Schädel."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#2" value="Schädel ohne Augen."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#3" value="Die Massen werden vor uns niederknien."/>
	<entry name="ChaosSpaceMarines/Headquarters:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/Headquarters:Shaken#0" value="Wir dürfen nicht scheitern, Leichenfreunde!"/>
	<entry name="ChaosSpaceMarines/Headquarters:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Victory#0" value="Und so finden alle, die sich uns entgegenstellen, den Tod."/>
	<entry name="ChaosSpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="Eldar/Warlock:Attack#0" value="Ein Runenspeer im Duell mit dem Tod."/>
	<entry name="Eldar/Warlock:Attack#1" value="Die Flammen unseres Zorns läutern sie."/>
	<entry name="Eldar/Warlock:Attack#2" value="Unsere Speere singen."/>
	<entry name="Eldar/Warlock:Attack#3" value="Der Geist ist unsere stärkste Waffe."/>
	<entry name="Eldar/Warlock:AttackCount" value="4"/>
	<entry name="Eldar/Warlock:Broken#0" value="Zurück zum Weltenschiff."/>
	<entry name="Eldar/Warlock:BrokenCount" value="1"/>
	<entry name="Eldar/Warlock:Hurt#0" value="Unser Kader geht zugrunde."/>
	<entry name="Eldar/Warlock:HurtCount" value="1"/>
	<entry name="Eldar/Warlock:Idle#0" value="Krieger und Seher zugleich."/>
	<entry name="Eldar/Warlock:Idle#1" value="Der große Feind wartet im Warp."/>
	<entry name="Eldar/Warlock:Idle#2" value="Ich sehne mich nach den Kristallblüten."/>
	<entry name="Eldar/Warlock:Idle#3" value="Ich vermisse die Masken meines Weltenschiffs."/>
	<entry name="Eldar/Warlock:IdleCount" value="4"/>
	<entry name="Eldar/Warlock:Shaken#0" value="Ermutigt uns, wir taumeln."/>
	<entry name="Eldar/Warlock:ShakenCount" value="1"/>
	<entry name="Eldar/Warlock:Victory#0" value="Wir haben ihren Niedergang vorhergesehen."/>
	<entry name="Eldar/Warlock:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekWraith" value="Necrons/CanoptekScarab"/>
	<entry name="Orks/KillBursta" value="Orks/Battlewagon"/>
	<entry name="Orks/KrootoxRider" value="Neutral/KrootHound"/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#0" value="Wir zielen auf die Panzerung. Gepriesen sei der Imperator!"/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#1" value="Wir konzentrieren das Feuer."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#2" value="Belagerungsspezialisten einsatzbereit."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#3" value="Hohe Reichweite, niedrige Überlebenschance."/>
	<entry name="SpaceMarines/DevastatorCenturion:AttackCount" value="4"/>
	<entry name="SpaceMarines/DevastatorCenturion:Broken#0" value="Die Kampfanzüge sind noch intakt, aber wir lassen uns zurückfallen."/>
	<entry name="SpaceMarines/DevastatorCenturion:BrokenCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Hurt#0" value="Das ging durch die Panzerung… durch beide Schichten."/>
	<entry name="SpaceMarines/DevastatorCenturion:HurtCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#0" value="Wir rücken gemächlich vor."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#1" value="Eine Schicht Panzerung kann weg."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#2" value="Diese Kampfanzüge hätten unsere Vorfahren während der Häresie gebraucht."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#3" value="Belagerungspanzerung wird angelegt."/>
	<entry name="SpaceMarines/DevastatorCenturion:IdleCount" value="4"/>
	<entry name="SpaceMarines/DevastatorCenturion:Shaken#0" value="Ich stehe unter Beschuss, aber der Kampfanzug hält."/>
	<entry name="SpaceMarines/DevastatorCenturion:ShakenCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Victory#0" value="Sie wurden… vernichtet."/>
	<entry name="SpaceMarines/DevastatorCenturion:VictoryCount" value="1"/>
	<entry name="Tau/TigerShark" value="RazorsharkStrikeFighter"/>
	<entry name="Tyranids/Venomthrope" value="Tyranids/Carnifex"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#0" value="Pssssst."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#1" value="Heulen: Transsonische Waffen."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#2" value="Abgedeckt: Neurostatisches Spektrum."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#3" value="Verkrüppelt: Feind."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Broken#0" value="Mut: Verloren."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Hurt#0" value="Sterben: Erneut."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#0" value="Aktivität: Untätig."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#1" value="Angeforderte Daten: Entspannung."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#2" value="Erlernen: Lingua Technis."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#3" value="Definition: Potenzieller Rivale."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Shaken#0" value="Gefahr: Gefahr."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Victory#0" value="Stolz: Triumph."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:VictoryCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#0" value="Wuuuhuuuu, dakka-dakka-dakka!"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#1" value="Nich' weglauf'n, auf euch wartät da Bohra!"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#2" value="DAKKA AUF ALLÄ KNÖPFÄ!"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#3" value="WAAAGH, DIE FLIEGABOYS!"/>
	<entry name="Orks/MegatrakkScrapjet:AttackCount" value="4"/>
	<entry name="Orks/MegatrakkScrapjet:Broken#0" value="Schon wieda abgestüazt?!"/>
	<entry name="Orks/MegatrakkScrapjet:BrokenCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Hurt#0" value="Die hab'n die Tür'n weggemoscht!"/>
	<entry name="Orks/MegatrakkScrapjet:HurtCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#0" value="Da Boss hat gesagt, ich muss unt'n bleib'n. Pah!"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#1" value="Wär ich da ob'n, würd ich hia runta ballan!"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#2" value="WARUM GEHT'S NOCH NICH' LOS?!"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#3" value="Hmm. Brauchä mehr Bombän."/>
	<entry name="Orks/MegatrakkScrapjet:IdleCount" value="4"/>
	<entry name="Orks/MegatrakkScrapjet:Shaken#0" value="Flak? SONST NIX, ODA WIE?"/>
	<entry name="Orks/MegatrakkScrapjet:ShakenCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Victory#0" value="DIE ORKZ SIN' DIE BEST'N! Vollä Kannä reingebohrt!"/>
	<entry name="Orks/MegatrakkScrapjet:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Attack#0" value="Seht, wie sie schmelzen."/>
	<entry name="AstraMilitarum/DevilDog:Attack#1" value="Asche zu Asche."/>
	<entry name="AstraMilitarum/DevilDog:Attack#2" value="Hier kommen die Teufelshunde! Hell-yeah!"/>
	<entry name="AstraMilitarum/DevilDog:Attack#3" value="Fusion hat sich noch nie so gut angefühlt."/>
	<entry name="AstraMilitarum/DevilDog:AttackCount" value="4"/>
	<entry name="AstraMilitarum/DevilDog:Broken#0" value="Das Ding wird in die Luft fliegen!"/>
	<entry name="AstraMilitarum/DevilDog:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Hurt#0" value="Haltet sie von den Promethium-Tanks fern!"/>
	<entry name="AstraMilitarum/DevilDog:HurtCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Idle#0" value="Was gibt's Neues?"/>
	<entry name="AstraMilitarum/DevilDog:Idle#1" value="Wann treffen die Höllenhunde ein?"/>
	<entry name="AstraMilitarum/DevilDog:Idle#2" value="Todeswölfe… Die Dinger machen mir Angst."/>
	<entry name="AstraMilitarum/DevilDog:Idle#3" value="Ganz schön kalt hier, wenn die Geschütze nicht feuern."/>
	<entry name="AstraMilitarum/DevilDog:IdleCount" value="4"/>
	<entry name="AstraMilitarum/DevilDog:Shaken#0" value="Startet den Vulcanor und bringt uns außer Reichweite!"/>
	<entry name="AstraMilitarum/DevilDog:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Victory#0" value="Vor Hitze flimmernde Luft… So sieht ein Sieg aus."/>
	<entry name="AstraMilitarum/DevilDog:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="ChaosSpaceMarines/Defiler"/>
	<entry name="Tyranids/HiveGuard" value="Tyranids/Carnifex"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="AdeptusMechanicus/SkitariiVanguard"/>
	<entry name="SpaceMarines/Scout" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="Necrons/GhostArk" value="Necrons/Monolith"/>
	<entry name="Eldar/Hornet" value="Eldar/FirePrism"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#0" value="Avenger-Jagdbomber meldet sich zur Stelle."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#1" value="Feind wird unter Beschuss genommen, Principalis."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#2" value="Vernichtungsschlag aus der Luft, Ma'am."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#3" value="Die Munition geht zur Neige."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AttackCount" value="4"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Broken#0" value="Ausweichmanöver!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Hurt#0" value="Sie müssen Luftabwehr-Unterstützung haben…"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:HurtCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#0" value="Wir haben die Sonne im Rücken."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#1" value="Ein ungewöhnlicher Einsatz, Ma'am."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#2" value="Wir wollen hoch hinaus!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#3" value="Wir sind froh, dass wir am Leben sind!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:IdleCount" value="4"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Shaken#0" value="Von hier oben… sieht man das Grauen."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Victory#0" value="Ein weiterer Punkt für die Flottenasse!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Ist… Ist das die Heilige?"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Attack#0" value="Im Namen des Imperators!"/>
	<entry name="SistersOfBattle/BattleSister:Attack#1" value="Gottimperator, lenke meine Boltkanone!"/>
	<entry name="SistersOfBattle/BattleSister:Attack#2" value="Wir entfesseln Seinen göttlichen Zorn!"/>
	<entry name="SistersOfBattle/BattleSister:Attack#3" value="Häretiker haben keine Daseinsberechtigung!"/>
	<entry name="SistersOfBattle/BattleSister:AttackCount" value="4"/>
	<entry name="SistersOfBattle/BattleSister:Broken#0" value="Ohne unseren Glauben sind wir nichts…"/>
	<entry name="SistersOfBattle/BattleSister:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Hurt#0" value="Ich fürchte nicht ihre scharfen Klingen."/>
	<entry name="SistersOfBattle/BattleSister:HurtCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Idle#0" value="Wir sind die Werkzeuge der Erlösung."/>
	<entry name="SistersOfBattle/BattleSister:Idle#1" value="Schwestern, lasst uns beten."/>
	<entry name="SistersOfBattle/BattleSister:Idle#2" value="Wir sollten nicht ruhen, während das Böse wirkt."/>
	<entry name="SistersOfBattle/BattleSister:Idle#3" value="So unendlich viel Ketzerei… So viel zu tun."/>
	<entry name="SistersOfBattle/BattleSister:IdleCount" value="4"/>
	<entry name="SistersOfBattle/BattleSister:Shaken#0" value="In nomine Imperia…"/>
	<entry name="SistersOfBattle/BattleSister:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Victory#0" value="Ruhe in Frieden, Feind."/>
	<entry name="SistersOfBattle/BattleSister:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Die Heilige…? Wohin sie auch geht, wir werden ihr folgen!"/>
	<entry name="SistersOfBattle/BattleSister:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Attack#0" value="Möge ihnen der Frieden des Imperators zuteilwerden."/>
	<entry name="SistersOfBattle/Canoness:Attack#1" value="Häretiker!"/>
	<entry name="SistersOfBattle/Canoness:Attack#2" value="Kostet von meiner Erlösung!"/>
	<entry name="SistersOfBattle/Canoness:Attack#3" value="Spürt mein Gebet!"/>
	<entry name="SistersOfBattle/Canoness:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Canoness:Broken#0" value="Meine… Schwestern…"/>
	<entry name="SistersOfBattle/Canoness:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Hurt#0" value="Ich kämpfe… weiter!"/>
	<entry name="SistersOfBattle/Canoness:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Idle#0" value="Das Chaos schafft Arbeit für untätige Hände."/>
	<entry name="SistersOfBattle/Canoness:Idle#1" value="Wir sind nicht ohne Tadel, aber wir wissen um unsere Sünden."/>
	<entry name="SistersOfBattle/Canoness:Idle#2" value="Die heilige Dreifaltigkeit ist mein Werkzeug… Bolter, Flammenwerfer und Melter."/>
	<entry name="SistersOfBattle/Canoness:Idle#3" value="Die Erlösung ist jeden Preis wert, meine Schwestern."/>
	<entry name="SistersOfBattle/Canoness:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Canoness:Shaken#0" value="Ich… Ich kann nicht."/>
	<entry name="SistersOfBattle/Canoness:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Victory#0" value="Du erhältst die Absolution."/>
	<entry name="SistersOfBattle/Canoness:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Ich führe, aber sie… sie inspiriert uns."/>
	<entry name="SistersOfBattle/Canoness:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Attack#0" value="… Der Imperator, welch mächtige Festung…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#1" value="… Erhebt euch, oh Adepta Sororitas…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#2" value="… Oh Imperator, der Du bist unser helles Licht…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#3" value="… Höre das Frohlocken unserer Waffen!"/>
	<entry name="SistersOfBattle/Exorcist:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Exorcist:Broken#0" value="… Imperator, erbarme Dich meiner."/>
	<entry name="SistersOfBattle/Exorcist:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Hurt#0" value="… Aus tiefer Not rufe ich Dich an…"/>
	<entry name="SistersOfBattle/Exorcist:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Idle#0" value="Der Imperator lebt!"/>
	<entry name="SistersOfBattle/Exorcist:Idle#1" value="Ruhm! Ruhm! Ruhm!"/>
	<entry name="SistersOfBattle/Exorcist:Idle#2" value="Der Imperator hat Sein flinkes Schwert gezückt."/>
	<entry name="SistersOfBattle/Exorcist:Idle#3" value="Erzittere vor Furcht!"/>
	<entry name="SistersOfBattle/Exorcist:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Exorcist:Shaken#0" value="Imperator, schütze uns mit Deinem Blut und Deiner Rechtschaffenheit!"/>
	<entry name="SistersOfBattle/Exorcist:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Victory#0" value="Die Schlange zertreten und dann weiter…"/>
	<entry name="SistersOfBattle/Exorcist:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Werdet Zeuge des wundersamen Mysteriums der Lebenden Heiligen!"/>
	<entry name="SistersOfBattle/Exorcist:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#0" value="Kommt näher, Häretiker."/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#1" value="Spürt den Zorn des Imperators!"/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#2" value="Der Glaube ist unser Schild."/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#3" value="Wir werden euch zur ewigen Ruhe betten."/>
	<entry name="SistersOfBattle/CelestianSacresant:AttackCount" value="4"/>
	<entry name="SistersOfBattle/CelestianSacresant:Broken#0" value="Wir können unser Werk nicht vollenden. Wir müssen--"/>
	<entry name="SistersOfBattle/CelestianSacresant:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Hurt#0" value="Unsere Mission droht zu scheitern…"/>
	<entry name="SistersOfBattle/CelestianSacresant:HurtCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#0" value="Wir haben die Galaxis durchquert, um… hier zu enden."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#1" value="Unsere Panzerung ist undurchdringlich."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#2" value="Unsere Tugendhaftigkeit sucht ihresgleichen."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#3" value="Wir sind das Ideal, dem alle nacheifern."/>
	<entry name="SistersOfBattle/CelestianSacresant:IdleCount" value="4"/>
	<entry name="SistersOfBattle/CelestianSacresant:Shaken#0" value="Nicht einen Schritt zurück!"/>
	<entry name="SistersOfBattle/CelestianSacresant:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Victory#0" value="Wir haben unsere Pflicht getan."/>
	<entry name="SistersOfBattle/CelestianSacresant:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Unsere Namensgeberin, unsere Herrin! Unsere Suche ist zu Ende."/>
	<entry name="SistersOfBattle/CelestianSacresant:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#0" value="Für die Ehre des Questor Mechanicus!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#1" value="Wir sind nur eine Waffe in der rechten Hand des Imperators."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#2" value="Fürchtet meinen Angriff!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#3" value="Ritter, zum Angriff!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AttackCount" value="4"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Broken#0" value="Welch Schurkerei…?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Hurt#0" value="Nur Geschwindigkeit kann mich noch retten."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:HurtCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#0" value="Während Ihr zaudert, roste ich."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#1" value="Principalis, benutze mich!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#2" value="Meine Lanze muss eingesetzt werden."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#3" value="Die Maschinengeister lechzen nach Ruhm!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:IdleCount" value="4"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Shaken#0" value="Was soll daran ruhmreich sein?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Victory#0" value="Sie konnten meiner Kampfeswut nicht standhalten."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Eine… Eine lebende Heilige?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:EnemyUnit:AdeptusMechanicus/KnightCrusader#0" value="Ein würdiger Gegner! Komm, lass uns kämpfen."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:EnemyUnit:AdeptusMechanicus/KnightCrusaderCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Attack#0" value="Der Imperator wurde Zeuge ihres Todes und sah, dass es gut war."/>
	<entry name="SistersOfBattle/Dialogus:Attack#1" value="Rastet nicht, Schwestern, solange sie noch leben!"/>
	<entry name="SistersOfBattle/Dialogus:Attack#2" value="Häretikern begegnet man nicht mit Worten, sondern mit dem Flammenwerfer."/>
	<entry name="SistersOfBattle/Dialogus:Attack#3" value="Gemeinsam sind wir stark!"/>
	<entry name="SistersOfBattle/Dialogus:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Dialogus:Broken#0" value="Buße ist Strafe genug."/>
	<entry name="SistersOfBattle/Dialogus:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Hurt#0" value="Was ist schon eine Wunde mehr für eine Gläubige?"/>
	<entry name="SistersOfBattle/Dialogus:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Idle#0" value="Vox-Übertragung: überprüft. Sensoria-Vorrichtung: überprüft."/>
	<entry name="SistersOfBattle/Dialogus:Idle#1" value="Heute sollt ihr ruhen, denn morgen sind wir bei Ihm."/>
	<entry name="SistersOfBattle/Dialogus:Idle#2" value="Der Imperator liebt euch alle."/>
	<entry name="SistersOfBattle/Dialogus:Idle#3" value="Fürchtet nicht die Xenos."/>
	<entry name="SistersOfBattle/Dialogus:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Dialogus:Shaken#0" value="Auch wenn ich durch Tod und Mühsal schreiten muss…"/>
	<entry name="SistersOfBattle/Dialogus:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Victory#0" value="Bleib liegen, damit ich höher stehen kann."/>
	<entry name="SistersOfBattle/Dialogus:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Mir fehlen die Worte… Celestine höchstselbst!"/>
	<entry name="SistersOfBattle/Dialogus:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Attack#0" value="Und sehet, der Chor der Gläubigen erklingt."/>
	<entry name="SistersOfBattle/Headquarters:Attack#1" value="Unsere Mauern sind gewappnet."/>
	<entry name="SistersOfBattle/Headquarters:Attack#2" value="Unsere Schreine sind nicht wehrlos."/>
	<entry name="SistersOfBattle/Headquarters:Attack#3" value="Ihr irrt euch, wenn ihr uns für schwach haltet."/>
	<entry name="SistersOfBattle/Headquarters:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Headquarters:Broken#0" value="Unsere Mauern… bröckeln?"/>
	<entry name="SistersOfBattle/Headquarters:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Hurt#0" value="Der Schrein wurde getroffen!"/>
	<entry name="SistersOfBattle/Headquarters:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Idle#0" value="Wir beten zum Gottimperator."/>
	<entry name="SistersOfBattle/Headquarters:Idle#1" value="Wir läutern Häretiker."/>
	<entry name="SistersOfBattle/Headquarters:Idle#2" value="Wir verstreuen heilige Asche."/>
	<entry name="SistersOfBattle/Headquarters:Idle#3" value="Wir bilden Novizinnen aus."/>
	<entry name="SistersOfBattle/Headquarters:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Headquarters:Shaken#0" value="Hinter die Schutzwälle, schnell!"/>
	<entry name="SistersOfBattle/Headquarters:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Victory#0" value="Stellt nicht unsere Wälle des Glaubens auf die Probe."/>
	<entry name="SistersOfBattle/Headquarters:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Ach, würde sie doch zu uns kommen… Celestine!"/>
	<entry name="SistersOfBattle/Headquarters:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Attack#0" value="Meine Instrumente töten und heilen."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#1" value="Wir hassen euch, Schänder des Glaubens!"/>
	<entry name="SistersOfBattle/Hospitaller:Attack#2" value="Bleibt stark im Namen des Imperators!"/>
	<entry name="SistersOfBattle/Hospitaller:Attack#3" value="Glückseligkeit im Kampfe ist unser Lohn."/>
	<entry name="SistersOfBattle/Hospitaller:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Hospitaller:Broken#0" value="Ich muss mich zurückziehen, um die Jüngeren zu beschützen."/>
	<entry name="SistersOfBattle/Hospitaller:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Hurt#0" value="Heilerin… Heile dich selbst."/>
	<entry name="SistersOfBattle/Hospitaller:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Idle#0" value="In der Stärke pulsiert das Leben."/>
	<entry name="SistersOfBattle/Hospitaller:Idle#1" value="Unsere Wunden sind Zeugnisse unseres Schaffens."/>
	<entry name="SistersOfBattle/Hospitaller:Idle#2" value="Wo soll ich unsere Schreine errichten?"/>
	<entry name="SistersOfBattle/Hospitaller:Idle#3" value="Hass ist keine Sünde, wenn das Universum so schändlich ist."/>
	<entry name="SistersOfBattle/Hospitaller:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Hospitaller:Shaken#0" value="Meine Aufgabe ist es zu heilen… Warum bin ich hier?"/>
	<entry name="SistersOfBattle/Hospitaller:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Victory#0" value="Hier können meine heilenden Hände nichts mehr ausrichten."/>
	<entry name="SistersOfBattle/Hospitaller:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Meine Wunden… schließen sich von selbst. Wie ist das möglich?"/>
	<entry name="SistersOfBattle/Hospitaller:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Attack#0" value="Lasst mich raus, lasst mich raus, lasst mich raus!"/>
	<entry name="SistersOfBattle/Mortifier:Attack#1" value="Es tut mir leid, es tut mir leid, es tut mir leid!"/>
	<entry name="SistersOfBattle/Mortifier:Attack#2" value="Sterben, sterben, sterben!"/>
	<entry name="SistersOfBattle/Mortifier:Attack#3" value="Warum sterbe ich nicht?"/>
	<entry name="SistersOfBattle/Mortifier:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Mortifier:Broken#0" value="Brecht mich noch mehr, damit mein Bewusstsein schwindet."/>
	<entry name="SistersOfBattle/Mortifier:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Hurt#0" value="Hol mich, Tod. Jetzt…"/>
	<entry name="SistersOfBattle/Mortifier:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Idle#0" value="Imperator, erlöse mich!"/>
	<entry name="SistersOfBattle/Mortifier:Idle#1" value="Lass mich sterben!"/>
	<entry name="SistersOfBattle/Mortifier:Idle#2" value="Ja, ich habe gesündigt, aber das…"/>
	<entry name="SistersOfBattle/Mortifier:Idle#3" value="Welch Pein…"/>
	<entry name="SistersOfBattle/Mortifier:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Mortifier:Shaken#0" value="Tod, komm näher!"/>
	<entry name="SistersOfBattle/Mortifier:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Victory#0" value="Sie wurden erlöst… Aber was ist mit mir?"/>
	<entry name="SistersOfBattle/Mortifier:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Bitte… Bitte…"/>
	<entry name="SistersOfBattle/Mortifier:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#0" value="Eines Tages wird die Menschheit davon befreit sein."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#1" value="Ich bringe die Erlösung… durch Vernichtung."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#2" value="Ich führe im Namen des Imperators, auf dass Sein Wille geschehe."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#3" value="Er kennt mein Schicksal."/>
	<entry name="SistersOfBattle/SaintCelestine:AttackCount" value="4"/>
	<entry name="SistersOfBattle/SaintCelestine:Broken#0" value="Ich darf nicht wanken."/>
	<entry name="SistersOfBattle/SaintCelestine:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Hurt#0" value="Der Tod ist nicht das Ende, also warum ihn fürchten?"/>
	<entry name="SistersOfBattle/SaintCelestine:HurtCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#0" value="Lieber Dunkelheit, als vom Unreinen geblendet zu werden."/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#1" value="Ich lebe in den Herzen der Rechtschaffenen."/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#2" value="Sie würden mir selbst zum Auge des Schreckens folgen…"/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#3" value="Ich sitze hier fest."/>
	<entry name="SistersOfBattle/SaintCelestine:IdleCount" value="4"/>
	<entry name="SistersOfBattle/SaintCelestine:Shaken#0" value="Ich fürchte nicht den Tod."/>
	<entry name="SistersOfBattle/SaintCelestine:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Victory#0" value="Das ist euer Ende, nicht das meine."/>
	<entry name="SistersOfBattle/SaintCelestine:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#0" value="Für die Erlösung!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#1" value="An unseren Taten sollt ihr uns erkennen!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#2" value="Sühne ist ein gutes Werk!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#3" value="Unsere Eviscerator-Kettenschwerter sind wie Gebetsmühlen."/>
	<entry name="SistersOfBattle/SisterRepentia:AttackCount" value="4"/>
	<entry name="SistersOfBattle/SisterRepentia:Broken#0" value="Sie werden uns in Marterer stecken!"/>
	<entry name="SistersOfBattle/SisterRepentia:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Hurt#0" value="Unser Glaube ist unsere Rüstung!"/>
	<entry name="SistersOfBattle/SisterRepentia:HurtCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#0" value="Wir haben gesündigt."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#1" value="Feigheit im Angesicht des Feindes."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#2" value="Oh Herr, oh Imperator, wir bitten Dich um Vergebung."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#3" value="Ein Moment des Friedens vor dem Tod."/>
	<entry name="SistersOfBattle/SisterRepentia:IdleCount" value="4"/>
	<entry name="SistersOfBattle/SisterRepentia:Shaken#0" value="Vielleicht können wir keine Buße tun…"/>
	<entry name="SistersOfBattle/SisterRepentia:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Victory#0" value="Ein weiterer Schritt in Richtung Erlösung oder Tod."/>
	<entry name="SistersOfBattle/SisterRepentia:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Celestine war einst eine Repentia… unser Leuchtfeuer der Erlösung."/>
	<entry name="SistersOfBattle/SisterRepentia:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#0" value="Feuern im Flug!"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#1" value="Brennen sollt ihr!"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#2" value="Der Imperator führt uns!"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#3" value="Wenn der Himmel in Flammen steht, gehört ihr uns."/>
	<entry name="SistersOfBattle/Zephyrim:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Zephyrim:Broken#0" value="Fliegt, der Imperator beschützt uns!"/>
	<entry name="SistersOfBattle/Zephyrim:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Hurt#0" value="Unser Blut und unsere Wunden sind Worte des Gebets!"/>
	<entry name="SistersOfBattle/Zephyrim:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Idle#0" value="Wir sind die göttlichen Zerstörer."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#1" value="Wir jagen die Bösen und die Mächtigen."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#2" value="Auch Flammenzungen sind der Gebete mächtig."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#3" value="Der Engelschor erwartet Befehle."/>
	<entry name="SistersOfBattle/Zephyrim:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Zephyrim:Shaken#0" value="Wir formieren uns in den Wolken neu."/>
	<entry name="SistersOfBattle/Zephyrim:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Victory#0" value="Und so gehen alle Gottlosen zugrunde!"/>
	<entry name="SistersOfBattle/Zephyrim:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Ach, wären wir doch wahre Engel wie sie…"/>
	<entry name="SistersOfBattle/Zephyrim:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Dominion" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Immolator" value="SistersOfBattle/Exorcist"/>
	<entry name="SistersOfBattle/Lightning" value="SistersOfBattle/Avenger"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Retributor" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Castigator" value="SistersOfBattle/Exorcist"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#0" value="C32: Feuer-Template wird umgesetzt."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#1" value="7T32: Alle Waffen ausgerichtet."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#2" value="8-6: Sturm-Kataphron-Klade aktiv."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#3" value="P4-78: Panzer/Transporter suchen und zerstören."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Broken#0" value="Material verfügbar… Einst menschlich…"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Hurt#0" value="Funktionsfähigkeit beeinträchtigt. Maschine intakt. Fleisch 45%, abnehmend."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#0" value="Nekrotisches Gewebe wird herausgeschnitten."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#1" value="Servitor wartet auf Nutzung."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#2" value="Erinnerungen an Gras, Wolken, blauen Himmel… Formatierungsfehler. Erinnerungen werden gelöscht."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#3" value="Binhär-Chor setzt ein."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Shaken#0" value="Stehe unter Dauerbeschuss."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Victory#0" value="Eliminiert – auf brutale Weise und befriedigend."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#0" value="Unterdrückungsfeuer!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#1" value="Im Namen des Primarchen!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#2" value="Panzerangriff!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#3" value="Feuern aus einem Rohr… auf alle!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:AttackCount" value="4"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Broken#0" value="Jemand muss den Panzer bergen…"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Hurt#0" value="Direkt durch den Rumpf…"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:HurtCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#0" value="Was würde Dorn tun?"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#1" value="Stets im Dienste des Imperators."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#2" value="Armageddon, das war eine Panzerschlacht…"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#3" value="Weniger Infanterie, mehr Panzer."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:IdleCount" value="4"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Shaken#0" value="Die haben was auf dem Kasten…"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Victory#0" value="Stets auf der Suche nach neuen Zielen."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#0" value="Khrrrrne!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#1" value="Rrrrr!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#2" value="Wrrrrr…"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#3" value="K-k-k-k!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Broken#0" value="Sklzzz…"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Hurt#0" value="Ztt! Rrrr…"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#0" value="Crck."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#1" value="Blddd…"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#2" value="Mhnnn."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#3" value="Mrrrr."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Shaken#0" value="Hnh!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Victory#0" value="RRRRRRR!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:VictoryCount" value="1"/>
	<entry name="Eldar/DarkReaper:Attack#0" value="Wir wissen die Kunst der Zerstörung zu schätzen."/>
	<entry name="Eldar/DarkReaper:Attack#1" value="Wir bringen den Tod."/>
	<entry name="Eldar/DarkReaper:Attack#2" value="Im Namen des Khaine!"/>
	<entry name="Eldar/DarkReaper:Attack#3" value="Wir verfehlen nie unser Ziel."/>
	<entry name="Eldar/DarkReaper:AttackCount" value="4"/>
	<entry name="Eldar/DarkReaper:Broken#0" value="Wir sind es, die vernichtet werden sollen…"/>
	<entry name="Eldar/DarkReaper:BrokenCount" value="1"/>
	<entry name="Eldar/DarkReaper:Hurt#0" value="Der Vernichtung fällt jeder anheim."/>
	<entry name="Eldar/DarkReaper:HurtCount" value="1"/>
	<entry name="Eldar/DarkReaper:Idle#0" value="Altansar bleibt uns stets in Erinnerung."/>
	<entry name="Eldar/DarkReaper:Idle#1" value="Die Vernichtung ist nur eine Station auf unserem langen Lebensweg."/>
	<entry name="Eldar/DarkReaper:Idle#2" value="Ein furchtloser Tod ist unser Schicksal."/>
	<entry name="Eldar/DarkReaper:Idle#3" value="Der Zerstörer hat sich den Schmiedegott schon vor langer Zeit geholt."/>
	<entry name="Eldar/DarkReaper:IdleCount" value="4"/>
	<entry name="Eldar/DarkReaper:Shaken#0" value="Der Zerstörer hat sich gegen uns gestellt!"/>
	<entry name="Eldar/DarkReaper:ShakenCount" value="1"/>
	<entry name="Eldar/DarkReaper:Victory#0" value="Der Zerstörer hat sie sich geholt."/>
	<entry name="Eldar/DarkReaper:VictoryCount" value="1"/>
	<entry name="Necrons/Deathmark:Attack#0" value="Wir nehmen ihre Anführer ins Visier."/>
	<entry name="Necrons/Deathmark:Attack#1" value="Wir lösen ihre Synapsen auf."/>
	<entry name="Necrons/Deathmark:Attack#2" value="Ehrloser Feind vor dem Fadenkreuz."/>
	<entry name="Necrons/Deathmark:Attack#3" value="Wir zeichnen sie für den Tod."/>
	<entry name="Necrons/Deathmark:AttackCount" value="4"/>
	<entry name="Necrons/Deathmark:Broken#0" value="Kehren in Parallel-Dimension zurück!"/>
	<entry name="Necrons/Deathmark:BrokenCount" value="1"/>
	<entry name="Necrons/Deathmark:Hurt#0" value="Reanimationsprotokolle fehlgeschlagen."/>
	<entry name="Necrons/Deathmark:HurtCount" value="1"/>
	<entry name="Necrons/Deathmark:Idle#0" value="Wir überwachen die Kommunikation."/>
	<entry name="Necrons/Deathmark:Idle#1" value="Ist der Gaukler wirklich hier?"/>
	<entry name="Necrons/Deathmark:Idle#2" value="Wir orten Kommandoknoten."/>
	<entry name="Necrons/Deathmark:Idle#3" value="Enthauptungsprotokolle angehalten."/>
	<entry name="Necrons/Deathmark:IdleCount" value="4"/>
	<entry name="Necrons/Deathmark:Shaken#0" value="Wir sind Präzisionswerkzeuge, keine Ziele."/>
	<entry name="Necrons/Deathmark:ShakenCount" value="1"/>
	<entry name="Necrons/Deathmark:Victory#0" value="Wie erwartet eliminiert."/>
	<entry name="Necrons/Deathmark:VictoryCount" value="1"/>
	<entry name="Neutral/Poxwalker:Attack#0" value="Hehe."/>
	<entry name="Neutral/Poxwalker:Attack#1" value="Urrnnnn…"/>
	<entry name="Neutral/Poxwalker:Attack#2" value="Harrrr…"/>
	<entry name="Neutral/Poxwalker:Attack#3" value="Nurg Elll…"/>
	<entry name="Neutral/Poxwalker:AttackCount" value="4"/>
	<entry name="Neutral/Poxwalker:Broken#0" value="Vater…"/>
	<entry name="Neutral/Poxwalker:BrokenCount" value="1"/>
	<entry name="Neutral/Poxwalker:Hurt#0" value="Wir sterben…"/>
	<entry name="Neutral/Poxwalker:HurtCount" value="1"/>
	<entry name="Neutral/Poxwalker:Idle#0" value="… Seelen… gefangen…"/>
	<entry name="Neutral/Poxwalker:Idle#1" value="… Seuchengott…"/>
	<entry name="Neutral/Poxwalker:Idle#2" value="… Vater…"/>
	<entry name="Neutral/Poxwalker:Idle#3" value="… Uhhhh…"/>
	<entry name="Neutral/Poxwalker:IdleCount" value="4"/>
	<entry name="Neutral/Poxwalker:Shaken#0" value="… Naahhh…"/>
	<entry name="Neutral/Poxwalker:ShakenCount" value="1"/>
	<entry name="Neutral/Poxwalker:Victory#0" value="… Infiziert…"/>
	<entry name="Neutral/Poxwalker:VictoryCount" value="1"/>
	<entry name="Orks/DeffDread:Attack#0" value="Ich stampfä un' tötä!"/>
	<entry name="Orks/DeffDread:Attack#1" value="Da Einsatz hat sich gelohnt!"/>
	<entry name="Orks/DeffDread:Attack#2" value="Oh, ich hab ja Raket'n!"/>
	<entry name="Orks/DeffDread:Attack#3" value="Ich balla auf alles!"/>
	<entry name="Orks/DeffDread:AttackCount" value="4"/>
	<entry name="Orks/DeffDread:Broken#0" value="Ich mach die Fliegä!"/>
	<entry name="Orks/DeffDread:BrokenCount" value="1"/>
	<entry name="Orks/DeffDread:Hurt#0" value="Ich glaub, ich hab 'n Loch."/>
	<entry name="Orks/DeffDread:HurtCount" value="1"/>
	<entry name="Orks/DeffDread:Idle#0" value="Hier iss'n Squig drin!"/>
	<entry name="Orks/DeffDread:Idle#1" value="Kann jemand meinä Nasä kratz'n? Das macht mich varrückt!"/>
	<entry name="Orks/DeffDread:Idle#2" value="Wozu sin' die ganz'n Kabäl da?"/>
	<entry name="Orks/DeffDread:Idle#3" value="Laaaangweilig."/>
	<entry name="Orks/DeffDread:IdleCount" value="4"/>
	<entry name="Orks/DeffDread:Shaken#0" value="Wie vasteckt man sich mit so 'nem Ding?"/>
	<entry name="Orks/DeffDread:ShakenCount" value="1"/>
	<entry name="Orks/DeffDread:Victory#0" value="WAAAGH! Gargbots sin' da Bestän!"/>
	<entry name="Orks/DeffDread:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Attack#0" value="Und Celestine streckte sie nieder und sah, dass es gut war."/>
	<entry name="SistersOfBattle/Imagifier:Attack#1" value="Und sie waren die glorreichen Toten!"/>
	<entry name="SistersOfBattle/Imagifier:Attack#2" value="Ihr heiliger Zorn, oh meine Schwestern!"/>
	<entry name="SistersOfBattle/Imagifier:Attack#3" value="Trotzig stellten sie sich dem Feind entgegen!"/>
	<entry name="SistersOfBattle/Imagifier:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Imagifier:Broken#0" value="Mein Loblied… verstummt…"/>
	<entry name="SistersOfBattle/Imagifier:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Hurt#0" value="Sie erlitten schlimme Wunden, aber gaben nicht auf!"/>
	<entry name="SistersOfBattle/Imagifier:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Idle#0" value="Ich trage das Simulacrum Imperialis."/>
	<entry name="SistersOfBattle/Imagifier:Idle#1" value="In kurzen Augenblicken beschworen sie die Liebe des Imperators."/>
	<entry name="SistersOfBattle/Imagifier:Idle#2" value="… Und Celestine wusste, dass ihr Moment gekommen war…"/>
	<entry name="SistersOfBattle/Imagifier:Idle#3" value="Der Erzverräter umklammerte den gefallenen Engel mit einer einzigen Klaue…"/>
	<entry name="SistersOfBattle/Imagifier:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Imagifier:Shaken#0" value="Und verängstigt versiegelten sie das Tor der Ewigkeit…"/>
	<entry name="SistersOfBattle/Imagifier:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Victory#0" value="Und triumphierend kehrten sie zum heiligen Planeten Terra zurück!"/>
	<entry name="SistersOfBattle/Imagifier:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Attack#0" value="Raketen abgefeuert, Kommandant."/>
	<entry name="SpaceMarines/Whirlwind:Attack#1" value="Whirlwind schießt."/>
	<entry name="SpaceMarines/Whirlwind:Attack#2" value="Indirektes Sperrfeuer wirkungsvoll."/>
	<entry name="SpaceMarines/Whirlwind:Attack#3" value="Bereit für Flächenbombardement."/>
	<entry name="SpaceMarines/Whirlwind:AttackCount" value="4"/>
	<entry name="SpaceMarines/Whirlwind:Broken#0" value="Wir ziehen uns zurück."/>
	<entry name="SpaceMarines/Whirlwind:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Hurt#0" value="In Reichweite von Handfeuerwaffen… Schaden erlitten."/>
	<entry name="SpaceMarines/Whirlwind:HurtCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Idle#0" value="Maschinengeister werden gewartet."/>
	<entry name="SpaceMarines/Whirlwind:Idle#1" value="Einsatzbereit, warten auf Befehle."/>
	<entry name="SpaceMarines/Whirlwind:Idle#2" value="Artillerie zum Weichklopfen des Feindes bereit."/>
	<entry name="SpaceMarines/Whirlwind:Idle#3" value="Raketen werden nachgeladen."/>
	<entry name="SpaceMarines/Whirlwind:IdleCount" value="4"/>
	<entry name="SpaceMarines/Whirlwind:Shaken#0" value="Das ist unsere Ordenswelt… Wie kann das sein?"/>
	<entry name="SpaceMarines/Whirlwind:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Victory#0" value="Meldung eingegangen, Ziel zerstört."/>
	<entry name="SpaceMarines/Whirlwind:VictoryCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Attack#0" value="Festhalten…"/>
	<entry name="Tau/RVarnaBattlesuit:Attack#1" value="Plasmafeuer entfesselt."/>
	<entry name="Tau/RVarnaBattlesuit:AttackCount" value="2"/>
	<entry name="Tau/RVarnaBattlesuit:Broken#0" value="Wir brauchen Unterstützung."/>
	<entry name="Tau/RVarnaBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Hurt#0" value="Reaktor undicht… Kein Rückzug."/>
	<entry name="Tau/RVarnaBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Idle#0" value="Was soll dieses Fu'llasso?"/>
	<entry name="Tau/RVarnaBattlesuit:Idle#1" value="Ho'or-ata-t'chel-Syndrom… Heute sind die Phantomschmerzen wirklich schlimm."/>
	<entry name="Tau/RVarnaBattlesuit:Idle#2" value="Shas'vre meldet sich zur Stelle."/>
	<entry name="Tau/RVarnaBattlesuit:Idle#3" value="Wie wird dieser Fio'tak eigentlich gebaut? Er ist undurchdringbar!"/>
	<entry name="Tau/RVarnaBattlesuit:IdleCount" value="4"/>
	<entry name="Tau/RVarnaBattlesuit:Shaken#0" value="Lur'tae'mont! Wo ist Commander Weitsicht, wenn man ihn braucht?"/>
	<entry name="Tau/RVarnaBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Victory#0" value="Da bewegt sich nichts mehr."/>
	<entry name="Tau/RVarnaBattlesuit:VictoryCount" value="1"/>
	<entry name="Tyranids/Tyrannocyte" value="Tyranids/Carnifex"/>
	<entry name="Drukhari/Archon:Attack#0" value="Zu leiden ist euer Schicksal!"/>
	<entry name="Drukhari/Archon:Attack#1" value="Welch Torheit, sich gegen mich zu stellen."/>
	<entry name="Drukhari/Archon:Attack#2" value="Ihr seid es kaum wert, getötet zu werden."/>
	<entry name="Drukhari/Archon:Attack#3" value="Oh, welch süße Angst und Pein…"/>
	<entry name="Drukhari/Archon:AttackCount" value="4"/>
	<entry name="Drukhari/Archon:Broken#0" value="Zurück, zurück in die Stadt der Schatten!"/>
	<entry name="Drukhari/Archon:BrokenCount" value="1"/>
	<entry name="Drukhari/Archon:Hurt#0" value="Ein Glückstreffer."/>
	<entry name="Drukhari/Archon:HurtCount" value="1"/>
	<entry name="Drukhari/Archon:Idle#0" value="Einfache Qualen, einfache Freuden."/>
	<entry name="Drukhari/Archon:Idle#1" value="Haben wir sie schon alle getötet? Ein Jammer."/>
	<entry name="Drukhari/Archon:Idle#2" value="Das Einzige, was diese Welt zu bieten hat, ist das Leid ihrer Bevölkerung."/>
	<entry name="Drukhari/Archon:Idle#3" value="So verbringt man ereignislose Äonen. Ahhh…"/>
	<entry name="Drukhari/Archon:IdleCount" value="4"/>
	<entry name="Drukhari/Archon:Shaken#0" value="Wenn das so weitergeht, lande ich unter dem Messer eines Haemonculus…"/>
	<entry name="Drukhari/Archon:ShakenCount" value="1"/>
	<entry name="Drukhari/Archon:Victory#0" value="Was habt ihr erwartet? Erbärmlich…"/>
	<entry name="Drukhari/Archon:VictoryCount" value="1"/>
	<entry name="Drukhari/Cronos:Attack#0" value="Hrrrr."/>
	<entry name="Drukhari/Cronos:Attack#1" value="… Datenspeicher…"/>
	<entry name="Drukhari/Cronos:Attack#2" value="Kthhh…"/>
	<entry name="Drukhari/Cronos:Attack#3" value="Rrrr…"/>
	<entry name="Drukhari/Cronos:AttackCount" value="4"/>
	<entry name="Drukhari/Cronos:Broken#0" value="…"/>
	<entry name="Drukhari/Cronos:BrokenCount" value="1"/>
	<entry name="Drukhari/Cronos:Hurt#0" value="Hrrr."/>
	<entry name="Drukhari/Cronos:HurtCount" value="1"/>
	<entry name="Drukhari/Cronos:Idle#0" value="Bssssst…"/>
	<entry name="Drukhari/Cronos:Idle#1" value="Trrrt."/>
	<entry name="Drukhari/Cronos:Idle#2" value="Tffffttt."/>
	<entry name="Drukhari/Cronos:Idle#3" value="Tcht."/>
	<entry name="Drukhari/Cronos:IdleCount" value="4"/>
	<entry name="Drukhari/Cronos:Shaken#0" value="Iiiiiii!"/>
	<entry name="Drukhari/Cronos:ShakenCount" value="1"/>
	<entry name="Drukhari/Cronos:Victory#0" value="Bsst."/>
	<entry name="Drukhari/Cronos:VictoryCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Attack#0" value="Die Kunst der Agonie!"/>
	<entry name="Drukhari/Haemonculus:Attack#1" value="Leidet, leidet, leidet!"/>
	<entry name="Drukhari/Haemonculus:Attack#2" value="Schmerz ist erquickend."/>
	<entry name="Drukhari/Haemonculus:Attack#3" value="Ich spiele mit ihren Nerven."/>
	<entry name="Drukhari/Haemonculus:AttackCount" value="4"/>
	<entry name="Drukhari/Haemonculus:Broken#0" value="Vielleicht ein andermal?"/>
	<entry name="Drukhari/Haemonculus:BrokenCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Hurt#0" value="Mein eigener Schmerz genügt."/>
	<entry name="Drukhari/Haemonculus:HurtCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Idle#0" value="Von welchen süßen Qualen koste ich als Nächstes?"/>
	<entry name="Drukhari/Haemonculus:Idle#1" value="Erinnere ich mich an den Niedergang?"/>
	<entry name="Drukhari/Haemonculus:Idle#2" value="Meine Experimente tragen Früchte."/>
	<entry name="Drukhari/Haemonculus:Idle#3" value="Wo sind meine Folterer? Ich brauche Aufmerksamkeit!"/>
	<entry name="Drukhari/Haemonculus:IdleCount" value="4"/>
	<entry name="Drukhari/Haemonculus:Shaken#0" value="Oh, sie sind kräftig… Ich muss sie haben!"/>
	<entry name="Drukhari/Haemonculus:ShakenCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Victory#0" value="Und, was haben wir gelernt?"/>
	<entry name="Drukhari/Haemonculus:VictoryCount" value="1"/>
	<entry name="Drukhari/Headquarters:Attack#0" value="Sie wagen es, unser Machtzentrum anzugreifen?"/>
	<entry name="Drukhari/Headquarters:Attack#1" value="Narren!"/>
	<entry name="Drukhari/Headquarters:Attack#2" value="Welch ein Wahnsinn, unsere Mauern anzugreifen."/>
	<entry name="Drukhari/Headquarters:Attack#3" value="Beißt euch an unseren Zinnen die Zähne aus!"/>
	<entry name="Drukhari/Headquarters:AttackCount" value="4"/>
	<entry name="Drukhari/Headquarters:Broken#0" value="Archon, rettet Eure loy-- Eure Versuchsobjekte!"/>
	<entry name="Drukhari/Headquarters:BrokenCount" value="1"/>
	<entry name="Drukhari/Headquarters:Hurt#0" value="Sie sind durchgebrochen. Aber nur Schwächlinge fanden den Tod."/>
	<entry name="Drukhari/Headquarters:HurtCount" value="1"/>
	<entry name="Drukhari/Headquarters:Idle#0" value="In Friedenszeiten nur Wohnraum."/>
	<entry name="Drukhari/Headquarters:Idle#1" value="In den Foltergärten herrscht heute Nacht emsiges Treiben."/>
	<entry name="Drukhari/Headquarters:Idle#2" value="Ein ganzes Zeitalter der Untätigkeit…"/>
	<entry name="Drukhari/Headquarters:Idle#3" value="Bescheidener Luxus, zum Glück nur vorübergehend."/>
	<entry name="Drukhari/Headquarters:IdleCount" value="4"/>
	<entry name="Drukhari/Headquarters:Shaken#0" value="Oh, ich will zurück in die Dunkle Stadt Commorragh!"/>
	<entry name="Drukhari/Headquarters:ShakenCount" value="1"/>
	<entry name="Drukhari/Headquarters:Victory#0" value="Wir sind wieder sicher."/>
	<entry name="Drukhari/Headquarters:VictoryCount" value="1"/>
	<entry name="Drukhari/Hellion:Attack#0" value="Der Nervenkitzel des Tötens!"/>
	<entry name="Drukhari/Hellion:Attack#1" value="Der Tod kommt von oben!"/>
	<entry name="Drukhari/Hellion:Attack#2" value="Rasend schlagen wir zu!"/>
	<entry name="Drukhari/Hellion:Attack#3" value="Ein überwältigendes Gefühl!"/>
	<entry name="Drukhari/Hellion:AttackCount" value="4"/>
	<entry name="Drukhari/Hellion:Broken#0" value="Weg, bevor sie uns nach unten reißen."/>
	<entry name="Drukhari/Hellion:BrokenCount" value="1"/>
	<entry name="Drukhari/Hellion:Hurt#0" value="Die Langsamen sind gefallen."/>
	<entry name="Drukhari/Hellion:HurtCount" value="1"/>
	<entry name="Drukhari/Hellion:Idle#0" value="Wir werden nicht warten!"/>
	<entry name="Drukhari/Hellion:Idle#1" value="Der Himmel ruft! Der Blitz lockt uns!"/>
	<entry name="Drukhari/Hellion:Idle#2" value="Stillstand ist eine Qual!"/>
	<entry name="Drukhari/Hellion:Idle#3" value="Entfesselt uns, Archon!"/>
	<entry name="Drukhari/Hellion:IdleCount" value="4"/>
	<entry name="Drukhari/Hellion:Shaken#0" value="Am Boden sterben wir."/>
	<entry name="Drukhari/Hellion:ShakenCount" value="1"/>
	<entry name="Drukhari/Hellion:Victory#0" value="Sie sterben schneller und schneller!"/>
	<entry name="Drukhari/Hellion:VictoryCount" value="1"/>
	<entry name="Drukhari/Incubi:Attack#0" value="Wir sind eins mit dem Klaivar."/>
	<entry name="Drukhari/Incubi:Attack#1" value="Der Todesstoß… genau so."/>
	<entry name="Drukhari/Incubi:Attack#2" value="Die Schwachen reichen wir Khaine dar."/>
	<entry name="Drukhari/Incubi:Attack#3" value="Bezahlt, um zu töten."/>
	<entry name="Drukhari/Incubi:AttackCount" value="4"/>
	<entry name="Drukhari/Incubi:Broken#0" value="Khaine vergibt Feiglingen nicht."/>
	<entry name="Drukhari/Incubi:BrokenCount" value="1"/>
	<entry name="Drukhari/Incubi:Hurt#0" value="Ein würdiger Gegner."/>
	<entry name="Drukhari/Incubi:HurtCount" value="1"/>
	<entry name="Drukhari/Incubi:Idle#0" value="Kein Verrat, bevor der Pakt nicht geschlossen ist."/>
	<entry name="Drukhari/Incubi:Idle#1" value="Unsere Zeit ist erkauft. Es wäre Verschwendung, uns nicht einzusetzen."/>
	<entry name="Drukhari/Incubi:Idle#2" value="In Hass steckt Reinheit."/>
	<entry name="Drukhari/Incubi:Idle#3" value="Folgt Arhra und dem Pfad der Verdammnis."/>
	<entry name="Drukhari/Incubi:IdleCount" value="4"/>
	<entry name="Drukhari/Incubi:Shaken#0" value="Niemand wird für solche wie uns auf dem Pfad der Trauer wandeln."/>
	<entry name="Drukhari/Incubi:ShakenCount" value="1"/>
	<entry name="Drukhari/Incubi:Victory#0" value="Ein Tribut für Khaine."/>
	<entry name="Drukhari/Incubi:VictoryCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#0" value="Beute!"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#1" value="Hochmütige Narren!"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#2" value="Sie stellen sich gegen die Fleischgeborenen? Hahaha…"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#3" value="Der Archon gibt die Befehle…"/>
	<entry name="Drukhari/KabaliteTrueborn:AttackCount" value="4"/>
	<entry name="Drukhari/KabaliteTrueborn:Broken#0" value="Zurück, bewahrt unser wertvolles Fleisch!"/>
	<entry name="Drukhari/KabaliteTrueborn:BrokenCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Hurt#0" value="Nur wahre Fleischgeborene halten noch stand."/>
	<entry name="Drukhari/KabaliteTrueborn:HurtCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#0" value="Bringt uns wimmernde Opfer!"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#1" value="Es ist klug, unsere kostbaren Leben nicht zu vergeuden."/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#2" value="Jahrtausende des Nichtstuns erwarten uns…"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#3" value="Oh, welch schöner Müßiggang!"/>
	<entry name="Drukhari/KabaliteTrueborn:IdleCount" value="4"/>
	<entry name="Drukhari/KabaliteTrueborn:Shaken#0" value="Wir sind kein Kanonenfutter!"/>
	<entry name="Drukhari/KabaliteTrueborn:ShakenCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Victory#0" value="Was haben sie erwartet? Wir sind Fleischgeborene."/>
	<entry name="Drukhari/KabaliteTrueborn:VictoryCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Attack#0" value="Unheil naht."/>
	<entry name="Drukhari/KabaliteWarrior:Attack#1" value="Kauert nieder, Sterbliche."/>
	<entry name="Drukhari/KabaliteWarrior:Attack#2" value="Commorragh fordert Tribut!"/>
	<entry name="Drukhari/KabaliteWarrior:Attack#3" value="Für die Kabale!"/>
	<entry name="Drukhari/KabaliteWarrior:AttackCount" value="4"/>
	<entry name="Drukhari/KabaliteWarrior:Broken#0" value="Wenn wir fliehen, beschert uns der Archon Schlimmeres als den Tod."/>
	<entry name="Drukhari/KabaliteWarrior:BrokenCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Hurt#0" value="Das werden sie bereuen…"/>
	<entry name="Drukhari/KabaliteWarrior:HurtCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Idle#0" value="Wir mögen keine Fleischgeborenen sein, aber wir sind wahre Drukhari."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#1" value="Bringt Gefangene für unsere Zielübungen."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#2" value="Schützt die Kabalenmitglieder und verheizt die Söldner."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#3" value="Commorragh, unsere imposante Heimat…"/>
	<entry name="Drukhari/KabaliteWarrior:IdleCount" value="4"/>
	<entry name="Drukhari/KabaliteWarrior:Shaken#0" value="Wir fürchten uns? SIE sollten sich fürchten!"/>
	<entry name="Drukhari/KabaliteWarrior:ShakenCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Victory#0" value="Narren! Die Kabale schickt keine Schwächlinge."/>
	<entry name="Drukhari/KabaliteWarrior:VictoryCount" value="1"/>
	<entry name="Drukhari/Raider:Attack#0" value="Tief gleiten, süßes Klingenruder."/>
	<entry name="Drukhari/Raider:Attack#1" value="Wir bringen Schmerzen…"/>
	<entry name="Drukhari/Raider:Attack#2" value="Bring uns näher…"/>
	<entry name="Drukhari/Raider:Attack#3" value="Das Summen der Ladung…"/>
	<entry name="Drukhari/Raider:AttackCount" value="4"/>
	<entry name="Drukhari/Raider:Broken#0" value="Zurückziehen, bevor sie uns erwischen!"/>
	<entry name="Drukhari/Raider:BrokenCount" value="1"/>
	<entry name="Drukhari/Raider:Hurt#0" value="Geschwindigkeit sollte unsere Rüstung sein!"/>
	<entry name="Drukhari/Raider:HurtCount" value="1"/>
	<entry name="Drukhari/Raider:Idle#0" value="Unsere Trophäenhaken sind leer!"/>
	<entry name="Drukhari/Raider:Idle#1" value="Stapelt die Körper hinten. Nur die Schwachen sterben."/>
	<entry name="Drukhari/Raider:Idle#2" value="Schärft das Klingenruder und die Finnen."/>
	<entry name="Drukhari/Raider:Idle#3" value="Nur Vect erinnert sich an die Vergnügungsjachten von einst…"/>
	<entry name="Drukhari/Raider:IdleCount" value="4"/>
	<entry name="Drukhari/Raider:Shaken#0" value="Schneller, auf sie!"/>
	<entry name="Drukhari/Raider:ShakenCount" value="1"/>
	<entry name="Drukhari/Raider:Victory#0" value="Steckt die Gefangenen auf die Trophäenhaken – tot oder lebendig."/>
	<entry name="Drukhari/Raider:VictoryCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#0" value="Wir durchlöchern den Boden, Archon!"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#1" value="Unsere Anmut bringt den Untergang."/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#2" value="Oh, flieh, Beute, flieh!"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#3" value="Massakriert sie! Lasst niemanden entkommen."/>
	<entry name="Drukhari/RazorwingJetfighter:AttackCount" value="4"/>
	<entry name="Drukhari/RazorwingJetfighter:Broken#0" value="Rettet eure Haut!"/>
	<entry name="Drukhari/RazorwingJetfighter:BrokenCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Hurt#0" value="Ich war nicht auf einen fairen Kampf aus!"/>
	<entry name="Drukhari/RazorwingJetfighter:HurtCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#0" value="Lieber hier als bei Todesrennen in der Arena…"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#1" value="Schieb es auf die Nachtwinde…"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#2" value="Wir sind die besten Raubjäger."/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#3" value="Aus der Distanz zu töten ist grausam, weil wir ihre Qualen nicht mitansehen können."/>
	<entry name="Drukhari/RazorwingJetfighter:IdleCount" value="4"/>
	<entry name="Drukhari/RazorwingJetfighter:Shaken#0" value="Sie schießen zurück?"/>
	<entry name="Drukhari/RazorwingJetfighter:ShakenCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Victory#0" value="Schön zweigeteilt. Sie wussten nicht, wie ihnen geschieht."/>
	<entry name="Drukhari/RazorwingJetfighter:VictoryCount" value="1"/>
	<entry name="Drukhari/Scourge:Attack#0" value="Tretet auf die Sterbenden, nicht auf den Schmutz."/>
	<entry name="Drukhari/Scourge:Attack#1" value="Raubvögel im Sturzflug…"/>
	<entry name="Drukhari/Scourge:Attack#2" value="Hört ihr den Wind von eurem Tod künden?"/>
	<entry name="Drukhari/Scourge:Attack#3" value="Reißt ihnen die Augen heraus, Geier!"/>
	<entry name="Drukhari/Scourge:AttackCount" value="4"/>
	<entry name="Drukhari/Scourge:Broken#0" value="Erhebt euch in die Lüfte!"/>
	<entry name="Drukhari/Scourge:BrokenCount" value="1"/>
	<entry name="Drukhari/Scourge:Hurt#0" value="Es zeugt von Tapferkeit, eine Harpyie zu töten."/>
	<entry name="Drukhari/Scourge:HurtCount" value="1"/>
	<entry name="Drukhari/Scourge:Idle#0" value="Boten der Dunklen Stadt."/>
	<entry name="Drukhari/Scourge:Idle#1" value="Werkzeuge für Intrigen."/>
	<entry name="Drukhari/Scourge:Idle#2" value="Unser Wert liegt in unseren Flügeln."/>
	<entry name="Drukhari/Scourge:Idle#3" value="Meine Flügel möchten gespannt werden."/>
	<entry name="Drukhari/Scourge:IdleCount" value="4"/>
	<entry name="Drukhari/Scourge:Shaken#0" value="Wir müssen höher fliegen!"/>
	<entry name="Drukhari/Scourge:ShakenCount" value="1"/>
	<entry name="Drukhari/Scourge:Victory#0" value="Hört nur, diese Schreie. Ahh…"/>
	<entry name="Drukhari/Scourge:VictoryCount" value="1"/>
	<entry name="Drukhari/Succubus:Attack#0" value="Amüsiert ihr euch nicht?"/>
	<entry name="Drukhari/Succubus:Attack#1" value="Noch mehr Verehrer? Uff."/>
	<entry name="Drukhari/Succubus:Attack#2" value="Ihr, die ihr gleich sterben werdet… seid gegrüßt."/>
	<entry name="Drukhari/Succubus:Attack#3" value="Welch jämmerliche Herausforderung ihr doch seid!"/>
	<entry name="Drukhari/Succubus:AttackCount" value="4"/>
	<entry name="Drukhari/Succubus:Broken#0" value="Ich werde nicht riskieren, mein Rouge zu verschmieren!"/>
	<entry name="Drukhari/Succubus:BrokenCount" value="1"/>
	<entry name="Drukhari/Succubus:Hurt#0" value="Sie haben mir zugesetzt!"/>
	<entry name="Drukhari/Succubus:HurtCount" value="1"/>
	<entry name="Drukhari/Succubus:Idle#0" value="Noch mehr Fanpost. Gähn…"/>
	<entry name="Drukhari/Succubus:Idle#1" value="In Commorragh lebt der Pöbel, und ich kontrolliere ihn."/>
	<entry name="Drukhari/Succubus:Idle#2" value="Sag dem Archon, dass ich mehr Süßes brauche."/>
	<entry name="Drukhari/Succubus:Idle#3" value="Hach, ich sehne mich zurück zur Zivilisation und zum wahren Leiden."/>
	<entry name="Drukhari/Succubus:Idle#4" value="Schatten und Staub. Furcht und Staunen."/>
	<entry name="Drukhari/Succubus:IdleCount" value="5"/>
	<entry name="Drukhari/Succubus:Shaken#0" value="Unsere Männer bekommen uns Todesbräute nicht mehr zu Gesicht!"/>
	<entry name="Drukhari/Succubus:ShakenCount" value="1"/>
	<entry name="Drukhari/Succubus:Victory#0" value="Ich bin… eine Überlebenskünstlerin."/>
	<entry name="Drukhari/Succubus:VictoryCount" value="1"/>
	<entry name="Drukhari/Tantalus:Attack#0" value="Schattenpulskanonen abfeuern!"/>
	<entry name="Drukhari/Tantalus:Attack#1" value="Seht ihr die Gegner wie Wellen weichen?"/>
	<entry name="Drukhari/Tantalus:Attack#2" value="Strafft die Äthersegel! Schnappt sie euch!"/>
	<entry name="Drukhari/Tantalus:Attack#3" value="Wenden und windwärts ausrichten."/>
	<entry name="Drukhari/Tantalus:AttackCount" value="4"/>
	<entry name="Drukhari/Tantalus:Broken#0" value="Rettet das Schiff!"/>
	<entry name="Drukhari/Tantalus:BrokenCount" value="1"/>
	<entry name="Drukhari/Tantalus:Hurt#0" value="Der Rumpf ist beschädigt! Wir sinken…"/>
	<entry name="Drukhari/Tantalus:HurtCount" value="1"/>
	<entry name="Drukhari/Tantalus:Idle#0" value="Schärft die Sensenklingen."/>
	<entry name="Drukhari/Tantalus:Idle#1" value="Vorsicht! Ein Kratzer und der Archon wird…"/>
	<entry name="Drukhari/Tantalus:Idle#2" value="Eine Konstruktion der Kabale des Dunklen Spiegels. Längst Geschichte…"/>
	<entry name="Drukhari/Tantalus:Idle#3" value="Welch Freude zu segeln!"/>
	<entry name="Drukhari/Tantalus:IdleCount" value="4"/>
	<entry name="Drukhari/Tantalus:Shaken#0" value="Der Wind ist abgeflaut!"/>
	<entry name="Drukhari/Tantalus:ShakenCount" value="1"/>
	<entry name="Drukhari/Tantalus:Victory#0" value="Gedenkt den Gefallenen, einst waren sie so groß und stolz wie ihr."/>
	<entry name="Drukhari/Tantalus:VictoryCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Attack#0" value="Bomben abgeworfen. Nicht ins Unlicht schauen!"/>
	<entry name="Drukhari/VoidravenBomber:Attack#1" value="Nachtlanze im Einsatz. Unlichtbombe bereit."/>
	<entry name="Drukhari/VoidravenBomber:Attack#2" value="Der Bordschütze hat das Kommando."/>
	<entry name="Drukhari/VoidravenBomber:Attack#3" value="Lautlos schweben wir…"/>
	<entry name="Drukhari/VoidravenBomber:AttackCount" value="4"/>
	<entry name="Drukhari/VoidravenBomber:Broken#0" value="Weg, sonst holen sie uns vom Himmel!"/>
	<entry name="Drukhari/VoidravenBomber:BrokenCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Hurt#0" value="Stört mich nicht bei der Arbeit!"/>
	<entry name="Drukhari/VoidravenBomber:HurtCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Idle#0" value="Pilot und Bordschütze."/>
	<entry name="Drukhari/VoidravenBomber:Idle#1" value="Ich muss meine nächste Symphonie der Zerstörung komponieren."/>
	<entry name="Drukhari/VoidravenBomber:Idle#2" value="Triebwerke abstellen. Wir wollen sie überraschen."/>
	<entry name="Drukhari/VoidravenBomber:Idle#3" value="Vorsicht mit der Antimaterie, ihr Narren!"/>
	<entry name="Drukhari/VoidravenBomber:IdleCount" value="4"/>
	<entry name="Drukhari/VoidravenBomber:Shaken#0" value="So kann ein Künstler nicht arbeiten!"/>
	<entry name="Drukhari/VoidravenBomber:ShakenCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Victory#0" value="Ein rauchender Krater vollendet mein Werk."/>
	<entry name="Drukhari/VoidravenBomber:VictoryCount" value="1"/>
	<entry name="Drukhari/Wrack:Attack#0" value="Realraumkreaturen…"/>
	<entry name="Drukhari/Wrack:Attack#1" value="Immer neue Qualen."/>
	<entry name="Drukhari/Wrack:Attack#2" value="Zeigt mir… gebt mir… euer Fleisch!"/>
	<entry name="Drukhari/Wrack:Attack#3" value="Kommt, kommt… auf den Operationstisch."/>
	<entry name="Drukhari/Wrack:AttackCount" value="4"/>
	<entry name="Drukhari/Wrack:Broken#0" value="Oh, das wird dem Meister nicht gefallen…"/>
	<entry name="Drukhari/Wrack:BrokenCount" value="1"/>
	<entry name="Drukhari/Wrack:Hurt#0" value="Ich bin ein Niemand. Niemand wurde verwundet."/>
	<entry name="Drukhari/Wrack:HurtCount" value="1"/>
	<entry name="Drukhari/Wrack:Idle#0" value="Ich wählte diese Daseinsform… aus Langeweile."/>
	<entry name="Drukhari/Wrack:Idle#1" value="Welche neuen Hypertoxine und entzückenden Gifte erwarten uns?"/>
	<entry name="Drukhari/Wrack:Idle#2" value="Gehorcht dem Acothysten, haben sie gesagt."/>
	<entry name="Drukhari/Wrack:Idle#3" value="Sind wir nicht bewundernswert?"/>
	<entry name="Drukhari/Wrack:IdleCount" value="4"/>
	<entry name="Drukhari/Wrack:Shaken#0" value="Beschützt den Meister!"/>
	<entry name="Drukhari/Wrack:ShakenCount" value="1"/>
	<entry name="Drukhari/Wrack:Victory#0" value="Mehr Versuchsobjekte für die Experimente des Meisters…"/>
	<entry name="Drukhari/Wrack:VictoryCount" value="1"/>
	<entry name="Drukhari/Wyche:Attack#0" value="Kämpft mit uns!"/>
	<entry name="Drukhari/Wyche:Attack#1" value="Seht zu und lernt… und dann sterbt!"/>
	<entry name="Drukhari/Wyche:Attack#2" value="Tänzelnd naht der Tod…"/>
	<entry name="Drukhari/Wyche:Attack#3" value="Süße, todbringende Anmut."/>
	<entry name="Drukhari/Wyche:AttackCount" value="4"/>
	<entry name="Drukhari/Wyche:Broken#0" value="Dafür wurde ich nicht ausgebildet."/>
	<entry name="Drukhari/Wyche:BrokenCount" value="1"/>
	<entry name="Drukhari/Wyche:Hurt#0" value="Wir bluten! Aber der Kampf hat gerade erst begonnen."/>
	<entry name="Drukhari/Wyche:HurtCount" value="1"/>
	<entry name="Drukhari/Wyche:Idle#0" value="Wir müssen üben."/>
	<entry name="Drukhari/Wyche:Idle#1" value="Mit unseren Taten nähren wir Commorragh."/>
	<entry name="Drukhari/Wyche:Idle#2" value="Wir wollen unser Können beweisen!"/>
	<entry name="Drukhari/Wyche:Idle#3" value="Handwerker, bringt uns unser Werkzeug."/>
	<entry name="Drukhari/Wyche:IdleCount" value="4"/>
	<entry name="Drukhari/Wyche:Shaken#0" value="Wissen sie unsere Darbietung nicht zu schätzen?!"/>
	<entry name="Drukhari/Wyche:ShakenCount" value="1"/>
	<entry name="Drukhari/Wyche:Victory#0" value="Verbeugt euch und lächelt für die Zuschauer, Schwestern."/>
	<entry name="Drukhari/Wyche:VictoryCount" value="1"/>
	<entry name="Drukhari/Ravager" value="Drukhari/Raider"/>
	<entry name="Drukhari/Reaver" value="Drukhari/RazorwingJetfighter"/>
	<entry name="Drukhari/Venom" value="Drukhari/Raider"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#0" value="Weihrauchausstoß wird aktiviert."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#1" value="Servitor segnen und angreifen!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#2" value="Vernichtung vor Ruhm!"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#3" value="Lanzen angelegt."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:AttackCount" value="4"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Broken#0" value="Beschützt die Maschinen!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:BrokenCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Hurt#0" value="Kritischer Schaden erlitten."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:HurtCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#0" value="Servitor im Ruhezustand."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#1" value="Sydonia ist so fern…"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#2" value="Erhaben über Zweifel und den Tod."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#3" value="Ewige Bewegung für ein ewiges Reich!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:IdleCount" value="4"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Shaken#0" value="Der Stelzenschreiter strauchelt!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:ShakenCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Victory#0" value="Für Vingh und Sydonia!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:VictoryCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#0" value="Ziele werden markiert."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#1" value="Der Wind wird berücksichtigt."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#2" value="Mit Vergnügen, Sir."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#3" value="Feuerlösung berechnet, Sir."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:AttackCount" value="4"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Broken#0" value="Geschütz zurücklassen!"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:BrokenCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Hurt#0" value="Volltreffer – aber leider bei uns…"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:HurtCount" value="1"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#0" value="Das Armeeleben ist verdammt hart."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#1" value="Welcher Schütze?"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#2" value="Kann man noch tiefer sinken als hier?"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#3" value="Keine Ziele? Perfekt."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:IdleCount" value="4"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Shaken#0" value="Ich sagte doch, ich bin krank."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:ShakenCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Victory#0" value="Haben wir-- Ich glaube, wir haben sie erwischt!"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="ChaosSpaceMarines/ChaosSpaceMarine"/>
	<entry name="Eldar/Wraithlord" value="Eldar/Wraithblade"/>
	<entry name="Necrons/SkorpekhDestroyer" value="Necrons/HeavyDestroyer"/>
	<entry name="Orks/BurnaBoyz:Attack#0" value="Wir heiz'n ihnen ein!"/>
	<entry name="Orks/BurnaBoyz:Attack#1" value="Höhöhö! Guckt mal, wie se brenn'!"/>
	<entry name="Orks/BurnaBoyz:Attack#2" value="Es brennt lichtaloh!"/>
	<entry name="Orks/BurnaBoyz:Attack#3" value="Wir sin' Feua un' Flammä!"/>
	<entry name="Orks/BurnaBoyz:AttackCount" value="4"/>
	<entry name="Orks/BurnaBoyz:Broken#0" value="Wir ham nich' mal gezündelt!"/>
	<entry name="Orks/BurnaBoyz:BrokenCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Hurt#0" value="Autsch, hab mir die Finga vabrannt!"/>
	<entry name="Orks/BurnaBoyz:HurtCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Idle#0" value="Zünd'ln tu ich gernä!"/>
	<entry name="Orks/BurnaBoyz:Idle#1" value="Wir woll'n was vabrenn'!"/>
	<entry name="Orks/BurnaBoyz:Idle#2" value="Ich will die Welt gar nich' in Brand steck'n… oda doch?"/>
	<entry name="Orks/BurnaBoyz:Idle#3" value="Na los, Grottie, gib ma Feua!"/>
	<entry name="Orks/BurnaBoyz:IdleCount" value="4"/>
	<entry name="Orks/BurnaBoyz:Shaken#0" value="Das brennt, brennt, brennt!"/>
	<entry name="Orks/BurnaBoyz:ShakenCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Victory#0" value="Das Inferno is' vorbei!"/>
	<entry name="Orks/BurnaBoyz:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#0" value="Exkulpieren!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#1" value="Purgatus!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#2" value="In extremis!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#3" value="Töte mich!!!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:AttackCount" value="4"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Broken#0" value="Nnnnn--"/>
	<entry name="SistersOfBattle/ArcoFlagellant:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Hurt#0" value="Ich bin dir näher, mein Imperator!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:HurtCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#0" value="Am Schlummern…"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#1" value="Glaube…"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#2" value="Entfessle… mich."/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#3" value="Ich… gehorche."/>
	<entry name="SistersOfBattle/ArcoFlagellant:IdleCount" value="4"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Shaken#0" value="Gemartert?"/>
	<entry name="SistersOfBattle/ArcoFlagellant:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Victory#0" value=""/>
	<entry name="SistersOfBattle/ArcoFlagellant:VictoryCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#0" value="Stirb durch meine Hand!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#1" value="Es gibt nur den Krieg!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#2" value="Niemand wird überleben!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#3" value="Wir ebnen den Weg."/>
	<entry name="SpaceMarines/AssaultTerminator:AttackCount" value="4"/>
	<entry name="SpaceMarines/AssaultTerminator:Broken#0" value="Ein Hinterhalt!"/>
	<entry name="SpaceMarines/AssaultTerminator:BrokenCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Hurt#0" value="Taktische Dreadnought-Rüstung hält stand."/>
	<entry name="SpaceMarines/AssaultTerminator:HurtCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#0" value="Ein Augenblick des Müßiggangs…"/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#1" value="Unsere Rüstungen sind einsatzbereit."/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#2" value="Erste Kompanie zur Stelle."/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#3" value="Wir segnen unsere Rüstungen."/>
	<entry name="SpaceMarines/AssaultTerminator:IdleCount" value="4"/>
	<entry name="SpaceMarines/AssaultTerminator:Shaken#0" value="In Seinem Namen!"/>
	<entry name="SpaceMarines/AssaultTerminator:ShakenCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Victory#0" value="Für den Imperator!"/>
	<entry name="SpaceMarines/AssaultTerminator:VictoryCount" value="1"/>
	<entry name="Tau/FireWarriorBreacher" value="Tau/FireWarrior"/>
	<entry name="Tyranids/Biovore" value="Tyranids/Carnifex"/>
	<entry name="Drukhari/Talos" value="Drukhari/Cronos"/>
	<entry name="Necrons/CanoptekReanimator" value="Necrons/CanoptekScarab"/>
	<entry name="Tyranids/NornEmissary" value="Tyranids/Carnifex"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#0" value="Hoch die Lanzen!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#1" value="Ruhm in der Schlacht!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#2" value="Für Attila und den Imperator!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#3" value="Los, los, los! Reitet sie nieder!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:AttackCount" value="4"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Broken#0" value="Rettet die Pferde!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Hurt#0" value="Narben ziemen sich für einen Krieger!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:HurtCount" value="1"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Idle#0" value="Die attilanische Tradition wird selbst diesen Befehlshaber überdauern."/>
	<entry name="AstraMilitarum/AttilanRoughRider:Idle#1" value="Werden wir jemals in die Steppe zurückkehren…?"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Idle#2" value="Todesreiter? Pah!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Idle#3" value="Einem Attilanischen Gardereiter bedeutet sein Pferd alles!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:IdleCount" value="4"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Shaken#0" value="Wie feige!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Victory#0" value="Ein würdiger Gegner!"/>
	<entry name="AstraMilitarum/AttilanRoughRider:VictoryCount" value="1"/>
	<entry name="Drukhari/Mandrake:Attack#0" value="Nur eine eiskalte Berührung…"/>
	<entry name="Drukhari/Mandrake:Attack#1" value="Würdige Gegner…"/>
	<entry name="Drukhari/Mandrake:Attack#2" value="Gute Beute…"/>
	<entry name="Drukhari/Mandrake:Attack#3" value="… ein Angriff aus den Schatten."/>
	<entry name="Drukhari/Mandrake:AttackCount" value="4"/>
	<entry name="Drukhari/Mandrake:Broken#0" value="Zurück in die Finsternis…"/>
	<entry name="Drukhari/Mandrake:BrokenCount" value="1"/>
	<entry name="Drukhari/Mandrake:Hurt#0" value="Der unwerte Niedergang…"/>
	<entry name="Drukhari/Mandrake:HurtCount" value="1"/>
	<entry name="Drukhari/Mandrake:Idle#0" value="Kheradruakh wartet."/>
	<entry name="Drukhari/Mandrake:Idle#1" value="Die Kälte brennt so sehr…"/>
	<entry name="Drukhari/Mandrake:Idle#2" value="Aelindrach ruft."/>
	<entry name="Drukhari/Mandrake:Idle#3" value="Die Angst der Unseren nährt uns…"/>
	<entry name="Drukhari/Mandrake:IdleCount" value="4"/>
	<entry name="Drukhari/Mandrake:Shaken#0" value="Sie wagen es…?"/>
	<entry name="Drukhari/Mandrake:ShakenCount" value="1"/>
	<entry name="Drukhari/Mandrake:Victory#0" value="Ein Jammer… Welche Beute bleibt uns noch?"/>
	<entry name="Drukhari/Mandrake:VictoryCount" value="1"/>
	<entry name="Orks/SquighogBoy:Attack#0" value="Benutzt die Spießa!"/>
	<entry name="Orks/SquighogBoy:Attack#1" value="WAAAGH!"/>
	<entry name="Orks/SquighogBoy:Attack#2" value="Holt die Gitz!"/>
	<entry name="Orks/SquighogBoy:Attack#3" value="Fangt die Biesta!"/>
	<entry name="Orks/SquighogBoy:AttackCount" value="4"/>
	<entry name="Orks/SquighogBoy:Broken#0" value="Die ham mia die Zähnä eingeschlag'n!"/>
	<entry name="Orks/SquighogBoy:BrokenCount" value="1"/>
	<entry name="Orks/SquighogBoy:Hurt#0" value=""/>
	<entry name="Orks/SquighogBoy:HurtCount" value="1"/>
	<entry name="Orks/SquighogBoy:Idle#0" value="Zähmt die Squigs!"/>
	<entry name="Orks/SquighogBoy:Idle#1" value="Warum krump'n wa nich'?"/>
	<entry name="Orks/SquighogBoy:Idle#2" value="Die alt'n Wegä sin' die bestän!"/>
	<entry name="Orks/SquighogBoy:Idle#3" value="Wea hat mein' Sattelgit vaputzt?!"/>
	<entry name="Orks/SquighogBoy:IdleCount" value="4"/>
	<entry name="Orks/SquighogBoy:Shaken#0" value="Wiss'n die nich', wea wia sin'?"/>
	<entry name="Orks/SquighogBoy:ShakenCount" value="1"/>
	<entry name="Orks/SquighogBoy:Victory#0" value="Wia ham die bestän Grunzasquigs!"/>
	<entry name="Orks/SquighogBoy:VictoryCount" value="1"/>
	<entry name="Eldar/Vyper:Attack#0" value="Bei Kurnous!"/>
	<entry name="Eldar/Vyper:Attack#1" value="Bruder, dort!"/>
	<entry name="Eldar/Vyper:Attack#2" value="Schnell zuschlagen!"/>
	<entry name="Eldar/Vyper:Attack#3" value="Der Jäger schlägt zu."/>
	<entry name="Eldar/Vyper:AttackCount" value="4"/>
	<entry name="Eldar/Vyper:Broken#0" value="Flieh, Bruder, flieh!"/>
	<entry name="Eldar/Vyper:BrokenCount" value="1"/>
	<entry name="Eldar/Vyper:Hurt#0" value="Unseren Ahnen näher…"/>
	<entry name="Eldar/Vyper:HurtCount" value="1"/>
	<entry name="Eldar/Vyper:Idle#0" value="Ich spüre deinen Kummer, Bruder. Das geht vorüber."/>
	<entry name="Eldar/Vyper:Idle#1" value="Immer bereit aufzubrechen."/>
	<entry name="Eldar/Vyper:Idle#2" value="Führen Übungsmanöver durch."/>
	<entry name="Eldar/Vyper:Idle#3" value="Ich sehne mich nach den Phantomkristalltürmen des Weltenschiffes!"/>
	<entry name="Eldar/Vyper:IdleCount" value="4"/>
	<entry name="Eldar/Vyper:Shaken#0" value="Zu nah, Bruder, zu nah!"/>
	<entry name="Eldar/Vyper:ShakenCount" value="1"/>
	<entry name="Eldar/Vyper:Victory#0" value="Das Alter siegt."/>
	<entry name="Eldar/Vyper:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#0" value="Resonanz: Übereinstimmung."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#1" value="Heulen: Transsonische Waffen."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#2" value="Zorn: Freigesetzt."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#3" value="Geschwächt: Feind."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Broken#0" value="Mut: Verloren."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Hurt#0" value="Wiederhole: Tod."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Idle#0" value="Untätig: Müßiggang."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Idle#1" value="Datenanforderung: Entspannungslochstreifen."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Idle#2" value="Erlernen: Lingua Technis."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Idle#3" value="Chorda-Klauen: Bereit."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Shaken#0" value="Gefahr: Extrem."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Victory#0" value="Triumph: Erwartet."/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:VictoryCount" value="1"/>
</language>
