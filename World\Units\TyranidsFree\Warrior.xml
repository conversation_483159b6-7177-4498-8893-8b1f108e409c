<?xml version="1.0" encoding="utf-8"?>
<unit>
	<model>
		<unit mesh="Units/Tyranids/Warrior"
				material="Units/Tyranids/Warrior"
				idleAnimation="Units/Tyranids/WarriorIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				scale="0.9 0.9 0.9"
				bloodBone="Bone001"
				walker="1"/>
	</model>
	<group size="3" rowSize="3" memberDeltaX="70" memberDeltaY="80"/>
	<weapons>
		<weapon name="Devourer">
			<model>
				<projectileWeapon muzzleBone=".Muzzle"
						fireInterval="0.166666666667"/>
			</model>
		</weapon>
		<weapon name="ScythingTalons">
			<model>
				<weapon fireInterval="0.366666666667"/>
			</model>
		</weapon>
		<weapon name="FleshHooks" requiredUpgrade="Tyranids/FleshHooks">
			<model>
				<projectileWeapon muzzleBone="Bone001" fireInterval="10.0"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="6"/> <!-- %armor base armor=4+ -->
				<biomassUpkeep base="3.0"/> <!-- %biomassUpkeep base tier=7 factor=1 -->
				<biomassCost base="60.0"/> <!-- %biomassCost base tier=7 factor=1 -->
				<hitpointsMax base="9.0"/> <!-- %hitpointsMax base toughness=4 wounds=3 -->
				<meleeAccuracy base="10"/> <!-- %meleeAccuracy base weaponSkill=5 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="1.5"/> <!-- %strengthDamage base strength=4 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=9 -->
				<movementMax base="3"/>
				<productionCost base="42.0"/> <!-- %productionCost base tier=7 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseInfantryScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/WarriorAttack"
						beginFire="0.666666666667"
						endFire="2.33333333333"
						chargeAnimation="Units/Tyranids/WarriorCharge"
						chargeBeginFire="0.333333333333"
						chargeEndFire="1.16666666667"
						meleeAnimation="Units/Tyranids/WarriorMelee"
						meleeBeginSwing="0.533333333333"
						meleeEndSwing="1.0"/>
			</model>				
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/WarriorDie"
						animationCount="2"
						sound="Units/MediumUnarmoredDie"
						soundCount="4"
						soundDelay="0.5"
						voiceSound="Units/Tyranids/MediumDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/WarriorMove"
						sound="Units/Tyranids/InfantryMove"
						soundCount="4"/>
			</model>
		</move>
		<genericUnitAbility name="Tyranids/SynapseCreature" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/SynapseLink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<genericUnitAbility name="Tyranids/ShadowInTheWarp" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1" excludeRadius="0">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/ShadowInTheWarp"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="ExtraInfantryArmour" requiredUpgrade="Tyranids/ExtraInfantryArmour"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="Tyranids/InfantryUpkeep" requiredUpgrade="Tyranids/InfantryUpkeep"/>
		<trait name="Tyranids/ToxinSacs" requiredUpgrade="Tyranids/ToxinSacs"/>
		<trait name="VeryBulky"/>
	</traits>
</unit>
