<?xml version="1.0" encoding="utf-8"?>
<unit>
	<model>
		<unit mesh="Units/Tyranids/Trygon"
				material="Units/Tyranids/Trygon"
				idleAnimation="Units/Tyranids/TrygonIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				scale="1.2 1.2 1.2"
				bloodBone="BloodBone"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="BioElectricPulse">
			<model>
				<beamWeapon muzzleBone="BloodBone" fireInterval="0.2"/>
			</model>
		</weapon>
		<weapon name="ScythingTalons" count="2">
			<model>
				<weapon fireInterval="1.0"/>
			</model>
		</weapon>
		<weapon name="Toxinspike" requiredUpgrade="Tyranids/Toxinspike">
			<model>
				<weapon fireInterval="1.0"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="6.0"/> <!-- %biomassUpkeep base tier=9 factor=1 -->
				<biomassCost base="120.0"/> <!-- %biomassCost base tier=9 factor=1 -->
				<hitpointsMax base="42.0"/> <!-- %hitpointsMax base toughness=6 wounds=7 -->
				<meleeAccuracy base="10"/> <!-- %meleeAccuracy base weaponSkill=5 -->
				<meleeAttacks base="3"/>
				<strengthDamage base="3"/> <!-- %strengthDamage base strength=6 -->
				<moraleMax base="8"/> <!-- %moraleMax base leadership=7 -->
				<movementMax base="3"/>
				<productionCost base="54.0"/> <!-- %productionCost base tier=9 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseVehiclesScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/TrygonAttack"
						beginFire="0.35"
						endFire="2.5"
						chargeAnimation="Units/Tyranids/TrygonCharge"
						chargeBeginFire="0.333"
						chargeEndFire="1.167"
						meleeAnimation="Units/Tyranids/TrygonMelee"
						meleeBeginSwing="0.333"
						meleeEndSwing="0.666"/>
			</model>				
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/TrygonDie"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="1.0"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/TrygonMove"
						sound="Units/Tyranids/TrygonMove"
						soundCount="2"/>
			</model>
		</move>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				requiredActionPoints="0"
				requiredUpgrade="Tyranids/HammerOfWrath">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="HammerOfWrath" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>
		<constructImperialStrongpoint 
				cooldown="5"
				icon="Units/Tyranids/Tunnel"
				name="Tyranids/ConstructTunnel">
			<model>
				<action sound="Actions/TyranidsFoundCity"/>
			</model>
			<modifiers>
				<modifier visible="0">
					<effects>
						<biomassCost base="20"/>
						<influenceCost base="10"/>
					</effects>
				</modifier>
			</modifiers>
			<beginTargets>
				<target rangeMax="1">
					<conditions>
						<tile>
							<noFeatureCategory name="Resource"/>
							<noFeatureCategory name="WireWeed"/>
							<land/>
							<noEnemyCity/>
							<noUnit/>
							<noFeature name="GravityWaves"/>
						</tile>
					</conditions>
					<areas>
						<area affects="Tile">
							<modifiers>
								<modifier>
									<effects>
										<addUnit name="Tyranids/Tunnel"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</target>
			</beginTargets>
		</constructImperialStrongpoint>
		<instinctiveBehaviourOverride consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/InstinctiveBehaviourOverride"
				requiredActionPoints="0">
			<model>
				<action sound="Actions/HiveMindBuff"/>
			</model>
			<modifiers>
				<modifier requiredUpgrade="Tyranids/InstinctiveBehaviourOverride2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
			</modifiers>
			<conditions>
				<unit>
					<noTrait name="Tyranids/InstinctiveBehaviourOverride"/>
					<noTrait name="Tyranids/SynapseLink"/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/InstinctiveBehaviourOverride" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</instinctiveBehaviourOverride>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Fearless"/>
		<trait name="Fleet"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="Tyranids/InstinctiveBehaviour"/>
		<trait name="MonstrousCreature"/>
		<trait name="MoveThroughCover"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<trait name="Relentless"/>
		<trait name="Smash"/>
		<trait name="Tyranids/ToxinSacs" requiredUpgrade="Tyranids/ToxinSacs"/>
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
	</traits>
</unit>
