<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#0" value="Vecteur entré."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#1" value="Cinq sur cinq."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#2" value="Début du vol de reconnaissance"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#3" value="Début de la saturation en obus."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Broken#0" value="Préservation du capital aérien"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Hurt#0" value="Ailes coupées"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#0" value="On tourne, on tourne."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#1" value="Distribution de libation aérienne."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#2" value="Changement de pression. Morphose des nanofibres."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#3" value="Préparation de l'évacuation des Land Crawlers."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Shaken#0" value="Mayday, mayday !"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Victory#0" value="Plus aucune signature ennemie détectée. Retour au cycle."/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#0" value="Direction des Kastelans."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#1" value="Onction des runes."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#2" value="Suppression de l'animus de silice."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#3" value="Insertion des puces de doctrines."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Broken#0" value="…Les Hommes de Fer…"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Hurt#0" value="…Irréparable…"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#0" value="La chair est faible, la machine est forte."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#1" value="En binaire, rythme et chant. J'aimerais que tu entendes."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#2" value="Il ne peut y avoir d'amélioration aux plans de l'Omnimessie."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#3" value="In nomine imperii et mechanici et spiritus omnissiah."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Shaken#0" value="Puces de retraite trouvées."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Victory#0" value="Ancienne sagesse, puissance prouvée."/>
	<entry name="AdeptusMechanicus/CyberneticaDatasmith:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#0" value="VESTRA INDUSTRIA NOSTRUM"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#1" value="LE GACHIS EST UN PECHE"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#2" value="POUR LA FORCE MOTRICE !"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#3" value="L'ECLAT SE MEUT EN MOI"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Broken#0" value="Nostra potex decit… notre volonté cède !"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Hurt#0" value="NOUS MARCHONS TOUJOURS"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#0" value="JE PLEURE LES LARMES DE L'OMNIMESSIE"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#1" value="MAUDIT SOIT LES CORPUSCARII"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#2" value="MON ELECTRO-TATOUAGE A FAIM"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#3" value="IN NOMINE DEI MECHANICI"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Shaken#0" value="ETINCELLE ETEINTE"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Victory#0" value="NOUS SOMME LA VIGUEUR PERSONNIFIEE"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#0" value="Ils vont goûter à la sagesse de Mars !"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#1" value="Solution longue distance trouvée."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#2" value="Data-câble ciblé accepté."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#3" value="Esprits de la Machine libérés."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Broken#0" value="Dominus, nous devons-"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Hurt#0" value="Serviteurs mono-tâche en souffrance."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#0" value="Serviteurs en repos."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#1" value="Huilage du châssis."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#2" value="Excision de la faiblesse."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#3" value="Rêve d'une planète rouge…"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Shaken#0" value="Solutions de tir ennemies supérieures !"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:Victory#0" value="Pendant 10 000 ans, notre victoire a été certaine."/>
	<entry name="AdeptusMechanicus/IronstriderBallistarius:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#0" value="C32: Schéma De Tir Réussi."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#1" value="8-7: Clade De Destructeurs Actif."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#2" value="7T32: Toutes Armes Sorties."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#3" value="867: Ignition De Cognis Et-ou Phosphore."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Broken#0" value="Seuls Les Hommes Craignent. Autrefois Nous Étions-"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Hurt#0" value="Fonctionnalité Compromise. Chair Toujours Existante."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#0" value="T418: Serviteur Inactif Présent."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#1" value="182: En Attente De Maintenance."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#2" value="ERREUR: Qu'Etais-Je ?"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#3" value="326: Mémoires Réapparaissant… Supprimées."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Shaken#0" value="991: Échappée D'émotions."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:Victory#0" value="T1: Fonction Remplie, Prochaine Cible."/>
	<entry name="AdeptusMechanicus/KataphronDestroyer:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#0" value="Nous ne sommes qu'une arme dans la main droite de l'Empereur."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#1" value="Pour l'honneur du Questor Mechanicus !"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#2" value="Craignez mon avancée !"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Attack#3" value="JE SUIS UN CHEVALIER IMPERIAL !"/>
	<entry name="AdeptusMechanicus/KnightCrusader:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Broken#0" value="Ce… C'est impossible."/>
	<entry name="AdeptusMechanicus/KnightCrusader:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Hurt#0" value="Plutôt la mort que le déshonneur."/>
	<entry name="AdeptusMechanicus/KnightCrusader:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#0" value="Ma dame attend."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#1" value="Pour les joutes de mon monde natal."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#2" value="Ma maison est réputée pour son humilité, vous savez."/>
	<entry name="AdeptusMechanicus/KnightCrusader:Idle#3" value="Ce géant de fer attend."/>
	<entry name="AdeptusMechanicus/KnightCrusader:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Shaken#0" value="TU OSES."/>
	<entry name="AdeptusMechanicus/KnightCrusader:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KnightCrusader:Victory#0" value="Il n'y a pas de honte à tomber par ma main."/>
	<entry name="AdeptusMechanicus/KnightCrusader:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#0" value="On purge, Dominus."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#1" value="On purge toujours, Dominus."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#2" value="Mes serres font des coupures nettes."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#3" value="Vous ne pouvez pas échapper au phosphore, imbéciles."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Broken#0" value="Déjà en vol…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Hurt#0" value="Notre purge est proche…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#0" value="Nettoyer… doit nettoyer."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#1" value="Mes moignons… me démangent."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#2" value="Toujours en vol."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:IdleCount" value="3"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Shaken#0" value="La saleté est… partout."/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Victory#0" value="Les cendres aux cendres…"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#0" value="Libération de la Force Motrice, tch."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#1" value="Galvanisation des Skitarii, ouiii…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#2" value="…lllance magnarail…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#3" value="Ne sommes-nous pas plein de vigueur ? !"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Broken#0" value="La logique veut que ça ne se produit pas vraiment."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Hurt#0" value="Nous… Nous n'avions pas prévu ça."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#0" value="Oh, j'ai une fuite d'énergie."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#1" value="Utilisation de la cellule galvanique."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#2" value="Trop de recharge, pas assez de service."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#3" value="Pour comprendre les Xenos…"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Shaken#0" value="Protégez votre Magos, insectes !"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:Victory#0" value="Leur bio-électricité… est à moi."/>
	<entry name="AdeptusMechanicus/TechPriestManipulus:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#0" value="A couvert, conservez vos munitions."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#1" value="J'ai besoin de vous vivant, Skitarii."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#2" value="Pas de dispute, pour le moment on se bat."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#3" value="J'ai le contrôle !"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Broken#0" value="Nous sommes jetables…"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Hurt#0" value="Je meurs.. ? Sauvez le Magos !"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#0" value="Je suis vos plans, Magos."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#1" value="Pourrions-nous avoir quelques Skitarii ?"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#2" value="Nous vivons pour mourir, Magos."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#3" value="Servo crâne, je te nomme Morte."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:Shaken#0" value="Appliquez l'édit de contrôle !"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker:Attack#0" value="Résonance : Égalée."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Attack#1" value="Hurlement : Transonique."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Attack#2" value="Rage : libérée."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Attack#3" value="Estropié : ennemi."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:AttackCount" value="4"/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Broken#0" value="Courage: perdu"/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:BrokenCount" value="1"/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Hurt#0" value="Répéter : Mort"/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:HurtCount" value="1"/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Idle#0" value="Inactif : en boucle."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Idle#1" value="Demande de données : cassettes de relaxation."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Idle#2" value="Apprendre : Lingua Technis."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Idle#3" value="Griffe-cordes : Prêt."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:IdleCount" value="4"/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Shaken#0" value="Danger : extrême."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:ShakenCount" value="1"/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:Victory#0" value="Triomphe: Attendu."/>
    <entry name="AdeptusMechanicus/SicarianRuststalker:VictoryCount" value="1"/>
	
	
	<entry name="AdeptusMechanicus/SkitariiMarshal:Victory#0" value="Nos pertes sont minimes, Magos."/>
	<entry name="AdeptusMechanicus/SkitariiMarshal:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#0" value="Les rad-troopers sont sur le champ de bataille !"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#1" value="Voyons qui sera tué par le radium en premier…"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#2" value="Carabines allumées."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#3" value="Rien ne nous arrêtera."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Broken#0" value="Et si on mourait plus tard ?"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Hurt#0" value="On perd des radiations et du sang là."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#0" value="J'ai 99 améliorations, mais la résistance aux radiations n'en est pas une."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#1" value="Je brûle."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#2" value="Argh… Je me sens pas super bien."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#3" value="Je veux dire, je mangerais pas à côté de nous non plus."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Shaken#0" value="Magos, on meure pour vous."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:Victory#0" value="Il ne leur reste même pas une demi-vie."/>
	<entry name="AdeptusMechanicus/SkitariiVanguard:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#0" value="Je joue mon coup, tu meurs."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#1" value="Pions, avancez."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#2" value="Je vois toutes les pièces."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#3" value="Je joue tous les coups."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Broken#0" value="Une défense devient nécessaire… Loujine ?"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Hurt#0" value="Viser le roi, c'est malin."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#0" value="Aucun cycle n'est inactif."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#1" value="L'inefficacité devient l'entropie."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#2" value="Quelle joie de manipuler."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#3" value="Oui… Je vois leurs coups et leurs contres."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Shaken#0" value="C'est inattendu, un gambit. C'est admirable."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/TechPriestDominus:Victory#0" value="J'avais prévu cette victoire il y a plusieurs coups. Mais elle n'en reste pas moins douce."/>
	<entry name="AdeptusMechanicus/TechPriestDominus:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#0" value="La ville attend."/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#1" value="Leurs vies nous appartiennent, pas à vous."/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#2" value="Les serviteurs tirent."/>
	<entry name="AdeptusMechanicus/Headquarters:Attack#3" value="Forge au combat."/>
	<entry name="AdeptusMechanicus/Headquarters:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/Headquarters:Broken#0" value="La ville va tomber."/>
	<entry name="AdeptusMechanicus/Headquarters:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Hurt#0" value="Nous subissons des pertes civiles. C'est tolérable."/>
	<entry name="AdeptusMechanicus/Headquarters:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#0" value="Une révolte est étouffée."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#1" value="Des clones sont envoyés aux berceaux de protoserviteurs."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#2" value="Au nom de l'Omnimessie, les hab-sanctuaires sont nettoyés."/>
	<entry name="AdeptusMechanicus/Headquarters:Idle#3" value="La production d'implants bioniques a augmenté de 0.1%"/>
	<entry name="AdeptusMechanicus/Headquarters:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/Headquarters:Shaken#0" value="Les masses sont terrifiées. Les prévôts ont été envoyés."/>
	<entry name="AdeptusMechanicus/Headquarters:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Victory#0" value="Menaces externes éliminées. Attention tournée sur les dissensions."/>
	<entry name="AdeptusMechanicus/Headquarters:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="AdeptusMechanicus/ArchaeopterTransvector"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="AdeptusMechanicus/IronstriderBallistarius"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="AdeptusMechanicus/IronstriderBallistarius"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="AdeptusMechanicus/SkitariiVanguard"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#0" value="C32 : schéma de tir réalisé."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Attack#1" value="7T32 : Tous les armements sont déployés."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Attack#2" value="8-6 : Clade brécheur actif."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Attack#3" value="P4-78 : Recherche et destruction de blindés/transporteurs."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:AttackCount" value="4"/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Broken#0" value="Biens jetables—humain une fois -"/>
    <entry name="AdeptusMechanicus/KataphronBreacher:BrokenCount" value="1"/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Hurt#0" value="Fonctionnalité compromise. Machine existante. Chair 45%, en déclin."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:HurtCount" value="1"/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Idle#0" value="Tissu nécrotique en cours d'excision."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Idle#1" value="Serviteur en attente d'expulsion."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Idle#2" value="Mémoire d'herbe, de nuages, de ciel bleu Erreur d'effacement de l'esprit. Effacement."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Idle#3" value="Commencement du chœur binharique."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:IdleCount" value="4"/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Shaken#0" value="Sous un feu soutenu."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:ShakenCount" value="1"/>
    <entry name="AdeptusMechanicus/KataphronBreacher:Victory#0" value="Éliminé, brutalement. Satisfaisant."/>
    <entry name="AdeptusMechanicus/KataphronBreacher:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/AttilanRoughRider:Attack#0" value="Préparez vos lances !"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Attack#1" value="Au combat, gloire !"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Attack#2" value="Pour Attila et l'Empereur !"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Attack#3" value="En avant, en avant, en avant ! Écrasez-les !"/>
    <entry name="AstraMilitarum/AttilanRoughRider:AttackCount" value="4"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Broken#0" value="Sauvez les chevaux !"/>
    <entry name="AstraMilitarum/AttilanRoughRider:BrokenCount" value="1"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Hurt#0" value="Les cicatrices vont bien aux guerriers !"/>
    <entry name="AstraMilitarum/AttilanRoughRider:HurtCount" value="1"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Idle#0" value="La tradition d'Attilan survivra même à ce commandant."/>
    <entry name="AstraMilitarum/AttilanRoughRider:Idle#1" value="Retournerons-nous un jour dans la steppe… ?"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Idle#2" value="Des cavaliers de la mort ? Pfff."/>
    <entry name="AstraMilitarum/AttilanRoughRider:Idle#3" value="Le cheval d'un Attilan, c'est sa vie !"/>
    <entry name="AstraMilitarum/AttilanRoughRider:IdleCount" value="4"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Shaken#0" value="Lâche !" />
    <entry name="AstraMilitarum/AttilanRoughRider:ShakenCount" value="1"/>
    <entry name="AstraMilitarum/AttilanRoughRider:Victory#0" value="Un adversaire digne de ce nom !" />
    <entry name="AstraMilitarum/AttilanRoughRider:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedFaction:SpaceMarines#0" value="Space Marines en vue, commandant. Les canaux de communication sont ouverts."/>
	<entry name="AstraMilitarum/Baneblade:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Seigneur Commissaire en vue. Préparation en cours pour le conduire plus près de l'ennemi."/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/TankCommander#0" value="Ravi de voir un officier qui apprécie à sa juste valeur le blindage, chef !"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/TankCommanderCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Artefact#0" value="Structure Xenos en vue."/>
	<entry name="AstraMilitarum/Baneblade:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Attack#0" value="Solution de tir tracée."/>
	<entry name="AstraMilitarum/Baneblade:Attack#1" value="Cible acquise."/>
	<entry name="AstraMilitarum/Baneblade:Attack#2" value="On tire les canons 1 à 11, chef."/>
	<entry name="AstraMilitarum/Baneblade:AttackCount" value="3"/>
	<entry name="AstraMilitarum/Baneblade:Broken#0" value="Marche arrière engagée, chef."/>
	<entry name="AstraMilitarum/Baneblade:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Cover#0" value="On ne sait pas trop comment, mais le char est à couvert, chef."/>
	<entry name="AstraMilitarum/Baneblade:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:AstraMilitarum#0" value="Traîtres en vue, chef."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:Necrons#0" value="Xenos en vue, chef. On se prépare à leur montrer ce qu'est un vrai monstre de métal."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:Orks#0" value="Peaux-vertes dans la zone, chef. Ils ne vont pas faire de notre char un Skullhamma !"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:SpaceMarines#0" value="Adeptus Astartes hostiles détectés. Pas de Fellblade en vue."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyUnit:Neutral/Enslaver#0" value="Chef, une retraite rapide est conseillée. Nous ne pouvons pas garantir que ce véhicule n'a pas été compromis."/>
	<entry name="AstraMilitarum/Baneblade:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Hurt#0" value="Des dommages sévères ont été subis, chef. Un retour à Mars pour être réparé est conseillé."/>
	<entry name="AstraMilitarum/Baneblade:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Idle#0" value="On trace un sentier avec le char, chef."/>
	<entry name="AstraMilitarum/Baneblade:Idle#1" value="On vérifie que les onze canons soient prêts à déchaîner l'enfer sur nos ennemis, chef."/>
	<entry name="AstraMilitarum/Baneblade:Idle#2" value="L'ennemi est faible, chef. On va l'aider à échouer."/>
	<entry name="AstraMilitarum/Baneblade:Idle#3" value="Je compte les rivets, chef."/>
	<entry name="AstraMilitarum/Baneblade:Idle#4" value="Quand je serais grand, je veux être un Hellhammer. Chef."/>
	<entry name="AstraMilitarum/Baneblade:Idle#5" value="Vous êtes au courant que Vance Stubbs a perdu une centaine de Baneblades, chef ? Il est un peu négligent."/>
	<entry name="AstraMilitarum/Baneblade:IdleCount" value="6"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory0#0" value="Prêt à reconquérir Gladius Prime, chef."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory0Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory1#0" value="On reforme les rangs de la Garde, chef."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory1Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory2#0" value="Chef, avec tout mon respect, ce Technoprêtre ne mérite même pas d'huiler nos tourelles latérales."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory2Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory3#0" value="On laisse l'exploration souterraines à l'infanterie. Chef."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory3Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory4#0" value="On peut empêcher les Xenos de s'approcher, aucun problème, chef."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory4Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory5#0" value="Des Kastelans ! Nous aurons besoin des onze canons, chef !"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory5Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Shaken#0" value="Pas de retraite, chef ! Je ne suis pas sûr que cette machine ait une marche arrière de toute façon."/>
	<entry name="AstraMilitarum/Baneblade:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Victory#0" value="Cible éliminée, chef. On recharge tous les canons."/>
	<entry name="AstraMilitarum/Baneblade:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:WireWeed#0" value="Chef, je conseille que l'on bouge notre véhicule; l'équipage ne peut pas gérer le barbelé."/>
	<entry name="AstraMilitarum/Baneblade:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:AlliedFaction:SpaceMarines#0" value="Astartes alliés en vue, loué soit l'Empereur."/>
	<entry name="AstraMilitarum/Basilisk:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Commissaire en vue. Content qu'il soit sur le front… loin de nous."/>
	<entry name="AstraMilitarum/Basilisk:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Artefact#0" value="Nos obus n'égratignent même pas cette chose. En quoi est-elle faite ?"/>
	<entry name="AstraMilitarum/Basilisk:Artefact#1" value="Nous allons être purgés juste pour avoir vu ça."/>
	<entry name="AstraMilitarum/Basilisk:ArtefactCount" value="2"/>
	<entry name="AstraMilitarum/Basilisk:Attack#0" value="Coordonnées reçues, solution établie. Feu."/>
	<entry name="AstraMilitarum/Basilisk:Attack#1" value="Obus en route."/>
	<entry name="AstraMilitarum/Basilisk:Attack#2" value="Observateurs sur le terrain."/>
	<entry name="AstraMilitarum/Basilisk:Attack#3" value="La cible est sous nos tirs. Heureux qu'on soit loin au fond !"/>
	<entry name="AstraMilitarum/Basilisk:AttackCount" value="4"/>
	<entry name="AstraMilitarum/Basilisk:Broken#0" value="Abandonnez le canon et courez !"/>
	<entry name="AstraMilitarum/Basilisk:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Cover#0" value="Un endroit parfait pour une pièce d'artillerie. Ils ne nous verront pas arriver."/>
	<entry name="AstraMilitarum/Basilisk:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:AstraMilitarum#0" value="Nous avons toujours un cadeau pour les traîtres-132mm de la colère de l'Empereur."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:Necrons#0" value="Des hommes de métal ? 15 km semble être la distance parfaite pour les affronter."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:Orks#0" value="Ah, des Peaux-Vertes. Exactement ce pour quoi ce canon a été conçu."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:SpaceMarines#0" value="Des Marines renégats ? Je me demande si leurs halos les protègent de l'artillerie ?"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyUnit:Neutral/Enslaver#0" value="Qu'est-ce que c'est que ces CHOSES ? Gardez-les à distance, soldats."/>
	<entry name="AstraMilitarum/Basilisk:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Hurt#0" value="Le compartiment de l'équipage a été pénétré… C'est le bazar là-dedans."/>
	<entry name="AstraMilitarum/Basilisk:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Idle#0" value="Tu es au courant pour le Saint des Basilisks ? Il a été canonisé. Tu l'as ? Canonisé !"/>
	<entry name="AstraMilitarum/Basilisk:Idle#1" value="N'envoyez pas un Manticore pour faire le travaille d'un Basilisk."/>
	<entry name="AstraMilitarum/Basilisk:Idle#2" value="J'ai pitié des pauvres gars au front… Mais passez pour les r'joindre."/>
	<entry name="AstraMilitarum/Basilisk:Idle#3" value="Prévenez-nous quand il y a quelque chose sur quoi tirer."/>
	<entry name="AstraMilitarum/Basilisk:Idle#4" value="Rien ne se passe pendant la guerre… Et ça, de manière répétée."/>
	<entry name="AstraMilitarum/Basilisk:IdleCount" value="5"/>
	<entry name="AstraMilitarum/Basilisk:Shaken#0" value="Est-ce quelqu'un est réellement… en train de nous tirer dessus ?"/>
	<entry name="AstraMilitarum/Basilisk:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Victory#0" value="On transforme les ennemis en cratère depuis dix millénaires, m'sieur !"/>
	<entry name="AstraMilitarum/Basilisk:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:WireWeed#0" value="Du bio-barbelé ? Il y a pire position de tir, comme à l'intérieur de la Grande Faille…"/>
	<entry name="AstraMilitarum/Basilisk:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedFaction:SpaceMarines#0" value="Mecchef Marines !"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Bullgryns loyaux."/>
	<entry name="AstraMilitarum/Bullgryn:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Artefact#0" value="Gros truk ?"/>
	<entry name="AstraMilitarum/Bullgryn:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Attack#0" value="Bullgryns se battent, Mecchef."/>
	<entry name="AstraMilitarum/Bullgryn:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Broken#0" value="Ogryns pas si costauds."/>
	<entry name="AstraMilitarum/Bullgryn:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Cover#0" value="Cachés dans l'buisson."/>
	<entry name="AstraMilitarum/Bullgryn:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:AstraMilitarum#0" value="Attends, qui battre pour Empereur ? Eux ou nous ?"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:Necrons#0" value="Peux pas mâcher alu'. Zut."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:Orks#0" value="Orks. Costauds, bo, inteligen. Comme nous."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:SpaceMarines#0" value="Les fistons d'l'Empereur ? On doit s'battre avec eu ? !"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyUnit:Neutral/Enslaver#0" value="Nah, ché pas skisson."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Hurt#0" value="Ouille."/>
	<entry name="AstraMilitarum/Bullgryn:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#0" value="Cé Gros Malins… Y sont inteligen."/>
	<entry name="AstraMilitarum/Bullgryn:Idle#1" value="Un Ogryn sur la poitrine d'un Ork mort. Euh… deux ! Deux Ogryns…"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#2" value="On affûte nos matraques, m'sieur."/>
	<entry name="AstraMilitarum/Bullgryn:Idle#3" value="On fait s'kon nou dit. Et vous nous avez dit rien."/>
	<entry name="AstraMilitarum/Bullgryn:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Bullgryn:Shaken#0" value="Attends… Où kon va ?"/>
	<entry name="AstraMilitarum/Bullgryn:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Victory#0" value="Hein ? On a gagné ?"/>
	<entry name="AstraMilitarum/Bullgryn:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:WireWeed#0" value="Ouille, ma jamb !"/>
	<entry name="AstraMilitarum/Bullgryn:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:AlliedFaction:SpaceMarines#0" value="Loué soit l'Empereur, les Astartes sont là !"/>
	<entry name="AstraMilitarum/Guardsman:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:AlliedUnit:AstraMilitarum/LordCommissar#0" value="On gonfle la poitrine et on rentre le ventre les gars, voilà le Seigneur Commissaire."/>
	<entry name="AstraMilitarum/Guardsman:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Artefact#0" value="Est-ce que ça a été conçu par… des Xenos ?"/>
	<entry name="AstraMilitarum/Guardsman:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Attack#0" value="Est-ce quelqu'un voit ce sur quoi on tire ?"/>
	<entry name="AstraMilitarum/Guardsman:Attack#1" value="Des ennemis devant, et le commissaire derrière nous…"/>
	<entry name="AstraMilitarum/Guardsman:Attack#2" value="Donnez tout ce que vous avez !"/>
	<entry name="AstraMilitarum/Guardsman:Attack#3" value="Je voudrais que Creed soit là…"/>
	<entry name="AstraMilitarum/Guardsman:Attack#4" value="Bourrez-le de laser, on va peut-être survivre."/>
	<entry name="AstraMilitarum/Guardsman:AttackCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Broken#0" value="J'ai pas signé pour ça !"/>
	<entry name="AstraMilitarum/Guardsman:Broken#1" value="Fuyez les gars, et espérez que le Commissaire regarde pas !"/>
	<entry name="AstraMilitarum/Guardsman:Broken#2" value="Ils sont en train de nous tuer ! Et on peut pas les tuer en retour, nous !"/>
	<entry name="AstraMilitarum/Guardsman:Broken#3" value="C'est un fichu Monde Mortel !"/>
	<entry name="AstraMilitarum/Guardsman:Broken#4" value="Où sont les renforts ? Où sont- !"/>
	<entry name="AstraMilitarum/Guardsman:BrokenCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Cover#0" value="De retour dans une tranchée, l'Empereur merci."/>
	<entry name="AstraMilitarum/Guardsman:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/Enslaver#0" value="Des Asservisseurs ? Oh, Empereur protège-nous."/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/Psychneuein#0" value="Des gros insectes de Prospero-gardez-les loin des psykers !"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnitNeutral/KhorneLordOfSkulls#0" value="Le Chaos ! Ici ?"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnitNeutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Hurt#0" value="On est fichus, chef."/>
	<entry name="AstraMilitarum/Guardsman:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Idle#0" value="Par le Trône d'Or… !"/>
	<entry name="AstraMilitarum/Guardsman:Idle#1" value="Pour Guilliman et l'Empereur !"/>
	<entry name="AstraMilitarum/Guardsman:Idle#2" value="Tout de suite, chef !"/>
	<entry name="AstraMilitarum/Guardsman:Idle#3" value="Le pire, c'est le silence avant le combat."/>
	<entry name="AstraMilitarum/Guardsman:Idle#4" value="On joue juste, chef, le Commissaire a dit que c'était bon pour le moral."/>
	<entry name="AstraMilitarum/Guardsman:Idle#5" value="La vie dans la Garde, chef. Terriblement difficile."/>
	<entry name="AstraMilitarum/Guardsman:Idle#6" value="Reçu cinq sur cinq, chef !"/>
	<entry name="AstraMilitarum/Guardsman:Idle#7" value="Chef, oui, chef !"/>
	<entry name="AstraMilitarum/Guardsman:Idle#8" value="Attention, le Commissaire arrive !"/>
	<entry name="AstraMilitarum/Guardsman:IdleCount" value="9"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory0#0" value="J'arrive toujours pas à croire qu'on ait survécu. Tellement sont…"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory0Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory1#0" value="On reforme l'équipe, chef."/>
	<entry name="AstraMilitarum/Guardsman:QuestStory1Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory2#0" value="Une nouvelle ville ? Est-on sûr-non, chef, je ne discute pas vos ordres, chef !"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory2Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory3#0" value="Ces tempêtes me donnent mal à la tête- va-t-on être piégé ici pour toujours ?"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory3Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory4#0" value="Ils sont dans la ville ! Repli !"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory4Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory5#0" value="Ma maman me disait toujours, 'Ne fais jamais confiance à un technoprêtre'. Elle avait raison, ma maman."/>
	<entry name="AstraMilitarum/Guardsman:QuestStory5Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#0" value="On peut prier ? Ô Empereur, Ô Guilliman, protégez-nous."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#1" value="Commandant, on se replie."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#2" value="Courage, les gars, courage."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#3" value="Si on reste, l'ennemi va nous descendre. Si on s'enfuie, c'est le Commissaire qui va nous descendre !"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#4" value="On peut avoir des meilleurs armes, s'il vous plaît ?"/>
	<entry name="AstraMilitarum/Guardsman:ShakenCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Victory#0" value="Monsieur le Commissaire, permission de fêter cette victoire ?"/>
	<entry name="AstraMilitarum/Guardsman:Victory#1" value="Ha ! J'ai l'impression qu'on a abattu un Titan avec un bâton lumineux."/>
	<entry name="AstraMilitarum/Guardsman:Victory#2" value="A la mémoire de Cadia et de Creed…"/>
	<entry name="AstraMilitarum/Guardsman:Victory#3" value="On gagne.. ? On gagne !"/>
	<entry name="AstraMilitarum/Guardsman:Victory#4" value="Wow, le Manuel a raison ! Ces Xenos sont faciles à battre !"/>
	<entry name="AstraMilitarum/Guardsman:VictoryCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:WireWeed#0" value="Chef, le barbelé, il est vivant !"/>
	<entry name="AstraMilitarum/Guardsman:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:AlliedFaction:SpaceMarines#0" value="Commandant, le QG a repéré des Space Marines alliés."/>
	<entry name="AstraMilitarum/Headquarters:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Ravi de vous voir sur le terrain, chef."/>
	<entry name="AstraMilitarum/Headquarters:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Artefact#0" value="Des artefacts Xenos sont dangereusement près de la ville, chef."/>
	<entry name="AstraMilitarum/Headquarters:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Attack#0" value="On maintient le contrôle du territoire."/>
	<entry name="AstraMilitarum/Headquarters:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Broken#0" value="Un dernier carré a été ordonné."/>
	<entry name="AstraMilitarum/Headquarters:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Cover#0" value="Comment… est-ce qu'une ville… est à couvert ?"/>
	<entry name="AstraMilitarum/Headquarters:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:AstraMilitarum#0" value="Des traîtres s'approchent de la ville, chef !"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:Necrons#0" value="Troupes Nécrons en approche."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:Orks#0" value="Les Peaux-Vertes sont vraiment très proches, chef."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:SpaceMarines#0" value="Des Spaces Marines approchent. Ils vont effacer la ville de la carte s'ils réussissent à s'infiltrer à l'intérieur."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyUnit:Neutral/Enslaver#0" value="ATTENTION : DES XENOS TRES DANGEREUX ONT ETE REPERES."/>
	<entry name="AstraMilitarum/Headquarters:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Hurt#0" value="L'infrastructure défensive a subis des dégâts !"/>
	<entry name="AstraMilitarum/Headquarters:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Idle#0" value="Des Gardes patrouillent."/>
	<entry name="AstraMilitarum/Headquarters:Idle#1" value="Toujours vigilant."/>
	<entry name="AstraMilitarum/Headquarters:Idle#2" value="Qui est en train de jouer avec le Tarot ? Rangez-le."/>
	<entry name="AstraMilitarum/Headquarters:Idle#3" value="Tout est calme, chef."/>
	<entry name="AstraMilitarum/Headquarters:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Headquarters:Shaken#0" value="On se replie vers les positions défensives, chef."/>
	<entry name="AstraMilitarum/Headquarters:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Victory#0" value="La ville est sécurisée, chef."/>
	<entry name="AstraMilitarum/Headquarters:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:WireWeed#0" value="Est-ce que construire cette ville sur du bio-barbelé était vraiment une si bonne idée ?"/>
	<entry name="AstraMilitarum/Headquarters:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedFaction:SpaceMarines#0" value="Sympa des Astartes de se pointer."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Où est-ce que les commissaires vont chercher leur déjeuner ? Au commissariat. Hé hé."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Artefact#0" value="Si on peut pas tirer sur ce truc, ça ne m'intéresse pas."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#0" value="Est-ce quelqu'un peut voir si la cible est toujours là ?"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#1" value="Par le Trône, le recul !"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#2" value="Équipe d'armes lourdes, on tire !"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#3" value="Ouvrez-les en deux !"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#4" value="Quand les pistolets-laser ne sont pas suffisants…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AttackCount" value="5"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Broken#0" value="Retraite, bande de chiens, retraite !"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Cover#0" value="Comme ils disent sur Krieg : Gardez vos mortiers à couvert, et ils vous couvriront."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:CoverCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:AstraMilitarum#0" value="Sales traîtres. Tirez-leur dessus !"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:Necrons#0" value="On va leur décoller leurs peaux de métal."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:Orks#0" value="Tirer sur des Peaux-Vertes est un gâchis de munition."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:SpaceMarines#0" value="Si vous devez tirer sur un Space Marine, assurez-vous de le toucher."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyUnit:Neutral/Enslaver#0" value="Tirez sur ces rejetons de l'enfer jusqu'à ce qu'ils retournent dans le warp !"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Hurt#0" value="J'ai pas le temps de saigner."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:HurtCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#0" value="Tu sais combien de temps il faut pour mettre une baïonnette sur un canon laser ?"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#1" value="Que les Xenos viennent à nous."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#2" value="Il y a quelque chose qui nous attend là-bas…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#3" value="On est comme un char qui peut toucher sa cible."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:IdleCount" value="4"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Shaken#0" value="On n'est pas censés se faire tirer dessus, chef."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Victory#0" value="Mort, évidemment."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:WireWeed#0" value="Sales pièges Xenos."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:AlliedFaction:SpaceMarines#0" value="Les anges sont avec nous."/>
	<entry name="AstraMilitarum/Hydra:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:AlliedUnit:AstraMilitarum/LordCommissar#0" value="C'est utile d'avoir un homme qui nous forcer à garder les pieds sur terre."/>
	<entry name="AstraMilitarum/Hydra:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Artefact#0" value="Quelque chose d'étrange."/>
	<entry name="AstraMilitarum/Hydra:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Attack#0" value="On garde les cieux clairs."/>
	<entry name="AstraMilitarum/Hydra:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Broken#0" value="On se retire vers un terrain plus élevé."/>
	<entry name="AstraMilitarum/Hydra:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Cover#0" value="Une couverture parfaite pour regarder les nuages."/>
	<entry name="AstraMilitarum/Hydra:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:AstraMilitarum#0" value="Des Impériaux, mais avec l'esprit embrumé."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:Necrons#0" value="Quelles machines volantes étranges, et pourtant si mortelles."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:Orks#0" value="Les unités volantes des Peaux-Vertes sont grossières mais efficaces."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:SpaceMarines#0" value="Est-ce que je peux abattre des Thunderhawks ? On va voir ça !"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyUnit:Neutral/Enslaver#0" value="Flotter n'est pas voler, espèce d'horreur."/>
	<entry name="AstraMilitarum/Hydra:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Hurt#0" value="On surveille les cieux, vous êtes supposés nous protéger."/>
	<entry name="AstraMilitarum/Hydra:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Idle#0" value="On surveille les nuages, chef."/>
	<entry name="AstraMilitarum/Hydra:Idle#1" value="Nimbus jaghatai, stratus rogal, cirrosanguinius…"/>
	<entry name="AstraMilitarum/Hydra:Idle#2" value="Les étoiles sont superbes ici."/>
	<entry name="AstraMilitarum/Hydra:Idle#3" value="Cette planète a les meilleurs couchers de soleil. Chef."/>
	<entry name="AstraMilitarum/Hydra:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Hydra:Shaken#0" value="Quelqu'un nous tire dessus, chef."/>
	<entry name="AstraMilitarum/Hydra:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Victory#0" value="Les cieux sont clairs, chef."/>
	<entry name="AstraMilitarum/Hydra:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:WireWeed#0" value="Des complications terrestres, chef."/>
	<entry name="AstraMilitarum/Hydra:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedFaction:SpaceMarines#0" value="Ici le bastion, on note des Adeptus Astartes dans notre périmètre- amicaux."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/Baneblade#0" value="Excitant de voir un Baneblade, chef. Surtout quand les canons sont pointés de l'autre côt."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/BanebladeCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Ici le bastion, on tient bon, chef."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Artefact#0" value="Ici le bastion, on note un artefact Xenos à proximité."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Attack#0" value="Ici le bastion, on déploie notre arsenal."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AttackCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Broken#0" value="Le bastion est perdu, je répète, le bastion est perdu."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:AstraMilitarum#0" value="Ici le bastion, on note des mouvements de traîtres à la périphérie."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:Necrons#0" value="Ici le bastion, on note des mouvements de Nécrons."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:Orks#0" value="Ici le bastion, on note une présence Ork."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:SpaceMarines#0" value="Ici le bastion… L'Empereur nous protège… Des Adeptus Astartes hostiles."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyUnit:Neutral/Enslaver#0" value="Ici le bastion, on note des Asservisseurs Xenos, chef."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Hurt#0" value="Ici le bastion-pas sûr qu'on survive à ça, chef."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:HurtCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#0" value="Un Garde surveille les alentours, chef."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#1" value="Toujours vigilant."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#2" value="Ici le bastion-Rien à signaler."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#3" value="Tout est calme, chef."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:IdleCount" value="4"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Shaken#0" value="Chef, vous nous entendez ? Nous sommes sous le feu ennemi."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Victory#0" value="Le bastion tient, chef."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:WireWeed#0" value="Le bio-barbelé infeste le centre de commande, chef."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedFaction:SpaceMarines#0" value="Les Marines ont rejoint la fête ! Chouette de leur part."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Un autre noble fantassin. Pourquoi est-ce que Yarrick est le seul à avoir son propre Baneblade ?"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Artefact#0" value="On protège l'artefact. Notre plaisir."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Attack#0" value="On envoie de sympathiques obus dans leur direction, chef."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AttackCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Broken#0" value="Malheureusement, chef, on se replie."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Cover#0" value="On est dans la forêt, on prépare une embuscade, chef."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:CoverCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:AstraMilitarum#0" value="On roulera sur les os des hérétiques avant la fin de la journée."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:Necrons#0" value="Ah, exactement nos cibles préférés-des Nécrons."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:Orks#0" value="On est prêt à s'auto-détruire- Les Orks adorent emprunter les chars Leman Russ.."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:SpaceMarines#0" value="Ce canon est conçu pour faire pleurer les Marines."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyUnit:Neutral/Enslaver#0" value="Des Xénos hostiles- Gardez-les à distance."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Hurt#0" value="Wow, celui-ci a complètement traversé le blindage !"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:HurtCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#0" value="Est-ce que Leman Russ pouvait conduire au moins ?"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#1" value="On trace un sentier."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#2" value="On tanke."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#3" value="J'aimerais que Pask soit le conducteur."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:IdleCount" value="4"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Shaken#0" value="Char en état de choc !"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Victory#0" value="Une autre victoire pour le char le plus populaire de la galaxie !"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:WireWeed#0" value="Du bio-barbelé, la seule chose plus courante que le Leman Russ."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedFaction:SpaceMarines#0" value="C'est toujours un plaisir de voir des Astartes loyaux… bien que surprenant."/>
	<entry name="AstraMilitarum/LordCommissar:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Ah, un camarade"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Artefact#0" value="Un sale artefact Xenos. On devrait brûler ça."/>
	<entry name="AstraMilitarum/LordCommissar:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Attack#0" value="Tirez-leur dessus, ou je vous descend !"/>
	<entry name="AstraMilitarum/LordCommissar:AttackCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Broken#0" value="Je ne peux pas laisser les soldats me voir comme ça !"/>
	<entry name="AstraMilitarum/LordCommissar:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Cover#0" value="C'est tactiquement pertinent de se cacher, mais pas très bon pour le moral."/>
	<entry name="AstraMilitarum/LordCommissar:CoverCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:AstraMilitarum#0" value="Traîtres ! Vermines ! VOUS ALLEZ SUBIR LA JUSTICE DE L'EMPEREUR !"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:Necrons#0" value="De la vermine métallique. Faisons-les fondre pour avoir plus de balles."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:Orks#0" value="Des bandits Peaux-Vertes. La rigueur impériale devrait vite s'en débarrasser."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:SpaceMarines#0" value="La lie renégate de la plus grande création de notre Empereur. Nous devons les éliminer de la galaxie."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyUnit:Neutral/Enslaver#0" value="Des horreurs Xenos, elles sont dans ma tête !"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Hurt#0" value="Ce n'est qu'une égratignure."/>
	<entry name="AstraMilitarum/LordCommissar:HurtCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#0" value="Administrer la justice est si épuisant."/>
	<entry name="AstraMilitarum/LordCommissar:Idle#1" value="Les soldats oisifs ne travaillent pas pour l'Empereur."/>
	<entry name="AstraMilitarum/LordCommissar:Idle#2" value="Vous avez vu des fantômes ? Vous croyez que je suis Gaunt ou quoi ?"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#3" value="Qu'est-ce que Cain ferait ?"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#4" value="Adjudant ! Nettoyez ça encore une fois !"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#5" value="Faites votre devoir au nom de- Oh, j'oublie toujours cette partie."/>
	<entry name="AstraMilitarum/LordCommissar:IdleCount" value="6"/>
	<entry name="AstraMilitarum/LordCommissar:Shaken#0" value="A spiritu dominatus, Domine, libra nos."/>
	<entry name="AstraMilitarum/LordCommissar:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Victory#0" value="Je montre l'exemple à mes soldats"/>
	<entry name="AstraMilitarum/LordCommissar:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:WireWeed#0" value="Il serait suicidaire de rester dans ce bio-barbelé, peu importe à quel point vous trouvez ça brave."/>
	<entry name="AstraMilitarum/LordCommissar:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedFaction:SpaceMarines#0" value="Il y a quelques Astartes en bas. Amicaux."/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedUnit:AstraMilitarum/Thunderbolt#0" value="C'est chouette d'avoir des chasseurs en support."/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedUnit:AstraMilitarum/ThunderboltCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Artefact#0" value="Cible importante en vue."/>
	<entry name="AstraMilitarum/MarauderBomber:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#0" value="Bombes larguées."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#1" value="Si vous pouvez les voir, on peut les bombarder."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#2" value="Ouverture de la soute à bombes."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#3" value="Tout ce qui est sous le viseur de bombardement est une cible valide."/>
	<entry name="AstraMilitarum/MarauderBomber:AttackCount" value="4"/>
	<entry name="AstraMilitarum/MarauderBomber:Broken#0" value="On s'écrase !"/>
	<entry name="AstraMilitarum/MarauderBomber:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Cover#0" value="Si on volerait plus bas, eh ben on aurait besoin d'un klaxon sur c'tengin."/>
	<entry name="AstraMilitarum/MarauderBomber:CoverCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Hurt#0" value="On a trois moteurs hors services, on a plus de trous que le grox d'un marchand, le transmetteur vox est mort, et notre réservoir fuit…"/>
	<entry name="AstraMilitarum/MarauderBomber:HurtCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#0" value="J'suis pas très doux pour faire des discours."/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#1" value="Je te jure, Carpenter, ces bombes me répondent."/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#2" value="Est-ce quelqu'un sait comment atterrir ?"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#3" value="J'pratique mes mains d’œuvres. Comme l'Manuel le dit."/>
	<entry name="AstraMilitarum/MarauderBomber:IdleCount" value="4"/>
	<entry name="AstraMilitarum/MarauderBomber:Shaken#0" value="On monte en altitude-y a de l'anti-aérien qui nous canarde."/>
	<entry name="AstraMilitarum/MarauderBomber:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Victory#0" value="Où est la cible potentielle la plus proche ?"/>
	<entry name="AstraMilitarum/MarauderBomber:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedFaction:SpaceMarines#0" value="Je sens… Un grand bien en eux."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Un esprit tordu jusqu'à devenir absolutiste… fascinant."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Artefact#0" value="Les Anciens ont construit ces petits jouets."/>
	<entry name="AstraMilitarum/PrimarisPsyker:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#0" value="Vos esprits vont brûler…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#1" value="Je sens les yeux de l'Empereur sur moi."/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#2" value="Je vous brûle en Son nom."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AttackCount" value="3"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Broken#0" value="Sa lumière, où est Sa lumière ?"/>
	<entry name="AstraMilitarum/PrimarisPsyker:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Cover#0" value="L'esprit caché est un esprit sage."/>
	<entry name="AstraMilitarum/PrimarisPsyker:CoverCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:AstraMilitarum#0" value="Nos avis divergent, il semblerait."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:Necrons#0" value="Je ne sens rien en eux. Rien."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:Orks#0" value="Une vague de-, quel violent sentiment d'adoration."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:SpaceMarines#0" value="Quelle confiance de fer… en surface."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyUnit:Neutral/Enslaver#0" value="Je sens sa faim, son désire pour la fécondité de notre esprit."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Hurt#0" value="Ce n'est que de la chair."/>
	<entry name="AstraMilitarum/PrimarisPsyker:HurtCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#0" value="Même à travers ces tempêtes, j'entraperçois encore Sa lumière."/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#1" value="Je sais à quoi tu penses…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#2" value="Un travail lent pour un esprit oisif."/>
	<entry name="AstraMilitarum/PrimarisPsyker:IdleCount" value="3"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Shaken#0" value="Il est préférable de mourir ici que dans les Vaisseaux Noirs."/>
	<entry name="AstraMilitarum/PrimarisPsyker:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Victory#0" value="Comme l'avait prédit le Tarot de l'Empereur."/>
	<entry name="AstraMilitarum/PrimarisPsyker:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:WireWeed#0" value="Il y a de la vie, aussi étrange et sanguinaire qu'elle soit."/>
	<entry name="AstraMilitarum/PrimarisPsyker:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedFaction:SpaceMarines#0" value="Des grosses épaulettes, ce sont les Marines !"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Est-ce que je peux retourner au front s'il vous plaît ?"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Artefact#0" value="Woah, vous devriez voir ça !"/>
	<entry name="AstraMilitarum/ScoutSentinel:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Attack#0" value="Je balance tout ce que j'ai !"/>
	<entry name="AstraMilitarum/ScoutSentinel:AttackCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Broken#0" value="Je m'enfuie avec les longues jambes de la Sentinelle !"/>
	<entry name="AstraMilitarum/ScoutSentinel:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Cover#0" value="Je me cache derrière un arbre ou un truc du genre."/>
	<entry name="AstraMilitarum/ScoutSentinel:CoverCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:AstraMilitarum#0" value="Woh, c'est nous mais méchaaaaaant."/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:Necrons#0" value="Hommes de métal, droit devant !"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:Orks#0" value="Je vois des yeux rouges qui me scrutent pour voir s'ils peuvent me transformer en ferraille !"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:SpaceMarines#0" value="Hein ? On se bat contre eux ? On est les méchants du coup ?"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyUnit:Neutral/Enslaver#0" value="Euh… Qu'est-ce que c'est ?"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Hurt#0" value="Oh, quelqu'un m'a découpé une nouvelle lucarne."/>
	<entry name="AstraMilitarum/ScoutSentinel:HurtCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#0" value="Est-ce que je devrais pas, vous savez, faire quelque chose ?"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#1" value="Ces jambes montent jusqu'en haut."/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#2" value="Imaginez ce qu'il y a là-bas, attendant d'être découvert !"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#3" value="Allons-y !"/>
	<entry name="AstraMilitarum/ScoutSentinel:IdleCount" value="4"/>
	<entry name="AstraMilitarum/ScoutSentinel:Shaken#0" value="Sortez-moi de là !"/>
	<entry name="AstraMilitarum/ScoutSentinel:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Victory#0" value="Je savais depuis le début qu'on allait gagner. Suivant !"/>
	<entry name="AstraMilitarum/ScoutSentinel:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:WireWeed#0" value="Bougez-moi de là rapidement, ce truc est en train de grimper le long des jambes."/>
	<entry name="AstraMilitarum/ScoutSentinel:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:AlliedFaction:SpaceMarines#0" value="S'ils n'ont pas de Land Raiders, je m'en vais."/>
	<entry name="AstraMilitarum/TankCommander:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:AlliedUnit:AstraMilitarum/Baneblade#0" value="Ohhhh… Quelle beauté."/>
	<entry name="AstraMilitarum/TankCommander:AlliedUnit:AstraMilitarum/BanebladeCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Artefact#0" value="Si ce n'est pas un blindé ennemi, ça ne m'intéresse pas."/>
	<entry name="AstraMilitarum/TankCommander:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Attack#0" value="Boum ! Hahaha !"/>
	<entry name="AstraMilitarum/TankCommander:Attack#1" value="Conduisez plus près de l'ennemi ! Vous savez pourquoi…"/>
	<entry name="AstraMilitarum/TankCommander:Attack#2" value="Tirez avec le canon ! Et avec l'autre ! Tirez avec tout !"/>
	<entry name="AstraMilitarum/TankCommander:Attack#3" value="Feu ! Hahaha !"/>
	<entry name="AstraMilitarum/TankCommander:AttackCount" value="4"/>
	<entry name="AstraMilitarum/TankCommander:Broken#0" value="Non, n'allez pas en arrière ! Qui commande ici ?"/>
	<entry name="AstraMilitarum/TankCommander:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Cover#0" value="Une embuscade, j'aime ça."/>
	<entry name="AstraMilitarum/TankCommander:CoverCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:AstraMilitarum#0" value="Des traîtres ! Hérétiques… Ont-ils des chars ?"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:Necrons#0" value="Des Monoliths, ça c'est un défi."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:Orks#0" value="Eh, on aurait dû amener le Demolisher."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:SpaceMarines#0" value="Des traîtres ! Ils ne savent probablement pas différencier l'avant de l'arrière d'un Rhino."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyUnit:Neutral/KastelanRobot#0" value="Oh, ils ne font plus des blindés comme ça."/>
	<entry name="AstraMilitarum/TankCommander:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Hurt#0" value="On les tient occupés en les laissant nous tirer des trous dedans."/>
	<entry name="AstraMilitarum/TankCommander:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Idle#0" value="Mieux vaut être un fondu de blindés que fondu par un tir ennemi. Ha !"/>
	<entry name="AstraMilitarum/TankCommander:Idle#1" value="Je polis juste mes médailles."/>
	<entry name="AstraMilitarum/TankCommander:Idle#2" value="Comment ça, rentrer à l'intérieur ? J'ai besoin de voir."/>
	<entry name="AstraMilitarum/TankCommander:Idle#3" value="Je suis impatient qu'il y ait de l'action."/>
	<entry name="AstraMilitarum/TankCommander:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TankCommander:Shaken#0" value="Je suis pas sûr que la vieille cannette puisse encaisser beaucoup d'autres comme ça."/>
	<entry name="AstraMilitarum/TankCommander:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Victory#0" value="Un bon ennemi est un ennemi sous vos chenilles."/>
	<entry name="AstraMilitarum/TankCommander:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:WireWeed#0" value="Hey, ce barbelé est en train de rayer la peinture !"/>
	<entry name="AstraMilitarum/TankCommander:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Heureux d'être en-dehors de la juridiction de ce fanatique."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Artefact#0" value="Démantèlement tentant."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Attack#0" value="En combat."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AttackCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Broken#0" value="La chair est faible. Doit battre en retraite."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Cover#0" value="Probabilité qu'un tir ennemi touche réduite drastiquement."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:CoverCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:AstraMilitarum#0" value="Des contrariétés mineures."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:Necrons#0" value="L'apparence de vrais dévots du Dieu-Machine, avec l’âme d'ignobles Xenos."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:Orks#0" value="Des primitifs. Gardez-les loin de nos machines sacrées."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:SpaceMarines#0" value="Quel dommage."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/KastelanRobot#0" value="Une relique sacrée ! Minimisez les dommages qu'elle subit."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/TechpriestBetrayer#0" value="Un heretek, ici ? Peu probable."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/TechpriestBetrayerCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Hurt#0" value="Réparations requises."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#0" value="Application des onguents sacrés en cours."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#1" value="Vérification des cogitateurs en cours."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#2" value="Rites de la machine en cours."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#3" value="Revitalisation des esprits en cours."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Shaken#0" value="L'Omnimessie est avec moi."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Victory#0" value="Prédictible."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:WireWeed#0" value="Le bio-barbelé est tellement plaisant. Nous devons le reproduire."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion" value="AstraMilitarum/Guardsman"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedFaction:SpaceMarines#0" value="Il y a quelque chose de brillant là-bas !"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedUnit:AstraMilitarum/MarauderBomber#0" value="Nous autres les gars de l'air, on doit se serrer les coudes."/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedUnit:AstraMilitarum/MarauderBomberCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Artefact#0" value="Est-ce quelqu'un a vu ce que c'était ? Wow."/>
	<entry name="AstraMilitarum/Thunderbolt:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Attack#0" value="Courtes rafales uniquement ! Conservez les munitions !"/>
	<entry name="AstraMilitarum/Thunderbolt:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Broken#0" value="Retour au bercail."/>
	<entry name="AstraMilitarum/Thunderbolt:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Cover#0" value="Juste le couvert des nuages."/>
	<entry name="AstraMilitarum/Thunderbolt:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:AstraMilitarum#0" value="Des gardes traîtres. Un environnement riche en cibles."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:Orks#0" value="Des orks. Je serais content que s'ils doivent atterrir trop vite pour leur bien, avec leur tête en feu."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:SpaceMarines#0" value="Des marines. Ils passent plus de temps à décorer leurs armures qu'à se battre."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyUnit:Neutral/Psychneuein#0" value="Quelqu'un a envie d'effleurer une grosse abeille ?"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Hurt#0" value="Hnn, pas de points pour le deuxième arrivé."/>
	<entry name="AstraMilitarum/Thunderbolt:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#0" value="Je ressens le désir du tir."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#1" value="Pas le temps de réfléchir là-haut."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#2" value="Cette planète est une promenade de santé."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#3" value="Je te prends comme coéquipier quand tu voudras."/>
	<entry name="AstraMilitarum/Thunderbolt:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Thunderbolt:Shaken#0" value="On va les engager en combat tournoyant !"/>
	<entry name="AstraMilitarum/Thunderbolt:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Victory#0" value="Gagner demande bien plus que voler de manière compétente."/>
	<entry name="AstraMilitarum/Thunderbolt:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Artefact#0" value="Non, sérieusement, je peux pas le ramasser."/>
	<entry name="AstraMilitarum/Valkyrie:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Attack#0" value="On les extraie sous les tirs ennemis."/>
	<entry name="AstraMilitarum/Valkyrie:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Broken#0" value="On n'est pas censés rester ici longtemps !"/>
	<entry name="AstraMilitarum/Valkyrie:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:AstraMilitarum#0" value="Y a des troufions en face."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:Necrons#0" value="Y a des hommes de métal qui viennent rapidement vers nous."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:Orks#0" value="Des Peaux-Vertes dans la ZA."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:SpaceMarines#0" value="Je me suis pas enrôlé pour affronter des Marines !"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Hurt#0" value="C'est chaud ici, je répète c'est chaud !"/>
	<entry name="AstraMilitarum/Valkyrie:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#0" value="Est-ce qu'on peut réquisitionner des haut-parleurs ?"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#1" value="Bon sang, où est-ce qu'il y a de l'action ?"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#2" value="C'est le silence qui vous mine."/>
	<entry name="AstraMilitarum/Valkyrie:Idle#3" value="On continue jusqu'à ce qu'on s'écrase."/>
	<entry name="AstraMilitarum/Valkyrie:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Valkyrie:Shaken#0" value="On n'est pas censés être en première ligne ! Sortez-nous de là !"/>
	<entry name="AstraMilitarum/Valkyrie:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Victory#0" value="Ennemi éliminé, on extrait s'il vous plaît."/>
	<entry name="AstraMilitarum/Valkyrie:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedFaction:SpaceMarines#0" value="Nous nous sentons… rassurés."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedUnit:AstraMilitarum/PrimarisPsyker#0" value="Notre frère des Vaisseaux Noirs…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedUnit:AstraMilitarum/PrimarisPsykerCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Artefact#0" value="Le cœur de l'impiété."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#0" value="Accelerando maintenant, ensemble nous les déchiquetons."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#1" value="Une codetta pour leurs tristes vies."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#2" value="Nous dansons, unis."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#3" value="Une simple Tempête d'Âmes pour les carboniser."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AttackCount" value="4"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Broken#0" value="Nous sommes divisés, nous sommes perdus."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Cover#0" value="Protégés des yeux ennemis, pas de leur esprit."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:CoverCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:AstraMilitarum#0" value="Nos frères et amis, égarés dans le péché."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:Necrons#0" value="Rien, il n'y a rien ici."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:Orks#0" value="Une autre volonté collective… Non-humaine, cependant."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:SpaceMarines#0" value="Les fils dévoyés de l'Empereur lui-même…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyUnit:Neutral/Psychneuein#0" value="Des avatars de la fécondité, prêts à faire naître de nouvelles horreurs."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Hurt#0" value="Nous entrons en dissonance, nous nous séparons."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:HurtCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#0" value="Just au-delà de notre champ de vision, il est là. Le Warp."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#1" value="Si loin de Sa lumière."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#2" value="Nous avons survécu aux Vaisseaux Noirs, nous pouvons survivre à ça."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#3" value="La planète murmure à nos oreilles."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:IdleCount" value="4"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Shaken#0" value="Notre harmonie s'effondre, notre requiem approche."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Victory#0" value="Quel chant funèbre pour ceux qui meurent comme du bétail ?"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:WireWeed#0" value="Des serpents argentés nous étreignent, nous piquent."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#0" value="Feu oppressant !"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Attack#1" value="Au nom du Primarque !"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Attack#2" value="Assaut de blindés !"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Attack#3" value="On tire sur tout !"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:AttackCount" value="4"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Broken#0" value="Il faut préserver le tank…"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:BrokenCount" value="1"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Hurt#0" value="Droit dans la coque"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:HurtCount" value="1"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Idle#0" value="Que ferait Dorn ?"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Idle#1" value="On sert la volonté de l'Empereur."/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Idle#2" value="Armageddon, c'était une bataille de chars…"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Idle#3" value="Moins d'infanterie, plus de blindés."/>
    <entry name="AstraMilitarum/RogalDornBattleTank:IdleCount" value="4"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Shaken#0" value="Ils ont tout à fait atteint One."/>
    <entry name="AstraMilitarum/RogalDornBattleTank:ShakenCount" value="1"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Victory#0" value="One est toujours à la recherche de nouvelles cibles."/>
    <entry name="AstraMilitarum/RogalDornBattleTank:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekReanimator" value="Necrons/CanoptekScarab"/>
	<entry name="Necrons/CanoptekScarab:Attack#0" value="Protocol de combat."/>
	<entry name="Necrons/CanoptekScarab:AttackCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Broken#0" value="Contrôle des dégâts."/>
	<entry name="Necrons/CanoptekScarab:BrokenCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Hurt#0" value="Dégâts subis."/>
	<entry name="Necrons/CanoptekScarab:HurtCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Idle#0" value="Protocole de maintenance."/>
	<entry name="Necrons/CanoptekScarab:Idle#1" value="Réduction de la consommation énergétique."/>
	<entry name="Necrons/CanoptekScarab:Idle#2" value="Réparation des unités endommagées."/>
	<entry name="Necrons/CanoptekScarab:Idle#3" value="Nous avons attendu des éons. Nous attendrons… encore."/>
	<entry name="Necrons/CanoptekScarab:IdleCount" value="4"/>
	<entry name="Necrons/CanoptekScarab:Shaken#0" value="Fuite conseillée."/>
	<entry name="Necrons/CanoptekScarab:ShakenCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Victory#0" value="Envahisseur repoussé."/>
	<entry name="Necrons/CanoptekScarab:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekSpyder" value="Necrons/CanoptekScarab"/>
	<entry name="Necrons/Cryptek:Attack#0" value="Meurs, bétail."/>
	<entry name="Necrons/Cryptek:Attack#1" value="Les lois de la Physique elles-mêmes vous trahissent."/>
	<entry name="Necrons/Cryptek:Attack#2" value="Vous ne comprendrez jamais ce qui vous tue."/>
	<entry name="Necrons/Cryptek:AttackCount" value="3"/>
	<entry name="Necrons/Cryptek:Broken#0" value="Des éons d'apprentissage, et j'ai enfin appris la peur."/>
	<entry name="Necrons/Cryptek:BrokenCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:AstraMilitarum#0" value="Des races ses esclaves de nos ennemis les Anciens."/>
	<entry name="Necrons/Cryptek:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:Necrons#0" value="Une autre cour ? Peut-être ont-ils besoin de mes services…"/>
	<entry name="Necrons/Cryptek:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:Orks#0" value="La chair transformée en arme. Admirable."/>
	<entry name="Necrons/Cryptek:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:SpaceMarines#0" value="Des esclaves auto-améliorés. Leur dissection pourrait nous apprendre beaucoup."/>
	<entry name="Necrons/Cryptek:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Cryptek:Hurt#0" value="Fascinant, ils ont réussi à me blesser."/>
	<entry name="Necrons/Cryptek:HurtCount" value="1"/>
	<entry name="Necrons/Cryptek:Idle#0" value="Dissection ou vivisection ? Ah, l'éternelle question."/>
	<entry name="Necrons/Cryptek:Idle#1" value="Vous souvenez-vous des rêves ? Moi oui."/>
	<entry name="Necrons/Cryptek:Idle#2" value="Je vais fissionner des atomes, jusqu'à ce qu'on ait besoin de moi."/>
	<entry name="Necrons/Cryptek:Idle#3" value="J'ai tant à apprendre, et pourtant je reste là."/>
	<entry name="Necrons/Cryptek:IdleCount" value="4"/>
	<entry name="Necrons/Cryptek:Shaken#0" value="Peut-être que les créations des Anciens valent quelque chose."/>
	<entry name="Necrons/Cryptek:ShakenCount" value="1"/>
	<entry name="Necrons/Cryptek:Victory#0" value="Ainsi finit toute vie qui nous défie."/>
	<entry name="Necrons/Cryptek:VictoryCount" value="1"/>
	<entry name="Necrons/DestroyerLord" value="Necrons/Lord"/>
	<entry name="Necrons/DoomScythe:Attack#0" value="Piqué sur le bétail."/>
	<entry name="Necrons/DoomScythe:AttackCount" value="1"/>
	<entry name="Necrons/DoomScythe:Broken#0" value="Nous devons partir."/>
	<entry name="Necrons/DoomScythe:BrokenCount" value="1"/>
	<entry name="Necrons/DoomScythe:Hurt#0" value="Ils… osent ?"/>
	<entry name="Necrons/DoomScythe:HurtCount" value="1"/>
	<entry name="Necrons/DoomScythe:Idle#0" value="On tourne."/>
	<entry name="Necrons/DoomScythe:Idle#1" value="Manœuvres."/>
	<entry name="Necrons/DoomScythe:Idle#2" value="On regarde l'horizon."/>
	<entry name="Necrons/DoomScythe:Idle#3" value="Concentré."/>
	<entry name="Necrons/DoomScythe:IdleCount" value="4"/>
	<entry name="Necrons/DoomScythe:Shaken#0" value="Endommagé ?"/>
	<entry name="Necrons/DoomScythe:ShakenCount" value="1"/>
	<entry name="Necrons/DoomScythe:Victory#0" value="On humilie nos ennemis."/>
	<entry name="Necrons/DoomScythe:VictoryCount" value="1"/>
	<entry name="Necrons/DoomsdayArk" value="Necrons/AnnihilationBarge"/>
	<entry name="Necrons/Headquarters" value="Necrons/Monolith"/>
	<entry name="Necrons/HeavyDestroyer:Attack#0" value="La vie est notre ennemie !"/>
	<entry name="Necrons/HeavyDestroyer:AttackCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Broken#0" value="La mort est notre victoire !"/>
	<entry name="Necrons/HeavyDestroyer:BrokenCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:AstraMilitarum#0" value="Écrasez la faible chair !"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:Necrons#0" value="Notre propre race, un ennemi valeureux !"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:Orks#0" value="Purgez la galaxie de la vermine fongique !"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:SpaceMarines#0" value="Pions du Dieu-Empereur !"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Hurt#0" value="La douleur nous encourage !"/>
	<entry name="Necrons/HeavyDestroyer:HurtCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Idle#0" value="Nous sommes nihilistes !"/>
	<entry name="Necrons/HeavyDestroyer:Idle#1" value="Nous avons faim !"/>
	<entry name="Necrons/HeavyDestroyer:Idle#2" value="Nous devons nous améliorer, pour détruire !"/>
	<entry name="Necrons/HeavyDestroyer:Idle#3" value="Pourquoi ne bougeons-nous pas ? ? Pourquoi ne tuons-nous pas ? !"/>
	<entry name="Necrons/HeavyDestroyer:IdleCount" value="4"/>
	<entry name="Necrons/HeavyDestroyer:Shaken#0" value="Pourquoi ne meurent-ils pas plus vite ?"/>
	<entry name="Necrons/HeavyDestroyer:ShakenCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Victory#0" value="Ils meurent ! Ce n'est pas assez !"/>
	<entry name="Necrons/HeavyDestroyer:VictoryCount" value="1"/>
	<entry name="Necrons/Immortal" value="Necrons/Warrior"/>
	<entry name="Necrons/Lord:Attack#0" value="Ressentez la force immortelle des Nécrons !"/>
	<entry name="Necrons/Lord:Attack#1" value="Mourez fièrement de mes mains."/>
	<entry name="Necrons/Lord:Attack#2" value="Qui ose me défier ?"/>
	<entry name="Necrons/Lord:Attack#3" value="Vous apprendrez l'obéissance !"/>
	<entry name="Necrons/Lord:AttackCount" value="4"/>
	<entry name="Necrons/Lord:Broken#0" value="Où sont mes Immortels ? Mes gardes ?"/>
	<entry name="Necrons/Lord:BrokenCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:AstraMilitarum#0" value="Des éphémères, destinés à ne vivre qu'un jour."/>
	<entry name="Necrons/Lord:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:Necrons#0" value="Quelle immoralité ! Des rivaux, sur mon Monde Primordial ? !"/>
	<entry name="Necrons/Lord:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:Orks#0" value="Quelles brutes ! Et pourtant ils règnent sur ma galaxie."/>
	<entry name="Necrons/Lord:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:SpaceMarines#0" value="Si sages, si jeunes. Ils ne vivront pas longtemps."/>
	<entry name="Necrons/Lord:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Lord:Hurt#0" value="Ils ont abîmé mon voile ? Détruisez-les !"/>
	<entry name="Necrons/Lord:HurtCount" value="1"/>
	<entry name="Necrons/Lord:Idle#0" value="La conscience est un simple mot, conçu pour restreindra les puissants."/>
	<entry name="Necrons/Lord:Idle#1" value="Les prophètes, je les tuerai en dernier."/>
	<entry name="Necrons/Lord:Idle#2" value="Que ferais-je en premier, quand nous dirigerons ce monde ?"/>
	<entry name="Necrons/Lord:IdleCount" value="3"/>
	<entry name="Necrons/Lord:Shaken#0" value="La mort n'a pas d'emprise sur moi."/>
	<entry name="Necrons/Lord:Shaken#1" value="Les Anciens les ont bien entraînés."/>
	<entry name="Necrons/Lord:ShakenCount" value="2"/>
	<entry name="Necrons/Lord:Victory#0" value="Désespère et meurs."/>
	<entry name="Necrons/Lord:VictoryCount" value="1"/>
	<entry name="Necrons/Monolith:Attack#0" value="Éruption de particules déployée."/>
	<entry name="Necrons/Monolith:AttackCount" value="1"/>
	<entry name="Necrons/Monolith:Broken#0" value="Repli en cours… lentement."/>
	<entry name="Necrons/Monolith:BrokenCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:AstraMilitarum#0" value="Humains en vue."/>
	<entry name="Necrons/Monolith:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:Necrons#0" value="Cour ennemie sur la planète."/>
	<entry name="Necrons/Monolith:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:Orks#0" value="Orks repérés."/>
	<entry name="Necrons/Monolith:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:SpaceMarines#0" value="Humains améliorés repérés."/>
	<entry name="Necrons/Monolith:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Monolith:Hurt#0" value="Blindage percé, équipage endommagé."/>
	<entry name="Necrons/Monolith:HurtCount" value="1"/>
	<entry name="Necrons/Monolith:Idle#0" value="Charge de la Porte d'Eternité en cours."/>
	<entry name="Necrons/Monolith:Idle#1" value="Génération de secrets impénétrables."/>
	<entry name="Necrons/Monolith:Idle#2" value="Décomposition en valeurs singulières."/>
	<entry name="Necrons/Monolith:Idle#3" value="Reconstruction de particules fondamentales."/>
	<entry name="Necrons/Monolith:Idle#4" value="Vidage des banques de mémoires de l'Arche."/>
	<entry name="Necrons/Monolith:Idle#5" value="Réticulation des splines."/>
	<entry name="Necrons/Monolith:IdleCount" value="6"/>
	<entry name="Necrons/Monolith:Shaken#0" value="Nous sommes attaqués. L'intégrité de la structure est compromise."/>
	<entry name="Necrons/Monolith:ShakenCount" value="1"/>
	<entry name="Necrons/Monolith:Victory#0" value="Ennemi éliminé. Comme prévu."/>
	<entry name="Necrons/Monolith:VictoryCount" value="1"/>
	<entry name="Necrons/NightScythe" value="Necrons/DoomScythe"/>
	<entry name="Necrons/Obelisk" value="Necrons/Monolith"/>
	<entry name="Necrons/TesseractVault:Attack#0" value="Ressens la colère d'un Dieu des Étoiles !"/>
	<entry name="Necrons/TesseractVault:AttackCount" value="1"/>
	<entry name="Necrons/TesseractVault:Broken#0" value="Je suis presque libre !"/>
	<entry name="Necrons/TesseractVault:BrokenCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:Necrons#0" value="M'utiliser contre les Nécrons ? Quelles stupides créatures."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:Orks#0" value="Des bêtes méprisables."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:SpaceMarines#0" value="Leurs vies m'appartiennent."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/TesseractVault:Hurt#0" value="Ma prison s'affaiblit."/>
	<entry name="Necrons/TesseractVault:HurtCount" value="1"/>
	<entry name="Necrons/TesseractVault:Idle#0" value="Sentinelles, scarabées, sangsues… tout ça pour moi ?"/>
	<entry name="Necrons/TesseractVault:Idle#1" value="Oh, vous me libérez ?"/>
	<entry name="Necrons/TesseractVault:Idle#2" value="Cette prison ne PEUT PAS ME RETENIR."/>
	<entry name="Necrons/TesseractVault:Idle#3" value="Je suis si… affaibli. J'étais un dieu stellaire !"/>
	<entry name="Necrons/TesseractVault:Idle#4" value="Je suis un dieu stellaire. Laissez-moi vous éblouir."/>
	<entry name="Necrons/TesseractVault:IdleCount" value="5"/>
	<entry name="Necrons/TesseractVault:Shaken#0" value="Oui, détruisez ma prison…"/>
	<entry name="Necrons/TesseractVault:ShakenCount" value="1"/>
	<entry name="Necrons/TesseractVault:Victory#0" value="Ce n'est pas suffisant. Ce ne sera pas assez tant que toute vie ne sera pas mienne."/>
	<entry name="Necrons/TesseractVault:VictoryCount" value="1"/>
	<entry name="Necrons/TombBlade:Attack#0" value="Attaque rapide."/>
	<entry name="Necrons/TombBlade:AttackCount" value="1"/>
	<entry name="Necrons/TombBlade:Broken#0" value="Dispersés. Retraite."/>
	<entry name="Necrons/TombBlade:BrokenCount" value="1"/>
	<entry name="Necrons/TombBlade:Hurt#0" value="Tirs de faible intensité, guerriers abattus."/>
	<entry name="Necrons/TombBlade:HurtCount" value="1"/>
	<entry name="Necrons/TombBlade:Idle#0" value="Jamais immobile."/>
	<entry name="Necrons/TombBlade:Idle#1" value="Programmation des schémas d'attaque."/>
	<entry name="Necrons/TombBlade:Idle#2" value="L'espace… Nous nous souvenons."/>
	<entry name="Necrons/TombBlade:Idle#3" value="Test des répulseurs dimensionnels."/>
	<entry name="Necrons/TombBlade:IdleCount" value="4"/>
	<entry name="Necrons/TombBlade:Shaken#0" value="Touchés. C'est improbable, mais nous avons été touchés."/>
	<entry name="Necrons/TombBlade:ShakenCount" value="1"/>
	<entry name="Necrons/TombBlade:Victory#0" value="Ennemi… désintégré."/>
	<entry name="Necrons/TombBlade:VictoryCount" value="1"/>
	<entry name="Necrons/TranscendentCtan" value="Necrons/TesseractVault"/>
	<entry name="Necrons/TriarchPraetorian:Attack#0" value="La première loi est… Ne vous dressez pas contre le Triarcat."/>
	<entry name="Necrons/TriarchPraetorian:AttackCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Broken#0" value="La dernière loi est… partez pour vous battre un autre jour."/>
	<entry name="Necrons/TriarchPraetorian:BrokenCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:AstraMilitarum#0" value="Ah, les primitifs. Nous étions leurs dieux à une époque."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:Necrons#0" value="Une autre cour ? Nous devons les ramener dans les rangs."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:Orks#0" value="Indisciplinables, des sauvages."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:SpaceMarines#0" value="D'honorables ennemis. Nous devons les tuer respectueusement."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Hurt#0" value="La loi c'est nous et l'ordre !"/>
	<entry name="Necrons/TriarchPraetorian:HurtCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Idle#0" value="Connaissez-vous les codes de la loi ?"/>
	<entry name="Necrons/TriarchPraetorian:Idle#1" value="Le premier code est celui de l'honneur."/>
	<entry name="Necrons/TriarchPraetorian:Idle#2" value="Le second code est celui de la fierté."/>
	<entry name="Necrons/TriarchPraetorian:Idle#3" value="Le troisième code… Dois-je partager cette information ?"/>
	<entry name="Necrons/TriarchPraetorian:Idle#4" value="Le cinquante-septième code… Obéissez tous les codes."/>
	<entry name="Necrons/TriarchPraetorian:IdleCount" value="5"/>
	<entry name="Necrons/TriarchPraetorian:Shaken#0" value="La résurrection cause tant de pertes."/>
	<entry name="Necrons/TriarchPraetorian:ShakenCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Victory#0" value="Nous ne prenons aucun plaisir à ça."/>
	<entry name="Necrons/TriarchPraetorian:VictoryCount" value="1"/>
	<entry name="Necrons/TriarchStalker" value="Necrons/TriarchPraetorian"/>
	<entry name="Necrons/Warrior:Artefact#0" value="Des horreurs des Anciens-Sur notre Monde Primordial ? Comment osent-ils ?"/>
	<entry name="Necrons/Warrior:ArtefactCount" value="1"/>
	<entry name="Necrons/Warrior:Attack#0" value="Est-ce que ça fait mal, d'avoir vos nerfs épluchés, atome par atome ?"/>
	<entry name="Necrons/Warrior:Attack#1" value="On engage l'ennemi."/>
	<entry name="Necrons/Warrior:Attack#2" value="L'histoire les oubliera."/>
	<entry name="Necrons/Warrior:Attack#3" value="Leur résistance est… ennuyante."/>
	<entry name="Necrons/Warrior:AttackCount" value="4"/>
	<entry name="Necrons/Warrior:Broken#0" value="Les simulations de dégâts tournent à 99%."/>
	<entry name="Necrons/Warrior:Broken#1" value="Notre unité est dispersée. Nous nous replions."/>
	<entry name="Necrons/Warrior:Broken#2" value="Protocole de retour initié."/>
	<entry name="Necrons/Warrior:Broken#3" value="La tombe appelle et nous obéissons."/>
	<entry name="Necrons/Warrior:Broken#4" value="Vos ordres sont impossibles, mon Seigneur."/>
	<entry name="Necrons/Warrior:BrokenCount" value="5"/>
	<entry name="Necrons/Warrior:Cover#0" value="Utilisation du couvert."/>
	<entry name="Necrons/Warrior:CoverCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:AstraMilitarum#0" value="Des faibles êtres de chair, mais une bonne race d'esclave."/>
	<entry name="Necrons/Warrior:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:Necrons#0" value="Nos cousins, dans l'erreur."/>
	<entry name="Necrons/Warrior:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:Orks#0" value="Une bio-arme sauvage. Brutalement efficace."/>
	<entry name="Necrons/Warrior:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:SpaceMarines#0" value="De la chair, reconstruite à notre image. Ne pas les sous-estimer."/>
	<entry name="Necrons/Warrior:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/CatachanDevil#0" value="Insectoïde organique géant. Peut-être un précuseur Tyranide ?"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/Enslaver#0" value="Un effet secondaire des actions des Anciens ? Les senseurs ne sont qu'à 30% d'efficacité."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KastelanRobot#0" value="Les humains se moquent-ils de nous ? Quelles copies décevantes."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="Les senseurs dysfonctionnent ! Inconnu inconnu inconnu."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/Psychneuein#0" value="Mégafaune. Une menace uniquement pour les races organiques."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="Necrons/Warrior:Hurt#0" value="Les dégâts dépassent not attentes… Protocoles de rappel ?"/>
	<entry name="Necrons/Warrior:HurtCount" value="1"/>
	<entry name="Necrons/Warrior:Idle#0" value="Tous les systèmes sont opérationnels."/>
	<entry name="Necrons/Warrior:Idle#1" value="Pour tout ce temps… Maudit soit le Mystificateur."/>
	<entry name="Necrons/Warrior:Idle#2" value="Protocole de veille initié."/>
	<entry name="Necrons/Warrior:Idle#3" value="Réduction de la consommation énergétique."/>
	<entry name="Necrons/Warrior:Idle#4" value="Ce corps métallique… si étrange."/>
	<entry name="Necrons/Warrior:Idle#5" value="La machine est forte, la chair n'est plus."/>
	<entry name="Necrons/Warrior:Idle#6" value="Nous avons attendu des millénaires…Nous attendrons d'autres millénaires."/>
	<entry name="Necrons/Warrior:IdleCount" value="7"/>
	<entry name="Necrons/Warrior:QuestStory0#0" value="Surveillance de signes de vie des Anciens."/>
	<entry name="Necrons/Warrior:QuestStory0Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory1#0" value="Prétoriens et Crypteks ? Nos forces grandissent."/>
	<entry name="Necrons/Warrior:QuestStory1Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory2#0" value="Nous devrions avoir pitié de ceux qui ont perdu leur esprit à cause de la malédiction du C'tan."/>
	<entry name="Necrons/Warrior:QuestStory2Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory3#0" value="Chasser ces babioles des Anciens ? Bien… mon seigneur."/>
	<entry name="Necrons/Warrior:QuestStory3Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory4#0" value="Où sont les Prétoriens ?"/>
	<entry name="Necrons/Warrior:QuestStory4Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory5#0" value="Mephet'ran ? Le Messager ! Le Mystificateur ! Le voleur d'âmes !"/>
	<entry name="Necrons/Warrior:QuestStory5Count" value="1"/>
	<entry name="Necrons/Warrior:Shaken#0" value="Une race inférieure s'enfuirait"/>
	<entry name="Necrons/Warrior:Shaken#1" value="Les Prétoriens ne nous ont pas briefé là-dessus."/>
	<entry name="Necrons/Warrior:Shaken#2" value="Impossible. Nous… perdons ?"/>
	<entry name="Necrons/Warrior:Shaken#3" value="Nous sommes au-delà de sentiments comme la peur."/>
	<entry name="Necrons/Warrior:ShakenCount" value="4"/>
	<entry name="Necrons/Warrior:Victory#0" value="Pertes acceptables. Leurs pertes."/>
	<entry name="Necrons/Warrior:Victory#1" value="Comme prévu, nous sommes victorieux."/>
	<entry name="Necrons/Warrior:Victory#2" value="Élimine."/>
	<entry name="Necrons/Warrior:Victory#3" value="Pour le Roi Silencieux et pour mon seigneur."/>
	<entry name="Necrons/Warrior:Victory#4" value="Mon Seigneur, l'ennemi s'avère être faible."/>
	<entry name="Necrons/Warrior:Victory#5" value="Les Anciens ont créé de la chair si fragile."/>
	<entry name="Necrons/Warrior:Victory#6" value="Ils connaîtront les Nécrons et la peur."/>
	<entry name="Necrons/Warrior:Victory#7" value="Gagner si facilement est si… insatisfaisant."/>
	<entry name="Necrons/Warrior:VictoryCount" value="8"/>
	<entry name="Neutral/Ambull:Attack#0" value="Roekoe !"/>
	<entry name="Neutral/Ambull:AttackCount" value="1"/>
	<entry name="Neutral/Ambull:Broken#0" value="Kurr."/>
	<entry name="Neutral/Ambull:BrokenCount" value="1"/>
	<entry name="Neutral/Ambull:Hurt#0" value="Rou rou."/>
	<entry name="Neutral/Ambull:HurtCount" value="1"/>
	<entry name="Neutral/Ambull:Idle#0" value="Guru Guru"/>
	<entry name="Neutral/Ambull:Idle#1" value="Grl Grl"/>
	<entry name="Neutral/Ambull:Idle#2" value="Curcuurucu"/>
	<entry name="Neutral/Ambull:Idle#3" value="Oo ho oo ho"/>
	<entry name="Neutral/Ambull:IdleCount" value="4"/>
	<entry name="Neutral/Ambull:Shaken#0" value="Burukk"/>
	<entry name="Neutral/Ambull:ShakenCount" value="1"/>
	<entry name="Neutral/Ambull:Victory#0" value="Uuu"/>
	<entry name="Neutral/Ambull:VictoryCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Attack#0" value="KYKYLIKY…"/>
	<entry name="Neutral/CatachanDevil:AttackCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Broken#0" value="KUKELEKU"/>
	<entry name="Neutral/CatachanDevil:BrokenCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Hurt#0" value="CHICHIRICHI"/>
	<entry name="Neutral/CatachanDevil:HurtCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Idle#0" value="ke-kok-o"/>
	<entry name="Neutral/CatachanDevil:Idle#1" value="kok-ko-o"/>
	<entry name="Neutral/CatachanDevil:Idle#2" value="ko-ko-o"/>
	<entry name="Neutral/CatachanDevil:Idle#3" value="ko-ke-koko"/>
	<entry name="Neutral/CatachanDevil:IdleCount" value="4"/>
	<entry name="Neutral/CatachanDevil:Shaken#0" value="Ch-ch-ch !"/>
	<entry name="Neutral/CatachanDevil:ShakenCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Victory#0" value="Kckeliku !"/>
	<entry name="Neutral/CatachanDevil:VictoryCount" value="1"/>
	<entry name="Neutral/Enslaver:Attack#0" value="Heiiii…"/>
	<entry name="Neutral/Enslaver:AttackCount" value="1"/>
	<entry name="Neutral/Enslaver:Broken#0" value="Oummm"/>
	<entry name="Neutral/Enslaver:BrokenCount" value="1"/>
	<entry name="Neutral/Enslaver:Hurt#0" value="Harrhm"/>
	<entry name="Neutral/Enslaver:HurtCount" value="1"/>
	<entry name="Neutral/Enslaver:Idle#0" value="HmmMmm."/>
	<entry name="Neutral/Enslaver:Idle#1" value="Mmmhmmm."/>
	<entry name="Neutral/Enslaver:Idle#2" value="Tct tct."/>
	<entry name="Neutral/Enslaver:Idle#3" value="Chhrrr."/>
	<entry name="Neutral/Enslaver:IdleCount" value="4"/>
	<entry name="Neutral/Enslaver:Shaken#0" value="Hrhrhrh"/>
	<entry name="Neutral/Enslaver:ShakenCount" value="1"/>
	<entry name="Neutral/Enslaver:Victory#0" value="mmmMMM !"/>
	<entry name="Neutral/Enslaver:VictoryCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Attack#0" value="Cible acquise, Datasmith."/>
	<entry name="Neutral/KastelanRobot:AttackCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Broken#0" value="Ordres incomplets, application du protocole."/>
	<entry name="Neutral/KastelanRobot:BrokenCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Hurt#0" value="Réparations requises, Datasmith. Vos ordres ?"/>
	<entry name="Neutral/KastelanRobot:HurtCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Idle#0" value="Vos ordres ?"/>
	<entry name="Neutral/KastelanRobot:Idle#1" value="Information non-trouvée."/>
	<entry name="Neutral/KastelanRobot:Idle#2" value="Protocoles de veilles dans 5,4,3-"/>
	<entry name="Neutral/KastelanRobot:Idle#3" value="Alerte !"/>
	<entry name="Neutral/KastelanRobot:IdleCount" value="4"/>
	<entry name="Neutral/KastelanRobot:Shaken#0" value="Données inconsistantes détectées !"/>
	<entry name="Neutral/KastelanRobot:ShakenCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Victory#0" value="Cible atomisée. Comme prévu."/>
	<entry name="Neutral/KastelanRobot:VictoryCount" value="1"/>
	<entry name="Neutral/KrootHound:Attack#0" value="Hoeea !"/>
	<entry name="Neutral/KrootHound:Attack#1" value="Hrrrr !"/>
	<entry name="Neutral/KrootHound:AttackCount" value="2"/>
	<entry name="Neutral/KrootHound:Broken#0" value="owoooo"/>
	<entry name="Neutral/KrootHound:BrokenCount" value="1"/>
	<entry name="Neutral/KrootHound:Hurt#0" value="uhuu"/>
	<entry name="Neutral/KrootHound:HurtCount" value="1"/>
	<entry name="Neutral/KrootHound:Idle#0" value="oou"/>
	<entry name="Neutral/KrootHound:Idle#1" value="cha-cha"/>
	<entry name="Neutral/KrootHound:Idle#2" value="ouah ouah"/>
	<entry name="Neutral/KrootHound:Idle#3" value="guf guf"/>
	<entry name="Neutral/KrootHound:IdleCount" value="4"/>
	<entry name="Neutral/KrootHound:Shaken#0" value="yu-yu-yuuu"/>
	<entry name="Neutral/KrootHound:ShakenCount" value="1"/>
	<entry name="Neutral/KrootHound:Victory#0" value="Oooahh !"/>
	<entry name="Neutral/KrootHound:VictoryCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Attack#0" value="DU SANG POUR LE DIEU DU SANG."/>
	<entry name="Neutral/LordOfSkulls:AttackCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Broken#0" value="SEIGNEUR, JE VOUS AI FAILLI."/>
	<entry name="Neutral/LordOfSkulls:BrokenCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Hurt#0" value="MON SANG POUR MON DIEU"/>
	<entry name="Neutral/LordOfSkulls:HurtCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Idle#0" value="DECHIRE DETRUIT MEURS"/>
	<entry name="Neutral/LordOfSkulls:Idle#1" value="TUE TUE TUE"/>
	<entry name="Neutral/LordOfSkulls:Idle#2" value="CRANE ECHINE CRIS"/>
	<entry name="Neutral/LordOfSkulls:Idle#3" value="NOIE-TOI DANS LE SANG"/>
	<entry name="Neutral/LordOfSkulls:Idle#4" value="JE DOIS… TUER"/>
	<entry name="Neutral/LordOfSkulls:IdleCount" value="5"/>
	<entry name="Neutral/LordOfSkulls:Shaken#0" value="JE NE CONNAIS PAS LA PEUR !"/>
	<entry name="Neutral/LordOfSkulls:ShakenCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Victory#0" value="DU SANG ! POUR ! KHORNE !"/>
	<entry name="Neutral/LordOfSkulls:VictoryCount" value="1"/>
	<entry name="Neutral/Psychneuein:Attack#0" value="Zzzz !"/>
	<entry name="Neutral/Psychneuein:AttackCount" value="1"/>
	<entry name="Neutral/Psychneuein:Broken#0" value="Zzzee !"/>
	<entry name="Neutral/Psychneuein:BrokenCount" value="1"/>
	<entry name="Neutral/Psychneuein:Hurt#0" value="Zzzaaa."/>
	<entry name="Neutral/Psychneuein:HurtCount" value="1"/>
	<entry name="Neutral/Psychneuein:Idle#0" value="Zzzm"/>
	<entry name="Neutral/Psychneuein:Idle#1" value="Zzzth. Zzzth."/>
	<entry name="Neutral/Psychneuein:Idle#2" value="Zzzaaa. Zzzm."/>
	<entry name="Neutral/Psychneuein:Idle#3" value="Zzzthzzz."/>
	<entry name="Neutral/Psychneuein:IdleCount" value="4"/>
	<entry name="Neutral/Psychneuein:Shaken#0" value="Zzzm !"/>
	<entry name="Neutral/Psychneuein:ShakenCount" value="1"/>
	<entry name="Neutral/Psychneuein:Victory#0" value="ZZZZ !"/>
	<entry name="Neutral/Psychneuein:VictoryCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Attack#0" value="Meurs pour l'Omnimessie !"/>
	<entry name="Neutral/TechpriestBetrayer:AttackCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Broken#0" value="Probabilité d'échec, 100%."/>
	<entry name="Neutral/TechpriestBetrayer:BrokenCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Hurt#0" value="Fonctions détériorées."/>
	<entry name="Neutral/TechpriestBetrayer:HurtCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#0" value="Test…"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#1" value="Extinction… Allumage."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#2" value="Onguents appliqués."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#3" value="Du serf au Servo-crâne… Complet."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#4" value="Artefact acquis…"/>
	<entry name="Neutral/TechpriestBetrayer:IdleCount" value="5"/>
	<entry name="Neutral/TechpriestBetrayer:Shaken#0" value="Je dois survivre !"/>
	<entry name="Neutral/TechpriestBetrayer:Shaken#1" value="Mars doit être mise au courant."/>
	<entry name="Neutral/TechpriestBetrayer:ShakenCount" value="2"/>
	<entry name="Neutral/TechpriestBetrayer:Victory#0" value="La fuite est la seule victoire."/>
	<entry name="Neutral/TechpriestBetrayer:VictoryCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Attack#0" value="Pour le culte !"/>
	<entry name="Neutral/NeophyteHybrid:Attack#1" value="Le Magus l'ordonne."/>
	<entry name="Neutral/NeophyteHybrid:Attack#2" value="Le Dévoreur arrive."/>
	<entry name="Neutral/NeophyteHybrid:AttackCount" value="3"/>
	<entry name="Neutral/NeophyteHybrid:Broken#0" value="Où est le Primus."/>
	<entry name="Neutral/NeophyteHybrid:BrokenCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Hurt#0" value="Patriarche, protège nous !"/>
	<entry name="Neutral/NeophyteHybrid:HurtCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Idle#0" value="Ça s'approche, mes frères."/>
	<entry name="Neutral/NeophyteHybrid:Idle#1" value="L'ombre s'approche."/>
	<entry name="Neutral/NeophyteHybrid:Idle#2" value="Le Magus prédit une grande faim."/>
	<entry name="Neutral/NeophyteHybrid:Idle#3" value="Quand le jour viendra, nous nous offrirons volontairement."/>
	<entry name="Neutral/NeophyteHybrid:Idle#4" value="C'est presque LA."/>
	<entry name="Neutral/NeophyteHybrid:IdleCount" value="5"/>
	<entry name="Neutral/NeophyteHybrid:Shaken#0" value="Nous sommes de la pauvre viande, en effet."/>
	<entry name="Neutral/NeophyteHybrid:ShakenCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Victory#0" value="Ils sont de la viande pour la Reine en devenir !"/>
	<entry name="Neutral/NeophyteHybrid:VictoryCount" value="1"/>
	<entry name="Orks/Battlewagon:Attack#0" value="Wouh, dakka, youpi !"/>
	<entry name="Orks/Battlewagon:Attack#1" value="Lé rouj frap plu for !"/>
	<entry name="Orks/Battlewagon:Attack#2" value="Roul leur desu !"/>
	<entry name="Orks/Battlewagon:Attack#3" value="On tir en boujan !"/>
	<entry name="Orks/Battlewagon:AttackCount" value="4"/>
	<entry name="Orks/Battlewagon:Broken#0" value="Fé d'mi-tour !"/>
	<entry name="Orks/Battlewagon:Broken#1" value="Bordel ! Bouj s'truc !"/>
	<entry name="Orks/Battlewagon:BrokenCount" value="2"/>
	<entry name="Orks/Battlewagon:EnemyFaction:AstraMilitarum#0" value="Dé zoms ! Tir sur tou ski bouj !"/>
	<entry name="Orks/Battlewagon:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:Necrons#0" value="Fèt-zen de la feraye !"/>
	<entry name="Orks/Battlewagon:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:Orks#0" value="Cé nou ! Enfin un vré comba !"/>
	<entry name="Orks/Battlewagon:EnemyFaction:OrksCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:SpaceMarines#0" value="Roule sur lé boit' de konserve !"/>
	<entry name="Orks/Battlewagon:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Orks/Battlewagon:Hurt#0" value="Kombien de rou s'truk é censé avoir ?"/>
	<entry name="Orks/Battlewagon:HurtCount" value="1"/>
	<entry name="Orks/Battlewagon:Idle#0" value="Et j'y dis, et si on lui mété deux moteurs ?"/>
	<entry name="Orks/Battlewagon:Idle#1" value="Kekun a d'la peinture rouj' ?"/>
	<entry name="Orks/Battlewagon:Idle#2" value="Fo allé plu vit, boss ! PLU VIT !"/>
	<entry name="Orks/Battlewagon:Idle#3" value="Toot-toooot !"/>
	<entry name="Orks/Battlewagon:IdleCount" value="4"/>
	<entry name="Orks/Battlewagon:Shaken#0" value="S'truk a pas dé ressor ?"/>
	<entry name="Orks/Battlewagon:Shaken#1" value="Sale bouffeur de grot de suceurs de Squig !"/>
	<entry name="Orks/Battlewagon:Shaken#2" value="Y nous tir desu ! !"/>
	<entry name="Orks/Battlewagon:ShakenCount" value="3"/>
	<entry name="Orks/Battlewagon:Victory#0" value="WAAAGH ! Orks !"/>
	<entry name="Orks/Battlewagon:VictoryCount" value="1"/>
	<entry name="Orks/Boy:Attack#0" value="Tapé-lé plu for !"/>
	<entry name="Orks/Boy:Attack#1" value="Dakka, dakka, dakka !"/>
	<entry name="Orks/Boy:Attack#2" value="Hah-hah-hah, cé sa la vi !"/>
	<entry name="Orks/Boy:Attack#3" value="WAAAGH !"/>
	<entry name="Orks/Boy:Attack#4" value="Où kon s'ba ?"/>
	<entry name="Orks/Boy:AttackCount" value="5"/>
	<entry name="Orks/Boy:Broken#0" value="AAAWGH ! J'me kass !"/>
	<entry name="Orks/Boy:Broken#1" value="Chakun pour soi !"/>
	<entry name="Orks/Boy:Broken#2" value="Jé laissé un squig sur le feu… J'revien vit' !"/>
	<entry name="Orks/Boy:Broken#3" value="Non… non, non, non."/>
	<entry name="Orks/Boy:Broken#4" value="Cours cours COURS !"/>
	<entry name="Orks/Boy:BrokenCount" value="5"/>
	<entry name="Orks/Boy:EnemyFaction:AstraMilitarum#0" value="Dé zom ! Y zont des chouét' truk à piké."/>
	<entry name="Orks/Boy:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Orks/Boy:EnemyFaction:Necrons#0" value="Dé truk de métal. Cé sal truk veule pas crévé !"/>
	<entry name="Orks/Boy:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Orks/Boy:EnemyFaction:Orks#0" value="Oh, une otr' WAAAGH ! Maint'nant on cé kon va avoir un chouét' komba."/>
	<entry name="Orks/Boy:EnemyFaction:OrksCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/CatachanDevil#0" value="Kombien d'jamb ça a ? Y a bokou a mangé desu."/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/Enslaver#0" value="O, ma tête. Kastoa !"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KastelanRobot#0" value="Woah ! Cé pa drole d'affronté dé truk en boit'."/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="Oh, lé zom ki pu son là ? On va bien s'battre !"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/Psychneuein#0" value="Dé truk ki fon bzz ! Gaf, y rentre dans ta tete et-POP !"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="Orks/Boy:Hurt#0" value="Atan, mon cervo y dégouline. Kekun a un marteau et dé klou ?"/>
	<entry name="Orks/Boy:Hurt#1" value="Il é mor ! Ha ha. J'pren cé den !"/>
	<entry name="Orks/Boy:Hurt#2" value="Cé mon bra ?"/>
	<entry name="Orks/Boy:Hurt#3" value="On é lé plu for !"/>
	<entry name="Orks/Boy:HurtCount" value="4"/>
	<entry name="Orks/Boy:Idle#0" value="Cél'Boss ! Kestuveu, Boss ?"/>
	<entry name="Orks/Boy:Idle#1" value="Plus de kanon cé plus de dakka. Plus de dakka cé plus drole !"/>
	<entry name="Orks/Boy:Idle#2" value="Nan mec, cé mon squig. Trouve zan un otre !"/>
	<entry name="Orks/Boy:Idle#3" value="Eh boss, keskis'pass ?"/>
	<entry name="Orks/Boy:Idle#4" value="Un squig sur le mur. Deux…"/>
	<entry name="Orks/Boy:Idle#5" value="S'kikoup' é pa acé gro !"/>
	<entry name="Orks/Boy:Idle#6" value="Où cé kon s'ba ?"/>
	<entry name="Orks/Boy:Idle#7" value="Ki tu ve ke j'frap ?"/>
	<entry name="Orks/Boy:Idle#8" value="Koi ?"/>
	<entry name="Orks/Boy:IdleCount" value="9"/>
	<entry name="Orks/Boy:QuestStory0#0" value="Bordel de Gork, on s'ennui Boss. Y a rien à frapé ?"/>
	<entry name="Orks/Boy:QuestStory0Count" value="1"/>
	<entry name="Orks/Boy:QuestStory1#0" value="Garde s'bizarboy loin de nous, on veut pas s'faire pété la tête."/>
	<entry name="Orks/Boy:QuestStory1Count" value="1"/>
	<entry name="Orks/Boy:QuestStory2#0" value="Ki vit sous terre ? Y a rien à s'battre contre ici."/>
	<entry name="Orks/Boy:QuestStory2Count" value="1"/>
	<entry name="Orks/Boy:QuestStory3#0" value="Tu veux kon ramasse plein de truks ? Pas de souci, Boss."/>
	<entry name="Orks/Boy:QuestStory3Count" value="1"/>
	<entry name="Orks/Boy:QuestStory4#0" value="Peignon la ville en rouj', Boss, ha ha ha."/>
	<entry name="Orks/Boy:QuestStory4Count" value="1"/>
	<entry name="Orks/Boy:QuestStory5#0" value="J'pourré pa boufé un squiggoth, Boss, tous lé poil restent colé à mé den."/>
	<entry name="Orks/Boy:QuestStory5Count" value="1"/>
	<entry name="Orks/Boy:Shaken#0" value="Va l'tapé toi-même, Boss."/>
	<entry name="Orks/Boy:Shaken#1" value="J'ai b'soin de c'te courage liquide au champignon."/>
	<entry name="Orks/Boy:Shaken#2" value="Chui pas Makari, J'veu vivre ! J'ai dé truc à combattre !"/>
	<entry name="Orks/Boy:Shaken#3" value="Cé pa trè Ork."/>
	<entry name="Orks/Boy:ShakenCount" value="4"/>
	<entry name="Orks/Boy:Victory#0" value="Ta aimé ta mor ?"/>
	<entry name="Orks/Boy:Victory#1" value="L'truk avec lé pa-Ork cé ki cass vite."/>
	<entry name="Orks/Boy:Victory#2" value="Tir, tir, tir, BOUM. YEAH !"/>
	<entry name="Orks/Boy:Victory#3" value="Ha ha ha !"/>
	<entry name="Orks/Boy:Victory#4" value="T't'attendé pa à ça, hein ?"/>
	<entry name="Orks/Boy:Victory#5" value="Oh, r'lève-toi ! On n'a pas fini d'te défoncé !"/>
	<entry name="Orks/Boy:Victory#6" value="Lé Orks son fé pour deux choses. Se battre, gagner, et compter !"/>
	<entry name="Orks/Boy:Victory#7" value="BOUM. BOUM. BOUM."/>
	<entry name="Orks/Boy:Victory#8" value="Y sont cassé ce-là, Boss. Y en a dotre ?"/>
	<entry name="Orks/Boy:VictoryCount" value="9"/>
	<entry name="Orks/BurnaBommer:Attack#0" value="On lé survole, Boss."/>
	<entry name="Orks/BurnaBommer:AttackCount" value="1"/>
	<entry name="Orks/BurnaBommer:Broken#0" value="On s'kass d'ici !"/>
	<entry name="Orks/BurnaBommer:BrokenCount" value="1"/>
	<entry name="Orks/BurnaBommer:Hurt#0" value="Lé gro trou le rendent plus mieux volant, hein ?"/>
	<entry name="Orks/BurnaBommer:HurtCount" value="1"/>
	<entry name="Orks/BurnaBommer:Idle#0" value="On tourne é on tourne é on tourne…"/>
	<entry name="Orks/BurnaBommer:Idle#1" value="On mont', mont', mont', et on descend, descend, descend…"/>
	<entry name="Orks/BurnaBommer:Idle#2" value="Cé superb' meks dans leur engin flottan."/>
	<entry name="Orks/BurnaBommer:Idle#3" value="Brûler ou bombarder, tel é la kestion."/>
	<entry name="Orks/BurnaBommer:IdleCount" value="4"/>
	<entry name="Orks/BurnaBommer:Shaken#0" value="Donné-nous une chance, bordel !"/>
	<entry name="Orks/BurnaBommer:ShakenCount" value="1"/>
	<entry name="Orks/BurnaBommer:Victory#0" value="WAAAGH ! MANGE-CA !"/>
	<entry name="Orks/BurnaBommer:VictoryCount" value="1"/>
	<entry name="Orks/Dakkajet" value="Orks/BurnaBommer"/>
	<entry name="Orks/Deffkopta:Attack#0" value="Ca cé un kikoup'."/>
	<entry name="Orks/Deffkopta:AttackCount" value="1"/>
	<entry name="Orks/Deffkopta:Broken#0" value="Oh, j'ai la tête ki tourn'."/>
	<entry name="Orks/Deffkopta:BrokenCount" value="1"/>
	<entry name="Orks/Deffkopta:Hurt#0" value="Oh, viens là-haut ke j'te tape."/>
	<entry name="Orks/Deffkopta:HurtCount" value="1"/>
	<entry name="Orks/Deffkopta:Idle#0" value="J'adore respiré l'odeur du kramé l'matin."/>
	<entry name="Orks/Deffkopta:Idle#1" value="On s'ba ou on s'ba ?"/>
	<entry name="Orks/Deffkopta:Idle#2" value="Lé T'au surfent pas."/>
	<entry name="Orks/Deffkopta:Idle#3" value="J'me demand si j'peu volé la tête en bas ?"/>
	<entry name="Orks/Deffkopta:IdleCount" value="4"/>
	<entry name="Orks/Deffkopta:Shaken#0" value="Fo kombien de baton pour k'ça continue de voler ?"/>
	<entry name="Orks/Deffkopta:ShakenCount" value="1"/>
	<entry name="Orks/Deffkopta:Victory#0" value="Un jour cét bagar va sfinir…"/>
	<entry name="Orks/Deffkopta:VictoryCount" value="1"/>
	<entry name="Orks/FlashGitz" value="Orks/Boy"/>
	<entry name="Orks/GargantuanSquiggoth:Attack#0" value="Par là squiggie, whoah !"/>
	<entry name="Orks/GargantuanSquiggoth:AttackCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Broken#0" value="Nan, idiot de squig, de lot'côté."/>
	<entry name="Orks/GargantuanSquiggoth:BrokenCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Hurt#0" value="Lui di pas s'il é mort."/>
	<entry name="Orks/GargantuanSquiggoth:HurtCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Idle#0" value="Par ici, squiggy, y a d'la bouffe."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#1" value="Gentil squiggy."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#2" value="Vilain squiggy. Rekrach Udgrub ! VILAIN !"/>
	<entry name="Orks/GargantuanSquiggoth:Idle#3" value="Burp ! Rien ne bat un bouf'tête pour le goûter."/>
	<entry name="Orks/GargantuanSquiggoth:IdleCount" value="4"/>
	<entry name="Orks/GargantuanSquiggoth:Shaken#0" value="T'inquiète, squiggy, continue d'manger."/>
	<entry name="Orks/GargantuanSquiggoth:ShakenCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Victory#0" value="Gentil Squiggy ! C'est l'heure de manger !"/>
	<entry name="Orks/GargantuanSquiggoth:VictoryCount" value="1"/>
	<entry name="Orks/Gorkanaut:Attack#0" value="DAKKA DAKKA !"/>
	<entry name="Orks/Gorkanaut:Attack#1" value="BOUM BOUM !"/>
	<entry name="Orks/Gorkanaut:AttackCount" value="2"/>
	<entry name="Orks/Gorkanaut:Broken#0" value="Mon bidule é tou kassé !"/>
	<entry name="Orks/Gorkanaut:BrokenCount" value="1"/>
	<entry name="Orks/Gorkanaut:Hurt#0" value="Hé, arrêtez d'tirer ! Vous savez combien d'dents s'truc coûte ?"/>
	<entry name="Orks/Gorkanaut:HurtCount" value="1"/>
	<entry name="Orks/Gorkanaut:Idle#0" value="On peut y coller plus de dakka ? Siouplaît ?"/>
	<entry name="Orks/Gorkanaut:Idle#1" value="J'ressens l'pouvoir de Gork !"/>
	<entry name="Orks/Gorkanaut:Idle#2" value="Si on y colle plus de tôle, on peut faire un Gargant ?"/>
	<entry name="Orks/Gorkanaut:Idle#3" value="J'crois j'ai fait tombé mon r'pas ici. La s'maine dernière."/>
	<entry name="Orks/Gorkanaut:IdleCount" value="4"/>
	<entry name="Orks/Gorkanaut:Shaken#0" value="MEM PA PEUR !"/>
	<entry name="Orks/Gorkanaut:ShakenCount" value="1"/>
	<entry name="Orks/Gorkanaut:Victory#0" value="WAAAGH ! Les toleboyz son lé meyeurs."/>
	<entry name="Orks/Gorkanaut:VictoryCount" value="1"/>
	<entry name="Orks/Headquarters:Attack#0" value="Enfin, on tire sur kekchose."/>
	<entry name="Orks/Headquarters:AttackCount" value="1"/>
	<entry name="Orks/Headquarters:Broken#0" value="Oskour, Boss !"/>
	<entry name="Orks/Headquarters:BrokenCount" value="1"/>
	<entry name="Orks/Headquarters:Hurt#0" value="Y tir sur l'fort, Boss !"/>
	<entry name="Orks/Headquarters:HurtCount" value="1"/>
	<entry name="Orks/Headquarters:Idle#0" value="Dé tour de gard ! Où é la WAAAGH là-d'dans ?"/>
	<entry name="Orks/Headquarters:Idle#1" value="Ki cé l'boss ici ?"/>
	<entry name="Orks/Headquarters:Idle#2" value="Blood Axes au rapor chef !"/>
	<entry name="Orks/Headquarters:Idle#3" value="S'cacher derrière dé murs é pa trè Ork."/>
	<entry name="Orks/Headquarters:IdleCount" value="4"/>
	<entry name="Orks/Headquarters:Shaken#0" value="Lé Orks fuient pa !"/>
	<entry name="Orks/Headquarters:ShakenCount" value="1"/>
	<entry name="Orks/Headquarters:Victory#0" value="WAAAGH ! J'lé eu !"/>
	<entry name="Orks/Headquarters:VictoryCount" value="1"/>
	<entry name="Orks/KillaKan" value="Orks/Gorkanaut"/>
	<entry name="Orks/Meganob" value="Orks/Boy"/>
	<entry name="Orks/Mek:Attack#0" value="J'tire avec toutes mé armes !"/>
	<entry name="Orks/Mek:AttackCount" value="1"/>
	<entry name="Orks/Mek:Broken#0" value="Kekchose a b'soin d'être réparé au fort."/>
	<entry name="Orks/Mek:BrokenCount" value="1"/>
	<entry name="Orks/Mek:Hurt#0" value="Mé champ kustom sont kacé !"/>
	<entry name="Orks/Mek:HurtCount" value="1"/>
	<entry name="Orks/Mek:Idle#0" value="J'pense que j'pourrai faire un truk spécial…"/>
	<entry name="Orks/Mek:Idle#1" value="Pass'-moi l'machin. Nan, tien sa pendan que j'le frap…"/>
	<entry name="Orks/Mek:Idle#2" value="Imagine un fling' avec CINQ canons. Yeahhhh."/>
	<entry name="Orks/Mek:Idle#3" value="Hmm. Boît'Kitu march pa. Faut plus gros Grot, plus petits klous."/>
	<entry name="Orks/Mek:IdleCount" value="4"/>
	<entry name="Orks/Mek:Shaken#0" value="Par lé intestin de Mork, sa se peu pas !"/>
	<entry name="Orks/Mek:ShakenCount" value="1"/>
	<entry name="Orks/Mek:Victory#0" value="WAAAGH ! L'intelijance dé Mek gagne encore !"/>
	<entry name="Orks/Mek:VictoryCount" value="1"/>
	<entry name="Orks/MekGun:Attack#0" value="On tir avec le gro canon, m'sieur Boss Ork !"/>
	<entry name="Orks/MekGun:AttackCount" value="1"/>
	<entry name="Orks/MekGun:Broken#0" value="Chacun pour soi !"/>
	<entry name="Orks/MekGun:BrokenCount" value="1"/>
	<entry name="Orks/MekGun:Hurt#0" value="Davantaje de grot morts."/>
	<entry name="Orks/MekGun:HurtCount" value="1"/>
	<entry name="Orks/MekGun:Idle#0" value="Lé grots son lé plu intelijans et lé meyeurs."/>
	<entry name="Orks/MekGun:Idle#1" value="Loin du kombat, avec un gro canon. Cé l'pied."/>
	<entry name="Orks/MekGun:Idle#2" value="Moki, sor du canon !"/>
	<entry name="Orks/MekGun:Idle#3" value="Un Mek ki pu."/>
	<entry name="Orks/MekGun:IdleCount" value="4"/>
	<entry name="Orks/MekGun:Shaken#0" value="Arété d'nou taper, on va pa s'barrer !"/>
	<entry name="Orks/MekGun:ShakenCount" value="1"/>
	<entry name="Orks/MekGun:Victory#0" value="Lé grots, je veux dire, lé Orks son lé meyeur, hein Boss ?"/>
	<entry name="Orks/MekGun:VictoryCount" value="1"/>
	<entry name="Orks/Painboy:Attack#0" value="Hi hi, opération sur l'champ d'bataille !"/>
	<entry name="Orks/Painboy:AttackCount" value="1"/>
	<entry name="Orks/Painboy:Broken#0" value="Lé Doks et lé p'tit snots d'abord !"/>
	<entry name="Orks/Painboy:BrokenCount" value="1"/>
	<entry name="Orks/Painboy:Hurt#0" value="Fo just que j'rakroch ça, attend."/>
	<entry name="Orks/Painboy:HurtCount" value="1"/>
	<entry name="Orks/Painboy:Idle#0" value="Dis, Boss, kestu diré d'un chouette nouveau gro bras ?"/>
	<entry name="Orks/Painboy:Idle#1" value="Jé b'soin d'té den. Ouv' grand. Dis WAAAGH !"/>
	<entry name="Orks/Painboy:Idle#2" value="Cé koi être 'fou', de toute façon ?"/>
	<entry name="Orks/Painboy:Idle#3" value="J'ai besoin d'un plus gros squig-aiguille."/>
	<entry name="Orks/Painboy:IdleCount" value="4"/>
	<entry name="Orks/Painboy:Shaken#0" value="Eh, cé pa bon."/>
	<entry name="Orks/Painboy:ShakenCount" value="1"/>
	<entry name="Orks/Painboy:Victory#0" value="Regard tou cé truc… Jénial."/>
	<entry name="Orks/Painboy:VictoryCount" value="1"/>
	<entry name="Orks/SquighogBoy:Attack#0" value="Utilisez les Stikkas !" />
    <entry name="Orks/SquighogBoy:Attack#1" value="WAAAGH !" />
    <entry name="Orks/SquighogBoy:Attack#2" value="Attrapez les gitz !" />
    <entry name="Orks/SquighogBoy:Attack#3" value="Snagg lé bètes!"/>
    <entry name="Orks/SquighogBoy:AttackCount" value="4"/>
    <entry name="Orks/SquighogBoy:Broken#0" value="Qui m'a savaté les dents !"/>
    <entry name="Orks/SquighogBoy:BrokenCount" value="1"/>
    <entry name="Orks/SquighogBoy:Hurt#0" value=""/>
    <entry name="Orks/SquighogBoy:HurtCount" value="1"/>
    <entry name="Orks/SquighogBoy:Idle#0" value="Métrisez lé squigs!"/>
    <entry name="Orks/SquighogBoy:Idle#1" value="Pourquoi on ne fait pas de krump ?" />
    <entry name="Orks/SquighogBoy:Idle#2" value="Les anciennes méthodes sont les meilleures." />
    <entry name="Orks/SquighogBoy:Idle#3" value="Qui a mangé ma saddlegit ?!" />
    <entry name="Orks/SquighogBoy:IdleCount" value="4"/>
    <entry name="Orks/SquighogBoy:Shaken#0" value="Ils savent pas qui on est ?!"/>
    <entry name="Orks/SquighogBoy:ShakenCount" value="1"/>
    <entry name="Orks/SquighogBoy:Victory#0" value="On a les meilleurs squighogz !" />
    <entry name="Orks/SquighogBoy:VictoryCount" value="1" />
	<entry name="Orks/Tankbusta" value="Orks/Boy"/>
	<entry name="Orks/Warboss:Attack#0" value="Ki cé l'myeur Ork. WAAAGH ! CE MOI."/>
	<entry name="Orks/Warboss:AttackCount" value="1"/>
	<entry name="Orks/Warboss:Broken#0" value="J'utilise dé tactik, cé tou."/>
	<entry name="Orks/Warboss:BrokenCount" value="1"/>
	<entry name="Orks/Warboss:Hurt#0" value="Ouille, tu cé pa ki chui ?"/>
	<entry name="Orks/Warboss:HurtCount" value="1"/>
	<entry name="Orks/Warboss:Idle#0" value="Rien à faire. BORDEL !"/>
	<entry name="Orks/Warboss:Idle#1" value="OU CE KON S'BA ?"/>
	<entry name="Orks/Warboss:Idle#2" value="Du curry de squig pour l'déjeuner ENCORE ? Où sont lé chiot' ?"/>
	<entry name="Orks/Warboss:Idle#3" value="J'ai b'soin d'taper un truk !"/>
	<entry name="Orks/Warboss:Idle#4" value="Hey, le grot, Ta enlevé lé squigs de ma Mega-Armure ou pa encor ?"/>
	<entry name="Orks/Warboss:IdleCount" value="5"/>
	<entry name="Orks/Warboss:Shaken#0" value="Plus vit on s'barre d'ici, mieu cé."/>
	<entry name="Orks/Warboss:ShakenCount" value="1"/>
	<entry name="Orks/Warboss:Victory#0" value="'Videmment kon gagne, j'étais là !"/>
	<entry name="Orks/Warboss:VictoryCount" value="1"/>
	<entry name="Orks/Warbuggy" value="Orks/Battlewagon"/>
	<entry name="Orks/Weirdboy:Attack#0" value="Aïe… ma tête."/>
	<entry name="Orks/Weirdboy:AttackCount" value="1"/>
	<entry name="Orks/Weirdboy:Broken#0" value="Sorté-moi d'là !"/>
	<entry name="Orks/Weirdboy:BrokenCount" value="1"/>
	<entry name="Orks/Weirdboy:Hurt#0" value="Y a des têtes qu'explosent !"/>
	<entry name="Orks/Weirdboy:HurtCount" value="1"/>
	<entry name="Orks/Weirdboy:Idle#0" value="Blurble."/>
	<entry name="Orks/Weirdboy:Idle#1" value="Mork parle. Gork marche."/>
	<entry name="Orks/Weirdboy:Idle#2" value="L'ombre dans le warp… !"/>
	<entry name="Orks/Weirdboy:Idle#3" value="Sous Gladius se trouvent les Krorks et les Gross'Têt, qui attendent le combat final."/>
	<entry name="Orks/Weirdboy:IdleCount" value="4"/>
	<entry name="Orks/Weirdboy:Shaken#0" value="Gork et Mort t'oblige !"/>
	<entry name="Orks/Weirdboy:ShakenCount" value="1"/>
	<entry name="Orks/Weirdboy:Victory#0" value="Ha ha, WAAAGH !"/>
	<entry name="Orks/Weirdboy:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Attack#0" value="Je ne fais pas que soigner."/>
	<entry name="SpaceMarines/Apothecary:AttackCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Broken#0" value="Je ne peux pas abandonner les glandes progénoïdes !"/>
	<entry name="SpaceMarines/Apothecary:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:AstraMilitarum#0" value="Méprisable."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:Necrons#0" value="Des monstres dépourvus de sang."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:Orks#0" value="Des Peaux-Vertes ! J'aimerais que l'on puisse régénérer nos membres comme eux."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:SpaceMarines#0" value="Des frère renégats, mais pas des traîtres. Je récupérerai leurs glandes progénoïdes tout pareil."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Hurt#0" value="Je me soigne."/>
	<entry name="SpaceMarines/Apothecary:HurtCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Idle#0" value="Apothicaire disponible."/>
	<entry name="SpaceMarines/Apothecary:Idle#1" value="Les médics n'ont pas besoin de repos."/>
	<entry name="SpaceMarines/Apothecary:Idle#2" value="Entraînement à l'utilisation du Reductor."/>
	<entry name="SpaceMarines/Apothecary:Idle#3" value="La glande progénoïde est connectée aux Betchers et à la membrane cataleptique…"/>
	<entry name="SpaceMarines/Apothecary:IdleCount" value="4"/>
	<entry name="SpaceMarines/Apothecary:Shaken#0" value="Bonnes nouvelles ! Je ne suis pas mort."/>
	<entry name="SpaceMarines/Apothecary:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Victory#0" value="Ils sont morts ! Je ne suis pas sûr que ce soit la meilleure façon d'utiliser mon temps !"/>
	<entry name="SpaceMarines/Apothecary:VictoryCount" value="1"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/Captain:Attack#0" value="Meurs avec joie, vermine, en sachant que tu meurs de ma main !"/>
	<entry name="SpaceMarines/Captain:Attack#1" value="Mange des bolts."/>
	<entry name="SpaceMarines/Captain:Attack#2" value="Pour l'Empereur !"/>
	<entry name="SpaceMarines/Captain:Attack#3" value="Vous affrontez un capitaine Space Marine. Rendez-vous maintenant pour une mort rapide."/>
	<entry name="SpaceMarines/Captain:Attack#4" value="Guilliman attends.."/>
	<entry name="SpaceMarines/Captain:Attack#5" value="Ma foi est mon bouclier !"/>
	<entry name="SpaceMarines/Captain:AttackCount" value="6"/>
	<entry name="SpaceMarines/Captain:Broken#0" value="L'Emperor nous protège !"/>
	<entry name="SpaceMarines/Captain:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:AstraMilitarum#0" value="Des traîtres de la garde. trivial."/>
	<entry name="SpaceMarines/Captain:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:Necrons#0" value="Détruisez leurs tombes ! Écrasez ces horreurs mécaniques !"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:Orks#0" value="L'Empereur ne nous libérera-t-il jamais de cette peste verte ?"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:SpaceMarines#0" value="Des Renégats. Marines, préparez vos armes, nous nous battons pour l'honneur."/>
	<entry name="SpaceMarines/Captain:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Captain:Hurt#0" value="Comment est-ce que c'est passé à travers mon armure ?"/>
	<entry name="SpaceMarines/Captain:HurtCount" value="1"/>
	<entry name="SpaceMarines/Captain:Idle#0" value="Je devrais mener depuis les premières lignes."/>
	<entry name="SpaceMarines/Captain:Idle#1" value="Les discours inspirants ne s'écrivent pas tout seul."/>
	<entry name="SpaceMarines/Captain:Idle#2" value="Nous devons sauver l'humanité d'elle-même."/>
	<entry name="SpaceMarines/Captain:Idle#3" value="Pourquoi ici ? Pourquoi Gladius ? Sommes-nous testés ?"/>
	<entry name="SpaceMarines/Captain:IdleCount" value="4"/>
	<entry name="SpaceMarines/Captain:Shaken#0" value="Je ne recule devant personne !"/>
	<entry name="SpaceMarines/Captain:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Captain:Victory#0" value="Ainsi tombent tous les ennemis du Trône d'Or !"/>
	<entry name="SpaceMarines/Captain:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Attack#0" value="Reçois la bénédiction du Crozius !"/>
	<entry name="SpaceMarines/Chaplain:Attack#1" value="Je te ferai croire !"/>
	<entry name="SpaceMarines/Chaplain:Attack#2" value="In nomine Imperator !"/>
	<entry name="SpaceMarines/Chaplain:AttackCount" value="3"/>
	<entry name="SpaceMarines/Chaplain:Broken#0" value="Pas un pas en arrière !"/>
	<entry name="SpaceMarines/Chaplain:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Hurt#0" value="Bien que je subisse leurs coups, je ne tomberai pas."/>
	<entry name="SpaceMarines/Chaplain:HurtCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Idle#0" value="La mort ne comporte aucun regret, si ce n'est que notre travail soit incomplet."/>
	<entry name="SpaceMarines/Chaplain:Idle#1" value="Donnez votre vie pour l'Empereur, ce ne sera pas en vain."/>
	<entry name="SpaceMarines/Chaplain:Idle#2" value="Il ne peut y avoir de paix tant que nos ennemis vivent."/>
	<entry name="SpaceMarines/Chaplain:Idle#3" value="Nous montrons notre résistance jusqu'au bout."/>
	<entry name="SpaceMarines/Chaplain:IdleCount" value="4"/>
	<entry name="SpaceMarines/Chaplain:Shaken#0" value="Pour un guerrier, le seul crime est la couardise."/>
	<entry name="SpaceMarines/Chaplain:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Victory#0" value="La foi est plus forte que l'adamantium."/>
	<entry name="SpaceMarines/Chaplain:VictoryCount" value="1"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/Dreadnought:Attack#0" value="Chaque mise à mort est rafraîchissante, même maintenant."/>
	<entry name="SpaceMarines/Dreadnought:Attack#1" value="Pour le Chapitre !"/>
	<entry name="SpaceMarines/Dreadnought:Attack#2" value="Hrm… De nouveau au combat."/>
	<entry name="SpaceMarines/Dreadnought:Attack#3" value="Dix mille ans de guerre…"/>
	<entry name="SpaceMarines/Dreadnought:AttackCount" value="4"/>
	<entry name="SpaceMarines/Dreadnought:Broken#0" value="Après un millénaire, je sais quand battre en retraite."/>
	<entry name="SpaceMarines/Dreadnought:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Dreadnought:Hurt#0" value="Mon sarcophage… a été touché."/>
	<entry name="SpaceMarines/Dreadnought:Hurt#1" value="La litanie de la préservation, Chapelain ?"/>
	<entry name="SpaceMarines/Dreadnought:HurtCount" value="2"/>
	<entry name="SpaceMarines/Dreadnought:Idle#0" value="Une tombe vivante…"/>
	<entry name="SpaceMarines/Dreadnought:Idle#1" value="La guerre est-elle vraiment éternelle ?"/>
	<entry name="SpaceMarines/Dreadnought:Idle#2" value="Dormir… mais rêver. Tel est le problème."/>
	<entry name="SpaceMarines/Dreadnought:Idle#3" value="Qui me réveille ?"/>
	<entry name="SpaceMarines/Dreadnought:IdleCount" value="4"/>
	<entry name="SpaceMarines/Dreadnought:Shaken#0" value="La peur n'est jamais de l'histoire ancienne."/>
	<entry name="SpaceMarines/Dreadnought:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Dreadnought:Victory#0" value="L'histoire du chapitre s'allonge."/>
	<entry name="SpaceMarines/Dreadnought:VictoryCount" value="1"/>
	<entry name="SpaceMarines/FortressOfRedemption" value="SpaceMarines/Headquarters"/>
	<entry name="SpaceMarines/Headquarters:Attack#0" value="Brisez-vous sur nos défenses !"/>
	<entry name="SpaceMarines/Headquarters:Attack#1" value="Quels idiots de s'attaquer à une forteresse de l'Adeptus Astartes."/>
	<entry name="SpaceMarines/Headquarters:Attack#2" value="Ici le commandant de la Forteresse. Feu."/>
	<entry name="SpaceMarines/Headquarters:Attack#3" value="Pourquoi ne pas essayer de passer par la porte de devant ?"/>
	<entry name="SpaceMarines/Headquarters:AttackCount" value="4"/>
	<entry name="SpaceMarines/Headquarters:Broken#0" value="Notre chapitre ne. Tombera. PAS."/>
	<entry name="SpaceMarines/Headquarters:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:AstraMilitarum#0" value="Garde à l'horizon. Hostile."/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:Necrons#0" value="Les Nécrons arrivent. Éliminez-les."/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:Orks#0" value="Des peaux-vertes, encore ? Préparez les bolters !"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:SpaceMarines#0" value="Les scouts ont engagé des renégats. De vils traîtres, ici ?"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Hurt#0" value="La forteresse a une brèche, je répète, la forteresse a une brèche."/>
	<entry name="SpaceMarines/Headquarters:HurtCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Idle#0" value="Tout est calme à domicile."/>
	<entry name="SpaceMarines/Headquarters:Idle#1" value="Commandant, on est prêt à toute éventualité."/>
	<entry name="SpaceMarines/Headquarters:Idle#2" value="Des litanies sont lues dans le Reclusiam."/>
	<entry name="SpaceMarines/Headquarters:Idle#3" value="Les serfs travaillent dur, monsieur."/>
	<entry name="SpaceMarines/Headquarters:IdleCount" value="4"/>
	<entry name="SpaceMarines/Headquarters:Shaken#0" value="Rappelez toutes les unités à la Forteresse !"/>
	<entry name="SpaceMarines/Headquarters:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Victory#0" value="Envahisseurs éliminés. Comment osent-ils ?"/>
	<entry name="SpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Attack#0" value="Visée en cours."/>
	<entry name="SpaceMarines/Hunter:AttackCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Broken#0" value="Le blindé se replie."/>
	<entry name="SpaceMarines/Hunter:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Hurt#0" value="Blindage pénétré."/>
	<entry name="SpaceMarines/Hunter:Hurt#1" value="Des tirs d'armes de poing."/>
	<entry name="SpaceMarines/Hunter:Hurt#2" value="Les chenilles ont été touchées."/>
	<entry name="SpaceMarines/Hunter:HurtCount" value="3"/>
	<entry name="SpaceMarines/Hunter:Idle#0" value="Le Cogitateur fonctionne correctement."/>
	<entry name="SpaceMarines/Hunter:Idle#1" value="Invocation des Esprits de la Machine."/>
	<entry name="SpaceMarines/Hunter:Idle#2" value="Réparations en cours."/>
	<entry name="SpaceMarines/Hunter:IdleCount" value="3"/>
	<entry name="SpaceMarines/Hunter:Shaken#0" value="L'équipage est un peu inquiet, monsieur."/>
	<entry name="SpaceMarines/Hunter:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Victory#0" value="Le blindé avance."/>
	<entry name="SpaceMarines/Hunter:VictoryCount" value="1"/>
	<entry name="SpaceMarines/LandRaider" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/LandSpeeder" value="SpaceMarines/ScoutBiker"/>
	<entry name="SpaceMarines/Librarian:Attack#0" value="Laisse-moi regarder ton futur… Ah. Il ne reste pas grand chose."/>
	<entry name="SpaceMarines/Librarian:Attack#1" value="J'ai un don. Il serait impoli de ne pas le partager…"/>
	<entry name="SpaceMarines/Librarian:Attack#2" value="Un claquement de doigt… et tes os se brisent."/>
	<entry name="SpaceMarines/Librarian:AttackCount" value="3"/>
	<entry name="SpaceMarines/Librarian:Broken#0" value="J'entends le démon hurler dans mes oreilles."/>
	<entry name="SpaceMarines/Librarian:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Hurt#0" value="La douleur physique n'est rien comparé à la damnation éternelle."/>
	<entry name="SpaceMarines/Librarian:HurtCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Idle#0" value="Béni soit l'esprit trop petit pour le doute."/>
	<entry name="SpaceMarines/Librarian:Idle#1" value="Je ne crains pas le warp."/>
	<entry name="SpaceMarines/Librarian:Idle#2" value="La tempête warp… Parfois la douleur est insupportable."/>
	<entry name="SpaceMarines/Librarian:Idle#3" value="Quand ce sera fini… Le Librarius m'appelle."/>
	<entry name="SpaceMarines/Librarian:IdleCount" value="4"/>
	<entry name="SpaceMarines/Librarian:Shaken#0" value="Nos archives n'ont rien là-dessus…"/>
	<entry name="SpaceMarines/Librarian:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Victory#0" value="Leur échec était prévu."/>
	<entry name="SpaceMarines/Librarian:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Predator" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#0" value="Mon devoir est mon armure."/>
    <entry name="SpaceMarines/PrimarisAggressor:Attack#1" value="tempête de feu."/>
    <entry name="SpaceMarines/PrimarisAggressor:Attack#2" value="Attendez qu'ils approchent."/>
    <entry name="SpaceMarines/PrimarisAggressor:Attack#3" value="Fouillez-les !"/>
    <entry name="SpaceMarines/PrimarisAggressor:AttackCount" value="4"/>
    <entry name="SpaceMarines/PrimarisAggressor:Broken#0" value="Attendez. Attendez !"/>
    <entry name="SpaceMarines/PrimarisAggressor:BrokenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisAggressor:Hurt#0" value="En son nom !"/>
    <entry name="SpaceMarines/PrimarisAggressor:HurtCount" value="1"/>
    <entry name="SpaceMarines/PrimarisAggressor:Idle#0" value="Nous attendons."/>
    <entry name="SpaceMarines/PrimarisAggressor:Idle#1" value="Je suis à la hauteur de ma tâche."/>
    <entry name="SpaceMarines/PrimarisAggressor:Idle#2" value="Le feu purificateur."/>
    <entry name="SpaceMarines/PrimarisAggressor:Idle#3" value="Nous réduirons ses ennemis en cendres."/>
    <entry name="SpaceMarines/PrimarisAggressor:IdleCount" value="4"/>
    <entry name="SpaceMarines/PrimarisAggressor:Shaken#0" value="Feu de couverture !"/>
    <entry name="SpaceMarines/PrimarisAggressor:ShakenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisAggressor:Victory#0" value="Purifié par la fureur de l'Empereur !"/>
    <entry name="SpaceMarines/PrimarisAggressor:VictoryCount" value="1"/>
    <entry name="SpaceMarines/PrimarisHellblaster:Attack#0" value="Fusillade de plasma."/>
    <entry name="SpaceMarines/PrimarisHellblaster:Attack#1" value="Incinérateurs suralimentés."/>
    <entry name="SpaceMarines/PrimarisHellblaster:Attack#2" value="Chasseurs d'armures."/>
    <entry name="SpaceMarines/PrimarisHellblaster:Attack#3" value="Faisant fondre ses ennemis."/>
    <entry name="SpaceMarines/PrimarisHellblaster:AttackCount" value="4"/>
    <entry name="SpaceMarines/PrimarisHellblaster:Broken#0" value="Un dernier baroud d'honneur."/>
    <entry name="SpaceMarines/PrimarisHellblaster:BrokenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisHellblaster:Hurt#0" value="Inévitable."/>
    <entry name="SpaceMarines/PrimarisHellblaster:HurtCount" value="1"/>
    <entry name="SpaceMarines/PrimarisHellblaster:Idle#0" value="Refroidissement des reliques."/>
    <entry name="SpaceMarines/PrimarisHellblaster:Idle#1" value="Fusion sacrée."/>
    <entry name="SpaceMarines/PrimarisHellblaster:Idle#2" value="Ils n'auront pas ce monde."/>
    <entry name="SpaceMarines/PrimarisHellblaster:Idle#3" value="Stabilisation des champs de confinement."/>
    <entry name="SpaceMarines/PrimarisHellblaster:IdleCount" value="4"/>
    <entry name="SpaceMarines/PrimarisHellblaster:Shaken#0" value="Bobines… déstabilisantes."/>
    <entry name="SpaceMarines/PrimarisHellblaster:ShakenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisHellblaster:Victory#0" value="Les ennemis sont incinérés."/>
    <entry name="SpaceMarines/PrimarisHellblaster:VictoryCount" value="1"/>
    <entry name="SpaceMarines/PrimarisInceptor:Attack#0" value="Soutien orbital."/>
    <entry name="SpaceMarines/PrimarisInceptor:Attack#1" value="Établissement d'une tête de pont."/>
    <entry name="SpaceMarines/PrimarisInceptor:Attack#2" value="Intercédant !"/>
    <entry name="SpaceMarines/PrimarisInceptor:Attack#3" value="Une pluie de vengeance ardente."/>
    <entry name="SpaceMarines/PrimarisInceptor:AttackCount" value="4"/>
    <entry name="SpaceMarines/PrimarisInceptor:Broken#0" value="Dans les cieux."/>
    <entry name="SpaceMarines/PrimarisInceptor:BrokenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisInceptor:Hurt#0" value="Exfiltration !"/>
    <entry name="SpaceMarines/PrimarisInceptor:HurtCount" value="1"/>
    <entry name="SpaceMarines/PrimarisInceptor:Idle#0" value="En attente de largage."/>
    <entry name="SpaceMarines/PrimarisInceptor:Idle#1" value="Prêts à frapper."/>
    <entry name="SpaceMarines/PrimarisInceptor:Idle#2" value="Pour le chapitre !"/>
    <entry name="SpaceMarines/PrimarisInceptor:Idle#3" value="Plus de fils innombrables."/>
    <entry name="SpaceMarines/PrimarisInceptor:IdleCount" value="4"/>
    <entry name="SpaceMarines/PrimarisInceptor:Shaken#0" value="Concentrez-vous !"/>
    <entry name="SpaceMarines/PrimarisInceptor:ShakenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisInceptor:Victory#0" value="Envoyez-nous là où on a besoin de nous."/>
    <entry name="SpaceMarines/PrimarisInceptor:VictoryCount" value="1"/>
    <entry name="SpaceMarines/PrimarisIntercessor:AlliedUnit:SpaceMarines/TacticalSpaceMarine#0" value="…anciens Frères…"/>
    <entry name="SpaceMarines/PrimarisIntercessor:AlliedUnit:SpaceMarines/TacticalSpaceMarineCount" value="1"/>
    <entry name="SpaceMarines/PrimarisIntercessor:Attack#0" value="Mitraillez, Frères."/>
    <entry name="SpaceMarines/PrimarisIntercessor:Attack#1" value="Modèle de tir standard."/>
    <entry name="SpaceMarines/PrimarisIntercessor:Attack#2" value="Surveillez !"/>
    <entry name="SpaceMarines/PrimarisIntercessor:Attack#3" value="Surveillez les traînards."/>
    <entry name="SpaceMarines/PrimarisIntercessor:AttackCount" value="4"/>
    <entry name="SpaceMarines/PrimarisIntercessor:Broken#0" value="Battez en retraite en tant qu'unité."/>
    <entry name="SpaceMarines/PrimarisIntercessor:BrokenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisIntercessor:Hurt#0" value="Un frère est tombé. Répétez."/>
    <entry name="SpaceMarines/PrimarisIntercessor:HurtCount" value="1"/>
    <entry name="SpaceMarines/PrimarisIntercessor:Idle#0" value="En son nom."/>
    <entry name="SpaceMarines/PrimarisIntercessor:Idle#1" value="Nous avons tous été des Greyshields."/>
    <entry name="SpaceMarines/PrimarisIntercessor:Idle#2" value="Une preuve contre l'hérésie !"/>
    <entry name="SpaceMarines/PrimarisIntercessor:Idle#3" value="Pour les Primarques !"/>
    <entry name="SpaceMarines/PrimarisIntercessor:IdleCount" value="4"/>
    <entry name="SpaceMarines/PrimarisIntercessor:Shaken#0" value="Tenez bon, mes frères."/>
    <entry name="SpaceMarines/PrimarisIntercessor:ShakenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisIntercessor:Victory#0" value="L'escouade demande de nouvelles cibles."/>
    <entry name="SpaceMarines/PrimarisIntercessor:VictoryCount" value="1"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Attack#0" value="Tirez ! En avant !"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Attack#1" value="Anti-infanterie."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Attack#2" value="Frappez l'ennemi !"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Attack#3" value="Escorte."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:AttackCount" value="4"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Broken#0" value="Désengagement."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:BrokenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Hurt#0" value="Recevons le feu."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:HurtCount" value="1"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Idle#0" value="Vérification du mouvement de la tourelle."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Idle#1" value="Cela nécessitera l'attention d'un Tech-marine."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Idle#2" value="Recherche d'Orks."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Idle#3" value="C'est trop calme."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:IdleCount" value="4"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Shaken#0" value="Continuez à avancer !"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:ShakenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisInvaderATV:Victory#0" value="Ennemi éliminé. Extraction."/>
    <entry name="SpaceMarines/PrimarisInvaderATV:VictoryCount" value="1"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#0" value="Tir des canons principaux."/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#1" value="Exécution !"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#2" value="Xenos, hérétiques… tous tombent !"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#3" value="Par le Codex !"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:AttackCount" value="4"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Broken#0" value="Par l'Empereur !"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:BrokenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Hurt#0" value="Brèche dans la coque ! Continuez à tirer."/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:HurtCount" value="1"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#0" value="Vérification de tous les canons."/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#1" value="Rangement des munitions."/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#2" value="Restauration du blindage."/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#3" value="Un char digne d'un Astartes !"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:IdleCount" value="4"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Shaken#0" value="Impensable !"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:ShakenCount" value="1"/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:Victory#0" value="Inévitable."/>
    <entry name="SpaceMarines/PrimarisRepulsorExecutioner:VictoryCount" value="1"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Attack#0" value="Macro… plasma… incinérateur !"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Attack#1" value="Je suis… Sa colère !"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Attack#2" value="Notre heure… est arrivée !"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Attack#3" value="Affrontez… moi !"/>
    <entry name="SpaceMarines/RedemptorDreadnought:AttackCount" value="4"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Broken#0" value="Debout et combattez… frères !"/>
    <entry name="SpaceMarines/RedemptorDreadnought:BrokenCount" value="1"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Hurt#0" value="Seulement… dans la mort…"/>
    <entry name="SpaceMarines/RedemptorDreadnought:HurtCount" value="1"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Idle#0" value="La rédemption… attend."/>
    <entry name="SpaceMarines/RedemptorDreadnought:Idle#1" value="Il y a si longtemps."/>
    <entry name="SpaceMarines/RedemptorDreadnought:Idle#2" value="Le devoir… ne s'arrête jamais."/>
    <entry name="SpaceMarines/RedemptorDreadnought:Idle#3" value="En avant… vers la bataille."/>
    <entry name="SpaceMarines/RedemptorDreadnought:IdleCount" value="4"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Shaken#0" value="Empereur…"/>
    <entry name="SpaceMarines/RedemptorDreadnought:ShakenCount" value="1"/>
    <entry name="SpaceMarines/RedemptorDreadnought:Victory#0" value="Indomitus… continue."/>
    <entry name="SpaceMarines/RedemptorDreadnought:VictoryCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Attack#0" value="On frappe vite et fort."/>
	<entry name="SpaceMarines/ScoutBiker:AttackCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Broken#0" value="On va à une distance sûre."/>
	<entry name="SpaceMarines/ScoutBiker:BrokenCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Hurt#0" value="Nous n'avons pas le blindage pour ça."/>
	<entry name="SpaceMarines/ScoutBiker:HurtCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Idle#0" value="Un scout oisif est inutile."/>
	<entry name="SpaceMarines/ScoutBiker:IdleCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Shaken#0" value="On doit continuer d'avancer."/>
	<entry name="SpaceMarines/ScoutBiker:ShakenCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Victory#0" value="Une autre victoire grâce à une attaque rapide."/>
	<entry name="SpaceMarines/ScoutBiker:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Attack#0" value="Le support aérien engage l'ennemi."/>
	<entry name="SpaceMarines/StormravenGunship:AttackCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Broken#0" value="Retour à la base."/>
	<entry name="SpaceMarines/StormravenGunship:BrokenCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Hurt#0" value="Du tir DCA arrive."/>
	<entry name="SpaceMarines/StormravenGunship:Hurt#1" value="On n'a plus de moteur !"/>
	<entry name="SpaceMarines/StormravenGunship:HurtCount" value="2"/>
	<entry name="SpaceMarines/StormravenGunship:Idle#0" value="Vos ordres ?"/>
	<entry name="SpaceMarines/StormravenGunship:Idle#1" value="On tourne, on cherche des cibles."/>
	<entry name="SpaceMarines/StormravenGunship:Idle#2" value="Un œil dans le ciel"/>
	<entry name="SpaceMarines/StormravenGunship:IdleCount" value="3"/>
	<entry name="SpaceMarines/StormravenGunship:Shaken#0" value="C'était comme voler à travers une montagne, outch."/>
	<entry name="SpaceMarines/StormravenGunship:ShakenCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Victory#0" value="On ne voit plus rien en-bas."/>
	<entry name="SpaceMarines/StormravenGunship:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormtalonGunship" value="SpaceMarines/StormravenGunship"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#0" value="Attaquer rapidement."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Attack#1" value="Frappe depuis le soleil."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Attack#2" value="Ne laissez pas vivre le xénos ou l'hérétique."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Attack#3" value="Pas de pitié."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:AttackCount" value="4"/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Broken#0" value="Rupture."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:BrokenCount" value="1"/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Hurt#0" value="Mettez-nous hors de portée !"/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:HurtCount" value="1"/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Idle#0" value="Le devoir jusqu'à la mort."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Idle#1" value="L'honneur d'être les premiers au combat."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Idle#2" value="Leurs armures nous craignent."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Idle#3" value="L'oisiveté est une hérésie."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:IdleCount" value="4"/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Shaken#0" value="Quels sont les ordres, commandant ?"/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:ShakenCount" value="1"/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:Victory#0" value="Déjà sur la prochaine cible."/>
    <entry name="SpaceMarines/StormSpeederThunderstrike:VictoryCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Artefact#0" value="Quel est ce vil appareil Xenos ?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:ArtefactCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#0" value="Mange des projectiles !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#1" value="On engage l'ennemi !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#2" value="Pour la gloire de l'Imperium !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#3" value="Je suis votre sauveur !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#4" value="Space Marines, en avant !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#5" value="Goûte à la colère de l'Empereur !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#6" value="Nous sommes le poing de Guilliman."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#7" value="Vous polluez ce monde, déchets !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:AttackCount" value="8"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#0" value="Repli aux positions défensives !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#1" value="On se bat jusqu'au dernier !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#2" value="Préservez le matériel génétique !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#3" value="L'Empereur nous a abandonné !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#4" value="Nous entachons de honte notre Chapitre !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:BrokenCount" value="5"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Cover#0" value="L'Empereur protège ceux qui se protègent."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:CoverCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:AstraMilitarum#0" value="Même en tant qu'alliée, on ne peut pas faire confiance à la Garde."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:Necrons#0" value="D'horribles revenants ? Ha ! Des choses bien plus sombres se terrent dans le cœur des hommes."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:Orks#0" value="Vous osez envahir NOTRE monde natal, vermine verte ?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:SpaceMarines#0" value="Des traîtres, sur notre monde natal ?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/CatachanDevil#0" value="Un diable ! Restez à distance et visez les jambes."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/Enslaver#0" value="Saletés de marionnettistes-où sont les Archivistes ?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KastelanRobot#0" value="Nos propres créations tournées contre nous."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="Le vil Chaos, ici, sur notre planète ? Contactez l'Inquisition !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/Psychneuein#0" value="Space Marines, scellez vos armures pour éviter la contamination."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Hurt#0" value="Nous tiendrons jusqu'au dernier Marine."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Hurt#1" value="Nous… Ne… Tomberons… Pas !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:HurtCount" value="2"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#0" value="Brûle l'hérétique."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#1" value="J'ai soif de combats."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#2" value="Laissez-les venir… Je m'impatiente."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#3" value="Tant de Xenos… Si peu de bolts."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#4" value="Nous sommes le poing de l'Empereur."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#5" value="Nous nous battons, et nous gagnons."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#6" value="Nous ne devons pas toucher à ces artefacts Xenos, mon seigneur."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#7" value="Quels sont vos ordres, mon seigneur ?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:IdleCount" value="8"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory0#0" value="Munitions en faibles quantités… Chaque tir doit compter."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory0Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory1#0" value="L'Archivist a dit qu'on ne peut pas faire confiance à la Garde- nous ferons ce qui doit être fait."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory1Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory2#0" value="Notre monde natal est… Creux ?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory2Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory3#0" value="Étudier les Xenos ? Mon seigneur, est-ce dans le Codex Astartes ?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory3Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory4#0" value="Je ne crains rien, pas même l'Exterminatus."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory4Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory5#0" value="Notre matériel génétique nous survivra- il le doit !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory5Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#0" value="… Béni soit l'esprit trop étroit pour le doute."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#1" value="Nous gagnerons contre l'obscurité."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#2" value="Je suis un bouclier incassable, forgé sur l'obscurité."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#3" value="Je… crois en l'Empereur. Credo ! !"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#4" value="Tant que nos ennemis respirent, nous ne pouvons nous permettre la crainte."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:ShakenCount" value="5"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#0" value="Tous les Xénos et les traîtres sont des plaies qui doivent être purgées de notre galaxie."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#1" value="A vos ordres, Seigneur-Commandeur."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#2" value="Mourir de nos mains n'est pas un déshonneur."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#3" value="Au nom de l'Empereur."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#4" value="Mon poing, serré dans la céramite, frappe avec la force du Chapitre."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#5" value="Ainsi tombent tous les ennemis de l'Imperium."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#6" value="A travers la destruction de nos ennemis, nous gagnons notre salut."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#7" value="Nous sommes l'incarnation de la vengeance de l'Empereur."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:VictoryCount" value="8"/>
	<entry name="SpaceMarines/Terminator" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/Vindicator" value="SpaceMarines/Hunter"/>
    <entry name="Tau/BroadsideBattlesuit" value="Tau/CrisisBattlesuit"/>
	<entry name="Tau/BuilderDrone:Attack#0" value="Ceci est une utilisation inefficace de nos ressources."/>
	<entry name="Tau/BuilderDrone:Attack#1" value="J'effectue une attaque déconseillée."/>
	<entry name="Tau/BuilderDrone:Attack#2" value="J'inflige des dégâts minimaux."/>
	<entry name="Tau/BuilderDrone:AttackCount" value="3"/>
	<entry name="Tau/BuilderDrone:Broken#0" value="Unité en danger."/>
	<entry name="Tau/BuilderDrone:BrokenCount" value="1"/>
	<entry name="Tau/BuilderDrone:Hurt#0" value="Unité compromise."/>
	<entry name="Tau/BuilderDrone:HurtCount" value="1"/>
	<entry name="Tau/BuilderDrone:Idle#0" value="Mise en veille."/>
	<entry name="Tau/BuilderDrone:Idle#1" value="Evaluation des schémas…"/>
	<entry name="Tau/BuilderDrone:Idle#2" value="Je sers la Caste de la Terre et le Bien Suprême."/>
	<entry name="Tau/BuilderDrone:IdleCount" value="3"/>
	<entry name="Tau/BuilderDrone:Shaken#0" value="Unité de construction sous le feu ennemi."/>
	<entry name="Tau/BuilderDrone:ShakenCount" value="1"/>
	<entry name="Tau/BuilderDrone:Victory#0" value="La sur-ingénierie T'au en action."/>
	<entry name="Tau/BuilderDrone:VictoryCount" value="1"/>
	<entry name="Tau/CadreFireblade:Attack#0" value="Feu !"/>
	<entry name="Tau/CadreFireblade:Attack#1" value="Montrez-leur le Bien, Guerriers du Feu."/>
	<entry name="Tau/CadreFireblade:Attack#2" value="Ressentez la puissance de la technologie à impulsion !"/>
	<entry name="Tau/CadreFireblade:Attack#3" value="Attendez de voir le blanc de leurs yeux… à travers votre lunette."/>
	<entry name="Tau/CadreFireblade:AttackCount" value="4"/>
	<entry name="Tau/CadreFireblade:Broken#0" value="Qu'est-ce que Puretide ferait… ?"/>
	<entry name="Tau/CadreFireblade:BrokenCount" value="1"/>
	<entry name="Tau/CadreFireblade:Hurt#0" value="Il en faut plus pour m'abattre !"/>
	<entry name="Tau/CadreFireblade:HurtCount" value="1"/>
	<entry name="Tau/CadreFireblade:Idle#0" value="Entraînement de tir, Shas'El, en rang !"/>
	<entry name="Tau/CadreFireblade:Idle#1" value="Nous menons depuis les premières lignes."/>
	<entry name="Tau/CadreFireblade:Idle#2" value="Un bon meneur garde les pieds sur terre."/>
	<entry name="Tau/CadreFireblade:IdleCount" value="3"/>
	<entry name="Tau/CadreFireblade:Shaken#0" value="Ce n'est pas la voie…"/>
	<entry name="Tau/CadreFireblade:ShakenCount" value="1"/>
	<entry name="Tau/CadreFireblade:Victory#0" value="Tactiquement sage."/>
	<entry name="Tau/CadreFireblade:VictoryCount" value="1"/>
	<entry name="Tau/Commander:Attack#0" value="Pour Aun'Va !"/>
	<entry name="Tau/Commander:Attack#1" value="Nous vous apprendrons la voie."/>
	<entry name="Tau/Commander:Attack#2" value="Rejoignez-nous !"/>
	<entry name="Tau/Commander:AttackCount" value="3"/>
	<entry name="Tau/Commander:Broken#0" value="Battre en retraite est la seule option…"/>
	<entry name="Tau/Commander:BrokenCount" value="1"/>
	<entry name="Tau/Commander:Hurt#0" value="Exo-Armure compromise."/>
	<entry name="Tau/Commander:HurtCount" value="1"/>
	<entry name="Tau/Commander:Idle#0" value="Utilisez-moi, pour le Bien Suprême."/>
	<entry name="Tau/Commander:Idle#1" value="Toujours un coup d'avance."/>
	<entry name="Tau/Commander:Idle#2" value="Qu'est-ce qu'une vie comparée au Bien Suprême ?"/>
	<entry name="Tau/Commander:IdleCount" value="3"/>
	<entry name="Tau/Commander:Shaken#0" value="Je ne faillirai pas."/>
	<entry name="Tau/Commander:ShakenCount" value="1"/>
	<entry name="Tau/Commander:Victory#0" value="La Caste du Feu mène."/>
	<entry name="Tau/Commander:VictoryCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Attack#0" value="Exo-armures en action."/>
	<entry name="Tau/CrisisBattlesuit:Attack#1" value="Nous tirons avec nos armes à impulsion."/>
	<entry name="Tau/CrisisBattlesuit:Attack#2" value="Activation des contre-mesures."/>
	<entry name="Tau/CrisisBattlesuit:Attack#3" value="Vous pouvez compter sur nous !"/>
	<entry name="Tau/CrisisBattlesuit:AttackCount" value="4"/>
	<entry name="Tau/CrisisBattlesuit:Broken#0" value="Retraite… des élites."/>
	<entry name="Tau/CrisisBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Hurt#0" value="Nous perdons des systèmes !"/>
	<entry name="Tau/CrisisBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Idle#0" value="Toujours prêts."/>
	<entry name="Tau/CrisisBattlesuit:Idle#1" value="Nous accomplirons notre tâche."/>
	<entry name="Tau/CrisisBattlesuit:Idle#2" value="Quels sont les ordres des Éthérés ?"/>
	<entry name="Tau/CrisisBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/CrisisBattlesuit:Shaken#0" value="Nous ne pouvons pas tenir cette position."/>
	<entry name="Tau/CrisisBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Victory#0" value="C'est pour ça que vous envoyez les Exo-Armures."/>
	<entry name="Tau/CrisisBattlesuit:Victory#1" value="Coup fatal délivré."/>
	<entry name="Tau/CrisisBattlesuit:Victory#2" value="Est-ce qu'ils ont quelque chose de plus… résistant ?"/>
	<entry name="Tau/CrisisBattlesuit:Victory#3" value="Une autre victoire pour nous, Shas'El"/>
	<entry name="Tau/CrisisBattlesuit:VictoryCount" value="4"/>
	<entry name="Tau/Devilfish:Attack#0" value="On avance vers l'ennemi."/>
	<entry name="Tau/Devilfish:Attack#1" value="On enseigne à nos ennemis à craindre tous les T'au."/>
	<entry name="Tau/Devilfish:Attack#2" value="Ressens ma colère !"/>
	<entry name="Tau/Devilfish:Attack#3" value="Toutes les armes tirent."/>
	<entry name="Tau/Devilfish:AttackCount" value="4"/>
	<entry name="Tau/Devilfish:Broken#0" value="On y est jusqu'au cou !"/>
	<entry name="Tau/Devilfish:BrokenCount" value="1"/>
	<entry name="Tau/Devilfish:Hurt#0" value="On ne peut pas prendre beaucoup plus de tirs comme celui-ci."/>
	<entry name="Tau/Devilfish:HurtCount" value="1"/>
	<entry name="Tau/Devilfish:Idle#0" value="Prêt à bouger sur vos ordres."/>
	<entry name="Tau/Devilfish:Idle#1" value="On attend juste."/>
	<entry name="Tau/Devilfish:Idle#2" value="On vérifie les puces- celle-ci est morte."/>
	<entry name="Tau/Devilfish:IdleCount" value="3"/>
	<entry name="Tau/Devilfish:Shaken#0" value="Ils sont en train d'inverser la tendance !"/>
	<entry name="Tau/Devilfish:ShakenCount" value="1"/>
	<entry name="Tau/Devilfish:Victory#0" value="On les transporte vers un meilleur endroit."/>
	<entry name="Tau/Devilfish:Victory#1" value="On s'en va."/>
	<entry name="Tau/Devilfish:VictoryCount" value="2"/>
	<entry name="Tau/Ethereal:Attack#0" value="C'est pour votre bien."/>
	<entry name="Tau/Ethereal:Attack#1" value="Pour les T'au."/>
	<entry name="Tau/Ethereal:Attack#2" value="Pourquoi vous opposez-vous à nous ?"/>
	<entry name="Tau/Ethereal:AttackCount" value="3"/>
	<entry name="Tau/Ethereal:Broken#0" value="Peut-être que… la paix se trouve derrière nos lignes."/>
	<entry name="Tau/Ethereal:BrokenCount" value="1"/>
	<entry name="Tau/Ethereal:Hurt#0" value="Même si ma chair est faible, mon esprit vaincra !"/>
	<entry name="Tau/Ethereal:HurtCount" value="1"/>
	<entry name="Tau/Ethereal:Idle#0" value="Je prêche le Bien."/>
	<entry name="Tau/Ethereal:Idle#1" value="La compréhension vient avec la méditation."/>
	<entry name="Tau/Ethereal:Idle#2" value="Le simple bien n'est pas suffisant- c'est le Bien Suprême que nous recherchons."/>
	<entry name="Tau/Ethereal:IdleCount" value="3"/>
	<entry name="Tau/Ethereal:Shaken#0" value="Protégez-moi !"/>
	<entry name="Tau/Ethereal:ShakenCount" value="1"/>
	<entry name="Tau/Ethereal:Victory#0" value="On répand la bonne parole."/>
	<entry name="Tau/Ethereal:Victory#1" value="Je lamente chaque âme que l'on perd."/>
	<entry name="Tau/Ethereal:Victory#2" value="Ceci aussi était pour le Bien."/>
	<entry name="Tau/Ethereal:VictoryCount" value="3"/>
	<entry name="Tau/FireWarrior:Attack#0" value="On tire sur nos cibles !"/>
	<entry name="Tau/FireWarrior:Attack#1" value="Au combat !"/>
	<entry name="Tau/FireWarrior:Attack#2" value="Nous sommes la Caste du Feu !"/>
	<entry name="Tau/FireWarrior:Attack#3" value="Restez à distance !"/>
	<entry name="Tau/FireWarrior:Attack#4" value="Soutenez-vous les uns les autres !"/>
	<entry name="Tau/FireWarrior:Attack#5" value="En mémoire de Puretide !"/>
	<entry name="Tau/FireWarrior:AttackCount" value="6"/>
	<entry name="Tau/FireWarrior:Broken#0" value="En arrière ! Vite !"/>
	<entry name="Tau/FireWarrior:BrokenCount" value="1"/>
	<entry name="Tau/FireWarrior:Hurt#0" value="On perd des hommes !"/>
	<entry name="Tau/FireWarrior:HurtCount" value="1"/>
	<entry name="Tau/FireWarrior:Idle#0" value="On fait une pause."/>
	<entry name="Tau/FireWarrior:Idle#1" value="On s'entraîne, Shas'El."/>
	<entry name="Tau/FireWarrior:Idle#2" value="Mon Sept me manque."/>
	<entry name="Tau/FireWarrior:Idle#3" value="Ne parlons plus jamais de l'hyperespace…"/>
	<entry name="Tau/FireWarrior:Idle#4" value="Nous sommes prêts, Shas'El."/>
	<entry name="Tau/FireWarrior:IdleCount" value="5"/>
	<entry name="Tau/FireWarrior:Shaken#0" value="Ne les laissez pas approcher !"/>
	<entry name="Tau/FireWarrior:ShakenCount" value="1"/>
	<entry name="Tau/FireWarrior:Victory#0" value="Notre puissance de feu supérieure gagne une fois de plus !"/>
	<entry name="Tau/FireWarrior:Victory#1" value="Une preuve de la puissance de la Caste du Feu !"/>
	<entry name="Tau/FireWarrior:VictoryCount" value="2"/>
	<entry name="Tau/GhostkeelBattlesuit" value="Tau/CrisisBattlesuit"/>
	<entry name="Tau/GravInhibitorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/GunDrone:Attack#0" value="Solution de tir trouvée."/>
	<entry name="Tau/GunDrone:Attack#1" value="Drone en action."/>
	<entry name="Tau/GunDrone:Attack#2" value="Nous combattons pour le Bien."/>
	<entry name="Tau/GunDrone:Attack#3" value="Limites logiques atteintes. Tirs reçus."/>
	<entry name="Tau/GunDrone:Attack#4" value="Ennemis identifiés avec 89% de certitude. Engagement en cours."/>
	<entry name="Tau/GunDrone:AttackCount" value="5"/>
	<entry name="Tau/GunDrone:Broken#0" value="Préservation de l'intégrité de l'unité."/>
	<entry name="Tau/GunDrone:BrokenCount" value="1"/>
	<entry name="Tau/GunDrone:Hurt#0" value="Capacité d'opération réduite à 50%."/>
	<entry name="Tau/GunDrone:HurtCount" value="1"/>
	<entry name="Tau/GunDrone:Idle#0" value="Patrouille en cours."/>
	<entry name="Tau/GunDrone:Idle#1" value="Le Bien Suprême est logique."/>
	<entry name="Tau/GunDrone:Idle#2" value="Construit par la Caste de la Terre."/>
	<entry name="Tau/GunDrone:Idle#3" value="Sous-routines inactives."/>
	<entry name="Tau/GunDrone:Idle#4" value="Correction des b-bugs."/>
	<entry name="Tau/GunDrone:IdleCount" value="5"/>
	<entry name="Tau/GunDrone:Shaken#0" value="Routines inadéquates. Utilisation des routines de remplacement."/>
	<entry name="Tau/GunDrone:ShakenCount" value="1"/>
	<entry name="Tau/GunDrone:Victory#0" value="Objectif atteint. On continue."/>
	<entry name="Tau/GunDrone:VictoryCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Attack#0" value="Le char tire, les ennemis tombent."/>
	<entry name="Tau/HammerheadGunship:Attack#1" value="On avance prudemment."/>
	<entry name="Tau/HammerheadGunship:Attack#2" value="Tir de couverture."/>
	<entry name="Tau/HammerheadGunship:AttackCount" value="3"/>
	<entry name="Tau/HammerheadGunship:Broken#0" value="Le char se replie."/>
	<entry name="Tau/HammerheadGunship:BrokenCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Hurt#0" value="Ils nous ont endommagé, mais on peut continuer."/>
	<entry name="Tau/HammerheadGunship:HurtCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Idle#0" value="Prêt à tirer."/>
	<entry name="Tau/HammerheadGunship:Idle#1" value="Faites-nous savoir si vous avez besoin de cet oiseau dans le ciel."/>
	<entry name="Tau/HammerheadGunship:Idle#2" value="Le ky'husa ? C'est à quelqu'un d'autre, chef."/>
	<entry name="Tau/HammerheadGunship:IdleCount" value="3"/>
	<entry name="Tau/HammerheadGunship:Shaken#0" value="On ne devrait pas être en première ligne !"/>
	<entry name="Tau/HammerheadGunship:ShakenCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Victory#0" value="Nous sommes les premiers du Mont'ka."/>
	<entry name="Tau/HammerheadGunship:Victory#1" value="Balayage complet."/>
	<entry name="Tau/HammerheadGunship:Victory#2" value="A la recherche de nouvelles cibles."/>
	<entry name="Tau/HammerheadGunship:VictoryCount" value="3"/>
	<entry name="Tau/InterceptorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/MarkerDrone" value="Tau/GunDrone"/>
	<entry name="Tau/Pathfinder" value="Tau/FireWarrior"/>
	<entry name="Tau/Piranha:Attack#0" value="On tire avec l'éclateur."/>
	<entry name="Tau/Piranha:Attack#1" value="Un Cibleur a-t-il besoin de soutien ?"/>
	<entry name="Tau/Piranha:Attack#2" value="On frappe rapidement."/>
	<entry name="Tau/Piranha:Attack#3" value="On débute un passage en mitraillant."/>
	<entry name="Tau/Piranha:AttackCount" value="4"/>
	<entry name="Tau/Piranha:Broken#0" value="Le blindé éclaireur se replie."/>
	<entry name="Tau/Piranha:BrokenCount" value="1"/>
	<entry name="Tau/Piranha:Hurt#0" value="Esquive inefficace, des dégâts sérieux ont été subis."/>
	<entry name="Tau/Piranha:HurtCount" value="1"/>
	<entry name="Tau/Piranha:Idle#0" value="Des nouvelles des Cibleurs ?"/>
	<entry name="Tau/Piranha:Idle#1" value="Équipe de reconnaissance, en attente d'ordres."/>
	<entry name="Tau/Piranha:IdleCount" value="2"/>
	<entry name="Tau/Piranha:Shaken#0" value="Pourquoi est-ce que rien ne marche ? Pourqu-"/>
	<entry name="Tau/Piranha:ShakenCount" value="1"/>
	<entry name="Tau/Piranha:Victory#0" value="Cible… déchiquetée, Shas'El."/>
	<entry name="Tau/Piranha:VictoryCount" value="1"/>
	<entry name="Tau/PulseAcceleratorDrone" value="Tau/GunDrone"/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#0" value="Support aérien en approche."/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#1" value="La Caste de l'Air, pour le Bien Suprême."/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#2" value="Ils ne nous ont pas vu venir…"/>
	<entry name="Tau/RazorsharkStrikeFighter:AttackCount" value="3"/>
	<entry name="Tau/RazorsharkStrikeFighter:Broken#0" value="Repli en cours."/>
	<entry name="Tau/RazorsharkStrikeFighter:BrokenCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Hurt#0" value="Les tirs ennemis sont efficaces. Nous avons besoin de nouveaux ordres !"/>
	<entry name="Tau/RazorsharkStrikeFighter:HurtCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#0" value="On tourne en attendant les ordres."/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#1" value="Devrions-nous retourner à la base ?"/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#2" value="Des nouvelles de cette promotion ?"/>
	<entry name="Tau/RazorsharkStrikeFighter:IdleCount" value="3"/>
	<entry name="Tau/RazorsharkStrikeFighter:Shaken#0" value="Manœuvres d'esquive !"/>
	<entry name="Tau/RazorsharkStrikeFighter:ShakenCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#0" value="Les guetteurs rapportent des ennemis au sol."/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#1" value="Ca c'est de la supériorité aérienne."/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#2" value="Mission accomplie, on s'en va."/>
	<entry name="Tau/RazorsharkStrikeFighter:VictoryCount" value="3"/>
	<entry name="Tau/ReconDrone" value="Tau/GunDrone"/>
	<entry name="Tau/RiptideBattlesuit:Attack#0" value="On tire avec tout ce qu'on a."/>
	<entry name="Tau/RiptideBattlesuit:Attack#1" value="Pourquoi est-ce qu'ils s'embêtent à nous tirer dessus ?"/>
	<entry name="Tau/RiptideBattlesuit:Attack#2" value="Ils ont intérêt à ne pas rayer le Fio'tak."/>
	<entry name="Tau/RiptideBattlesuit:AttackCount" value="3"/>
	<entry name="Tau/RiptideBattlesuit:Broken#0" value="J'ai besoin de la protection d'un Drone de défense."/>
	<entry name="Tau/RiptideBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Hurt#0" value="Réacteur endommagé, blindage compromis. Je peux continuer."/>
	<entry name="Tau/RiptideBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Idle#0" value="Il y a intérêt à ce que vous ayez des plans pour moi."/>
	<entry name="Tau/RiptideBattlesuit:Idle#1" value="ZZ…"/>
	<entry name="Tau/RiptideBattlesuit:Idle#2" value="Tant de puissance de feu…"/>
	<entry name="Tau/RiptideBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/RiptideBattlesuit:Shaken#0" value="Le top du top. Top. Du. Top."/>
	<entry name="Tau/RiptideBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Victory#0" value="Ils sont morts. Continuez à tirer."/>
	<entry name="Tau/RiptideBattlesuit:Victory#1" value="Quelqu'un a vu où ma cible est partie ?"/>
	<entry name="Tau/RiptideBattlesuit:VictoryCount" value="2"/>
	<entry name="Tau/ShieldDrone" value="Tau/GunDrone"/>
	<entry name="Tau/ShieldedMissileDrone" value="Tau/GunDrone"/>
	<entry name="Tau/SkyRayGunship" value="Tau/HammerheadGunship"/>
	<entry name="Tau/StealthBattlesuit:Attack#0" value="Ils ne nous ont pas vu venir."/>
	<entry name="Tau/StealthBattlesuit:Attack#1" value="Depuis les ombres…"/>
	<entry name="Tau/StealthBattlesuit:Attack#2" value="Shas'El, on les a."/>
	<entry name="Tau/StealthBattlesuit:AttackCount" value="3"/>
	<entry name="Tau/StealthBattlesuit:Broken#0" value="Requête d'exfiltration envoyée."/>
	<entry name="Tau/StealthBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Hurt#0" value="Comment arrivent-ils à nous toucher ?"/>
	<entry name="Tau/StealthBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Idle#0" value="On reste à couvert."/>
	<entry name="Tau/StealthBattlesuit:Idle#1" value="Équipe furtive, silence radio."/>
	<entry name="Tau/StealthBattlesuit:Idle#2" value="Vous ne pouvez pas nous parler, nous ne sommes pas là."/>
	<entry name="Tau/StealthBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/StealthBattlesuit:Shaken#0" value="Est-ce qu'on s'est trop enfoncés ?"/>
	<entry name="Tau/StealthBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Victory#0" value="On répand la peur."/>
	<entry name="Tau/StealthBattlesuit:Victory#1" value="Cible éliminée."/>
	<entry name="Tau/StealthBattlesuit:Victory#2" value="On a notre façon de procéder."/>
	<entry name="Tau/StealthBattlesuit:VictoryCount" value="3"/>
	<entry name="Tau/StealthDrone" value="Tau/GunDrone"/>
	<entry name="Tau/Stormsurge" value="Tau/RiptideBattlesuit"/>
	<entry name="Tau/SunSharkBomber" value="Tau/RazorsharkStrikeFighter"/>
	<entry name="Tyranids/Carnifex:Attack#0" value="Krrkkk !"/>
	<entry name="Tyranids/Carnifex:Attack#1" value="Rrrr !"/>
	<entry name="Tyranids/Carnifex:Attack#2" value="Nnnnn !"/>
	<entry name="Tyranids/Carnifex:Attack#3" value="Srrr !"/>
	<entry name="Tyranids/Carnifex:Attack#4" value="Krk !"/>
	<entry name="Tyranids/Carnifex:Attack#5" value="Rrrr !"/>
	<entry name="Tyranids/Carnifex:Attack#6" value="Nrrrrr !"/>
	<entry name="Tyranids/Carnifex:Attack#7" value="Vrrrk !"/>
	<entry name="Tyranids/Carnifex:AttackCount" value="8"/>
	<entry name="Tyranids/Carnifex:Broken#0" value="…mmmm…"/>
	<entry name="Tyranids/Carnifex:Broken#1" value="…thhhh…"/>
	<entry name="Tyranids/Carnifex:Broken#2" value="…phhhh…"/>
	<entry name="Tyranids/Carnifex:Broken#3" value="…sssss…"/>
	<entry name="Tyranids/Carnifex:BrokenCount" value="4"/>
	<entry name="Tyranids/Carnifex:EnemyFaction:Necrons#0" value="Nrnnnnn."/>
	<entry name="Tyranids/Carnifex:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Tyranids/Carnifex:Hurt#0" value="Sssssss !"/>
	<entry name="Tyranids/Carnifex:HurtCount" value="1"/>
	<entry name="Tyranids/Carnifex:Idle#0" value="Krrck."/>
	<entry name="Tyranids/Carnifex:Idle#1" value="Nrrrk."/>
	<entry name="Tyranids/Carnifex:Idle#2" value="Rrrc."/>
	<entry name="Tyranids/Carnifex:Idle#3" value="Kckckc."/>
	<entry name="Tyranids/Carnifex:IdleCount" value="4"/>
	<entry name="Tyranids/Carnifex:Shaken#0" value="Rrhhrrr"/>
	<entry name="Tyranids/Carnifex:ShakenCount" value="1"/>
	<entry name="Tyranids/Carnifex:Victory#0" value="Hwlllllll !"/>
	<entry name="Tyranids/Carnifex:Victory#1" value="Iaiaiaia !"/>
	<entry name="Tyranids/Carnifex:Victory#2" value="Ulululu !"/>
	<entry name="Tyranids/Carnifex:Victory#3" value="Phphph !"/>
	<entry name="Tyranids/Carnifex:Victory#4" value="Ulllllla !"/>
	<entry name="Tyranids/Carnifex:VictoryCount" value="5"/>
	<entry name="Tyranids/Exocrine" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Gargoyle" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Haruspex" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Headquarters" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/HiveCrone" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/HiveTyrant" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Hormagaunt" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Lictor" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Malanthrope" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Ravener" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Termagant" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tervigon" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Trygon" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/TyranidPrime" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tyrannofex" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Warrior" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Zoanthrope" value="Tyranids/Carnifex"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#0" value="Ressens dix mille ans de colère."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#1" value="Pour les Dieux Sombres !"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#2" value="A genoux devant votre seigneur !"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#3" value="Mourez faiblards ! Meurs, vermine !"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#4" value="Pour la gloire du Chaos !"/>
	<entry name="ChaosSpaceMarines/ChaosLord:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Broken#0" value="Je ne mourrai pas ici !"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Broken#1" value="Retour à l'Oeil !"/>
	<entry name="ChaosSpaceMarines/ChaosLord:BrokenCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Hurt#0" value="Quoi, vous m'avez blessé MOI ?"/>
	<entry name="ChaosSpaceMarines/ChaosLord:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#0" value="Pendant que l'on reste immobiles, les Impériaux continuent de vivre."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#1" value="Tu te souviens ? Son sang avait taché tous les murs."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#2" value="Quelle joie de voir un ange tomber."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#3" value="Les cris et les chants ne font qu'un pour moi."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Idle#4" value="La damnation valait-elle le coup ?"/>
	<entry name="ChaosSpaceMarines/ChaosLord:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Shaken#0" value="QUI OSE ?"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Shaken#1" value="Je me vengerai de cet affront."/>
	<entry name="ChaosSpaceMarines/ChaosLord:ShakenCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#0" value="Je dédie cette mort aux dieux."/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#1" value="C'est tout ? Tu frappes comme un vieillard !"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Victory#2" value="La victoire n'est que cendre dans ma bouche tant que l'Imperium continue d'exister."/>
	<entry name="ChaosSpaceMarines/ChaosLord:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#0" value="Quel idiot nous envoie au front ?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#1" value="Écrase leurs os pour en faire de la poussière."/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#2" value="Plus vite !"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Broken#0" value="Fuyons ! Nous les tuerons un autre jour."/>
	<entry name="ChaosSpaceMarines/ChaosRhino:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Hurt#0" value="Qui est en train de trouer notre blindage ?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Idle#0" value="Qui voudrait être un conducteur de Rhino ?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Idle#1" value="On conduit le Seigneur au front, haaaaaaa."/>
	<entry name="ChaosSpaceMarines/ChaosRhino:IdleCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Shaken#0" value="La damnation, pour ça ?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:Victory#0" value="Vous n'êtes même pas capable de battre un Rhino ?"/>
	<entry name="ChaosSpaceMarines/ChaosRhino:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#0" value="Criez ! Votre fin est arrivée !"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#1" value="Ma haine est pure. Venez la tester !"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#2" value="J'étais le fils de l'Empereur, vous ne pouvez pas vous dresser contre moi !"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#3" value="Hahahaha ! MEURS !"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Broken#0" value="Nous sommes faits et refaits, nous devons fuir !"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Hurt#0" value="Les Dieux Sombres nous protègent."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#0" value="Est-ce que je peux tenir dix mille ans de plus comme ça ?"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#1" value="Passe-moi un autre civil. Je n'ai plus d'encre."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#2" value="Pourquoi protègent-ils toujours leur Empereur-cadavre ?"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#3" value="Une guerre infinie, une joie infinie."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Shaken#0" value="Nous sommes… en train de perdre ?"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#0" value="Ainsi tombent tous les ennemis d'Abaddon !"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#1" value="Un autre pas vers la domination de la galaxie… Notre droit de naissance."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#2" value="Votre mort n'est rien pour moi."/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#0" value="…Le vortex s'agrandit…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#1" value="*baragouine*"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#2" value="ahhhhaahhhh"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Broken#0" value="…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Hurt#0" value="…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#0" value="…une éternité de folie.. ?"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#1" value="ehaahahaahaha"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#2" value="mmmm mmmm"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#3" value="…affalé sur le canapé…"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#4" value="nnnhhhrrr."/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Shaken#0" value="eh"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Victory#0" value="ach"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#0" value="Le warp vous touche."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#1" value="Ressens le pouvoir d'un dieu en devenir !"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#2" value="Jouets, marionnettes, tombez !"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#3" value="Ahahahahaha."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Broken#0" value="Impossible, vaincu par-"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Hurt#0" value="TU OSES ME DEFIER ?"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#0" value="… Ces mortels sont tellement stupides !"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#1" value="Le plan physique… Il m'a manqué."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#2" value="Étrange, un monde dont la forme ne change pas selon mes désirs."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#3" value="Quelle étrange menace pend au-dessus de Gladius."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#4" value="Venez, esclaves. Divertissez votre dieu !"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#5" value="C'est bizarre quand je pense que ces serres étaient autrefois des mains de mortel…"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:IdleCount" value="6"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#0" value="J'ai arraché les cœurs d'un millier d'hommes… Et c'est comme ça que vous me remerciez ?"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#1" value="Le warp m'appelle…"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#2" value="…La folie prend son dû-je dois garder le contrôle de mon esprit."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#3" value="J'ai mon propre enfer pour les gens comme vous !"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:ShakenCount" value="4"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#0" value="Un autre crâne pour ma collection… Khorne peut m'en mendier."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#1" value="Des tête énucléées… C'est plaisant."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#2" value="Ne bouge plus, mortel. Je veux apprécier ta mort."/>
	<entry name="ChaosSpaceMarines/DaemonPrince:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#0" value="Oui, oui, mourez mortels, mourez mourez."/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#1" value="Plus près, les griffes, appel, venez !"/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#2" value="Rencontrez nos dieux, allez rencontrer nos dieux."/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#3" value="Pour le Chaos, du sang, des crânes, pour le Chaos."/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#4" value="Déchire et arrache… La mortalité est bonheur."/>
	<entry name="ChaosSpaceMarines/Defiler:Attack#5" value="Dépecez, coupez, écraser, bonheur, déchirer !"/>
	<entry name="ChaosSpaceMarines/Defiler:AttackCount" value="6"/>
	<entry name="ChaosSpaceMarines/Defiler:Broken#0" value="Par les Dieux Sombres !"/>
	<entry name="ChaosSpaceMarines/Defiler:Broken#1" value="Nous perdons, perdons, hahahaha, nous perdons !"/>
	<entry name="ChaosSpaceMarines/Defiler:BrokenCount" value="2"/>
	<entry name="ChaosSpaceMarines/Defiler:Hurt#0" value="Oui, renvoyez-nous au warp, oui, libérez-nous."/>
	<entry name="ChaosSpaceMarines/Defiler:Hurt#1" value="Pulvérisez ma cage, démon libre. Je…"/>
	<entry name="ChaosSpaceMarines/Defiler:HurtCount" value="2"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#0" value="Warp, où, le warp n'est plus là !"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#1" value="Dois pas tuer, lié par les runes, sorcelleries. Je ne dois pas… Je veux."/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#2" value="Pourquoi ne pas tuer, tuer, tuer, où tuer ?"/>
	<entry name="ChaosSpaceMarines/Defiler:Idle#3" value="Puanteur des Anciens ici. Puanteur des dieux morts, pue."/>
	<entry name="ChaosSpaceMarines/Defiler:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/Defiler:Shaken#0" value="Haha, le warp m'appelle, liiiiiiiibre."/>
	<entry name="ChaosSpaceMarines/Defiler:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#0" value="Casse si facilement, rompt, triste, plus ?"/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#1" value="Mort si vite."/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#2" value="Au warp, pour la gloire des maîtres."/>
	<entry name="ChaosSpaceMarines/Defiler:Victory#3" value="Petite âme. Forge chaude, brûlante qui attend. Pour toi."/>
	<entry name="ChaosSpaceMarines/Defiler:VictoryCount" value="4"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#0" value="Annihilation !"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#1" value="RETOURNEZ LE FEU, COUARDS !"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#2" value="Un bolt, deux bolts, trois bolts, quatre-"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#3" value="Pour la gloire du Chaos ! Pour le Mechanicum Sombre ! Pour le plaisir de tirer !"/>
	<entry name="ChaosSpaceMarines/Havoc:Attack#4" value="Nous tirons depuis l'enfer !"/>
	<entry name="ChaosSpaceMarines/Havoc:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/Havoc:Broken#0" value="Retour de flamme…"/>
	<entry name="ChaosSpaceMarines/Havoc:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Hurt#0" value="Pas encore mort, continuez de tirer."/>
	<entry name="ChaosSpaceMarines/Havoc:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#0" value="Quand tu parles avec une arme, les gens écoutent. Quand tu parles à une arme, les gens s'enfuient."/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#1" value="Mon sang est du plasma !"/>
	<entry name="ChaosSpaceMarines/Havoc:Idle#2" value="Béni soit le Warp qui nous donne tant."/>
	<entry name="ChaosSpaceMarines/Havoc:IdleCount" value="3"/>
	<entry name="ChaosSpaceMarines/Havoc:Shaken#0" value="Nos armes, pas les votes. De nos doigts rigides et froids…"/>
	<entry name="ChaosSpaceMarines/Havoc:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Havoc:Victory#0" value="Abattu."/>
	<entry name="ChaosSpaceMarines/Havoc:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#0" value="HhhrrraaaaaHHHH !"/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#1" value="Graaarh."/>
	<entry name="ChaosSpaceMarines/Heldrake:Attack#2" value="Arrrrrhhhh."/>
	<entry name="ChaosSpaceMarines/Heldrake:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/Heldrake:Broken#0" value="Eeeeeeeeeee !"/>
	<entry name="ChaosSpaceMarines/Heldrake:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Hurt#0" value="Arkh ! Arkh !"/>
	<entry name="ChaosSpaceMarines/Heldrake:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#0" value="Humain… un jour."/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#1" value="Grrrrhhh."/>
	<entry name="ChaosSpaceMarines/Heldrake:Idle#2" value="Rrrrrrr…"/>
	<entry name="ChaosSpaceMarines/Heldrake:IdleCount" value="3"/>
	<entry name="ChaosSpaceMarines/Heldrake:Shaken#0" value="Eeeeeekh."/>
	<entry name="ChaosSpaceMarines/Heldrake:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Victory#0" value="HhhrrrrrraaaaaaaHHHHHH !"/>
	<entry name="ChaosSpaceMarines/Heldrake:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#0" value="Du sang pour le dieu du sang !"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#1" value="Kohrne veut ton crâne."/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#2" value="Ma hache veut goûter ton sang !"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#3" value="Venez vous battre, couard !"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#4" value="Il n'y a pas d'honneur dans une mort de guerrier"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:AttackCount" value="5"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Broken#0" value="Khorne va nous tuer !"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Hurt#0" value="Mon… mon sang pour le dieu du SANG !"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Idle#0" value="POURQUOI NE SE BAT-ON PAS ?"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:IdleCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Shaken#0" value="On ne mourra pas si facilement."/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#0" value="DU SANG !"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#1" value="KHORNE T'APPELLE !"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#2" value="TOUS SE TRANSFORMENT EN CRANE !"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:VictoryCount" value="3"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#0" value="Par ton vrai nom, je t'asservis."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#1" value="Pourquoi votre crainte ? Une faille est proche."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#2" value="Mon bâton a faim, votre âme persiste."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Broken#0" value="Qu'est-ce qui vit mais ne meurt pas ?"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Hurt#0" value="Est-ce un de mes os ? Bon sang, je ne le vois pas."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#0" value="Des hommes et des fantômes inutiles… Nous avons besoin d'hôtes plus utiles."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#1" value="Bientôt l'athamé, et après du Fatewalker le nom confirmé…"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#2" value="La possession est neuf dixièmes des connaissances…"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#3" value="Ils goûteront au cœur de la planète, avant que notre heure n'arrive."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Shaken#0" value="Qu'est-ce que la peur qui tue l'esprit ?"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Victory#0" value="Mes démons murmurent entre eux. Ils te murmureront leurs paroles."/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Venomcrawler" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Attack#0" value="…proie…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:AttackCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Broken#0" value="…peur…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Hurt#0" value="…douleur…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Idle#0" value="…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:IdleCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Shaken#0" value="…peur…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Victory#0" value="…mort…"/>
	<entry name="ChaosSpaceMarines/WarpTalon:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#0" value="Diagnostique de la cible : vous… n'êtes pas parfait."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#1" value="Cogitateurs, mécavrilles, armes: déployés."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Attack#2" value="GOTO élimination de la cible. Exécuter."/>
	<entry name="ChaosSpaceMarines/Warpsmith:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Broken#0" value="Initiation du programme de limitation des dégâts."/>
	<entry name="ChaosSpaceMarines/Warpsmith:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Hurt#0" value="Diagnostique: ERREUR organique/méchanique."/>
	<entry name="ChaosSpaceMarines/Warpsmith:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#0" value="Les Maîtres de Forge sont ridicules. Ridicules !"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#1" value="L'homme, la machine et le démon. Ensembles. Perfection."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#2" value="Chères Mécavrilles."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#3" value="Erreur: récupération du noyau émotionnel. Suppression."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#4" value="Forge des âmes, dieux, nous sommes vos armes."/>
	<entry name="ChaosSpaceMarines/Warpsmith:IdleCount" value="5"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Shaken#0" value="Rapports: dégâts subis."/>
	<entry name="ChaosSpaceMarines/Warpsmith:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Warpsmith:Victory#0" value="Évaluation : opposition désactivée. Prévisible."/>
	<entry name="ChaosSpaceMarines/Warpsmith:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Helbrute" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/Maulerfiend" value="ChaosSpaceMarines/Defiler"/>
	<entry name="ChaosSpaceMarines/Obliterator" value="ChaosSpaceMarines/Havoc"/>
 	<entry name="ChaosSpaceMarines/Headquarters:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Headquarters:Victory#0" value="Ainsi meurent tous ceux qui nous font face."/>
 	<entry name="ChaosSpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#0" value="Khrrrrne !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#1" value="Rrrrr !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#2" value="Wrrrrr-"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#3" value="K-k-k-k !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:AttackCount" value="4"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Broken#0" value="Sklzzz-"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:BrokenCount" value="1"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Hurt#0" value="Ztt ! Rrrr-"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:HurtCount" value="1"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#0" value="Crck."/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#1" value="Bldddâ€¦"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#2" value="Mhnnn."/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#3" value="Mrrrr."/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:IdleCount" value="4"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Shaken#0" value="Hnh !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:ShakenCount" value="1"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Victory#0" value="RRRRRRR !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#0" value="Ainsi tombent tous les ennemis de l'Imperium."/>
	<entry name="AstraMilitarum/TempestusScion:Attack#1" value="Hérétiques, xenos : Tous tomberont."/>
	<entry name="AstraMilitarum/TempestusScion:Attack#2" value="Fusils Radiants prêts à tirer…"/>
	<entry name="AstraMilitarum/TempestusScion:Attack#3" value="Nos vies pour Terra !"/>
	<entry name="AstraMilitarum/TempestusScion:AttackCount" value="4"/>
	<entry name="AstraMilitarum/TempestusScion:Broken#0" value="Quelle honte ! Les troupes de choc se replient !"/>
	<entry name="AstraMilitarum/TempestusScion:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Hurt#0" value="Je donne volontiers ma vie pour l'Empereur."/>
	<entry name="AstraMilitarum/TempestusScion:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Idle#0" value="Ils nous appellent les 'starlettes'. La seule gloire que l'on cherche est celle de l'Empereur."/>
	<entry name="AstraMilitarum/TempestusScion:Idle#1" value="Le Standard Régimentaire ? Ce n'est pas une obligation mais une joie de le lire."/>
	<entry name="AstraMilitarum/TempestusScion:Idle#2" value="Vous avez lu cet article à propos de Marbo ? Comment un héros peut-il ignorer les règles du Munitorum ?"/>
	<entry name="AstraMilitarum/TempestusScion:Idle#3" value="Dans le mess, pour se reposer. C'est ça la vraie vie."/>
	<entry name="AstraMilitarum/TempestusScion:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TempestusScion:Shaken#0" value="J'ai passé les épreuves d'obéissance : Je ne m'enfuirai pas."/>
	<entry name="AstraMilitarum/TempestusScion:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Victory#0" value="Laissez la Garde faire la fête. Notre guerre est sans fin."/>
	<entry name="AstraMilitarum/TempestusScion:VictoryCount" value="1"/>

	<entry name="Necrons/FlayedOne:Attack#0" value="…chair"/>
	<entry name="Necrons/FlayedOne:Attack#1" value="-déchirer-"/>
	<entry name="Necrons/FlayedOne:Attack#2" value="…découper-écorcher-déchirer…"/>
	<entry name="Necrons/FlayedOne:AttackCount" value="3"/>
	<entry name="Necrons/FlayedOne:Broken#0" value="…ééééchapper…"/>
	<entry name="Necrons/FlayedOne:BrokenCount" value="1"/>
	<entry name="Necrons/FlayedOne:Hurt#0" value="…égratignure…"/>
	<entry name="Necrons/FlayedOne:HurtCount" value="1"/>
	<entry name="Necrons/FlayedOne:Idle#0" value="…faim…"/>
	<entry name="Necrons/FlayedOne:Idle#1" value="…rester…"/>
	<entry name="Necrons/FlayedOne:Idle#2" value="…langueur…"/>
	<entry name="Necrons/FlayedOne:IdleCount" value="3"/>
	<entry name="Necrons/FlayedOne:Shaken#0" value="…Llandu'gor… !"/>
	<entry name="Necrons/FlayedOne:ShakenCount" value="1"/>
	<entry name="Necrons/FlayedOne:Victory#0" value="…vivre de nouveau…"/>
	<entry name="Necrons/FlayedOne:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Attack#0" value="On tire pour soutenir l'infanterie."/>
	<entry name="AstraMilitarum/Chimera:Attack#1" value="Le transport attaque."/>
	<entry name="AstraMilitarum/Chimera:Attack#2" value="On tire avec tout ce qu'on a."/>
	<entry name="AstraMilitarum/Chimera:AttackCount" value="3"/>
	<entry name="AstraMilitarum/Chimera:Broken#0" value="Le blindé rapide bat rapidement en retraite…"/>
	<entry name="AstraMilitarum/Chimera:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Hurt#0" value="Blindé lourd requis !"/>
	<entry name="AstraMilitarum/Chimera:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Idle#0" value="On attend les ordres."/>
	<entry name="AstraMilitarum/Chimera:Idle#1" value="Quelqu'un à transporter ?"/>
	<entry name="AstraMilitarum/Chimera:Idle#2" value="C'est à qui de polir la lunette du multilaser ?"/>
	<entry name="AstraMilitarum/Chimera:IdleCount" value="3"/>
	<entry name="AstraMilitarum/Chimera:Shaken#0" value="Dégagez ce gros truc d'ici !"/>
	<entry name="AstraMilitarum/Chimera:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Victory#0" value="On est surpris."/>
	<entry name="AstraMilitarum/Chimera:VictoryCount" value="1"/>
	<entry name="Orks/Warbiker:Attack#0" value="C'KI EST ROUGE VA PLU'VITE !"/>
	<entry name="Orks/Warbiker:Attack#1" value="Dakka dakka dakka !"/>
	<entry name="Orks/Warbiker:Attack#2" value="Abattez-lé tous !"/>
	<entry name="Orks/Warbiker:AttackCount" value="3"/>
	<entry name="Orks/Warbiker:Broken#0" value="plu'vite plu'vite plu'vite plu'vite…"/>
	<entry name="Orks/Warbiker:BrokenCount" value="1"/>
	<entry name="Orks/Warbiker:Hurt#0" value="Eh, Baz, c'était ta roue ou la mienne ?"/>
	<entry name="Orks/Warbiker:Hurt#1" value="On n'a pas b'soin d'ces trucs de tout'"/>
	<entry name="Orks/Warbiker:HurtCount" value="2"/>
	<entry name="Orks/Warbiker:Idle#0" value="Donnez-nous queq'chose à faire, bordel."/>
	<entry name="Orks/Warbiker:Idle#1" value="Vroum, vroum, dakka, dakka. C'était l'bon temps.."/>
	<entry name="Orks/Warbiker:IdleCount" value="2"/>
	<entry name="Orks/Warbiker:Shaken#0" value="B'soin de peinture bleue, vite !"/>
	<entry name="Orks/Warbiker:ShakenCount" value="1"/>
	<entry name="Orks/Warbiker:Victory#0" value="WAAAGH ! Kulte d'la vitesse ! WAAAGH !"/>
	<entry name="Orks/Warbiker:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#0" value="Ils cherchent leur mort à nos mains."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#1" value="Écrasez leurs crânes sous no chenilles !"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#2" value="Ils cherchent leur mort à nos mains."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Broken#0" value="Les Dieux Sombres vont nous maudire pour ça…"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Hurt#0" value="Nous ne mourrons pas ici, minables !"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Idle#0" value="Il se tient plutôt bien pour avoir passé dix mille sans sans être utilisé.."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Idle#1" value="Dieux sombres protégez-nous."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:IdleCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Shaken#0" value="Sauvez-vous…"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Victory#0" value="Ils sont morts. Prévisible."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:VictoryCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Attack#0" value="Le canon Rail tire."/>
	<entry name="Tau/TidewallGunrig:Attack#1" value="Emplacement activé."/>
	<entry name="Tau/TidewallGunrig:Attack#2" value="Stabilisée et prête à tirer."/>
	<entry name="Tau/TidewallGunrig:AttackCount" value="3"/>
	<entry name="Tau/TidewallGunrig:Broken#0" value="La gunrig recule."/>
	<entry name="Tau/TidewallGunrig:BrokenCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Hurt#0" value="Le shieldwall ne fonctionne plus, emplacement compromis."/>
	<entry name="Tau/TidewallGunrig:HurtCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Idle#0" value="Vous avez des chars sur lesquels on doit tirer ?"/>
	<entry name="Tau/TidewallGunrig:Idle#1" value="Emplacement mobile prêt à agit."/>
	<entry name="Tau/TidewallGunrig:IdleCount" value="2"/>
	<entry name="Tau/TidewallGunrig:Shaken#0" value="La Tidewall ne peut pas résister à autant de tirs !"/>
	<entry name="Tau/TidewallGunrig:ShakenCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Victory#0" value="Cible… atomisée."/>
	<entry name="Tau/TidewallGunrig:VictoryCount" value="1"/>
	<entry name="Tyranids/ScythedHierodule" value="Tyranids/Carnifex"/>
	<entry name="SpaceMarines/Razorback" value="SpaceMarines/Hunter"/>
	<entry name="Neutral/Umbra:Attack#0" value="…eeee…"/>
	<entry name="Neutral/Umbra:Attack#1" value="(erre)"/>
	<entry name="Neutral/Umbra:Attack#2" value="Errer…"/>
	<entry name="Neutral/Umbra:Attack#3" value="…rrer…"/>
	<entry name="Neutral/Umbra:AttackCount" value="4"/>
	<entry name="Neutral/Umbra:Broken#0" value="Errer…"/>
	<entry name="Neutral/Umbra:BrokenCount" value="1"/>
	<entry name="Neutral/Umbra:Hurt#0" value="ER-"/>
	<entry name="Neutral/Umbra:HurtCount" value="1"/>
	<entry name="Neutral/Umbra:Idle#0" value="…"/>
	<entry name="Neutral/Umbra:Idle#1" value="…errer"/>
	<entry name="Neutral/Umbra:Idle#2" value="errer…"/>
	<entry name="Neutral/Umbra:Idle#3" value="…rr…"/>
	<entry name="Neutral/Umbra:IdleCount" value="4"/>
	<entry name="Neutral/Umbra:Shaken#0" value="er…rer…"/>
	<entry name="Neutral/Umbra:ShakenCount" value="1"/>
	<entry name="Neutral/Umbra:Victory#0" value="ERRER"/>
	<entry name="Neutral/Umbra:VictoryCount" value="1"/>
	<entry name="Eldar/Wraithblade:Attack#0" value="…l'ennemi tombera…"/>
	<entry name="Eldar/Wraithblade:Attack#1" value="…joignez les morts…"/>
	<entry name="Eldar/Wraithblade:Attack#2" value="…pour notre Vaisseau-monde…"/>
	<entry name="Eldar/Wraithblade:Attack#3" value="…pour les Aeldari…"/>
	<entry name="Eldar/Wraithblade:AttackCount" value="4"/>
	<entry name="Eldar/Wraithblade:Broken#0" value="…nous craignons l'Assoiffée…"/>
	<entry name="Eldar/Wraithblade:BrokenCount" value="1"/>
	<entry name="Eldar/Wraithblade:Hurt#0" value="…mourir, encore ?"/>
	<entry name="Eldar/Wraithblade:HurtCount" value="1"/>
	<entry name="Eldar/Wraithblade:Idle#0" value="…avons besoin d'ordres…"/>
	<entry name="Eldar/Wraithblade:Idle#1" value="…nous sommes morts…"/>
	<entry name="Eldar/Wraithblade:Idle#2" value="…laissez-nous reposer…"/>
	<entry name="Eldar/Wraithblade:Idle#3" value="…nous vivons pour la guerre…"/>
	<entry name="Eldar/Wraithblade:IdleCount" value="4"/>
	<entry name="Eldar/Wraithblade:Shaken#0" value="…nous sommes au-delà de la peur…"/>
	<entry name="Eldar/Wraithblade:ShakenCount" value="1"/>
	<entry name="Eldar/Wraithblade:Victory#0" value="…Ils nous ont rejoint…"/>
	<entry name="Eldar/Wraithblade:VictoryCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Attack#0" value="Nous tirons avec tous les systèmes"/>
	<entry name="Eldar/WaveSerpent:Attack#1" value="Oui, nous attaquons"/>
	<entry name="Eldar/WaveSerpent:Attack#2" value="Devons-nous utiliser le Bouclier-Serpent de manière offensive ?"/>
	<entry name="Eldar/WaveSerpent:Attack#3" value="Transport au combat."/>
	<entry name="Eldar/WaveSerpent:AttackCount" value="4"/>
	<entry name="Eldar/WaveSerpent:Broken#0" value="Sauvez-vous !"/>
	<entry name="Eldar/WaveSerpent:BrokenCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Hurt#0" value="Bouclier pénétré."/>
	<entry name="Eldar/WaveSerpent:HurtCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Idle#0" value="Prêt à bouger."/>
	<entry name="Eldar/WaveSerpent:Idle#1" value="Replié, prêt à bondir."/>
	<entry name="Eldar/WaveSerpent:Idle#2" value="Qui a besoin d'un transport ?"/>
	<entry name="Eldar/WaveSerpent:Idle#3" value="Quel monde merveilleux."/>
	<entry name="Eldar/WaveSerpent:IdleCount" value="4"/>
	<entry name="Eldar/WaveSerpent:Shaken#0" value="Sortez-nous de là."/>
	<entry name="Eldar/WaveSerpent:ShakenCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Victory#0" value="On recharge pour la prochaine cible."/>
	<entry name="Eldar/WaveSerpent:VictoryCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#0" value="La batterie gardienne tire."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#1" value="Nous tissons les ombres."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#2" value="Artillerie prête à bouger."/>
	<entry name="Eldar/VaulsWrathSupportBattery:AttackCount" value="3"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Broken#0" value="On ne peut pas tenir ici seul."/>
	<entry name="Eldar/VaulsWrathSupportBattery:BrokenCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Hurt#0" value="Nous ne survivrons pas."/>
	<entry name="Eldar/VaulsWrathSupportBattery:HurtCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#0" value="Mise en bobine des monofilaments."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#1" value="Vérification des générateurs anti-gravitiques."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#2" value="Allons soutenir depuis l'arrière"/>
	<entry name="Eldar/VaulsWrathSupportBattery:IdleCount" value="3"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Shaken#0" value="Nous devons nous désengager !"/>
	<entry name="Eldar/VaulsWrathSupportBattery:ShakenCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Victory#0" value="Nous avons… dissout… la formation ennemie."/>
	<entry name="Eldar/VaulsWrathSupportBattery:VictoryCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Attack#0" value="Nous frappons plus vite que l’œil ne peut voir."/>
	<entry name="Eldar/ShiningSpear:Attack#1" value="Pour Drastanta !"/>
	<entry name="Eldar/ShiningSpear:Attack#2" value="Ils font pâle figure face à notre vertu."/>
	<entry name="Eldar/ShiningSpear:Attack#3" value="Nous sommes la lance de Khaine !"/>
	<entry name="Eldar/ShiningSpear:AttackCount" value="4"/>
	<entry name="Eldar/ShiningSpear:Broken#0" value="La lance est… cassée."/>
	<entry name="Eldar/ShiningSpear:BrokenCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Hurt#0" value="Nous devons aller plus vite."/>
	<entry name="Eldar/ShiningSpear:HurtCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Idle#0" value="Nous sommes une légende incarnée."/>
	<entry name="Eldar/ShiningSpear:Idle#1" value="Nous attendons de pouvoir charger."/>
	<entry name="Eldar/ShiningSpear:Idle#2" value="Lances prêtes."/>
	<entry name="Eldar/ShiningSpear:Idle#3" value="J'aimerais enlever ce masque de guerre."/>
	<entry name="Eldar/ShiningSpear:IdleCount" value="4"/>
	<entry name="Eldar/ShiningSpear:Shaken#0" value="Nous avançons dans la vallée de la peur."/>
	<entry name="Eldar/ShiningSpear:ShakenCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Victory#0" value="Ils ne nous ont pas vu arriver."/>
	<entry name="Eldar/ShiningSpear:VictoryCount" value="1"/>
	<entry name="Eldar/Ranger:Attack#0" value="Choix des cibles…"/>
	<entry name="Eldar/Ranger:Attack#1" value="Ils mourront avant qu'ils sachent que nous avons tiré."/>
	<entry name="Eldar/Ranger:Attack#2" value="Le son de mon fusil long est le murmure de la mort."/>
	<entry name="Eldar/Ranger:Attack#3" value="Nous. Ne. Manquons. Pas."/>
	<entry name="Eldar/Ranger:AttackCount" value="4"/>
	<entry name="Eldar/Ranger:Broken#0" value="En arrière ! Ils arrivent !"/>
	<entry name="Eldar/Ranger:BrokenCount" value="1"/>
	<entry name="Eldar/Ranger:Hurt#0" value="Nous marchons dans le noir…"/>
	<entry name="Eldar/Ranger:HurtCount" value="1"/>
	<entry name="Eldar/Ranger:Idle#0" value="Notre exil est fini."/>
	<entry name="Eldar/Ranger:Idle#1" value="Notre caméléoline nous protège."/>
	<entry name="Eldar/Ranger:Idle#2" value="C'est bon d'être dans un nouvel endroit."/>
	<entry name="Eldar/Ranger:Idle#3" value="Venez, écoutez nos histoires de la galaxie !"/>
	<entry name="Eldar/Ranger:IdleCount" value="4"/>
	<entry name="Eldar/Ranger:Shaken#0" value="Loin du front, vite."/>
	<entry name="Eldar/Ranger:ShakenCount" value="1"/>
	<entry name="Eldar/Ranger:Victory#0" value="Ils sont partis, et nous sommes libres de voyager de nouveau."/>
	<entry name="Eldar/Ranger:VictoryCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Attack#0" value="Nos cris annoncent votre mort."/>
	<entry name="Eldar/HowlingBanshee:Attack#1" value="Ils sont fauchés comme de l'herbe face à nos lames."/>
	<entry name="Eldar/HowlingBanshee:Attack#2" value="Nous appelons leur jugement dernier."/>
	<entry name="Eldar/HowlingBanshee:Attack#3" value="Rapidement !"/>
	<entry name="Eldar/HowlingBanshee:AttackCount" value="4"/>
	<entry name="Eldar/HowlingBanshee:Broken#0" value="Entendez nos lamentations !"/>
	<entry name="Eldar/HowlingBanshee:BrokenCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Hurt#0" value="La vieille piqûre familière !"/>
	<entry name="Eldar/HowlingBanshee:HurtCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Idle#0" value="Nous hurlons leur fin."/>
	<entry name="Eldar/HowlingBanshee:Idle#1" value="Je voudrais que nos ennemis viennent essayer nos lames, tout de suite !"/>
	<entry name="Eldar/HowlingBanshee:Idle#2" value="Nous sommes les enfants de Morai-Heg."/>
	<entry name="Eldar/HowlingBanshee:Idle#3" value="Notre chant est la mort."/>
	<entry name="Eldar/HowlingBanshee:IdleCount" value="4"/>
	<entry name="Eldar/HowlingBanshee:Shaken#0" value="Ils interrompent notre chanson !"/>
	<entry name="Eldar/HowlingBanshee:ShakenCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Victory#0" value="Et seuls des murmures accueillent leur fin."/>
	<entry name="Eldar/HowlingBanshee:VictoryCount" value="1"/>
	<entry name="Eldar/Headquarters:Attack#0" value="On protège la Toile."/>
	<entry name="Eldar/Headquarters:Attack#1" value="Vous ne passerez pas !"/>
	<entry name="Eldar/Headquarters:Attack#2" value="La forteresse tire."/>
	<entry name="Eldar/Headquarters:Attack#3" value="Gardiens de la Toile"/>
	<entry name="Eldar/Headquarters:AttackCount" value="4"/>
	<entry name="Eldar/Headquarters:Broken#0" value="Ils ont fait une brèche !"/>
	<entry name="Eldar/Headquarters:BrokenCount" value="1"/>
	<entry name="Eldar/Headquarters:Hurt#0" value="Khaine aide-nous !"/>
	<entry name="Eldar/Headquarters:HurtCount" value="1"/>
	<entry name="Eldar/Headquarters:Idle#0" value="On surveille la Toile."/>
	<entry name="Eldar/Headquarters:Idle#1" value="Nous sommes prêts, Prophète."/>
	<entry name="Eldar/Headquarters:Idle#2" value="Je suis impatient de retourner sur mon Vaisseau-monde, pour retirer ce masque."/>
	<entry name="Eldar/Headquarters:Idle#3" value="Pourquoi sommes-nous là ?"/>
	<entry name="Eldar/Headquarters:IdleCount" value="4"/>
	<entry name="Eldar/Headquarters:Shaken#0" value="Je crains la mort et la Grande Ennemie."/>
	<entry name="Eldar/Headquarters:ShakenCount" value="1"/>
	<entry name="Eldar/Headquarters:Victory#0" value="Ce portail ne se fermera pas, pas encore."/>
	<entry name="Eldar/Headquarters:VictoryCount" value="1"/>
	<entry name="Eldar/Guardian:Attack#0" value="Le devoir nous appelle."/>
	<entry name="Eldar/Guardian:Attack#1" value="Nous ne pouvons pas tomber."/>
	<entry name="Eldar/Guardian:Attack#2" value="Notre peuple se bat uni."/>
	<entry name="Eldar/Guardian:Attack#3" value="On tire les shurikens."/>
	<entry name="Eldar/Guardian:AttackCount" value="4"/>
	<entry name="Eldar/Guardian:Broken#0" value="Est-ce la fin… ?"/>
	<entry name="Eldar/Guardian:BrokenCount" value="1"/>
	<entry name="Eldar/Guardian:Hurt#0" value="Notre fin est sur nous."/>
	<entry name="Eldar/Guardian:HurtCount" value="1"/>
	<entry name="Eldar/Guardian:Idle#0" value="La guerre est… intense."/>
	<entry name="Eldar/Guardian:Idle#1" value="Quand retournerons-nous dans les halls de cristal ?"/>
	<entry name="Eldar/Guardian:Idle#2" value="Asuryan, protège-nous."/>
	<entry name="Eldar/Guardian:Idle#3" value="Prophète, j'espère que votre vision est vraie."/>
	<entry name="Eldar/Guardian:IdleCount" value="4"/>
	<entry name="Eldar/Guardian:Shaken#0" value="Isha préserve-nous !"/>
	<entry name="Eldar/Guardian:ShakenCount" value="1"/>
	<entry name="Eldar/Guardian:Victory#0" value="Quel arrogance, de se dresser face aux Aeldari."/>
	<entry name="Eldar/Guardian:VictoryCount" value="1"/>
	<entry name="Eldar/FirePrism:Attack#0" value="Nous les découperons."/>
	<entry name="Eldar/FirePrism:Attack#1" value="Ils ne peuvent pas résister à nos tirs."/>
	<entry name="Eldar/FirePrism:Attack#2" value="Ils fondent."/>
	<entry name="Eldar/FirePrism:Attack#3" value="Armes des anciens, ne nous faillissez pas."/>
	<entry name="Eldar/FirePrism:AttackCount" value="4"/>
	<entry name="Eldar/FirePrism:Broken#0" value="…c'est impossible."/>
	<entry name="Eldar/FirePrism:BrokenCount" value="1"/>
	<entry name="Eldar/FirePrism:Hurt#0" value="Ces dégâts sont sans conséquence."/>
	<entry name="Eldar/FirePrism:HurtCount" value="1"/>
	<entry name="Eldar/FirePrism:Idle#0" value="Utilisez-nous."/>
	<entry name="Eldar/FirePrism:Idle#1" value="Vaul l'ancien a conçu cette machine."/>
	<entry name="Eldar/FirePrism:Idle#2" value="Pourquoi devons-nous utiliser un tel armement ?"/>
	<entry name="Eldar/FirePrism:Idle#3" value="Les Vaisseaux-mondes sont si loin."/>
	<entry name="Eldar/FirePrism:IdleCount" value="4"/>
	<entry name="Eldar/FirePrism:Shaken#0" value="On peut résister à leurs tirs."/>
	<entry name="Eldar/FirePrism:ShakenCount" value="1"/>
	<entry name="Eldar/FirePrism:Victory#0" value="Comme prévu, ils sont vaincus."/>
	<entry name="Eldar/FirePrism:VictoryCount" value="1"/>
	<entry name="Eldar/FireDragon:Attack#0" value="Brûle !"/>
	<entry name="Eldar/FireDragon:Attack#1" value="Flammes ancestrales relâchées !"/>
	<entry name="Eldar/FireDragon:Attack#2" value="Nous somme la colère, une colère brûlante !"/>
	<entry name="Eldar/FireDragon:Attack#3" value="Approchez-vous, petits chars…"/>
	<entry name="Eldar/FireDragon:AttackCount" value="4"/>
	<entry name="Eldar/FireDragon:Broken#0" value="Le rêve de feu s'efface…"/>
	<entry name="Eldar/FireDragon:BrokenCount" value="1"/>
	<entry name="Eldar/FireDragon:Hurt#0" value="Nous sommes nous-mêmes brûlés !"/>
	<entry name="Eldar/FireDragon:HurtCount" value="1"/>
	<entry name="Eldar/FireDragon:Idle#0" value="Laissez-nous raconter une histoire de tempêtes de feu et de cendre."/>
	<entry name="Eldar/FireDragon:Idle#1" value="Cendres et colère."/>
	<entry name="Eldar/FireDragon:Idle#2" value="Un monde recouvert de flammes."/>
	<entry name="Eldar/FireDragon:Idle#3" value="Il y a une certaine noblesse dans l'incinération, n'est-ce pas ?"/>
	<entry name="Eldar/FireDragon:IdleCount" value="4"/>
	<entry name="Eldar/FireDragon:Shaken#0" value="Nous sommes éteints."/>
	<entry name="Eldar/FireDragon:ShakenCount" value="1"/>
	<entry name="Eldar/FireDragon:Victory#0" value="Dans la mort, leur teint est… cendreux."/>
	<entry name="Eldar/FireDragon:VictoryCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Attack#0" value="Kurnous guide-nous !"/>
	<entry name="Eldar/CrimsonHunter:Attack#1" value="Nous chassons !"/>
	<entry name="Eldar/CrimsonHunter:Attack#2" value="Depuis les airs, nous distribuons… des leçons."/>
	<entry name="Eldar/CrimsonHunter:Attack#3" value="Nous sommes les lames aveuglantes !"/>
	<entry name="Eldar/CrimsonHunter:AttackCount" value="4"/>
	<entry name="Eldar/CrimsonHunter:Broken#0" value="Ce n'est pas une retraite."/>
	<entry name="Eldar/CrimsonHunter:BrokenCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Hurt#0" value="Ils… Ils nous ont touché ?"/>
	<entry name="Eldar/CrimsonHunter:HurtCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Idle#0" value="J'ai un désir… pour la vitesse."/>
	<entry name="Eldar/CrimsonHunter:Idle#1" value="Kurnous veut de l'action."/>
	<entry name="Eldar/CrimsonHunter:Idle#2" value="Cet environnement est riche en cibles…"/>
	<entry name="Eldar/CrimsonHunter:Idle#3" value="Rien n'est plus mortel que l'arrogance au combat."/>
	<entry name="Eldar/CrimsonHunter:IdleCount" value="4"/>
	<entry name="Eldar/CrimsonHunter:Shaken#0" value="Pas le temps de réfléchir !"/>
	<entry name="Eldar/CrimsonHunter:ShakenCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Victory#0" value="Leur ego était plus fort qu'eux."/>
	<entry name="Eldar/CrimsonHunter:VictoryCount" value="1"/>
	<entry name="Eldar/HemlockWraithfighter" value="Eldar/CrimsonHunter"/>
	<entry name="Eldar/Scorpion" value="Eldar/FirePrism"/>
	<entry name="Eldar/WarWalker" value="Eldar/Guardian"/>
	<entry name="Eldar/Wraithknight" value="Eldar/Wraithblade"/>
	<entry name="Eldar/Autarch:Attack#0" value="Affrontez tous les aspects !"/>
	<entry name="Eldar/Autarch:Attack#1" value="Je connais Khaine !"/>
	<entry name="Eldar/Autarch:Attack#2" value="Tombe par ma main !"/>
	<entry name="Eldar/Autarch:Attack#3" value="Je suis la guerre incarnée !"/>
	<entry name="Eldar/Autarch:AttackCount" value="4"/>
	<entry name="Eldar/Autarch:Broken#0" value="Battre en retraite fait partie de la guerre."/>
	<entry name="Eldar/Autarch:BrokenCount" value="1"/>
	<entry name="Eldar/Autarch:Hurt#0" value="Je… vais peut-être avoir besoin d'une autre incarnation."/>
	<entry name="Eldar/Autarch:HurtCount" value="1"/>
	<entry name="Eldar/Autarch:Idle#0" value="Cegorach donne du travail aux Autarques oisifs."/>
	<entry name="Eldar/Autarch:Idle#1" value="Je vais maintenant réciter la litanie de combat…"/>
	<entry name="Eldar/Autarch:Idle#2" value="Je suis perdu dans la guerre."/>
	<entry name="Eldar/Autarch:Idle#3" value="Il n'y a pas de paix dans mon esprit."/>
	<entry name="Eldar/Autarch:IdleCount" value="4"/>
	<entry name="Eldar/Autarch:Shaken#0" value="Ha ! Ils me testent !"/>
	<entry name="Eldar/Autarch:ShakenCount" value="1"/>
	<entry name="Eldar/Autarch:Victory#0" value="Ils ne me provoqueront plus."/>
	<entry name="Eldar/Autarch:VictoryCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Attack#0" value="Je façonne votre future… ."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#1" value="Avec moi, Lances étincelantes."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#2" value="Ma lame sorcière a faim."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#3" value="Je suis le guide de mon peuple."/>
	<entry name="Eldar/FarseerSkyrunner:AttackCount" value="4"/>
	<entry name="Eldar/FarseerSkyrunner:Broken#0" value="Je ne vois pas de futur."/>
	<entry name="Eldar/FarseerSkyrunner:BrokenCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Hurt#0" value="Je dois continuer !"/>
	<entry name="Eldar/FarseerSkyrunner:HurtCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Idle#0" value="Les écheveaux se resserrent."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#1" value="Le futur est toujours en mouvement."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#2" value="Je vois le futur et le façonne aussi."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#3" value="J'aspire au cristal et au circuit."/>
	<entry name="Eldar/FarseerSkyrunner:IdleCount" value="4"/>
	<entry name="Eldar/FarseerSkyrunner:Shaken#0" value="Ca ne devait pas se passer comme ça !"/>
	<entry name="Eldar/FarseerSkyrunner:ShakenCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Victory#0" value="Vaincu. Comme prédit."/>
	<entry name="Eldar/FarseerSkyrunner:VictoryCount" value="1"/>
	<entry name="Eldar/Spiritseer:Attack#0" value="Il n'y a pas de paix."/>
	<entry name="Eldar/Spiritseer:Attack#1" value="Les morts vous prennent !"/>
	<entry name="Eldar/Spiritseer:Attack#2" value="Craignez ma lame."/>
	<entry name="Eldar/Spiritseer:Attack#3" value="Je suis d'une race ancienne !"/>
	<entry name="Eldar/Spiritseer:AttackCount" value="4"/>
	<entry name="Eldar/Spiritseer:Broken#0" value="Je me replie, sans quoi je rejoindrai mes ouailles."/>
	<entry name="Eldar/Spiritseer:BrokenCount" value="1"/>
	<entry name="Eldar/Spiritseer:Hurt#0" value="Ma vie s'échappe vers le cristal…"/>
	<entry name="Eldar/Spiritseer:HurtCount" value="1"/>
	<entry name="Eldar/Spiritseer:Idle#0" value="Je prends soin des morts."/>
	<entry name="Eldar/Spiritseer:Idle#1" value="Je vous parle, mes amis défunts."/>
	<entry name="Eldar/Spiritseer:Idle#2" value="Les halls des esprits sont silencieux."/>
	<entry name="Eldar/Spiritseer:Idle#3" value="Ecoutez le circuit d'infinité de la planète. Il murmure !"/>
	<entry name="Eldar/Spiritseer:IdleCount" value="4"/>
	<entry name="Eldar/Spiritseer:Shaken#0" value="Nous sommes si peu nombreux à être encore vivant."/>
	<entry name="Eldar/Spiritseer:ShakenCount" value="1"/>
	<entry name="Eldar/Spiritseer:Victory#0" value="Je ne peux pas célébrer la mort, même celle d'un ennemi."/>
	<entry name="Eldar/Spiritseer:VictoryCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#0" value="TU OSES AFFRONTER LA GUERRE INCARNEE ? !"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#1" value="TON DESTIN EST L'IMMOLATION."/>
	<entry name="Eldar/AvatarOfKhaine:Attack#2" value="FEU !"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#3" value="VENEZ, IMBECILES, KHAINE VOUS VEUT"/>
	<entry name="Eldar/AvatarOfKhaine:AttackCount" value="4"/>
	<entry name="Eldar/AvatarOfKhaine:Broken#0" value="JE NE FUIRAI PAS."/>
	<entry name="Eldar/AvatarOfKhaine:BrokenCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Hurt#0" value="VOUS NE FAITES QU'ALIMENTER MA COLERE !"/>
	<entry name="Eldar/AvatarOfKhaine:HurtCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#0" value="CETTE PLANETE ME DECHIRE."/>
	<entry name="Eldar/AvatarOfKhaine:Idle#1" value="CETTE MAIN MANIE L'EPEE."/>
	<entry name="Eldar/AvatarOfKhaine:Idle#2" value="VAUL… EST MORT"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#3" value="BRISE, JE CONTINUE LE COMBAT."/>
	<entry name="Eldar/AvatarOfKhaine:IdleCount" value="4"/>
	<entry name="Eldar/AvatarOfKhaine:Shaken#0" value="APPROCHEZ, MORTELS."/>
	<entry name="Eldar/AvatarOfKhaine:ShakenCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Victory#0" value="ILS APPARTIENNENT A KHAINE DESORMAIS."/>
	<entry name="Eldar/AvatarOfKhaine:VictoryCount" value="1"/>
	<entry name="Eldar/DarkReaper:Attack#0" value="Apprécier l'art de la destruction."/>
    <entry name="Eldar/DarkReaper:Attack#1" value="Apporter la mort."/>
    <entry name="Eldar/DarkReaper:Attack#2" value="Au nom de Khaine !"/>
    <entry name="Eldar/DarkReaper:Attack#3" value="Nous ne manquons pas."/>
    <entry name="Eldar/DarkReaper:AttackCount" value="4"/>
    <entry name="Eldar/DarkReaper:Broken#0" value="C'est nous qui serons détruits."/>
    <entry name="Eldar/DarkReaper:BrokenCount" value="1"/>
    <entry name="Eldar/DarkReaper:Hurt#0" value="La destruction nous guette tous."/>
    <entry name="Eldar/DarkReaper:HurtCount" value="1"/>
    <entry name="Eldar/DarkReaper:Idle#0" value="Nous nous souvenons d'Altansar."/>
    <entry name="Eldar/DarkReaper:Idle#1" value="La destruction n'est qu'un autre chemin dans notre longue vie."/>
    <entry name="Eldar/DarkReaper:Idle#2" value="La mort sans peur est notre lot."/>
    <entry name="Eldar/DarkReaper:Idle#3" value="Le destructeur a pris le dieu Smith il y a longtemps."/>
    <entry name="Eldar/DarkReaper:IdleCount" value="4"/>
    <entry name="Eldar/DarkReaper:Shaken#0" value="Le Destructeur s'est retourné contre nous !"/>
    <entry name="Eldar/DarkReaper:ShakenCount" value="1"/>
    <entry name="Eldar/DarkReaper:Victory#0" value="Le Destructeur les a pris."/>
    <entry name="Eldar/DarkReaper:VictoryCount" value="1"/>
	<entry name="Eldar/Vyper:Attack#0" value="Par Kurnous !" />
    <entry name="Eldar/Vyper:Attack#1" value="Frère, là-bas !"/>
    <entry name="Eldar/Vyper:Attack#2" value="Frappe vite !"/>
    <entry name="Eldar/Vyper:Attack#3" value="Le chasseur frappe."/>
    <entry name="Eldar/Vyper:AttackCount" value="4"/>
    <entry name="Eldar/Vyper:Broken#0" value="Loin, mon frère, loin !" />
    <entry name="Eldar/Vyper:BrokenCount" value="1" />
    <entry name="Eldar/Vyper:Hurt#0" value="Plus près de nos ancêtres." />
    <entry name="Eldar/Vyper:HurtCount" value="1" />
    <entry name="Eldar/Vyper:Idle#0" value="Je ressens ta douleur, mon frère. Ça passera."/>
    <entry name="Eldar/Vyper:Idle#1" value="Toujours prêt à passer à l'action."/>
    <entry name="Eldar/Vyper:Idle#2" value="Je m'entraîne aux manœuvres."/>
    <entry name="Eldar/Vyper:Idle#3" value="Ah, comme je rêve des flèches de wraithbone du Craftworld !"/>
    <entry name="Eldar/Vyper:IdleCount" value="4"/>
    <entry name="Eldar/Vyper:Shaken#0" value="Trop près, mon frère, trop près."/>
    <entry name="Eldar/Vyper:ShakenCount" value="1"/>
    <entry name="Eldar/Vyper:Victory#0" value="L'âge l'emporte."/>
    <entry name="Eldar/Vyper:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Attack#0" value="Guide notre tir, Ô Empereur."/>
	<entry name="AstraMilitarum/Ratling:Attack#1" value="On tire de loin."/>
	<entry name="AstraMilitarum/Ratling:Attack#2" value="Si on peut le voir, on peut le toucher."/>
	<entry name="AstraMilitarum/Ratling:Attack#3" value="Comme abattre un éléphant dans un couloir."/>
	<entry name="AstraMilitarum/Ratling:AttackCount" value="4"/>
	<entry name="AstraMilitarum/Ratling:Broken#0" value="On se casse d'ici !"/>
	<entry name="AstraMilitarum/Ratling:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Hurt#0" value="Laissez-nous quitter le front !"/>
	<entry name="AstraMilitarum/Ratling:HurtCount" value="1"/>
 	<entry name="AstraMilitarum/Ratling:Idle#0" value="Y a du rab d'Amasec ? Avec plaisir."/>
	<entry name="AstraMilitarum/Ratling:Idle#1" value="Y a pas mal à manger sur un Grox, vous savez ?"/>
	<entry name="AstraMilitarum/Ratling:Idle#2" value="Ça ? Je l'ai pas pris ! Le dites pas au Commissaire ! ceci ?"/>
	<entry name="AstraMilitarum/Ratling:Idle#3" value="Paye ce que tu me dois, et dis rien au sergent."/>
	<entry name="AstraMilitarum/Ratling:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Ratling:Shaken#0" value="Protégez-nous et on vous protégera."/>
	<entry name="AstraMilitarum/Ratling:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Victory#0" value="En plein dans l’œil ! Je pense que ça mérite des rations en plus."/>
	<entry name="AstraMilitarum/Ratling:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#0" value="Pour les Dieux Sombres !"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#1" value="Gloire aux quatre"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#2" value="Nous avons vendu nos âmes !"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#3" value="De la violence pour le seigneur de la violence."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Broken#0" value="Seigneurs, protégez-nous !"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Hurt#0" value="Un sacrifice pour le Chaos Unifié !"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#0" value="Tu te demandes parfois… Non rien en fait."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#1" value="Nurgle, le seigneur des mouches."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#2" value="Pour Tzeentch, le changeur de voies."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#3" value="Pour la gloire du Prince."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Shaken#0" value="Où sont nos dieux ?"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Victory#0" value="Gloire, gloire, gloire !"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#0" value="Sens mon fouet !"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#1" value="L'abysse appelle."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#2" value="Aucun péché ne doit rester inaccompli."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#3" value="Pour la gloire de Lorgar !"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Broken#0" value="Je vois dans le warp, oh…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Hurt#0" value="Une telle extase divine !"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#0" value="Un sacrifice rituel prend du temps."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#1" value="L'humanité est condamnée. Le Chaos est notre sauveur."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#2" value="La vie éternelle via le Chaos !"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#3" value="La vérité de l'univers est à portée de main !"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Shaken#0" value="Encore tant de mystères à découvrir…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Victory#0" value="D'étranges visions m'assaillent…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#0" value="Ils osent se montrer devant nous !"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#1" value="Imbéciles, chair à canon !"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#2" value="Vos os seront bientôt un charnier !"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#3" value="Guerre éternelle !"/>
	<entry name="ChaosSpaceMarines/Headquarters:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/Headquarters:Broken#0" value="Impossible !"/>
	<entry name="ChaosSpaceMarines/Headquarters:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Hurt#0" value="Ils ont pénétré l'enceinte !"/>
	<entry name="ChaosSpaceMarines/Headquarters:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#0" value="On liquide des loyalistes."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#1" value="Des yeux sans crânes."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#2" value="Des crânes sans yeux."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#3" value="Les gens s'inclinent devant nous."/>
	<entry name="ChaosSpaceMarines/Headquarters:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/Headquarters:Shaken#0" value="Nous ne pouvons échouer, sales nécrophiles !"/>
	<entry name="ChaosSpaceMarines/Headquarters:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Victory#0" value="Ainsi meurent tous ceux qui nous affrontent."/>
	<entry name="ChaosSpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="Eldar/Warlock:Attack#0" value="Lance chantante, affrontant la mort."/>
	<entry name="Eldar/Warlock:Attack#1" value="Les flammes de notre furie les purifient."/>
	<entry name="Eldar/Warlock:Attack#2" value="Nos lances chantent."/>
	<entry name="Eldar/Warlock:Attack#3" value="L'esprit est notre meilleure arme."/>
	<entry name="Eldar/Warlock:AttackCount" value="4"/>
	<entry name="Eldar/Warlock:Broken#0" value="Repli, au Vaisseau-Monde."/>
	<entry name="Eldar/Warlock:BrokenCount" value="1"/>
	<entry name="Eldar/Warlock:Hurt#0" value="Notre groupe s'effrite."/>
	<entry name="Eldar/Warlock:HurtCount" value="1"/>
	<entry name="Eldar/Warlock:Idle#0" value="Guerriers et prophètes à la fois."/>
	<entry name="Eldar/Warlock:Idle#1" value="La grande ennemie attend dans le warp."/>
	<entry name="Eldar/Warlock:Idle#2" value="Je brûle d'envie de voir les cristaux éclore.."/>
	<entry name="Eldar/Warlock:Idle#3" value="Les bals masqués du Vaisseau-Monde me manquent."/>
	<entry name="Eldar/Warlock:IdleCount" value="4"/>
	<entry name="Eldar/Warlock:Shaken#0" value="Encouragez-nous, nous faiblissons."/>
	<entry name="Eldar/Warlock:ShakenCount" value="1"/>
	<entry name="Eldar/Warlock:Victory#0" value="Nous avions prédit leur chute."/>
	<entry name="Eldar/Warlock:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekWraith" value="Necrons/CanoptekScarab"/>
	<entry name="Orks/KillBursta" value="Orks/Battlewagon"/>
	<entry name="Orks/KrootoxRider" value="Neutral/KrootHound"/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#0" value="On vise du blindé, loué soit l'Empereur."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#1" value="On concentre les tirs."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#2" value="Les spécialistes du siège sont là."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#3" value="Grosse puissance de feu, petite cote."/>
	<entry name="SpaceMarines/DevastatorCenturion:AttackCount" value="4"/>
	<entry name="SpaceMarines/DevastatorCenturion:Broken#0" value="Les exo-armures tiennent, mais on se replie."/>
	<entry name="SpaceMarines/DevastatorCenturion:BrokenCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Hurt#0" value="Ca a traversé l'armure… Les deux armures."/>
	<entry name="SpaceMarines/DevastatorCenturion:HurtCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#0" value="On avance, lentement."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#1" value="On enlève une des armures."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#2" value="Si seulement nos prédécesseurs avaient eu ces exo-armures pendant l'Hérésie."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#3" value="Préparation de l'armure de siège."/>
	<entry name="SpaceMarines/DevastatorCenturion:IdleCount" value="4"/>
	<entry name="SpaceMarines/DevastatorCenturion:Shaken#0" value="La combinaison subit les tirs ennemis, mais elle tient."/>
	<entry name="SpaceMarines/DevastatorCenturion:ShakenCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Victory#0" value="Ils sont… dévastés."/>
	<entry name="SpaceMarines/DevastatorCenturion:VictoryCount" value="1"/>
	<entry name="Tau/TigerShark" value="RazorsharkStrikeFighter"/>
	<entry name="Tyranids/Venomthrope" value="Tyranids/Carnifex"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#0" value="Shhhhhh."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#1" value="Hurlement: Transsonique."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#2" value="Couvert: Spectre neurostatique."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#3" value="Estropié: Ennemi."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Broken#0" value="Courage: Perdu"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Hurt#0" value="Mourant: Encore"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#0" value="Passif: Passif."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#1" value="Données Requises: Vidéos Relaxantes."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#2" value="Apprentissage: Lingua Technis."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#3" value="Aveu: Adversaire Potentiel.."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Shaken#0" value="Danger: Danger."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Victory#0" value="Fierté: Triomphe."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:VictoryCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#0" value="Wouhou, dakkadakkadakka"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#1" value="Nan, courez pas. J'vais vous forer !"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#2" value="TAPE SUR TOUS LES BOUTONS"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#3" value="WAAAGH LE FLYBOYZ !"/>
	<entry name="Orks/MegatrakkScrapjet:AttackCount" value="4"/>
	<entry name="Orks/MegatrakkScrapjet:Broken#0" value="Enkor crashé ! ? !"/>
	<entry name="Orks/MegatrakkScrapjet:BrokenCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Hurt#0" value="Y zon fé pété lé portes !"/>
	<entry name="Orks/MegatrakkScrapjet:HurtCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#0" value="L'boss a dit qu'j'étais puni d'vol ! J'vé lui montré !"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#1" value="J'voudrais être là-haut, pour qu'j'puisse tirer ici."/>
	<entry name="Orks/MegatrakkScrapjet:Idle#2" value="POURQUOI KON BOUGE PA ? !"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#3" value="Hmm. Fo plus d'bombes."/>
	<entry name="Orks/MegatrakkScrapjet:IdleCount" value="4"/>
	<entry name="Orks/MegatrakkScrapjet:Shaken#0" value="D'l'anti-aérien ? CE TOU C'KE VOUS AVE ?"/>
	<entry name="Orks/MegatrakkScrapjet:ShakenCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Victory#0" value="LE ZORKS SON LE MEYEURS, fét-leur rentrer ça dans l'kran."/>
	<entry name="Orks/MegatrakkScrapjet:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Attack#0" value="Regardez-les fondre."/>
	<entry name="AstraMilitarum/DevilDog:Attack#1" value="Les cendres aux cendres."/>
	<entry name="AstraMilitarum/DevilDog:Attack#2" value="Voilà les Devil Dogs ! Oh yeah !"/>
	<entry name="AstraMilitarum/DevilDog:Attack#3" value="La fusion n'a jamais été aussi bonne."/>
	<entry name="AstraMilitarum/DevilDog:AttackCount" value="4"/>
	<entry name="AstraMilitarum/DevilDog:Broken#0" value="Ce truc va exploser !"/>
	<entry name="AstraMilitarum/DevilDog:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Hurt#0" value="Gardez-les loin des tanks à prométhéum !"/>
	<entry name="AstraMilitarum/DevilDog:HurtCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Idle#0" value="On se chauffe pour faire quelque chose ?"/>
	<entry name="AstraMilitarum/DevilDog:Idle#1" value="Quand est-ce que les Hellhounds arrivent ?"/>
	<entry name="AstraMilitarum/DevilDog:Idle#2" value="Les Bane Wolves… ces gars me terrifient."/>
	<entry name="AstraMilitarum/DevilDog:Idle#3" value="Il fait froid quand les canons ne tirent pas."/>
	<entry name="AstraMilitarum/DevilDog:IdleCount" value="4"/>
	<entry name="AstraMilitarum/DevilDog:Shaken#0" value="Allumez le Vulcanor et tirez-nous hors de portée !"/>
	<entry name="AstraMilitarum/DevilDog:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Victory#0" value="L'air surchauffé… a l'odeur de victoire."/>
	<entry name="AstraMilitarum/DevilDog:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="ChaosSpaceMarines/Defiler"/>
	<entry name="Tyranids/HiveGuard" value="Tyranids/Carnifex"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="AdeptusMechanicus/SkitariiVanguard"/>
	<entry name="SpaceMarines/Scout" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="Necrons/GhostArk" value="Necrons/Monolith"/>
	<entry name="Eldar/Hornet" value="Eldar/FirePrism"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#0" value="Rapport sur le chasseur d'assaut Avenger."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#1" value="Mitrailler l'ennemi, chanoinesse."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#2" value="La mort vient du ciel, Madame."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#3" value="Je suis à court de munitions."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AttackCount" value="4"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Broken#0" value="Manœuvres évasives !"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Hurt#0" value="Ils doivent avoir AA derrière eux."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:HurtCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#0" value="Nous avons le soleil derrière nous."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#1" value="Un détachement inhabituel, madame."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#2" value="Nous ne devons pas nous reposer pendant que le mal agit."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#3" value="Juste heureuse d'être en vie !"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:IdleCount" value="4"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Shaken#0" value="De là-haut… l'horreur."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Victory#0" value="Un autre point pour les as de la marine !"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="C'est… c'est la Sainte ?"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Attack#0" value="Au nom de l'Empereur."/>
	<entry name="SistersOfBattle/BattleSister:Attack#1" value="Le Dieu-Empereur guide mon projectile."/>
	<entry name="SistersOfBattle/BattleSister:Attack#2" value="Déclencher sa colère divine !"/>
	<entry name="SistersOfBattle/BattleSister:Attack#3" value="Ne laissez pas vivre l'hérétique !"/>
	<entry name="SistersOfBattle/BattleSister:AttackCount" value="4"/>
	<entry name="SistersOfBattle/BattleSister:Broken#0" value="Sans la foi, nous ne sommes rien…"/>
	<entry name="SistersOfBattle/BattleSister:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Hurt#0" value="Oui, même si leurs lames coupent, je ne crains rien."/>
	<entry name="SistersOfBattle/BattleSister:HurtCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Idle#0" value="Nous sommes les instruments du salut."/>
	<entry name="SistersOfBattle/BattleSister:Idle#1" value="Mes sœurs, prions."/>
	<entry name="SistersOfBattle/BattleSister:Idle#2" value="Nous devons nous reposer pendant que le mal agit."/>
	<entry name="SistersOfBattle/BattleSister:Idle#3" value="Tant de choses, tant d'hérésie… tant de travail."/>
	<entry name="SistersOfBattle/BattleSister:IdleCount" value="4"/>
	<entry name="SistersOfBattle/BattleSister:Shaken#0" value="In nomine Imperia…"/>
	<entry name="SistersOfBattle/BattleSister:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Victory#0" value="Repose-toi, ennemi."/>
	<entry name="SistersOfBattle/BattleSister:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="La Sainte… ? Où elle va, nous la suivrons !"/>
	<entry name="SistersOfBattle/BattleSister:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Attack#0" value="Donnez-leur la paix de l'Empereur."/>
	<entry name="SistersOfBattle/Canoness:Attack#1" value="Hérétiques !"/>
	<entry name="SistersOfBattle/Canoness:Attack#2" value="Goûtez à ma rédemption."/>
	<entry name="SistersOfBattle/Canoness:Attack#3" value="Sentez ma prière !"/>
	<entry name="SistersOfBattle/Canoness:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Canoness:Broken#0" value="Mes… sœurs…"/>
	<entry name="SistersOfBattle/Canoness:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Hurt#0" value="Je me bats… contre !"/>
	<entry name="SistersOfBattle/Canoness:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Idle#0" value="Le chaos fait travailler les mains oisives."/>
	<entry name="SistersOfBattle/Canoness:Idle#1" value="Nous ne sommes pas sans défaut. Mais nous connaissons notre péché."/>
	<entry name="SistersOfBattle/Canoness:Idle#2" value="La sainte trinité sont mes outils… bolter, lance-flammes, fuseur."/>
	<entry name="SistersOfBattle/Canoness:Idle#3" value="La rédemption vaut tous les prix, mes sœurs."/>
	<entry name="SistersOfBattle/Canoness:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Canoness:Shaken#0" value="Je… je ne peux pas."/>
	<entry name="SistersOfBattle/Canoness:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Victory#0" value="L'absolution est la tienne."/>
	<entry name="SistersOfBattle/Canoness:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Je dirige mais elle… elle inspire."/>
	<entry name="SistersOfBattle/Canoness:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Attack#0" value="…l'Empereur est une puissante forteresse…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#1" value="…surgissent, les Adepta Sororitas…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#2" value="…oh, Empereur, qui êtes notre lumière et notre jour…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#3" value="…Écoutez, le son joyeux de nos armes."/>
	<entry name="SistersOfBattle/Exorcist:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Exorcist:Broken#0" value="…Empereur, ayez pitié."/>
	<entry name="SistersOfBattle/Exorcist:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Hurt#0" value="…des profondeurs du malheur, je crie vers toi…"/>
	<entry name="SistersOfBattle/Exorcist:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Idle#0" value="L'Empereur vit !"/>
	<entry name="SistersOfBattle/Exorcist:Idle#1" value="Gloire à Dieu ! Gloire à Dieu ! Gloire à Dieu !"/>
	<entry name="SistersOfBattle/Exorcist:Idle#2" value="L'Empereur a délié son épée rapide."/>
	<entry name="SistersOfBattle/Exorcist:Idle#3" value="Tremblez, homme, dans la terreur."/>
	<entry name="SistersOfBattle/Exorcist:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Exorcist:Shaken#0" value="Empereur, votre sang et votre droiture nous préserveront."/>
	<entry name="SistersOfBattle/Exorcist:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Victory#0" value="…écraser le serpent et avancer…"/>
	<entry name="SistersOfBattle/Exorcist:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="…contemplez le merveilleux mystère du Saint Vivant !"/>
	<entry name="SistersOfBattle/Exorcist:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#0" value="Approchez, hérétiques."/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#1" value="Ressentez la colère de l'Empereur !"/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#2" value="La foi est notre bouclier."/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#3" value="Nous allons vous envoyer vous reposer."/>
	<entry name="SistersOfBattle/CelestianSacresant:AttackCount" value="4"/>
	<entry name="SistersOfBattle/CelestianSacresant:Broken#0" value="Nous ne pouvons pas l'achever. Nous devons…"/>
	<entry name="SistersOfBattle/CelestianSacresant:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Hurt#0" value="Ils menacent notre quête…"/>
	<entry name="SistersOfBattle/CelestianSacresant:HurtCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#0" value="Nous avons traversé la galaxie pour finir… ici."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#1" value="Notre armure, impénétrable."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#2" value="Notre vertu, incontestée."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#3" value="Nous sommes le modèle auquel tous aspirent."/>
	<entry name="SistersOfBattle/CelestianSacresant:IdleCount" value="4"/>
	<entry name="SistersOfBattle/CelestianSacresant:Shaken#0" value="Pas un pas en arrière."/>
	<entry name="SistersOfBattle/CelestianSacresant:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Victory#0" value="Notre quête est terminée."/>
	<entry name="SistersOfBattle/CelestianSacresant:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Notre homonyme, notre dame ! Notre quête touche à sa fin."/>
	<entry name="SistersOfBattle/CelestianSacresant:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#0" value="Pour l'honneur du Questor Mechanicus !"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#1" value="Nous ne sommes qu'une arme dans la main droite de l'Empereur."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#2" value="Craignez ma charge !"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#3" value="Chevaliers, en avant !"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AttackCount" value="4"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Broken#0" value="Quelle friponnerie… ?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Hurt#0" value="La vitesse sera mon armure."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:HurtCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#0" value="Je rouille, pendant que vous tergiversez."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#1" value="Chanoinesse, utilisez-moi !"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#2" value="Ma lance a besoin d'être utilisée."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#3" value="Les esprits des machines ont faim de gloire !"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:IdleCount" value="4"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Shaken#0" value="Quelle est la gloire dans tout cela ?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Victory#0" value="Ils n'ont pas pu résister à ma fureur."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Une… une sainte vivante ?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:EnemyUnit:AdeptusMechanicus/KnightCrusader#0" value="Un digne ennemi ! Viens, laisse-moi conclure avec toi."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:EnemyUnit:AdeptusMechanicus/KnightCrusaderCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Attack#0" value="Et, oui, l'empereur a vu leur mort et c'était bon."/>
	<entry name="SistersOfBattle/Dialogus:Attack#1" value="Ne vous reposez pas mes sœurs, dites qu'elles ne sont plus !"/>
	<entry name="SistersOfBattle/Dialogus:Attack#2" value="Ne demandez pas à l'hérétique quand un lance-flammes…"/>
	<entry name="SistersOfBattle/Dialogus:Attack#3" value="Je me tiens avec vous, ensemble !"/>
	<entry name="SistersOfBattle/Dialogus:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Dialogus:Broken#0" value="La pénitence est sa propre punition."/>
	<entry name="SistersOfBattle/Dialogus:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Hurt#0" value="Qu'est-ce qu'une blessure de plus pour les fidèles ?"/>
	<entry name="SistersOfBattle/Dialogus:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Idle#0" value="Vox-beads : vérifié. Sensorielle : oui."/>
	<entry name="SistersOfBattle/Dialogus:Idle#1" value="Reposez-vous aujourd'hui, car demain nous serons avec Lui."/>
	<entry name="SistersOfBattle/Dialogus:Idle#2" value="L'Empereur vous aime toutes."/>
	<entry name="SistersOfBattle/Dialogus:Idle#3" value="Ne craignez pas les xénos."/>
	<entry name="SistersOfBattle/Dialogus:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Dialogus:Shaken#0" value="Bien que je marche dans la mort et le labeur…"/>
	<entry name="SistersOfBattle/Dialogus:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Victory#0" value="Couchez-vous pour moi, pour que je puisse marcher plus haut."/>
	<entry name="SistersOfBattle/Dialogus:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Les mots… me manquent. Célestes elles-mêmes !"/>
	<entry name="SistersOfBattle/Dialogus:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Attack#0" value="Sœurs, le chœur fidèle retentit."/>
	<entry name="SistersOfBattle/Headquarters:Attack#1" value="Nos murs sont prêts."/>
	<entry name="SistersOfBattle/Headquarters:Attack#2" value="Nos sanctuaires ne sont pas sans défense."/>
	<entry name="SistersOfBattle/Headquarters:Attack#3" value="Vous vous trompez si vous nous croyez faibles."/>
	<entry name="SistersOfBattle/Headquarters:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Headquarters:Broken#0" value="Nos murs… tombent ?"/>
	<entry name="SistersOfBattle/Headquarters:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Hurt#0" value="Le sanctuaire est violé."/>
	<entry name="SistersOfBattle/Headquarters:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Idle#0" value="Offrir des prières au Dieu-Empereur."/>
	<entry name="SistersOfBattle/Headquarters:Idle#1" value="Purger les hérétiques."/>
	<entry name="SistersOfBattle/Headquarters:Idle#2" value="Disperser les cendres saintes."/>
	<entry name="SistersOfBattle/Headquarters:Idle#3" value="Formation des novices."/>
	<entry name="SistersOfBattle/Headquarters:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Headquarters:Shaken#0" value="Derrière les remparts, vite !"/>
	<entry name="SistersOfBattle/Headquarters:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Victory#0" value="Ne mettez pas à l'épreuve nos murs ou notre foi."/>
	<entry name="SistersOfBattle/Headquarters:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Oh, si seulement elle venait nous voir. Célestes !"/>
	<entry name="SistersOfBattle/Headquarters:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Attack#0" value="Mes outils blessent autant qu'ils guérissent."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#1" value="Nous vous détestons, affronts à la foi."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#2" value="Soyez fortes au nom de l'Empereur."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#3" value="La félicité martiale est notre récompense."/>
	<entry name="SistersOfBattle/Hospitaller:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Hospitaller:Broken#0" value="Je dois battre en retraite, pour préserver les jeunes."/>
	<entry name="SistersOfBattle/Hospitaller:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Hurt#0" value="Médecin… guéris-toi toi-même."/>
	<entry name="SistersOfBattle/Hospitaller:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Idle#0" value="Dans la force, la vie."/>
	<entry name="SistersOfBattle/Hospitaller:Idle#1" value="Nos blessures sont le témoignage de notre bon travail."/>
	<entry name="SistersOfBattle/Hospitaller:Idle#2" value="Où vais-je élever nos sanctuaires ?"/>
	<entry name="SistersOfBattle/Hospitaller:Idle#3" value="La haine n'est pas un péché quand l'univers est si immonde."/>
	<entry name="SistersOfBattle/Hospitaller:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Hospitaller:Shaken#0" value="Mon rôle est de guérir… pourquoi suis-je ici ?"/>
	<entry name="SistersOfBattle/Hospitaller:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Victory#0" value="Ma touche de guérison est un peu en retard pour celle-là."/>
	<entry name="SistersOfBattle/Hospitaller:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Mes blessures… se ressoudent. Comment ?"/>
	<entry name="SistersOfBattle/Hospitaller:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Attack#0" value="laissez-moi sortir."/>
	<entry name="SistersOfBattle/Mortifier:Attack#1" value="Je suis désolé. Je suis désolé. Je suis désolé. Je suis désolé."/>
	<entry name="SistersOfBattle/Mortifier:Attack#2" value="Meursmeursmeurs"/>
	<entry name="SistersOfBattle/Mortifier:Attack#3" value="pourquoi je ne peux pas mourir ?"/>
	<entry name="SistersOfBattle/Mortifier:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Mortifier:Broken#0" value="Brisez-moi plus, je n'ai pas encore compris"/>
	<entry name="SistersOfBattle/Mortifier:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Hurt#0" value="S'il vous plaît, la mort, maintenant, oui."/>
	<entry name="SistersOfBattle/Mortifier:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Idle#0" value="Empereur, libérez-moi."/>
	<entry name="SistersOfBattle/Mortifier:Idle#1" value="Laissez-moi mourir."/>
	<entry name="SistersOfBattle/Mortifier:Idle#2" value="Je suis une pécheresse, mais ça…"/>
	<entry name="SistersOfBattle/Mortifier:Idle#3" value="-Tourment-"/>
	<entry name="SistersOfBattle/Mortifier:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Mortifier:Shaken#0" value="Rapprochez de la mort !"/>
	<entry name="SistersOfBattle/Mortifier:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Victory#0" value="Ils sont libres… pour moi, il n'y a rien."/>
	<entry name="SistersOfBattle/Mortifier:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="S'il vous plaît… s'il vous plaît…"/>
	<entry name="SistersOfBattle/Mortifier:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#0" value="Un jour, l'humanité sera libérée de cela."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#1" value="J'apporte le salut… par l'anéantissement."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#2" value="Je suis l'outil de l'Empereur, pour le guider comme il l'entend."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#3" value="Il connaît mon chemin."/>
	<entry name="SistersOfBattle/SaintCelestine:AttackCount" value="4"/>
	<entry name="SistersOfBattle/SaintCelestine:Broken#0" value="Je ne peux pas faiblir."/>
	<entry name="SistersOfBattle/SaintCelestine:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Hurt#0" value="La mort n'est jamais la fin. Pourquoi la craindre ?"/>
	<entry name="SistersOfBattle/SaintCelestine:HurtCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#0" value="Plutôt l'obscurité que l'illumination de l'impur."/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#1" value="Je vis dans le cœur des Justes."/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#2" value="Ils me suivraient jusqu'à l'Œil de la Terreur…"/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#3" value="Piégée. Ici."/>
	<entry name="SistersOfBattle/SaintCelestine:IdleCount" value="4"/>
	<entry name="SistersOfBattle/SaintCelestine:Shaken#0" value="Je ne crains pas la mort."/>
	<entry name="SistersOfBattle/SaintCelestine:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Victory#0" value="C'est votre fin, pas la mienne."/>
	<entry name="SistersOfBattle/SaintCelestine:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#0" value="Pour la rédemption !"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#1" value="Par nos œuvres, reconnaissez-nous !"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#2" value="L'expiation est une bonne chose !"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#3" value="Nos éviscérators sont comme des moulins à prières."/>
	<entry name="SistersOfBattle/SisterRepentia:AttackCount" value="4"/>
	<entry name="SistersOfBattle/SisterRepentia:Broken#0" value="Ils vont nous mortifier !"/>
	<entry name="SistersOfBattle/SisterRepentia:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Hurt#0" value="Notre foi est notre armure !"/>
	<entry name="SistersOfBattle/SisterRepentia:HurtCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#0" value="Nous avons péché."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#1" value="Lâcheté face à l'ennemi."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#2" value="Seigneur, Empereur, nous implorons votre pardon."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#3" value="Un moment de paix avant la mort."/>
	<entry name="SistersOfBattle/SisterRepentia:IdleCount" value="4"/>
	<entry name="SistersOfBattle/SisterRepentia:Shaken#0" value="Peut-être que nous ne pouvons pas expier…"/>
	<entry name="SistersOfBattle/SisterRepentia:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Victory#0" value="Un autre pas vers la rédemption ou la mort."/>
	<entry name="SistersOfBattle/SisterRepentia:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="La Céleste était autrefois une Repentia… notre phare de rédemption."/>
	<entry name="SistersOfBattle/SisterRepentia:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#0" value="Feu et vol !"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#1" value="Venez, brûlez."/>
	<entry name="SistersOfBattle/Zephyrim:Attack#2" value="L'Empereur est notre guide !"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#3" value="À travers des cieux de flammes, vous êtes à nous."/>
	<entry name="SistersOfBattle/Zephyrim:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Zephyrim:Broken#0" value="Volez, l'Empereur nous protège !"/>
	<entry name="SistersOfBattle/Zephyrim:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Hurt#0" value="Notre sang, nos blessures, nos prières pour Lui !"/>
	<entry name="SistersOfBattle/Zephyrim:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Idle#0" value="Nous sommes les destructrices divines."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#1" value="Chassez les malins, les puissantes."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#2" value="Les langues de flamme disent aussi des prières."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#3" value="Chœur angélique attendant les ordres."/>
	<entry name="SistersOfBattle/Zephyrim:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Zephyrim:Shaken#0" value="Regroupement au milieu des nuages."/>
	<entry name="SistersOfBattle/Zephyrim:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Victory#0" value="Ainsi tous les méchants périssent !"/>
	<entry name="SistersOfBattle/Zephyrim:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Si seulement nous étions de vrais anges comme elle…"/>
	<entry name="SistersOfBattle/Zephyrim:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Dominion" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Immolator" value="SistersOfBattle/Exorcist"/>
	<entry name="SistersOfBattle/Lightning" value="SistersOfBattle/Avenger"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Retributor" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Castigator" value="SistersOfBattle/Exorcist"/>
	
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#0" value="C32 : Schéma de tir réalisé."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#1" value="7T32 : Tous les armements sont déployés."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#2" value="8-6 : Clade de brèche active."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#3" value="P4-78 : Armure/Transport Recherche et destruction."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Broken#0" value="Actifs jetables… humains une fois…"/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Hurt#0" value="Fonctionnalité compromise. Machine existante. Chair 45%, en déclin."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#0" value="Excision de tissus nécrotiques."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#1" value="Serviteur en attente d'utilisation."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#2" value="Mémoire de l'herbe, de nuages, de ciel bleu… Erreur d'effacement de l'esprit. Effacement."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#3" value="Début du chœur binharique."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Shaken#0" value="Sous un feu soutenu."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:Victory#0" value="Éliminé, brutalement. Satisfaisant."/>
 	<entry name="AdeptusMechanicus/KataphronBreacher:VictoryCount" value="1"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Attack#0" value="Feu oppressant !"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Attack#1" value="Au nom du Primarque !"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Attack#2" value="Assaut de blindés !"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Attack#3" value="On tire sur tout !"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:AttackCount" value="4"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Broken#0" value="Il faut préserver le tank…"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:BrokenCount" value="1"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Hurt#0" value="Droit dans la coque"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:HurtCount" value="1"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Idle#0" value="Que ferait Dorn ?"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Idle#1" value="On sert la volonté de l'Empereur."/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Idle#2" value="Armageddon, c'était une bataille de chars…"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Idle#3" value="Moins d'infanterie, plus de blindés."/>
    <entry name="AstraMilitarum/RogalDornBattleTank:IdleCount" value="4"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Shaken#0" value="Ils ont tout à fait atteint One."/>
    <entry name="AstraMilitarum/RogalDornBattleTank:ShakenCount" value="1"/>
    <entry name="AstraMilitarum/RogalDornBattleTank:Victory#0" value="One est toujours à la recherche de nouvelles cibles."/>
    <entry name="AstraMilitarum/RogalDornBattleTank:VictoryCount" value="1"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#0" value="Khrrrrne !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#1" value="Rrrrr !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#2" value="Wrrrrr-"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#3" value="K-k-k-k !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:AttackCount" value="4"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Broken#0" value="Sklzzz-"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:BrokenCount" value="1"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Hurt#0" value="Ztt ! Rrrr-"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:HurtCount" value="1"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#0" value="Crck."/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#1" value="Bldddâ€¦"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#2" value="Mhnnn."/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#3" value="Mrrrr."/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:IdleCount" value="4"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Shaken#0" value="Hnh !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:ShakenCount" value="1"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Victory#0" value="RRRRRRR !"/>
    <entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:VictoryCount" value="1"/>
	<entry name="Drukhari/Archon:Attack#1" value="Idiotie, de s'opposer à moi."/>
    <entry name="Drukhari/Archon:Attack#2" value="Vous ne valez guère la peine d'être tués."/>
    <entry name="Drukhari/Archon:Attack#3" value="Oh, douce peur et agonie."/>
    <entry name="Drukhari/Archon:AttackCount" value="4"/>
    <entry name="Drukhari/Archon:Broken#0" value="Retour, retour à la cité des ombres !"/>
    <entry name="Drukhari/Archon:BrokenCount" value="1"/>
    <entry name="Drukhari/Archon:Hurt#0" value="Un coup chanceux."/>
    <entry name="Drukhari/Archon:HurtCount" value="1"/>
    <entry name="Drukhari/Archon:Idle#0" value="Tourments simples, plaisirs simples."/>
    <entry name="Drukhari/Archon:Idle#1" value="Les avons-nous déjà tous tués ? Dommage."/>
    <entry name="Drukhari/Archon:Idle#2" value="Les richesses de ce monde ne sont que les souffrances de ses habitants."/>
    <entry name="Drukhari/Archon:Idle#3" value="Comment remplir les éons oisifs, ahhh…"/>
    <entry name="Drukhari/Archon:IdleCount" value="4"/>
    <entry name="Drukhari/Archon:Shaken#0" value="Si ça continue, il faudra un Haemonculus pour me réveiller…"/>
    <entry name="Drukhari/Archon:ShakenCount" value="1"/>
    <entry name="Drukhari/Archon:Victory#0" value="A quoi t'attendais-tu ? Pitoyable."/>
    <entry name="Drukhari/Archon:VictoryCount" value="1"/>
    <entry name="Drukhari/Cronos:Attack#0" value="Hrrrr."/>
    <entry name="Drukhari/Cronos:Attack#1" value="…mémoire…"/>
    <entry name="Drukhari/Cronos:Attack#2" value="Cthhh…"/>
    <entry name="Drukhari/Cronos:Attack#3" value="Rrrr…"/>
    <entry name="Drukhari/Cronos:AttackCount" value="4"/>
    <entry name="Drukhari/Cronos:Broken#0" value="…"/>
    <entry name="Drukhari/Cronos:BrokenCount" value="1"/>
    <entry name="Drukhari/Cronos:Hurt#0" value="Hrrr."/>
    <entry name="Drukhari/Cronos:HurtCount" value="1"/>
    <entry name="Drukhari/Cronos:Idle#0" value="Pssssst…"/>
    <entry name="Drukhari/Cronos:Idle#1" value="Thrrrt."/>
    <entry name="Drukhari/Cronos:Idle#2" value="Thffffttt."/>
    <entry name="Drukhari/Cronos:Idle#3" value="Tcht."/>
    <entry name="Drukhari/Cronos:IdleCount" value="4"/>
    <entry name="Drukhari/Cronos:Shaken#0" value="Eeeee !"/>
    <entry name="Drukhari/Cronos:ShakenCount" value="1"/>
    <entry name="Drukhari/Cronos:Victory#0" value="Psst."/>
    <entry name="Drukhari/Cronos:VictoryCount" value="1"/>
    <entry name="Drukhari/Haemonculus:Attack#0" value="L'art de l'agonie !"/>
    <entry name="Drukhari/Haemonculus:Attack#1" value="Souffrir, souffrir, souffrir !"/>
    <entry name="Drukhari/Haemonculus:Attack#2" value="Avec la douleur, je renoue."/>
    <entry name="Drukhari/Haemonculus:Attack#3" value="Laissez-moi jouer avec leurs nerfs."/>
    <entry name="Drukhari/Haemonculus:AttackCount" value="4"/>
    <entry name="Drukhari/Haemonculus:Broken#0" value="Peut-être une autre fois ?"/>
    <entry name="Drukhari/Haemonculus:BrokenCount" value="1"/>
    <entry name="Drukhari/Haemonculus:Hurt#0" value="Ma propre douleur suffira."/>
    <entry name="Drukhari/Haemonculus:HurtCount" value="1"/>
    <entry name="Drukhari/Haemonculus:Idle#0" value="Quelle douce souffrance recueillir ensuite ?"/>
    <entry name="Drukhari/Haemonculus:Idle#1" value="Est-ce que je me souviens de la chute ? Est-ce que je m'en souviens ?"/>
    <entry name="Drukhari/Haemonculus:Idle#2" value="Mes expériences portent leurs fruits."/>
    <entry name="Drukhari/Haemonculus:Idle#3" value="Où sont mes wracks ? ? Je demande de l'attention !"/>
    <entry name="Drukhari/Haemonculus:IdleCount" value="4"/>
    <entry name="Drukhari/Haemonculus:Shaken#0" value="Oh, ils sont puissants ! Je dois les avoir."/>
    <entry name="Drukhari/Haemonculus:ShakenCount" value="1"/>
    <entry name="Drukhari/Haemonculus:Victory#0" value="Maintenant, qu'avons-nous appris ?"/>
    <entry name="Drukhari/Haemonculus:VictoryCount" value="1"/>
    <entry name="Drukhari/Headquarters:Attack#0" value="Ils osent attaquer notre siège du pouvoir ?"/>
    <entry name="Drukhari/Headquarters:Attack#1" value="Folie !"/>
    <entry name="Drukhari/Headquarters:Attack#2" value="Quelle folie de s'attaquer à nos murs."/>
    <entry name="Drukhari/Headquarters:Attack#3" value="Brisez vous sur nos créneaux."/>
    <entry name="Drukhari/Headquarters:AttackCount" value="4"/>
    <entry name="Drukhari/Headquarters:Broken#0" value="Archon, Sauvez votre loy—vos sujets !"/>
    <entry name="Drukhari/Headquarters:BrokenCount" value="1"/>
    <entry name="Drukhari/Headquarters:Hurt#0" value="Brisé. Mais seuls les faibles sont morts."/>
    <entry name="Drukhari/Headquarters:HurtCount" value="1"/>
    <entry name="Drukhari/Headquarters:Idle#0" value="En temps de paix, des parcelles."/>
    <entry name="Drukhari/Headquarters:Idle#1" value="Les jardins de la torture sont occupés ce soir."/>
    <entry name="Drukhari/Headquarters:Idle#2" value="Un éon d'oisiveté…"/>
    <entry name="Drukhari/Headquarters:Idle#3" value="Le luxe dérisoire de cette maison temporaire."/>
    <entry name="Drukhari/Headquarters:IdleCount" value="4"/>
    <entry name="Drukhari/Headquarters:Shaken#0" value="Oh,retourner dans le sombre Commorragh !"/>
    <entry name="Drukhari/Headquarters:ShakenCount" value="1"/>
    <entry name="Drukhari/Headquarters:Victory#0" value="Sécurisé une fois de plus."/>
    <entry name="Drukhari/Headquarters:VictoryCount" value="1"/>
    <entry name="Drukhari/Hellion:Attack#0" value="Le frisson de la mise à mort !"/>
    <entry name="Drukhari/Hellion:Attack#1" value="La mort vue d'en haut !"/>
    <entry name="Drukhari/Hellion:Attack#2" value="On s'élance, on frappe !"/>
    <entry name="Drukhari/Hellion:Attack#3" value="Sensation d'écrasement !"/>
    <entry name="Drukhari/Hellion:AttackCount" value="4"/>
    <entry name="Drukhari/Hellion:Broken#0" value="Loin, avant qu'ils ne nous prennent au piège !"/>
    <entry name="Drukhari/Hellion:BrokenCount" value="1"/>
    <entry name="Drukhari/Hellion:Hurt#0" value="Les lents sont tombés."/>
    <entry name="Drukhari/Hellion:HurtCount" value="1"/>
    <entry name="Drukhari/Hellion:Idle#0" value="Nous n'attendrons pas !"/>
    <entry name="Drukhari/Hellion:Idle#1" value="Le ciel nous appelle ! La foudre nous appelle !"/>
    <entry name="Drukhari/Hellion:Idle#2" value="L'immobilité est un supplice !"/>
    <entry name="Drukhari/Hellion:Idle#3" value="Libère-nous, Archon !"/>
    <entry name="Drukhari/Hellion:IdleCount" value="4"/>
    <entry name="Drukhari/Hellion:Shaken#0" value="Au sol, nous mourrons."/>
    <entry name="Drukhari/Hellion:ShakenCount" value="1"/>
    <entry name="Drukhari/Hellion:Victory#0" value="Plus vite, plus vite ils tombent !"/>
    <entry name="Drukhari/Hellion:VictoryCount" value="1"/>
    <entry name="Drukhari/Incubi:Attack#0" value="Nous ne faisons qu'un avec khaine."/>
    <entry name="Drukhari/Incubi:Attack#1" value="Le coup fatal… juste comme ça."/>
    <entry name="Drukhari/Incubi:Attack#2" value="Nous offrons les faibles à Khaine."/>
    <entry name="Drukhari/Incubi:Attack#3" value="Payé pour tuer."/>
    <entry name="Drukhari/Incubi:AttackCount" value="4"/>
    <entry name="Drukhari/Incubi:Broken#0" value="Khaine ne pardonne pas les lâches."/>
    <entry name="Drukhari/Incubi:BrokenCount" value="1"/>
    <entry name="Drukhari/Incubi:Hurt#0" value="Un vrai adversaire."/>
    <entry name="Drukhari/Incubi:HurtCount" value="1"/>
    <entry name="Drukhari/Incubi:Idle#0" value="Pas de trahison tant que l'alliance n'est pas terminée."/>
    <entry name="Drukhari/Incubi:Idle#1" value="Notre temps est compté… ce serait un gâchis de ne pas nous utiliser."/>
    <entry name="Drukhari/Incubi:Idle#2" value="Il y a une pureté dans la haine."/>
    <entry name="Drukhari/Incubi:Idle#3" value="Suivez Arha et le chemin de la damnation."/>
    <entry name="Drukhari/Incubi:IdleCount" value="4"/>
    <entry name="Drukhari/Incubi:Shaken#0" value="Personne ne marchera sur le chemin du chagrin pour des gens comme nous."/>
    <entry name="Drukhari/Incubi:ShakenCount" value="1"/>
    <entry name="Drukhari/Incubi:Victory#0" value="Hommage à Khaine."/>
    <entry name="Drukhari/Incubi:VictoryCount" value="1"/>
    <entry name="Drukhari/KabaliteTrueborn:Attack#0" value="Biens meubles !"/>
    <entry name="Drukhari/KabaliteTrueborn:Attack#1" value="Imbéciles arrogants !"/>
    <entry name="Drukhari/KabaliteTrueborn:Attack#2" value="S'opposer aux Vrai-né ? Hahaha…"/>
    <entry name="Drukhari/KabaliteTrueborn:Attack#3" value="L'archonte commande…"/>
    <entry name="Drukhari/KabaliteTrueborn:AttackCount" value="4"/>
    <entry name="Drukhari/KabaliteTrueborn:Broken#0" value="Retour, préserve notre précieuse chair !"/>
    <entry name="Drukhari/KabaliteTrueborn:BrokenCount" value="1"/>
    <entry name="Drukhari/KabaliteTrueborn:Hurt#0" value="Seuls les vrais Vrai-né restent debout."/>
    <entry name="Drukhari/KabaliteTrueborn:HurtCount" value="1"/>
    <entry name="Drukhari/KabaliteTrueborn:Idle#0" value="Apportez-nous des vins ! Victimes !"/>
    <entry name="Drukhari/KabaliteTrueborn:Idle#1" value="Sage, de ne pas gaspiller nos précieuses vies."/>
    <entry name="Drukhari/KabaliteTrueborn:Idle#2" value="Nous passerons les millénaires dans une telle lassitude…"/>
    <entry name="Drukhari/KabaliteTrueborn:Idle#3" value="Oh, heureux jours d'indolence !"/>
    <entry name="Drukhari/KabaliteTrueborn:IdleCount" value="4"/>
    <entry name="Drukhari/KabaliteTrueborn:Shaken#0" value="Nous ne sommes pas des vatborn jetables !"/>
    <entry name="Drukhari/KabaliteTrueborn:ShakenCount" value="1"/>
    <entry name="Drukhari/KabaliteTrueborn:Victory#0" value="A quoi s'attendaient-ils ? Nous sommes des Vrai-né."/>
    <entry name="Drukhari/KabaliteTrueborn:VictoryCount" value="1"/>
    <entry name="Drukhari/KabaliteWarrior:Attack#0" value="L'espionnage est à l'ordre du jour."/>
    <entry name="Drukhari/KabaliteWarrior:Attack#1" value="Reculez, mortels."/>
    <entry name="Drukhari/KabaliteWarrior:Attack#2" value="Commorrah exige un tribut !"/>
    <entry name="Drukhari/KabaliteWarrior:Attack#3" value="Pour le Kabal !"/>
    <entry name="Drukhari/KabaliteWarrior:AttackCount" value="4"/>
    <entry name="Drukhari/KabaliteWarrior:Broken#0" value="Si nous fuyons, l'Archonte… pire que la mort."/>
    <entry name="Drukhari/KabaliteWarrior:BrokenCount" value="1"/>
    <entry name="Drukhari/KabaliteWarrior:Hurt#0" value="Ils le regretteront…"/>
    <entry name="Drukhari/KabaliteWarrior:HurtCount" value="1"/>
    <entry name="Drukhari/KabaliteWarrior:Idle#0" value="Nous sommes peut-être des Vatborn, mais nous sommes de vrais Drukhari."/>
    <entry name="Drukhari/KabaliteWarrior:Idle#1" value="Amenez des prisonniers, nous devons pratiquer notre but."/>
    <entry name="Drukhari/KabaliteWarrior:Idle#2" value="Préserver les kabalites, dépenser les mercenaires."/>
    <entry name="Drukhari/KabaliteWarrior:Idle#3" value="Commorragh, notre maison aux multiples angles…"/>
    <entry name="Drukhari/KabaliteWarrior:IdleCount" value="4"/>
    <entry name="Drukhari/KabaliteWarrior:Shaken#0" value="Nous avons peur ? Ils devraient avoir peur !"/>
    <entry name="Drukhari/KabaliteWarrior:ShakenCount" value="1"/>
    <entry name="Drukhari/KabaliteWarrior:Victory#0" value="Imbéciles ! La Kabale n'envoie pas de mauviettes."/>
    <entry name="Drukhari/KabaliteWarrior:VictoryCount" value="1"/>
    <entry name="Drukhari/Mandrake:Attack#0" value="Juste une touche glaciale…"/>
    <entry name="Drukhari/Mandrake:Attack#1" value="Des adversaires dignes…"/>
    <entry name="Drukhari/Mandrake:Attack#2" value="Une proie de choix…"/>
    <entry name="Drukhari/Mandrake:Attack#3" value="… attaque depuis l'ombre."/>
    <entry name="Drukhari/Mandrake:AttackCount" value="4"/>
    <entry name="Drukhari/Mandrake:Broken#0" value="Retour dans les ténèbres…"/>
    <entry name="Drukhari/Mandrake:BrokenCount" value="1"/>
    <entry name="Drukhari/Mandrake:Hurt#0" value="Les faibles tombent…"/>
    <entry name="Drukhari/Mandrake:HurtCount" value="1"/>
    <entry name="Drukhari/Mandrake:Idle#0" value="Kheradruakh attend."/>
    <entry name="Drukhari/Mandrake:Idle#1" value="Le froid brûle tellement…"/>
    <entry name="Drukhari/Mandrake:Idle#2" value="Aelindrach appelle."/>
    <entry name="Drukhari/Mandrake:Idle#3" value="Nourris-toi de la peur de nos frères…"/>
    <entry name="Drukhari/Mandrake:IdleCount" value="4"/>
    <entry name="Drukhari/Mandrake:Shaken#0" value="Ils osent… ?"/>
    <entry name="Drukhari/Mandrake:ShakenCount" value="1"/>
    <entry name="Drukhari/Mandrake:Victory#0" value="Dommage… que reste-t-il comme proie ?" />
    <entry name="Drukhari/Mandrake:VictoryCount" value="1"/>
	<entry name="Drukhari/Raider:Attack#0" value="Écumez bas, douce Keelblade."/>
    <entry name="Drukhari/Raider:Attack#1" value="Douleur passagère…"/>
    <entry name="Drukhari/Raider:Attack#2" value="Nous rapprocher…"/>
    <entry name="Drukhari/Raider:Attack#3" value="Le bourdonnement de la charge !"/>
    <entry name="Drukhari/Raider:AttackCount" value="4"/>
    <entry name="Drukhari/Raider:Broken#0" value="Retraite, avant qu'ils ne se rapprochent de nous !"/>
    <entry name="Drukhari/Raider:BrokenCount" value="1"/>
    <entry name="Drukhari/Raider:Hurt#0" value="La vitesse devrait être notre armure !"/>
    <entry name="Drukhari/Raider:HurtCount" value="1"/>
    <entry name="Drukhari/Raider:Idle#0" value="Nos crochets à trophées sont vides !"/>
    <entry name="Drukhari/Raider:Idle#1" value="Empilez les corps à l'arrière. Seuls les faibles meurent."/>
    <entry name="Drukhari/Raider:Idle#2" value="Aiguiser la Keelblade, les ailerons."/>
    <entry name="Drukhari/Raider:Idle#3" value="Seul Vect se souvient des yachts de plaisance d'autrefois…"/>
    <entry name="Drukhari/Raider:IdleCount" value="4"/>
    <entry name="Drukhari/Raider:Shaken#0" value="Plus vite, sur eux !"/>
    <entry name="Drukhari/Raider:ShakenCount" value="1"/>
    <entry name="Drukhari/Raider:Victory#0" value="Mettez les captifs sur les crochets à trophées. Vivants ou morts."/>
    <entry name="Drukhari/Raider:VictoryCount" value="1"/>
    <entry name="Drukhari/RazorwingJetfighter:Attack#0" value="Ratissez le sol, Archon !"/>
    <entry name="Drukhari/RazorwingJetfighter:Attack#1" value="Notre grâce entraîne le malheur."/>
    <entry name="Drukhari/RazorwingJetfighter:Attack#2" value="Oh, fuyez, proie, fuyez !"/>
    <entry name="Drukhari/RazorwingJetfighter:Attack#3" value="Massacre-les ! Ne laissez personne s'échapper."/>
    <entry name="Drukhari/RazorwingJetfighter:AttackCount" value="4"/>
    <entry name="Drukhari/RazorwingJetfighter:Broken#0" value="Sauvez votre peau !"/>
    <entry name="Drukhari/RazorwingJetfighter:BrokenCount" value="1"/>
    <entry name="Drukhari/RazorwingJetfighter:Hurt#0" value="Je n'ai pas cherché à me battre à la loyale !"/>
    <entry name="Drukhari/RazorwingJetfighter:HurtCount" value="1"/>
    <entry name="Drukhari/RazorwingJetfighter:Idle#0" value="Mieux ici que dans les courses à la mort de l'arène…"/>
    <entry name="Drukhari/RazorwingJetfighter:Idle#1" value="C'est la faute aux Nightwings…"/>
    <entry name="Drukhari/RazorwingJetfighter:Idle#2" value="Nous sommes les meilleurs des Reavers."/>
    <entry name="Drukhari/RazorwingJetfighter:Idle#3" value="La mort à une telle distance. La cruauté est que nous ne pouvons pas voir leur tourment."/>
    <entry name="Drukhari/RazorwingJetfighter:IdleCount" value="4"/>
    <entry name="Drukhari/RazorwingJetfighter:Shaken#0" value="Ils ripostent ?"/>
    <entry name="Drukhari/RazorwingJetfighter:ShakenCount" value="1"/>
    <entry name="Drukhari/RazorwingJetfighter:Victory#0" value="Une belle découpe en biseau. Il ne nous a pas vu arriver."/>
    <entry name="Drukhari/RazorwingJetfighter:VictoryCount" value="1"/>
    <entry name="Drukhari/Scourge:Attack#0" value="Marche sur les mourants, pas sur la terre."/>
    <entry name="Drukhari/Scourge:Attack#1" value="Raptors en piqué…"/>
    <entry name="Drukhari/Scourge:Attack#2" value="Entendez-vous votre mort dans le vent ?"/>
    <entry name="Drukhari/Scourge:Attack#3" value="Arrachez-leur les yeux, peau de vautour !"/>
    <entry name="Drukhari/Scourge:AttackCount" value="4"/>
    <entry name="Drukhari/Scourge:Broken#0" value="Prenez l'air ! Aiee !"/>
    <entry name="Drukhari/Scourge:BrokenCount" value="1"/>
    <entry name="Drukhari/Scourge:Hurt#0" value="Un brave qui tue un Fléau."/>
    <entry name="Drukhari/Scourge:HurtCount" value="1"/>
    <entry name="Drukhari/Scourge:Idle#0" value="Coursiers de la Cité des Ténèbres."/>
    <entry name="Drukhari/Scourge:Idle#1" value="Porteurs d'intrigues."/>
    <entry name="Drukhari/Scourge:Idle#2" value="Notre richesse est dans nos ailes."/>
    <entry name="Drukhari/Scourge:Idle#3" value="Mes ailerons ont besoin d'être étirés."/>
    <entry name="Drukhari/Scourge:IdleCount" value="4"/>
    <entry name="Drukhari/Scourge:Shaken#0" value="Nous devons nous élever plus haut !"/>
    <entry name="Drukhari/Scourge:ShakenCount" value="1"/>
    <entry name="Drukhari/Scourge:Victory#0" value="Écoutez ces cris, ahh…"/>
    <entry name="Drukhari/Scourge:VictoryCount" value="1"/>
    <entry name="Drukhari/Succubus:Attack#0" value="N'êtes-vous pas enfermé ?"/>
    <entry name="Drukhari/Succubus:Attack#1" value="Encore des prétendants ? Eh."/>
    <entry name="Drukhari/Succubus:Attack#2" value="Vous qui êtes sur le point de mourir… je vous salue."/>
    <entry name="Drukhari/Succubus:Attack#3" value="Quel pauvre défi tu es !"/>
    <entry name="Drukhari/Succubus:AttackCount" value="4"/>
    <entry name="Drukhari/Succubus:Broken#0" value="Je ne risquerai pas de tacher mon rouge !"/>
    <entry name="Drukhari/Succubus:BrokenCount" value="1"/>
    <entry name="Drukhari/Succubus:Hurt#0" value="Ils m'ont marqué !"/>
    <entry name="Drukhari/Succubus:HurtCount" value="1"/>
    <entry name="Drukhari/Succubus:Idle#0" value="Encore du courrier de fans. Baille."/>
    <entry name="Drukhari/Succubus:Idle#1" value="Commorragh est la foule. Et je les contrôle."/>
    <entry name="Drukhari/Succubus:Idle#2" value="Dites à l'Archon que j'ai besoin de plus de sucreries."/>
    <entry name="Drukhari/Succubus:Idle#3" value="Ooh ! J'aspire à retourner à la civilisation, à la vraie souffrance."/>
    <entry name="Drukhari/Succubus:Idle#4" value="Des ombres et de la poussière. La peur et l'émerveillement."/>
    <entry name="Drukhari/Succubus:IdleCount" value="5"/>
    <entry name="Drukhari/Succubus:Shaken#0" value="Nous, les épouses de la mort, ne rencontrons pas notre conjoint !"/>
    <entry name="Drukhari/Succubus:ShakenCount" value="1"/>
    <entry name="Drukhari/Succubus:Victory#0" value="J'ai… un talent pour la survie."/>
    <entry name="Drukhari/Succubus:VictoryCount" value="1"/>
    <entry name="Drukhari/Tantalus:Attack#0" value="Tirer sur les désintégrateurs d'impulsions !"/>
    <entry name="Drukhari/Tantalus:Attack#1" value="Voyez comment les ennemis se divisent en vagues ?"/>
    <entry name="Drukhari/Tantalus:Attack#2" value="Serrez les voiles d'aethers ! Poursuivez-les !"/>
    <entry name="Drukhari/Tantalus:Attack#3" value="Tournez et regardez au vent."/>
    <entry name="Drukhari/Tantalus:AttackCount" value="4"/>
    <entry name="Drukhari/Tantalus:Broken#0" value="Préservez le navire !"/>
    <entry name="Drukhari/Tantalus:BrokenCount" value="1"/>
    <entry name="Drukhari/Tantalus:Hurt#0" value="La coque est percée ! Nous coulons…"/>
    <entry name="Drukhari/Tantalus:HurtCount" value="1"/>
    <entry name="Drukhari/Tantalus:Idle#0" value="Aiguiser les faux."/>
    <entry name="Drukhari/Tantalus:Idle#1" value="Attention ! Une égratignure et l'Archonte…"/>
    <entry name="Drukhari/Tantalus:Idle#2" value="Une conception du Kabal du Miroir Noir. Disparu depuis longtemps."/>
    <entry name="Drukhari/Tantalus:Idle#3" value="Un tel plaisir, dans sa navigation."/>
    <entry name="Drukhari/Tantalus:IdleCount" value="4"/>
    <entry name="Drukhari/Tantalus:Shaken#0" value="Le vent est tombé !"/>
    <entry name="Drukhari/Tantalus:ShakenCount" value="1"/>
    <entry name="Drukhari/Tantalus:Victory#0" value="Considérez ceux qui sont tombés, autrefois grands et fiers comme vous."/>
    <entry name="Drukhari/Tantalus:VictoryCount" value="1"/>
    <entry name="Drukhari/VoidravenBomber:Attack#0" value="Les bombes s'envolent. Ne regardez pas la lumière noire !"/>
    <entry name="Drukhari/VoidravenBomber:Attack#1" value="Lance vide en cours de tir. Mine vide prête."/>
    <entry name="Drukhari/VoidravenBomber:Attack#2" value="L'artilleur commande."/>
    <entry name="Drukhari/VoidravenBomber:Attack#3" value="En silence, nous nous élevons."/>
    <entry name="Drukhari/VoidravenBomber:AttackCount" value="4"/>
    <entry name="Drukhari/VoidravenBomber:Broken#0" value="Loin, ils nous tirent vers le bas !"/>
    <entry name="Drukhari/VoidravenBomber:BrokenCount" value="1"/>
    <entry name="Drukhari/VoidravenBomber:Hurt#0" value="N'interrompez pas mon travail !"/>
    <entry name="Drukhari/VoidravenBomber:HurtCount" value="1"/>
    <entry name="Drukhari/VoidravenBomber:Idle#0" value="Pilote et artilleur."/>
    <entry name="Drukhari/VoidravenBomber:Idle#1" value="Je dois composer ma prochaine symphonie de destruction."/>
    <entry name="Drukhari/VoidravenBomber:Idle#2" value="Faites taire les moteurs. Donnez-leur le prix de la surprise."/>
    <entry name="Drukhari/VoidravenBomber:Idle#3" value="Attention à l'antimatière, bande d'imbéciles !"/>
    <entry name="Drukhari/VoidravenBomber:IdleCount" value="4"/>
    <entry name="Drukhari/VoidravenBomber:Shaken#0" value="Un artiste ne peut pas travailler comme ça !"/>
    <entry name="Drukhari/VoidravenBomber:ShakenCount" value="1"/>
    <entry name="Drukhari/VoidravenBomber:Victory#0" value="Un cratère fumant conclut mon œuvre."/>
    <entry name="Drukhari/VoidravenBomber:VictoryCount" value="1"/>
    <entry name="Drukhari/Wrack:Attack#0" value="Créatures de l'espace réel…"/>
    <entry name="Drukhari/Wrack:Attack#1" value="Tourmente changeante."/>
    <entry name="Drukhari/Wrack:Attack#2" value="Montre-moi—Donne-moi ! Ta chair."/>
    <entry name="Drukhari/Wrack:Attack#3" value="Venez, venez. À la dalle."/>
    <entry name="Drukhari/Wrack:AttackCount" value="4"/>
    <entry name="Drukhari/Wrack:Broken#0" value="Oh, le maître n'aimera pas ça…"/>
    <entry name="Drukhari/Wrack:BrokenCount" value="1"/>
    <entry name="Drukhari/Wrack:Hurt#0" value="Je ne suis personne. Personne n'est blessé."/>
    <entry name="Drukhari/Wrack:HurtCount" value="1"/>
    <entry name="Drukhari/Wrack:Idle#0" value="J'ai choisi cette forme… par ennui."/>
    <entry name="Drukhari/Wrack:Idle#1" value="Quelles nouvelles hyper-toxines. Quels délicieux venins nous attendent ?"/>
    <entry name="Drukhari/Wrack:Idle#2" value="Obéissez à l'Acothyste, ont-ils dit."/>
    <entry name="Drukhari/Wrack:Idle#3" value="Ne sommes-nous pas admirables ?"/>
    <entry name="Drukhari/Wrack:IdleCount" value="4"/>
    <entry name="Drukhari/Wrack:Shaken#0" value="Défendre le maître !"/>
    <entry name="Drukhari/Wrack:ShakenCount" value="1"/>
    <entry name="Drukhari/Wrack:Victory#0" value="Plus de sujets pour les expériences du maître…"/>
    <entry name="Drukhari/Wrack:VictoryCount" value="1"/>
    <entry name="Drukhari/Wyche:Attack#0" value="Disputer avec nous !"/>
    <entry name="Drukhari/Wyche:Attack#1" value="Regarde et apprends… puis meurs."/>
    <entry name="Drukhari/Wyche:Attack#2" value="La mort se rapproche…"/>
    <entry name="Drukhari/Wyche:Attack#3" value="La grâce meurtrière."/>
    <entry name="Drukhari/Wyche:AttackCount" value="4"/>
    <entry name="Drukhari/Wyche:Broken#0" value="Je ne me suis pas entraîné pour ça."/>
    <entry name="Drukhari/Wyche:BrokenCount" value="1"/>
    <entry name="Drukhari/Wyche:Hurt#0" value="Nous avons du sang ! Mais le combat ne fait que commencer."/>
    <entry name="Drukhari/Wyche:HurtCount" value="1"/>
    <entry name="Drukhari/Wyche:Idle#0" value="Nous devons nous entraîner."/>
    <entry name="Drukhari/Wyche:Idle#1" value="Nos performances nourrissent Commorragh."/>
    <entry name="Drukhari/Wyche:Idle#2" value="Prouvons notre talent !"/>
    <entry name="Drukhari/Wyche:Idle#3" value="Artisans, apportez-nous nos outils."/>
    <entry name="Drukhari/Wyche:IdleCount" value="4"/>
    <entry name="Drukhari/Wyche:Shaken#0" value="N'apprécient-ils pas notre art ? !"/>
    <entry name="Drukhari/Wyche:ShakenCount" value="1"/>
    <entry name="Drukhari/Wyche:Victory#0" value="Inclinez-vous et souriez pour le public, mes sœurs."/>
    <entry name="Drukhari/Wyche:VictoryCount" value="1"/>
    <entry name="Drukhari/Ravager" value="Drukhari/Raider"/>
    <entry name="Drukhari/Reaver" value="Drukhari/RazorwingJetfighter"/> 
	<entry name="Drukhari/Venom" value="Drukhari/Raider"/>
    <entry name="Eldar/DarkReaper:Attack#0" value="Apprécier l'art de la destruction."/>
    <entry name="Eldar/DarkReaper:Attack#1" value="Apporter la mort."/>
    <entry name="Eldar/DarkReaper:Attack#2" value="Au nom de Khaine !"/>
    <entry name="Eldar/DarkReaper:Attack#3" value="Nous ne manquons pas."/>
    <entry name="Eldar/DarkReaper:AttackCount" value="4"/>
    <entry name="Eldar/DarkReaper:Broken#0" value="C'est nous qui serons détruits."/>
    <entry name="Eldar/DarkReaper:BrokenCount" value="1"/>
    <entry name="Eldar/DarkReaper:Hurt#0" value="La destruction nous guette tous."/>
    <entry name="Eldar/DarkReaper:HurtCount" value="1"/>
    <entry name="Eldar/DarkReaper:Idle#0" value="Nous nous souvenons d'Altansar."/>
    <entry name="Eldar/DarkReaper:Idle#1" value="La destruction n'est qu'un autre chemin dans notre longue vie."/>
    <entry name="Eldar/DarkReaper:Idle#2" value="La mort sans peur est notre lot."/>
    <entry name="Eldar/DarkReaper:Idle#3" value="Le destructeur a pris le dieu Smith il y a longtemps."/>
    <entry name="Eldar/DarkReaper:IdleCount" value="4"/>
    <entry name="Eldar/DarkReaper:Shaken#0" value="Le Destructeur s'est retourné contre nous !"/>
    <entry name="Eldar/DarkReaper:ShakenCount" value="1"/>
    <entry name="Eldar/DarkReaper:Victory#0" value="Le Destructeur les a pris."/>
    <entry name="Eldar/DarkReaper:VictoryCount" value="1"/>
    <entry name="Necrons/Deathmark:Attack#0" value="Il a pris pour cible leurs dirigeants."/>
    <entry name="Necrons/Deathmark:Attack#1" value="Désintégrant leurs synapses."/>
    <entry name="Necrons/Deathmark:Attack#2" value="Ennemi déshonorant ciblé."/>
    <entry name="Necrons/Deathmark:Attack#3" value="Marqué pour la mort."/>
    <entry name="Necrons/Deathmark:AttackCount" value="4"/>
    <entry name="Necrons/Deathmark:Broken#0" value="Retour à la dimension alternative !"/>
    <entry name="Necrons/Deathmark:BrokenCount" value="1"/>
    <entry name="Necrons/Deathmark:Hurt#0" value="Échec des protocoles de réanimation."/>
    <entry name="Necrons/Deathmark:HurtCount" value="1"/>
    <entry name="Necrons/Deathmark:Idle#0" value="Surveillance des communications."/>
    <entry name="Necrons/Deathmark:Idle#1" value="Le Trompeur est-il vraiment ici ?"/>
    <entry name="Necrons/Deathmark:Idle#2" value="Localisation des nœuds de commandement."/>
    <entry name="Necrons/Deathmark:Idle#3" value="Protocoles de décapitation en attente."/>
    <entry name="Necrons/Deathmark:IdleCount" value="4"/>
    <entry name="Necrons/Deathmark:Shaken#0" value="Nous sommes des outils de précision, pas des cibles."/>
    <entry name="Necrons/Deathmark:ShakenCount" value="1"/>
    <entry name="Necrons/Deathmark:Victory#0" value="Comme prévu, éliminés."/>
    <entry name="Necrons/Deathmark:VictoryCount" value="1"/>
    <entry name="Neutral/Poxwalker:Attack#0" value="Heehee."/>
    <entry name="Neutral/Poxwalker:Attack#1" value="Urrnnnnâ€¦"/>
    <entry name="Neutral/Poxwalker:Attack#2" value="Harrrrâ€¦"/>
    <entry name="Neutral/Poxwalker:Attack#3" value="Nurg Elll"/>
    <entry name="Neutral/Poxwalker:AttackCount" value="4"/>
    <entry name="Neutral/Poxwalker:Broken#0" value="Père¦"/>
    <entry name="Neutral/Poxwalker:BrokenCount" value="1"/>
    <entry name="Neutral/Poxwalker:Hurt#0" value="Chuteâ€¦"/>
    <entry name="Neutral/Poxwalker:HurtCount" value="1"/>
    <entry name="Neutral/Poxwalker:Idle#0" value="Âmes piégées"/>
    <entry name="Neutral/Poxwalker:Idle#1" value="Dieu de la peste"/>
    <entry name="Neutral/Poxwalker:Idle#2" value="Père"/>
    <entry name="Neutral/Poxwalker:Idle#3" value="Unhhhhh¦"/>
    <entry name="Neutral/Poxwalker:IdleCount" value="4"/>
    <entry name="Neutral/Poxwalker:Shaken#0" value="Ãnnnaaaaaaaaaaaaaaaaaaaaaaaaaa"/>
    <entry name="Neutral/Poxwalker:ShakenCount" value="1"/>
    <entry name="Neutral/Poxwalker:Victory#0" value="Infecté"/>
    <entry name="Neutral/Poxwalker:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#0" value="L'activation de l'encens sacré épuise."/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#1" value="Bénir le serviteur et le charger."/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#2" value="La destruction plutôt que la gloire."/> 
 	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#3" value="Lances couchées."/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:AttackCount" value="4"/> 
 	<entry name="AdeptusMechanicus/SydonianDragoon:Broken#0" value="Protégez les moteurs !"/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:BrokenCount" value="1"/> 
 	<entry name="AdeptusMechanicus/SydonianDragoon:Hurt#0" value="Dégâts critiques subis."/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:HurtCount" value="1"/> 
 	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#0" value="Repos du serviteur."/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#1" value="Sydonia est si loin…"/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#2" value="Le doute et la mort me dépassent."/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#3" value="Un mouvement sans fin pour un empire éternel !"/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:IdleCount" value="4"/> 
 	<entry name="AdeptusMechanicus/SydonianDragoon:Shaken#0" value="L'échassier trébuche !"/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:ShakenCount" value="1"/> 
 	<entry name="AdeptusMechanicus/SydonianDragoon:Victory#0" value="Pour Vingh et Sydonia"/>
 	<entry name="AdeptusMechanicus/SydonianDragoon:VictoryCount" value="1"/> 
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#0" value="Marquage des cibles."/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#1" value="Tenir compte de la dérive du vent."/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#2" value="Très bien, monsieur."/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#3" value="Nous avons une solution de tir, monsieur."/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:AttackCount" value="4"/> 
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Broken#0" value="Laissez l'arme !"/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:BrokenCount" value="1"/> 
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Hurt#0" value="Coup direct. Sur nous, malheureusement."/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:HurtCount" value="1"/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#0" value="La vie dans la garde est terriblement difficile."/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#1" value="Artilleur qui ?"/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#2" value="S'agit-il d'une tranchée ou d'une dépression ?"/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#3" value="Pas de cibles ? Parfait."/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:IdleCount" value="4"/> 
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Shaken#0" value="Je vous ai dit que j'étais malade."/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:ShakenCount" value="1"/> 
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:Victory#0" value="Est-ce qu'on… ? Je crois qu'on les a eus !"/>
 	<entry name="AstraMilitarum/FieldOrdnanceBattery:VictoryCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosTerminator" value="ChaosSpaceMarines/ChaosSpaceMarine"/>
 	<entry name="Eldar/Wraithlord" value="Eldar/Wraithblade"/>
 	<entry name="Necrons/SkorpekhDestroyer" value="Necrons/HeavyDestroyer"/>
 	<entry name="Orks/BurnaBoyz:Attack#0" value="Allumez-lez !"/>
 	<entry name="Orks/BurnaBoyz:Attack#1" value="Hahaha ! Regardez-lez brûler !"/>
 	<entry name="Orks/BurnaBoyz:Attack#2" value="Brûlez, nora !"/>
 	<entry name="Orks/BurnaBoyz:Attack#3" value="Sis en feu !"/>
 	<entry name="Orks/BurnaBoyz:AttackCount" value="4"/>
 	<entry name="Orks/BurnaBoyz:Broken#0" value="Nous n'avons pas déclenché l'incendie !"/>
 	<entry name="Orks/BurnaBoyz:BrokenCount" value="1"/>
 	<entry name="Orks/BurnaBoyz:Hurt#0" value="Je suis tombé den un cercle de feu !"/>
 	<entry name="Orks/BurnaBoyz:HurtCount" value="1"/>
 	<entry name="Orks/BurnaBoyz:Idle#0" value="J'aime jouer avec le feu."/>
 	<entry name="Orks/BurnaBoyz:Idle#1" value="'Ont leur met la lumière."/>
 	<entry name="Orks/BurnaBoyz:Idle#2" value="Je ne veux pas mettre le feu au monde… attendez, si je le veux."/>
 	<entry name="Orks/BurnaBoyz:Idle#3" value="C'mon,grotty, allume-moi le feu !"/>
 	<entry name="Orks/BurnaBoyz:IdleCount" value="4"/>
 	<entry name="Orks/BurnaBoyz:Shaken#0" value="Ça brûle, brûle, brûle !"/>
 	<entry name="Orks/BurnaBoyz:ShakenCount" value="1"/>
 	<entry name="Orks/BurnaBoyz:Victory#0" value="L'enfer des remises !"/>
 	<entry name="Orks/BurnaBoyz:VictoryCount" value="1"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Attack#0" value="Déculpabilisez !"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Attack#1" value="Purger !"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Attack#2" value="In extremis !"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Attack#3" value="Tuuuez-moi !"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:AttackCount" value="4"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Broken#0" value="Nnnnn-"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:BrokenCount" value="1"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Hurt#0" value="Plus près de toi, mon Empereur"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:HurtCount" value="1"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Idle#0" value="Dormant"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Idle#1" value="Foi"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Idle#2" value="Libérez… moi."/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Idle#3" value="J'obéis."/>
 	<entry name="SistersOfBattle/ArcoFlagellant:IdleCount" value="4"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Shaken#0" value="Martyrisés ?"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:ShakenCount" value="1"/>
 	<entry name="SistersOfBattle/ArcoFlagellant:Victory#0" value=""/>
 	<entry name="SistersOfBattle/ArcoFlagellant:VictoryCount" value="1"/>
 	<entry name="SpaceMarines/AssaultTerminator:Attack#0" value="Meurs de ma main !"/>
 	<entry name="SpaceMarines/AssaultTerminator:Attack#1" value="Il n'y a que la guerre !"/>
 	<entry name="SpaceMarines/AssaultTerminator:Attack#2" value="Aucun ne survivra !"/>
 	<entry name="SpaceMarines/AssaultTerminator:Attack#3" value="Nous nettoyons."/>
 	<entry name="SpaceMarines/AssaultTerminator:AttackCount" value="4"/>
 	<entry name="SpaceMarines/AssaultTerminator:Broken#0" value="Embuscade !"/>
 	<entry name="SpaceMarines/AssaultTerminator:BrokenCount" value="1"/>
 	<entry name="SpaceMarines/AssaultTerminator:Hurt#0" value="Tenue d'armure tactique Dreadnought."/>
 	<entry name="SpaceMarines/AssaultTerminator:HurtCount" value="1"/>
 	<entry name="SpaceMarines/AssaultTerminator:Idle#0" value="Un moment de relâchement…"/>
 	<entry name="SpaceMarines/AssaultTerminator:Idle#1" value="Notre armure est prête."/>
 	<entry name="SpaceMarines/AssaultTerminator:Idle#2" value="Première compagnie en attente."/>
 	<entry name="SpaceMarines/AssaultTerminator:Idle#3" value="Bénir notre armure."/>
 	<entry name="SpaceMarines/AssaultTerminator:IdleCount" value="4"/>
 	<entry name="SpaceMarines/AssaultTerminator:Shaken#0" value="Par son nom !"/>
 	<entry name="SpaceMarines/AssaultTerminator:ShakenCount" value="1"/>
 	<entry name="SpaceMarines/AssaultTerminator:Victory#0" value="Pour l'empereur !"/>
 	<entry name="SpaceMarines/AssaultTerminator:VictoryCount" value="1"/>
 	<entry name="Tau/FireWarriorBreacher" value="Tau/FireWarrior"/>
 	<entry name="Tyranids/Biovore" value="Tyranids/Carnifex"/>
 	<entry name="Drukhari/Talos" value="Drukhari/Cronos"/>
	<entry name="Drukhari/Archon:Attack#0" value="La souffrance est votre lot !"/>
    <entry name="Orks/DeffDread:Attack#0" value="Un coup de pied et un coup de sang !"/>
    <entry name="Orks/DeffDread:Attack#1" value="Ça valait le coup !"/>
    <entry name="Orks/DeffDread:Attack#2" value="Ooh, j'ai des Rokkits !"/>
    <entry name="Orks/DeffDread:Attack#3" value="Des tirs à tout bout de champ !"/>
    <entry name="Orks/DeffDread:AttackCount" value="4"/>
    <entry name="Orks/DeffDread:Broken#0" value="La jambe en l'air !"/>
    <entry name="Orks/DeffDread:BrokenCount" value="1"/>
    <entry name="Orks/DeffDread:Hurt#0" value="Je pense qu'il y a un vieux en moi."/>
    <entry name="Orks/DeffDread:HurtCount" value="1"/>
    <entry name="Orks/DeffDread:Idle#0" value="Il y a un Squig ici !"/>
    <entry name="Orks/DeffDread:Idle#1" value="Un peu, gratte-moi le nez ! Ça me rend fou !"/>
    <entry name="Orks/DeffDread:Idle#2" value="Pourquoi tous ces fils ?"/>
    <entry name="Orks/DeffDread:Idle#3" value="Borrringgg."/>
    <entry name="Orks/DeffDread:IdleCount" value="4"/>
    <entry name="Orks/DeffDread:Shaken#0" value="Qu'est-ce que tu penses de cette chose ?"/>
    <entry name="Orks/DeffDread:ShakenCount" value="1"/>
    <entry name="Orks/DeffDread:Victory#0" value="WAAAGH ! Deff Dreads iz da bestest !"/>
    <entry name="Orks/DeffDread:VictoryCount" value="1"/>
    <entry name="SistersOfBattle/Imagifier:Attack#0" value="Célestin les frappa et vit que c'était bon."/>
    <entry name="SistersOfBattle/Imagifier:Attack#1" value="Et ils furent les morts glorifiés !"/>
    <entry name="SistersOfBattle/Imagifier:Attack#2" value="Leur sainte rage, mes sœurs !"/>
    <entry name="SistersOfBattle/Imagifier:Attack#3" value="Elles se sont dressées, défiantes, contre l'ennemi !"/>
    <entry name="SistersOfBattle/Imagifier:AttackCount" value="4"/>
    <entry name="SistersOfBattle/Imagifier:Broken#0" value="Mon chant faiblit"/>
    <entry name="SistersOfBattle/Imagifier:BrokenCount" value="1"/>
    <entry name="SistersOfBattle/Imagifier:Hurt#0" value="Elles ont souffert de blessures douloureuses, mais elles ont tenu bon !"/>
    <entry name="SistersOfBattle/Imagifier:HurtCount" value="1"/>
    <entry name="SistersOfBattle/Imagifier:Idle#0" value="Je porte le Simulacrum Imperialis."/>
    <entry name="SistersOfBattle/Imagifier:Idle#1" value="En quelques instants, ils ont invoqué l'amour de l'Empereur."/>
    <entry name="SistersOfBattle/Imagifier:Idle#2" value="Et Célestine sut que son heure était venue…"/>
    <entry name="SistersOfBattle/Imagifier:Idle#3" value="L'archi-traître serra d'une seule griffe l'ange déchu…"/>
    <entry name="SistersOfBattle/Imagifier:IdleCount" value="4"/>
    <entry name="SistersOfBattle/Imagifier:Shaken#0" value="Ils scellèrent la Porte de l'Eternité et furent réduits à l'impuissance…"/>
    <entry name="SistersOfBattle/Imagifier:ShakenCount" value="1"/>
    <entry name="SistersOfBattle/Imagifier:Victory#0" value="Et ils retournèrent en triomphe sur la sainte Terra !"/>
    <entry name="SistersOfBattle/Imagifier:VictoryCount" value="1"/>
    <entry name="SpaceMarines/Whirlwind:Attack#0" value="Missiles en route, Commandant."/>
    <entry name="SpaceMarines/Whirlwind:Attack#1" value="Tir en rafale."/>
    <entry name="SpaceMarines/Whirlwind:Attack#2" value="Barrage indirect en cours."/>
    <entry name="SpaceMarines/Whirlwind:Attack#3" value="Bombardement de saturation disponible."/>
    <entry name="SpaceMarines/Whirlwind:AttackCount" value="4"/>
    <entry name="SpaceMarines/Whirlwind:Broken#0" value="On se replie."/>
    <entry name="SpaceMarines/Whirlwind:BrokenCount" value="1"/>
    <entry name="SpaceMarines/Whirlwind:Hurt#0" value="A portée d'armes légères—dommages subis."/>
    <entry name="SpaceMarines/Whirlwind:HurtCount" value="1"/>
    <entry name="SpaceMarines/Whirlwind:Idle#0" value="Gestion des esprits-machines."/>
    <entry name="SpaceMarines/Whirlwind:Idle#1" value="Déployés, en attente d'ordres."/>
    <entry name="SpaceMarines/Whirlwind:Idle#2" value="Si les ennemis ont besoin d'être affaiblis, l'artillerie est disponible."/>
    <entry name="SpaceMarines/Whirlwind:Idle#3" value="Rechargement des missiles."/>
    <entry name="SpaceMarines/Whirlwind:IdleCount" value="4"/>
    <entry name="SpaceMarines/Whirlwind:Shaken#0" value="C'est notre Chapitre Monde… comment est-ce possible ?"/>
    <entry name="SpaceMarines/Whirlwind:ShakenCount" value="1"/>
    <entry name="SpaceMarines/Whirlwind:Victory#0" value="Réception de rapports sur la destruction de la cible."/>
    <entry name="SpaceMarines/Whirlwind:VictoryCount" value="1"/>
    <entry name="Tau/RVarnaBattlesuit:Attack#0" value="Tenir bon."/>
    <entry name="Tau/RVarnaBattlesuit:Attack#1" value="Saturation du plasma en cours."/>
    <entry name="Tau/RVarnaBattlesuit:AttackCount" value="2"/>
    <entry name="Tau/RVarnaBattlesuit:Broken#0" value="Besoin de soutien ici."/>
    <entry name="Tau/RVarnaBattlesuit:BrokenCount" value="1"/>
    <entry name="Tau/RVarnaBattlesuit:Hurt#0" value="Le réacteur fuit… pas de retraite."/>
    <entry name="Tau/RVarnaBattlesuit:HurtCount" value="1"/>
    <entry name="Tau/RVarnaBattlesuit:Idle#0" value="Que faisons-nous dans ce fu'llasso ?"/>
    <entry name="Tau/RVarnaBattlesuit:Idle#1" value="Les ho'or-ata-t'chel—les douleurs des fantômes—sont graves aujourd'hui."/>
    <entry name="Tau/RVarnaBattlesuit:Idle#2" value="Shas'vre au rapport."/>
    <entry name="Tau/RVarnaBattlesuit:Idle#3" value="Comment font-ils ce fio'tak ? Il est imprenable !"/>
    <entry name="Tau/RVarnaBattlesuit:IdleCount" value="4"/>
    <entry name="Tau/RVarnaBattlesuit:Shaken#0" value="Iur'tae'mont ! Où est Farsight quand on a besoin de lui ?"/>
    <entry name="Tau/RVarnaBattlesuit:ShakenCount" value="1"/>
    <entry name="Tau/RVarnaBattlesuit:Victory#0" value="Rien ne bouge là-bas."/>
    <entry name="Tau/RVarnaBattlesuit:VictoryCount" value="1"/>
	<entry name="Tyranids/NornEmissary" value="Tyranids/Carnifex"/>
    <entry name="Tyranids/Tyrannocyte" value="Tyranids/Carnifex"/>
</language>