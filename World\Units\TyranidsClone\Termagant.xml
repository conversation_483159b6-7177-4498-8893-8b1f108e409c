<?xml version="1.0" encoding="utf-8"?>
<unit>
	<model>
		<unit mesh="Units/Tyranids/Termagant"
				material="Units/Tyranids/Termagant"
				idleAnimation="Units/Tyranids/TermagantIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				scale="0.8 0.8 0.8"
				bloodBone="BloodBone"
				walker="1"/>
	</model>
	<group size="10" rowSize="5" memberDeltaX="100" memberDeltaY="50"/>
	<weapons>
		<weapon name="Fleshborer">
			<model>
				<projectileWeapon mesh="Weapons/Tyranids/TermagantGun"
						material="Units/Tyranids/Termagant"
						bone="Bone003"
						muzzleBone=".Muzzle"
						fireInterval="0.166666666667"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="2"/> <!-- %armor base armor=6+ -->
				<biomassUpkeep base="1.0"/> <!-- %biomassUpkeep base tier=4 factor=1 -->
				<biomassCost base="20.0"/> <!-- %biomassCost base tier=4 factor=1 -->
				<hitpointsMax base="2.0"/> <!-- %hitpointsMax base toughness=3 wounds=1 -->
				<meleeAccuracy base="6"/> <!-- %meleeAccuracy base weaponSkill=3 -->
				<meleeAttacks base="1"/>
				<strengthDamage base="1"/> <!-- %strengthDamage base strength=3 -->
				<moraleMax base="4"/> <!-- %moraleMax base leadership=5 -->
				<movementMax base="3"/>
				<productionCost base="24.0"/> <!-- %productionCost base tier=4 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseInfantryScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/TermagantAttack"
						beginFire="0.666666666667"
						endFire="2.06666666667"/>
			</model>				
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/TermagantDie"
						animationCount="2"
						sound="Units/SmallUnarmoredDie"
						soundCount="4"
						soundDelay="0.5"
						voiceSound="Units/Tyranids/SmallDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/TermagantMove"
						sound="Units/Tyranids/InfantryMove"
						soundCount="4"/>
			</model>
		</move>
		<instinctiveBehaviourOverride consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				name="TyranidsClone/InstinctiveBehaviourOverride"
				requiredActionPoints="0">
			<model>
				<action sound="Actions/HiveMindBuff"/>
			</model>
			<modifiers>
				<modifier requiredUpgrade="TyranidsClone/InstinctiveBehaviourOverride2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
			</modifiers>
			<conditions>
				<unit>
					<noTrait name="TyranidsClone/InstinctiveBehaviourOverride"/>
					<noTrait name="TyranidsClone/SynapseLink"/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="TyranidsClone/InstinctiveBehaviourOverride" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</instinctiveBehaviourOverride>
		<subterraneanAssault name="TyranidsClone/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="ExtraInfantryArmour" requiredUpgrade="TyranidsClone/ExtraInfantryArmour"/>
		<trait name="Fleet" requiredUpgrade="TyranidsClone/AdrenalGlands"/>
		<!-- <trait name="FuriousCharge" requiredUpgrade="TyranidsClone/AdrenalGlands"/> -->
		<trait name="TyranidsClone/InfantryUpkeep" requiredUpgrade="TyranidsClone/InfantryUpkeep"/>
		<trait name="TyranidsClone/InstinctiveBehaviour"/>
		<trait name="MoveThroughCover"/>
		<!-- <trait name="TyranidsClone/ToxinSacs" requiredUpgrade="TyranidsClone/ToxinSacs"/> -->
	</traits>
</unit>
