<?xml version="1.0" encoding="utf-8"?>
<unit>
	<model>
		<unit mesh="Units/Tyranids/TyranidPrime"
				material="Units/Tyranids/TyranidPrime"
				idleAnimation="Units/Tyranids/TyranidPrimeIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				scale="1.2 1.2 1.2"
				bloodBone="BloodBone"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="Deathspitter">
			<model>
				<projectileWeapon mesh="Weapons/Tyranids/TyranidPrimeGun"
						material="Units/Tyranids/TyranidPrime"
						fireInterval="0.533333333333"
						bone="WeaponBone"
						muzzleBone=".Muzzle"/>
			</model>
		</weapon>
		<weapon name="FleshHooks" requiredUpgrade="Tyranids/FleshHooks">
			<model>
				<projectileWeapon muzzleBone="BloodBone" fireInterval="10.0"
						effectFaceWeight="1.0"/>
			</model>
		</weapon>
		<weapon name="Boneswords">
			<model>
				<weapon fireInterval="0.4"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="3.0"/> <!-- %biomassUpkeep base tier=7 factor=1 -->
				<biomassCost base="60.0"/> <!-- %biomassCost base tier=7 factor=1 -->
				<hitpointsMax base="16.0"/> <!-- %hitpointsMax base toughness=5 wounds=4 -->
				<influenceUpkeep base="6.0"/> <!-- %influenceUpkeep base tier=7 factor=2 -->
				<influenceCost base="120.0"/> <!-- %influenceCost base tier=7 factor=2 -->
				<itemSlots base="6"/>
				<levelMax base="9"/>
				<meleeAccuracy base="12"/> <!-- %meleeAccuracy base weaponSkill=6 -->
				<meleeAttacks base="3"/>
				<strengthDamage base="2"/> <!-- %strengthDamage base strength=5 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=10 -->
				<movementMax base="3"/>
				<productionCost base="42.0"/> <!-- %productionCost base tier=7 factor=1 -->
				<rangedAccuracy base="8"/> <!-- %rangedAccuracy base ballisticSkill=4 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseHeroesScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/TyranidPrimeAttack"
						beginFire="0.733333333333"
						endFire="2"
						chargeAnimation="Units/Tyranids/TyranidPrimeCharge"
						chargeBeginFire="0.3"
						chargeEndFire="1.36666666667"
						meleeAnimation="Units/Tyranids/TyranidPrimeMelee"
						meleeBeginSwing="0.133333333333"
						meleeEndSwing="0.666"/>
			</model>				
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/TyranidPrimeDie"
						sound="Units/MediumUnarmoredDie"
						soundCount="4"
						soundDelay="1.0"
						voiceSound="Units/Tyranids/MediumDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/TyranidPrimeMove"
						sound="Units/Tyranids/InfantryMove"
						soundCount="4"/>
			</model>
		</move>
		<genericUnitAbility name="Tyranids/SynapseCreature" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/SynapseLink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<genericUnitAbility name="Tyranids/ShadowInTheWarp" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1" excludeRadius="0">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/ShadowInTheWarp"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
		<scourgeOfTheBrood consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/ScourgeOfTheBrood" 
				rank="-1"
				rankMax="2"
				requiredActionPoints="0">
			<model>
				<action animation="Units/Tyranids/TyranidPrimeAbility"
						sound="Actions/HiveMindBuff"/>
			</model>			
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/ScourgeOfTheBrood" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</scourgeOfTheBrood>
		<genericUnitAbility name="Tyranids/AdaptiveBiology" 
				passive="1"
				rank="-1"
				rankMax="2">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/AdaptiveBiology"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<genericUnitAbility name="Tyranids/AlphaWarrior" 
				passive="1"
				rank="-1"
				rankMax="2">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radiusMin="1" radiusMax="3">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/AlphaWarrior"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<exploitWeaknesses consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				elite="1"
				name="Tyranids/ExploitWeaknesses"
				rank="-1"
				rankMax="0"
				requiredActionPoints="0">
			<model>
				<action animation="Units/Tyranids/TyranidPrimeAbility"
						sound="Actions/HiveMindEliteDebuff"/>
			</model>
			<beginTargets>
				<target rangeMax="2">
					<conditions>
						<unit>
							<enemy/>
						</unit>
					</conditions>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/ExploitWeaknesses" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</target>
			</beginTargets>
		</exploitWeaknesses>
		<levelUp/>
		<shop/>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="ExtraInfantryArmour" requiredUpgrade="Tyranids/ExtraInfantryArmour"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="Hero"/>
		<trait name="Tyranids/InfantryUpkeep" requiredUpgrade="Tyranids/InfantryUpkeep"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<trait name="Tyranids/ToxinSacs" requiredUpgrade="Tyranids/ToxinSacs"/>
		<trait name="VeryBulky"/>
	</traits>
</unit>
