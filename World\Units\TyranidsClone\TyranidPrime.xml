<?xml version="1.0" encoding="utf-8"?>
<unit>
	<model>
		<unit mesh="Units/Tyranids/TyranidPrime"
				material="Units/Tyranids/TyranidPrime"
				idleAnimation="Units/Tyranids/TyranidPrimeIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				scale="1.2 1.2 1.2"
				bloodBone="BloodBone"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="Deathspitter">
			<model>
				<projectileWeapon mesh="Weapons/Tyranids/TyranidPrimeGun"
						material="Units/Tyranids/TyranidPrime"
						fireInterval="0.533333333333"
						bone="WeaponBone"
						muzzleBone=".Muzzle"/>
			</model>
		</weapon>
		<weapon name="FleshHooks" requiredUpgrade="TyranidsClone/FleshHooks">
			<model>
				<projectileWeapon muzzleBone="BloodBone" fireInterval="10.0"
						effectFaceWeight="1.0"/>
			</model>
		</weapon>
		<weapon name="Boneswords">
			<model>
				<weapon fireInterval="0.4"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="3.0"/> <!-- %biomassUpkeep base tier=7 factor=1 -->
				<biomassCost base="60.0"/> <!-- %biomassCost base tier=7 factor=1 -->
				<hitpointsMax base="16.0"/> <!-- %hitpointsMax base toughness=5 wounds=4 -->
				<influenceUpkeep base="6.0"/> <!-- %influenceUpkeep base tier=7 factor=2 -->
				<influenceCost base="120.0"/> <!-- %influenceCost base tier=7 factor=2 -->
				<itemSlots base="6"/>
				<levelMax base="9"/>
				<meleeAccuracy base="12"/> <!-- %meleeAccuracy base weaponSkill=6 -->
				<meleeAttacks base="3"/>
				<strengthDamage base="2"/> <!-- %strengthDamage base strength=5 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=10 -->
				<movementMax base="3"/>
				<productionCost base="42.0"/> <!-- %productionCost base tier=7 factor=1 -->
				<rangedAccuracy base="8"/> <!-- %rangedAccuracy base ballisticSkill=4 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseHeroesScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/TyranidPrimeAttack"
						beginFire="0.733333333333"
						endFire="2"
						chargeAnimation="Units/Tyranids/TyranidPrimeCharge"
						chargeBeginFire="0.3"
						chargeEndFire="1.36666666667"
						meleeAnimation="Units/Tyranids/TyranidPrimeMelee"
						meleeBeginSwing="0.133333333333"
						meleeEndSwing="0.666"/>
			</model>				
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/TyranidPrimeDie"
						sound="Units/MediumUnarmoredDie"
						soundCount="4"
						soundDelay="1.0"
						voiceSound="Units/Tyranids/MediumDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/TyranidPrimeMove"
						sound="Units/Tyranids/InfantryMove"
						soundCount="4"/>
			</model>
		</move>
		<genericUnitAbility name="TyranidsClone/SynapseCreature" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="TyranidsClone/SynapseLink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<genericUnitAbility name="TyranidsClone/ShadowInTheWarp" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1" excludeRadius="0">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="TyranidsClone/ShadowInTheWarp"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<subterraneanAssault name="TyranidsClone/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="ExtraInfantryArmour" requiredUpgrade="TyranidsClone/ExtraInfantryArmour"/>
		<trait name="TyranidsClone/InfantryUpkeep" requiredUpgrade="TyranidsClone/InfantryUpkeep"/>
		<trait name="Poisoned" rank="4"/>
		<trait name="MoveThroughCover"/>
		<trait name="Regeneration"/>
		<trait name="TyranidsClone/PreyAdaptation"/>
		<trait name="VeryBulky"/>
		<trait name="Hero"/>
	</traits>
</unit>
