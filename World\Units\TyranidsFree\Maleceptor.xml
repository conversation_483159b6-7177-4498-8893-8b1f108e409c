<?xml version="1.0" encoding="utf-8"?>
<unit dlc="Supplement14">
	<model>
		<unit mesh="Units/Tyranids/Maleceptor"
				material="Units/Tyranids/Maleceptor"
				idleAnimation="Units/Tyranids/MaleceptorIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				scale="1 1 1"
				bloodBone="Chest"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="ScythingTalons">
			<model>
				<weapon fireInterval="10.0"/>
			</model>
		</weapon>
		<weapon name="PsychicOverload" slotName="PsychicOverload" enabled="0">>
			<model>
				<beamWeapon muzzleBone="Head"/>
			</model>		
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="6"/> <!-- %armor base armor=4+ -->
				<biomassUpkeep base="6.0"/> <!-- %biomassUpkeep base tier=9 factor=1 -->
				<biomassCost base="120.0"/> <!-- %biomassCost base tier=9 factor=1 -->
				<hitpointsMax base="36.0"/> <!-- %hitpointsMax base toughness=6 wounds=6 -->
				<meleeAccuracy base="6"/> <!-- %meleeAccuracy base weaponSkill=3 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="3"/> <!-- %strengthDamage base strength=6 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=10 -->
				<movementMax base="3"/>
				<productionCost base="54.0"/> <!-- %productionCost base tier=9 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseSupportScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action chargeAnimation="Units/Tyranids/MaleceptorCharge"
						meleeAnimation="Units/Tyranids/MaleceptorMelee"
						meleeBeginSwing="0.1"/>
			</model>
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/MaleceptorDie0"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="1.35"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/MaleceptorMove"
						sound="Units/Tyranids/CarnifexMove"
						soundCount="2"/>
			</model>
		</move>
		<genericUnitAbility name="Tyranids/SynapseCreature" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/SynapseLink"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<genericUnitAbility name="Tyranids/ShadowInTheWarp" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1" excludeRadius="0">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/ShadowInTheWarp"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				requiredActionPoints="0"
				requiredUpgrade="Tyranids/HammerOfWrath">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="HammerOfWrath" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>
		<useWeapon cooldown="1"
				consumedActionPoints="0"
				consumedMovement="0"
				psychicPower="1"
				requiredActionPoints="0"
				weaponSlotName="PsychicOverload">
			<model>
				<action animation="Units/Tyranids/MaleceptorAbility"
						beginFire="0.1"
						endFire="0.2"/>
			</model>
		</useWeapon>
		<diffusionField cooldown="3"
				consumedActionPoints="0"
				consumedMovement="0"
				name="Tyranids/DiffusionField"
				requiredActionPoints="0">
			<model>
				<action animation="Units/Tyranids/MaleceptorAbility"
						sound="Actions/DiffusionField"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit" radius="1" excludeRadius="0">
							<modifiers>
								<modifier>
									<conditions>
										<unit>
											<allied/>
											<faction name="Tyranids"/>
										</unit>
									</conditions>
									<effects>
										<addTrait duration="1" name="Tyranids/DiffusionField"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</diffusionField>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Psyker"/>
		<trait name="Fearless"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="MoveThroughCover"/>
		<trait name="MonstrousCreature"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<!-- <trait name="Relentless"/> -->
		<trait name="Smash"/>
		<trait name="Tyranids/PsychicBarrier"/>
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
	</traits>
</unit>