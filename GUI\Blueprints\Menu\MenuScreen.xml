<?xml version="1.0" encoding="utf-8"?>
<menu:menuScreen extends="HUD">
	<cinematic name="cinematic" preferredSize="FillParent FillParent" repeat="1" video="MenuScreen"/>
	<image texture="Images/MenuGradient" preferredSize="FillParent FillParent"/>
	<container layout="Relative" layout.alignment="BottomLeft" margin="8 0; 0 4" preferredSize="FillParent FillParent">
		<label name="additionalContentLabel" style="<style name='ShadowedHeading' color='GUI/Gray'/>" caption="<string name='GUI/AdditionalContentAvailable'/>"/>
	</container>
	<container layout="Relative" layout.alignment="BottomCenter" margin="0 0; 0 4" preferredSize="FillParent FillParent">
		<label name="statsLabel" showEffect="FadeInSlow" style="<style name='ShadowedHeading' color='GUI/Gray'/>" visible="0"/>
	</container>
	<container layout="Relative" layout.alignment="BottomRight" margin="0 0; 8 4" preferredSize="FillParent FillParent">
		<label name="versionLabel" style="<style name='ShadowedHeading' color='GUI/Gray'/>"/>
	</container>
	<container layout="Relative" layout.alignment="MiddleCenter" name="hudContainer" preferredSize="FillParent FillParent" showEffect="FadeIn" hideEffect="FadeOut">
		<menu:creditsHUD name="creditsHUD"/>
		<menu:directConnectHUD name="directConnectHUD"/>
		<menu:extraHUD name="extraHUD"/>
		<loadGameHUD name="loadGameHUD"/>
		<menu:lobbyHUD name="lobbyHUD"/>
		<menu:mainMenuHUD name="mainMenuHUD"/>
		<menu:multiplayerHUD name="multiplayerHUD"/>
		<menu:modsHUD name="modsHUD"/>
		<menu:patchNotesHUD name="patchNotesHUD"/>
		<menu:selectFactionHUD name="selectFactionHUD"/>
		<settingsHUD name="settingsHUD"/>
	</container>
</menu:menuScreen>
