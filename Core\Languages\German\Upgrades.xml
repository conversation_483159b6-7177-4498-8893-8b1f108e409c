<?xml version="1.0" encoding="utf-8"?>
<language>

	<!-- Buildings -->
	<entry name="AdeptusMechanicus/Aircraft" value="<string name='Buildings/AdeptusMechanicus/Aircraft'/>"/>
	<entry name="AdeptusMechanicus/AircraftDescription" value="<string name='Buildings/AdeptusMechanicus/AircraftDescription'/>"/>
	<entry name="AdeptusMechanicus/AircraftFlavor" value="<string name='Buildings/AdeptusMechanicus/AircraftFlavor'/>"/>
	<entry name="AdeptusMechanicus/Construction" value="<string name='Buildings/AdeptusMechanicus/Construction'/>"/>
	<entry name="AdeptusMechanicus/ConstructionDescription" value="<string name='Buildings/AdeptusMechanicus/ConstructionDescription'/>"/>
	<entry name="AdeptusMechanicus/ConstructionFlavor" value="<string name='Buildings/AdeptusMechanicus/ConstructionFlavor'/>"/>
	<entry name="AdeptusMechanicus/Heroes" value="<string name='Buildings/AdeptusMechanicus/Heroes'/>"/>
	<entry name="AdeptusMechanicus/HeroesDescription" value="<string name='Buildings/AdeptusMechanicus/HeroesDescription'/>"/>
	<entry name="AdeptusMechanicus/HeroesFlavor" value="<string name='Buildings/AdeptusMechanicus/HeroesFlavor'/>"/>
	<entry name="AdeptusMechanicus/Housing" value="<string name='Buildings/AdeptusMechanicus/Housing'/>"/>
	<entry name="AdeptusMechanicus/HousingDescription" value="<string name='Buildings/AdeptusMechanicus/HousingDescription'/>"/>
	<entry name="AdeptusMechanicus/HousingFlavor" value="<string name='Buildings/AdeptusMechanicus/HousingFlavor'/>"/>
	<entry name="AdeptusMechanicus/Loyalty" value="<string name='Buildings/AdeptusMechanicus/Loyalty'/>"/>
	<entry name="AdeptusMechanicus/LoyaltyDescription" value="<string name='Buildings/AdeptusMechanicus/LoyaltyDescription'/>"/>
	<entry name="AdeptusMechanicus/LoyaltyFlavor" value="<string name='Buildings/AdeptusMechanicus/LoyaltyFlavor'/>"/>
	<entry name="AdeptusMechanicus/Vehicles" value="<string name='Buildings/AdeptusMechanicus/Vehicles'/>"/>
	<entry name="AdeptusMechanicus/VehiclesDescription" value="<string name='Buildings/AdeptusMechanicus/VehiclesDescription'/>"/>
	<entry name="AdeptusMechanicus/VehiclesFlavor" value="<string name='Buildings/AdeptusMechanicus/VehiclesFlavor'/>"/>
	<entry name="AstraMilitarum/Aircraft" value="<string name='Buildings/AstraMilitarum/Aircraft'/>"/>
	<entry name="AstraMilitarum/AircraftDescription" value="<string name='Buildings/AstraMilitarum/AircraftDescription'/>"/>
	<entry name="AstraMilitarum/AircraftFlavor" value="<string name='Buildings/AstraMilitarum/AircraftFlavor'/>"/>
	<entry name="AstraMilitarum/Construction" value="<string name='Buildings/AstraMilitarum/Construction'/>"/>
	<entry name="AstraMilitarum/ConstructionDescription" value="<string name='Buildings/AstraMilitarum/ConstructionDescription'/>"/>
	<entry name="AstraMilitarum/ConstructionFlavor" value="<string name='Buildings/AstraMilitarum/ConstructionFlavor'/>"/>
	<entry name="AstraMilitarum/Heroes" value="<string name='Buildings/AstraMilitarum/Heroes'/>"/>
	<entry name="AstraMilitarum/HeroesDescription" value="<string name='Buildings/AstraMilitarum/HeroesDescription'/>"/>
	<entry name="AstraMilitarum/HeroesFlavor" value="<string name='Buildings/AstraMilitarum/HeroesFlavor'/>"/>
	<entry name="AstraMilitarum/Housing" value="<string name='Buildings/AstraMilitarum/Housing'/>"/>
	<entry name="AstraMilitarum/HousingDescription" value="<string name='Buildings/AstraMilitarum/HousingDescription'/>"/>
	<entry name="AstraMilitarum/HousingFlavor" value="<string name='Buildings/AstraMilitarum/HousingFlavor'/>"/>
	<entry name="AstraMilitarum/Loyalty" value="<string name='Buildings/AstraMilitarum/Loyalty'/>"/>
	<entry name="AstraMilitarum/LoyaltyDescription" value="<string name='Buildings/AstraMilitarum/LoyaltyDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyFlavor" value="<string name='Buildings/AstraMilitarum/LoyaltyFlavor'/>"/>
	<entry name="AstraMilitarum/Psykers" value="<string name='Buildings/AstraMilitarum/Psykers'/>"/>
	<entry name="AstraMilitarum/PsykersDescription" value="<string name='Buildings/AstraMilitarum/PsykersDescription'/>"/>
	<entry name="AstraMilitarum/PsykersFlavor" value="<string name='Buildings/AstraMilitarum/PsykersFlavor'/>"/>
	<entry name="AstraMilitarum/Upgrades" value="<string name='Buildings/AstraMilitarum/Upgrades'/>"/>
	<entry name="AstraMilitarum/UpgradesDescription" value="<string name='Buildings/AstraMilitarum/UpgradesDescription'/>"/>
	<entry name="AstraMilitarum/UpgradesFlavor" value="<string name='Buildings/AstraMilitarum/UpgradesFlavor'/>"/>
	<entry name="AstraMilitarum/Vehicles" value="<string name='Buildings/AstraMilitarum/Vehicles'/>"/>
	<entry name="AstraMilitarum/VehiclesDescription" value="<string name='Buildings/AstraMilitarum/VehiclesDescription'/>"/>
	<entry name="AstraMilitarum/VehiclesFlavor" value="<string name='Buildings/AstraMilitarum/VehiclesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Aircraft" value="<string name='Buildings/ChaosSpaceMarines/Aircraft'/>"/>
	<entry name="ChaosSpaceMarines/AircraftDescription" value="<string name='Buildings/ChaosSpaceMarines/AircraftDescription'/>"/>
	<entry name="ChaosSpaceMarines/AircraftFlavor" value="<string name='Buildings/ChaosSpaceMarines/AircraftFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Construction" value="<string name='Buildings/ChaosSpaceMarines/Construction'/>"/>
	<entry name="ChaosSpaceMarines/ConstructionDescription" value="<string name='Buildings/ChaosSpaceMarines/ConstructionDescription'/>"/>
	<entry name="ChaosSpaceMarines/ConstructionFlavor" value="<string name='Buildings/ChaosSpaceMarines/ConstructionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Heroes" value="<string name='Buildings/ChaosSpaceMarines/Heroes'/>"/>
	<entry name="ChaosSpaceMarines/HeroesDescription" value="<string name='Buildings/ChaosSpaceMarines/HeroesDescription'/>"/>
	<entry name="ChaosSpaceMarines/HeroesFlavor" value="<string name='Buildings/ChaosSpaceMarines/HeroesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Housing" value="<string name='Buildings/ChaosSpaceMarines/Housing'/>"/>
	<entry name="ChaosSpaceMarines/HousingDescription" value="<string name='Buildings/ChaosSpaceMarines/HousingDescription'/>"/>
	<entry name="ChaosSpaceMarines/HousingFlavor" value="<string name='Buildings/ChaosSpaceMarines/HousingFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Infantry" value="<string name='Buildings/ChaosSpaceMarines/Infantry'/>"/>
	<entry name="ChaosSpaceMarines/InfantryDescription" value="<string name='Buildings/ChaosSpaceMarines/InfantryDescription'/>"/>
	<entry name="ChaosSpaceMarines/InfantryFlavor" value="<string name='Buildings/ChaosSpaceMarines/InfantryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Loyalty" value="<string name='Buildings/ChaosSpaceMarines/Loyalty'/>"/>
	<entry name="ChaosSpaceMarines/LoyaltyDescription" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyDescription'/>"/>
	<entry name="ChaosSpaceMarines/LoyaltyFlavor" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Vehicles" value="<string name='Buildings/ChaosSpaceMarines/Vehicles'/>"/>
	<entry name="ChaosSpaceMarines/VehiclesDescription" value="<string name='Buildings/ChaosSpaceMarines/VehiclesDescription'/>"/>
	<entry name="ChaosSpaceMarines/VehiclesFlavor" value="<string name='Buildings/ChaosSpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Drukhari/Aircraft" value="<string name='Buildings/Drukhari/Aircraft'/>"/>
	<entry name="Drukhari/AircraftDescription" value="<string name='Buildings/Drukhari/AircraftDescription'/>"/>
	<entry name="Drukhari/AircraftFlavor" value="<string name='Buildings/Drukhari/AircraftFlavor'/>"/>
	<entry name="Drukhari/Construction" value="<string name='Buildings/Drukhari/Construction'/>"/>
	<entry name="Drukhari/ConstructionDescription" value="<string name='Buildings/Drukhari/ConstructionDescription'/>"/>
	<entry name="Drukhari/ConstructionFlavor" value="<string name='Buildings/Drukhari/ConstructionFlavor'/>"/>
	<entry name="Drukhari/Heroes" value="<string name='Buildings/Drukhari/Heroes'/>"/>
	<entry name="Drukhari/HeroesDescription" value="<string name='Buildings/Drukhari/HeroesDescription'/>"/>
	<entry name="Drukhari/HeroesFlavor" value="<string name='Buildings/Drukhari/HeroesFlavor'/>"/>
	<entry name="Drukhari/Housing" value="<string name='Buildings/Drukhari/Housing'/>"/>
	<entry name="Drukhari/HousingDescription" value="<string name='Buildings/Drukhari/HousingDescription'/>"/>
	<entry name="Drukhari/HousingFlavor" value="<string name='Buildings/Drukhari/HousingFlavor'/>"/>
	<entry name="Drukhari/Loyalty" value="<string name='Buildings/Drukhari/Loyalty'/>"/>
	<entry name="Drukhari/LoyaltyDescription" value="<string name='Buildings/Drukhari/LoyaltyDescription'/>"/>
	<entry name="Drukhari/LoyaltyFlavor" value="<string name='Buildings/Drukhari/LoyaltyFlavor'/>"/>
	<entry name="Drukhari/Vehicles" value="<string name='Buildings/Drukhari/Vehicles'/>"/>
	<entry name="Drukhari/VehiclesDescription" value="<string name='Buildings/Drukhari/VehiclesDescription'/>"/>
	<entry name="Drukhari/VehiclesFlavor" value="<string name='Buildings/Drukhari/VehiclesFlavor'/>"/>
	<entry name="Eldar/Aircraft" value="<string name='Buildings/Eldar/Aircraft'/>"/>
	<entry name="Eldar/AircraftDescription" value="<string name='Buildings/Eldar/AircraftDescription'/>"/>
	<entry name="Eldar/AircraftFlavor" value="<string name='Buildings/Eldar/AircraftFlavor'/>"/>
	<entry name="Eldar/Construction" value="<string name='Buildings/Eldar/Construction'/>"/>
	<entry name="Eldar/ConstructionDescription" value="<string name='Buildings/Eldar/ConstructionDescription'/>"/>
	<entry name="Eldar/ConstructionFlavor" value="<string name='Buildings/Eldar/ConstructionFlavor'/>"/>
	<entry name="Eldar/Heroes" value="<string name='Buildings/Eldar/Heroes'/>"/>
	<entry name="Eldar/HeroesDescription" value="<string name='Buildings/Eldar/HeroesDescription'/>"/>
	<entry name="Eldar/HeroesFlavor" value="<string name='Buildings/Eldar/HeroesFlavor'/>"/>
	<entry name="Eldar/Housing" value="<string name='Buildings/Eldar/Housing'/>"/>
	<entry name="Eldar/HousingDescription" value="<string name='Buildings/Eldar/HousingDescription'/>"/>
	<entry name="Eldar/HousingFlavor" value="<string name='Buildings/Eldar/HousingFlavor'/>"/>
	<entry name="Eldar/Infantry" value="<string name='Buildings/Eldar/Infantry'/>"/>
	<entry name="Eldar/InfantryDescription" value="<string name='Buildings/Eldar/InfantryDescription'/>"/>
	<entry name="Eldar/InfantryFlavor" value="<string name='Buildings/Eldar/InfantryFlavor'/>"/>
	<entry name="Eldar/Loyalty" value="<string name='Buildings/Eldar/Loyalty'/>"/>
	<entry name="Eldar/LoyaltyDescription" value="<string name='Buildings/Eldar/LoyaltyDescription'/>"/>
	<entry name="Eldar/LoyaltyFlavor" value="<string name='Buildings/Eldar/LoyaltyFlavor'/>"/>
	<entry name="Eldar/Vehicles" value="<string name='Buildings/Eldar/Vehicles'/>"/>
	<entry name="Eldar/VehiclesDescription" value="<string name='Buildings/Eldar/VehiclesDescription'/>"/>
	<entry name="Eldar/VehiclesFlavor" value="<string name='Buildings/Eldar/VehiclesFlavor'/>"/>
	<entry name="Necrons/Aircraft" value="<string name='Buildings/Necrons/Aircraft'/>"/>
	<entry name="Necrons/AircraftDescription" value="<string name='Buildings/Necrons/AircraftDescription'/>"/>
	<entry name="Necrons/AircraftFlavor" value="<string name='Buildings/Necrons/AircraftFlavor'/>"/>
	<entry name="Necrons/Construction" value="<string name='Buildings/Necrons/Construction'/>"/>
	<entry name="Necrons/ConstructionDescription" value="<string name='Buildings/Necrons/ConstructionDescription'/>"/>
	<entry name="Necrons/ConstructionFlavor" value="<string name='Buildings/Necrons/ConstructionFlavor'/>"/>
	<entry name="Necrons/Heroes" value="<string name='Buildings/Necrons/Heroes'/>"/>
	<entry name="Necrons/HeroesDescription" value="<string name='Buildings/Necrons/HeroesDescription'/>"/>
	<entry name="Necrons/HeroesFlavor" value="<string name='Buildings/Necrons/HeroesFlavor'/>"/>
	<entry name="Necrons/Housing" value="<string name='Buildings/Necrons/Housing'/>"/>
	<entry name="Necrons/HousingDescription" value="<string name='Buildings/Necrons/HousingDescription'/>"/>
	<entry name="Necrons/HousingFlavor" value="<string name='Buildings/Necrons/HousingFlavor'/>"/>
	<entry name="Necrons/Loyalty" value="<string name='Buildings/Necrons/Loyalty'/>"/>
	<entry name="Necrons/LoyaltyDescription" value="<string name='Buildings/Necrons/LoyaltyDescription'/>"/>
	<entry name="Necrons/LoyaltyFlavor" value="<string name='Buildings/Necrons/LoyaltyFlavor'/>"/>
	<entry name="Necrons/Vehicles" value="<string name='Buildings/Necrons/Vehicles'/>"/>
	<entry name="Necrons/VehiclesDescription" value="<string name='Buildings/Necrons/VehiclesDescription'/>"/>
	<entry name="Necrons/VehiclesFlavor" value="<string name='Buildings/Necrons/VehiclesFlavor'/>"/>
	<entry name="Orks/Beasts" value="<string name='Buildings/Orks/Beasts'/>"/>
	<entry name="Orks/BeastsDescription" value="<string name='Buildings/Orks/BeastsDescription'/>"/>
	<entry name="Orks/BeastsFlavor" value="<string name='Buildings/Orks/BeastsFlavor'/>"/>
	<entry name="Orks/Colonizers" value="<string name='Buildings/Orks/Colonizers'/>"/>
	<entry name="Orks/ColonizersDescription" value="<string name='Buildings/Orks/ColonizersDescription'/>"/>
	<entry name="Orks/ColonizersFlavor" value="<string name='Buildings/Orks/ColonizersFlavor'/>"/>
	<entry name="Orks/Construction" value="<string name='Buildings/Orks/Construction'/>"/>
	<entry name="Orks/ConstructionDescription" value="<string name='Buildings/Orks/ConstructionDescription'/>"/>
	<entry name="Orks/ConstructionFlavor" value="<string name='Buildings/Orks/ConstructionFlavor'/>"/>
	<entry name="Orks/Heroes" value="<string name='Buildings/Orks/Heroes'/>"/>
	<entry name="Orks/HeroesDescription" value="<string name='Buildings/Orks/HeroesDescription'/>"/>
	<entry name="Orks/HeroesFlavor" value="<string name='Buildings/Orks/HeroesFlavor'/>"/>
	<entry name="Orks/Housing" value="<string name='Buildings/Orks/Housing'/>"/>
	<entry name="Orks/HousingDescription" value="<string name='Buildings/Orks/HousingDescription'/>"/>
	<entry name="Orks/HousingFlavor" value="<string name='Buildings/Orks/HousingFlavor'/>"/>
	<entry name="Orks/Loyalty" value="<string name='Buildings/Orks/Loyalty'/>"/>
	<entry name="Orks/LoyaltyDescription" value="<string name='Buildings/Orks/LoyaltyDescription'/>"/>
	<entry name="Orks/LoyaltyFlavor" value="<string name='Buildings/Orks/LoyaltyFlavor'/>"/>
	<entry name="Orks/Vehicles" value="<string name='Buildings/Orks/Vehicles'/>"/>
	<entry name="Orks/VehiclesDescription" value="<string name='Buildings/Orks/VehiclesDescription'/>"/>
	<entry name="Orks/VehiclesFlavor" value="<string name='Buildings/Orks/VehiclesFlavor'/>"/>
	<entry name="SistersOfBattle/Auxiliaries" value="<string name='Buildings/SistersOfBattle/Auxiliaries'/>"/>
	<entry name="SistersOfBattle/AuxiliariesDescription" value="<string name='Buildings/SistersOfBattle/AuxiliariesDescription'/>"/>
	<entry name="SistersOfBattle/AuxiliariesFlavor" value="<string name='Buildings/SistersOfBattle/AuxiliariesFlavor'/>"/>
	<entry name="SistersOfBattle/Construction" value="<string name='Buildings/SistersOfBattle/Construction'/>"/>
	<entry name="SistersOfBattle/ConstructionDescription" value="<string name='Buildings/SistersOfBattle/ConstructionDescription'/>"/>
	<entry name="SistersOfBattle/ConstructionFlavor" value="<string name='Buildings/SistersOfBattle/ConstructionFlavor'/>"/>
	<entry name="SistersOfBattle/Heroes" value="<string name='Buildings/SistersOfBattle/Heroes'/>"/>
	<entry name="SistersOfBattle/HeroesDescription" value="<string name='Buildings/SistersOfBattle/HeroesDescription'/>"/>
	<entry name="SistersOfBattle/HeroesFlavor" value="<string name='Buildings/SistersOfBattle/HeroesFlavor'/>"/>
	<entry name="SistersOfBattle/Housing" value="<string name='Buildings/SistersOfBattle/Housing'/>"/>
	<entry name="SistersOfBattle/HousingDescription" value="<string name='Buildings/SistersOfBattle/HousingDescription'/>"/>
	<entry name="SistersOfBattle/HousingFlavor" value="<string name='Buildings/SistersOfBattle/HousingFlavor'/>"/>
	<entry name="SistersOfBattle/Loyalty" value="<string name='Buildings/SistersOfBattle/Loyalty'/>"/>
	<entry name="SistersOfBattle/LoyaltyDescription" value="<string name='Buildings/SistersOfBattle/LoyaltyDescription'/>"/>
	<entry name="SistersOfBattle/LoyaltyFlavor" value="<string name='Buildings/SistersOfBattle/LoyaltyFlavor'/>"/>
	<entry name="SistersOfBattle/Vehicles" value="<string name='Buildings/SistersOfBattle/Vehicles'/>"/>
	<entry name="SistersOfBattle/VehiclesDescription" value="<string name='Buildings/SistersOfBattle/VehiclesDescription'/>"/>
	<entry name="SistersOfBattle/VehiclesFlavor" value="<string name='Buildings/SistersOfBattle/VehiclesFlavor'/>"/>
	<entry name="SpaceMarines/Aircraft" value="<string name='Buildings/SpaceMarines/Aircraft'/>"/>
	<entry name="SpaceMarines/AircraftDescription" value="<string name='Buildings/SpaceMarines/AircraftDescription'/>"/>
	<entry name="SpaceMarines/AircraftFlavor" value="<string name='Buildings/SpaceMarines/AircraftFlavor'/>"/>
	<entry name="SpaceMarines/Construction" value="<string name='Buildings/SpaceMarines/Construction'/>"/>
	<entry name="SpaceMarines/ConstructionDescription" value="<string name='Buildings/SpaceMarines/ConstructionDescription'/>"/>
	<entry name="SpaceMarines/ConstructionFlavor" value="<string name='Buildings/SpaceMarines/ConstructionFlavor'/>"/>
	<entry name="SpaceMarines/GeneseedBunker" value="<string name='Buildings/SpaceMarines/GeneseedBunker'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerDescription" value="<string name='Buildings/SpaceMarines/GeneseedBunkerDescription'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerFlavor" value="<string name='Buildings/SpaceMarines/GeneseedBunkerFlavor'/>"/>
	<entry name="SpaceMarines/Heroes" value="<string name='Buildings/SpaceMarines/Heroes'/>"/>
	<entry name="SpaceMarines/HeroesDescription" value="<string name='Buildings/SpaceMarines/HeroesDescription'/>"/>
	<entry name="SpaceMarines/HeroesFlavor" value="<string name='Buildings/SpaceMarines/HeroesFlavor'/>"/>
	<entry name="SpaceMarines/Housing" value="<string name='Buildings/SpaceMarines/Housing'/>"/>
	<entry name="SpaceMarines/HousingDescription" value="<string name='Buildings/SpaceMarines/HousingDescription'/>"/>
	<entry name="SpaceMarines/HousingFlavor" value="<string name='Buildings/SpaceMarines/HousingFlavor'/>"/>
	<entry name="SpaceMarines/Loyalty" value="<string name='Buildings/SpaceMarines/Loyalty'/>"/>
	<entry name="SpaceMarines/LoyaltyDescription" value="<string name='Buildings/SpaceMarines/LoyaltyDescription'/>"/>
	<entry name="SpaceMarines/LoyaltyFlavor" value="<string name='Buildings/SpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="SpaceMarines/Vehicles" value="<string name='Buildings/SpaceMarines/Vehicles'/>"/>
	<entry name="SpaceMarines/VehiclesDescription" value="<string name='Buildings/SpaceMarines/VehiclesDescription'/>"/>
	<entry name="SpaceMarines/VehiclesFlavor" value="<string name='Buildings/SpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Tau/Aircraft" value="<string name='Buildings/Tau/Aircraft'/>"/>
	<entry name="Tau/AircraftDescription" value="<string name='Buildings/Tau/AircraftDescription'/>"/>
	<entry name="Tau/AircraftFlavor" value="<string name='Buildings/Tau/AircraftFlavor'/>"/>
	<entry name="Tau/Construction" value="<string name='Buildings/Tau/Construction'/>"/>
	<entry name="Tau/ConstructionDescription" value="<string name='Buildings/Tau/ConstructionDescription'/>"/>
	<entry name="Tau/ConstructionFlavor" value="<string name='Buildings/Tau/ConstructionFlavor'/>"/>
	<entry name="Tau/Heroes" value="<string name='Buildings/Tau/Heroes'/>"/>
	<entry name="Tau/HeroesDescription" value="<string name='Buildings/Tau/HeroesDescription'/>"/>
	<entry name="Tau/HeroesFlavor" value="<string name='Buildings/Tau/HeroesFlavor'/>"/>
	<entry name="Tau/Housing" value="<string name='Buildings/Tau/Housing'/>"/>
	<entry name="Tau/HousingDescription" value="<string name='Buildings/Tau/HousingDescription'/>"/>
	<entry name="Tau/HousingFlavor" value="<string name='Buildings/Tau/HousingFlavor'/>"/>
	<entry name="Tau/Loyalty" value="<string name='Buildings/Tau/Loyalty'/>"/>
	<entry name="Tau/LoyaltyDescription" value="<string name='Buildings/Tau/LoyaltyDescription'/>"/>
	<entry name="Tau/LoyaltyFlavor" value="<string name='Buildings/Tau/LoyaltyFlavor'/>"/>
	<entry name="Tau/MonstrousCreatures" value="<string name='Buildings/Tau/MonstrousCreatures'/>"/>
	<entry name="Tau/MonstrousCreaturesDescription" value="<string name='Buildings/Tau/MonstrousCreaturesDescription'/>"/>
	<entry name="Tau/MonstrousCreaturesFlavor" value="<string name='Buildings/Tau/MonstrousCreaturesFlavor'/>"/>
	<entry name="Tau/Vehicles" value="<string name='Buildings/Tau/Vehicles'/>"/>
	<entry name="Tau/VehiclesDescription" value="<string name='Buildings/Tau/VehiclesDescription'/>"/>
	<entry name="Tau/VehiclesFlavor" value="<string name='Buildings/Tau/VehiclesFlavor'/>"/>
	<entry name="Tyranids/Aircraft" value="<string name='Buildings/Tyranids/Aircraft'/>"/>
	<entry name="Tyranids/AircraftDescription" value="<string name='Buildings/Tyranids/AircraftDescription'/>"/>
	<entry name="Tyranids/AircraftFlavor" value="<string name='Buildings/Tyranids/AircraftFlavor'/>"/>
	<entry name="Tyranids/Construction" value="<string name='Buildings/Tyranids/Construction'/>"/>
	<entry name="Tyranids/ConstructionDescription" value="<string name='Buildings/Tyranids/ConstructionDescription'/>"/>
	<entry name="Tyranids/ConstructionFlavor" value="<string name='Buildings/Tyranids/ConstructionFlavor'/>"/>
	<entry name="Tyranids/Heroes" value="<string name='Buildings/Tyranids/Heroes'/>"/>
	<entry name="Tyranids/HeroesDescription" value="<string name='Buildings/Tyranids/HeroesDescription'/>"/>
	<entry name="Tyranids/HeroesFlavor" value="<string name='Buildings/Tyranids/HeroesFlavor'/>"/>
	<entry name="Tyranids/Housing" value="<string name='Buildings/Tyranids/Housing'/>"/>
	<entry name="Tyranids/HousingDescription" value="<string name='Buildings/Tyranids/HousingDescription'/>"/>
	<entry name="Tyranids/HousingFlavor" value="<string name='Buildings/Tyranids/HousingFlavor'/>"/>
	<entry name="Tyranids/Loyalty" value="<string name='Buildings/Tyranids/Loyalty'/>"/>
	<entry name="Tyranids/LoyaltyDescription" value="<string name='Buildings/Tyranids/LoyaltyDescription'/>"/>
	<entry name="Tyranids/LoyaltyFlavor" value="<string name='Buildings/Tyranids/LoyaltyFlavor'/>"/>
	<entry name="Tyranids/Thropes" value="<string name='Buildings/Tyranids/Thropes'/>"/>
	<entry name="Tyranids/ThropesDescription" value="<string name='Buildings/Tyranids/ThropesDescription'/>"/>
	<entry name="Tyranids/ThropesFlavor" value="<string name='Buildings/Tyranids/ThropesFlavor'/>"/>
	<entry name="Tyranids/Vehicles" value="<string name='Buildings/Tyranids/Vehicles'/>"/>
	<entry name="Tyranids/VehiclesDescription" value="<string name='Buildings/Tyranids/VehiclesDescription'/>"/>
	<entry name="Tyranids/VehiclesFlavor" value="<string name='Buildings/Tyranids/VehiclesFlavor'/>"/>

	<!-- Edicts -->
	<entry name="AstraMilitarum/AircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictDescription" value="<string name='Actions/AstraMilitarumAircraftProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/DefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarum/DefenseEdictDescription" value="<string name='Actions/AstraMilitarumDefenseEdictDescription'/>"/>
	<entry name="AstraMilitarum/DefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarum/EnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarum/EnergyEdictDescription" value="<string name='Actions/AstraMilitarumEnergyEdictDescription'/>"/>
	<entry name="AstraMilitarum/EnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/FoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarum/FoodEdictDescription" value="<string name='Actions/AstraMilitarumFoodEdictDescription'/>"/>
	<entry name="AstraMilitarum/FoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarum/GrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarum/GrowthEdictDescription" value="<string name='Actions/AstraMilitarumGrowthEdictDescription'/>"/>
	<entry name="AstraMilitarum/GrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictDescription" value="<string name='Actions/AstraMilitarumInfantryProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictDescription" value="<string name='Actions/AstraMilitarumLoyaltyEdictDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/OreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarum/OreEdictDescription" value="<string name='Actions/AstraMilitarumOreEdictDescription'/>"/>
	<entry name="AstraMilitarum/OreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictDescription" value="<string name='Actions/AstraMilitarumPsykerProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/ResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarum/ResearchEdictDescription" value="<string name='Actions/AstraMilitarumResearchEdictDescription'/>"/>
	<entry name="AstraMilitarum/ResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictDescription" value="<string name='Actions/AstraMilitarumVehicleProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>

	<!-- Units -->
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptor'/>"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorDescription" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorDescription'/>"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorFlavor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorFlavor'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriest'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestDescription" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestDescription'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestFlavor" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestFlavor'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobot" value="<string name='Units/Neutral/KastelanRobot'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobotDescription" value="<string name='Units/Neutral/KastelanRobotDescription'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobotFlavor" value="<string name='Units/Neutral/KastelanRobotFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacher" value="<string name='Units/AdeptusMechanicus/KataphronBreacher'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherDescription" value="<string name='Units/AdeptusMechanicus/KataphronBreacherDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherFlavor" value="<string name='Units/AdeptusMechanicus/KataphronBreacherFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer" value="<string name='Units/AdeptusMechanicus/KataphronDestroyer'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerDescription" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerFlavor" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerFlavor'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusader" value="<string name='Units/AdeptusMechanicus/KnightCrusader'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusaderDescription" value="<string name='Units/AdeptusMechanicus/KnightCrusaderDescription'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusaderFlavor" value="<string name='Units/AdeptusMechanicus/KnightCrusaderFlavor'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawler'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerDescription" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerDescription'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerFlavor" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerFlavor'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizor'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorDescription" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorDescription'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorFlavor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhound'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundDescription" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundDescription'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundFlavor" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator" value="<string name='Units/AdeptusMechanicus/SicarianInfiltrator'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorDescription" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorDescription'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorFlavor" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker" value="<string name='Units/AdeptusMechanicus/SicarianRuststalker'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerDescription" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerDescription'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerFlavor" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal" value="<string name='Units/AdeptusMechanicus/SkitariiMarshal'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalDescription" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="<string name='Units/AdeptusMechanicus/SkitariiRanger'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerDescription" value="<string name='Units/AdeptusMechanicus/SkitariiRangerDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiRangerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegrator'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorDescription'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDunerider" value="<string name='Units/AdeptusMechanicus/SkorpiusDunerider'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderDescription'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderFlavor'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoon" value="<string name='Units/AdeptusMechanicus/SydonianDragoon'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoonDescription" value="<string name='Units/AdeptusMechanicus/SydonianDragoonDescription'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoonFlavor" value="<string name='Units/AdeptusMechanicus/SydonianDragoonFlavor'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominus" value="<string name='Units/AdeptusMechanicus/TechPriestDominus'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestDominusDescription'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestDominusFlavor'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus" value="<string name='Units/AdeptusMechanicus/TechPriestManipulus'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusDescription'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusFlavor'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRider" value="<string name='Units/AstraMilitarum/AttilanRoughRider'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderDescription" value="<string name='Units/AstraMilitarum/AttilanRoughRiderDescription'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderFlavor" value="<string name='Units/AstraMilitarum/AttilanRoughRiderFlavor'/>"/>
	<entry name="AstraMilitarum/Baneblade" value="<string name='Units/AstraMilitarum/Baneblade'/>"/>
	<entry name="AstraMilitarum/BanebladeDescription" value="<string name='Units/AstraMilitarum/BanebladeDescription'/>"/>
	<entry name="AstraMilitarum/BanebladeFlavor" value="<string name='Units/AstraMilitarum/BanebladeFlavor'/>"/>
	<entry name="AstraMilitarum/Basilisk" value="<string name='Units/AstraMilitarum/Basilisk'/>"/>
	<entry name="AstraMilitarum/BasiliskDescription" value="<string name='Units/AstraMilitarum/BasiliskDescription'/>"/>
	<entry name="AstraMilitarum/BasiliskFlavor" value="<string name='Units/AstraMilitarum/BasiliskFlavor'/>"/>
	<entry name="AstraMilitarum/Bullgryn" value="<string name='Units/AstraMilitarum/Bullgryn'/>"/>
	<entry name="AstraMilitarum/BullgrynDescription" value="<string name='Units/AstraMilitarum/BullgrynDescription'/>"/>
	<entry name="AstraMilitarum/BullgrynFlavor" value="<string name='Units/AstraMilitarum/BullgrynFlavor'/>"/>
	<entry name="AstraMilitarum/Chimera" value="<string name='Units/AstraMilitarum/Chimera'/>"/>
	<entry name="AstraMilitarum/ChimeraDescription" value="<string name='Units/AstraMilitarum/ChimeraDescription'/>"/>
	<entry name="AstraMilitarum/ChimeraFlavor" value="<string name='Units/AstraMilitarum/ChimeraFlavor'/>"/>
	<entry name="AstraMilitarum/DevilDog" value="<string name='Units/AstraMilitarum/DevilDog'/>"/>
	<entry name="AstraMilitarum/DevilDogDescription" value="<string name='Units/AstraMilitarum/DevilDogDescription'/>"/>
	<entry name="AstraMilitarum/DevilDogFlavor" value="<string name='Units/AstraMilitarum/DevilDogFlavor'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery" value="<string name='Units/AstraMilitarum/FieldOrdnanceBattery'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryDescription" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryDescription'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryFlavor" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryFlavor'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquad'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadDescription" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadDescription'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadFlavor" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadFlavor'/>"/>
	<entry name="AstraMilitarum/Hydra" value="<string name='Units/AstraMilitarum/Hydra'/>"/>
	<entry name="AstraMilitarum/HydraDescription" value="<string name='Units/AstraMilitarum/HydraDescription'/>"/>
	<entry name="AstraMilitarum/HydraFlavor" value="<string name='Units/AstraMilitarum/HydraFlavor'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTank" value="<string name='Units/AstraMilitarum/LemanRussBattleTank'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankDescription" value="<string name='Units/AstraMilitarum/LemanRussBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankFlavor" value="<string name='Units/AstraMilitarum/LemanRussBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/LordCommissar" value="<string name='Units/AstraMilitarum/LordCommissar'/>"/>
	<entry name="AstraMilitarum/LordCommissarDescription" value="<string name='Units/AstraMilitarum/LordCommissarDescription'/>"/>
	<entry name="AstraMilitarum/LordCommissarFlavor" value="<string name='Units/AstraMilitarum/LordCommissarFlavor'/>"/>
	<entry name="AstraMilitarum/MarauderBomber" value="<string name='Units/AstraMilitarum/MarauderBomber'/>"/>
	<entry name="AstraMilitarum/MarauderBomberDescription" value="<string name='Units/AstraMilitarum/MarauderBomberDescription'/>"/>
	<entry name="AstraMilitarum/MarauderBomberFlavor" value="<string name='Units/AstraMilitarum/MarauderBomberFlavor'/>"/>
	<entry name="AstraMilitarum/PrimarisPsyker" value="<string name='Units/AstraMilitarum/PrimarisPsyker'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerDescription" value="<string name='Units/AstraMilitarum/PrimarisPsykerDescription'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerFlavor" value="<string name='Units/AstraMilitarum/PrimarisPsykerFlavor'/>"/>
	<entry name="AstraMilitarum/Ratling" value="<string name='Units/AstraMilitarum/Ratling'/>"/>
	<entry name="AstraMilitarum/RatlingDescription" value="<string name='Units/AstraMilitarum/RatlingDescription'/>"/>
	<entry name="AstraMilitarum/RatlingFlavor" value="<string name='Units/AstraMilitarum/RatlingFlavor'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTank" value="<string name='Units/AstraMilitarum/RogalDornBattleTank'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTankDescription" value="<string name='Units/AstraMilitarum/RogalDornBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTankFlavor" value="<string name='Units/AstraMilitarum/RogalDornBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/ScoutSentinel" value="<string name='Units/AstraMilitarum/ScoutSentinel'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelDescription" value="<string name='Units/AstraMilitarum/ScoutSentinelDescription'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelFlavor" value="<string name='Units/AstraMilitarum/ScoutSentinelFlavor'/>"/>
	<entry name="AstraMilitarum/TankCommander" value="<string name='Units/AstraMilitarum/TankCommander'/>"/>
	<entry name="AstraMilitarum/TankCommanderDescription" value="<string name='Units/AstraMilitarum/TankCommanderDescription'/>"/>
	<entry name="AstraMilitarum/TankCommanderFlavor" value="<string name='Units/AstraMilitarum/TankCommanderFlavor'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseer" value="<string name='Units/AstraMilitarum/TechpriestEnginseer'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerDescription" value="<string name='Units/AstraMilitarum/TechpriestEnginseerDescription'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerFlavor" value="<string name='Units/AstraMilitarum/TechpriestEnginseerFlavor'/>"/>
	<entry name="AstraMilitarum/TempestusScion" value="<string name='Units/AstraMilitarum/TempestusScion'/>"/>
	<entry name="AstraMilitarum/TempestusScionDescription" value="<string name='Units/AstraMilitarum/TempestusScionDescription'/>"/>
	<entry name="AstraMilitarum/TempestusScionFlavor" value="<string name='Units/AstraMilitarum/TempestusScionFlavor'/>"/>
	<entry name="AstraMilitarum/Thunderbolt" value="<string name='Units/AstraMilitarum/Thunderbolt'/>"/>
	<entry name="AstraMilitarum/ThunderboltDescription" value="<string name='Units/AstraMilitarum/ThunderboltDescription'/>"/>
	<entry name="AstraMilitarum/ThunderboltFlavor" value="<string name='Units/AstraMilitarum/ThunderboltFlavor'/>"/>
	<entry name="AstraMilitarum/Valkyrie" value="<string name='Units/AstraMilitarum/Valkyrie'/>"/>
	<entry name="AstraMilitarum/ValkyrieDescription" value="<string name='Units/AstraMilitarum/ValkyrieDescription'/>"/>
	<entry name="AstraMilitarum/ValkyrieFlavor" value="<string name='Units/AstraMilitarum/ValkyrieFlavor'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsyker" value="<string name='Units/AstraMilitarum/WyrdvanePsyker'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerDescription" value="<string name='Units/AstraMilitarum/WyrdvanePsykerDescription'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerFlavor" value="<string name='Units/AstraMilitarum/WyrdvanePsykerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaider'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderDescription" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn" value="<string name='Units/ChaosSpaceMarines/ChaosSpawn'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnDescription" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="<string name='Units/ChaosSpaceMarines/ChaosTerminator'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorDescription" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrince" value="<string name='Units/ChaosSpaceMarines/DaemonPrince'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceDescription" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceDescription'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceFlavor" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DarkDisciple" value="<string name='Units/ChaosSpaceMarines/DarkDisciple'/>"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleDescription" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleDescription'/>"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleFlavor" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Defiler" value="<string name='Units/ChaosSpaceMarines/Defiler'/>"/>
	<entry name="ChaosSpaceMarines/DefilerDescription" value="<string name='Units/ChaosSpaceMarines/DefilerDescription'/>"/>
	<entry name="ChaosSpaceMarines/DefilerFlavor" value="<string name='Units/ChaosSpaceMarines/DefilerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Havoc" value="<string name='Units/ChaosSpaceMarines/Havoc'/>"/>
	<entry name="ChaosSpaceMarines/HavocDescription" value="<string name='Units/ChaosSpaceMarines/HavocDescription'/>"/>
	<entry name="ChaosSpaceMarines/HavocFlavor" value="<string name='Units/ChaosSpaceMarines/HavocFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Helbrute" value="<string name='Units/ChaosSpaceMarines/Helbrute'/>"/>
	<entry name="ChaosSpaceMarines/HelbruteDescription" value="<string name='Units/ChaosSpaceMarines/HelbruteDescription'/>"/>
	<entry name="ChaosSpaceMarines/HelbruteFlavor" value="<string name='Units/ChaosSpaceMarines/HelbruteFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerker'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerDescription" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerDescription'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerFlavor" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="<string name='Units/ChaosSpaceMarines/Forgefiend'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendDescription" value="<string name='Units/ChaosSpaceMarines/ForgefiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendFlavor" value="<string name='Units/ChaosSpaceMarines/ForgefiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession" value="<string name='Units/ChaosSpaceMarines/MasterOfPossession'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionDescription" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionDescription'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionFlavor" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Maulerfiend" value="<string name='Units/ChaosSpaceMarines/Maulerfiend'/>"/>
	<entry name="ChaosSpaceMarines/MaulerfiendDescription" value="<string name='Units/ChaosSpaceMarines/MaulerfiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/MaulerfiendFlavor" value="<string name='Units/ChaosSpaceMarines/MaulerfiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrown" value="<string name='Units/ChaosSpaceMarines/NoctilithCrown'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrownDescription" value="Ermöglicht es Chaos Cultists, Befestigungen zu errichten."/>
	<entry name="ChaosSpaceMarines/NoctilithCrownFlavor" value="<string name='Units/ChaosSpaceMarines/NoctilithCrownFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Obliterator" value="<string name='Units/ChaosSpaceMarines/Obliterator'/>"/>
	<entry name="ChaosSpaceMarines/ObliteratorDescription" value="<string name='Units/ChaosSpaceMarines/ObliteratorDescription'/>"/>
	<entry name="ChaosSpaceMarines/ObliteratorFlavor" value="<string name='Units/ChaosSpaceMarines/ObliteratorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarine" value="<string name='Units/ChaosSpaceMarines/RubricMarine'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineDescription" value="<string name='Units/ChaosSpaceMarines/RubricMarineDescription'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineFlavor" value="<string name='Units/ChaosSpaceMarines/RubricMarineFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Venomcrawler" value="<string name='Units/ChaosSpaceMarines/Venomcrawler'/>"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerDescription" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerDescription'/>"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerFlavor" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Warpsmith" value="<string name='Units/ChaosSpaceMarines/Warpsmith'/>"/>
	<entry name="ChaosSpaceMarines/WarpsmithDescription" value="<string name='Units/ChaosSpaceMarines/WarpsmithDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpsmithFlavor" value="<string name='Units/ChaosSpaceMarines/WarpsmithFlavor'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalon" value="<string name='Units/ChaosSpaceMarines/WarpTalon'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonDescription" value="<string name='Units/ChaosSpaceMarines/WarpTalonDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonFlavor" value="<string name='Units/ChaosSpaceMarines/WarpTalonFlavor'/>"/>
	<entry name="Drukhari/Cronos" value="<string name='Units/Drukhari/Cronos'/>"/>
	<entry name="Drukhari/CronosDescription" value="<string name='Units/Drukhari/CronosDescription'/>"/>
	<entry name="Drukhari/CronosFlavor" value="<string name='Units/Drukhari/CronosFlavor'/>"/>
	<entry name="Drukhari/Haemonculus" value="<string name='Units/Drukhari/Haemonculus'/>"/>
	<entry name="Drukhari/HaemonculusDescription" value="<string name='Units/Drukhari/HaemonculusDescription'/>"/>
	<entry name="Drukhari/HaemonculusFlavor" value="<string name='Units/Drukhari/HaemonculusFlavor'/>"/>
	<entry name="Drukhari/Hellion" value="<string name='Units/Drukhari/Hellion'/>"/>
	<entry name="Drukhari/HellionDescription" value="<string name='Units/Drukhari/HellionDescription'/>"/>
	<entry name="Drukhari/HellionFlavor" value="<string name='Units/Drukhari/HellionFlavor'/>"/>
	<entry name="Drukhari/Incubi" value="<string name='Units/Drukhari/Incubi'/>"/>
	<entry name="Drukhari/IncubiDescription" value="<string name='Units/Drukhari/IncubiDescription'/>"/>
	<entry name="Drukhari/IncubiFlavor" value="<string name='Units/Drukhari/IncubiFlavor'/>"/>
	<entry name="Drukhari/KabaliteTrueborn" value="<string name='Units/Drukhari/KabaliteTrueborn'/>"/>
	<entry name="Drukhari/KabaliteTruebornDescription" value="<string name='Units/Drukhari/KabaliteTruebornDescription'/>"/>
	<entry name="Drukhari/KabaliteTruebornFlavor" value="<string name='Units/Drukhari/KabaliteTruebornFlavor'/>"/>
	<entry name="Drukhari/Mandrake" value="<string name='Units/Drukhari/Mandrake'/>"/>
	<entry name="Drukhari/MandrakeDescription" value="<string name='Units/Drukhari/MandrakeDescription'/>"/>
	<entry name="Drukhari/MandrakeFlavor" value="<string name='Units/Drukhari/MandrakeFlavor'/>"/>
	<entry name="Drukhari/Raider" value="<string name='Units/Drukhari/Raider'/>"/>
	<entry name="Drukhari/RaiderDescription" value="<string name='Units/Drukhari/RaiderDescription'/>"/>
	<entry name="Drukhari/RaiderFlavor" value="<string name='Units/Drukhari/RaiderFlavor'/>"/>
	<entry name="Drukhari/Ravager" value="<string name='Units/Drukhari/Ravager'/>"/>
	<entry name="Drukhari/RavagerDescription" value="<string name='Units/Drukhari/RavagerDescription'/>"/>
	<entry name="Drukhari/RavagerFlavor" value="<string name='Units/Drukhari/RavagerFlavor'/>"/>
	<entry name="Drukhari/Reaver" value="<string name='Units/Drukhari/Reaver'/>"/>
	<entry name="Drukhari/ReaverDescription" value="<string name='Units/Drukhari/ReaverDescription'/>"/>
	<entry name="Drukhari/ReaverFlavor" value="<string name='Units/Drukhari/ReaverFlavor'/>"/>
	<entry name="Drukhari/Scourge" value="<string name='Units/Drukhari/Scourge'/>"/>
	<entry name="Drukhari/ScourgeDescription" value="<string name='Units/Drukhari/ScourgeDescription'/>"/>
	<entry name="Drukhari/ScourgeFlavor" value="<string name='Units/Drukhari/ScourgeFlavor'/>"/>
	<entry name="Drukhari/Succubus" value="<string name='Units/Drukhari/Succubus'/>"/>
	<entry name="Drukhari/SuccubusDescription" value="<string name='Units/Drukhari/SuccubusDescription'/>"/>
	<entry name="Drukhari/SuccubusFlavor" value="<string name='Units/Drukhari/SuccubusFlavor'/>"/>
	<entry name="Drukhari/Talos" value="<string name='Units/Drukhari/Talos'/>"/>
	<entry name="Drukhari/TalosDescription" value="<string name='Units/Drukhari/TalosDescription'/>"/>
	<entry name="Drukhari/TalosFlavor" value="<string name='Units/Drukhari/TalosFlavor'/>"/>
	<entry name="Drukhari/Tantalus" value="<string name='Units/Drukhari/Tantalus'/>"/>
	<entry name="Drukhari/TantalusDescription" value="<string name='Units/Drukhari/TantalusDescription'/>"/>
	<entry name="Drukhari/TantalusFlavor" value="<string name='Units/Drukhari/TantalusFlavor'/>"/>
	<entry name="Drukhari/VoidravenBomber" value="<string name='Units/Drukhari/VoidravenBomber'/>"/>
	<entry name="Drukhari/VoidravenBomberDescription" value="<string name='Units/Drukhari/VoidravenBomberDescription'/>"/>
	<entry name="Drukhari/VoidravenBomberFlavor" value="<string name='Units/Drukhari/VoidravenBomberFlavor'/>"/>
	<entry name="Drukhari/Wrack" value="<string name='Units/Drukhari/Wrack'/>"/>
	<entry name="Drukhari/WrackDescription" value="<string name='Units/Drukhari/WrackDescription'/>"/>
	<entry name="Drukhari/WrackFlavor" value="<string name='Units/Drukhari/WrackFlavor'/>"/>
	<entry name="Drukhari/Wyche" value="<string name='Units/Drukhari/Wyche'/>"/>
	<entry name="Drukhari/WycheDescription" value="<string name='Units/Drukhari/WycheDescription'/>"/>
	<entry name="Drukhari/WycheFlavor" value="<string name='Units/Drukhari/WycheFlavor'/>"/>
	<entry name="Eldar/AvatarOfKhaine" value="<string name='Units/Eldar/AvatarOfKhaine'/>"/>
	<entry name="Eldar/AvatarOfKhaineDescription" value="<string name='Units/Eldar/AvatarOfKhaineDescription'/>"/>
	<entry name="Eldar/AvatarOfKhaineFlavor" value="<string name='Units/Eldar/AvatarOfKhaineFlavor'/>"/>
	<entry name="Eldar/DarkReaper" value="<string name='Units/Eldar/DarkReaper'/>"/>
	<entry name="Eldar/DarkReaperDescription" value="<string name='Units/Eldar/DarkReaperDescription'/>"/>
	<entry name="Eldar/DarkReaperFlavor" value="<string name='Units/Eldar/DarkReaperFlavor'/>"/>
	<entry name="Eldar/FarseerSkyrunner" value="<string name='Units/Eldar/FarseerSkyrunner'/>"/>
	<entry name="Eldar/FarseerSkyrunnerDescription" value="<string name='Units/Eldar/FarseerSkyrunnerDescription'/>"/>
	<entry name="Eldar/FarseerSkyrunnerFlavor" value="<string name='Units/Eldar/FarseerSkyrunnerFlavor'/>"/>
	<entry name="Eldar/FireDragon" value="<string name='Units/Eldar/FireDragon'/>"/>
	<entry name="Eldar/FireDragonDescription" value="<string name='Units/Eldar/FireDragonDescription'/>"/>
	<entry name="Eldar/FireDragonFlavor" value="<string name='Units/Eldar/FireDragonFlavor'/>"/>
	<entry name="Eldar/FirePrism" value="<string name='Units/Eldar/FirePrism'/>"/>
	<entry name="Eldar/FirePrismDescription" value="<string name='Units/Eldar/FirePrismDescription'/>"/>
	<entry name="Eldar/FirePrismFlavor" value="<string name='Units/Eldar/FirePrismFlavor'/>"/>
	<entry name="Eldar/HemlockWraithfighter" value="<string name='Units/Eldar/HemlockWraithfighter'/>"/>
	<entry name="Eldar/HemlockWraithfighterDescription" value="<string name='Units/Eldar/HemlockWraithfighterDescription'/>"/>
	<entry name="Eldar/HemlockWraithfighterFlavor" value="<string name='Units/Eldar/HemlockWraithfighterFlavor'/>"/>
	<entry name="Eldar/Hornet" value="<string name='Units/Eldar/Hornet'/>"/>
	<entry name="Eldar/HornetDescription" value="<string name='Units/Eldar/HornetDescription'/>"/>
	<entry name="Eldar/HornetFlavor" value="<string name='Units/Eldar/HornetFlavor'/>"/>
	<entry name="Eldar/HowlingBanshee" value="<string name='Units/Eldar/HowlingBanshee'/>"/>
	<entry name="Eldar/HowlingBansheeDescription" value="<string name='Units/Eldar/HowlingBansheeDescription'/>"/>
	<entry name="Eldar/HowlingBansheeFlavor" value="<string name='Units/Eldar/HowlingBansheeFlavor'/>"/>
	<entry name="Eldar/Ranger" value="<string name='Units/Eldar/Ranger'/>"/>
	<entry name="Eldar/RangerDescription" value="<string name='Units/Eldar/RangerDescription'/>"/>
	<entry name="Eldar/RangerFlavor" value="<string name='Units/Eldar/RangerFlavor'/>"/>
	<entry name="Eldar/Scorpion" value="<string name='Units/Eldar/Scorpion'/>"/>
	<entry name="Eldar/ScorpionDescription" value="<string name='Units/Eldar/ScorpionDescription'/>"/>
	<entry name="Eldar/ScorpionFlavor" value="<string name='Units/Eldar/ScorpionFlavor'/>"/>
	<entry name="Eldar/Spiritseer" value="<string name='Units/Eldar/Spiritseer'/>"/>
	<entry name="Eldar/SpiritseerDescription" value="<string name='Units/Eldar/SpiritseerDescription'/>"/>
	<entry name="Eldar/SpiritseerFlavor" value="<string name='Units/Eldar/SpiritseerFlavor'/>"/>
	<entry name="Eldar/VaulsWrathSupportBattery" value="<string name='Units/Eldar/VaulsWrathSupportBattery'/>"/>
	<entry name="Eldar/VaulsWrathSupportBatteryDescription" value="<string name='Units/Eldar/VaulsWrathSupportBatteryDescription'/>"/>
	<entry name="Eldar/VaulsWrathSupportBatteryFlavor" value="<string name='Units/Eldar/VaulsWrathSupportBatteryFlavor'/>"/>
	<entry name="Eldar/Vyper" value="<string name='Units/Eldar/Vyper'/>"/>
	<entry name="Eldar/VyperDescription" value="<string name='Units/Eldar/VyperDescription'/>"/>
	<entry name="Eldar/VyperFlavor" value="<string name='Units/Eldar/VyperFlavor'/>"/>
	<entry name="Eldar/WarWalker" value="<string name='Units/Eldar/WarWalker'/>"/>
	<entry name="Eldar/WarWalkerDescription" value="<string name='Units/Eldar/WarWalkerDescription'/>"/>
	<entry name="Eldar/WarWalkerFlavor" value="<string name='Units/Eldar/WarWalkerFlavor'/>"/>
	<entry name="Eldar/Warlock" value="<string name='Units/Eldar/Warlock'/>"/>
	<entry name="Eldar/WarlockDescription" value="<string name='Units/Eldar/WarlockDescription'/>"/>
	<entry name="Eldar/WarlockFlavor" value="<string name='Units/Eldar/WarlockFlavor'/>"/>
	<entry name="Eldar/WaveSerpent" value="<string name='Units/Eldar/WaveSerpent'/>"/>
	<entry name="Eldar/WaveSerpentDescription" value="<string name='Units/Eldar/WaveSerpentDescription'/>"/>
	<entry name="Eldar/WaveSerpentFlavor" value="<string name='Units/Eldar/WaveSerpentFlavor'/>"/>
	<entry name="Eldar/Wraithblade" value="<string name='Units/Eldar/Wraithblade'/>"/>
	<entry name="Eldar/WraithbladeDescription" value="<string name='Units/Eldar/WraithbladeDescription'/>"/>
	<entry name="Eldar/WraithbladeFlavor" value="<string name='Units/Eldar/WraithbladeFlavor'/>"/>
	<entry name="Eldar/Wraithknight" value="<string name='Units/Eldar/Wraithknight'/>"/>
	<entry name="Eldar/WraithknightDescription" value="<string name='Units/Eldar/WraithknightDescription'/>"/>
	<entry name="Eldar/WraithknightFlavor" value="<string name='Units/Eldar/WraithknightFlavor'/>"/>
	<entry name="Eldar/Wraithlord" value="<string name='Units/Eldar/Wraithlord'/>"/>
	<entry name="Eldar/WraithlordDescription" value="<string name='Units/Eldar/WraithlordDescription'/>"/>
	<entry name="Eldar/WraithlordFlavor" value="<string name='Units/Eldar/WraithlordFlavor'/>"/>
	<entry name="Necrons/AnnihilationBarge" value="<string name='Units/Necrons/AnnihilationBarge'/>"/>
	<entry name="Necrons/AnnihilationBargeDescription" value="<string name='Units/Necrons/AnnihilationBargeDescription'/>"/>
	<entry name="Necrons/AnnihilationBargeFlavor" value="<string name='Units/Necrons/AnnihilationBargeFlavor'/>"/>
	<entry name="Necrons/CanoptekReanimator" value="<string name='Units/Necrons/CanoptekReanimator'/>"/>
	<entry name="Necrons/CanoptekReanimatorDescription" value="<string name='Units/Necrons/CanoptekReanimatorDescription'/>"/>
	<entry name="Necrons/CanoptekReanimatorFlavor" value="<string name='Units/Necrons/CanoptekReanimatorFlavor'/>"/>
	<entry name="Necrons/CanoptekSpyder" value="<string name='Units/Necrons/CanoptekSpyder'/>"/>
	<entry name="Necrons/CanoptekSpyderDescription" value="<string name='Units/Necrons/CanoptekSpyderDescription'/>"/>
	<entry name="Necrons/CanoptekSpyderFlavor" value="<string name='Units/Necrons/CanoptekSpyderFlavor'/>"/>
	<entry name="Necrons/CanoptekWraith" value="<string name='Units/Necrons/CanoptekWraith'/>"/>
	<entry name="Necrons/CanoptekWraithDescription" value="<string name='Units/Necrons/CanoptekWraithDescription'/>"/>
	<entry name="Necrons/CanoptekWraithFlavor" value="<string name='Units/Necrons/CanoptekWraithFlavor'/>"/>
	<entry name="Necrons/Cryptek" value="<string name='Units/Necrons/Cryptek'/>"/>
	<entry name="Necrons/CryptekDescription" value="<string name='Units/Necrons/CryptekDescription'/>"/>
	<entry name="Necrons/CryptekFlavor" value="<string name='Units/Necrons/CryptekFlavor'/>"/>
	<entry name="Necrons/Deathmark" value="<string name='Units/Necrons/Deathmark'/>"/>
	<entry name="Necrons/DeathmarkDescription" value="<string name='Units/Necrons/DeathmarkDescription'/>"/>
	<entry name="Necrons/DeathmarkFlavor" value="<string name='Units/Necrons/DeathmarkFlavor'/>"/>
	<entry name="Necrons/DestroyerLord" value="<string name='Units/Necrons/DestroyerLord'/>"/>
	<entry name="Necrons/DestroyerLordDescription" value="<string name='Units/Necrons/DestroyerLordDescription'/>"/>
	<entry name="Necrons/DestroyerLordFlavor" value="<string name='Units/Necrons/DestroyerLordFlavor'/>"/>
	<entry name="Necrons/DoomScythe" value="<string name='Units/Necrons/DoomScythe'/>"/>
	<entry name="Necrons/DoomScytheDescription" value="<string name='Units/Necrons/DoomScytheDescription'/>"/>
	<entry name="Necrons/DoomScytheFlavor" value="<string name='Units/Necrons/DoomScytheFlavor'/>"/>
	<entry name="Necrons/DoomsdayArk" value="<string name='Units/Necrons/DoomsdayArk'/>"/>
	<entry name="Necrons/DoomsdayArkDescription" value="<string name='Units/Necrons/DoomsdayArkDescription'/>"/>
	<entry name="Necrons/DoomsdayArkFlavor" value="<string name='Units/Necrons/DoomsdayArkFlavor'/>"/>
	<entry name="Necrons/FlayedOne" value="<string name='Units/Necrons/FlayedOne'/>"/>
	<entry name="Necrons/FlayedOneDescription" value="<string name='Units/Necrons/FlayedOneDescription'/>"/>
	<entry name="Necrons/FlayedOneFlavor" value="<string name='Units/Necrons/FlayedOneFlavor'/>"/>
	<entry name="Necrons/GhostArk" value="<string name='Units/Necrons/GhostArk'/>"/>
	<entry name="Necrons/GhostArkDescription" value="<string name='Units/Necrons/GhostArkDescription'/>"/>
	<entry name="Necrons/GhostArkFlavor" value="<string name='Units/Necrons/GhostArkFlavor'/>"/>
	<entry name="Necrons/HeavyDestroyer" value="<string name='Units/Necrons/HeavyDestroyer'/>"/>
	<entry name="Necrons/HeavyDestroyerDescription" value="<string name='Units/Necrons/HeavyDestroyerDescription'/>"/>
	<entry name="Necrons/HeavyDestroyerFlavor" value="<string name='Units/Necrons/HeavyDestroyerFlavor'/>"/>
	<entry name="Necrons/Immortal" value="<string name='Units/Necrons/Immortal'/>"/>
	<entry name="Necrons/ImmortalDescription" value="<string name='Units/Necrons/ImmortalDescription'/>"/>
	<entry name="Necrons/ImmortalFlavor" value="<string name='Units/Necrons/ImmortalFlavor'/>"/>
	<entry name="Necrons/Lord" value="<string name='Units/Necrons/Lord'/>"/>
	<entry name="Necrons/LordDescription" value="<string name='Units/Necrons/LordDescription'/>"/>
	<entry name="Necrons/LordFlavor" value="<string name='Units/Necrons/LordFlavor'/>"/>
	<entry name="Necrons/Monolith" value="<string name='Units/Necrons/Monolith'/>"/>
	<entry name="Necrons/MonolithDescription" value="<string name='Units/Necrons/MonolithDescription'/>"/>
	<entry name="Necrons/MonolithFlavor" value="<string name='Units/Necrons/MonolithFlavor'/>"/>
	<entry name="Necrons/NightScythe" value="<string name='Units/Necrons/NightScythe'/>"/>
	<entry name="Necrons/NightScytheDescription" value="<string name='Units/Necrons/NightScytheDescription'/>"/>
	<entry name="Necrons/NightScytheFlavor" value="<string name='Units/Necrons/NightScytheFlavor'/>"/>
	<entry name="Necrons/Obelisk" value="<string name='Units/Necrons/Obelisk'/>"/>
	<entry name="Necrons/ObeliskDescription" value="<string name='Units/Necrons/ObeliskDescription'/>"/>
	<entry name="Necrons/ObeliskFlavor" value="<string name='Units/Necrons/ObeliskFlavor'/>"/>
	<entry name="Necrons/SkorpekhDestroyer" value="<string name='Units/Necrons/SkorpekhDestroyer'/>"/>
	<entry name="Necrons/SkorpekhDestroyerDescription" value="<string name='Units/Necrons/SkorpekhDestroyerDescription'/>"/>
	<entry name="Necrons/SkorpekhDestroyerFlavor" value="<string name='Units/Necrons/SkorpekhDestroyerFlavor'/>"/>
	<entry name="Necrons/TombBlade" value="<string name='Units/Necrons/TombBlade'/>"/>
	<entry name="Necrons/TombBladeDescription" value="<string name='Units/Necrons/TombBladeDescription'/>"/>
	<entry name="Necrons/TombBladeFlavor" value="<string name='Units/Necrons/TombBladeFlavor'/>"/>
	<entry name="Necrons/TranscendentCtan" value="<string name='Units/Necrons/TranscendentCtan'/>"/>
	<entry name="Necrons/TranscendentCtanDescription" value="<string name='Units/Necrons/TranscendentCtanDescription'/>"/>
	<entry name="Necrons/TranscendentCtanFlavor" value="<string name='Units/Necrons/TranscendentCtanFlavor'/>"/>
	<entry name="Necrons/TriarchPraetorian" value="<string name='Units/Necrons/TriarchPraetorian'/>"/>
	<entry name="Necrons/TriarchPraetorianDescription" value="<string name='Units/Necrons/TriarchPraetorianDescription'/>"/>
	<entry name="Necrons/TriarchPraetorianFlavor" value="<string name='Units/Necrons/TriarchPraetorianFlavor'/>"/>
	<entry name="Necrons/TriarchStalker" value="<string name='Units/Necrons/TriarchStalker'/>"/>
	<entry name="Necrons/TriarchStalkerDescription" value="<string name='Units/Necrons/TriarchStalkerDescription'/>"/>
	<entry name="Necrons/TriarchStalkerFlavor" value="<string name='Units/Necrons/TriarchStalkerFlavor'/>"/>
	<entry name="Orks/Battlewagon" value="<string name='Units/Orks/Battlewagon'/>"/>
	<entry name="Orks/BattlewagonDescription" value="<string name='Units/Orks/BattlewagonDescription'/>"/>
	<entry name="Orks/BattlewagonFlavor" value="<string name='Units/Orks/BattlewagonFlavor'/>"/>
	<entry name="Orks/BigMek" value="<string name='Units/Orks/BigMek'/>"/>
	<entry name="Orks/BigMekDescription" value="<string name='Units/Orks/BigMekDescription'/>"/>
	<entry name="Orks/BigMekFlavor" value="<string name='Units/Orks/BigMekFlavor'/>"/>
	<entry name="Orks/BurnaBommer" value="<string name='Units/Orks/BurnaBommer'/>"/>
	<entry name="Orks/BurnaBommerDescription" value="<string name='Units/Orks/BurnaBommerDescription'/>"/>
	<entry name="Orks/BurnaBommerFlavor" value="<string name='Units/Orks/BurnaBommerFlavor'/>"/>
	<entry name="Orks/BurnaBoy" value="<string name='Units/Orks/BurnaBoy'/>"/>
	<entry name="Orks/BurnaBoyDescription" value="<string name='Units/Orks/BurnaBoyDescription'/>"/>
	<entry name="Orks/BurnaBoyFlavor" value="<string name='Units/Orks/BurnaBoyFlavor'/>"/>
	<entry name="Orks/Dakkajet" value="<string name='Units/Orks/Dakkajet'/>"/>
	<entry name="Orks/DakkajetDescription" value="<string name='Units/Orks/DakkajetDescription'/>"/>
	<entry name="Orks/DakkajetFlavor" value="<string name='Units/Orks/DakkajetFlavor'/>"/>
	<entry name="Orks/DeffDread" value="<string name='Units/Orks/DeffDread'/>"/>
	<entry name="Orks/DeffDreadDescription" value="<string name='Units/Orks/DeffDreadDescription'/>"/>
	<entry name="Orks/DeffDreadFlavor" value="<string name='Units/Orks/DeffDreadFlavor'/>"/>
	<entry name="Orks/Deffkopta" value="<string name='Units/Orks/Deffkopta'/>"/>
	<entry name="Orks/DeffkoptaDescription" value="<string name='Units/Orks/DeffkoptaDescription'/>"/>
	<entry name="Orks/DeffkoptaFlavor" value="<string name='Units/Orks/DeffkoptaFlavor'/>"/>
	<entry name="Orks/FlashGitz" value="<string name='Units/Orks/FlashGitz'/>"/>
	<entry name="Orks/FlashGitzDescription" value="<string name='Units/Orks/FlashGitzDescription'/>"/>
	<entry name="Orks/FlashGitzFlavor" value="<string name='Units/Orks/FlashGitzFlavor'/>"/>
	<entry name="Orks/GargantuanSquiggoth" value="<string name='Units/Orks/GargantuanSquiggoth'/>"/>
	<entry name="Orks/GargantuanSquiggothDescription" value="<string name='Units/Orks/GargantuanSquiggothDescription'/>"/>
	<entry name="Orks/GargantuanSquiggothFlavor" value="<string name='Units/Orks/GargantuanSquiggothFlavor'/>"/>
	<entry name="Orks/Gorkanaut" value="<string name='Units/Orks/Gorkanaut'/>"/>
	<entry name="Orks/GorkanautDescription" value="<string name='Units/Orks/GorkanautDescription'/>"/>
	<entry name="Orks/GorkanautFlavor" value="<string name='Units/Orks/GorkanautFlavor'/>"/>
	<entry name="Orks/KillBursta" value="<string name='Units/Orks/KillBursta'/>"/>
	<entry name="Orks/KillBurstaDescription" value="<string name='Units/Orks/KillBurstaDescription'/>"/>
	<entry name="Orks/KillBurstaFlavor" value="<string name='Units/Orks/KillBurstaFlavor'/>"/>
	<entry name="Orks/KillaKan" value="<string name='Units/Orks/KillaKan'/>"/>
	<entry name="Orks/KillaKanDescription" value="<string name='Units/Orks/KillaKanDescription'/>"/>
	<entry name="Orks/KillaKanFlavor" value="<string name='Units/Orks/KillaKanFlavor'/>"/>
	<entry name="Orks/Meganob" value="<string name='Units/Orks/Meganob'/>"/>
	<entry name="Orks/MeganobDescription" value="<string name='Units/Orks/MeganobDescription'/>"/>
	<entry name="Orks/MeganobFlavor" value="<string name='Units/Orks/MeganobFlavor'/>"/>
	<entry name="Orks/MegatrakkScrapjet" value="<string name='Units/Orks/MegatrakkScrapjet'/>"/>
	<entry name="Orks/MegatrakkScrapjetDescription" value="<string name='Units/Orks/MegatrakkScrapjetDescription'/>"/>
	<entry name="Orks/MegatrakkScrapjetFlavor" value="<string name='Units/Orks/MegatrakkScrapjetFlavor'/>"/>
	<entry name="Orks/Mek" value="<string name='Units/Orks/Mek'/>"/>
	<entry name="Orks/MekDescription" value="<string name='Units/Orks/MekDescription'/>"/>
	<entry name="Orks/MekFlavor" value="<string name='Units/Orks/MekFlavor'/>"/>
	<entry name="Orks/MekGun" value="<string name='Units/Orks/MekGun'/>"/>
	<entry name="Orks/MekGunDescription" value="<string name='Units/Orks/MekGunDescription'/>"/>
	<entry name="Orks/MekGunFlavor" value="<string name='Units/Orks/MekGunFlavor'/>"/>
	<entry name="Orks/Painboy" value="<string name='Units/Orks/Painboy'/>"/>
	<entry name="Orks/PainboyDescription" value="<string name='Units/Orks/PainboyDescription'/>"/>
	<entry name="Orks/PainboyFlavor" value="<string name='Units/Orks/PainboyFlavor'/>"/>
	<entry name="Orks/SquighogBoy" value="<string name='Units/Orks/SquighogBoy'/>"/>
	<entry name="Orks/SquighogBoyDescription" value="<string name='Units/Orks/SquighogBoyDescription'/>"/>
	<entry name="Orks/SquighogBoyFlavor" value="<string name='Units/Orks/SquighogBoyFlavor'/>"/>
	<entry name="Orks/Tankbusta" value="<string name='Units/Orks/Tankbusta'/>"/>
	<entry name="Orks/TankbustaDescription" value="<string name='Units/Orks/TankbustaDescription'/>"/>
	<entry name="Orks/TankbustaFlavor" value="<string name='Units/Orks/TankbustaFlavor'/>"/>
	<entry name="Orks/Warbiker" value="<string name='Units/Orks/Warbiker'/>"/>
	<entry name="Orks/WarbikerDescription" value="<string name='Units/Orks/WarbikerDescription'/>"/>
	<entry name="Orks/WarbikerFlavor" value="<string name='Units/Orks/WarbikerFlavor'/>"/>
	<entry name="Orks/Warboss" value="<string name='Units/Orks/Warboss'/>"/>
	<entry name="Orks/WarbossDescription" value="<string name='Units/Orks/WarbossDescription'/>"/>
	<entry name="Orks/WarbossFlavor" value="<string name='Units/Orks/WarbossFlavor'/>"/>
	<entry name="Orks/Warbuggy" value="<string name='Units/Orks/Warbuggy'/>"/>
	<entry name="Orks/WarbuggyDescription" value="<string name='Units/Orks/WarbuggyDescription'/>"/>
	<entry name="Orks/WarbuggyFlavor" value="<string name='Units/Orks/WarbuggyFlavor'/>"/>
	<entry name="Orks/Weirdboy" value="<string name='Units/Orks/Weirdboy'/>"/>
	<entry name="Orks/WeirdboyDescription" value="<string name='Units/Orks/WeirdboyDescription'/>"/>
	<entry name="Orks/WeirdboyFlavor" value="<string name='Units/Orks/WeirdboyFlavor'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellant" value="<string name='Units/SistersOfBattle/ArcoFlagellant'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellantDescription" value="<string name='Units/SistersOfBattle/ArcoFlagellantDescription'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellantFlavor" value="<string name='Units/SistersOfBattle/ArcoFlagellantFlavor'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter" value="<string name='Units/SistersOfBattle/AvengerStrikeFighter'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterDescription" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterDescription'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterFlavor" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterFlavor'/>"/>
	<entry name="SistersOfBattle/Castigator" value="<string name='Units/SistersOfBattle/Castigator'/>"/>
	<entry name="SistersOfBattle/CastigatorDescription" value="<string name='Units/SistersOfBattle/CastigatorDescription'/>"/>
	<entry name="SistersOfBattle/CastigatorFlavor" value="<string name='Units/SistersOfBattle/CastigatorFlavor'/>"/>
	<entry name="SistersOfBattle/CelestianSacresant" value="<string name='Units/SistersOfBattle/CelestianSacresant'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantDescription" value="<string name='Units/SistersOfBattle/CelestianSacresantDescription'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantFlavor" value="<string name='Units/SistersOfBattle/CelestianSacresantFlavor'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancer" value="<string name='Units/SistersOfBattle/CerastusKnightLancer'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerDescription" value="<string name='Units/SistersOfBattle/CerastusKnightLancerDescription'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerFlavor" value="<string name='Units/SistersOfBattle/CerastusKnightLancerFlavor'/>"/>
	<entry name="SistersOfBattle/Dialogus" value="<string name='Units/SistersOfBattle/Dialogus'/>"/>
	<entry name="SistersOfBattle/DialogusDescription" value="<string name='Units/SistersOfBattle/DialogusDescription'/>"/>
	<entry name="SistersOfBattle/DialogusFlavor" value="<string name='Units/SistersOfBattle/DialogusFlavor'/>"/>
	<entry name="SistersOfBattle/Dominion" value="<string name='Units/SistersOfBattle/Dominion'/>"/>
	<entry name="SistersOfBattle/DominionDescription" value="<string name='Units/SistersOfBattle/DominionDescription'/>"/>
	<entry name="SistersOfBattle/DominionFlavor" value="<string name='Units/SistersOfBattle/DominionFlavor'/>"/>
	<entry name="SistersOfBattle/Exorcist" value="<string name='Units/SistersOfBattle/Exorcist'/>"/>
	<entry name="SistersOfBattle/ExorcistDescription" value="<string name='Units/SistersOfBattle/ExorcistDescription'/>"/>
	<entry name="SistersOfBattle/ExorcistFlavor" value="<string name='Units/SistersOfBattle/ExorcistFlavor'/>"/>
	<entry name="SistersOfBattle/Headquarters" value="<string name='Buildings/SistersOfBattle/Headquarters'/>"/>
	<entry name="SistersOfBattle/HeadquartersDescription" value="<string name='Units/SistersOfBattle/HeadquartersDescription'/>"/>
	<entry name="SistersOfBattle/HeadquartersFlavor" value="<string name='Buildings/SistersOfBattle/HeadquartersFlavor'/>"/>
	<entry name="SistersOfBattle/Hospitaller" value="<string name='Units/SistersOfBattle/Hospitaller'/>"/>
	<entry name="SistersOfBattle/HospitallerDescription" value="<string name='Units/SistersOfBattle/HospitallerDescription'/>"/>
	<entry name="SistersOfBattle/HospitallerFlavor" value="<string name='Units/SistersOfBattle/HospitallerFlavor'/>"/>
	<entry name="SistersOfBattle/Imagifier" value="<string name='Units/SistersOfBattle/Imagifier'/>"/>
	<entry name="SistersOfBattle/ImagifierDescription" value="<string name='Units/SistersOfBattle/ImagifierDescription'/>"/>
	<entry name="SistersOfBattle/ImagifierFlavor" value="<string name='Units/SistersOfBattle/ImagifierFlavor'/>"/>
	<entry name="SistersOfBattle/Mortifier" value="<string name='Units/SistersOfBattle/Mortifier'/>"/>
	<entry name="SistersOfBattle/MortifierDescription" value="<string name='Units/SistersOfBattle/MortifierDescription'/>"/>
	<entry name="SistersOfBattle/MortifierFlavor" value="<string name='Units/SistersOfBattle/MortifierFlavor'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="<string name='Units/SistersOfBattle/ParagonWarsuit'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="<string name='Units/SistersOfBattle/ParagonWarsuitDescription'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="<string name='Units/SistersOfBattle/ParagonWarsuitFlavor'/>"/>
	<entry name="SistersOfBattle/Retributor" value="<string name='Units/SistersOfBattle/Retributor'/>"/>
	<entry name="SistersOfBattle/RetributorDescription" value="<string name='Units/SistersOfBattle/RetributorDescription'/>"/>
	<entry name="SistersOfBattle/RetributorFlavor" value="<string name='Units/SistersOfBattle/RetributorFlavor'/>"/>
	<entry name="SistersOfBattle/SaintCelestine" value="<string name='Units/SistersOfBattle/SaintCelestine'/>"/>
	<entry name="SistersOfBattle/SaintCelestineDescription" value="<string name='Units/SistersOfBattle/SaintCelestineDescription'/>"/>
	<entry name="SistersOfBattle/SaintCelestineFlavor" value="<string name='Units/SistersOfBattle/SaintCelestineFlavor'/>"/>
	<entry name="SistersOfBattle/SisterRepentia" value="<string name='Units/SistersOfBattle/SisterRepentia'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaDescription" value="<string name='Units/SistersOfBattle/SisterRepentiaDescription'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaFlavor" value="<string name='Units/SistersOfBattle/SisterRepentiaFlavor'/>"/>
	<entry name="SistersOfBattle/Zephyrim" value="<string name='Units/SistersOfBattle/Zephyrim'/>"/>
	<entry name="SistersOfBattle/ZephyrimDescription" value="<string name='Units/SistersOfBattle/ZephyrimDescription'/>"/>
	<entry name="SistersOfBattle/ZephyrimFlavor" value="<string name='Units/SistersOfBattle/ZephyrimFlavor'/>"/>
	<entry name="SpaceMarines/Apothecary" value="<string name='Units/SpaceMarines/Apothecary'/>"/>
	<entry name="SpaceMarines/ApothecaryDescription" value="<string name='Units/SpaceMarines/ApothecaryDescription'/>"/>
	<entry name="SpaceMarines/ApothecaryFlavor" value="<string name='Units/SpaceMarines/ApothecaryFlavor'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannon" value="<string name='Units/SpaceMarines/AquilaMacroCannon'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonDescription" value="<string name='Units/SpaceMarines/AquilaMacroCannonDescription'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonFlavor" value="<string name='Units/SpaceMarines/AquilaMacroCannonFlavor'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="<string name='Units/SpaceMarines/AssaultSpaceMarine'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineDescription" value="<string name='Units/SpaceMarines/AssaultSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineFlavor" value="<string name='Units/SpaceMarines/AssaultSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/AssaultTerminator" value="<string name='Units/SpaceMarines/AssaultTerminator'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorDescription" value="<string name='Units/SpaceMarines/AssaultTerminatorDescription'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorFlavor" value="<string name='Units/SpaceMarines/AssaultTerminatorFlavor'/>"/>
	<entry name="SpaceMarines/Captain" value="<string name='Units/SpaceMarines/Captain'/>"/>
	<entry name="SpaceMarines/CaptainDescription" value="<string name='Units/SpaceMarines/CaptainDescription'/>"/>
	<entry name="SpaceMarines/CaptainFlavor" value="<string name='Units/SpaceMarines/CaptainFlavor'/>"/>
	<entry name="SpaceMarines/Chaplain" value="<string name='Units/SpaceMarines/Chaplain'/>"/>
	<entry name="SpaceMarines/ChaplainDescription" value="<string name='Units/SpaceMarines/ChaplainDescription'/>"/>
	<entry name="SpaceMarines/ChaplainFlavor" value="<string name='Units/SpaceMarines/ChaplainFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorCenturion" value="<string name='Units/SpaceMarines/DevastatorCenturion'/>"/>
	<entry name="SpaceMarines/DevastatorCenturionDescription" value="<string name='Units/SpaceMarines/DevastatorCenturionDescription'/>"/>
	<entry name="SpaceMarines/DevastatorCenturionFlavor" value="<string name='Units/SpaceMarines/DevastatorCenturionFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="<string name='Units/SpaceMarines/DevastatorSpaceMarine'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineDescription" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineFlavor" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/Dreadnought" value="<string name='Units/SpaceMarines/Dreadnought'/>"/>
	<entry name="SpaceMarines/DreadnoughtDescription" value="<string name='Units/SpaceMarines/DreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/DreadnoughtFlavor" value="<string name='Units/SpaceMarines/DreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Hunter" value="<string name='Units/SpaceMarines/Hunter'/>"/>
	<entry name="SpaceMarines/HunterDescription" value="<string name='Units/SpaceMarines/HunterDescription'/>"/>
	<entry name="SpaceMarines/HunterFlavor" value="<string name='Units/SpaceMarines/HunterFlavor'/>"/>
	<entry name="SpaceMarines/LandRaider" value="<string name='Units/SpaceMarines/LandRaider'/>"/>
	<entry name="SpaceMarines/LandRaiderDescription" value="<string name='Units/SpaceMarines/LandRaiderDescription'/>"/>
	<entry name="SpaceMarines/LandRaiderFlavor" value="<string name='Units/SpaceMarines/LandRaiderFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeeder" value="<string name='Units/SpaceMarines/LandSpeeder'/>"/>
	<entry name="SpaceMarines/LandSpeederDescription" value="<string name='Units/SpaceMarines/LandSpeederDescription'/>"/>
	<entry name="SpaceMarines/LandSpeederFlavor" value="<string name='Units/SpaceMarines/LandSpeederFlavor'/>"/>
	<entry name="SpaceMarines/Librarian" value="<string name='Units/SpaceMarines/Librarian'/>"/>
	<entry name="SpaceMarines/LibrarianDescription" value="<string name='Units/SpaceMarines/LibrarianDescription'/>"/>
	<entry name="SpaceMarines/LibrarianFlavor" value="<string name='Units/SpaceMarines/LibrarianFlavor'/>"/>
	<entry name="SpaceMarines/Predator" value="<string name='Units/SpaceMarines/Predator'/>"/>
	<entry name="SpaceMarines/PredatorDescription" value="<string name='Units/SpaceMarines/PredatorDescription'/>"/>
	<entry name="SpaceMarines/PredatorFlavor" value="<string name='Units/SpaceMarines/PredatorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressor" value="<string name='Units/SpaceMarines/PrimarisAggressor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressorDescription" value="<string name='Units/SpaceMarines/PrimarisAggressorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisAggressorFlavor" value="<string name='Units/SpaceMarines/PrimarisAggressorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisHellblaster" value="<string name='Units/SpaceMarines/PrimarisHellblaster'/>"/>
	<entry name="SpaceMarines/PrimarisHellblasterDescription" value="<string name='Units/SpaceMarines/PrimarisHellblasterDescription'/>"/>
	<entry name="SpaceMarines/PrimarisHellblasterFlavor" value="<string name='Units/SpaceMarines/PrimarisHellblasterFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisInceptor" value="<string name='Units/SpaceMarines/PrimarisInceptor'/>"/>
	<entry name="SpaceMarines/PrimarisInceptorDescription" value="<string name='Units/SpaceMarines/PrimarisInceptorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisInceptorFlavor" value="<string name='Units/SpaceMarines/PrimarisInceptorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessor" value="<string name='Units/SpaceMarines/PrimarisIntercessor'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessorDescription" value="<string name='Units/SpaceMarines/PrimarisIntercessorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessorFlavor" value="<string name='Units/SpaceMarines/PrimarisIntercessorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATV" value="<string name='Units/SpaceMarines/PrimarisInvaderATV'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATVDescription" value="<string name='Units/SpaceMarines/PrimarisInvaderATVDescription'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATVFlavor" value="<string name='Units/SpaceMarines/PrimarisInvaderATVFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutioner'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerDescription" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerDescription'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerFlavor" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerFlavor'/>"/>
	<entry name="SpaceMarines/Razorback" value="<string name='Units/SpaceMarines/Razorback'/>"/>
	<entry name="SpaceMarines/RazorbackDescription" value="<string name='Units/SpaceMarines/RazorbackDescription'/>"/>
	<entry name="SpaceMarines/RazorbackFlavor" value="<string name='Units/SpaceMarines/RazorbackFlavor'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnought" value="<string name='Units/SpaceMarines/RedemptorDreadnought'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtDescription" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtFlavor" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Scout" value="<string name='Units/SpaceMarines/Scout'/>"/>
	<entry name="SpaceMarines/ScoutDescription" value="<string name='Units/SpaceMarines/ScoutDescription'/>"/>
	<entry name="SpaceMarines/ScoutFlavor" value="<string name='Units/SpaceMarines/ScoutFlavor'/>"/>
	<entry name="SpaceMarines/ScoutBiker" value="<string name='Units/SpaceMarines/ScoutBiker'/>"/>
	<entry name="SpaceMarines/ScoutBikerDescription" value="<string name='Units/SpaceMarines/ScoutBikerDescription'/>"/>
	<entry name="SpaceMarines/ScoutBikerFlavor" value="<string name='Units/SpaceMarines/ScoutBikerFlavor'/>"/>
	<entry name="SpaceMarines/StormravenGunship" value="<string name='Units/SpaceMarines/StormravenGunship'/>"/>
	<entry name="SpaceMarines/StormravenGunshipDescription" value="<string name='Units/SpaceMarines/StormravenGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormravenGunshipFlavor" value="<string name='Units/SpaceMarines/StormravenGunshipFlavor'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike" value="<string name='Units/SpaceMarines/StormSpeederThunderstrike'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeDescription" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeDescription'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeFlavor" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeFlavor'/>"/>
	<entry name="SpaceMarines/StormtalonGunship" value="<string name='Units/SpaceMarines/StormtalonGunship'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipDescription" value="<string name='Units/SpaceMarines/StormtalonGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipFlavor" value="<string name='Units/SpaceMarines/StormtalonGunshipFlavor'/>"/>
	<entry name="SpaceMarines/Terminator" value="<string name='Units/SpaceMarines/Terminator'/>"/>
	<entry name="SpaceMarines/TerminatorDescription" value="<string name='Units/SpaceMarines/TerminatorDescription'/>"/>
	<entry name="SpaceMarines/TerminatorFlavor" value="<string name='Units/SpaceMarines/TerminatorFlavor'/>"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="<string name='Units/SpaceMarines/ThunderfireCannon'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonDescription" value="<string name='Units/SpaceMarines/ThunderfireCannonDescription'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonFlavor" value="<string name='Units/SpaceMarines/ThunderfireCannonFlavor'/>"/>
	<entry name="SpaceMarines/Vindicator" value="<string name='Units/SpaceMarines/Vindicator'/>"/>
	<entry name="SpaceMarines/VindicatorDescription" value="<string name='Units/SpaceMarines/VindicatorDescription'/>"/>
	<entry name="SpaceMarines/VindicatorFlavor" value="<string name='Units/SpaceMarines/VindicatorFlavor'/>"/>
	<entry name="SpaceMarines/Whirlwind" value="<string name='Units/SpaceMarines/Whirlwind'/>"/>
	<entry name="SpaceMarines/WhirlwindDescription" value="<string name='Units/SpaceMarines/WhirlwindDescription'/>"/>
	<entry name="SpaceMarines/WhirlwindFlavor" value="<string name='Units/SpaceMarines/WhirlwindFlavor'/>"/>
	<entry name="Tau/BroadsideBattlesuit" value="<string name='Units/Tau/BroadsideBattlesuit'/>"/>
	<entry name="Tau/BroadsideBattlesuitDescription" value="<string name='Units/Tau/BroadsideBattlesuitDescription'/>"/>
	<entry name="Tau/BroadsideBattlesuitFlavor" value="<string name='Units/Tau/BroadsideBattlesuitFlavor'/>"/>
	<entry name="Tau/BuilderDrone" value="<string name='Units/Tau/BuilderDrone'/>"/>
	<entry name="Tau/BuilderDroneDescription" value="<string name='Units/Tau/BuilderDroneDescription'/>"/>
	<entry name="Tau/BuilderDroneFlavor" value="<string name='Units/Tau/BuilderDroneFlavor'/>"/>
	<entry name="Tau/Commander" value="<string name='Units/Tau/Commander'/>"/>
	<entry name="Tau/CommanderDescription" value="<string name='Units/Tau/CommanderDescription'/>"/>
	<entry name="Tau/CommanderFlavor" value="<string name='Units/Tau/CommanderFlavor'/>"/>
	<entry name="Tau/CrisisBattlesuit" value="<string name='Units/Tau/CrisisBattlesuit'/>"/>
	<entry name="Tau/CrisisBattlesuitDescription" value="<string name='Units/Tau/CrisisBattlesuitDescription'/>"/>
	<entry name="Tau/CrisisBattlesuitFlavor" value="<string name='Units/Tau/CrisisBattlesuitFlavor'/>"/>
	<entry name="Tau/Devilfish" value="<string name='Units/Tau/Devilfish'/>"/>
	<entry name="Tau/DevilfishDescription" value="<string name='Units/Tau/DevilfishDescription'/>"/>
	<entry name="Tau/DevilfishFlavor" value="<string name='Units/Tau/DevilfishFlavor'/>"/>
	<entry name="Tau/Ethereal" value="<string name='Units/Tau/Ethereal'/>"/>
	<entry name="Tau/EtherealDescription" value="<string name='Units/Tau/EtherealDescription'/>"/>
	<entry name="Tau/EtherealFlavor" value="<string name='Units/Tau/EtherealFlavor'/>"/>
	<entry name="Tau/FireWarriorBreacher" value="<string name='Units/Tau/FireWarriorBreacher'/>"/>
	<entry name="Tau/FireWarriorBreacherDescription" value="<string name='Units/Tau/FireWarriorBreacherDescription'/>"/>
	<entry name="Tau/FireWarriorBreacherFlavor" value="<string name='Units/Tau/FireWarriorBreacherFlavor'/>"/>
	<entry name="Tau/GunDrone" value="<string name='Units/Tau/GunDrone'/>"/>
	<entry name="Tau/GunDroneDescription" value="Ermöglicht es Feuerkriegern, Angriffsfeuerkriegern, Kampfanzügen XV8 Krisis, Kampfanzügen XV25 Geist, Kampfanzügen XV88 Breitseite, Kader-Feuerklingen, Himmlischen und Commandern, vorübergehend gewöhnliche Kampfdrohneneinheiten einzusetzen."/>
	<entry name="Tau/GunDroneFlavor" value="<string name='Units/Tau/GunDroneFlavor'/>"/>
	<entry name="Tau/HammerheadGunship" value="<string name='Units/Tau/HammerheadGunship'/>"/>
	<entry name="Tau/HammerheadGunshipDescription" value="<string name='Units/Tau/HammerheadGunshipDescription'/>"/>
	<entry name="Tau/HammerheadGunshipFlavor" value="<string name='Units/Tau/HammerheadGunshipFlavor'/>"/>
	<entry name="Tau/KrootoxRider" value="<string name='Units/Tau/KrootoxRider'/>"/>
	<entry name="Tau/KrootoxRiderDescription" value="<string name='Units/Tau/KrootoxRiderDescription'/>"/>
	<entry name="Tau/KrootoxRiderFlavor" value="<string name='Units/Tau/KrootoxRiderFlavor'/>"/>
	<entry name="Tau/MarkerDrone" value="<string name='Units/Tau/MarkerDrone'/>"/>
	<entry name="Tau/MarkerDroneDescription" value="Ermöglicht es Feuerkriegern, Angriffsfeuerkriegern, Kampfanzügen XV8 Krisis, Kampfanzügen XV25 Geist, Kampfanzügen XV88 Breitseite, Kader-Feuerklingen, Himmlischen und Commandern, vorübergehend Drohnen einzusetzen, die feindliche Einheiten für erhöhten Schaden markieren."/>
	<entry name="Tau/MarkerDroneFlavor" value="<string name='Units/Tau/MarkerDroneFlavor'/>"/>
	<entry name="Tau/Pathfinder" value="<string name='Units/Tau/Pathfinder'/>"/>
	<entry name="Tau/PathfinderDescription" value="<string name='Units/Tau/PathfinderDescription'/>"/>
	<entry name="Tau/PathfinderFlavor" value="<string name='Units/Tau/PathfinderFlavor'/>"/>
	<entry name="Tau/RiptideBattlesuit" value="<string name='Units/Tau/RiptideBattlesuit'/>"/>
	<entry name="Tau/RiptideBattlesuitDescription" value="<string name='Units/Tau/RiptideBattlesuitDescription'/>"/>
	<entry name="Tau/RiptideBattlesuitFlavor" value="<string name='Units/Tau/RiptideBattlesuitFlavor'/>"/>
	<entry name="Tau/RVarnaBattlesuit" value="<string name='Units/Tau/RVarnaBattlesuit'/>"/>
	<entry name="Tau/RVarnaBattlesuitDescription" value="<string name='Units/Tau/RVarnaBattlesuitDescription'/>"/>
	<entry name="Tau/RVarnaBattlesuitFlavor" value="<string name='Units/Tau/RVarnaBattlesuitFlavor'/>"/>
	<entry name="Tau/ShieldDrone" value="<string name='Units/Tau/ShieldDrone'/>"/>
	<entry name="Tau/ShieldDroneDescription" value="Ermöglicht es Feuerkriegern, Angriffsfeuerkriegern, Kampfanzügen XV8 Krisis, Kampfanzügen XV25 Geist, Kampfanzügen XV88 Breitseite, Kader-Feuerklingen, Himmlischen und Commandern, vorübergehend Drohnen einzusetzen, die nicht-feindliche Einheiten beschützen."/>
	<entry name="Tau/ShieldDroneFlavor" value="<string name='Units/Tau/ShieldDroneFlavor'/>"/>
	<entry name="Tau/SkyRayGunship" value="<string name='Units/Tau/SkyRayGunship'/>"/>
	<entry name="Tau/SkyRayGunshipDescription" value="<string name='Units/Tau/SkyRayGunshipDescription'/>"/>
	<entry name="Tau/SkyRayGunshipFlavor" value="<string name='Units/Tau/SkyRayGunshipFlavor'/>"/>
	<entry name="Tau/StealthBattlesuit" value="<string name='Units/Tau/StealthBattlesuit'/>"/>
	<entry name="Tau/StealthBattlesuitDescription" value="<string name='Units/Tau/StealthBattlesuitDescription'/>"/>
	<entry name="Tau/StealthBattlesuitFlavor" value="<string name='Units/Tau/StealthBattlesuitFlavor'/>"/>
	<entry name="Tau/Stormsurge" value="<string name='Units/Tau/Stormsurge'/>"/>
	<entry name="Tau/StormsurgeDescription" value="<string name='Units/Tau/StormsurgeDescription'/>"/>
	<entry name="Tau/StormsurgeFlavor" value="<string name='Units/Tau/StormsurgeFlavor'/>"/>
	<entry name="Tau/SunSharkBomber" value="<string name='Units/Tau/SunSharkBomber'/>"/>
	<entry name="Tau/SunSharkBomberDescription" value="<string name='Units/Tau/SunSharkBomberDescription'/>"/>
	<entry name="Tau/SunSharkBomberFlavor" value="<string name='Units/Tau/SunSharkBomberFlavor'/>"/>
	<entry name="Tau/TidewallGunrig" value="<string name='Units/Tau/TidewallGunrig'/>"/>
	<entry name="Tau/TidewallGunrigDescription" value="Ermöglicht es Builder Drones, eine schwer bewaffnete Befestigungsanlage zu bauen, die von transportierten Truppen bewegt werden kann."/>
	<entry name="Tau/TidewallGunrigFlavor" value="<string name='Units/Tau/TidewallGunrigFlavor'/>"/>
	<entry name="Tau/TigerShark" value="<string name='Units/Tau/TigerShark'/>"/>
	<entry name="Tau/TigerSharkDescription" value="<string name='Units/Tau/TigerSharkDescription'/>"/>
	<entry name="Tau/TigerSharkFlavor" value="<string name='Units/Tau/TigerSharkFlavor'/>"/>
	<entry name="Tyranids/Biovore" value="<string name='Units/Tyranids/Biovore'/>"/>
	<entry name="Tyranids/BiovoreDescription" value="<string name='Units/Tyranids/BiovoreDescription'/>"/>
	<entry name="Tyranids/BiovoreFlavor" value="<string name='Units/Tyranids/BiovoreFlavor'/>"/>
	<entry name="Tyranids/Carnifex" value="<string name='Units/Tyranids/Carnifex'/>"/>
	<entry name="Tyranids/CarnifexDescription" value="<string name='Units/Tyranids/CarnifexDescription'/>"/>
	<entry name="Tyranids/CarnifexFlavor" value="<string name='Units/Tyranids/CarnifexFlavor'/>"/>
	<entry name="Tyranids/Exocrine" value="<string name='Units/Tyranids/Exocrine'/>"/>
	<entry name="Tyranids/ExocrineDescription" value="<string name='Units/Tyranids/ExocrineDescription'/>"/>
	<entry name="Tyranids/ExocrineFlavor" value="<string name='Units/Tyranids/ExocrineFlavor'/>"/>
	<entry name="Tyranids/Gargoyle" value="<string name='Units/Tyranids/Gargoyle'/>"/>
	<entry name="Tyranids/GargoyleDescription" value="<string name='Units/Tyranids/GargoyleDescription'/>"/>
	<entry name="Tyranids/GargoyleFlavor" value="<string name='Units/Tyranids/GargoyleFlavor'/>"/>
	<entry name="Tyranids/Haruspex" value="<string name='Units/Tyranids/Haruspex'/>"/>
	<entry name="Tyranids/HaruspexDescription" value="<string name='Units/Tyranids/HaruspexDescription'/>"/>
	<entry name="Tyranids/HaruspexFlavor" value="<string name='Units/Tyranids/HaruspexFlavor'/>"/>
	<entry name="Tyranids/HiveTyrant" value="<string name='Units/Tyranids/HiveTyrant'/>"/>
	<entry name="Tyranids/HiveTyrantDescription" value="<string name='Units/Tyranids/HiveTyrantDescription'/>"/>
	<entry name="Tyranids/HiveTyrantFlavor" value="<string name='Units/Tyranids/HiveTyrantFlavor'/>"/>
	<entry name="Tyranids/HiveGuard" value="<string name='Units/Tyranids/HiveGuard'/>"/>
	<entry name="Tyranids/HiveGuardDescription" value="<string name='Units/Tyranids/HiveGuardDescription'/>"/>
	<entry name="Tyranids/HiveGuardFlavor" value="<string name='Units/Tyranids/HiveGuardFlavor'/>"/>
	<entry name="Tyranids/Hormagaunt" value="<string name='Units/Tyranids/Hormagaunt'/>"/>
	<entry name="Tyranids/HormagauntDescription" value="<string name='Units/Tyranids/HormagauntDescription'/>"/>
	<entry name="Tyranids/HormagauntFlavor" value="<string name='Units/Tyranids/HormagauntFlavor'/>"/>
	<entry name="Tyranids/Lictor" value="<string name='Units/Tyranids/Lictor'/>"/>
	<entry name="Tyranids/LictorDescription" value="<string name='Units/Tyranids/LictorDescription'/>"/>
	<entry name="Tyranids/LictorFlavor" value="<string name='Units/Tyranids/LictorFlavor'/>"/>
	<entry name="Tyranids/Maleceptor" value="<string name='Units/Tyranids/Maleceptor'/>"/>
	<entry name="Tyranids/MaleceptorDescription" value="<string name='Units/Tyranids/MaleceptorDescription'/>"/>
	<entry name="Tyranids/MaleceptorFlavor" value="<string name='Units/Tyranids/MaleceptorFlavor'/>"/>
	<entry name="Tyranids/NornEmissary" value="<string name='Units/Tyranids/NornEmissary'/>"/>
	<entry name="Tyranids/NornEmissaryDescription" value="<string name='Units/Tyranids/NornEmissaryDescription'/>"/>
	<entry name="Tyranids/NornEmissaryFlavor" value="<string name='Units/Tyranids/NornEmissaryFlavor'/>"/>
	<entry name="Tyranids/Ravener" value="<string name='Units/Tyranids/Ravener'/>"/>
	<entry name="Tyranids/RavenerDescription" value="<string name='Units/Tyranids/RavenerDescription'/>"/>
	<entry name="Tyranids/RavenerFlavor" value="<string name='Units/Tyranids/RavenerFlavor'/>"/>
	<entry name="Tyranids/ScythedHierodule" value="<string name='Units/Tyranids/ScythedHierodule'/>"/>
	<entry name="Tyranids/ScythedHieroduleDescription" value="<string name='Units/Tyranids/ScythedHieroduleDescription'/>"/>
	<entry name="Tyranids/ScythedHieroduleFlavor" value="<string name='Units/Tyranids/ScythedHieroduleFlavor'/>"/>
	<entry name="Tyranids/Tervigon" value="<string name='Units/Tyranids/Tervigon'/>"/>
	<entry name="Tyranids/TervigonDescription" value="<string name='Units/Tyranids/TervigonDescription'/>"/>
	<entry name="Tyranids/TervigonFlavor" value="<string name='Units/Tyranids/TervigonFlavor'/>"/>
	<entry name="Tyranids/Trygon" value="<string name='Units/Tyranids/Trygon'/>"/>
	<entry name="Tyranids/TrygonDescription" value="<string name='Units/Tyranids/TrygonDescription'/>"/>
	<entry name="Tyranids/TrygonFlavor" value="<string name='Units/Tyranids/TrygonFlavor'/>"/>
	<entry name="Tyranids/Tyrannofex" value="<string name='Units/Tyranids/Tyrannofex'/>"/>
	<entry name="Tyranids/TyrannofexDescription" value="<string name='Units/Tyranids/TyrannofexDescription'/>"/>
	<entry name="Tyranids/TyrannofexFlavor" value="<string name='Units/Tyranids/TyrannofexFlavor'/>"/>
	<entry name="Tyranids/Venomthrope" value="<string name='Units/Tyranids/Venomthrope'/>"/>
	<entry name="Tyranids/VenomthropeDescription" value="<string name='Units/Tyranids/VenomthropeDescription'/>"/>
	<entry name="Tyranids/VenomthropeFlavor" value="<string name='Units/Tyranids/VenomthropeFlavor'/>"/>
	<entry name="Tyranids/Warrior" value="<string name='Units/Tyranids/Warrior'/>"/>
	<entry name="Tyranids/WarriorDescription" value="<string name='Units/Tyranids/WarriorDescription'/>"/>
	<entry name="Tyranids/WarriorFlavor" value="<string name='Units/Tyranids/WarriorFlavor'/>"/>
	<entry name="Tyranids/Zoanthrope" value="<string name='Units/Tyranids/Zoanthrope'/>"/>
	<entry name="Tyranids/ZoanthropeDescription" value="<string name='Units/Tyranids/ZoanthropeDescription'/>"/>
	<entry name="Tyranids/ZoanthropeFlavor" value="<string name='Units/Tyranids/ZoanthropeFlavor'/>"/>

	<!-- Other -->
	<entry name="SmokeLauncher" value="Nebelwerfer"/>
	<entry name="SmokeLauncherDescription" value="Ermöglicht es Bodenfahrzeugen, eine Nebelwand zu erzeugen, welche die Fernkampf-Schadensreduktion erhöht."/>
	<entry name="SmokeLauncherFlavor" value="An manchen Fahrzeugen wurden kleine Nebelwerfer angebracht, die das Fahrzeug vorübergehend hinter einer wogenden Nebelwand verschwinden lassen, was das Überqueren von offenem Gelände sicherer macht. Dies geht allerdings auf Kosten der eigenen Kampffähigkeit, da nicht mehr geschossen werden kann."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegration'/>"/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Erhöht für dieses Gebäude die Generierung von Forschungspunkten mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegrationFlavor'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von Sturmwaffen."/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceDescription'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
	<entry name="AdeptusMechanicus/BlessedConduits" value="Gesegnete Leitungen"/>
	<entry name="AdeptusMechanicus/BlessedConduitsDescription" value="Verringert die Abklingzeit von „Energieschub“."/>
	<entry name="AdeptusMechanicus/BlessedConduitsFlavor" value="„Und als er endlich an das Fahrzeug herantrat, gewahrte er die Pein des Motors, streckte sogleich die Rune empor und sah, dass es gut war. Alsdann erwachte der Motor zum Leben und war erfüllt von Stärke…“<br/> – Herr der Maschinen, 16. Band, Vers 2001"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Eine Fähigkeit von Eisenschreiter-Ballistarii, Sydonianischen Dragonern, Skorpius-Dünenschwebern, Onager-Dünenläufern und Skorpius-Desintegratoren, die den Moralverlust von angrenzenden Einheiten des Adeptus Mechanicus verringert."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistDescription'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier2" value="<string name='Traits/AdeptusMechanicus/CityTier2'/>"/>
	<entry name="AdeptusMechanicus/CityTier2Description" value="<string name='Traits/AdeptusMechanicus/CityTier2Description'/>"/>
	<entry name="AdeptusMechanicus/CityTier2Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier2Flavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier3" value="<string name='Traits/AdeptusMechanicus/CityTier3'/>"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="<string name='Traits/AdeptusMechanicus/CityTier3Description'/>"/>
	<entry name="AdeptusMechanicus/CityTier3Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier3Flavor'/>"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubber" value="<string name='Weapons/CognisHeavyStubber'/>"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberDescription" value="Ermöglicht es Onager-Dünenläufern, Cognis-Maschinengewehre einzusetzen."/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberFlavor" value="<string name='Weapons/CognisHeavyStubberFlavor'/>"/>
	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Traits/AdeptusMechanicus/CommandUplink'/>"/>
	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Archaeopter Transvector und Archaeopter Stratoraptor haben die Fähigkeit, den Moralverlust von angrenzenden Einheiten des Adeptus Mechanicus zu verringern."/>
	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Traits/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTether" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTether'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Erhöht die Moral von Skitarii-Strahlenkriegern und Skitarii-Jägern."/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmour" value="Graianische Protokolle"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterie."/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourFlavor" value="Die Integration von Graianischen Protokollen in die Befehlsschaltungen eines Skitarii erhöht seine ohnehin schon beeindruckende Überlebensfähigkeit noch weiter. Die Fabrikwelt Graia ist bekannt für ihre Starrköpfigkeit, da ihre Logik einfach unwiderlegbar ist."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmour" value="Agripinaa-Schmelztechnik"/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourDescription" value="Erhöht die Panzerung von Fahrzeugen."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourFlavor" value="Das Leben nahe dem Auge des Schreckens nach dem Fall von Cadia hat das Adeptus Mechanicus auf Agripinaa zu Verteidigungsexperten gemacht – vor allem, wenn es darum geht, ihre Maschinen unter Feuer am Laufen zu halten."/>
	<entry name="AdeptusMechanicus/FidorumVossPrime" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrime'/>"/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Erhöht für dieses Gebäude die Generierung von Einfluss mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrimeFlavor'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrathDescription" value="Ermöglicht es Eisenschreiter-Ballistarii, Sydonianischen Dragonern, Fulguriten-Elektropriestern, Onager-Dünenläufern, Pteraxii-Sterilisatoren und Kreuzrittern, vernichtendere Angriffe durchzuführen."/>
	<entry name="AdeptusMechanicus/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von schweren Waffen."/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiah'/>"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Erhöht für dieses Gebäude die Generierung von Loyalität mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiahFlavor'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulDescription'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightDescription'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancer" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancer'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerDescription" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerDescription'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerFlavor" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerFlavor'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisation" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisation'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationDescription'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationFlavor'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/Omnispex" value="<string name='Traits/AdeptusMechanicus/Omnispex'/>"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="Skitarii-Strahlenkrieger und Skitarii-Jäger ignorieren jegliche Fernkampf-Schadensreduktion."/>
	<entry name="AdeptusMechanicus/OmnispexFlavor" value="<string name='Traits/AdeptusMechanicus/OmnispexFlavor'/>"/>
	<entry name="AdeptusMechanicus/OptateRestrictions" value="<string name='Traits/AdeptusMechanicus/OptateRestrictions'/>"/>
	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Erhöht das Bevölkerungslimit von Einkehr-Habitaten."/>
	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="<string name='Traits/AdeptusMechanicus/OptateRestrictionsFlavor'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocols'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Erhöht die Wachstumsrate von Städten."/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocolsFlavor'/>"/>
	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmDescription'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SolarReflectors" value="<string name='Traits/AdeptusMechanicus/SolarReflectors'/>"/>
	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Erhöht für dieses Gebäude die Energiegewinnung mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="<string name='Traits/AdeptusMechanicus/SolarReflectorsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeams'/>"/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Erhöht für dieses Gebäude die Nahrungsproduktion mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeamsFlavor'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenment" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenment'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentDescription'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentFlavor'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralism" value="<string name='Traits/AdeptusMechanicus/TerranGeneralism'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismDescription'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismFlavor'/>"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiency" value="Thermotauscher-Effektivität"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyDescription" value="Erhöht den Produktionsbonus durch „Energieschub“."/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyFlavor" value="Paradoxerweise können die Verlangsamung der Zufuhr zum Thermotauscher und die Einspeisung des Plasmas durch einen zweiten Satz von Wärmekupplungen und Kondensatoren die Betriebsdauer über die nominellen Werte hinaus verlängern – oder zumindest die Anzahl an verlorenen Leben und erforderlichen Reparaturen verringern."/>
	<entry name="AdeptusMechanicus/TriplexNecessity" value="<string name='Traits/AdeptusMechanicus/TriplexNecessity'/>"/>
	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Erhöht für dieses Gebäude die Erzgewinnung mit jedem Gebäude auf einem angrenzenden Hexfeld."/>
	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="<string name='Traits/AdeptusMechanicus/TriplexNecessityFlavor'/>"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannon" value="<string name='Weapons/TwinLinkedIcarusAutocannon'/>"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonDescription" value="Ermöglicht es Kreuzrittern, Icarus-Zwillingsmaschinenkanonen einzusetzen."/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonFlavor" value="<string name='Weapons/TwinLinkedIcarusAutocannonFlavor'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Erhöht den Moralverlust von feindlichen Einheiten, die an Sicarianische Infiltratoren angrenzen."/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
	<entry name="AdeptusMechanicus/XenariteAcceptance" value="Xenariten-Akzeptanz"/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceDescription" value="Erhöht die feste Anzahl von Forschungspunkten, die durch Ruinen des Vaul gewonnen werden."/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceFlavor" value="Es scheint, als ob die Xenariten-Techpriester vom Mond Stygies VIII auch auf Gladius Primus aktiv sind. Sie stationieren an jedem Außenposten Beobachtungseinheiten und Drohnen, um die Xenos besser zu verstehen. Der praktische Nutzen für die Forschung liegt auf der Hand…"/>
	<entry name="AstraMilitarum/AdditionalHeavyBolters" value="Zusätzliche Schwere Bolter"/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersDescription" value="Baneblades, Kampfpanzer Leman Russ, Kampfpanzer Rogal Dorn und Valkyries erhalten zusätzliche Schwere Bolter."/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachine" value="<string name='Actions/AwakenTheMachine'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachineDescription" value="Ermöglicht es Tech-Priest Enginseers, den Schaden von Fahrzeugen zu erhöhen."/>
	<entry name="AstraMilitarum/AwakenTheMachineFlavor" value="<string name='Actions/AwakenTheMachineFlavor'/>"/>
	<entry name="AstraMilitarum/BanebladeLascannon" value="Baneblade-Laserkanonen"/>
	<entry name="AstraMilitarum/BanebladeLascannonDescription" value="Baneblades erhalten zusätzliche Laserkanonen."/>
	<entry name="AstraMilitarum/BanebladeLascannonFlavor" value="<string name='Weapons/LascannonFlavor'/>"/>
	<entry name="AstraMilitarum/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag von Granaten, Raketen und Explosivwaffen."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Erhöht den Panzerungsdurchschlag von Bolterwaffen."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BruteShield" value="<string name='Traits/BruteShield'/>"/>
	<entry name="AstraMilitarum/BruteShieldDescription" value="Erhöht den Schaden und die Schadensreduktion bei Bullgryns."/>
	<entry name="AstraMilitarum/BruteShieldFlavor" value="<string name='Traits/BruteShieldFlavor'/>"/>
	<entry name="AstraMilitarum/CamoNetting" value="<string name='Traits/CamoNetting'/>"/>
	<entry name="AstraMilitarum/CamoNettingDescription" value="Erhöht die Fernkampf-Schadensreduktion von Bodenfahrzeugen."/>
	<entry name="AstraMilitarum/CamoNettingFlavor" value="<string name='Traits/CamoNettingFlavor'/>"/>
	<entry name="AstraMilitarum/ChaffLauncher" value="Täuschkörperwerfer"/>
	<entry name="AstraMilitarum/ChaffLauncherDescription" value="Ermöglicht es Thunderbolts und Marauder Bombern, Täuschkörper auszustoßen, die die Fernkampf-Schadensreduktion erhöhen."/>
	<entry name="AstraMilitarum/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="AstraMilitarum/CityTier2" value="<string name='Traits/AstraMilitarum/CityTier2'/>"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Erhöht den Radius, in dem sich Städte Hexfelder einverleiben können."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="<string name='Traits/AstraMilitarum/CityTier2Flavor'/>"/>
	<entry name="AstraMilitarum/CityTier3" value="<string name='Traits/AstraMilitarum/CityTier3'/>"/>
	<entry name="AstraMilitarum/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="<string name='Traits/AstraMilitarum/CityTier3Flavor'/>"/>
	<entry name="AstraMilitarum/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="AstraMilitarum/DozerBladeDescription" value="Verringert den Bewegungsmalus von Panzern in Wäldern und imperialen Ruinen."/>
	<entry name="AstraMilitarum/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterie."/>
	<entry name="AstraMilitarum/ExtraInfantryArmourFlavor" value="Die meisten Infanterieeinheiten des 41. Jahrtausends sind mit Armaplastrüstung oder einer ähnlichen Panzerung ausgestattet. Falls einem Kommandanten daran liegt, dass seine Truppen mehr als nur eine minimale Überlebenschance haben, wird er sie mit einer Panzerung ausrüsten, die ähnlich schützt wie die von der Armaplastrüstung abgeleitete Plattenrüstung des Astra Militarum."/>
	<entry name="AstraMilitarum/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="AstraMilitarum/ExtraVehicleArmourDescription" value="Erhöht die Panzerung von Fahrzeugen."/>
	<entry name="AstraMilitarum/ExtraVehicleArmourFlavor" value="Zusätzliche Panzerplatten an Panzern anzubringen könnte von Techpriestern als höchste Form der Ketzerei erachtet werden. Bei Soldaten des Astra Militarum ist es hingegen gang und gäbe. Ablative Schilde oder selbst angepasste Panzerung zum Schutz gegen bestimmte Waffen sind also nicht ungewöhnlich."/>
	<entry name="AstraMilitarum/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="AstraMilitarum/FragGrenadeDescription" value="Ermöglicht es Bullgryns, Feldgeschützbatterien, Soldaten, Heavy Weapons Squads, Lord Commissars, Primaris Psykern, Tech-Priest Enginseers und Tempestus Scions, Anti-Personen-Granaten einzusetzen."/>
	<entry name="AstraMilitarum/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AstraMilitarum/HammerOfWrathDescription" value="Ermöglicht es Scout Sentinels und Bullgryns, vernichtendere Angriffe durchzuführen."/>
	<entry name="AstraMilitarum/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissileDescription" value="Ermöglicht es Bodenfahrzeugen, Suchkopfraketen abzufeuern."/>
	<entry name="AstraMilitarum/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialSplendour" value="<string name='Traits/ImperialSplendour'/>"/>
	<entry name="AstraMilitarum/ImperialSplendourDescription" value="Erhöht den Einfluss von Städten des Astra Militarum."/>
	<entry name="AstraMilitarum/ImperialSplendourFlavor" value="<string name='Traits/ImperialSplendourFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpoint" value="<string name='Units/AstraMilitarum/ImperialStrongpoint'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpointDescription" value="Ermöglicht es Tech-Priest Enginseers, Befestigungen mit Schweren Boltern zu errichten."/>
	<entry name="AstraMilitarum/ImperialStrongpointFlavor" value="<string name='Units/AstraMilitarum/ImperialStrongpointFlavor'/>"/>
	<entry name="AstraMilitarum/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="AstraMilitarum/KrakGrenadeDescription" value="Ermöglicht es Feldgeschützbatterien, Soldaten, Heavy Weapons Squads, Lord Commissars, Tech-Priest Enginseers und Tempestus Scions, Panzerabwehrgranaten einzusetzen."/>
	<entry name="AstraMilitarum/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Erhöht den Panzerungsdurchschlag von Laserwaffen."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="AstraMilitarum/MediPack" value="<string name='Actions/MediPack'/>"/>
	<entry name="AstraMilitarum/MediPackDescription" value="Ermöglicht es Soldaten und Tempestus Scions, sich im Kampf selbst zu heilen."/>
	<entry name="AstraMilitarum/MediPackFlavor" value="<string name='Actions/MediPackFlavor'/>"/>
	<entry name="AstraMilitarum/Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="AstraMilitarum/MisfortuneDescription" value="Ermöglicht es Wyrdvane Psykers, feindliche Einheiten zu verfluchen, woraufhin sie erhöhten Schaden erleiden."/>
	<entry name="AstraMilitarum/MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="AstraMilitarum/RecoveryGear" value="<string name='Traits/RecoveryGear'/>"/>
	<entry name="AstraMilitarum/RecoveryGearDescription" value="Erhöht die Heilungsrate von Bodenfahrzeugen."/>
	<entry name="AstraMilitarum/RecoveryGearFlavor" value="<string name='Traits/RecoveryGearFlavor'/>"/>
	<entry name="AstraMilitarum/RelicPlating" value="<string name='Traits/RelicPlating'/>"/>
	<entry name="AstraMilitarum/RelicPlatingDescription" value="Erhöht die Hexenfeuer-Schadensreduktion bei Bodenfahrzeugen."/>
	<entry name="AstraMilitarum/RelicPlatingFlavor" value="<string name='Traits/RelicPlatingFlavor'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissile" value="<string name='Weapons/SkystrikeMissile'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissileDescription" value="Ermöglicht es Thunderbolts, Flugabwehrraketen einzusetzen."/>
	<entry name="AstraMilitarum/SkystrikeMissileFlavor" value="<string name='Weapons/SkystrikeMissileFlavor'/>"/>
	<entry name="AstraMilitarum/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilots" value="<string name='Traits/TrainedSentinelPilots'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsDescription" value="Erhöht den Schaden von Scout Sentinels."/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsFlavor" value="<string name='Traits/TrainedSentinelPilotsFlavor'/>"/>
	<entry name="AstraMilitarum/VoidShieldGenerator" value="<string name='Units/AstraMilitarum/VoidShieldGenerator'/>"/>
	<entry name="AstraMilitarum/VoidShieldGeneratorDescription" value="Ermöglicht es Tech-Priest Enginseers, Schildgeneratoren zu errichten, die bei Einheiten in Reichweite für eine Fernkampf-Schadensreduktion sorgen."/>
	<entry name="AstraMilitarum/VoidShieldGeneratorFlavor" value="<string name='Units/AstraMilitarum/VoidShieldGeneratorFlavor'/>"/>
	<entry name="AstraMilitarum/VoxCaster" value="<string name='Traits/VoxCaster'/>"/>
	<entry name="AstraMilitarum/VoxCasterDescription" value="Verringert den Moralverlust von Soldaten und Tempestus Scions."/>
	<entry name="AstraMilitarum/VoxCasterFlavor" value="<string name='Traits/VoxCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculum'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Gewährt Champions des Chaos die Chance auf eine dauerhafte Erhöhung der Genauigkeit, wenn sie einen Gegner töten (Segen des Chaos)."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculumFlavor'/>"/>
	<entry name="ChaosSpaceMarines/BlastDamage" value="<string name='Traits/ChaosSpaceMarines/BlastDamage'/>"/>
	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag von Granaten, Raketen und Explosivwaffen."/>
	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Bloated" value="<string name='Traits/ChaosSpaceMarines/Bloated'/>"/>
	<entry name="ChaosSpaceMarines/BloatedDescription" value="Gewährt Champions des Chaos die Chance, ihre Trefferpunkte wiederherzustellen, wenn sie einen Gegner töten."/>
	<entry name="ChaosSpaceMarines/BloatedFlavor" value="<string name='Traits/ChaosSpaceMarines/BloatedFlavor'/>"/>
	<entry name="ChaosSpaceMarines/BoltDamage" value="<string name='Traits/ChaosSpaceMarines/BoltDamage'/>"/>
	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Erhöht den Panzerungsdurchschlag von Bolterwaffen."/>
	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosRising" value="Wachsendes Chaos"/>
	<entry name="ChaosSpaceMarines/ChaosRisingDescription" value="Verringert die Kosten für die Gründung neuer Städte."/>
	<entry name="ChaosSpaceMarines/ChaosRisingFlavor" value="Die imperiale Bevölkerung auf dem Planeten Gladius weiß nicht viel über das Chaos, aber die über sie hereingebrochene Hölle in Form der Xenos-Invasionen hat den Glauben an den fernen Imperator in seinen Grundfesten erschüttert. Für Ihre Chaos Cultists und Dark Apostles ist es ein Leichtes, sie den Göttern in die Arme zu treiben – und damit ihrem Untergang."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="<string name='Traits/ChaosSpaceMarines/CityTier2'/>"/>
	<entry name="ChaosSpaceMarines/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier2Flavor'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3" value="<string name='Traits/ChaosSpaceMarines/CityTier3'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier3Flavor'/>"/>
	<entry name="ChaosSpaceMarines/CrystallineBody" value="<string name='Traits/ChaosSpaceMarines/CrystallineBody'/>"/>
	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Gewährt Champions des Chaos die Chance auf dauerhaft erhöhte Trefferpunkte, wenn sie einen Gegner töten (Segen des Chaos)."/>
	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="<string name='Traits/ChaosSpaceMarines/CrystallineBodyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Ermöglicht es Rhinos, Defilers und Land Raiders des Chaos, Abwehrfeuer-Attacken zu verhindern."/>
	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterie."/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourFlavor" value="Die meisten Infanterieeinheiten des 41. Jahrtausends sind mit Armaplastrüstung oder einer ähnlichen Panzerung ausgestattet. Falls einem Kommandanten daran liegt, dass seine Truppen mehr als nur eine minimale Überlebenschance haben, wird er sie mit einer Panzerung ausrüsten, die ähnlich schützt wie die von der Armaplastrüstung abgeleitete Plattenrüstung des Astra Militarum."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourDescription" value="Erhöht die Panzerung von Fahrzeugen."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourFlavor" value="Zusätzliche Panzerplatten an Panzern anzubringen könnte von Techpriestern als höchste Form der Ketzerei erachtet werden. Bei Soldaten des Astra Militarum ist es hingegen gang und gäbe. Ablative Schilde oder selbst angepasste Panzerung zum Schutz gegen bestimmte Waffen sind also nicht ungewöhnlich."/>
	<entry name="ChaosSpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="ChaosSpaceMarines/FragGrenadeDescription" value="Ermöglicht es Chaos Lords, Chaos Space Marines, Havocs, Khorne Berzerkers, Masters of Possession und Warpsmiths, Anti-Personen-Granaten einzusetzen."/>
	<entry name="ChaosSpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutation" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutation'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="Gewährt Chaos Lords, Chaos Space Marines, Chaosterminatoren, Havocs, Khorne Berzerkers, Masters of Possession, Warp Talons und Warpsmiths einen zufälligen freigeschalteten Segen des Chaos."/>
	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutationFlavor'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrathDescription" value="Ermöglicht es Daemon Princes, Defilers, Schmiedemonstren, Großen Messingskorpionen, Helbrutes, Maulerfiends, Venomcrawlers und Warp Talons, vernichtendere Angriffe durchzuführen."/>
	<entry name="ChaosSpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/HavocLauncher" value="<string name='Weapons/HavocLauncher'/>"/>
	<entry name="ChaosSpaceMarines/HavocLauncherDescription" value="Rhinos und Land Raiders des Chaos erhalten eine Explosivwaffe mit mittlerer Reichweite."/>
	<entry name="ChaosSpaceMarines/HavocLauncherFlavor" value="<string name='Weapons/HavocLauncherFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlame" value="<string name='Actions/ChaosSpaceMarines/IconOfFlame'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlameDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlameFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeance" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeance'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenadeDescription" value="Ermöglicht es Chaos Lords, Chaos Space Marines, Havocs, Khorne Berzerkers, Masters of Possession und Warpsmiths, Panzerabwehrgranaten einzusetzen."/>
	<entry name="ChaosSpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LasDamage" value="<string name='Traits/ChaosSpaceMarines/LasDamage'/>"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Erhöht den Panzerungsdurchschlag von Laser- und Plasmawaffen."/>
	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/LasDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Mechanoid" value="<string name='Traits/ChaosSpaceMarines/Mechanoid'/>"/>
	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Gewährt Champions des Chaos die Chance auf eine dauerhafte Erhöhung der Panzerung, wenn sie einen Gegner töten (Segen des Chaos)."/>
	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="<string name='Traits/ChaosSpaceMarines/MechanoidFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MeleeDamage" value="<string name='Traits/ChaosSpaceMarines/MeleeDamage'/>"/>
	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="ChaosSpaceMarines/MeltaBombDescription" value="Ermöglicht es Chaos Lords, Chaos Space Marines, Havocs und Khorne Berzerkers, eine Melterbombe einzusetzen, die äußerst effektiv gegen schwere Fahrzeuge und Befestigungsanlagen ist."/>
	<entry name="ChaosSpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGods" value="Unsere Leben für die Götter"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsDescription" value="Erhöht die Wachstumsrate durch Kultistenopfer."/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsFlavor" value="So viele wurden auf Gladius durch die Klingen der Kultisten getötet, dass der Tod eines einzelnen Geschöpfs keinerlei Bedeutung hat. Allerdings zeigen die Opfer eine erhöhte Wirkung, da diese Welt dank der Opfer näher an die gefallene Höllendimension der Dunklen Götter herangerückt ist. Dunkle Segen können dadurch leichter die Barriere des Immateriums durchbrechen."/>
	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
	<entry name="ChaosSpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="ChaosSpaceMarines/SmokeLauncherDescription" value="Ermöglicht es Rhinos, Defilers und Land Raiders des Chaos, eine Nebelwand zu erzeugen, welche die Fernkampf-Schadensreduktion erhöht."/>
	<entry name="ChaosSpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="ChaosSpaceMarines/TemporalDistortion" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortion'/>"/>
	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Gewährt Champions des Chaos die Chance auf eine dauerhafte Erhöhung der Bewegungspunkte, wenn sie einen Gegner töten (Segen des Chaos)."/>
	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWar'/>"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Verringert den Moralverlust und erhöht die Nahkampfgenauigkeit der Infanterie beim Kampf gegen Einheiten der Space Marines."/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWarFlavor'/>"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoyles" value="Warpfeuerspeier"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesDescription" value="Waffen der Rhinos, Defilers und Land Raiders des Chaos verursachen über einen bestimmten Zeitraum hinweg Schaden."/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesFlavor" value="Die Laufmündungen der Waffen dieses Fahrzeugs flackern beim Feuern in einem unnatürlichen Licht."/>
	<entry name="ChaosSpaceMarines/WarpFrenzy" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzy'/>"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Gewährt Champions des Chaos die Chance auf dauerhaft erhöhte Angriffe, wenn sie einen Gegner töten (Segen des Chaos)."/>
	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzyFlavor'/>"/>
	<entry name="CompendiumFlavor" value="Jede Fraktion erweitert ihre Städte auf unterschiedliche Weise. Ein Waaaghboss der Orks zum Beispiel stößt zu seinen Boyz, um die schützenden Mauern anzuheben und sie weiter nach vorne zu schieben. Ein Necron Lord wiederum befiehlt seinen Sklaven vielleicht, einen größeren Teil seiner uralten Gruft auszugraben. Wie auch immer eine Fraktion vorgeht, stets geht es ihr darum, mehr Platz für fortschrittlichere Gebäude zu schaffen, die der Fraktion mehr Möglichkeiten eröffnen."/>
	<entry name="Drukhari/AssaultWeaponBonus" value="<string name='Traits/Drukhari/AssaultWeaponBonus'/>"/>
	<entry name="Drukhari/AssaultWeaponBonusDescription" value="<string name='Upgrades/Eldar/AssaultWeaponBonusDescription'/>"/>
	<entry name="Drukhari/AssaultWeaponBonusFlavor" value="<string name='Traits/Drukhari/AssaultWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgrade" value="Gezielte Attentate"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="Erhöht die Loyalität von Städten der Drukhari."/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="In den Straßen von Städten der Drukhari ist der Tod allgegenwärtig: In jedem Winkel liegen Leichen und warten darauf, von den Ur'Ghulen weggeschleppt zu werden. Der Tod von Draconten und Kabalenadligen ist allerdings eher eine Seltenheit – zumindest war es so, bis die Incubi des Archons ein weiteres Komplott aufgedeckt haben. Nun reicht schon der kleinste Hinweis auf Untreue, damit ein weiterer Adliger auf einem Pfahl über dem Schlafgemach des Archons endet…"/>
	<entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
	<entry name="Drukhari/BonusResourcesDescription" value="<string name='Actions/Drukhari/BonusResourcesDescription'/>"/>
	<entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
	<entry name="Drukhari/CityTier2" value="<string name='Traits/Drukhari/CityTier2'/>"/>
	<entry name="Drukhari/CityTier2Description" value="<string name='Traits/Drukhari/CityTier2Description'/>"/>
	<entry name="Drukhari/CityTier2Flavor" value="<string name='Traits/Drukhari/CityTier2Flavor'/>"/>
	<entry name="Drukhari/CityTier3" value="<string name='Traits/Drukhari/CityTier3'/>"/>
	<entry name="Drukhari/CityTier3Description" value="<string name='Traits/Drukhari/CityTier3Description'/>"/>
	<entry name="Drukhari/CityTier3Flavor" value="<string name='Traits/Drukhari/CityTier3Flavor'/>"/>
	<entry name="Drukhari/CombatDrugsUpgrade" value="Zwielichtiger Kulthandel"/>
	<entry name="Drukhari/CombatDrugsUpgradeDescription" value="Ermöglicht es allen Infanterieeinheiten der Drukhari, Kampfdrogen zu benutzen."/>
	<entry name="Drukhari/CombatDrugsUpgradeFlavor" value="Kampfdrogen sind überall in der Gesellschaft der Drukhari weitverbreitet, doch die Hekatarii-Kulte nutzen sie trotz ihrer schädlichen Wirkung auf ihre Physiologie und Lebenserwartung am exzessivsten. Einen Verbindungsmann zu diesen Kulten zu haben, der auf die riesigen Vorräte dieser schrecklichen Substanzen zugreifen kann, ist für eine Kabale auf Realraum-Raubzügen ein großer Vorteil."/>
	<entry name="Drukhari/CorsairOutpostsUpgrade" value="Seelenrausch"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="Erhöht die standardmäßige Wachstumsrate von Städten der Drukhari."/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="In der Dunklen Stadt Commorragh verbreiten sich Gerüchte wie ein Lauffeuer. Man erzählt sich vom Reichtum auf Gladius Primus, von Milliarden leidender Menschen, die dort unter dem Warpsturm festsitzen, und vom seltsamen Phantomkristallkern des Planeten mit seinen unzähligen verlorenen Aeldari-Seelen… Ob wahr oder nicht, die Drukhari kommen. Doch wer hat diese Gerüchte überhaupt in die Welt gesetzt?"/>
	<entry name="Drukhari/EnergyBuildingBonus" value="<string name='Traits/Drukhari/EnergyBuildingBonus'/>"/>
	<entry name="Drukhari/EnergyBuildingBonusDescription" value="Erhöht die Energiegewinnung durch Energie erzeugende Gebäude."/>
	<entry name="Drukhari/EnergyBuildingBonusFlavor" value="<string name='Traits/Drukhari/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
	<entry name="Drukhari/EnhancedAethersailsDescription" value="Erhöht die Bewegung von Schattenbarken, Schattenjägern und Tantaloi."/>
	<entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
	<entry name="Drukhari/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Drukhari/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterie."/>
	<entry name="Drukhari/ExtraInfantryArmourFlavor" value="Eine ganze Armee mit Geisterharnischen auszustatten würde die Kriegskasse leeren. Einigen wenigen Auserwählten gewährt der Archon jedoch diesen Luxus. Geisterharnische werden aus eigentümlichen Kunstharzen gefertigt und mit Einschlüssen von Gas versehen, das leichter ist als Luft. Diese Kombination sorgt dafür, dass die Harnische beträchtlichen Schutz bieten, aber unwahrscheinlich leicht sind."/>
	<entry name="Drukhari/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Drukhari/ExtraVehicleArmourDescription" value="<string name='Traits/ExtraVehicleArmourDescription'/>"/>
	<entry name="Drukhari/ExtraVehicleArmourFlavor" value="Die Fahrzeuge der Drukhari sind mit Flackerfeldern oder Nachtfeldern ausgestattet und daher schwer auszumachen und noch schwerer zu treffen. Die wertvollsten Maschinen mit Geisterharnischen zu schützen ist ein teures Unterfangen, reduziert jedoch ihr Gewicht und erhöht ihre Geschwindigkeit, wodurch die Wahrscheinlichkeit, getroffen zu werden, zusätzlich verringert wird."/>
	<entry name="Drukhari/FieldRepairs" value="<string name='Actions/Drukhari/FieldRepairs'/>"/>
	<entry name="Drukhari/FieldRepairsDescription" value="Verleiht allen Fahrzeugeinheiten der Drukhari die Fähigkeit, Trefferpunkte wiederherzustellen."/>
	<entry name="Drukhari/FieldRepairsFlavor" value="<string name='Actions/Drukhari/FieldRepairsFlavor'/>"/>
	<entry name="Drukhari/GraveLotus" value="Grablotus"/>
	<entry name="Drukhari/GraveLotusDescription" value="Kampfdrogen erhöhen den Nahkampfschaden."/>
	<entry name="Drukhari/GraveLotusFlavor" value="Im Hain des Teufels sprießt aus einem Mosaik von Toten der übelriechende Grablotus. Es handelt sich dabei um einen leuchtend violetten Pilz, der den kürzlich Verstorbenen die bereits fast gänzlich entschwundene Kraft entzieht, um sein Wachstum zu beschleunigen. Die Hekatarii-Kulte nehmen den Grablotus in flüssiger Form zu sich, um ihre eigenen physischen Kräfte zu stärken."/>
	<entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
	<entry name="Drukhari/GrislyTrophiesDescription" value="Verleiht Schattenvipern, Schattenbarken, Schattenjägern und Tantaloi eine Aura, die den Moralverlust angrenzender nicht-feindlicher Einheiten der Drukhari verringert."/>
	<entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
	<entry name="Drukhari/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Drukhari/HammerOfWrathDescription" value="Ermöglicht es Einheiten des Typs Cronos, Hellion, Raubjäger, Harpyie und Talos, vernichtendere Angriffe durchzuführen."/>
	<entry name="Drukhari/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Drukhari/HaywireGrenade" value="<string name='Weapons/HaywireGrenade'/>"/>
	<entry name="Drukhari/HaywireGrenadeDescription" value="Ermöglicht es Kabalenkriegern, Succubi und Hekatarii, Fahrzeugabwehrgranaten einzusetzen."/>
	<entry name="Drukhari/HaywireGrenadeFlavor" value="<string name='Weapons/HaywireGrenadeFlavor'/>"/>
	<entry name="Drukhari/HeavyWeaponBonus" value="<string name='Traits/Drukhari/HeavyWeaponBonus'/>"/>
	<entry name="Drukhari/HeavyWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von schweren Waffen."/>
	<entry name="Drukhari/HeavyWeaponBonusFlavor" value="<string name='Traits/Drukhari/HeavyWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/Hypex" value="Hypex"/>
	<entry name="Drukhari/HypexDescription" value="Kampfdrogen verleihen die Eigenschaft „Durch Deckung bewegen“."/>
	<entry name="Drukhari/HypexFlavor" value="Eine Psychne einzufangen ist ein gefährliches Unterfangen, aber wem es gelingt, der kann sie für einen hohen Preis an die Hekatarii-Kulte verkaufen. Die Kampfdroge Hypex, die aus den Gehirnflüssigkeiten der insektoiden Kreatur destilliert wird, steigert das ohnehin schon hohe Reaktionsvermögen der Drukhari auf ein wirklich erstaunliches Niveau."/>
	<entry name="Drukhari/MeleeWeaponBonus" value="<string name='Traits/Drukhari/MeleeWeaponBonus'/>"/>
	<entry name="Drukhari/MeleeWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="Drukhari/MeleeWeaponBonusFlavor" value="<string name='Traits/Drukhari/MeleeWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/NightShields" value="<string name='Traits/Drukhari/NightShields'/>"/>
	<entry name="Drukhari/NightShieldsDescription" value="Erhöht die Fernkampf-Schadensreduktion von Schattenbarken, Schattenjägern, Sichelflügel-Jägern, Tantaloi und Nachtrabe-Jagdbombern."/>
	<entry name="Drukhari/NightShieldsFlavor" value="<string name='Traits/Drukhari/NightShieldsFlavor'/>"/>
	<entry name="Drukhari/Painbringer" value="Schmerzbringer"/>
	<entry name="Drukhari/PainbringerDescription" value="Kampfdrogen verleihen die Schadensreduktion „Verletzungen ignorieren“."/>
	<entry name="Drukhari/PainbringerFlavor" value="Nur der verbannte Herzog Sliscus hat Anspruch auf einen ständigen Vorrat an Schmerzbringer, eines der seltensten Augmentationselixiere. Es härtet die Haut zu einer flexiblen Hülle, die so widerstandsfähig ist wie gehärtetes Leder. Dieser Prozess ist äußerst schmerzhaft, wenngleich die Befürworter diese Schmerzen als einen geringen Preis erachten, den man für den Nutzen bereit sein muss zu zahlen."/>
	<entry name="Drukhari/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
	<entry name="Drukhari/PlasmaGrenadeDescription" value="Ermöglicht es Archonten, Harpyien, Succubi und Hekatarii, verschiedenste Granaten einzusetzen."/>
	<entry name="Drukhari/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
	<entry name="Drukhari/RaiderFortress" value="<string name='Actions/Drukhari/RaiderFortress'/>"/>
	<entry name="Drukhari/RaiderFortressDescription" value="Verleiht die Fähigkeit, neue Städte auf einverleibten Netzportalen zu gründen."/>
	<entry name="Drukhari/RaiderFortressFlavor" value="<string name='Actions/Drukhari/RaiderFortressFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsDamage" value="<string name='Traits/Drukhari/RaidersTacticsDamage'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageDescription" value="Erhöht den Schaden von Infanterieeinheiten der Drukhari, wenn sie eine Transporteinheit verlassen."/>
	<entry name="Drukhari/RaidersTacticsDamageFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageReduction" value="<string name='Traits/Drukhari/RaidersTacticsDamageReduction'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Erhöht die Unverwundbar-Schadensreduktion von Infanterieeinheiten der Drukhari, wenn sie eine Transporteinheit verlassen."/>
	<entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageReductionFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsHealingRate" value="<string name='Traits/Drukhari/RaidersTacticsHealingRate'/>"/>
	<entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Erhöht die Heilungsrate transportierter Infanterieeinheiten."/>
	<entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="<string name='Traits/Drukhari/RaidersTacticsHealingRateFlavor'/>"/>
	<entry name="Drukhari/SacrificeToKhaine" value="<string name='Traits/Drukhari/SacrificeToKhaine'/>"/>
	<entry name="Drukhari/SacrificeToKhaineDescription" value="<string name='Actions/Drukhari/SacrificeToKhaineDescription'/>"/>
	<entry name="Drukhari/SacrificeToKhaineFlavor" value="<string name='Traits/Drukhari/SacrificeToKhaineFlavor'/>"/>
	<entry name="Drukhari/ShroudGate" value="<string name='Traits/Drukhari/ShroudGate'/>"/>
	<entry name="Drukhari/ShroudGateDescription" value="Erhöht die Fernkampf-Schadensreduktion von Einheiten, die ein Netzportal oder einen Portalfokus nutzen."/>
	<entry name="Drukhari/ShroudGateFlavor" value="<string name='Traits/Drukhari/ShroudGateFlavor'/>"/>
	<entry name="Drukhari/SoulHungerCost" value="Seelenschleuser"/>
	<entry name="Drukhari/SoulHungerCostDescription" value="Verringert die Kosten von Seelenhunger-Fähigkeiten."/>
	<entry name="Drukhari/SoulHungerCostFlavor" value="In dem Maße, wie sich die Einfälle der Drukhari in den Realraum häufen, steigt auch die Anzahl der Verbindungen zwischen dieser Welt und Commorragh. Selbst wenn sie physisch versiegelt wurden, scheint die Kabale noch in der Lage zu sein, Macht aus diesen Verbindungen zu ziehen und geraubtes Leben hindurchzuleiten."/>
	<entry name="Drukhari/SoulHungerLoyalty" value="<string name='Traits/Drukhari/SoulHungerLoyalty'/>"/>
	<entry name="Drukhari/SoulHungerLoyaltyDescription" value="<string name='Actions/Drukhari/SoulHungerLoyaltyDescription'/>"/>
	<entry name="Drukhari/SoulHungerLoyaltyFlavor" value="<string name='Traits/Drukhari/SoulHungerLoyaltyFlavor'/>"/>
	<entry name="Drukhari/SoulHungerOutposts" value="<string name='Traits/Drukhari/SoulHungerOutposts'/>"/>
	<entry name="Drukhari/SoulHungerOutpostsDescription" value="<string name='Actions/Drukhari/SoulHungerOutpostsDescription'/>"/>
	<entry name="Drukhari/SoulHungerOutpostsFlavor" value="<string name='Traits/Drukhari/SoulHungerOutpostsFlavor'/>"/>
	<entry name="Drukhari/SoulHungerUpgrade" value="Seelensadisten"/>
	<entry name="Drukhari/SoulHungerUpgradeDescription" value="Erhöht den Einfluss, den Einheiten der Drukhari für das Ausschalten von feindlichen Einheiten generieren."/>
	<entry name="Drukhari/SoulHungerUpgradeFlavor" value="„Du musst sie betäuben. Töte die Wachen. Öffne das Tor. Wecke sie vorsichtig auf. Führe sie hinaus in die Freiheit. Gib ihnen die Hoffnung auf Flucht. Nimm die Verfolgung auf. Täusche deinen Tod vor. Zeig ihnen einen Ausgang. Dann entlarve die Scharade und bring sie mit einem boshaften Grinsen zurück in die Folterkammer. Ohne sie auch nur zu berühren hast du ihnen schlimme seelische Schmerzen zugefügt, die sie nicht mehr loswerden.“ – Gyrthineus Roche, Archon der Letzten Klinge"/>
	<entry name="Drukhari/FeastOfTorment" value="<string name='Traits/Drukhari/FeastOfTorment'/>"/>
	<entry name="Drukhari/FeastOfTormentDescription" value="<string name='Actions/Drukhari/FeastOfTormentDescription'/>"/>
	<entry name="Drukhari/FeastOfTormentFlavor" value="<string name='Traits/Drukhari/FeastOfTormentFlavor'/>"/>
	<entry name="Drukhari/SoulShelling" value="<string name='Actions/Drukhari/SoulShelling'/>"/>
	<entry name="Drukhari/SoulShellingDescription" value="<string name='Actions/Drukhari/SoulShellingDescription'/>"/>
	<entry name="Drukhari/SoulShellingFlavor" value="<string name='Actions/Drukhari/SoulShellingFlavor'/>"/>
	<entry name="Drukhari/Splintermind" value="Seelenbrecher"/>
	<entry name="Drukhari/SplintermindDescription" value="Kampfdrogen verringern den Moralverlust."/>
	<entry name="Drukhari/SplintermindFlavor" value="Seelenbrecher wird aus den kristallinen Überresten eines toten Runenpropheten der Aeldari hergestellt. Diese staubähnliche Substanz sorgt zwar nicht für Hellsichtigkeit, ermöglicht es dem Anwender jedoch, mit seinem Gehirn mehrere Gedankengänge gleichzeitig zu verarbeiten – ein unschätzbarer Vorteil, wenn das Chaos in der Hitze des Gefechts selbst den durchdachtesten Schlachtplan zunichtemacht."/>
	<entry name="Drukhari/TormentGrenadeLaunchers" value="<string name='Weapons/TormentGrenadeLaunchers'/>"/>
	<entry name="Drukhari/TormentGrenadeLaunchersDescription" value="Schattenbarken, Schattenjäger und Tantaloi erhalten Terrorgas-Granatwerfer."/>
	<entry name="Drukhari/TormentGrenadeLaunchersFlavor" value="<string name='Weapons/TormentGrenadeLaunchersFlavor'/>"/>
	<entry name="Drukhari/WealthPlunder" value="<string name='Actions/Drukhari/WealthPlunder'/>"/>
	<entry name="Drukhari/WealthPlunderDescription" value="<string name='Actions/Drukhari/WealthPlunderDescription'/>"/>
	<entry name="Drukhari/WealthPlunderFlavor" value="<string name='Actions/Drukhari/WealthPlunderFlavor'/>"/>
	<entry name="Drukhari/WeaponRacks" value="<string name='Traits/Drukhari/WeaponRacks'/>"/>
	<entry name="Drukhari/WeaponRacksDescription" value="Einheiten, die eine Schattenbarke oder einen Tantalos verlassen, werden mit Zwillingsausführungen ihrer Fernkampfwaffen ausgestattet."/>
	<entry name="Drukhari/WeaponRacksFlavor" value="<string name='Traits/Drukhari/WeaponRacksFlavor'/>"/>
	<entry name="Drukhari/WebwayTravelAction" value="Realraum-Invasion"/>
	<entry name="Drukhari/WebwayTravelActionDescription" value="Entfernt die Aktionskosten für Netzreisen."/>
	<entry name="Drukhari/WebwayTravelActionFlavor" value="Das Durchschreiten eines Netzportals ist an sich zwar keine Kunst, doch versucht man, eine ganze Armee hindurchzubewegen, sind die Truppen aufgrund der schieren Größe des Manövers verwundbar. Die Drukhari lösen das Problem dadurch, dass sie temporäre Tore um permanente Portale herum errichten, durch die das gesamte Truppenkontingent zügig geschleust werden kann."/>
	<entry name="Eldar/AircraftBuildingBonus" value="<string name='Traits/Eldar/AircraftBuildingBonus'/>"/>
	<entry name="Eldar/AircraftBuildingBonusDescription" value="Erhöht die Produktionsrate von Portalspitzen."/>
	<entry name="Eldar/AircraftBuildingBonusFlavor" value="<string name='Traits/Eldar/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Eldar/AssaultWeaponBonus" value="<string name='Traits/Eldar/AssaultWeaponBonus'/>"/>
	<entry name="Eldar/AssaultWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von Sturmwaffen."/>
	<entry name="Eldar/AssaultWeaponBonusFlavor" value="<string name='Traits/Eldar/AssaultWeaponBonusFlavor'/>"/>
	<entry name="Eldar/AsuryaniArrivalsBonus" value="Ruf des Runenpropheten"/>
	<entry name="Eldar/AsuryaniArrivalsBonusDescription" value="Verringert die Kosten für „Hilfe der Asuryani“."/>
	<entry name="Eldar/AsuryaniArrivalsBonusFlavor" value="„Meine Worte sind mehr als nur Ratschläge: Sie sind eine Verwünschung, gefiltert durch das Bewusstsein unserer schillernden Toten, um einen Hauch von Ordnung in das dem Chaos anheimgefallene Universum zu bringen. Unsere Rasse versteht dies und schenkt meinen Worten Gehör, wobei sie mich nicht als Diktator, sondern als aufmerksamen Schüler erachtet.“<br/> – Runenprophet Kataimon von Malan'tai"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2" value="Gelöbnis der Weltenschiffe"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Description" value="Verringert die Abklingzeit von „Hilfe der Asuryani“."/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Flavor" value="„Wenn die Runenleser und Runenpropheten verschiedener Weltenschiffe die gleiche Vision über die Zukunft ereilt, arbeiten sie und ihre Weltenschiffe als eine Einheit. Sie öffnen ihre eigenen Hallen des Lebens und Geistes, um für diese Zukunft einzustehen.“ – Lehrvortragsabschrift, Grigomen Delr, Freihändler und Hobbyxenologist"/>
	<entry name="Eldar/CityTier2" value="<string name='Traits/Eldar/CityTier2'/>"/>
	<entry name="Eldar/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Eldar/CityTier2Flavor" value="<string name='Traits/Eldar/CityTier2Flavor'/>"/>
	<entry name="Eldar/CityTier3" value="<string name='Traits/Eldar/CityTier3'/>"/>
	<entry name="Eldar/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Eldar/CityTier3Flavor" value="<string name='Traits/Eldar/CityTier3Flavor'/>"/>
	<entry name="Eldar/CleansingFlame" value="<string name='Actions/CleansingFlame'/>"/>
	<entry name="Eldar/CleansingFlameDescription" value="Verleiht Runenlesern die Fähigkeit, angrenzende gegnerische Einheiten mit einer gleißend hellen psionischen Flamme anzugreifen."/>
	<entry name="Eldar/CleansingFlameFlavor" value="<string name='Actions/CleansingFlameFlavor'/>"/>
	<entry name="Eldar/ConstructionBuildingBonus" value="<string name='Traits/Eldar/ConstructionBuildingBonus'/>"/>
	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Erhöht die Produktionsrate von Kristallsänger-Kantoreien."/>
	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="<string name='Traits/Eldar/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Eldar/CrackShot" value="<string name='Traits/Eldar/CrackShot'/>"/>
	<entry name="Eldar/CrackShotDescription" value="Erhöht die Genauigkeit und den Panzerungsdurchschlag von Feuerdrachen."/>
	<entry name="Eldar/CrackShotFlavor" value="<string name='Traits/Eldar/CrackShotFlavor'/>"/>
	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Traits/Eldar/CrystalTargetingMatrix'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixDescription" value="Erhöht vorübergehend die Genauigkeit von Kristalldrachen, Windwespen, Wellenschlangen und Kampfläufern."/>
	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Traits/Eldar/CrystalTargetingMatrixFlavor'/>"/>
	<entry name="Eldar/Dominate" value="<string name='Actions/Dominate'/>"/>
	<entry name="Eldar/DominateDescription" value="Ermöglicht es Seelengift-Phantomjägern, feindliche Einheiten zu betäuben, wenn es sich dabei nicht um Fahrzeuge oder Festungseinheiten handelt."/>
	<entry name="Eldar/DominateFlavor" value="<string name='Actions/DominateFlavor'/>"/>
	<entry name="Eldar/ExpertHunter" value="<string name='Traits/Eldar/ExpertHunter'/>"/>
	<entry name="Eldar/ExpertHunterDescription" value="Erhöht den Schaden von Schimmerspeeren gegen Monströse Kreaturen, Fahrzeuge und Festungseinheiten."/>
	<entry name="Eldar/ExpertHunterFlavor" value="<string name='Traits/Eldar/ExpertHunterFlavor'/>"/>
	<entry name="Eldar/ExtraInfantryArmour" value="Labyrinthische Aramidrüstung"/>
	<entry name="Eldar/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterie."/>
	<entry name="Eldar/ExtraInfantryArmourFlavor" value="Die thermoplastische Aramidrüstung der Aeldari ist nicht nur für bestmöglichen Schutz konzipiert. Sie ermöglicht es ihrem Träger auch, sich so frei zu bewegen, als wäre sie nicht vorhanden. Dadurch, dass die komplexen Ableitungsprotokolle weiterentwickelt wurden, ist es nun sogar möglich, die Widerstandsfähigkeit ohne Mobilitätseinbußen weiter zu erhöhen."/>
	<entry name="Eldar/ExtraVehicleArmour" value="Phantomkristall-Einflechtung"/>
	<entry name="Eldar/ExtraVehicleArmourDescription" value="Erhöht die Panzerung von Fahrzeugen."/>
	<entry name="Eldar/ExtraVehicleArmourFlavor" value="Alle Fahrzeuge und Bauwerke der Aeldari werden im wahrsten Sinne des Wortes mit Gesang erschaffen. Dafür zuständig sind die Kristallsänger, deren Gesangsfähigkeiten und psionische Kräfte ein konstruktives Gegenstück zum Kriegsgeschrei der Todesfeen bilden. Mit den Ressourcen auf Gladius Primus und dank einer Änderung ihrer inneren Harmonien können die Kristallsänger ihren Schöpfungen nun noch mehr Kraft einhauchen, wodurch sich ihre Widerstandsfähigkeit erhöht."/>
	<entry name="Eldar/FoodBuildingBonus" value="<string name='Traits/Eldar/FoodBuildingBonus'/>"/>
	<entry name="Eldar/FoodBuildingBonusDescription" value="Erhöht die Nahrungsproduktion in Ishas Gärten."/>
	<entry name="Eldar/FoodBuildingBonusFlavor" value="<string name='Traits/Eldar/FoodBuildingBonusFlavor'/>"/>
	<entry name="Eldar/GhostwalkMatrix" value="Geisterpfadmatrix"/>
	<entry name="Eldar/GhostwalkMatrixDescription" value="Ermöglicht es Kristalldrachen, Windwespen, Wellenschlangen und Kampfläufern, sich durch Deckung zu bewegen."/>
	<entry name="Eldar/GhostwalkMatrixFlavor" value="Eine Geisterpfadmatrix macht sich das Wissen und die Weisheit, die in einem Seelenstein enthalten sind, zunutze, um das Fahrzeug auf seinem Weg zu leiten."/>
	<entry name="Eldar/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Eldar/HammerOfWrathDescription" value="Ermöglicht es Schimmerspeeren, Himmelspropheten, Avataren des Khaine, Kampfläufern, Phantomlords und Phantomrittern, vernichtendere Angriffe durchzuführen."/>
	<entry name="Eldar/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Eldar/HeavyWeaponBonus" value="<string name='Traits/Eldar/HeavyWeaponBonus'/>"/>
	<entry name="Eldar/HeavyWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von schweren Waffen."/>
	<entry name="Eldar/HeavyWeaponBonusFlavor" value="<string name='Traits/Eldar/HeavyWeaponBonusFlavor'/>"/>
	<entry name="Eldar/HoloFields" value="<string name='Traits/Eldar/HoloFields'/>"/>
	<entry name="Eldar/HoloFieldsDescription" value="Gewährt Kristalldrachen, Windwespen und Wellenschlangen nach der Bewegung Unverwundbar-Schadensreduktion."/>
	<entry name="Eldar/HoloFieldsFlavor" value="<string name='Traits/Eldar/HoloFieldsFlavor'/>"/>
	<entry name="Eldar/InfantryBuildingBonus" value="<string name='Traits/Eldar/InfantryBuildingBonus'/>"/>
	<entry name="Eldar/InfantryBuildingBonusDescription" value="Erhöht die Produktionsrate von Asuryans Schmelztiegeln."/>
	<entry name="Eldar/InfantryBuildingBonusFlavor" value="<string name='Traits/Eldar/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Eldar/InfluenceBuildingBonus" value="<string name='Traits/Eldar/InfluenceBuildingBonus'/>"/>
	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Erhöht die Generierung von Einfluss durch Dome der Seher."/>
	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="<string name='Traits/Eldar/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Eldar/MarksmansEye" value="<string name='Traits/Eldar/MarksmansEye'/>"/>
	<entry name="Eldar/MarksmansEyeDescription" value="Erhöht die Genauigkeit von Blutjägern."/>
	<entry name="Eldar/MarksmansEyeFlavor" value="<string name='Traits/Eldar/MarksmansEyeFlavor'/>"/>
	<entry name="Eldar/MeleeWeaponBonus" value="<string name='Traits/Eldar/MeleeWeaponBonus'/>"/>
	<entry name="Eldar/MeleeWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="Eldar/MeleeWeaponBonusFlavor" value="<string name='Traits/Eldar/MeleeWeaponBonusFlavor'/>"/>
	<entry name="Eldar/OreBuildingBonus" value="<string name='Traits/Eldar/OreBuildingBonus'/>"/>
	<entry name="Eldar/OreBuildingBonusDescription" value="Erhöht die Erzgewinnung durch Altmarle des Vaul."/>
	<entry name="Eldar/OreBuildingBonusFlavor" value="<string name='Traits/Eldar/OreBuildingBonusFlavor'/>"/>
	<entry name="Eldar/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
	<entry name="Eldar/PlasmaGrenadeDescription" value="Ermöglicht es Gardisten und Autarchen, verschiedenste Granaten einzusetzen."/>
	<entry name="Eldar/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
	<entry name="Eldar/ResearchBuildingBonus" value="<string name='Traits/Eldar/ResearchBuildingBonus'/>"/>
	<entry name="Eldar/ResearchBuildingBonusDescription" value="Erhöht die Generierung von Forschungspunkten in Beinhäusern der Geister."/>
	<entry name="Eldar/ResearchBuildingBonusFlavor" value="<string name='Traits/Eldar/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Eldar/SpiritPreservationBonus" value="Escutcheon-Ummantelung"/>
	<entry name="Eldar/SpiritPreservationBonusDescription" value="Erhöht den Energieertrag, wenn Einheiten der Aeldari sterben."/>
	<entry name="Eldar/SpiritPreservationBonusFlavor" value="Lange Zeit wurden die Seelensteine der Aeldari mit einer einfachen Ummantelung geschützt, und man überließ es ihren Besitzern, für die Unversehrtheit der Seelensteine zu sorgen. Bei der Nutzung der uralten Netzstruktur auf Gladius Primus zeigte sich jedoch, dass jeder Phantomkristallschutz komplex verstärkt werden kann, wodurch mehr tote Krieger aus dem Schlund von „Sie, die dürstet“ gezogen werden können."/>
	<entry name="Eldar/SpiritStones" value="<string name='Traits/Eldar/SpiritStones'/>"/>
	<entry name="Eldar/SpiritStonesDescription" value="Verringert den Moralverlust von Kristalldrachen, Windwespen, Kampfläufern und Wellenschlangen."/>
	<entry name="Eldar/SpiritStonesFlavor" value="<string name='Traits/Eldar/SpiritStonesFlavor'/>"/>
	<entry name="Eldar/StarEngines" value="<string name='Traits/Eldar/StarEngines'/>"/>
	<entry name="Eldar/StarEnginesDescription" value="Erhöht die Bewegung von Kristalldrachen, Windwespen, Skorpionen, Kampfläufern und Wellenschlangen."/>
	<entry name="Eldar/StarEnginesFlavor" value="<string name='Traits/Eldar/StarEnginesFlavor'/>"/>
	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
	<entry name="Eldar/TranscendentBlissDescription" value="Ermöglicht es Städten, Einfluss für eine vorübergehende Erhöhung der Loyalität aufzuwenden."/>
	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
	<entry name="Eldar/TranscendentBlissBonus" value="Niedergang der Aeldari"/>
	<entry name="Eldar/TranscendentBlissBonusDescription" value="Erhöht die Generierung von Loyalität durch „Transzendente Glückseligkeit“."/>
	<entry name="Eldar/TranscendentBlissBonusFlavor" value="„Für einen freien Aeldari-Verstand gibt es weder Vernunft noch Wahnsinn, sondern nur eine Welle der perfekten Existenz, erfüllt von ihrem eigenen wilden Schwung.“<br/> – Ralamine Mung, Ordo Xenos"/>
	<entry name="Eldar/VectoredEngines" value="<string name='Traits/Eldar/VectoredEngines'/>"/>
	<entry name="Eldar/VectoredEnginesDescription" value="Ermöglicht es Kristalldrachen, Windwespen, Kampfläufern, Wellenschlangen und Skorpionen, vorübergehend ihre Panzerung gegen feindliche Fernkampfwaffen zu erhöhen."/>
	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Traits/Eldar/VectoredEnginesFlavor'/>"/>
	<entry name="Eldar/VehicleBuildingBonus" value="<string name='Traits/Eldar/VehicleBuildingBonus'/>"/>
	<entry name="Eldar/VehicleBuildingBonusDescription" value="Erhöht die Produktionsrate von Portalerweiterungen."/>
	<entry name="Eldar/VehicleBuildingBonusFlavor" value="<string name='Traits/Eldar/VehicleBuildingBonusFlavor'/>"/>
	<entry name="Eldar/WarShout" value="<string name='Actions/Eldar/WarShout'/>"/>
	<entry name="Eldar/WarShoutDescription" value="Ermöglicht es Todesfeen, angrenzende feindliche Einheiten zu demoralisieren."/>
	<entry name="Eldar/WarShoutFlavor" value="<string name='Actions/Eldar/WarShoutFlavor'/>"/>
	<entry name="Eldar/WebwayGateBonus" value="Netzkartografie"/>
	<entry name="Eldar/WebwayGateBonusDescription" value="Keine Kosten für das Aktivieren von Netzportalen."/>
	<entry name="Eldar/WebwayGateBonusFlavor" value="Nur die Harlekine und die Hüter der Schwarzen Bibliothek haben tiefgreifende Kenntnisse über die transdimensionale Beschaffenheit des Netzes der Tausend Tore. Sie offenbarten den Aeldari auf Gladius das, was sie in dieser Hinsicht über den Planeten wissen mussten, und sorgten damit dafür, dass diese uralten Portale wieder mit Leichtigkeit geöffnet werden können."/>
	<entry name="Eldar/WebwayGateBonus2" value="Netzrouten"/>
	<entry name="Eldar/WebwayGateBonus2Description" value="Verringert die Aktionskosten für Netzreisen."/>
	<entry name="Eldar/WebwayGateBonus2Flavor" value="Die Phantomseher wurden von der Schwarzen Bibliothek in das Wissen über die Netzstruktur auf dem Planeten Gladius eingeweiht und konnten so die kürzesten Routen zwischen zwei Portalen errechnen. Dadurch wurde das Reisen zwischen Portalen mit minimalstem Aufwand möglich."/>
	<entry name="Eldar/WebwayRedoubt" value="Netzbollwerk"/>
	<entry name="Eldar/WebwayRedoubtDescription" value="Verleiht die Fähigkeit, neue Städte auf einverleibten Netzportalen zu gründen."/>
	<entry name="Eldar/WebwayRedoubtFlavor" value="<string name='Actions/Eldar/WebwayRedoubtFlavor'/>"/>
	<entry name="Eldar/WraithknightStarcannon" value="Phantomritter-Sternenkanonen"/>
	<entry name="Eldar/WraithknightStarcannonDescription" value="Phantomritter erhalten zwei Sternenkanonen."/>
	<entry name="Eldar/WraithknightStarcannonFlavor" value="<string name='Weapons/StarcannonFlavor'/>"/>
	<entry name="Necrons/AircraftBuildingBonus" value="<string name='Traits/Necrons/AircraftBuildingBonus'/>"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Erhöht die Produktionsrate von Namenlosen Dämmen."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="<string name='Traits/Necrons/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Necrons/AttackCityBonus" value="<string name='Traits/Necrons/AttackCityBonus'/>"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Erhöht die Genauigkeit von Einheiten gegen feindliche Einheiten in Städten."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="<string name='Traits/Necrons/AttackCityBonusFlavor'/>"/>
	<entry name="Necrons/BlastDamage" value="<string name='Traits/Necrons/BlastDamage'/>"/>
	<entry name="Necrons/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag von Explosiv- und Flammenschablonenwaffen."/>
	<entry name="Necrons/BlastDamageFlavor" value="<string name='Traits/Necrons/BlastDamageFlavor'/>"/>
	<entry name="Necrons/CityDefenseBonus" value="Unbeschreibliche Hindernisse"/>
	<entry name="Necrons/CityDefenseBonusDescription" value="Erhöht die Schadensreduktion bei Einheiten in Städten."/>
	<entry name="Necrons/CityDefenseBonusFlavor" value="Angriffe gegen die Verteidiger der Gruftstadt schlagen nun mysteriöserweise fehl. Energieangriffe verebben, Angriffe mit Gravitonwaffen werden abgelenkt und die zyklopischen Mauern der Grüfte scheinen physische Projektile einfach vom Himmel zu holen."/>
	<entry name="Necrons/CityTier2" value="<string name='Traits/Necrons/CityTier2'/>"/>
	<entry name="Necrons/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier2Flavor" value="<string name='Traits/Necrons/CityTier2Flavor'/>"/>
	<entry name="Necrons/CityTier3" value="<string name='Traits/Necrons/CityTier3'/>"/>
	<entry name="Necrons/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier3Flavor" value="<string name='Traits/Necrons/CityTier3Flavor'/>"/>
	<entry name="Necrons/ConstructionBuildingBonus" value="<string name='Traits/Necrons/ConstructionBuildingBonus'/>"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Erhöht die Produktionsrate von Sklavenmastabas."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="<string name='Traits/Necrons/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor" value="<string name='Actions/Necrons/DimensionalCorridor'/>"/>
	<entry name="Necrons/DimensionalCorridorDescription" value="Ermöglicht es Infanterie, sich zu Städten und Monoliths zu teleportieren."/>
	<entry name="Necrons/DimensionalCorridorFlavor" value="<string name='Actions/Necrons/DimensionalCorridorFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor2" value="Dimensionsstabilität"/>
	<entry name="Necrons/DimensionalCorridor2Description" value="Verringert die Einflusskosten des Dimensionskorridors."/>
	<entry name="Necrons/DimensionalCorridor2Flavor" value="Die Necrons haben sich die zurückgelassenen Technologien der Alten zunutze gemacht, um ihre Rückholtechnologie zu stabilisieren. Die Kosten für Krieger, die zu Toren der Ewigkeit teleportiert werden, werden dadurch stark verringert."/>
	<entry name="Necrons/DimensionalCorridor3" value="Dimensionsmaßnahme"/>
	<entry name="Necrons/DimensionalCorridor3Description" value="Entfernt die Aktions- und Bewegungskosten des Dimensionskorridors."/>
	<entry name="Necrons/DimensionalCorridor3Flavor" value="Eine gerissene Cryptek-Maßnahme erlaubt es den Necrons, ihre Rückholtechnologie zu optimieren, wodurch sie effektiver zurückteleportiert werden können."/>
	<entry name="Necrons/EnergyBuildingBonus" value="<string name='Traits/Necrons/EnergyBuildingBonus'/>"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Erhöht die Energiegewinnung durch Energiekerne."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="<string name='Traits/Necrons/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Necrons/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterie."/>
	<entry name="Necrons/ExtraInfantryArmourFlavor" value="Selbst nach Jahrtausenden des Krieges und Schlafens haben die Crypteks der Necrons nicht ihre Leidenschaft für Innovationen verloren. Durch kleinere Anpassungen an der Necrodermis erhöhen sie die Überlebenschancen der Truppen gegen die Waffen der modernen Zeit."/>
	<entry name="Necrons/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Necrons/ExtraVehicleArmourDescription" value="Erhöht die Panzerung von Fahrzeugen und Canoptek-Einheiten."/>
	<entry name="Necrons/ExtraVehicleArmourFlavor" value="Für Außenstehende gleichen diese Fahrzeuge der Necrons ihren Vorgängermodellen. Ein Cryptek oder vielleicht sogar ein fachkundiger Aeldari erkennt jedoch, dass das Material, aus dem das Fahrzeug gefertigt wurde, widerstandsfähiger ist, ohne Einbußen bei der Dichte und dem Gewicht hinnehmen zu müssen."/>
	<entry name="Necrons/GaussDamage" value="<string name='Traits/GaussDamage'/>"/>
	<entry name="Necrons/GaussDamageDescription" value="Erhöht den Panzerungsdurchschlag von Gausswaffen."/>
	<entry name="Necrons/GaussDamageFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="Necrons/GaussPylon" value="<string name='Units/Necrons/GaussPylon'/>"/>
	<entry name="Necrons/GaussPylonDescription" value="Ermöglicht es Städten, mächtige Gauss-Befestigungen aus dem Boden schießen zu lassen."/>
	<entry name="Necrons/GaussPylonFlavor" value="<string name='Units/Necrons/GaussPylonFlavor'/>"/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="Erhöht bei Canoptek Spyders und angrenzenden nicht-feindlichen Einheiten die Hexenfeuer-Schadensreduktion."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GrowthBonus" value="<string name='Traits/Necrons/GrowthBonus'/>"/>
	<entry name="Necrons/GrowthBonusDescription" value="Erhöht die Wachstumsrate von Städten der Necrons."/>
	<entry name="Necrons/GrowthBonusFlavor" value="<string name='Traits/Necrons/GrowthBonusFlavor'/>"/>
	<entry name="Necrons/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Necrons/HammerOfWrathDescription" value="Ermöglicht es Destroyer Lords, Tomb Blades, Canoptek Spyders, Transzendierten C'tan, Triarch Praetorians und Triarch Stalkers, vernichtendere Angriffe durchzuführen."/>
	<entry name="Necrons/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Necrons/HousingBuildingBonus" value="<string name='Traits/Necrons/HousingBuildingBonus'/>"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Erhöht das Bevölkerungslimit von Unterkünften."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="<string name='Traits/Necrons/HousingBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfantryBuildingBonus" value="<string name='Traits/Necrons/InfantryBuildingBonus'/>"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Erhöht die Produktionsrate von Beschwörungskernen."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="<string name='Traits/Necrons/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfluenceBuildingBonus" value="<string name='Traits/Necrons/InfluenceBuildingBonus'/>"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Erhöht die Generierung von Einfluss durch Stelen."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="<string name='Traits/Necrons/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Necrons/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="Necrons/LivingMetal2" value="Unsterbliche Formen"/>
	<entry name="Necrons/LivingMetal2Description" value="Erhöht die Heilung von lebendem Metall."/>
	<entry name="Necrons/LivingMetal2Flavor" value="Aus unerfindlichen Gründen erinnern sich die Necron-Maschinen an ihren alten Ruhm und versuchen unentwegt, ihn wiederzuerlangen. Ihr lebendes Metall nimmt nach einem Treffer schnell wieder seine ursprüngliche Form an und macht so den Schaden wett."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="<string name='Traits/Necrons/LoyaltyBuildingBonus'/>"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Erhöht die Generierung von Loyalität durch Groteske Schreine."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="<string name='Traits/Necrons/LoyaltyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/MeleeDamage" value="<string name='Traits/Necrons/MeleeDamage'/>"/>
	<entry name="Necrons/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="Necrons/MeleeDamageFlavor" value="<string name='Traits/Necrons/MeleeDamageFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="<string name='Traits/Necrons/Nebuloscope'/>"/>
	<entry name="Necrons/NebuloscopeDescription" value="Tomb Blades ignorieren jegliche Fernkampf-Schadensreduktion."/>
	<entry name="Necrons/NebuloscopeFlavor" value="<string name='Traits/Necrons/NebuloscopeFlavor'/>"/>
	<entry name="Necrons/NecrodermisRepair2" value="Beschleunigte Erneuerung"/>
	<entry name="Necrons/NecrodermisRepair2Description" value="Erhöht die Wiederherstellung der Trefferpunkte durch „Necrodermis-Reparatur“."/>
	<entry name="Necrons/NecrodermisRepair2Flavor" value="Die Crypteks haben die nanomechanische Struktur des lebenden Metalls der Necron-Körper ein weiteres Mal verbessert, wodurch sich die Necrons von fast jedem Schaden erholen können."/>
	<entry name="Necrons/NecrodermisRepair3" value="Zähe Necrodermis"/>
	<entry name="Necrons/NecrodermisRepair3Description" value="Entfernt die Abklingzeit für „Necrodermis-Reparatur“."/>
	<entry name="Necrons/NecrodermisRepair3Flavor" value="Das lebende Metall, aus dem die Körper und Fahrzeuge der Necrons bestehen, verformt sich permanent und erneuert sich dadurch quasi selbst."/>
	<entry name="Necrons/OreBuildingBonus" value="<string name='Traits/Necrons/OreBuildingBonus'/>"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Erhöht die Erzgewinnung durch Alkhemische Erzgruben."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="<string name='Traits/Necrons/OreBuildingBonusFlavor'/>"/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Gewährt Annihilation Barges, Geisterbarken, Doomsday Arks und Triarch Stalkers Unverwundbar-Schadensreduktion, die jedoch zu Beginn der nächsten Runde eine Abklingzeit nach sich zieht, falls die Einheit Schaden erleidet."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/RapidRiseBonus" value="Der Befehl des Lords"/>
	<entry name="Necrons/RapidRiseBonusDescription" value="Verringert die Kosten für „Rapider Anstieg“."/>
	<entry name="Necrons/RapidRiseBonusFlavor" value="Der Lord stärkt seine Befehlsprotokolle, damit seine Anweisungen schneller und ohne viel Nachdenken ausgeführt werden – und ohne Murren."/>
	<entry name="Necrons/ReanimationProtocols2" value="Effiziente Reanimationsprotokolle"/>
	<entry name="Necrons/ReanimationProtocols2Description" value="Erhöht die Heilungsrate von „Reanimationsprotokolle“."/>
	<entry name="Necrons/ReanimationProtocols2Flavor" value="Mit diesen von Crypteks ersonnenen Reparatur- und Erneuerungssystemen können die Necrons mit noch größerer Wahrscheinlichkeit eigentlich tödlichen Schaden wegstecken."/>
	<entry name="Necrons/ResearchBuildingBonus" value="<string name='Traits/Necrons/ResearchBuildingBonus'/>"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Erhöht die Generierung von Forschungspunkten durch Verbotene Archive."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="<string name='Traits/Necrons/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ScarabHive" value="<string name='Actions/Necrons/ScarabHive'/>"/>
	<entry name="Necrons/ScarabHiveDescription" value="Ermöglicht es Canoptek Spyders, Canoptek Scarabs zu bauen."/>
	<entry name="Necrons/ScarabHiveFlavor" value="<string name='Actions/Necrons/ScarabHiveFlavor'/>"/>
	<entry name="Necrons/SeismicAssault" value="Seismischer Angriff"/>
	<entry name="Necrons/SeismicAssaultDescription" value="Ermöglicht es Transzendierten C'tan und Tesserakt-Verliesen, einen vernichtenden Angriff durchzuführen."/>
	<entry name="Necrons/SeismicAssaultFlavor" value="<string name='Weapons/SeismicAssaultTranscendentFlavor'/>"/>
	<entry name="Necrons/ShieldVane" value="<string name='Traits/Necrons/ShieldVane'/>"/>
	<entry name="Necrons/ShieldVaneDescription" value="Erhöht die Panzerung von Tomb Blades."/>
	<entry name="Necrons/ShieldVaneFlavor" value="<string name='Traits/Necrons/ShieldVaneFlavor'/>"/>
	<entry name="Necrons/TeslaDamage" value="<string name='Traits/TeslaDamage'/>"/>
	<entry name="Necrons/TeslaDamageDescription" value="Erhöht den Panzerungsdurchschlag von Tesla-Waffen."/>
	<entry name="Necrons/TeslaDamageFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="Necrons/TheBoundCoalescent" value="<string name='Actions/Necrons/TheBoundCoalescent'/>"/>
	<entry name="Necrons/TheBoundCoalescentDescription" value="Ermöglicht es Transzendierten C'tan, mit Obelisks zu verschmelzen, um Tesserakt-Verliese zu erschaffen."/>
	<entry name="Necrons/TheBoundCoalescentFlavor" value="<string name='Actions/Necrons/TheBoundCoalescentFlavor'/>"/>
	<entry name="Necrons/VehiclesBuildingBonus" value="<string name='Traits/Necrons/VehiclesBuildingBonus'/>"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Erhöht die Produktionsrate von Hypostylos-Tempeln."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="<string name='Traits/Necrons/VehiclesBuildingBonusFlavor'/>"/>
	<entry name="Orks/AmmoRunt" value="<string name='Actions/AmmoRunt'/>"/>
	<entry name="Orks/AmmoRuntDescription" value="Ermöglicht es Big Mekz, Flash Gitz und Mek Gunz, ihre Fernkampfgenauigkeit zu erhöhen."/>
	<entry name="Orks/AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Orks/BattlewagonBigShootas" value="Fette Wummen für Battlewagons"/>
	<entry name="Orks/BattlewagonBigShootasDescription" value="Battlewagons erhalten Fette Wummen."/>
	<entry name="Orks/BattlewagonBigShootasFlavor" value="<string name='Weapons/BigShootaFlavor'/>"/>
	<entry name="Orks/BattlewagonRokkitLaunchas" value="Bazzukkas für Battlewagons"/>
	<entry name="Orks/BattlewagonRokkitLaunchasDescription" value="Battlewagons erhalten Bazzukkas."/>
	<entry name="Orks/BattlewagonRokkitLaunchasFlavor" value="<string name='Weapons/RokkitLaunchaFlavor'/>"/>
	<entry name="Orks/Bigbomm" value="<string name='Weapons/Bigbomm'/>"/>
	<entry name="Orks/BigbommDescription" value="Ermöglicht es Deffkoptas, Anti-Personen-Bomben abzuwerfen."/>
	<entry name="Orks/BigbommFlavor" value="<string name='Weapons/BigbommFlavor'/>"/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/Orks/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag von Granaten, Raketen und Explosivwaffen."/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/Orks/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="<string name='Traits/Orks/BoltDamage'/>"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="<string name='Traits/Orks/BoltDamageFlavor'/>"/>
	<entry name="Orks/BonusBeastsProduction" value="<string name='Traits/Orks/BonusBeastsProduction'/>"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="<string name='Traits/Orks/BonusBeastsProductionDescription'/>"/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="<string name='Traits/Orks/BonusBeastsProductionFlavor'/>"/>
	<entry name="Orks/BonusColonizersProduction" value="<string name='Traits/Orks/BonusColonizersProduction'/>"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="<string name='Traits/Orks/BonusColonizersProductionDescription'/>"/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="<string name='Traits/Orks/BonusColonizersProductionFlavor'/>"/>
	<entry name="Orks/BonusInfantryProduction" value="<string name='Traits/Orks/BonusInfantryProduction'/>"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="<string name='Traits/Orks/BonusInfantryProductionDescription'/>"/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="<string name='Traits/Orks/BonusInfantryProductionFlavor'/>"/>
	<entry name="Orks/BonusVehiclesProduction" value="<string name='Traits/Orks/BonusVehiclesProduction'/>"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="<string name='Traits/Orks/BonusVehiclesProductionDescription'/>"/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="<string name='Traits/Orks/BonusVehiclesProductionFlavor'/>"/>
	<entry name="Orks/Bosspole" value="Trophä'nstangä"/>
	<entry name="Orks/BosspoleDescription" value="Verringert bei Big Mekz, Boyz, Meganobz, Panzaknakkaz, Waaaghbikes und Waaaghbossen weiter den Moralverlust durch „Grüner Mob“."/>
	<entry name="Orks/BosspoleFlavor" value="Ork-Nobz tragen oft eine Trophä'nstangä, die zeigt, dass man sich besser nicht mit ihnen anlegen sollte. Außerdem finden es viele Nobz praktisch, ein Werkzeug dabeizuhaben, mit dem man Köpfe einschlagen kann, um in der Hitze des Gefechts wieder ein bisschen Ordnung herzustellen."/>
	<entry name="Orks/CityEnergy" value="<string name='Traits/Orks/CityEnergy'/>"/>
	<entry name="Orks/CityEnergyDescription" value="Erhöht die Energiegewinnung durch Städte der Orks."/>
	<entry name="Orks/CityEnergyFlavor" value="<string name='Traits/Orks/CityEnergyFlavor'/>"/>
	<entry name="Orks/CityGrowth" value="<string name='Traits/Orks/CityGrowth'/>"/>
	<entry name="Orks/CityGrowthDescription" value="<string name='Traits/Orks/CityGrowthDescription'/>"/>
	<entry name="Orks/CityGrowthFlavor" value="<string name='Traits/Orks/CityGrowthFlavor'/>"/>
	<entry name="Orks/CityInfluence" value="<string name='Traits/Orks/CityInfluence'/>"/>
	<entry name="Orks/CityInfluenceDescription" value="Erhöht die Generierung von Einfluss in den Städten der Orks."/>
	<entry name="Orks/CityInfluenceFlavor" value="<string name='Traits/Orks/CityInfluenceFlavor'/>"/>
	<entry name="Orks/CityLoyalty" value="<string name='Traits/Orks/CityLoyalty'/>"/>
	<entry name="Orks/CityLoyaltyDescription" value="Erhöht die Generierung von Loyalität in den Städten der Orks."/>
	<entry name="Orks/CityLoyaltyFlavor" value="<string name='Traits/Orks/CityLoyaltyFlavor'/>"/>
	<entry name="Orks/CityPopulationLimit" value="<string name='Traits/Orks/CityPopulationLimit'/>"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Erhöht das Bevölkerungslimit von Städten der Orks."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="<string name='Traits/Orks/CityPopulationLimitFlavor'/>"/>
	<entry name="Orks/CityResearch" value="<string name='Traits/Orks/CityResearch'/>"/>
	<entry name="Orks/CityResearchDescription" value="Erhöht die Generierung von Forschungspunkten in den Städten der Orks."/>
	<entry name="Orks/CityResearchFlavor" value="<string name='Traits/Orks/CityResearchFlavor'/>"/>
	<entry name="Orks/CityTier2" value="<string name='Traits/Orks/CityTier2'/>"/>
	<entry name="Orks/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier2Flavor" value="<string name='Traits/Orks/CityTier2Flavor'/>"/>
	<entry name="Orks/CityTier3" value="<string name='Traits/Orks/CityTier3'/>"/>
	<entry name="Orks/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier3Flavor" value="<string name='Traits/Orks/CityTier3Flavor'/>"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeath" value="Dauerhafter Verfall"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathDescription" value="Wenn Einheiten sterben, wachsen dauerhaft orkoide Pilze."/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathFlavor" value="<string name='Traits/Orks/CreateOrkoidFungusOnDeathFlavor'/>"/>
	<entry name="Orks/DakkajetSupaShoota" value="Supawumme für Dakkajets"/>
	<entry name="Orks/DakkajetSupaShootaDescription" value="Dakkajets erhalten eine Supawumme."/>
	<entry name="Orks/DakkajetSupaShootaFlavor" value="<string name='Weapons/TwinLinkedSupaShootaFlavor'/>"/>
	<entry name="Orks/EavyArmour" value="<string name='Traits/EavyArmour'/>"/>
	<entry name="Orks/EavyArmourDescription" value="Erhöht die Panzerung von Boyz und Waaaghbossen."/>
	<entry name="Orks/EavyArmourFlavor" value="<string name='Traits/EavyArmourFlavor'/>"/>
	<entry name="Orks/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Orks/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterie."/>
	<entry name="Orks/ExtraInfantryArmourFlavor" value="Jeder anderen Rasse muss die Panzerung der Orks lächerlich erscheinen, da sie aus großen Klumpen dicker Metallplatten besteht, die mit Kabeln, Nägeln und widersinnigen Vorrichtungen aneinander befestigt wurden, nachdem ihnen von primitiven Meks und Grotz ein goldener Anstrich verpasst worden war. Wenn die Orks jedoch an den Nutzen ihrer Panzerung glauben, kann sie wahre Wunder wirken."/>
	<entry name="Orks/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Orks/ExtraVehicleArmourDescription" value="Erhöht die Panzerung von Fahrzeugen."/>
	<entry name="Orks/ExtraVehicleArmourFlavor" value="Wenn ein Mek den Ärger eines Waaaghbosses zu spüren bekommt, weil seine Warbuggys und Battlewagons dauernd außer Gefecht gesetzt werden, befiehlt er seinen Grotz augenblicklich, neuen Schrott herbeizuschaffen und zusätzliche Panzerung überall dort hinzunageln, wo sie hinpasst. Allerdings weiß er, dass die Heizaz sie sowieso bald wieder runterreißen werden…"/>
	<entry name="Orks/Flyboss" value="<string name='Traits/Orks/Flyboss'/>"/>
	<entry name="Orks/FlybossDescription" value="Erhöht die Genauigkeit von Dakkajets im Fernkampf gegen Flieger, Jetbikes und Antigraveinheiten."/>
	<entry name="Orks/FlybossFlavor" value="<string name='Traits/Orks/FlybossFlavor'/>"/>
	<entry name="Orks/GrabbinKlaw" value="<string name='Actions/GrabbinKlaw'/>"/>
	<entry name="Orks/GrabbinKlawDescription" value="Ermöglicht es Battlewagons, feindliche Bodenfahrzeuge bewegungsunfähig zu machen."/>
	<entry name="Orks/GrabbinKlawFlavor" value="<string name='Actions/GrabbinKlawFlavor'/>"/>
	<entry name="Orks/GrotRiggers" value="Grothälfaz"/>
	<entry name="Orks/GrotRiggersDescription" value="Ermöglicht es Einheiten des Typs Warbuggy, Killa Kan, Battlewagon, Gargbot, Gorkanaut und Mordsprenga, passiv Trefferpunkte zu regenerieren."/>
	<entry name="Orks/GrotRiggersFlavor" value="<string name='Traits/GrotRiggersFlavor'/>"/>
	<entry name="Orks/HealingRate" value="Grünzeug"/>
	<entry name="Orks/HealingRateDescription" value="Erhöht die Heilungsrate der Einheiten."/>
	<entry name="Orks/HealingRateFlavor" value="Basierend auf einer Zusammenarbeit zwischen Bad Doks und Treibaz hat Ihr Warlord seinen Boyz eine neue, „orkigere“ Diät verordnet, damit sie größer, stärker und schneller werden. Es spielt keine Rolle, ob die kalten Ölsquigs zum Frühstück und Fratzenfressasquigs zum Mittagessen wirklich eine Auswirkung auf den Organismus haben. Die Boyz glauben es und deswegen funktioniert es auch."/>
	<entry name="Orks/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Orks/HammerOfWrathDescription" value="Ermöglicht es Gargbots, Deffkoptas, Gigantischen Squiggofanten, Gorkanauts, Killa Kanz und Waaaghbikes, vernichtendere Angriffe durchzuführen."/>
	<entry name="Orks/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="<string name='Traits/Orks/MeleeDamage'/>"/>
	<entry name="Orks/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="Orks/MeleeDamageFlavor" value="<string name='Traits/Orks/MeleeDamageFlavor'/>"/>
	<entry name="Orks/MightMakesRight2" value="Fürchtä die Orks!"/>
	<entry name="Orks/MightMakesRight2Description" value="Erhöht den Einfluss, den Einheiten für das Verursachen von Schaden erhalten."/>
	<entry name="Orks/MightMakesRight2Flavor" value="Wenn ein Waaagh mächtig genug ist, gerät förmlich eine Lawine ins Rollen und jeder weitere Treffer erweitert das ausströmende psionische Feld in einer äußerst gewaltsamen orkischen Kettenreaktion."/>
	<entry name="Orks/OrkoidFungusBonusHealingRate" value="<string name='Traits/OrkoidFungusBonusHealingRate'/>"/>
	<entry name="Orks/OrkoidFungusBonusHealingRateDescription" value="Erhöht die Heilung durch orkoide Pilze."/>
	<entry name="Orks/OrkoidFungusBonusHealingRateFlavor" value="<string name='Traits/OrkoidFungusBonusHealingRateFlavor'/>"/>
	<entry name="Orks/OrkoidFungusFood" value="<string name='Traits/OrkoidFungusFood'/>"/>
	<entry name="Orks/OrkoidFungusFoodDescription" value="Erhöht die Nahrungsproduktion auf Hexfeldern mit orkoiden Pilzen."/>
	<entry name="Orks/OrkoidFungusFoodFlavor" value="<string name='Traits/OrkoidFungusFoodFlavor'/>"/>
	<entry name="Orks/RedPaintJob" value="Rotä Farbä"/>
	<entry name="Orks/RedPaintJobDescription" value="Erhöht den Schaden von Warbuggys, Megakett'n-Schrottjets, Battlewagons, Dakkajets und Burna-bommers."/>
	<entry name="Orks/RedPaintJobFlavor" value="<string name='Traits/RedPaintJobFlavor'/>"/>
	<entry name="Orks/Scavenger2" value="Grot-Plünderer"/>
	<entry name="Orks/Scavenger2Description" value="Erhöht die Erzmenge, die erbeutet wird, wenn feindliche Einheiten getötet werden."/>
	<entry name="Orks/Scavenger2Flavor" value="Gut organisierte Treibaz richten ihre Grotz dazu ab, Schrott für die Orks zu sammeln und zu sortieren, wobei sie darauf achten, einen großen Bogen um die gefährlichsten Patronenhülsen und anderen Munitionsüberreste zu machen."/>
	<entry name="Orks/SkorchaMissile" value="<string name='Weapons/SkorchaMissile'/>"/>
	<entry name="Orks/SkorchaMissileDescription" value="Ermöglicht es Burna-bommers, Anti-Personen-Raketen einzusetzen, die die feindliche Deckung ignorieren."/>
	<entry name="Orks/SkorchaMissileFlavor" value="<string name='Weapons/SkorchaMissileFlavor'/>"/>
	<entry name="Orks/Stikkbomb" value="<string name='Weapons/Stikkbomb'/>"/>
	<entry name="Orks/StikkbombDescription" value="Ermöglicht es Infanterie, Anti-Personen-Granaten einzusetzen."/>
	<entry name="Orks/StikkbombFlavor" value="<string name='Weapons/StikkbombFlavor'/>"/>
	<entry name="Orks/TankbustaBomb" value="<string name='Weapons/TankbustaBomb'/>"/>
	<entry name="Orks/TankbustaBombDescription" value="Ermöglicht es Panzaknakkaz, panzerbrechende Bomben einzusetzen."/>
	<entry name="Orks/TankbustaBombFlavor" value="<string name='Weapons/TankbustaBombFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolter" value="Zusätzlicher Schwerer Bolter"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterDescription" value="Immolatoren, Exorcists und Castigatoren erhalten einen zusätzlichen Schweren Bolter."/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalSacredRite" value="Zweifacher Glaube"/>
	<entry name="SistersOfBattle/AdditionalSacredRiteDescription" value="Zwei Heilige Riten können gleichzeitig aktiv sein."/>
	<entry name="SistersOfBattle/AdditionalSacredRiteFlavor" value="„Früher wurden unsere rituellen Choräle – ob ein- oder mehrstimmig – unabhängig voneinander gesungen. Heute lassen wir unsere Hymnen gemeinsam erklingen, um die Wirkung der Klänge zu verstärken und völlig neue Harmoniegebilde zu erschaffen. So ist es uns möglich, den Gottimperator auf noch vielfältigere Weise zu preisen.“<br/> – Unbekannter Memorator, Evangelium des Netzes der Tausend Tore"/>
	<entry name="SistersOfBattle/AircraftsMissiles" value="Flugzeugraketen"/>
	<entry name="SistersOfBattle/AircraftsMissilesDescription" value="Blitzjäger erhalten Himmelsschlag-Raketen und Avenger-Jagdbomber Donnerkeil-Raketen."/>
	<entry name="SistersOfBattle/AircraftsMissilesFlavor" value="Mit diesen Raketen können die Flugzeuge verschiedenste Ziele wirkungsvoll bekämpfen. Donnerkeil-Raketen eignen sich für den Einsatz gegen gepanzerte Fahrzeuge, Himmelsschlag-Raketen für den Einsatz gegen Lufteinheiten."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagus'/>"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Erhöht die Panzerung von Marterern."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagusFlavor'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonus'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von Sturmwaffen."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/AvengeTheMartyrs" value="Rache für die Märtyrerinnen"/>
	<entry name="SistersOfBattle/AvengeTheMartyrsDescription" value="Erhöht die Verringerung des Moralverlusts durch „Eifer der Vergeltung“."/>
	<entry name="SistersOfBattle/AvengeTheMartyrsFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/ChaffLauncher" value="Täuschkörperwerfer"/>
	<entry name="SistersOfBattle/ChaffLauncherDescription" value="Ermöglicht es Blitzjägern und Avenger-Jagdbombern, Täuschkörper abzuwerfen, wodurch die Fernkampf-Schadensreduktion erhöht wird."/>
	<entry name="SistersOfBattle/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfane" value="<string name='Actions/SistersOfBattle/ChaseTheProfane'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneDescription" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneDescription'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneFlavor" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="<string name='Traits/SistersOfBattle/CityGrowth'/>"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Erhöht die Wachstumsrate von Städten."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="<string name='Traits/SistersOfBattle/CityGrowthFlavor'/>"/>
	<entry name="SistersOfBattle/CityTier2" value="<string name='Traits/SistersOfBattle/CityTier2'/>"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Erhöht für die Hauptstadt den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="<string name='Traits/SistersOfBattle/CityTier2Flavor'/>"/>
	<entry name="SistersOfBattle/CityTier3" value="<string name='Traits/SistersOfBattle/CityTier3'/>"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Erhöht für die Hauptstadt den Radius zur Einverleibung von Hexfeldern."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="<string name='Traits/SistersOfBattle/CityTier3Flavor'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaith" value="<string name='Actions/SistersOfBattle/ConvictionOfFaith'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithDescription" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithFlavor" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SistersOfBattle/DozerBladeDescription" value="Verringert bei Immolatoren, Exorcists und Castigatoren den Bewegungsmalus in Wäldern und imperialen Ruinen."/>
	<entry name="SistersOfBattle/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="<string name='Actions/SistersOfBattle/EternalCrusade'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="<string name='Actions/SistersOfBattle/EternalCrusadeDescription'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="<string name='Actions/SistersOfBattle/EternalCrusadeFlavor'/>"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Meisterhafte Kampfpiloten"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Erhöht die Genauigkeit von Blitzjägern und Avenger-Jagdbombern."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="<string name='Traits/SistersOfBattle/ExpertFightersFlavor'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterie."/>
	<entry name="SistersOfBattle/ExtraInfantryArmourFlavor" value="Die aus dicken Ceramit-Platten gefertigten Servorüstungen der Adepta Sororitas basieren auf denselben archaischen Systemen wie die des Adeptus Astartes. Sie bieten ebenso viel gepanzerten Schutz, allerdings musste auf die fortschrittlicheren Unterstützungssysteme und Eigenschaften zur Steigerung der Stärke verzichtet werden, da die Sororitas nicht über die Fähigkeit der Space Marines verfügen, sich direkt mit ihren eigenen Rüstungen zu verbinden."/>
	<entry name="SistersOfBattle/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SistersOfBattle/ExtraVehicleArmourDescription" value="Erhöht die Panzerung von Fahrzeugen."/>
	<entry name="SistersOfBattle/ExtraVehicleArmourFlavor" value="Die Fahrzeuge der Adepta Sororitas sind ein Zeugnis für den inbrünstigen Glauben der Ordensschwestern, daher ist es wichtig, die aufmontierten, freiliegenden Artefakte instand zu halten und zu beschützen. Auf diese Weise können die Fahrzeuge ihrerseits die vorstoßenden Kriegerinnen der Adepta Sororitas besser beschützen."/>
	<entry name="SistersOfBattle/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SistersOfBattle/FragGrenadeDescription" value="Ermöglicht es Sororitas, Principalis, Celestia Sacresantis, Dialogus, Dominator-Schwestern, Hospitalis, Imaginifer, Paragon-Kriegsanzügen, Retributoren, der Heiligen Celestine, Repentia und Zephyrim, Anti-Personen-Granaten einzusetzen."/>
	<entry name="SistersOfBattle/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/HammerOfWrath" value="<string name='Traits/HammerOfWrath'/>"/>
	<entry name="SistersOfBattle/HammerOfWrathDescription" value="Ermöglicht es Cerastus-Lanzenrittern, Marterern, Paragon-Kriegsanzügen, der Heiligen Celestine und Zephyrim, vernichtendere Angriffe durchzuführen."/>
	<entry name="SistersOfBattle/HammerOfWrathFlavor" value="<string name='Traits/HammerOfWrathFlavor'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonus'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag schwerer Waffen."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SistersOfBattle/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SistersOfBattle/KrakGrenadeDescription" value="Ermöglicht es Sororitas, Principalis, Dialogus, Dominator-Schwestern, Hospitalis, Imaginifer, Retributoren, Repentia und Zephyrim, Panzerabwehrgranaten einzusetzen."/>
	<entry name="SistersOfBattle/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/LaudHailer" value="<string name='Traits/SistersOfBattle/LaudHailer'/>"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="Gewährt Castigatoren, Exorcists und Immolatoren eine Aura, die es angrenzenden aufgewühlten Einheiten ermöglicht, Glaubensakte zu vollziehen."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Traits/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Traits/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Verleiht nicht-feindlichen Infanterieeinheiten, die an eine Hospitalis-Einheit angrenzen, die Schadensreduktion „Verletzungen ignorieren“."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Traits/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonus'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SistersOfBattle/MeltaBombDescription" value="Ermöglicht es Sororitas, Principalis, Dialogus, Dominator-Schwestern, Retributoren, Repentia und Zephyrim, eine Melterbombe einzusetzen, die äußerst effektiv gegen schwere Fahrzeuge und Befestigungsanlagen ist."/>
	<entry name="SistersOfBattle/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SistersOfBattle/MinistorumIndoctrination" value="Ministorum-Indoktrinierung"/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationDescription" value="Blitzjäger, Avenger-Jagdbomber und Cerastus-Lanzenritter erhalten „Märtyrergeist“."/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationFlavor" value="„Da unsere Verbündeten – die Imperiale Kriegsflotte und eine Handvoll überlebender Cerastus-Lanzenritter – lange Zeit unseren Riten und Gebeten lauschten, begannen auch sie damit, unseren Riten beizuwohnen und unseren Glauben zu verinnerlichen. Unser Glaube, so scheint es, ist ansteckend.“<br/> – Unbekannter Memorator, Evangelium des Netzes der Tausend Tore"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisation'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationDescription'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationFlavor'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="<string name='Actions/SistersOfBattle/PurifyingRecitations'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsDescription'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsFlavor'/>"/>
	<entry name="SistersOfBattle/RagingFervour" value="<string name='Actions/SistersOfBattle/RagingFervour'/>"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="<string name='Actions/SistersOfBattle/RagingFervourDescription'/>"/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="<string name='Actions/SistersOfBattle/RagingFervourFlavor'/>"/>
	<entry name="SistersOfBattle/RitualizedCeremonies" value="Rituelle Zeremonien"/>
	<entry name="SistersOfBattle/RitualizedCeremoniesDescription" value="Verringert die Kosten Heiliger Riten."/>
	<entry name="SistersOfBattle/RitualizedCeremoniesFlavor" value="„Zunächst beteten wir unterwegs in Ruinen oder Höhlen, wann immer wir Ruhe fanden. Als aus dem Konflikt ein endloser, zermürbender Krieg geworden war, entwickelte sich mit der Zeit auch bei unseren Riten und Gebeten eine gewisse Routine.“<br/> – Unbekannter Memorator, Evangelium des Netzes der Tausend Tore"/>
	<entry name="SistersOfBattle/SacralVigor" value="<string name='Actions/SistersOfBattle/SacralVigor'/>"/>
	<entry name="SistersOfBattle/SacralVigorDescription" value="<string name='Actions/SistersOfBattle/SacralVigorDescription'/>"/>
	<entry name="SistersOfBattle/SacralVigorFlavor" value="<string name='Actions/SistersOfBattle/SacralVigorFlavor'/>"/>
	<entry name="SistersOfBattle/SanctifiedWorld" value="Geheiligte Welt"/>
	<entry name="SistersOfBattle/SanctifiedWorldDescription" value="Erhöht den Loyalitätsbonus durch „Konvent des Glaubens“."/>
	<entry name="SistersOfBattle/SanctifiedWorldFlavor" value="Die meisten Orden tragen das Licht des Gottimperators hinaus in Welten, die weit von ihren größten Heiligtümern entfernt sind, und gründen dabei in den entlegensten Winkeln Missionen und Zweigkirchen, um den Einflussbereich ihres Ordens und der Ekklesiarchie zu vergrößern."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="<string name='Traits/SistersOfBattle/SimulacrumImperialis'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisDescription'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisFlavor'/>"/>
	<entry name="SistersOfBattle/SisterSuperior" value="<string name='Traits/SistersOfBattle/SisterSuperior'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="<string name='Traits/SistersOfBattle/SisterSuperiorDescription'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="<string name='Traits/SistersOfBattle/SisterSuperiorFlavor'/>"/>
	<entry name="SistersOfBattle/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SistersOfBattle/UniversalCatechism" value="Universeller Katechismus"/>
	<entry name="SistersOfBattle/UniversalCatechismDescription" value="Blitzjäger, Avenger-Jagdbomber und Cerastus-Lanzenritter erhalten „Schild des Glaubens“."/>
	<entry name="SistersOfBattle/UniversalCatechismFlavor" value="Imperiale Truppen, die für die Glaubenskriege angefordert werden, beten vor der Schlacht nicht selten Seite an Seite mit den Schwestern der Adepta Sororitas und lassen sich von deren Überzeugungen leiten."/>
	<entry name="SistersOfBattle/VengefulSpirit" value="<string name='Actions/SistersOfBattle/VengefulSpirit'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="<string name='Actions/SistersOfBattle/VengefulSpiritDescription'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="<string name='Actions/SistersOfBattle/VengefulSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrder" value="Eid des Militärischen Ordens"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderDescription" value="Einheiten behalten ihren Schild des Glaubens, wenn sie gebrochen sind."/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderFlavor" value="<string name='Traits/SistersOfBattle/ShieldOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="<string name='Actions/SistersOfBattle/WarmachinesWrath'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="<string name='Actions/SistersOfBattle/WarmachinesWrathDescription'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="<string name='Actions/SistersOfBattle/WarmachinesWrathFlavor'/>"/>
	<entry name="SpaceMarines/AssaultDoctrine" value="<string name='Traits/AssaultDoctrine'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineDescription" value="<string name='Actions/AssaultDoctrineDescription'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineFlavor" value="<string name='Traits/AssaultDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/SpaceMarines/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag von Granaten, Raketen und Explosivwaffen."/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/SpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="SpaceMarines/BolsterDefencesDescription" value="Ermöglicht es Thunderfire Cannons, die Fernkampf-Schadensreduktion des Zielhexfelds zu erhöhen."/>
	<entry name="SpaceMarines/BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/SpaceMarines/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/SpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDrill" value="<string name='Traits/BolterDrill'/>"/>
	<entry name="SpaceMarines/BolterDrillDescription" value="<string name='Actions/BolterDrillDescription'/>"/>
	<entry name="SpaceMarines/BolterDrillFlavor" value="<string name='Traits/BolterDrillFlavor'/>"/>
	<entry name="SpaceMarines/CeramitePlating" value="<string name='Traits/CeramitePlating'/>"/>
	<entry name="SpaceMarines/CeramitePlatingDescription" value="Erhöht die Panzerung von Stormraven Gunships und Stormtalon Gunships."/>
	<entry name="SpaceMarines/CeramitePlatingFlavor" value="<string name='Traits/CeramitePlatingFlavor'/>"/>
	<entry name="SpaceMarines/ChapterUnity" value="<string name='Traits/ChapterUnity'/>"/>
	<entry name="SpaceMarines/ChapterUnityDescription" value="Erhöht die Generierung von Loyalität durch die Große Halle."/>
	<entry name="SpaceMarines/ChapterUnityFlavor" value="<string name='Traits/ChapterUnityFlavor'/>"/>
	<entry name="SpaceMarines/CityTier2" value="<string name='Traits/SpaceMarines/CityTier2'/>"/>
	<entry name="SpaceMarines/CityTier2Description" value="Erhöht den Radius, in dem sich die Stadt Hexfelder einverleiben kann."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="<string name='Traits/SpaceMarines/CityTier2Flavor'/>"/>
	<entry name="SpaceMarines/CityTier3" value="<string name='Traits/SpaceMarines/CityTier3'/>"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="<string name='Traits/SpaceMarines/CityTier3Flavor'/>"/>
	<entry name="SpaceMarines/CityTier4" value="<string name='Traits/SpaceMarines/CityTier4'/>"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="<string name='Traits/SpaceMarines/CityTier4Flavor'/>"/>
	<entry name="SpaceMarines/ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="SpaceMarines/ClusterMinesDescription" value="Ermöglicht es Scout Bikers, Splitterminen einzusetzen."/>
	<entry name="SpaceMarines/ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="SpaceMarines/CombatShield" value="<string name='Traits/CombatShield'/>"/>
	<entry name="SpaceMarines/CombatShieldDescription" value="Erhöht die Schadensreduktion bei Assault Squads der Space Marines."/>
	<entry name="SpaceMarines/CombatShieldFlavor" value="<string name='Traits/CombatShieldFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrine" value="<string name='Traits/DevastatorDoctrine'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineDescription" value="<string name='Actions/DevastatorDoctrineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineFlavor" value="<string name='Traits/DevastatorDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SpaceMarines/DozerBladeDescription" value="Verringert den Bewegungsmalus von Hunters, Predators, Razorbacks und Whirlwinds in Wäldern und imperialen Ruinen."/>
	<entry name="SpaceMarines/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourFlavor" value="Reinheitssiegel mögen einen Adeptus Astartes zwar länger auf dem Schlachtfeld halten, aber der Glaube kann ihm dennoch nicht alle Geschosse vom Leib halten. Wenn ein Infanterist der Space Marines jedoch eine neuere Servorüstung anlegt, erhöht er seine Überlebenschancen beträchtlich."/>
	<entry name="SpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraVehicleArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourFlavor" value="Wenn die richtigen Salben aufgetragen und Segen gesprochen wurden, kann ein Techmarine durchaus auch geringe Veränderungen an seiner Munition vornehmen, um seine Überlebenschancen zu erhöhen – sofern er damit nicht gegen den Codex Astartes verstößt."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Festungsschild"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Festungen der Erlösung erhalten Unverwundbar-Schadensreduktion."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="<string name='Traits/SpaceMarines/FortressOfRedemptionDamageReductionFlavor'/>"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSilo" value="Raketensilo für Festung der Erlösung"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloDescription" value="Festungen der Erlösung erhalten ein Sprengsturm-Raketensilo."/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloFlavor" value="<string name='Weapons/KrakstormMissileSiloFlavor'/>"/>
	<entry name="SpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SpaceMarines/FragGrenadeDescription" value="Ermöglicht es Apothecaries, Assault Squads, Captains, Devastortrupps, Librarians, Scouts, Scout Bikers, Tactical Squads und Thunderfire Cannons, Anti-Personen-Granaten einzusetzen."/>
	<entry name="SpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="SpaceMarines/HammerOfWrathDescription" value="Ermöglicht es Assault Squads, Dreadnoughts und Scout Bikers, zerstörerischere Angriffe durchzuführen."/>
	<entry name="SpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="SpaceMarines/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SpaceMarines/HurricaneBolter" value="Hurricane-Bolter"/>
	<entry name="SpaceMarines/HurricaneBolterDescription" value="Stormraven Gunships erhalten einen Hurricane-Bolter."/>
	<entry name="SpaceMarines/HurricaneBolterFlavor" value="Hurricane-Bolter wurden erstmals vom Black Templars Orden eingesetzt. Diese Waffe bündelt die unwahrscheinlich hohe Feuerkraft mehrerer synchronisierter Bolter und deckt den Feind mit einem Geschosshagel ein."/>
	<entry name="SpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SpaceMarines/KrakGrenadeDescription" value="Ermöglicht es Apothecaries, Assault Squads, Captains, Devastortrupps, Librarians, Scouts, Scout Bikers, Tactical Squads und Thunderfire Cannons, Panzerabwehrgranaten einzusetzen."/>
	<entry name="SpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMelta" value="<string name='Weapons/MultiMelta'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaDescription" value="Land Speeder und Land Raider erhalten einen Multimelter."/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaFlavor" value="<string name='Weapons/MultiMeltaFlavor'/>"/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/SpaceMarines/LasDamage'/>"/>
	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Upgrades/AstraMilitarum/LasDamageDescription'/>"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/SpaceMarines/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/LastStand" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LastStandDescription" value="Erhöht die Moral aller Einheiten der Space Marines."/>
	<entry name="SpaceMarines/LastStandFlavor" value="<string name='Traits/LastStandFlavor'/>"/>
	<entry name="SpaceMarines/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LocatorBeacon" value="<string name='Traits/LocatorBeacon'/>"/>
	<entry name="SpaceMarines/LocatorBeaconDescription" value="Ermöglicht das Absetzen aus dem Orbit ohne Verbrauch von Aktionspunkten, wenn die Einheit neben Scout Bikers oder Stormraven Gunships platziert wird."/>
	<entry name="SpaceMarines/LocatorBeaconFlavor" value="<string name='Traits/LocatorBeaconFlavor'/>"/>
	<entry name="SpaceMarines/MachineEmpathy" value="<string name='Traits/MachineEmpathy'/>"/>
	<entry name="SpaceMarines/MachineEmpathyDescription" value="<string name='Actions/MachineEmpathyDescription'/>"/>
	<entry name="SpaceMarines/MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="<string name='Traits/SpaceMarines/MeleeDamage'/>"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="<string name='Traits/SpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SpaceMarines/MeltaBombDescription" value="Ermöglicht es Tactical Squads, Assault Squads, Devastortrupps, Scouts und Scout Bikers, eine Melterbombe einzusetzen, die äußerst effektiv gegen schwere Fahrzeuge und Befestigungsanlagen ist."/>
	<entry name="SpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
	<entry name="SpaceMarines/OmniscopeDescription" value="Verleiht Devastor-Centurionen die Fähigkeit, die Fernkampf-Schadensreduktion zu ignorieren."/>
	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalBombardment" value="<string name='Actions/OrbitalBombardment'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentDescription" value="<string name='Actions/OrbitalBombardmentDescription'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentFlavor" value="<string name='Actions/OrbitalBombardmentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalDeployment" value="<string name='Actions/OrbitalDeployment'/>"/>
	<entry name="SpaceMarines/OrbitalDeploymentDescription" value="Ermöglicht es Einheiten, mit einem Drop Pod an jeder beliebigen Stelle des Schlachtfelds zu landen."/>
	<entry name="SpaceMarines/OrbitalDeploymentFlavor" value="<string name='Actions/OrbitalDeploymentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalScan" value="<string name='Actions/OrbitalScan'/>"/>
	<entry name="SpaceMarines/OrbitalScanDescription" value="<string name='Actions/OrbitalScanDescription'/>"/>
	<entry name="SpaceMarines/OrbitalScanFlavor" value="<string name='Actions/OrbitalScanFlavor'/>"/>
	<entry name="SpaceMarines/PredatorLascannon" value="Zusätzliche Schwere Bolter"/>
	<entry name="SpaceMarines/PredatorLascannonDescription" value="Festungen der Erlösung, Predators und Aquila-Makrokanonen erhalten zusätzliche Schwere Bolter."/>
	<entry name="SpaceMarines/PredatorLascannonFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SpaceMarines/SiegeMasters" value="<string name='Traits/SiegeMasters'/>"/>
	<entry name="SpaceMarines/SiegeMastersDescription" value="<string name='Actions/SiegeMastersDescription'/>"/>
	<entry name="SpaceMarines/SiegeMastersFlavor" value="<string name='Actions/SiegeMastersFlavor'/>"/>
	<entry name="SpaceMarines/SiegeShield" value="<string name='Traits/SiegeShield'/>"/>
	<entry name="SpaceMarines/SiegeShieldDescription" value="Erhöht die Panzerung von Vindicators und verringert den Bewegungsmalus in Wäldern und imperialen Ruinen."/>
	<entry name="SpaceMarines/SiegeShieldFlavor" value="<string name='Traits/SiegeShieldFlavor'/>"/>
	<entry name="SpaceMarines/Signum" value="<string name='Actions/Signum'/>"/>
	<entry name="SpaceMarines/SignumDescription" value="Hebt bei Devastortrupps den Malus für schwere Waffen, Geschütze und Salvenwaffen auf."/>
	<entry name="SpaceMarines/SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SpaceMarines/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SpaceMarines/TacticalDoctrine" value="<string name='Traits/TacticalDoctrine'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineDescription" value="<string name='Actions/TacticalDoctrineDescription'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/TeleportHomer" value="<string name='Traits/TeleportHomer'/>"/>
	<entry name="SpaceMarines/TeleportHomerDescription" value="Ermöglicht das Absetzen von Chaplains, Sturmterminatoren und Terminators aus dem Orbit ohne Verbrauch von Aktionspunkten, wenn die entsprechenden Einheiten neben Tactical Marines oder Scouts platziert werden."/>
	<entry name="SpaceMarines/TeleportHomerFlavor" value="<string name='Traits/TeleportHomerFlavor'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeak" value="<string name='Traits/TheFleshIsWeak'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakDescription" value="<string name='Actions/TheFleshIsWeakDescription'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="Tau/AdvancedTargetingSystem" value="<string name='Traits/Tau/AdvancedTargetingSystem'/>"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Kampfanzug-Unterstützungssystem und Fahrzeugausrüstung zur Erhöhung der Fernkampfgenauigkeit."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="<string name='Traits/Tau/AdvancedTargetingSystemFlavor'/>"/>
	<entry name="Tau/AutomatedRepairSystem" value="<string name='Traits/Tau/AutomatedRepairSystem'/>"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Fahrzeuge stellen in jeder Runde Trefferpunkte wieder her."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="<string name='Traits/Tau/AutomatedRepairSystemFlavor'/>"/>
	<entry name="Tau/BlacksunFilter" value="<string name='Traits/Tau/BlacksunFilter'/>"/>
	<entry name="Tau/BlacksunFilterDescription" value="Erhöht die Sichtweite von Fahrzeugen, Himmlischen, Commandern, Spähern und Kampfanzügen."/>
	<entry name="Tau/BlacksunFilterFlavor" value="<string name='Traits/Tau/BlacksunFilterFlavor'/>"/>
	<entry name="Tau/BlastDamage" value="<string name='Traits/Tau/BlastDamage'/>"/>
	<entry name="Tau/BlastDamageDescription" value="Erhöht den Panzerungsdurchschlag von Flammenwaffen und Raketenwaffen."/>
	<entry name="Tau/BlastDamageFlavor" value="<string name='Traits/Tau/BlastDamageFlavor'/>"/>
	<entry name="Tau/BoltDamage" value="<string name='Traits/Tau/BoltDamage'/>"/>
	<entry name="Tau/BoltDamageDescription" value="Erhöht den Panzerungsdurchschlag von Bündelwaffen und Massebeschleunigerwaffen."/>
	<entry name="Tau/BoltDamageFlavor" value="<string name='Traits/Tau/BoltDamageFlavor'/>"/>
	<entry name="Tau/BondingKnifeRitual" value="<string name='Actions/Tau/BondingKnifeRitual'/>"/>
	<entry name="Tau/BondingKnifeRitualDescription" value="Ermöglicht es Feuerkriegern, Angriffsfeuerkriegern, Spähern, Kampfanzügen XV25 Geist, Kampfanzügen XV8 Krisis, Kampfanzügen XV88 Breitseite, Kampfanzügen XV95 Phantom und Kampfanzügen XV104 Sturmflut, ihre Moral wiederherzustellen."/>
	<entry name="Tau/BondingKnifeRitualFlavor" value="<string name='Actions/Tau/BondingKnifeRitualFlavor'/>"/>
	<entry name="Tau/CityTier2" value="<string name='Traits/Tau/CityTier2'/>"/>
	<entry name="Tau/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier2Flavor" value="<string name='Traits/Tau/CityTier2Flavor'/>"/>
	<entry name="Tau/CityTier3" value="<string name='Traits/Tau/CityTier3'/>"/>
	<entry name="Tau/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier3Flavor" value="<string name='Traits/Tau/CityTier3Flavor'/>"/>
	<entry name="Tau/CounterfireDefenceSystem" value="<string name='Traits/Tau/CounterfireDefenceSystem'/>"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Kampfanzug-Unterstützungssystem, das die Genauigkeit bei Abwehrfeuer-Attacken erhöht."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Traits/Tau/CounterfireDefenceSystemFlavor'/>"/>
	<entry name="Tau/DisruptionPod" value="<string name='Traits/Tau/DisruptionPod'/>"/>
	<entry name="Tau/DisruptionPodDescription" value="Erhöht die Fernkampf-Schadensreduktion von Fahrzeugen."/>
	<entry name="Tau/DisruptionPodFlavor" value="<string name='Traits/Tau/DisruptionPodFlavor'/>"/>
	<entry name="Tau/DroneController" value="<string name='Traits/Tau/DroneController'/>"/>
	<entry name="Tau/DroneControllerDescription" value="Kampfanzug-Unterstützungssystem, das die Genauigkeit angrenzender Drohnen erhöht."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Traits/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/EMPGrenade" value="<string name='Weapons/EMPGrenade'/>"/>
	<entry name="Tau/EMPGrenadeDescription" value="Ermöglicht es Feuerkriegern, Angriffsfeuerkriegern und Spähern, Fahrzeugabwehrgranaten einzusetzen."/>
	<entry name="Tau/EMPGrenadeFlavor" value="<string name='Weapons/EMPGrenadeFlavor'/>"/>
	<entry name="Tau/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tau/ExtraInfantryArmourDescription" value="Erhöht die Panzerung von Infanterieeinheiten und Monströsen Kreaturen."/>
	<entry name="Tau/ExtraInfantryArmourFlavor" value="Dank weiterer Studien des Chitinpanzers unserer Kameraden von den Thraxianern können wir bedeutsame strukturelle Verbesserungen an unserer Kader- und Kampfanzug-Panzerung vornehmen."/>
	<entry name="Tau/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Tau/ExtraVehicleArmourDescription" value="Erhöht die Panzerung von Fahrzeugen."/>
	<entry name="Tau/ExtraVehicleArmourFlavor" value="Im Gegensatz zu den festgefahrenen Techpriestern des Mars ist die Erdkaste stets darum bemüht, Neues zu erschaffen. Das Baumaterial Fio'tak ist derzeit ihre größte kreative Errungenschaft. Es handelt sich dabei um ein hartes und äußerst dichtes nanokristallines Verbundmaterial, das sie sparsam für ihre besten Schöpfungen verwenden."/>
	<entry name="Tau/FlechetteDischarger" value="<string name='Traits/Tau/FlechetteDischarger'/>"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Ermöglicht es Fahrzeugen, Nahkampfangreifern Schaden zuzufügen."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="<string name='Traits/Tau/FlechetteDischargerFlavor'/>"/>
	<entry name="Tau/ForTheGreaterGoodBonus" value="Charpactin-Entourage"/>
	<entry name="Tau/ForTheGreaterGoodBonusDescription" value="Verringert die Einflusskosten von „Für das Höhere Wohl“."/>
	<entry name="Tau/ForTheGreaterGoodBonusFlavor" value="Hin und wieder erachten es die Diplomaten der Wasserkaste als sinnvoll, sich auf ihren Missionen von den verbündeten Charpactin begleiten zu lassen, einem Volk empfindungsfähiger pilzähnlicher Kreaturen, deren im UV-Bereich flackernde Kommunikationssysteme eine beruhigende, fast schon hypnotische Wirkung auf fast alle Rassen haben."/>
	<entry name="Tau/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tau/HammerOfWrathDescription" value="Ermöglicht es Kampfanzügen XV95 Phantom, Kampfanzügen XV104 Sturmflut, Kampfanzügen XV107 R'Varna und Ballistischen Anzügen KV128 Orkanwoge, vernichtendere Angriffe durchzuführen."/>
	<entry name="Tau/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tau/LasDamage" value="<string name='Traits/Tau/LasDamage'/>"/>
	<entry name="Tau/LasDamageDescription" value="Erhöht den Panzerungsdurchschlag von Fusionswaffen, Ionenwaffen, Plasmawaffen und Pulswaffen."/>
	<entry name="Tau/LasDamageFlavor" value="<string name='Traits/Tau/LasDamageFlavor'/>"/>
	<entry name="Tau/PhotonGrenade" value="<string name='Weapons/PhotonGrenade'/>"/>
	<entry name="Tau/PhotonGrenadeDescription" value="Ermöglicht es Feuerkriegern, Angriffsfeuerkriegern, Spähern und Kader-Feuerklingen, Blendgranaten einzusetzen."/>
	<entry name="Tau/PhotonGrenadeFlavor" value="<string name='Weapons/PhotonGrenadeFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="<string name='Traits/Tau/PointDefenceTargetingRelay'/>"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Fahrzeuge verursachen erhöhten Abwehrfeuerschaden gegen feindliche Einheiten, die an nicht-feindliche Einheiten angrenzen."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="<string name='Traits/Tau/PointDefenceTargetingRelayFlavor'/>"/>
	<entry name="Tau/PurchaseEnergy" value="<string name='Actions/Tau/PurchaseEnergy'/>"/>
	<entry name="Tau/PurchaseEnergyDescription" value="Ermöglicht es Ihnen, unter Aufwendung von Einfluss Energie zu kaufen."/>
	<entry name="Tau/PurchaseEnergyFlavor" value="<string name='Actions/Tau/PurchaseEnergyFlavor'/>"/>
	<entry name="Tau/PurchaseFood" value="<string name='Actions/Tau/PurchaseFood'/>"/>
	<entry name="Tau/PurchaseFoodDescription" value="Ermöglicht es Ihnen, unter Aufwendung von Einfluss Nahrung zu kaufen."/>
	<entry name="Tau/PurchaseFoodFlavor" value="<string name='Actions/Tau/PurchaseFoodFlavor'/>"/>
	<entry name="Tau/PurchaseOre" value="<string name='Actions/Tau/PurchaseOre'/>"/>
	<entry name="Tau/PurchaseOreDescription" value="Ermöglicht es Ihnen, unter Aufwendung von Einfluss Erz zu kaufen."/>
	<entry name="Tau/PurchaseOreFlavor" value="<string name='Actions/Tau/PurchaseOreFlavor'/>"/>
	<entry name="Tau/PurchasePopulation" value="<string name='Actions/Tau/PurchasePopulation'/>"/>
	<entry name="Tau/PurchasePopulationDescription" value="Ermöglicht es Ihnen, unter Aufwendung von Einfluss die Bevölkerung zu erhöhen."/>
	<entry name="Tau/PurchasePopulationFlavor" value="<string name='Actions/Tau/PurchasePopulationFlavor'/>"/>
	<entry name="Tau/PurchaseResearch" value="<string name='Actions/Tau/PurchaseResearch'/>"/>
	<entry name="Tau/PurchaseResearchDescription" value="Ermöglicht es Ihnen, unter Aufwendung von Einfluss Forschungspunkte zu kaufen."/>
	<entry name="Tau/PurchaseResearchFlavor" value="<string name='Actions/Tau/PurchaseResearchFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="<string name='Traits/Tau/RipykaVa'/>"/>
	<entry name="Tau/RipykaVaDescription" value="Verringert die Abklingzeit für Metastrategien des Commanders."/>
	<entry name="Tau/RipykaVaFlavor" value="<string name='Traits/Tau/RipykaVaFlavor'/>"/>
	<entry name="Tau/SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="Tau/SeekerMissileDescription" value="Fahrzeuge und Kampfanzüge XV88 Breitseite erhalten eine Raketenwaffe."/>
	<entry name="Tau/SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Tau/SensorSpines" value="Fahrzeugsensorik"/>
	<entry name="Tau/SensorSpinesDescription" value="Ermöglicht es Fahrzeugen, sich durch Gelände mit Deckung zu bewegen."/>
	<entry name="Tau/SensorSpinesFlavor" value="Fahrzeugsensorik wird verwendet, um mit Daten ein hochentwickeltes Geländeführungssystem zu füttern, das sichere Routen durch riskantes Gelände berechnet, um mögliche Fallen und Minen zu umgehen, die sich den Blicken der Fahrzeuge womöglich entziehen."/>
	<entry name="Tau/ShieldGenerator" value="<string name='Traits/Tau/ShieldGenerator'/>"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Kampfanzug-Unterstützungssystem, das für eine Unverwundbar-Schadensreduktion sorgt."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Traits/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StimulantInjector" value="<string name='Traits/Tau/StimulantInjector'/>"/>
	<entry name="Tau/StimulantInjectorDescription" value="Kampfanzug-Unterstützungssystem, das die Schadensreduktion „Verletzungen ignorieren“ erhöht."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Traits/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/SubversionBonus" value="Aufrührerische Forschung"/>
	<entry name="Tau/SubversionBonusDescription" value="Erhöht den Loyalitätsmalus durch „Stadt unterwandern“."/>
	<entry name="Tau/SubversionBonusFlavor" value="Als die T'au Nachforschungen über ihre Feinde anstellten, fanden sie in einem militärischen Leitfaden der Menschen den Satz „Denken Sie wie Ihr Gegner“. Die Wasserkaste nahm sich dies zu Herzen und versucht seitdem in Erfahrung zu bringen, welche Bedürfnisse und Wünsche die Bewohner einer Siedlung haben, bevor sie dort eine Revolte anzettelt – ganz gleich, ob es sich bei den Bewohnern um schaudernde Sklaven der Necrons oder um squigfressende Boyz der Orks handelt."/>
	<entry name="Tau/TacticalSupportTurret" value="<string name='Weapons/TacticalSupportTurret'/>"/>
	<entry name="Tau/TacticalSupportTurretDescription" value="Stationäre Feuerkrieger erhalten eine zusätzliche Waffe."/>
	<entry name="Tau/TacticalSupportTurretFlavor" value="<string name='Weapons/TacticalSupportTurretFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="<string name='Traits/Tau/UtopiaBonus'/>"/>
	<entry name="Tau/UtopiaBonusDescription" value="<string name='Traits/Tau/UtopiaBonusDescription'/>"/>
	<entry name="Tau/UtopiaBonusFlavor" value="<string name='Traits/Tau/UtopiaBonusFlavor'/>"/>
	<entry name="Tau/VectoredRetroThrusters" value="<string name='Traits/Tau/VectoredRetroThrusters'/>"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Kampfanzug-Unterstützungssystem, das die Bewegung erhöht und dazu führt, dass die Einheit feindliche Kontrollzonen ignoriert."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Traits/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="<string name='Traits/Tau/VelocityTracker'/>"/>
	<entry name="Tau/VelocityTrackerDescription" value="Kampfanzug-Unterstützungssystem, das die Fernkampfgenauigkeit gegen Flieger erhöht."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Traits/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tyranids/AcidBlood" value="Säureblut"/>
	<entry name="Tyranids/AcidBloodDescription" value="Monströse Kreaturen und Alphakrieger der Tyraniden erhalten die Eigenschaft, allen Nahkampfangreifern automatisch Schaden zuzufügen."/>
	<entry name="Tyranids/AcidBloodFlavor" value="<string name='Traits/Tyranids/AcidBloodFlavor'/>"/>
	<entry name="Tyranids/AdrenalGlands" value="Adrenalindrüsen"/>
	<entry name="Tyranids/AdrenalGlandsDescription" value="Erhöht die Bewegung und den Nahkampfschaden der Einheiten."/>
	<entry name="Tyranids/AdrenalGlandsFlavor" value="Adrenalindrüsen sättigen die Körper ihrer Wirte mit Chemikalien, die den Metabolismus der betroffenen Kreatur in einen hyperaktiven Zustand der Raserei versetzen."/>
	<entry name="Tyranids/BiomorphDamage" value="<string name='Traits/Tyranids/BiomorphDamage'/>"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Erhöht den Panzerungsdurchschlag von Biomorphs."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="<string name='Traits/Tyranids/BiomorphDamageFlavor'/>"/>
	<entry name="Tyranids/BioPlasma" value="Carnifex-Bioplasma"/>
	<entry name="Tyranids/BioPlasmaDescription" value="Carnifexe erhalten eine zusätzliche Fernkampfwaffe."/>
	<entry name="Tyranids/BioPlasmaFlavor" value="<string name='Weapons/BioPlasmaFlavor'/>"/>
	<entry name="Tyranids/BoneMace" value="Carnifex-Knochenkeule"/>
	<entry name="Tyranids/BoneMaceDescription" value="Carnifexe erhalten eine zusätzliche Nahkampfwaffe."/>
	<entry name="Tyranids/BoneMaceFlavor" value="<string name='Weapons/BoneMaceFlavor'/>"/>
	<entry name="Tyranids/CityCost" value="Metamorphe Malantrophen"/>
	<entry name="Tyranids/CityCostDescription" value="Verringert die Kosten für die Gründung neuer Städte."/>
	<entry name="Tyranids/CityCostFlavor" value="Die Rolle der Malantrophen bei der Gründung neuer Tyranidenkolonien ist wenig erforscht, da die imperialen Genetoren in den meisten Fällen entweder starben oder flohen, wenn eine Kolonie entstand. Man geht jedoch davon aus, dass sie die Stadtsaat an neue Orte bringen, wenn ein vorgeschobener Brutstandort benötigt wird. Bisweilen wurden außergewöhnliche Malantrophen gesichtet, die über besondere physische Eigenheiten verfügen, um dieser Aufgabe effizienter nachkommen zu können."/>
	<entry name="Tyranids/CityDamage" value="<string name='Traits/Tyranids/CityDamage'/>"/>
	<entry name="Tyranids/CityDamageDescription" value="Feindliche Einheiten in Städten der Tyraniden erleiden jede Runde Schaden."/>
	<entry name="Tyranids/CityDamageFlavor" value="<string name='Traits/Tyranids/CityDamageFlavor'/>"/>
	<entry name="Tyranids/CityGrowth" value="<string name='Traits/Tyranids/CityGrowth'/>"/>
	<entry name="Tyranids/CityGrowthDescription" value="Erhöht die Wachstumsrate von Städten der Tyraniden."/>
	<entry name="Tyranids/CityGrowthFlavor" value="<string name='Traits/Tyranids/CityGrowthFlavor'/>"/>
	<entry name="Tyranids/CityLoyalty" value="<string name='Traits/Tyranids/CityLoyalty'/>"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Verringert den Loyalitätsmalus für die Anzahl von Tyranidenstädten."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="<string name='Traits/Tyranids/CityLoyaltyFlavor'/>"/>
	<entry name="Tyranids/CityPopulationLimit" value="<string name='Traits/Tyranids/CityPopulationLimit'/>"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Erhöht das Bevölkerungslimit von Städten der Tyraniden."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="<string name='Traits/Tyranids/CityPopulationLimitFlavor'/>"/>
	<entry name="Tyranids/CityProduction" value="<string name='Traits/Tyranids/CityProduction'/>"/>
	<entry name="Tyranids/CityProductionDescription" value="Erhöht die Produktionsrate von Städten der Tyraniden."/>
	<entry name="Tyranids/CityProductionFlavor" value="<string name='Traits/Tyranids/CityProductionFlavor'/>"/>
	<entry name="Tyranids/CityTier2" value="<string name='Traits/Tyranids/CityTier2'/>"/>
	<entry name="Tyranids/CityTier2Description" value="Erhöht den Radius, in dem sich die Stadt Hexfelder einverleiben kann."/>
	<entry name="Tyranids/CityTier2Flavor" value="<string name='Traits/Tyranids/CityTier2Flavor'/>"/>
	<entry name="Tyranids/ConsumeTile2" value="Effiziente Verdauung"/>
	<entry name="Tyranids/ConsumeTile2Description" value="Verringert die Einflusskosten für das Verzehren von Hexfeldern."/>
	<entry name="Tyranids/ConsumeTile2Flavor" value="Es ist für Malantrophen oder Absorber ein Leichtes, sich mit ihren rasiermesserscharfen Kiefern durch Fleisch, Knochen oder sogar Plaststahl zu fressen. Meistens sind sie jedoch damit beschäftigt, in kürzester Zeit riesige Mengen an Erde und Felsgestein für das Schwarmbewusstsein zu verschlingen. Man nimmt an, dass spezialisierte Organismen dabei zum Einsatz kommen, allerdings hat bisher niemand überlebt, der dies hätte verifizieren können."/>
	<entry name="Tyranids/Deathspitter" value="Venatoren-Säurespucker"/>
	<entry name="Tyranids/DeathspitterDescription" value="Venatoren erhalten eine Fernkampfwaffe."/>
	<entry name="Tyranids/DeathspitterFlavor" value="<string name='Weapons/DeathspitterFlavor'/>"/>
	<entry name="Tyranids/DesiccatorLarvae" value="<string name='Weapons/DesiccatorLarvae'/>"/>
	<entry name="Tyranids/DesiccatorLarvaeDescription" value="Schwarmtyranten, Tervigonen und Tyrannofexe erhalten eine Flammenschablonenwaffe."/>
	<entry name="Tyranids/DesiccatorLarvaeFlavor" value="<string name='Weapons/DesiccatorLarvaeFlavor'/>"/>
	<entry name="Tyranids/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourFlavor" value="Das Schwarmbewusstsein ist ziemlich pragmatisch. Warum Ressourcen für die Panzerung der Truppen aufwenden, wenn sie doch nach ihrem Tod ohnehin wiederverwertet werden? Nur wenn es taktisch Sinn macht, investiert das Schwarmbewusstsein in extradicke Chitinpanzerung und Knochen für seine Fauna."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmour" value="<string name='Traits/ExtraMonstrousCreatureArmour'/>"/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourDescription" value="Erhöht die Panzerung Monströser Kreaturen."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourFlavor" value="Mit einer Kombination aus gehärtetem Carapax, Störfeldern und abgetöteten Enden an Nervensträngen kann das Schwarmbewusstsein die Widerstandsfähigkeit seiner größeren Kreaturen auf einfache Weise erhöhen."/>
	<entry name="Tyranids/FleshHooks" value="<string name='Weapons/FleshHooks'/>"/>
	<entry name="Tyranids/FleshHooksDescription" value="Tyranidenkrieger, Alphakrieger und Liktoren erhalten eine zusätzliche Fernkampfwaffe."/>
	<entry name="Tyranids/FleshHooksFlavor" value="<string name='Weapons/FleshHooksFlavor'/>"/>
	<entry name="Tyranids/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tyranids/HammerOfWrathDescription" value="Ermöglicht es Exocrinen, Gargoyles, Haruspexen, Schwarmdruden, Schwarmtyranten, Maleceptoren, Sturmhierodulen, Tervigonen, Trygonen, Tyrannozyten und Tyrannofexen, vernichtendere Angriffe durchzuführen."/>
	<entry name="Tyranids/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="<string name='Traits/Tyranids/InfantryUpkeep'/>"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Verringert die laufenden Biomassekosten für Infanterie."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="<string name='Traits/Tyranids/InfantryUpkeepFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2" value="Rasende Apressanten"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Description" value="Verringert die Einflusskosten für das Unterdrücken instinktiven Verhaltens."/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Flavor" value="Für das Schwarmbewusstsein ist es äußerst wichtig, die Kontrolle über seine Truppen aufrechterhalten zu können, und dies gelingt ihm mit verschiedenen Adaptionen, darunter die Synapsenkreaturen. Dabei gilt es auch, die naturgegebene Wildheit der Einheiten auf dem Schlachtfeld im Zaum zu halten, damit weniger psionische Energie aufgebracht werden muss, um sie wieder unter Kontrolle zu bekommen."/>
	<entry name="Tyranids/LongRangedDamage" value="<string name='Traits/Tyranids/LongRangedDamage'/>"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Erhöht den Panzerungsdurchschlag von Waffen mit hoher Reichweite."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="<string name='Traits/Tyranids/LongRangedDamageFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="<string name='Traits/Tyranids/MeleeDamage'/>"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Erhöht den Panzerungsdurchschlag von Nahkampfwaffen."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="<string name='Traits/Tyranids/MeleeDamageFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="Ermöglicht es Zoantrophen, feindliche Einheiten zu verfluchen und damit ihre Genauigkeit zu verringern."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation2" value="Malantrope Gustarflagelli"/>
	<entry name="Tyranids/PreyAdaptation2Description" value="Erhöht die Anzahl der erhaltenen Forschungspunkte, wenn feindliche Einheiten in der Nähe eines Malantrophen ausgeschaltet werden."/>
	<entry name="Tyranids/PreyAdaptation2Flavor" value="Gustarflagelli sind eine ungewöhnliche Anpassung des Malantrophen-Körpers. Es handelt sich dabei um drahtig-dünne Tentakel, die den Malantrophen wie ein fleischiges Schlingennetz umgeben. Man nimmt an, dass es ihm dadurch möglich ist, mehr genetische Informationen aus gefallenen feindlichen Truppen zu extrahieren und zu speichern."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="<string name='Traits/Tyranids/ProductionBuildingUpkeep'/>"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Verringert die laufenden Einflusskosten für Produktionsgebäude."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ProductionBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="<string name='Traits/Tyranids/Reclamation2'/>"/>
	<entry name="Tyranids/Reclamation2Description" value="Verringert die Einflusskosten für das Rückverwandeln von Einheiten."/>
	<entry name="Tyranids/Reclamation2Flavor" value="<string name='Traits/Tyranids/Reclamation2Flavor'/>"/>
	<entry name="Tyranids/Reclamation3" value="<string name='Traits/Tyranids/Reclamation3'/>"/>
	<entry name="Tyranids/Reclamation3Description" value="Entfernt die Abklingzeit für das Rückverwandeln von Einheiten."/>
	<entry name="Tyranids/Reclamation3Flavor" value="<string name='Traits/Tyranids/Reclamation3Flavor'/>"/>
	<entry name="Tyranids/Regeneration" value="Regeneration"/>
	<entry name="Tyranids/RegenerationDescription" value="Monströse Kreaturen und Alphakrieger stellen in jeder Runde Trefferpunkte wieder her."/>
	<entry name="Tyranids/RegenerationFlavor" value="<string name='Traits/RegenerationFlavor'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="<string name='Traits/Tyranids/ResourceBuildingUpkeep'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Verringert die laufenden Einflusskosten für Ressourcengebäude."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ResourceBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="<string name='Traits/Tyranids/ShortRangedDamage'/>"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Erhöht den Panzerungsdurchschlag von Waffen mit geringer Reichweite."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="<string name='Traits/Tyranids/ShortRangedDamageFlavor'/>"/>
	<entry name="Tyranids/StingerSalvo" value="<string name='Weapons/StingerSalvo'/>"/>
	<entry name="Tyranids/StingerSalvoDescription" value="Schwarmdruden erhalten eine zusätzliche Fernkampfwaffe."/>
	<entry name="Tyranids/StingerSalvoFlavor" value="<string name='Weapons/StingerSalvoFlavor'/>"/>
	<entry name="Tyranids/ThresherScythe" value="<string name='Weapons/ThresherScythe'/>"/>
	<entry name="Tyranids/ThresherScytheDescription" value="Exocrinen und Haruspexe erhalten eine zusätzliche Nahkampfwaffe."/>
	<entry name="Tyranids/ThresherScytheFlavor" value="<string name='Weapons/ThresherScytheFlavor'/>"/>
	<entry name="Tyranids/ToxinSacs" value="<string name='Traits/Tyranids/ToxinSacs'/>"/>
	<entry name="Tyranids/ToxinSacsDescription" value="Erhöht den Schaden von Nahkampfwaffen gegen Infanterie und Monströse Kreaturen."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="<string name='Traits/Tyranids/ToxinSacsFlavor'/>"/>
	<entry name="Tyranids/Toxinspike" value="Trygonen-Toxinstachel"/>
	<entry name="Tyranids/ToxinspikeDescription" value="Trygonen erhalten eine zusätzliche Nahkampfwaffe."/>
	<entry name="Tyranids/ToxinspikeFlavor" value="<string name='Weapons/ToxinspikeFlavor'/>"/>
	<entry name="Tyranids/Tunnel2" value="<string name='Traits/Tyranids/Tunnel2'/>"/>
	<entry name="Tyranids/Tunnel2Description" value="Erhöht die Trefferpunkte von Brutkolonien."/>
	<entry name="Tyranids/Tunnel2Flavor" value="<string name='Traits/Tyranids/Tunnel2Flavor'/>"/>
	<entry name="Tyranids/VehiclesUpkeep" value="<string name='Traits/Tyranids/VehiclesUpkeep'/>"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Verringert die laufenden Biomassekosten für Monströse Kreaturen."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="<string name='Traits/Tyranids/VehiclesUpkeepFlavor'/>"/>

	<entry name="Missing" value="Fehlt"/>
</language>
