<?xml version="1.0" encoding="utf-8"?> 
<language> 
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#0" value="Vector introducido."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#1" value="Cinco-de-cinco."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#2" value="Comenzando pasada."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Attack#3" value="Comenzando saturación con proyectiles de ametralladora."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Broken#0" value="Preservando activo aéreo"/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Hurt#0" value="Alas alcanzadas"/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#0" value="Dando vuelta, dando vueltas."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#1" value="Distribución aérea de libaciones."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#2" value="Cambio de presión. Cambiando patrón de fibras."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Idle#3" value="Preparando evacuación de Land Crawlers."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Shaken#0" value="¡Mayday, mayday!"/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:Victory#0" value="Ya no se detectan señales enemigas. Reiniciando ciclo."/>
 	<entry name="AdeptusMechanicus/ArchaeopterTransvector:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#0" value="Dirigiendo Kastelans."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#1" value="Ungiendo runas."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#2" value="Suprimiendo ánimo del silicio."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Attack#3" value="Insertando obleas doctrinales."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Broken#0" value="…los Hombres de Hierro…"/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Hurt#0" value="…reparación imposible…"/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#0" value="La carne es débil, la máquina es fuerte."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#1" value="En binario, ritmo y son. Lo haría para que pudieras oírlo."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#2" value="No puede haber mejora en el diseño del Omnissiah."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Idle#3" value="In nomine imperii et mechanicus et spiritui omnissiah."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Shaken#0" value="Se han encontrado obleas de retirada."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:Victory#0" value="Sabiduría antigua, poder probado."/>
 	<entry name="AdeptusMechanicus/CyberneticaDatasmith:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#0" value="VESTRA INDUSTRIA NOSTRUM"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#1" value="EL DESPILFARRO ES PECADO"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#2" value="¡POR LA FUERZA MOTRIZ!"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Attack#3" value="LA CHISPA SE MUEVE EN SU INTERIOR"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Broken#0" value="Nostra potex decit…¡nuestra voluntad falla!"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Hurt#0" value="Y, A PESAR DE ELLO, MARCHAMOS."/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#0" value="LLORO LAS LÁGRIMAS DEL OMNISSIAH"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#1" value="MALDITOS CORPUSCARII"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#2" value="MI ELECTOO, TIENE HAMBRE"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Idle#3" value="IN NOMINE DEUS MECHANICUS"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Shaken#0" value="CHISPA"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:Victory#0" value="SOMOS VIGOR PERSONIFICADO"/>
 	<entry name="AdeptusMechanicus/FulguriteElectroPriest:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#0" value="¡Probarán la sabiduría de Marte!"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#1" value="Solución a larga distancia encontrada."/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#2" value="Objetivo por enlace de datos aceptado."/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Attack#3" value="Espíritus Máquinas liberados."/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Broken#0" value="Dominus, debemos-"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Hurt#0" value="Servidores monotarea fallando."/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#0" value="Servidores descansando."/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#1" value="Aceitando chasis."/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#2" value="Extirpando debilidad."/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Idle#3" value="Soñando con un planeta rojo…"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Shaken#0" value="¡Soluciones de fuego del enemigo superiores!"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:Victory#0" value="Durante 10.000 años, nuestrav victoria ha sido segura."/>
 	<entry name="AdeptusMechanicus/IronstriderBallistarius:VictoryCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#0" value="C32: Alcanzado Patrón de Fuego."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#1" value="7T32: Todo El Armamento Desplegado."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#2" value="8-6: Clado Breacher activo."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Attack#3" value="P4-78: Blindado/Transporte Buscar & Destruir."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Broken#0" value="Activos Desechables…una vez humanos-"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Hurt#0" value="Funcionalidad Comprometida. Máquina Vigente. Carne 45%, Bajando."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#0" value="Tejido Necrótico Siendo Extirpado."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#1" value="Servidor Esperando A Ser Gastado."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#2" value="Memoria De La Hierba, Nubes, Cielo Azul…Error De Borrado De Memoria. Borrando."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Idle#3" value="Comenzando Coro Binario."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Shaken#0" value="Bajo Fuego Sostenido."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/KataphronBreacher:Victory#0" value="Eliminado, Brutalmente. Satisfactorio."/>
	<entry name="AdeptusMechanicus/KataphronBreacher:VictoryCount" value="1"/>	
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#0" value="C32: Conseguido Patrón de Fuego."/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#1" value="8-7: Activada Clade Destructora"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#2" value="7T32: Todo el armamento desplegado."/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Attack#3" value="867: Ignición Cognis y/o Phosphor."/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Broken#0" value="Sólo los hombres temen. Una vez fuimos-"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Hurt#0" value="Funcionalidad Comprometida. Carne Existente."/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#0" value="T418: Servidor Inactivo Presente."/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#1" value="182: A la Espera de Mantenimiento."/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#2" value="ERROR: ¿Qué Era Yo?"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Idle#3" value="326: Memorias Resurgiendo…Suprimidas"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Shaken#0" value="991: Emociones Escapando."/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:Victory#0" value="T1: Función Completada, Siguiente Objetivo."/>
 	<entry name="AdeptusMechanicus/KataphronDestroyer:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Attack#0" value="No somos más que un arma en la mano derecha del Emperador."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Attack#1" value="¡Por el honor del Questor Mechanicus!"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Attack#2" value="Temed mis pasos!"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Attack#3" value="¡SOY UN CABALLERO IMPERIAL!"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Broken#0" value="No… no puede ser."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Hurt#0" value="Muerte antes que deshonor."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Idle#0" value="Mi dama espera."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Idle#1" value="Por las justas de mi mundo natal."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Idle#2" value="Mi casa es famosa por su humildad, sabes."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Idle#3" value="Este gigante de hierro espera."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Shaken#0" value="TE ATREVES."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/KnightCrusader:Victory#0" value="No es ninguna deshonra caer por mi mano."/>
 	<entry name="AdeptusMechanicus/KnightCrusader:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#0" value="Purgando, Dominus."/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#1" value="Todavía purgando, Dominus."/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#2" value="Mis garras cortan limpiamente."/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Attack#3" value="No podéis escapar del fósforo, idiotas."/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Broken#0" value="Ya en vuelo…"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Hurt#0" value="Nuestra purga está al alcance…"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#0" value="Limpiar…debemos limpiar."/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#1" value="Los muñones…pican."/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Idle#2" value="Siempre en el ala."/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:IdleCount" value="3"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Shaken#0" value="La suciedad es…galopante."/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:Victory#0" value="Polvo al polvo…"/>
 	<entry name="AdeptusMechanicus/PteraxiiSterylizor:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#0" value="Liberando la Fuerza Motriz, tch."/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#1" value="Galvanizando a los Skitarii, ssssí…"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#2" value="…lanza mmmagnaraíl…"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Attack#3" value="¡¿No estamos revigorizados?!"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Broken#0" value="La lógica dicta que esto no está pasando."/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Hurt#0" value="No…no planificamos esto."/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#0" value="Oh, sólo estoy despilfarrando energía."/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#1" value="Enganchado a la Célula Galvánica."/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#2" value="Sobrecargando, infrasirviendo."/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Idle#3" value="Para entender a los Xenos…"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Shaken#0" value="¡Proteged a vuestro Magos, insectos!"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:Victory#0" value="Su bioelectricidad…es mía."/>
 	<entry name="AdeptusMechanicus/TechPriestManipulus:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#0" value="Usad la cobertura, conservad el fuego."/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#1" value="Te necesito vivo, Skitarii."/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#2" value="No hay disensión, ahora luchamos."/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Attack#3" value="¡Tengo el control!"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Broken#0" value="Somos desechables…"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Hurt#0" value="¿Morimos..? ¡Salvad al Magos!"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#0" value="Seguimos tus planes, Magos."/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#1" value="¿Podemos salvar algunos Skitarii?"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#2" value="Vivimos para morir, Magos."/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Idle#3" value="Servocráneo, te bautizo como Morte."/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Shaken#0" value="¡Emitid el Edicto de Control!"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:Victory#0" value="Nuestras pérdidas fueron mínimas, Magos."/>
 	<entry name="AdeptusMechanicus/SkitariiMarshal:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#0" value="¡Rad-Soldados en el campo!"/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#1" value="Veamos a quién mata antes el Radium…"/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#2" value="Carabines radiantes."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Attack#3" value="No hay quien nos pare."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Broken#0" value="Muramos más tarde, mejor."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Hurt#0" value="Perdiendo rad y sangre, aquí."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#0" value="Tengo 99 mejoras, pero la protección anti-rad no es una de ellas."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#1" value="Me quemo."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#2" value="Ugh…no me encuentro muy bien."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Idle#3" value="Quiero decir, yo tampoco comería cerca de nosotros."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Shaken#0" value="Magos, morimos por ti."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:Victory#0" value="No les queda ni media vida."/>
 	<entry name="AdeptusMechanicus/SkitariiVanguard:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#0" value="Mi movimiento, tu muerte."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#1" value="Peones, avanzad."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#2" value="Veo todas las piezas."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Attack#3" value="Haciendo cada uno de los movimientos."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Broken#0" value="Parece que se hace necesaria una defensa…¿Luzhnin?"/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:BrokenCount" value="1"/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Hurt#0" value="Apuntando al rey, inteligente."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#0" value="Ningún ciclo inactivo."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#1" value="La ineficiencia se convierte en entropía."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#2" value="Qué alegría, la manipulación."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Idle#3" value="Sí…veo sus movimientos y contramovimientos."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Shaken#0" value="Inesperado, un gambito. Admirable."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:Victory#0" value="Planée esta victoria hace muchos movimientos. Pero la dulzura permanece."/>
 	<entry name="AdeptusMechanicus/TechPriestDominus:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/Headquarters:Attack#0" value="La ciudad espera."/>
 	<entry name="AdeptusMechanicus/Headquarters:Attack#1" value="Sus vidas nos pertenecen a nosotros, no a ti."/>
 	<entry name="AdeptusMechanicus/Headquarters:Attack#2" value="Servidores disparando."/>
 	<entry name="AdeptusMechanicus/Headquarters:Attack#3" value="Forja en combate."/>
 	<entry name="AdeptusMechanicus/Headquarters:AttackCount" value="4"/>
 	<entry name="AdeptusMechanicus/Headquarters:Broken#0" value="La ciudad caerá."/>
 	<entry name="AdeptusMechanicus/Headquarters:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/Headquarters:Hurt#0" value="Estamos sufriendo bajas civiles. Aceptable."/>
	<entry name="AdeptusMechanicus/Headquarters:HurtCount" value="1"/>
 	<entry name="AdeptusMechanicus/Headquarters:Idle#0" value="Revuelta siendo suprimida."/>
 	<entry name="AdeptusMechanicus/Headquarters:Idle#1" value="Se están enviando clones a las Cunas de Protoservidores."/>
 	<entry name="AdeptusMechanicus/Headquarters:Idle#2" value="En el nombre del Omnissiah, templos hab limpios."/>
 	<entry name="AdeptusMechanicus/Headquarters:Idle#3" value="Producción de biónicos aumentada en 0.1%"/>
 	<entry name="AdeptusMechanicus/Headquarters:IdleCount" value="4"/>
 	<entry name="AdeptusMechanicus/Headquarters:Shaken#0" value="Las masas están aterrorizadas. Desplegando Provostes."/>
 	<entry name="AdeptusMechanicus/Headquarters:ShakenCount" value="1"/>
 	<entry name="AdeptusMechanicus/Headquarters:Victory#0" value="Amenazas externas eliminadas. Prestando atención a la disensión."/>
 	<entry name="AdeptusMechanicus/Headquarters:VictoryCount" value="1"/>
 	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="AdeptusMechanicus/ArchaeopterTransvector"/>
 	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="AdeptusMechanicus/IronstriderBallistarius"/>
 	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="AdeptusMechanicus/IronstriderBallistarius"/>
 	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="AdeptusMechanicus/SkitariiVanguard"/>
	<entry name="AstraMilitarum/Baneblade:AlliedFaction:SpaceMarines#0" value="Marines Espaciales divisados. Los canales de comunicación están abiertos."/>
	<entry name="AstraMilitarum/Baneblade:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Avistado Comisario Mayor. Preparen para traerlo más cerca."/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/TankCommander#0" value="Señor, encantado de ver un oficial que aprecia el valor de una armadura, ¡señor!"/>
	<entry name="AstraMilitarum/Baneblade:AlliedUnit:AstraMilitarum/TankCommanderCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Artefact#0" value="Divisada estructura Xenos."/>
	<entry name="AstraMilitarum/Baneblade:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Attack#0" value="Acción de fuego trazado."/>
	<entry name="AstraMilitarum/Baneblade:Attack#1" value="Objetivo fijado."/>
	<entry name="AstraMilitarum/Baneblade:Attack#2" value="Disparar barriles 1-11, señor."/>
	<entry name="AstraMilitarum/Baneblade:AttackCount" value="3"/>
	<entry name="AstraMilitarum/Baneblade:Broken#0" value="Activando marcha atrás, señor."/>
	<entry name="AstraMilitarum/Baneblade:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Cover#0" value="No se como , pero el tanque está a cubierto, señor."/>
	<entry name="AstraMilitarum/Baneblade:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:AstraMilitarum#0" value="Traidores divisados, señor."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:Necrons#0" value="Xenos divisados, señor. Prepárense para demostrarles lo que es un monstruo de metal real."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:Orks#0" value="Pieles verdes en el área, señor. ¡No van a hacer un partekráneoz de nosotros!"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:SpaceMarines#0" value="Enemigo Adeptus Astartes localizado. No hay señal de Fellblades."/>
	<entry name="AstraMilitarum/Baneblade:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:EnemyUnit:Neutral/Enslaver#0" value="Señor, aconsejamos retirada rápida. No podemos garantizar que este vehículo no se vea comprometido."/>
	<entry name="AstraMilitarum/Baneblade:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Hurt#0" value="Daño severo constante. Recomendamos volver a Marte para reparar."/>
	<entry name="AstraMilitarum/Baneblade:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Idle#0" value="Dejando huellas, señor."/>
	<entry name="AstraMilitarum/Baneblade:Idle#1" value="Comprobando los once barriles del infierno, señor."/>
	<entry name="AstraMilitarum/Baneblade:Idle#2" value="El enemigo es débil, señor. Ayudaremos a que caigan."/>
	<entry name="AstraMilitarum/Baneblade:Idle#3" value="Contando remaches, señor."/>
	<entry name="AstraMilitarum/Baneblade:Idle#4" value="Cuando crezca, quiero ser un Hellhammer. Señor."/>
	<entry name="AstraMilitarum/Baneblade:Idle#5" value="¿Ha oído que Vance Stubbs perdió cien Baneblades, señor? Algo descuidado."/>
	<entry name="AstraMilitarum/Baneblade:IdleCount" value="6"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory0#0" value="Preparados para reclamar Gladius Prime, señor."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory0Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory1#0" value="Reagrupando la guardia, señor."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory1Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory2#0" value="Señor, con el debido respeto, ese Tecno-Sacerdote no es digno de lubricar nuestros espolones."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory2Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory3#0" value="Dejaremos la exploración subterránea a la infantería. Señor."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory3Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory4#0" value="Podemos mantener a los Xenos atrás, no es un problema, señor."/>
	<entry name="AstraMilitarum/Baneblade:QuestStory4Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory5#0" value="¡Kastelans! ¡Necesitaremos los once barriles, señor!"/>
	<entry name="AstraMilitarum/Baneblade:QuestStory5Count" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Shaken#0" value="¡Sin retirada, señor! No estoy seguro de si esta cosa va marcha atrás de todo modos."/>
	<entry name="AstraMilitarum/Baneblade:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:Victory#0" value="Objetivo eliminado, señor. Recargando todos los barriles."/>
	<entry name="AstraMilitarum/Baneblade:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Baneblade:WireWeed#0" value="Señor, recomendamos mover el vehículo; la tripulación no puede hacerse cargo de esta alambrada."/>
	<entry name="AstraMilitarum/Baneblade:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:AlliedFaction:SpaceMarines#0" value="Aliados Astartes divisados, alabad al Emperador"/>
	<entry name="AstraMilitarum/Basilisk:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Comisario divisado. Me alegra que se encuentre en primera línea… lejos de nosotros."/>
	<entry name="AstraMilitarum/Basilisk:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Artefact#0" value="Los proyectiles no están ni arañando esa cosa. ¿De qué está hecha?"/>
	<entry name="AstraMilitarum/Basilisk:Artefact#1" value="Seremos purgados solo por mirarlo."/>
	<entry name="AstraMilitarum/Basilisk:ArtefactCount" value="2"/>
	<entry name="AstraMilitarum/Basilisk:Attack#0" value="Coordenadas recibidas, blanco fijado. Disparando."/>
	<entry name="AstraMilitarum/Basilisk:Attack#1" value="Proyectiles en ruta."/>
	<entry name="AstraMilitarum/Basilisk:Attack#2" value="Observadores en el campo."/>
	<entry name="AstraMilitarum/Basilisk:Attack#3" value="Objetivo bajo fuego. ¡Encantados de estar de vuelta!"/>
	<entry name="AstraMilitarum/Basilisk:AttackCount" value="4"/>
	<entry name="AstraMilitarum/Basilisk:Broken#0" value="¡Dejad el arma y corred!"/>
	<entry name="AstraMilitarum/Basilisk:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Cover#0" value="Localización perfecta para la artillería. No nos verán llegar."/>
	<entry name="AstraMilitarum/Basilisk:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:AstraMilitarum#0" value="Siempre tenemos un regalo para los traidores—132mm de furia del Emperador."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:Necrons#0" value="¿Hombres lata? 15km parece una distancia perfecta para enfrentarlos."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:Orks#0" value="Ah, pielesverdes. Para lo que este cañón fue construido."/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:SpaceMarines#0" value="¿Marines renegados? Me pregunto si sus auras los protegerán contra sus ex"/>
	<entry name="AstraMilitarum/Basilisk:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:EnemyUnit:Neutral/Enslaver#0" value="¿Que son esas COSAS? Mantenedlos a distancia, soldados."/>
	<entry name="AstraMilitarum/Basilisk:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Hurt#0" value="Compartimiento de la tripulación penetrado… es un desastre aquí dentro."/>
	<entry name="AstraMilitarum/Basilisk:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Idle#0" value="¿Has oído hablar del Patrón de los Basiliscos? Fue canonizado. ¿Lo pillas?"/>
	<entry name="AstraMilitarum/Basilisk:Idle#1" value="No envíes a una Manticora a hacer el trabajo de un Basilisco."/>
	<entry name="AstraMilitarum/Basilisk:Idle#2" value="Siento lastima por los grunts de primera línea… pero no la suficiente para unirme a ellos."/>
	<entry name="AstraMilitarum/Basilisk:Idle#3" value="Solo dinos cuando hay algo a lo que dispararle."/>
	<entry name="AstraMilitarum/Basilisk:Idle#4" value="No pasa nada en la guerra… hasta que pasa repentinamente."/>
	<entry name="AstraMilitarum/Basilisk:IdleCount" value="5"/>
	<entry name="AstraMilitarum/Basilisk:Shaken#0" value="¿Hay algo disparando… a nosotros?"/>
	<entry name="AstraMilitarum/Basilisk:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:Victory#0" value="¡Convirtiendo los enemigos en cráteres durante milenios, sah!"/>
	<entry name="AstraMilitarum/Basilisk:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Basilisk:WireWeed#0" value="¿Biowire? Quiero decir que hay peores posiciones de disparo, como dentro de la Gran Fisura…"/>
	<entry name="AstraMilitarum/Basilisk:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedFaction:SpaceMarines#0" value="¡Hombrejefe Marine!"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Bullgryns leales."/>
	<entry name="AstraMilitarum/Bullgryn:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Artefact#0" value="¿Gran fing?"/>
	<entry name="AstraMilitarum/Bullgryn:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Attack#0" value="Bullgryns disparando, yer hombrejefe."/>
	<entry name="AstraMilitarum/Bullgryn:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Broken#0" value="Ogryns no tan duros."/>
	<entry name="AstraMilitarum/Bullgryn:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Cover#0" value="Escondidos en un arbusto."/>
	<entry name="AstraMilitarum/Bullgryn:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:AstraMilitarum#0" value="Espera, ¿Estamos luchando por el Emperador?¿Nosotros o ellos?"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:Necrons#0" value="No puedo morder chapa. Maldición."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:Orks#0" value="Orkos. Duros, apuestos, liztos. Como nosotros."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:SpaceMarines#0" value="Los niños del Emperador? ¡¿Luchamos contra ellos?!"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:EnemyUnit:Neutral/Enslaver#0" value="Nah, No se que son."/>
	<entry name="AstraMilitarum/Bullgryn:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Hurt#0" value="Ow."/>
	<entry name="AstraMilitarum/Bullgryn:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#0" value="Los come huesos… duros de matar."/>
	<entry name="AstraMilitarum/Bullgryn:Idle#1" value="Un Ogryn en el pecho de un Orko muerto. Uh… dos! Dos Ogryns…"/>
	<entry name="AstraMilitarum/Bullgryn:Idle#2" value="Afilando nuestras estacas, señor."/>
	<entry name="AstraMilitarum/Bullgryn:Idle#3" value="Hacemos lo que nos dijiste. Y tu dijiste nada."/>
	<entry name="AstraMilitarum/Bullgryn:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Bullgryn:Shaken#0" value="Espera… ¿Por donde vamos?"/>
	<entry name="AstraMilitarum/Bullgryn:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:Victory#0" value="¿Qué? ¿Hemos ganado?"/>
	<entry name="AstraMilitarum/Bullgryn:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Bullgryn:WireWeed#0" value="¡Ow, mi pierna!"/>
	<entry name="AstraMilitarum/Bullgryn:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:AlliedFaction:SpaceMarines#0" value="¡Alabad al Emperador, los Astartes están aquí!"/>
	<entry name="AstraMilitarum/Guardsman:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Sacad pecho, meted barriga, es el Comisario Mayor."/>
	<entry name="AstraMilitarum/Guardsman:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Artefact#0" value="¿Esto lo han hecho los Xenos…?"/>
	<entry name="AstraMilitarum/Guardsman:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Attack#0" value="¿Puede alguien ver a lo que le estamos disparando?"/>
	<entry name="AstraMilitarum/Guardsman:Attack#1" value="Enemigos enfrente, comisarios a nuestra espalda…"/>
	<entry name="AstraMilitarum/Guardsman:Attack#2" value="¡Dale todo lo que tienes!"/>
	<entry name="AstraMilitarum/Guardsman:Attack#3" value="Desearía que Creed estuviese aquí…"/>
	<entry name="AstraMilitarum/Guardsman:Attack#4" value="Ponlos bajo el fuego, podemos hacerlo."/>
	<entry name="AstraMilitarum/Guardsman:AttackCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Broken#0" value="¡No me enrolé para esto!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#1" value="¡Corred muchachos, y esperad que el Comisario no esté mirando!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#2" value="¡Nos están matando! ¡Y no podemos contraatacar!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#3" value="¡Es un sangriento Mundo de Muerte!"/>
	<entry name="AstraMilitarum/Guardsman:Broken#4" value="¿Donde están los refuerzos? ¡Donde están-!"/>
	<entry name="AstraMilitarum/Guardsman:BrokenCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Cover#0" value="Volver a las trincheras, gracias al Emperador."/>
	<entry name="AstraMilitarum/Guardsman:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/Enslaver#0" value="¿Esclavistas? Oh, Emperador, sálvanos."/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/Psychneuein#0" value="Grandes bichos de Prospero—Mantenedlos alejados de los psíquicos!"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnitNeutral/KhorneLordOfSkulls#0" value="¡Caos! ¿Aquí?"/>
	<entry name="AstraMilitarum/Guardsman:EnemyUnitNeutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Hurt#0" value="Es para lo que estamos hechos, señor."/>
	<entry name="AstraMilitarum/Guardsman:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Idle#0" value="¡Por el trono dorado…!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#1" value="¡Por Guilliman y el Emperador!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#2" value="¿Como de alto, señor?"/>
	<entry name="AstraMilitarum/Guardsman:Idle#3" value="Lo peor es el silencio antes de la batalla."/>
	<entry name="AstraMilitarum/Guardsman:Idle#4" value="Estamos jugando señor, el Comisario dice que es bueno para la moral."/>
	<entry name="AstraMilitarum/Guardsman:Idle#5" value="La vida en la guardia, señor. Terriblemente dura."/>
	<entry name="AstraMilitarum/Guardsman:Idle#6" value="¡Recibiéndote alto y claro, señor!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#7" value="¡Señor, si, señor!"/>
	<entry name="AstraMilitarum/Guardsman:Idle#8" value="¡Atentos, el Comisario está llegando!"/>
	<entry name="AstraMilitarum/Guardsman:IdleCount" value="9"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory0#0" value="Aún no puedo creer que sobreviviésemos. Tantos…"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory0Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory1#0" value="Mantenemos la banda unida, señor."/>
	<entry name="AstraMilitarum/Guardsman:QuestStory1Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory2#0" value="¿Una nueva ciudad? Estamos seguros—¡No, señor, no cuestionamos sus ordenes, señor!"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory2Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory3#0" value="Esas tormentas me dan dolor de cabeza—¿Quedaremos aquí atrapados para siempre?"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory3Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory4#0" value="¡Están dentro de la ciudad! ¡Volved!"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory4Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:QuestStory5#0" value="Mi madre siempre decía, 'nunca confíes en un tecnosacerdote'. Tenía razón mi madre."/>
	<entry name="AstraMilitarum/Guardsman:QuestStory5Count" value="1"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#0" value="¿Podemos rezar? Oh, Emperador, oh Guilliman, sálvanos."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#1" value="Comandante, estamos retrocediendo."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#2" value="Coraje, muchachos, coraje."/>
	<entry name="AstraMilitarum/Guardsman:Shaken#3" value="¡Si nos quedamos, el enemigo nos disparará!. ¡Si nos vamos, el comisario nos disparará!"/>
	<entry name="AstraMilitarum/Guardsman:Shaken#4" value="¿Podemos tener mejores armas, por favor?"/>
	<entry name="AstraMilitarum/Guardsman:ShakenCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:Victory#0" value="Comisario, señor, ¿Permiso para celebrar?"/>
	<entry name="AstraMilitarum/Guardsman:Victory#1" value="¡Ja! Me siento somo si hubiese derribado un Titan con una bengala."/>
	<entry name="AstraMilitarum/Guardsman:Victory#2" value="A la memoria de Cadia y Creed…"/>
	<entry name="AstraMilitarum/Guardsman:Victory#3" value="¿Estamos ganando..? ¡Estamos ganando!"/>
	<entry name="AstraMilitarum/Guardsman:Victory#4" value="¡Guau, el Primer tiene razón! ¡Estos xenos son fáciles!"/>
	<entry name="AstraMilitarum/Guardsman:VictoryCount" value="5"/>
	<entry name="AstraMilitarum/Guardsman:WireWeed#0" value="¡Señor, el alambre, está vivo!"/>
	<entry name="AstraMilitarum/Guardsman:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:AlliedFaction:SpaceMarines#0" value="Comandante, el Cuartel General ha divisado Marines Espaciales aliados."/>
	<entry name="AstraMilitarum/Headquarters:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Estamos contentos de en el campo de batalla, Señor."/>
	<entry name="AstraMilitarum/Headquarters:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Artefact#0" value="Artefacto Xenos peligrosamente cerca de la ciudad, señor."/>
	<entry name="AstraMilitarum/Headquarters:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Attack#0" value="Manteniendo el control territorial."/>
	<entry name="AstraMilitarum/Headquarters:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Broken#0" value="Problemas para mantener las ordenes de aguantar hasta el final."/>
	<entry name="AstraMilitarum/Headquarters:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Cover#0" value="¿Como se pone a cubierto una ciudad?"/>
	<entry name="AstraMilitarum/Headquarters:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:AstraMilitarum#0" value="¡Traidores aproximándose a la a la ciudad, señor!"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:Necrons#0" value="Tropas Necrón aproximándose."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:Orks#0" value="Pielesverdes incómodamente cerca, Señor."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:SpaceMarines#0" value="Marines Espaciales aproximándose. Arrasarán la ciudad si consiguen traspasar los muros."/>
	<entry name="AstraMilitarum/Headquarters:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:EnemyUnit:Neutral/Enslaver#0" value="PELIGRO: XENOS EXTREMADAMENTE PELIGROSOS DETECTADOS."/>
	<entry name="AstraMilitarum/Headquarters:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Hurt#0" value="¡La infraestructura ha sufrida daños!"/>
	<entry name="AstraMilitarum/Headquarters:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Idle#0" value="Guardias en servicio."/>
	<entry name="AstraMilitarum/Headquarters:Idle#1" value="Siempre vigilantes."/>
	<entry name="AstraMilitarum/Headquarters:Idle#2" value="¿Quién ha estado jugando al Tarot? Guardadlo."/>
	<entry name="AstraMilitarum/Headquarters:Idle#3" value="Todo tranquilo, señor."/>
	<entry name="AstraMilitarum/Headquarters:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Headquarters:Shaken#0" value="Retirándonos a posiciones defensivas, señor."/>
	<entry name="AstraMilitarum/Headquarters:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:Victory#0" value="Ciudad segura, señor."/>
	<entry name="AstraMilitarum/Headquarters:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Headquarters:WireWeed#0" value="¿Fue construir esta ciudad en bio-wire una buena idea?"/>
	<entry name="AstraMilitarum/Headquarters:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedFaction:SpaceMarines#0" value="Menos mal que los Astarte han aparecido."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedUnit:AstraMilitarum/LordCommissar#0" value="¿Donde almuerzan los Comisarios? En la comisaria. Je."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Artefact#0" value="Si no podemos dispararle a esta cosa, no me interesa."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#0" value="¿Puede alguien ver si el objetivo está aún allí?"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#1" value="¡Por el trono, que retrocedan!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#2" value="¿Fuego pesado!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#3" value="¡Abridlos en canal!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Attack#4" value="Cuando los láser no cortan…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:AttackCount" value="5"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Broken#0" value="¡Retroceder, sabuesos, retroceder!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Cover#0" value="Como dicen en Krieg: mantén tus morteros a cubierto y ellos te cubrirán."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:CoverCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:AstraMilitarum#0" value="Malditos traidores. ¡Iluminadlos!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:Necrons#0" value="Vamos a pelar su piel metálica."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:Orks#0" value="Dispararles a los pielesverdes es un desperdicio de munición."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:SpaceMarines#0" value="Si tienes que dispararle a un Marine Espacial, asegurate de acertar."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyUnit:Neutral/Enslaver#0" value="¡Mandad a esos engendros infernales de vuelta a la disformidad!"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Hurt#0" value="No tengo tiempo de sangrar."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:HurtCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#0" value="¿Sabes cuanto tiempo hace falta para montar las bayonetas a los lascannons?"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#1" value="Los Xenos pueden venir a nosotros."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#2" value="Hay algo esperándonos ahí fuera…"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Idle#3" value="Somos como un tanque que realmente puede acertar al objetivo."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:IdleCount" value="4"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Shaken#0" value="No deberíamos estar bajo fuego enemigo, Señor."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:Victory#0" value="Muerto, por supuesto."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:WireWeed#0" value="Inmundas trampas Xenos."/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:AlliedFaction:SpaceMarines#0" value="Los ángeles están con nosotros."/>
	<entry name="AstraMilitarum/Hydra:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Es útil tener un hombre que nos mantengan con los pies en el suelo."/>
	<entry name="AstraMilitarum/Hydra:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Artefact#0" value="Algo alíen."/>
	<entry name="AstraMilitarum/Hydra:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Attack#0" value="Manteniendo los cielos despejados."/>
	<entry name="AstraMilitarum/Hydra:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Broken#0" value="Retrocediendo a terreno elevado."/>
	<entry name="AstraMilitarum/Hydra:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Cover#0" value="Cobertura perfecta para observar nubes."/>
	<entry name="AstraMilitarum/Hydra:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:AstraMilitarum#0" value="Imperiales, pero de mentes empañadas."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:Necrons#0" value="Extrañas máquinas voladoras, muy letales."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:Orks#0" value="Los voladores pielesverdes son rudimentarios, pero efectivos."/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:SpaceMarines#0" value="¿Puedo derribar un Thunderhawks? ¡Vamos a averiguarlo!"/>
	<entry name="AstraMilitarum/Hydra:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:EnemyUnit:Neutral/Enslaver#0" value="Flotar no es volar, horrores alienígenas."/>
	<entry name="AstraMilitarum/Hydra:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Hurt#0" value="Nosotros vigilamos los cielos, y se supone que vosotros nos vigiláis a nosotros."/>
	<entry name="AstraMilitarum/Hydra:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Idle#0" value="Observando las nubes, señor."/>
	<entry name="AstraMilitarum/Hydra:Idle#1" value="Jaghatai nimbus, rogal stratus, cirrosanguinius…"/>
	<entry name="AstraMilitarum/Hydra:Idle#2" value="Las estrellas son increíbles por aquí."/>
	<entry name="AstraMilitarum/Hydra:Idle#3" value="Este planeta tiene los más increíbles amaneceres. Señor."/>
	<entry name="AstraMilitarum/Hydra:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Hydra:Shaken#0" value="Algo nos está derribando, señor."/>
	<entry name="AstraMilitarum/Hydra:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:Victory#0" value="Cielos despejados, señor."/>
	<entry name="AstraMilitarum/Hydra:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Hydra:WireWeed#0" value="Enredos terrestres, señor."/>
	<entry name="AstraMilitarum/Hydra:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedFaction:SpaceMarines#0" value="Fortificación reportando Adeptus Astartes en el perímetro—amigables."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/Baneblade#0" value="Emocionado de ver un Baneblade, señor. Especialmente cuando los cañones apuntan al otro lado."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/BanebladeCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Fortificación manteniendo la línea, señor."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Artefact#0" value="Fortificación reportando artefacto Xenos en las inmediaciones."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Attack#0" value="Fortificación desplegando activos militares."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:AttackCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Broken#0" value="La fortificación está perdida, repito, la fortificación está perdida."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:AstraMilitarum#0" value="Fortificación reportando movimiento de traidores en la periferia."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:Necrons#0" value="Fortificación reportando que lo Necrones está en movimiento."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:Orks#0" value="Fortificación reportando presencia Orka."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:SpaceMarines#0" value="Fortificación reportando… El Emperador nos salve… enemigos Adeptus Astartes."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyUnit:Neutral/Enslaver#0" value="Fortificación reportando Xenos—Esclavistas, señor."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Hurt#0" value="Fortificación reportando—no estamos seguros de poder sobrevivir a esto, señor."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:HurtCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#0" value="Guardia vigilando, señor."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#1" value="Siempre vigilantes."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#2" value="Fortificación reportando—nada que reportar."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Idle#3" value="Todo tranquilo, señor."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:IdleCount" value="4"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Shaken#0" value="¿Señor, puede oírnos? Estamos bajo fuego."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:Victory#0" value="Fortificación aguantando, señor."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ImperialStrongpoint:WireWeed#0" value="La Bio-wire está infestando el centro de comando, señor."/>
	<entry name="AstraMilitarum/ImperialStrongpoint:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedFaction:SpaceMarines#0" value="¡Los Marines se han unido a la fiesta! Bien por ellos."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Otro noble sin montura. ¿Por que solo Yarrick puede tener su propio Baneblade?"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Artefact#0" value="Protegiendo el artefacto. Nuestros placer."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Attack#0" value="Enviando algunos encantadores proyectiles en su dirección señor."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:AttackCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Broken#0" value="Tristemente, señor, nos estamos retirando."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Cover#0" value="En los árboles, preparando una emboscada, señor."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:CoverCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:AstraMilitarum#0" value="Estaremos machando los huesos de los herejes antes de que el día termine."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:Necrons#0" value="Ah, justo nuestros objetivos preferidos—Necrones."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:Orks#0" value="Preparados para autodestrucción—a los Orkos les encanta coger prestados nuestros tanques Leman Russ."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:SpaceMarines#0" value="Este cañón está diseñado para hacer llorar a los Marines."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyUnit:Neutral/Enslaver#0" value="Enemigos Xenos—Mantenedlos a distancia."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Hurt#0" value="¡Wow, ese ha ido derecho al blindaje!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:HurtCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#0" value="¿Podrá el Leman Russ moverse?"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#1" value="Dejando rastro."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#2" value="Esperando."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Idle#3" value="Deseando que Pask estuviese conduciendo."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:IdleCount" value="4"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Shaken#0" value="¡Tanque impactado!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:Victory#0" value="¡Otra victoria para el tanque más famoso de la galaxia!"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/LemanRussBattleTank:WireWeed#0" value="Bio-wire, la única cosa más común que un Leman Russ."/>
	<entry name="AstraMilitarum/LemanRussBattleTank:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedFaction:SpaceMarines#0" value="Siempre se agradece ver a Astartes leales… si sorprendente."/>
	<entry name="AstraMilitarum/LordCommissar:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Ah, un cámara en armas."/>
	<entry name="AstraMilitarum/LordCommissar:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Artefact#0" value="Xenos asquerosos. Deberíamos quemarlo."/>
	<entry name="AstraMilitarum/LordCommissar:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Attack#0" value="¡Dispárales o yo te disparare a ti!"/>
	<entry name="AstraMilitarum/LordCommissar:AttackCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Broken#0" value="¡No puede dejar que los hombres me vean así!"/>
	<entry name="AstraMilitarum/LordCommissar:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Cover#0" value="Tácticamente es bueno esconderse, pero no demasiado para la moral."/>
	<entry name="AstraMilitarum/LordCommissar:CoverCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:AstraMilitarum#0" value="¡Traidores! ¡Basura! ¡OS ENFRENTAREIS A LA JUSTICIA DEL EMPERADOR!"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:Necrons#0" value="Latas asquerosas. Vamos a fundirlos para hacer más balas."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:Orks#0" value="Esbirros pielesverdes. El rigor imperial debería encargarse rápidamente de ellos."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:SpaceMarines#0" value="Escoria renegada de la mayor creación de nuestro Emperador. Debemos eliminarlos de la galaxia."/>
	<entry name="AstraMilitarum/LordCommissar:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyUnit:Neutral/Enslaver#0" value="¡Horrores Xenos en mi cabeza!"/>
	<entry name="AstraMilitarum/LordCommissar:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Hurt#0" value="Es solo una herida de la carne."/>
	<entry name="AstraMilitarum/LordCommissar:HurtCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#0" value="Dispensar justicia es muy cansado."/>
	<entry name="AstraMilitarum/LordCommissar:Idle#1" value="Mano ociosas hacen el mal trabajar"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#2" value="Las raciones han sido reducidas. Por eso parezco un poco demacrado."/>
	<entry name="AstraMilitarum/LordCommissar:Idle#3" value="¿Que haría Cain?"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#4" value="¡Ayudante! ¡Limpia esto otra vez!"/>
	<entry name="AstraMilitarum/LordCommissar:Idle#5" value="Haz tu deber en el nombre de- Oh, siempre olvido esta parte."/>
	<entry name="AstraMilitarum/LordCommissar:IdleCount" value="6"/>
	<entry name="AstraMilitarum/LordCommissar:Shaken#0" value="A espíritu dominatus, Domine, libra nos."/>
	<entry name="AstraMilitarum/LordCommissar:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:Victory#0" value="Un ejemplo para mis tropas."/>
	<entry name="AstraMilitarum/LordCommissar:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/LordCommissar:WireWeed#0" value="Será duro mantenerse en el bio-wire, aunque valiente."/>
	<entry name="AstraMilitarum/LordCommissar:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedFaction:SpaceMarines#0" value="Alguno Astartes Allí abajo. Amistosos."/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedUnit:AstraMilitarum/Thunderbolt#0" value="Encantados de tener soporte de caza."/>
	<entry name="AstraMilitarum/MarauderBomber:AlliedUnit:AstraMilitarum/ThunderboltCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Artefact#0" value="Objetivo de alto valor divisado."/>
	<entry name="AstraMilitarum/MarauderBomber:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#0" value="Bombas fuera."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#1" value="Si puedes verlos, puedes acertarles."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#2" value="Abriendo el compartimento de las bombas."/>
	<entry name="AstraMilitarum/MarauderBomber:Attack#3" value="Cualquier cosa al alcance de las bombas es juego limpio."/>
	<entry name="AstraMilitarum/MarauderBomber:AttackCount" value="4"/>
	<entry name="AstraMilitarum/MarauderBomber:Broken#0" value="¡Nos vamos abajo!"/>
	<entry name="AstraMilitarum/MarauderBomber:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Cover#0" value="Si estábamos volando bajo, para que necesitamos campanillas en esta cosa."/>
	<entry name="AstraMilitarum/MarauderBomber:CoverCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Hurt#0" value="Tenemos tres motores inutilizados, más agujeros que un queso, sin comunicación y perdiendo fuel…"/>
	<entry name="AstraMilitarum/MarauderBomber:HurtCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#0" value="No soy muy bueno para los discursos"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#1" value="Te juro, Carpenter, que esas bombas hablan."/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#2" value="¿Alguien sabe como aterrizar esta cosa?"/>
	<entry name="AstraMilitarum/MarauderBomber:Idle#3" value="Practisin manoov-res. Como el Primer dice."/>
	<entry name="AstraMilitarum/MarauderBomber:IdleCount" value="4"/>
	<entry name="AstraMilitarum/MarauderBomber:Shaken#0" value="Moviéndonos a altitudes mayores—los antiaéreos resuenan a nuestro alrededor."/>
	<entry name="AstraMilitarum/MarauderBomber:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/MarauderBomber:Victory#0" value="¿Cual es el siguiente objetivo?"/>
	<entry name="AstraMilitarum/MarauderBomber:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedFaction:SpaceMarines#0" value="Siento… un gran bien de estos."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedUnit:AstraMilitarum/LordCommissar#0" value="UNa mente retorcida al absolutismo… fascinante."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Artefact#0" value="Los huesos viejos construyeron estos pequeños juguetes."/>
	<entry name="AstraMilitarum/PrimarisPsyker:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#0" value="Vuestras mentes arderán…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#1" value="Siento los ojos del Emperador en mí."/>
	<entry name="AstraMilitarum/PrimarisPsyker:Attack#2" value="Te quemaré in Su nombre."/>
	<entry name="AstraMilitarum/PrimarisPsyker:AttackCount" value="3"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Broken#0" value="¿Su luz, donde está su luz?"/>
	<entry name="AstraMilitarum/PrimarisPsyker:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Cover#0" value="La mente oculta es la mente sabia."/>
	<entry name="AstraMilitarum/PrimarisPsyker:CoverCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:AstraMilitarum#0" value="Somos dos mentes, parece ser."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:Necrons#0" value="No puedo sentir nada de ellos. Nada."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:Orks#0" value="Una oleada de emoción-, amor a la ferocidad."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:SpaceMarines#0" value="Gran seguridad de acero… en la superficie."/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyUnit:Neutral/Enslaver#0" value="Siento su hambre, su deseo por fecundar nuestra mente"/>
	<entry name="AstraMilitarum/PrimarisPsyker:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Hurt#0" value="Es solo carne."/>
	<entry name="AstraMilitarum/PrimarisPsyker:HurtCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#0" value="Incluso a través de estas tormentas, puedo atisbar Su luz."/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#1" value="Se lo que estás pensando…"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Idle#2" value="Trabajo ocioso para mentes ociosas"/>
	<entry name="AstraMilitarum/PrimarisPsyker:IdleCount" value="3"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Shaken#0" value="Mejor morir aquí que en las Naves Negras."/>
	<entry name="AstraMilitarum/PrimarisPsyker:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:Victory#0" value="Como el Tarot del Emperador predijo."/>
	<entry name="AstraMilitarum/PrimarisPsyker:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/PrimarisPsyker:WireWeed#0" value="Ahí vida aquí, aunque parece extraña y sangrienta."/>
	<entry name="AstraMilitarum/PrimarisPsyker:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedFaction:SpaceMarines#0" value="Hombreras grandes, ¡son los Marines!"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedUnit:AstraMilitarum/LordCommissar#0" value="¿Puedo volver a primera línea, por favor?"/>
	<entry name="AstraMilitarum/ScoutSentinel:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#0" value="¡Fuego opresor!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#1" value="¡En nombre del Primarca!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#2" value="¡Asalto blindado!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Attack#3" value="¡Uno está disparando…con todo!"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:AttackCount" value="4"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Broken#0" value="Uno ha de preservar el tanque-"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Hurt#0" value="Directo a través del casco-"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:HurtCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#0" value="¿Qué haría Dorn?"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#1" value="Uno sirve a la voluntad del Emperador."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#2" value="Armageddon, eso sí que fue una batalla de tanques…"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Idle#3" value="Menos infantería, más blindados."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:IdleCount" value="4"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Shaken#0" value="Han alcanzado a Uno."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/RogalDornBattleTank:Victory#0" value="Uno siempre está buscando nuevos objetivos."/>
	<entry name="AstraMilitarum/RogalDornBattleTank:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Artefact#0" value="¡Whoah, deberías ver esto!"/>
	<entry name="AstraMilitarum/ScoutSentinel:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Attack#0" value="¡Vaciando todo el cargador!"/>
	<entry name="AstraMilitarum/ScoutSentinel:AttackCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Broken#0" value="Patas largas, ¡a correr!"/>
	<entry name="AstraMilitarum/ScoutSentinel:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Cover#0" value="Esconderos detrás de un árbol o lo que sea."/>
	<entry name="AstraMilitarum/ScoutSentinel:CoverCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:AstraMilitarum#0" value="Maldición, es de los nuestros, pero maaaaalo."/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:Necrons#0" value="¡Hombres de chapa, ahoy!"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:Orks#0" value="He visto algunos ojos rojos observándome desde la chatarra!"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:SpaceMarines#0" value="Ah, ¿Qué? ¿Estamos luchando contra ellos? ¿Somos los malos entonces?"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyUnit:Neutral/Enslaver#0" value="Uh… ¿Qué demonios es eso?"/>
	<entry name="AstraMilitarum/ScoutSentinel:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Hurt#0" value="Huh, alguien me ha abierto un nuevo tragaluz."/>
	<entry name="AstraMilitarum/ScoutSentinel:HurtCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#0" value="¿No debería, ya sabes, estar haciendo algo?"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#1" value="Estas patas recorrerán el camino."/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#2" value="¡Imagina todo lo que hay fuera esperando ser descubierto!"/>
	<entry name="AstraMilitarum/ScoutSentinel:Idle#3" value="¡Mantengamos la marcha!"/>
	<entry name="AstraMilitarum/ScoutSentinel:IdleCount" value="4"/>
	<entry name="AstraMilitarum/ScoutSentinel:Shaken#0" value="¡Sácame de aquí!"/>
	<entry name="AstraMilitarum/ScoutSentinel:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:Victory#0" value="Siempre supe que íbamos a ganar. ¡Siguiente!"/>
	<entry name="AstraMilitarum/ScoutSentinel:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/ScoutSentinel:WireWeed#0" value="Muéveme rápido, esto se me está metiendo en las patas."/>
	<entry name="AstraMilitarum/ScoutSentinel:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:AlliedFaction:SpaceMarines#0" value="Si ellos no tienen land raiders, estoy fuera."/>
	<entry name="AstraMilitarum/TankCommander:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:AlliedUnit:AstraMilitarum/Baneblade#0" value="Ohhhh… que preciosidad."/>
	<entry name="AstraMilitarum/TankCommander:AlliedUnit:AstraMilitarum/BanebladeCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Artefact#0" value="Si no es un blindaje del enemigo, no me interesa."/>
	<entry name="AstraMilitarum/TankCommander:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Attack#0" value="¡Boom! ¡Jajaja!"/>
	<entry name="AstraMilitarum/TankCommander:Attack#1" value="¡Acércame! Ya sabes porque…"/>
	<entry name="AstraMilitarum/TankCommander:Attack#2" value="¡Disparad el cañón! ¡Y el otro! ¡Diparadlo todo!"/>
	<entry name="AstraMilitarum/TankCommander:Attack#3" value="¡Fuego! ¡Jajaja!"/>
	<entry name="AstraMilitarum/TankCommander:AttackCount" value="4"/>
	<entry name="AstraMilitarum/TankCommander:Broken#0" value="¡No, no retrocedáis! ¿Quién está al cargo aquí?"/>
	<entry name="AstraMilitarum/TankCommander:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Cover#0" value="Una emboscada, me gusta."/>
	<entry name="AstraMilitarum/TankCommander:CoverCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:AstraMilitarum#0" value="¡Traidores! Herejes… ¿tienen algún tanque?"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:Necrons#0" value="Monolitos, ahora si tenemos un reto."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:Orks#0" value="Eh, deberíamos haber traído el Demolisher."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:SpaceMarines#0" value="¡Traidores! Probablemente ni sepan cual es la parte delantera del Rhino."/>
	<entry name="AstraMilitarum/TankCommander:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:EnemyUnit:Neutral/KastelanRobot#0" value="Oh, ya no se hacen blindajes como este."/>
	<entry name="AstraMilitarum/TankCommander:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Hurt#0" value="Los mantengo ocupados dejando que me hagan agujeros."/>
	<entry name="AstraMilitarum/TankCommander:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Idle#0" value="Mejor una cabeza pisoteada que una cabeza muerta. ¡Ja!"/>
	<entry name="AstraMilitarum/TankCommander:Idle#1" value="Pues sacándole brillo a mis medallas."/>
	<entry name="AstraMilitarum/TankCommander:Idle#2" value="¿Qué quieres decir con meterme dentro? Necesito ver lo que pasa."/>
	<entry name="AstraMilitarum/TankCommander:Idle#3" value="Deseando entrar en acción."/>
	<entry name="AstraMilitarum/TankCommander:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TankCommander:Shaken#0" value="No creo que la vieja dama pueda aguantar muchos más de esos."/>
	<entry name="AstraMilitarum/TankCommander:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:Victory#0" value="El mejor sitio para tu enemigo es debajo de las orugas."/>
	<entry name="AstraMilitarum/TankCommander:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TankCommander:WireWeed#0" value="¡Hey, Ese alambre esta arañando la pintura!"/>
	<entry name="AstraMilitarum/TankCommander:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AlliedUnit:AstraMilitarum/LordCommissar#0" value="Encantado de estar bajo esta fanática jurisprudencia."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AlliedUnit:AstraMilitarum/LordCommissarCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Artefact#0" value="Desmantelando la tentación."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Attack#0" value="En combate."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:AttackCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Broken#0" value="La carne es débil, debemos retirarnos."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Cover#0" value="Probabilidad de impacto por balística enemiga fuertemente reducida."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:CoverCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:AstraMilitarum#0" value="Molestias."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:Necrons#0" value="Las formas de verdaderos devotos del Dios Máquina, con el alma de estúpidos Xenos."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:Orks#0" value="Primitivos. Mantenedlos alejados de nuestras máquinas sagradas."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:SpaceMarines#0" value="Un pena."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/KastelanRobot#0" value="¡Una reliquia sagrada! Minimizar el daño sobre ella."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/TechpriestBetrayer#0" value="¿Un hereje? Aquí. Improbable."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:EnemyUnit:Neutral/TechpriestBetrayerCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Hurt#0" value="Reparación necesaria."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:HurtCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#0" value="Aplicando ungüentos sagrados."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#1" value="Comprobando cogitadores."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#2" value="Realizando ritos de máquina."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Idle#3" value="Revitalizando espíritus de máquina."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:IdleCount" value="4"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Shaken#0" value="El Omnissiah está conmigo."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:Victory#0" value="Predecible."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/TechpriestEnginseer:WireWeed#0" value="La Biowire es una diversión. Tenemos que replicarla."/>
	<entry name="AstraMilitarum/TechpriestEnginseer:WireWeedCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion" value="AstraMilitarum/Guardsman"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedFaction:SpaceMarines#0" value="¡Algo brillante allí abajo!"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedUnit:AstraMilitarum/MarauderBomber#0" value="¡Los chicos voladores tenemos que permanecer unidos!"/>
	<entry name="AstraMilitarum/Thunderbolt:AlliedUnit:AstraMilitarum/MarauderBomberCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Artefact#0" value="¿Alguien ha visto que era eso? ¡Wow!"/>
	<entry name="AstraMilitarum/Thunderbolt:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Attack#0" value="¡Solo ráfagas cortas! ¡Conservad la munición!"/>
	<entry name="AstraMilitarum/Thunderbolt:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Broken#0" value="Camino a casa."/>
	<entry name="AstraMilitarum/Thunderbolt:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Cover#0" value="Solo cobertura nubosa."/>
	<entry name="AstraMilitarum/Thunderbolt:CoverCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:AstraMilitarum#0" value="Guardia traidor.Un entorno rico de objetivos."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:Orks#0" value="Orkos. No están contentos a menos que estén aterrizando demasiado rápido con fuego sobre sus cabezas."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:SpaceMarines#0" value="Marines. Pasan más tiempo decorando sus armaduras que luchando."/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyUnit:Neutral/Psychneuein#0" value="¿Alguien quiere zumbar a una gran abeja?"/>
	<entry name="AstraMilitarum/Thunderbolt:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Hurt#0" value="Umm, sin puntos para el segundo puesto."/>
	<entry name="AstraMilitarum/Thunderbolt:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#0" value="Siento el deseo de disparar."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#1" value="Sin tiempo para pensar aquí arriba."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#2" value="Este planeta es como un paseo por el parque."/>
	<entry name="AstraMilitarum/Thunderbolt:Idle#3" value="Puedes ser mi compañero de vuelo siempre que quieras."/>
	<entry name="AstraMilitarum/Thunderbolt:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Thunderbolt:Shaken#0" value="¡Vamos a salir ardiendo!"/>
	<entry name="AstraMilitarum/Thunderbolt:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Thunderbolt:Victory#0" value="¡La victoria cuesta mucho más que un vuelo de lujo."/>
	<entry name="AstraMilitarum/Thunderbolt:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Artefact#0" value="No, en serio, no puedo llevar esto."/>
	<entry name="AstraMilitarum/Valkyrie:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Attack#0" value="Extracción bajo fuego."/>
	<entry name="AstraMilitarum/Valkyrie:AttackCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Broken#0" value="¡Se supone que tenemos que entrar y salir rápido!"/>
	<entry name="AstraMilitarum/Valkyrie:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:AstraMilitarum#0" value="Grunts en el campo."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:Necrons#0" value="Hombres de hojalata llegando rápido."/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:Orks#0" value="Pielesverdes en la ZA"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:SpaceMarines#0" value="¡No me enrolé para combatir Marines!"/>
	<entry name="AstraMilitarum/Valkyrie:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Hurt#0" value="¡Esto está caliente, repito, caliente!"/>
	<entry name="AstraMilitarum/Valkyrie:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#0" value="¿Podemos requisar altavoces?"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#1" value="Maldición, ¿Dónde está la acción?"/>
	<entry name="AstraMilitarum/Valkyrie:Idle#2" value="Es la calma lo que te mata."/>
	<entry name="AstraMilitarum/Valkyrie:Idle#3" value="No paramos hasta que caemos."/>
	<entry name="AstraMilitarum/Valkyrie:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Valkyrie:Shaken#0" value="¡No somos de primera línea, sácanos de aquí!"/>
	<entry name="AstraMilitarum/Valkyrie:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Valkyrie:Victory#0" value="Enemigo abatido, extracción por favor."/>
	<entry name="AstraMilitarum/Valkyrie:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedFaction:SpaceMarines#0" value="Nos sentimos… tranquilos."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedUnit:AstraMilitarum/PrimarisPsyker#0" value="Nuestros hermanos de las Naves Negras…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AlliedUnit:AstraMilitarum/PrimarisPsykerCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Artefact#0" value="Impío de los santos."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:ArtefactCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#0" value="Acelerad ahora, juntos los despedazaremos."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#1" value="Una codetta para sus tristes vidas."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#2" value="Y, como uno, damos brincos."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Attack#3" value="Una única alma atormentada para cantarle aparte."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:AttackCount" value="4"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Broken#0" value="Nos han cercenado, estamos perdidos."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Cover#0" value="Protegidos del ojo, no de la mente."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:CoverCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:AstraMilitarum#0" value="Familiares y amigos, pero condenados al pecado."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:Necrons#0" value="Nada, no hay nada aquí."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:NecronsCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:Orks#0" value="Otra voluntad colectiva… alienígena, sin embargo."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:OrksCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:SpaceMarines#0" value="Los propios hijos descabellados del Emperador…"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyUnit:Neutral/Psychneuein#0" value="Los avatares de la fertilidad, esperando dar a luz más horrores."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Hurt#0" value="Llega la disonancia, nosotros nos vamos."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:HurtCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#0" value="Más allá de la vista, allí están. La Disformidad."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#1" value="Tan lejos de Su luz."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#2" value="Si sobrevivimos a las Naves Negras, podemos sobrevivir a esto."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Idle#3" value="El planeta nos susurra."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:IdleCount" value="4"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Shaken#0" value="Nuestra armonía se derrumba, nuestro requiem se acerca."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:Victory#0" value="¿Que elegía para aquellos que mueren como ganado?"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/WyrdvanePsyker:WireWeed#0" value="Las serpientes de plata se nos enredan, pinchándonos."/>
	<entry name="AstraMilitarum/WyrdvanePsyker:WireWeedCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Attack#0" value="Protocolo de combate"/>
	<entry name="Necrons/CanoptekScarab:AttackCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Broken#0" value="Protocolo de daño"/>
	<entry name="Necrons/CanoptekScarab:BrokenCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Hurt#0" value="Daño sufrido."/>
	<entry name="Necrons/CanoptekScarab:HurtCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Idle#0" value="Protocolo de mantenimiento."/>
	<entry name="Necrons/CanoptekScarab:Idle#1" value="Minimizando el consumo de potencia."/>
	<entry name="Necrons/CanoptekScarab:Idle#2" value="Cuidando de los dañados"/>
	<entry name="Necrons/CanoptekScarab:Idle#3" value="Hemos esperados eones. Esperaremos más…."/>
	<entry name="Necrons/CanoptekScarab:IdleCount" value="4"/>
	<entry name="Necrons/CanoptekScarab:Shaken#0" value="Evasión recomendada."/>
	<entry name="Necrons/CanoptekScarab:ShakenCount" value="1"/>
	<entry name="Necrons/CanoptekScarab:Victory#0" value="Invasor disuadido."/>
	<entry name="Necrons/CanoptekScarab:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekSpyder" value="Necrons/CanoptekScarab"/>
	<entry name="Necrons/Cryptek:Attack#0" value="Muere, ganado."/>
	<entry name="Necrons/Cryptek:Attack#1" value="La propia física te traiciona."/>
	<entry name="Necrons/Cryptek:Attack#2" value="Nunca llegarás a entender que te ha matado."/>
	<entry name="Necrons/Cryptek:AttackCount" value="3"/>
	<entry name="Necrons/Cryptek:Broken#0" value="Eones de aprendizaje, y al final he aprendido a temer."/>
	<entry name="Necrons/Cryptek:BrokenCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:AstraMilitarum#0" value="Razas esclavas del Antiguo enemigo."/>
	<entry name="Necrons/Cryptek:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:Necrons#0" value="¿Otro cortejo? Quizás necesitan mis servicios…"/>
	<entry name="Necrons/Cryptek:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:Orks#0" value="Carne, convertida en arma. Admirable."/>
	<entry name="Necrons/Cryptek:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Cryptek:EnemyFaction:SpaceMarines#0" value="Esclavos auto-mejorados. La disección puede enseñarnos mucho."/>
	<entry name="Necrons/Cryptek:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Cryptek:Hurt#0" value="Fascinante, se las han arreglado para herirme."/>
	<entry name="Necrons/Cryptek:HurtCount" value="1"/>
	<entry name="Necrons/Cryptek:Idle#0" value="¿Disección o vivisección? Ah, la eterna pregunta."/>
	<entry name="Necrons/Cryptek:Idle#1" value="Recuerdas… los sueños? Yo si."/>
	<entry name="Necrons/Cryptek:Idle#2" value="Estaré separando átomos hasta que se me necesite."/>
	<entry name="Necrons/Cryptek:Idle#3" value="Tanto por aprender, y yo aquí sentado."/>
	<entry name="Necrons/Cryptek:IdleCount" value="4"/>
	<entry name="Necrons/Cryptek:Shaken#0" value="Quizás hay algo en las creaciones del Viejo."/>
	<entry name="Necrons/Cryptek:ShakenCount" value="1"/>
	<entry name="Necrons/Cryptek:Victory#0" value="Y así termina todo ser viviente que nos reta."/>
	<entry name="Necrons/Cryptek:VictoryCount" value="1"/>
	<entry name="Necrons/Deathmark:Attack#0" value="Apuntando a sus líderes."/>
	<entry name="Necrons/Deathmark:Attack#1" value="Desintegrando sus sinapsis."/>
	<entry name="Necrons/Deathmark:Attack#2" value="Apuntando a enemigo deshonroso."/>
	<entry name="Necrons/Deathmark:Attack#3" value="Marcando para su muerte."/>
	<entry name="Necrons/Deathmark:AttackCount" value="4"/>
	<entry name="Necrons/Deathmark:Broken#0" value="¡Volved a la dimensión alternativa!"/>
	<entry name="Necrons/Deathmark:BrokenCount" value="1"/>
	<entry name="Necrons/Deathmark:Hurt#0" value="Protocolos de reanimación fallando."/>
	<entry name="Necrons/Deathmark:HurtCount" value="1"/>
	<entry name="Necrons/Deathmark:Idle#0" value="Monitorizando comunicaciones."/>
	<entry name="Necrons/Deathmark:Idle#1" value="¿Está el Mentiroso realmente aquí?"/>
	<entry name="Necrons/Deathmark:Idle#2" value="Localizando nodos de mando."/>
	<entry name="Necrons/Deathmark:Idle#3" value="Protocolos de decapitación en espera."/>
	<entry name="Necrons/Deathmark:IdleCount" value="4"/>
	<entry name="Necrons/Deathmark:Shaken#0" value="Somos herramientas de precisión, no blancos."/>
	<entry name="Necrons/Deathmark:ShakenCount" value="1"/>
	<entry name="Necrons/Deathmark:Victory#0" value="Como esperado, eliminado."/>
	<entry name="Necrons/Deathmark:VictoryCount" value="1"/>
	<entry name="Necrons/DestroyerLord" value="Necrons/Lord"/>
	<entry name="Necrons/DoomScythe:Attack#0" value="Descendiendo al mobiliario."/>
	<entry name="Necrons/DoomScythe:AttackCount" value="1"/>
	<entry name="Necrons/DoomScythe:Broken#0" value="Tenemos que partir."/>
	<entry name="Necrons/DoomScythe:BrokenCount" value="1"/>
	<entry name="Necrons/DoomScythe:Hurt#0" value="Ellos… ¿osan?"/>
	<entry name="Necrons/DoomScythe:HurtCount" value="1"/>
	<entry name="Necrons/DoomScythe:Idle#0" value="Dando vueltas."/>
	<entry name="Necrons/DoomScythe:Idle#1" value="Maniobras."/>
	<entry name="Necrons/DoomScythe:Idle#2" value="Observando el horizonte."/>
	<entry name="Necrons/DoomScythe:Idle#3" value="Concentrado."/>
	<entry name="Necrons/DoomScythe:IdleCount" value="4"/>
	<entry name="Necrons/DoomScythe:Shaken#0" value="¿Dañado?"/>
	<entry name="Necrons/DoomScythe:ShakenCount" value="1"/>
	<entry name="Necrons/DoomScythe:Victory#0" value="Humillando a nuestros enemigos."/>
	<entry name="Necrons/DoomScythe:VictoryCount" value="1"/>
	<entry name="Necrons/DoomsdayArk" value="Necrons/AnnihilationBarge"/>
	<entry name="Necrons/Headquarters" value="Necrons/Monolith"/>
	<entry name="Necrons/HeavyDestroyer:Attack#0" value="¡La vida es nuestro enemigo!"/>
	<entry name="Necrons/HeavyDestroyer:AttackCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Broken#0" value="¡La muerte es nuestra victoria!"/>
	<entry name="Necrons/HeavyDestroyer:BrokenCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:AstraMilitarum#0" value="¡Machacad la carne débil!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:Necrons#0" value="¡Nosotros mismos, un soberbio enemigo!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:Orks#0" value="¡Purga la fungosa inmundicia de la galaxia!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:SpaceMarines#0" value="¡Peones del Dios-Emperador!"/>
	<entry name="Necrons/HeavyDestroyer:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Hurt#0" value="¡El dolor nos dirige!"/>
	<entry name="Necrons/HeavyDestroyer:HurtCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Idle#0" value="¡Somos nihilistas!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#1" value="¡Estamos hambrientos!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#2" value="¡Tenemos que mejorar, para destruir!"/>
	<entry name="Necrons/HeavyDestroyer:Idle#3" value="¿Por qué estamos aún aquí? ¡¿Por qué nos estamos matando?!"/>
	<entry name="Necrons/HeavyDestroyer:IdleCount" value="4"/>
	<entry name="Necrons/HeavyDestroyer:Shaken#0" value="¿Por qué no mueren más rápido?"/>
	<entry name="Necrons/HeavyDestroyer:ShakenCount" value="1"/>
	<entry name="Necrons/HeavyDestroyer:Victory#0" value="¡Mueren! ¡No es suficiente!"/>
	<entry name="Necrons/HeavyDestroyer:VictoryCount" value="1"/>
	<entry name="Necrons/Immortal" value="Necrons/Warrior"/>
	<entry name="Necrons/Lord:Attack#0" value="¡Siente la fuerza eterna de los Necrones!"/>
	<entry name="Necrons/Lord:Attack#1" value="Perece orgullosamente a mis manos."/>
	<entry name="Necrons/Lord:Attack#2" value="¿Quien se atreve a desafiarme?"/>
	<entry name="Necrons/Lord:Attack#3" value="¡Te enseñaremos obediencia!"/>
	<entry name="Necrons/Lord:AttackCount" value="4"/>
	<entry name="Necrons/Lord:Broken#0" value="¿Dónde están mis Inmortales? ¿Mis Guardias?"/>
	<entry name="Necrons/Lord:BrokenCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:AstraMilitarum#0" value="Moscas, destinados a vivir solo un día."/>
	<entry name="Necrons/Lord:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:Necrons#0" value="¡Malditos! ¡¿Rivales, en mis dominios?!"/>
	<entry name="Necrons/Lord:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:Orks#0" value="¡Tanta brutalidad! Y, sin embargo, gobiernan mi galaxia."/>
	<entry name="Necrons/Lord:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Lord:EnemyFaction:SpaceMarines#0" value="Tan razonables, tan jóvenes. No vivirán mucho tiempo."/>
	<entry name="Necrons/Lord:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Lord:Hurt#0" value="¿Han visto mi velo? ¡Destruidlos!"/>
	<entry name="Necrons/Lord:HurtCount" value="1"/>
	<entry name="Necrons/Lord:Idle#0" value="La consciencia es una mera palabra para mantener la fuerza contenida."/>
	<entry name="Necrons/Lord:Idle#1" value="Los videntes, mataré hasta el último de ellos."/>
	<entry name="Necrons/Lord:Idle#2" value="¿Qué será lo primero que haga cuando gobernemos este mundo?"/>
	<entry name="Necrons/Lord:IdleCount" value="3"/>
	<entry name="Necrons/Lord:Shaken#0" value="La muerte no puede marcarme."/>
	<entry name="Necrons/Lord:Shaken#1" value="Los Ancestrales los han entrenado bien."/>
	<entry name="Necrons/Lord:ShakenCount" value="2"/>
	<entry name="Necrons/Lord:Victory#0" value="Desespera y muere."/>
	<entry name="Necrons/Lord:VictoryCount" value="1"/>
	<entry name="Necrons/Monolith:Attack#0" value="Látigo de partículas preparado."/>
	<entry name="Necrons/Monolith:AttackCount" value="1"/>
	<entry name="Necrons/Monolith:Broken#0" value="Retrocediendo… despacio."/>
	<entry name="Necrons/Monolith:BrokenCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:AstraMilitarum#0" value="Humanos divisados."/>
	<entry name="Necrons/Monolith:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:Necrons#0" value="Corte enemiga en el planeta."/>
	<entry name="Necrons/Monolith:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:Orks#0" value="Orkos divisados."/>
	<entry name="Necrons/Monolith:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Monolith:EnemyFaction:SpaceMarines#0" value="Humanos mejorados divisados."/>
	<entry name="Necrons/Monolith:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Monolith:Hurt#0" value="Blindaje perforado, tripulación dañada."/>
	<entry name="Necrons/Monolith:HurtCount" value="1"/>
	<entry name="Necrons/Monolith:Idle#0" value="Canalizando Puerta Eterna."/>
	<entry name="Necrons/Monolith:Idle#1" value="Generando secretos inescrutables."/>
	<entry name="Necrons/Monolith:Idle#2" value="Descomponiendo Valores Singulares."/>
 	<entry name="Necrons/Monolith:Idle#3" value="Reconstruyendo Partículas Fundamentales."/>
 	<entry name="Necrons/Monolith:Idle#4" value="Desbancando Memorias del Arca."/>
	<entry name="Necrons/Monolith:Idle#5" value="Reticulación de engranajes."/>
	<entry name="Necrons/Monolith:IdleCount" value="6"/>
	<entry name="Necrons/Monolith:Shaken#0" value="Bajo ataque. Comprometido."/>
	<entry name="Necrons/Monolith:ShakenCount" value="1"/>
	<entry name="Necrons/Monolith:Victory#0" value="Enemigo eliminado. Conforme lo esperado."/>
	<entry name="Necrons/Monolith:VictoryCount" value="1"/>
	<entry name="Necrons/NightScythe" value="Necrons/DoomScythe"/>
	<entry name="Necrons/Obelisk" value="Necrons/Monolith"/>
	<entry name="Necrons/TesseractVault:Attack#0" value="¡Siente la furia de un Dios estelar!"/>
	<entry name="Necrons/TesseractVault:AttackCount" value="1"/>
	<entry name="Necrons/TesseractVault:Broken#0" value="¡Soy casi libre!"/>
	<entry name="Necrons/TesseractVault:BrokenCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:Necrons#0" value="¿Me pones en contra de los Necrones? Criaturas estúpidas."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:Orks#0" value="Criaturas despreciables."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/TesseractVault:EnemyFaction:SpaceMarines#0" value="Sus vidas son mías."/>
	<entry name="Necrons/TesseractVault:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/TesseractVault:Hurt#0" value="Mis débiles prisioneros…"/>
	<entry name="Necrons/TesseractVault:HurtCount" value="1"/>
	<entry name="Necrons/TesseractVault:Idle#0" value="Centinelas, escarabajos, sanguijuelas… ¿todo esto para mí?"/>
	<entry name="Necrons/TesseractVault:Idle#1" value="Oh, ¿Me liberas?"/>
	<entry name="Necrons/TesseractVault:Idle#2" value="Esta prisión NO PUEDE RETENERME."/>
	<entry name="Necrons/TesseractVault:Idle#3" value="Estoy tan… empequeñecido. ¡Yo era un Dios estelar!"/>
	<entry name="Necrons/TesseractVault:Idle#4" value="Soy un Dios estelar. Déjame hacer volar tu imaginación."/>
	<entry name="Necrons/TesseractVault:IdleCount" value="5"/>
	<entry name="Necrons/TesseractVault:Shaken#0" value="Si, destruye mi prisión…"/>
	<entry name="Necrons/TesseractVault:ShakenCount" value="1"/>
	<entry name="Necrons/TesseractVault:Victory#0" value="No es suficiente. No hasta que toda la vida sea mía."/>
	<entry name="Necrons/TesseractVault:VictoryCount" value="1"/>
	<entry name="Necrons/TombBlade:Attack#0" value="Asalto a velocidad."/>
	<entry name="Necrons/TombBlade:AttackCount" value="1"/>
	<entry name="Necrons/TombBlade:Broken#0" value="Dispersados, retrocediendo."/>
	<entry name="Necrons/TombBlade:BrokenCount" value="1"/>
	<entry name="Necrons/TombBlade:Hurt#0" value="Fuego de bajo nivel, guerreros caídos."/>
	<entry name="Necrons/TombBlade:HurtCount" value="1"/>
	<entry name="Necrons/TombBlade:Idle#0" value="Nunca tranquilo."/>
	<entry name="Necrons/TombBlade:Idle#1" value="Programando patrones de ataque."/>
	<entry name="Necrons/TombBlade:Idle#2" value="Espacio… lo recordamos."/>
	<entry name="Necrons/TombBlade:Idle#3" value="Probando repulsores dimensionales."/>
	<entry name="Necrons/TombBlade:IdleCount" value="4"/>
	<entry name="Necrons/TombBlade:Shaken#0" value="Alcanzado. Increíblemente hemos sido alcanzados."/>
	<entry name="Necrons/TombBlade:ShakenCount" value="1"/>
	<entry name="Necrons/TombBlade:Victory#0" value="Enemigo… desintegrado."/>
	<entry name="Necrons/TombBlade:VictoryCount" value="1"/>
	<entry name="Necrons/TranscendentCtan" value="Necrons/TesseractVault"/>
	<entry name="Necrons/TriarchPraetorian:Attack#0" value="La primera ley es… no plantes cara a la Triarca."/>
	<entry name="Necrons/TriarchPraetorian:AttackCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Broken#0" value="La última ley es… deja de luchar."/>
	<entry name="Necrons/TriarchPraetorian:BrokenCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:AstraMilitarum#0" value="Ah, primitivos. Una vez fuimos sus dioses."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:Necrons#0" value="¿Otra corte? Deben ser alineados."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:Orks#0" value="Indomesticables, salvajes."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:SpaceMarines#0" value="Honorables enemigos. Respetuosamente, debemos matarlos."/>
	<entry name="Necrons/TriarchPraetorian:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Hurt#0" value="¡Nosotros somos la ley!"/>
	<entry name="Necrons/TriarchPraetorian:HurtCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Idle#0" value="¿Sabes los códigos de la ley?"/>
	<entry name="Necrons/TriarchPraetorian:Idle#1" value="El primer código es el honor."/>
	<entry name="Necrons/TriarchPraetorian:Idle#2" value="El segundo código es el orgullo."/>
	<entry name="Necrons/TriarchPraetorian:Idle#3" value="El tercer código… ¿debería compartir este dato?"/>
	<entry name="Necrons/TriarchPraetorian:Idle#4" value="El código quincuagésimo séptimo… obedecer todos los códigos."/>
	<entry name="Necrons/TriarchPraetorian:IdleCount" value="5"/>
	<entry name="Necrons/TriarchPraetorian:Shaken#0" value="La resurrección es tan… costosa."/>
	<entry name="Necrons/TriarchPraetorian:ShakenCount" value="1"/>
	<entry name="Necrons/TriarchPraetorian:Victory#0" value="No obtenemos ningún placer de esto."/>
	<entry name="Necrons/TriarchPraetorian:VictoryCount" value="1"/>
	<entry name="Necrons/TriarchStalker" value="Necrons/TriarchPraetorian"/>
	<entry name="Necrons/Warrior:Artefact#0" value="Horrores ancestrales—en nuestro mundo corona? ¿Cómo se atreven?"/>
	<entry name="Necrons/Warrior:ArtefactCount" value="1"/>
	<entry name="Necrons/Warrior:Attack#0" value="¿Te duele, que te vayan arrancando los nervios, átomo a átomo?"/>
	<entry name="Necrons/Warrior:Attack#1" value="Atacando."/>
	<entry name="Necrons/Warrior:Attack#2" value="La historia los olvidará."/>
	<entry name="Necrons/Warrior:Attack#3" value="La resistencia es… molesta."/>
	<entry name="Necrons/Warrior:AttackCount" value="4"/>
	<entry name="Necrons/Warrior:Broken#0" value="Simulación de daño al 99%."/>
	<entry name="Necrons/Warrior:Broken#1" value="Nuestra unidad está dispersa. Nos retiramos."/>
	<entry name="Necrons/Warrior:Broken#2" value="Iniciado protocolo de retirada."/>
	<entry name="Necrons/Warrior:Broken#3" value="La tumba nos llama y nosotros obedecemos."/>
	<entry name="Necrons/Warrior:Broken#4" value="Tus ordenes son imposibles, mi Señor."/>
	<entry name="Necrons/Warrior:BrokenCount" value="5"/>
	<entry name="Necrons/Warrior:Cover#0" value="Haciendo uso de la cobertura."/>
	<entry name="Necrons/Warrior:CoverCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:AstraMilitarum#0" value="Débiles, cosas carnosas pero una buena raza esclava."/>
	<entry name="Necrons/Warrior:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:Necrons#0" value="Nuestros primos, por error."/>
	<entry name="Necrons/Warrior:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:Orks#0" value="Bioarma insolente. Brutalmente efectiva."/>
	<entry name="Necrons/Warrior:EnemyFaction:OrksCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyFaction:SpaceMarines#0" value="Carne, rehecha a nuestra forma. No es algo con lo que se pueda jugar."/>
	<entry name="Necrons/Warrior:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/CatachanDevil#0" value="Orgánico. insectoide, gigante. ¿Posible percusor de los tiránidos?"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/Enslaver#0" value="Un efecto colateral de los Ancestros? Los sensores son solo un 30% efectivos."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KastelanRobot#0" value="¿Intentan los humanos imitarnos? Que copias más decepcionantes."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="1"/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="¡Sensores fallando! Desconocido desconocido desconocido."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/Psychneuein#0" value="Megafauna. Una amenaza solo para las razas orgánicas."/>
	<entry name="Necrons/Warrior:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="Necrons/Warrior:Hurt#0" value="El daño excede lo esperado… ¿protocolos de retirada?"/>
	<entry name="Necrons/Warrior:HurtCount" value="1"/>
	<entry name="Necrons/Warrior:Idle#0" value="Todos los sistemas son operacionales."/>
	<entry name="Necrons/Warrior:Idle#1" value="Durante todo este tiempo… maldice al Embaucador."/>
	<entry name="Necrons/Warrior:Idle#2" value="Protocolo de espera iniciado."/>
	<entry name="Necrons/Warrior:Idle#3" value="Apagando la potencia."/>
	<entry name="Necrons/Warrior:Idle#4" value="Este cuerpo metálico… alienígena."/>
	<entry name="Necrons/Warrior:Idle#5" value="La máquina es fuerte, la carne ha desaparecido."/>
	<entry name="Necrons/Warrior:Idle#6" value="Hemos esperado milenios… esperaremos más milenios."/>
	<entry name="Necrons/Warrior:IdleCount" value="7"/>
	<entry name="Necrons/Warrior:QuestStory0#0" value="Buscando señales de los Ancestrales."/>
	<entry name="Necrons/Warrior:QuestStory0Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory1#0" value="¿Pretorianos y Criptecnólogos? Nuestras fuerzas crecen."/>
	<entry name="Necrons/Warrior:QuestStory1Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory2#0" value="Los carentes de inteligencia deberían ser compadecidos pro la maldición de C'tan."/>
	<entry name="Necrons/Warrior:QuestStory2Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory3#0" value="¿Perseguimos esos adornos ancestrales? Si… mi Señor."/>
	<entry name="Necrons/Warrior:QuestStory3Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory4#0" value="¿Dónde están los pretorianos?"/>
	<entry name="Necrons/Warrior:QuestStory4Count" value="1"/>
	<entry name="Necrons/Warrior:QuestStory5#0" value="¿Mephet'ran? ¡Emisario! ¡Embaucador! ¡Ladrón de almas!"/>
	<entry name="Necrons/Warrior:QuestStory5Count" value="1"/>
	<entry name="Necrons/Warrior:Shaken#0" value="Una raza inferior huiría."/>
	<entry name="Necrons/Warrior:Shaken#1" value="Las Triarcas no nos informaron sobre esto."/>
	<entry name="Necrons/Warrior:Shaken#2" value="Esto no es posible. ¿Estamos… perdiendo?"/>
	<entry name="Necrons/Warrior:Shaken#3" value="Estamos más allá del temor."/>
	<entry name="Necrons/Warrior:ShakenCount" value="4"/>
	<entry name="Necrons/Warrior:Victory#0" value="Perdidas aceptables. Sus perdidas."/>
	<entry name="Necrons/Warrior:Victory#1" value="Como se predijo, victoriosos."/>
	<entry name="Necrons/Warrior:Victory#2" value="Eliminado."/>
	<entry name="Necrons/Warrior:Victory#3" value="Por el Rey Silente y por mi Señor."/>
	<entry name="Necrons/Warrior:Victory#4" value="Mi Señor, el enemigo es débil."/>
	<entry name="Necrons/Warrior:Victory#5" value="Los Ancestrales engredaron tal carne débil."/>
	<entry name="Necrons/Warrior:Victory#6" value="Ellos deben conocer los necrones y conocer el temor."/>
	<entry name="Necrons/Warrior:Victory#7" value="Ganar tan fácilmente es tan… insatisfactorio."/>
	<entry name="Necrons/Warrior:VictoryCount" value="8"/>
	<entry name="Neutral/Ambull:Attack#0" value="Roekoe!"/>
	<entry name="Neutral/Ambull:AttackCount" value="1"/>
	<entry name="Neutral/Ambull:Broken#0" value="Kurr."/>
	<entry name="Neutral/Ambull:BrokenCount" value="1"/>
	<entry name="Neutral/Ambull:Hurt#0" value="Rou rou."/>
	<entry name="Neutral/Ambull:HurtCount" value="1"/>
	<entry name="Neutral/Ambull:Idle#0" value="Guru Guru"/>
	<entry name="Neutral/Ambull:Idle#1" value="Grl Grl"/>
	<entry name="Neutral/Ambull:Idle#2" value="Curcuurucu"/>
	<entry name="Neutral/Ambull:Idle#3" value="Oo ho oo ho"/>
	<entry name="Neutral/Ambull:IdleCount" value="4"/>
	<entry name="Neutral/Ambull:Shaken#0" value="Burukk"/>
	<entry name="Neutral/Ambull:ShakenCount" value="1"/>
	<entry name="Neutral/Ambull:Victory#0" value="Uuu"/>
	<entry name="Neutral/Ambull:VictoryCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Attack#0" value="KYKYLIKY…"/>
	<entry name="Neutral/CatachanDevil:AttackCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Broken#0" value="KUKELEKU"/>
	<entry name="Neutral/CatachanDevil:BrokenCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Hurt#0" value="CHICHIRICHI"/>
	<entry name="Neutral/CatachanDevil:HurtCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Idle#0" value="ke-kok-o"/>
	<entry name="Neutral/CatachanDevil:Idle#1" value="kok-ko-o"/>
	<entry name="Neutral/CatachanDevil:Idle#2" value="ko-ko-o"/>
	<entry name="Neutral/CatachanDevil:Idle#3" value="ko-ke-koko"/>
	<entry name="Neutral/CatachanDevil:IdleCount" value="4"/>
	<entry name="Neutral/CatachanDevil:Shaken#0" value="¡Ch-ch-ch!"/>
	<entry name="Neutral/CatachanDevil:ShakenCount" value="1"/>
	<entry name="Neutral/CatachanDevil:Victory#0" value="Kckeliku!"/>
	<entry name="Neutral/CatachanDevil:VictoryCount" value="1"/>
	<entry name="Neutral/Enslaver:Attack#0" value="Heiiii…"/>
	<entry name="Neutral/Enslaver:AttackCount" value="1"/>
	<entry name="Neutral/Enslaver:Broken#0" value="Oummm"/>
	<entry name="Neutral/Enslaver:BrokenCount" value="1"/>
	<entry name="Neutral/Enslaver:Hurt#0" value="Harrhm"/>
	<entry name="Neutral/Enslaver:HurtCount" value="1"/>
	<entry name="Neutral/Enslaver:Idle#0" value="HmmMmm."/>
	<entry name="Neutral/Enslaver:Idle#1" value="Mmmhmmm."/>
	<entry name="Neutral/Enslaver:Idle#2" value="Tct tct."/>
	<entry name="Neutral/Enslaver:Idle#3" value="Chhrrr."/>
	<entry name="Neutral/Enslaver:IdleCount" value="4"/>
	<entry name="Neutral/Enslaver:Shaken#0" value="Hrhrhrh"/>
	<entry name="Neutral/Enslaver:ShakenCount" value="1"/>
	<entry name="Neutral/Enslaver:Victory#0" value="mmmMMM!"/>
	<entry name="Neutral/Enslaver:VictoryCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Attack#0" value="Objetivo mapeado, Datasmith"/>
	<entry name="Neutral/KastelanRobot:AttackCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Broken#0" value="Ordenes incompletas, siguiendo protocolo."/>
	<entry name="Neutral/KastelanRobot:BrokenCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Hurt#0" value="Reparaciones requeridas, Datasmith. ¿Entrada?"/>
	<entry name="Neutral/KastelanRobot:HurtCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Idle#0" value="¿Entrada?"/>
	<entry name="Neutral/KastelanRobot:Idle#1" value="¿Datos no encontrados?"/>
	<entry name="Neutral/KastelanRobot:Idle#2" value="Protocolos de parada en 5,4,3-"/>
	<entry name="Neutral/KastelanRobot:Idle#3" value="¡Vigilancia!"/>
	<entry name="Neutral/KastelanRobot:IdleCount" value="4"/>
	<entry name="Neutral/KastelanRobot:Shaken#0" value="¡Dato inconsistente detectado!"/>
	<entry name="Neutral/KastelanRobot:ShakenCount" value="1"/>
	<entry name="Neutral/KastelanRobot:Victory#0" value="Objetivo atomizado. Dentro de los parámetros."/>
	<entry name="Neutral/KastelanRobot:VictoryCount" value="1"/>
	<entry name="Neutral/KrootHound:Attack#0" value="Hoeea!"/>
	<entry name="Neutral/KrootHound:Attack#1" value="Hrrrr!"/>
	<entry name="Neutral/KrootHound:AttackCount" value="2"/>
	<entry name="Neutral/KrootHound:Broken#0" value="owoooo"/>
	<entry name="Neutral/KrootHound:BrokenCount" value="1"/>
	<entry name="Neutral/KrootHound:Hurt#0" value="uhuu"/>
	<entry name="Neutral/KrootHound:HurtCount" value="1"/>
	<entry name="Neutral/KrootHound:Idle#0" value="oou"/>
	<entry name="Neutral/KrootHound:Idle#1" value="cha-cha"/>
	<entry name="Neutral/KrootHound:Idle#2" value="oauh ouah"/>
	<entry name="Neutral/KrootHound:Idle#3" value="guf guf"/>
	<entry name="Neutral/KrootHound:IdleCount" value="4"/>
	<entry name="Neutral/KrootHound:Shaken#0" value="yu-yu-yuuu"/>
	<entry name="Neutral/KrootHound:ShakenCount" value="1"/>
	<entry name="Neutral/KrootHound:Victory#0" value="Oooahh!"/>
	<entry name="Neutral/KrootHound:VictoryCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Attack#0" value="SANGRE PARA EL DIOS DE LA SANGRE"/>
	<entry name="Neutral/LordOfSkulls:AttackCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Broken#0" value="SEÑOR, TE HE FALLADO"/>
	<entry name="Neutral/LordOfSkulls:BrokenCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Hurt#0" value="MI SANGRE PARA MI DIOS"/>
	<entry name="Neutral/LordOfSkulls:HurtCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Idle#0" value="DESGARRAR KA MUERTE"/>
	<entry name="Neutral/LordOfSkulls:Idle#1" value="MATAR MATAR MATAR"/>
	<entry name="Neutral/LordOfSkulls:Idle#2" value="CRÁNEOS ESPINAS GRITOS"/>
	<entry name="Neutral/LordOfSkulls:Idle#3" value="AHOGARSE EN SANGRE"/>
	<entry name="Neutral/LordOfSkulls:Idle#4" value="DEBEMOS… MATAR"/>
	<entry name="Neutral/LordOfSkulls:IdleCount" value="5"/>
	<entry name="Neutral/LordOfSkulls:Shaken#0" value="¡NO CONOZCO EL MIEDO!"/>
	<entry name="Neutral/LordOfSkulls:ShakenCount" value="1"/>
	<entry name="Neutral/LordOfSkulls:Victory#0" value="¡SANGRE! PARA! KHORNE!"/>
	<entry name="Neutral/LordOfSkulls:VictoryCount" value="1"/>
	<entry name="Neutral/Psychneuein:Attack#0" value="Zzzz!"/>
	<entry name="Neutral/Psychneuein:AttackCount" value="1"/>
	<entry name="Neutral/Psychneuein:Broken#0" value="Zzzee!"/>
	<entry name="Neutral/Psychneuein:BrokenCount" value="1"/>
	<entry name="Neutral/Psychneuein:Hurt#0" value="Zzzaaa."/>
	<entry name="Neutral/Psychneuein:HurtCount" value="1"/>
	<entry name="Neutral/Psychneuein:Idle#0" value="Zzzm"/>
	<entry name="Neutral/Psychneuein:Idle#1" value="Zzzth. Zzzth."/>
	<entry name="Neutral/Psychneuein:Idle#2" value="Zzzaaa. Zzzm."/>
	<entry name="Neutral/Psychneuein:Idle#3" value="Zzzthzzz."/>
	<entry name="Neutral/Psychneuein:IdleCount" value="4"/>
	<entry name="Neutral/Psychneuein:Shaken#0" value="Zzzm!"/>
	<entry name="Neutral/Psychneuein:ShakenCount" value="1"/>
	<entry name="Neutral/Psychneuein:Victory#0" value="ZZZZ!"/>
	<entry name="Neutral/Psychneuein:VictoryCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Attack#0" value="Muere para el Omnissiah!"/>
	<entry name="Neutral/TechpriestBetrayer:AttackCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Broken#0" value="Probabilidad de fallo, 100%"/>
	<entry name="Neutral/TechpriestBetrayer:BrokenCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Hurt#0" value="Funcionalidad operacional deficiente."/>
	<entry name="Neutral/TechpriestBetrayer:HurtCount" value="1"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#0" value="Probando…"/>
	<entry name="Neutral/TechpriestBetrayer:Idle#1" value="Apagándolo… encendiéndolo."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#2" value="Ungüentos aplicados."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#3" value="Servir a Servoskull… completado."/>
	<entry name="Neutral/TechpriestBetrayer:Idle#4" value="Artefacto adquirido…"/>
	<entry name="Neutral/TechpriestBetrayer:IdleCount" value="5"/>
	<entry name="Neutral/TechpriestBetrayer:Shaken#0" value="Debo sobrevivir!"/>
	<entry name="Neutral/TechpriestBetrayer:Shaken#1" value="Marte debe estar informado."/>
	<entry name="Neutral/TechpriestBetrayer:ShakenCount" value="2"/>
	<entry name="Neutral/TechpriestBetrayer:Victory#0" value="El escape es la única victoria."/>
	<entry name="Neutral/TechpriestBetrayer:VictoryCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Attack#0" value="¡Por el culto!"/>
	<entry name="Neutral/NeophyteHybrid:Attack#1" value="El Mago está al mando."/>
	<entry name="Neutral/NeophyteHybrid:Attack#2" value="El Devorador llega."/>
	<entry name="Neutral/NeophyteHybrid:AttackCount" value="3"/>
	<entry name="Neutral/NeophyteHybrid:Broken#0" value="¿Dónde está el Primus?"/>
	<entry name="Neutral/NeophyteHybrid:BrokenCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Hurt#0" value="El patriarca nos salve!"/>
	<entry name="Neutral/NeophyteHybrid:HurtCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Idle#0" value="Aquí llega, hermanos."/>
	<entry name="Neutral/NeophyteHybrid:Idle#1" value="La sombra se acerca."/>
	<entry name="Neutral/NeophyteHybrid:Idle#2" value="El Mago vaticina un gran hambre."/>
	<entry name="Neutral/NeophyteHybrid:Idle#3" value="Cuando el día llegue, caminaremos de buena gana."/>
	<entry name="Neutral/NeophyteHybrid:Idle#4" value="Está casi AQUI."/>
	<entry name="Neutral/NeophyteHybrid:IdleCount" value="5"/>
	<entry name="Neutral/NeophyteHybrid:Shaken#0" value="Somos pobre carne, sin duda."/>
	<entry name="Neutral/NeophyteHybrid:ShakenCount" value="1"/>
	<entry name="Neutral/NeophyteHybrid:Victory#0" value="¡Somo carne para la Reina que viene!"/>
	<entry name="Neutral/NeophyteHybrid:VictoryCount" value="1"/>
	<entry name="Neutral/Poxwalker:Attack#0" value="Jeejee."/>
	<entry name="Neutral/Poxwalker:Attack#1" value="Urrnnnn…"/>
	<entry name="Neutral/Poxwalker:Attack#2" value="Harrrr…"/>
	<entry name="Neutral/Poxwalker:Attack#3" value="Nurg Elll…"/>
	<entry name="Neutral/Poxwalker:AttackCount" value="4"/>
	<entry name="Neutral/Poxwalker:Broken#0" value="Padre…"/>
	<entry name="Neutral/Poxwalker:BrokenCount" value="1"/>
	<entry name="Neutral/Poxwalker:Hurt#0" value="Cayendo…"/>
	<entry name="Neutral/Poxwalker:HurtCount" value="1"/>
	<entry name="Neutral/Poxwalker:Idle#0" value="…almas…atrapadas…"/>
	<entry name="Neutral/Poxwalker:Idle#1" value="…Dios Plaga…"/>
	<entry name="Neutral/Poxwalker:Idle#2" value="…Padre…"/>
	<entry name="Neutral/Poxwalker:Idle#3" value="…Unhhhh…"/>
	<entry name="Neutral/Poxwalker:IdleCount" value="4"/>
	<entry name="Neutral/Poxwalker:Shaken#0" value="…nnnaaaa…"/>
	<entry name="Neutral/Poxwalker:ShakenCount" value="1"/>
	<entry name="Neutral/Poxwalker:Victory#0" value="…infectado…"/>
	<entry name="Neutral/Poxwalker:VictoryCount" value="1"/>	
	<entry name="Orks/Battlewagon:Attack#0" value="Woo, dakka, whee!"/>
	<entry name="Orks/Battlewagon:Attack#1" value="Red wunz 'it arder!"/>
	<entry name="Orks/Battlewagon:Attack#2" value="Arrollazlos!"/>
	<entry name="Orks/Battlewagon:Attack#3" value="¡Dizparando a lo ke ze mueve!"/>
	<entry name="Orks/Battlewagon:AttackCount" value="4"/>
	<entry name="Orks/Battlewagon:Broken#0" value="¡Gira ezta cosa al revez!"/>
	<entry name="Orks/Battlewagon:Broken#1" value="¡Zog it! ¡Mueve ezta coza!"/>
	<entry name="Orks/Battlewagon:BrokenCount" value="2"/>
	<entry name="Orks/Battlewagon:EnemyFaction:AstraMilitarum#0" value="¡Oomies! ¡Dizparad a todo!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:Necrons#0" value="¡Desguazad ezas lataz!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:Orks#0" value="Zomoz nozotros! ¡Ahora tenemoz una lucha dizna!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:OrksCount" value="1"/>
	<entry name="Orks/Battlewagon:EnemyFaction:SpaceMarines#0" value="¡Arrollaz a ezaz Beakies!"/>
	<entry name="Orks/Battlewagon:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="Orks/Battlewagon:Hurt#0" value="¿Ow kuantaz ruedaz ze zupone ke tiene ezta koza?"/>
	<entry name="Orks/Battlewagon:HurtCount" value="1"/>
	<entry name="Orks/Battlewagon:Idle#0" value="Y digo yo, que paza si ezto tiene doz motorez?"/>
	<entry name="Orks/Battlewagon:Idle#1" value="¿Alguien tiene pintura roja?"/>
	<entry name="Orks/Battlewagon:Idle#2" value="¡Nezesitamoz ir máz rápido, jefe! MAZ RAPIDO!"/>
	<entry name="Orks/Battlewagon:Idle#3" value="Toot-toooot!"/>
	<entry name="Orks/Battlewagon:IdleCount" value="4"/>
	<entry name="Orks/Battlewagon:Shaken#0" value="¿Ezta koza no tiene muellez?"/>
	<entry name="Orks/Battlewagon:Shaken#1" value="¡Squig suckin grot munchers!"/>
	<entry name="Orks/Battlewagon:Shaken#2" value="¡Noz eztán dizparando!"/>
	<entry name="Orks/Battlewagon:ShakenCount" value="3"/>
	<entry name="Orks/Battlewagon:Victory#0" value="¡WAAAGH! ¡Orkoz!"/>
	<entry name="Orks/Battlewagon:VictoryCount" value="1"/>
	<entry name="Orks/Boy:Attack#0" value="¡Dadlez máz fuerte!"/>
	<entry name="Orks/Boy:Attack#1" value="¡Dakka, dakka, dakka!"/>
	<entry name="Orks/Boy:Attack#2" value="¡Hur-hur-hur, ezto ez vida!"/>
	<entry name="Orks/Boy:Attack#3" value="¡WAAAGH!"/>
	<entry name="Orks/Boy:Attack#4" value="¡Dónde eztá la luchat?"/>
	<entry name="Orks/Boy:AttackCount" value="5"/>
	<entry name="Orks/Boy:Broken#0" value="¡AAAWGH! Eztoy fuera."/>
	<entry name="Orks/Boy:Broken#1" value="¡Kada orko ze teme a zi mizmo!"/>
	<entry name="Orks/Boy:Broken#2" value="He dejado un garrapato kozinandoze… ¡volveré pronto!"/>
	<entry name="Orks/Boy:Broken#3" value="Naw… naw, naw, naw."/>
	<entry name="Orks/Boy:Broken#4" value="¡KorrekorreKORRE!"/>
	<entry name="Orks/Boy:BrokenCount" value="5"/>
	<entry name="Orks/Boy:EnemyFaction:AstraMilitarum#0" value="Oomies! Elloz tienen grandez kozas para kortar."/>
	<entry name="Orks/Boy:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="Orks/Boy:EnemyFaction:Necrons#0" value="Kozaz lata. ¡Eztaz kozaz no ze eztán muertaz!"/>
	<entry name="Orks/Boy:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Orks/Boy:EnemyFaction:Orks#0" value="¡Aw, otro WAAAGH! Ahora zabemoz ke va'ber una gran batalla."/>
	<entry name="Orks/Boy:EnemyFaction:OrksCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/CatachanDevil#0" value="'¿Ow muchaz pataz? Hay buena comilona aí."/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/Enslaver#0" value="Oh, yo azuztado. ¡Mantente alejado!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KastelanRobot#0" value="¡Yawn! ¿Donde ezta la diversión en luchar kontra hombrez d'hojalata?"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="¿Oh, eztos apeztozoz oomies eztán allí? ¡Vamoz a tener una buena lucha!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/Psychneuein#0" value="¡Kozaz zumbantes! Wotchit, ze meten dentro y entonzez—POP!"/>
	<entry name="Orks/Boy:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="Orks/Boy:Hurt#0" value="Ezpera, mi zerebro ezta chorreando. ¿Alguién tiene martillo y klavoz?"/>
	<entry name="Orks/Boy:Hurt#1" value="¡Él ezta muerto! Hur hur. Bagsy eztá frito"/>
	<entry name="Orks/Boy:Hurt#2" value="¿Ez ezo mi brazo?"/>
	<entry name="Orks/Boy:Hurt#3" value="¡Akí tenemoz a los tioz máz fuertez!"/>
	<entry name="Orks/Boy:HurtCount" value="4"/>
	<entry name="Orks/Boy:Idle#0" value="¡Erez el jefe! ¿Ke kierez, jefe?"/>
	<entry name="Orks/Boy:Idle#1" value="Más kañonez zignifika máz dakka. ¡Máz dakka zignifika máz diversión!"/>
	<entry name="Orks/Boy:Idle#2" value="Nah, tío, ezte e mi garrapato. ¡Buzkate el tuyo!"/>
	<entry name="Orks/Boy:Idle#3" value="Oi jefe, ¿ke paza?"/>
	<entry name="Orks/Boy:Idle#4" value="Un garrapato verde zentado en la'paré. Doz…"/>
	<entry name="Orks/Boy:Idle#5" value="¡Ezta rebanadora no ez sufizientemente grande!"/>
	<entry name="Orks/Boy:Idle#6" value="¿Donde ezta la pelea?"/>
	<entry name="Orks/Boy:Idle#7" value="¿Kién me dize que lo haga?"/>
	<entry name="Orks/Boy:Idle#8" value="¿Ké ez ezto?"/>
	<entry name="Orks/Boy:IdleCount" value="9"/>
	<entry name="Orks/Boy:QuestStory0#0" value="Zogging Gork, ez un jefe aburriiiiiiiiio. ¿Tienez algo para ezo?"/>
	<entry name="Orks/Boy:QuestStory0Count" value="1"/>
	<entry name="Orks/Boy:QuestStory1#0" value="Mantén a eze eztrambotiko alejado de nozotroz, no kiero que nueztraz kabezas hagan pop."/>
	<entry name="Orks/Boy:QuestStory1Count" value="1"/>
	<entry name="Orks/Boy:QuestStory2#0" value="¿Kién vive debajo de la'tierra? No hay nada kon lo ke luchar aí abajo."/>
	<entry name="Orks/Boy:QuestStory2Count" value="1"/>
	<entry name="Orks/Boy:QuestStory3#0" value="¿Kierez ke agarremoz toda eza bazura? Zin problema, jefe."/>
	<entry name="Orks/Boy:QuestStory3Count" value="1"/>
	<entry name="Orks/Boy:QuestStory4#0" value="Vamoz a pintar ezta ziudad de rojo, Jefe, hur-hur-hur."/>
	<entry name="Orks/Boy:QuestStory4Count" value="1"/>
	<entry name="Orks/Boy:QuestStory5#0" value="Yo nunka he podido comer garrapatoz, Jefe, todo eze pelo ze me keda enganchado en loz dientez."/>
	<entry name="Orks/Boy:QuestStory5Count" value="1"/>
	<entry name="Orks/Boy:Shaken#0" value="Lo he parado yo, Jefe."/>
	<entry name="Orks/Boy:Shaken#1" value="Nezezito máz coraje de zerveza de funguz."/>
	<entry name="Orks/Boy:Shaken#2" value="¡No zoy Makari, kiero vivir! ¡Tengo kozaz pa'luchar!"/>
	<entry name="Orks/Boy:Shaken#3" value="Ezto no ez Orko."/>
	<entry name="Orks/Boy:ShakenCount" value="4"/>
	<entry name="Orks/Boy:Victory#0" value="¿'Ow Ezto pareze la muerte metálika?"/>
	<entry name="Orks/Boy:Victory#1" value="Laz kozaz no orkaz ze rompen fazilmente."/>
	<entry name="Orks/Boy:Victory#2" value="Fuego, fuego, fuego, BOOM. YEAH!"/>
	<entry name="Orks/Boy:Victory#3" value="Hur hur hur!"/>
	<entry name="Orks/Boy:Victory#4" value="¿No ezperabas ezto, verdá?"/>
	<entry name="Orks/Boy:Victory#5" value="¡Oi, volvez! ¡No lo hemoz pizoteado aún!"/>
	<entry name="Orks/Boy:Victory#6" value="¿Loz orkoz han zido hechoz para doz kozaz! ¡Luchar, ganar y kontar!"/>
	<entry name="Orks/Boy:Victory#7" value="PIZOTEA. PIZOTEA. PIZOTEA."/>
	<entry name="Orks/Boy:Victory#8" value="Eztoz tíoz eztán rotoz, Jefe. ¿Algo máz kon lo ke luchar?"/>
	<entry name="Orks/Boy:VictoryCount" value="9"/>
	<entry name="Orks/BurnaBommer:Attack#0" value="¡Vamoz a hazerlo al vuelo, Jefe!"/>
	<entry name="Orks/BurnaBommer:AttackCount" value="1"/>
	<entry name="Orks/BurnaBommer:Broken#0" value="¡Apuntar el zog allí!"/>
	<entry name="Orks/BurnaBommer:BrokenCount" value="1"/>
	<entry name="Orks/BurnaBommer:Hurt#0" value="¿Agujeroz grandez lo hazen máz volador, verdá?"/>
	<entry name="Orks/BurnaBommer:HurtCount" value="1"/>
	<entry name="Orks/BurnaBommer:Idle#0" value="Vamoz dando vueltaz, y vueltaz y vueltaz…"/>
	<entry name="Orks/BurnaBommer:Idle#1" value="Vamoz arriba arriba arriba y abajo abajo abajo…"/>
	<entry name="Orks/BurnaBommer:Idle#2" value="Eztoz mágnifikoz mekánikos en eztaz mákinaz voladoraz…"/>
	<entry name="Orks/BurnaBommer:Idle#3" value="¿Kemar o bombardear, ezta ez la kuestión?"/>
	<entry name="Orks/BurnaBommer:IdleCount" value="4"/>
	<entry name="Orks/BurnaBommer:Shaken#0" value="¡Zog, danoz una oportunidad!"/>
	<entry name="Orks/BurnaBommer:ShakenCount" value="1"/>
	<entry name="Orks/BurnaBommer:Victory#0" value="¡WAAAGH! ¡COMEDLO!"/>
	<entry name="Orks/BurnaBommer:VictoryCount" value="1"/>
	<entry name="Orks/Dakkajet" value="Orks/BurnaBommer"/>
	<entry name="Orks/DeffDread:Attack#0" value="¡Pizando y Matando!"/>
	<entry name="Orks/DeffDread:Attack#1" value="¡Ezto ha merezido la operazión!"/>
	<entry name="Orks/DeffDread:Attack#2" value="¡Ooh, tengo Kohetez!"/>
	<entry name="Orks/DeffDread:Attack#3" value="¡Dizparando todo!"/>
	<entry name="Orks/DeffDread:AttackCount" value="4"/>
	<entry name="Orks/DeffDread:Broken#0" value="¡Korred!"/>
	<entry name="Orks/DeffDread:BrokenCount" value="1"/>
	<entry name="Orks/DeffDread:Hurt#0" value="Kreo ke tengo un agujero."/>
	<entry name="Orks/DeffDread:HurtCount" value="1"/>
	<entry name="Orks/DeffDread:Idle#0" value="¡Hay un Garrapato akí!"/>
	<entry name="Orks/DeffDread:Idle#1" value="¡Alguien, ke me razke la nariz! ¡Me eztá volviendo loko!"/>
	<entry name="Orks/DeffDread:Idle#2" value="¿Para ké zon todoz ezoz kablez?"/>
	<entry name="Orks/DeffDread:Idle#3" value="Abuuurriiiidooo."/>
	<entry name="Orks/DeffDread:IdleCount" value="4"/>
	<entry name="Orks/DeffDread:Shaken#0" value="¿Kómo te ezkondez en ezta koza?"/>
	<entry name="Orks/DeffDread:ShakenCount" value="1"/>
	<entry name="Orks/DeffDread:Victory#0" value="¡WAAAGH! ¡Loz Dreadnought Orkoz loz mejorez de loz mejorez!"/>
	<entry name="Orks/DeffDread:VictoryCount" value="1"/>	
	<entry name="Orks/Deffkopta:Attack#0" value="¡Ezto ez una rebanadora!"/>
	<entry name="Orks/Deffkopta:AttackCount" value="1"/>
	<entry name="Orks/Deffkopta:Broken#0" value="Oh, me eztoy mareando…"/>
	<entry name="Orks/Deffkopta:BrokenCount" value="1"/>
	<entry name="Orks/Deffkopta:Hurt#0" value="¡Oi, ven aki ke pueda golpearte!"/>
	<entry name="Orks/Deffkopta:HurtCount" value="1"/>
	<entry name="Orks/Deffkopta:Idle#0" value="Me enkanta el olor a kemado por la mañana."/>
	<entry name="Orks/Deffkopta:Idle#1" value="¿Deberíamos luchar o deberíamos luchar?"/>
	<entry name="Orks/Deffkopta:Idle#2" value="Los T'au no surfean"/>
	<entry name="Orks/Deffkopta:Idle#3" value="¿Me pregunto zi ezta koza puede zubir volando para abajo?"/>
	<entry name="Orks/Deffkopta:IdleCount" value="4"/>
	<entry name="Orks/Deffkopta:Shaken#0" value="¿Ow muchoz paloz hazen ke ezto ezte mejor arriba? Uh."/>
	<entry name="Orks/Deffkopta:ShakenCount" value="1"/>
	<entry name="Orks/Deffkopta:Victory#0" value="Algçun día ezta guerra terminará…"/>
	<entry name="Orks/Deffkopta:VictoryCount" value="1"/>
	<entry name="Orks/FlashGitz" value="Orks/Boy"/>
	<entry name="Orks/GargantuanSquiggoth:Attack#0" value="¡Por ezte kamino, garrapatos, whoah!"/>
	<entry name="Orks/GargantuanSquiggoth:AttackCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Broken#0" value="Naw, eztúpido garrapato, el otro kamino."/>
	<entry name="Orks/GargantuanSquiggoth:BrokenCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Hurt#0" value="No se lo digaz zi ze muere."/>
	<entry name="Orks/GargantuanSquiggoth:HurtCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Idle#0" value="'Ere, garrapto, dezentierrate."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#1" value="Buen garrapato."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#2" value="Garrapato malo. ¡Haz ezcupido a Udgrub! ¡MALO!."/>
	<entry name="Orks/GargantuanSquiggoth:Idle#3" value="¡Urp! No puedo zuperar a un buen devoracaraz haziendo té."/>
	<entry name="Orks/GargantuanSquiggoth:IdleCount" value="4"/>
	<entry name="Orks/GargantuanSquiggoth:Shaken#0" value="Ezta bien, garrapato, zigue comiendo"/>
	<entry name="Orks/GargantuanSquiggoth:ShakenCount" value="1"/>
	<entry name="Orks/GargantuanSquiggoth:Victory#0" value="¡Garrapato bueno! ¡Hora de zenar!"/>
	<entry name="Orks/GargantuanSquiggoth:VictoryCount" value="1"/>
	<entry name="Orks/Gorkanaut:Attack#0" value="DAKKA DAKKA!"/>
	<entry name="Orks/Gorkanaut:Attack#1" value="STOMP STOMP!"/>
	<entry name="Orks/Gorkanaut:AttackCount" value="2"/>
	<entry name="Orks/Gorkanaut:Broken#0" value="Me bitz is all smashed!"/>
	<entry name="Orks/Gorkanaut:BrokenCount" value="1"/>
	<entry name="Orks/Gorkanaut:Hurt#0" value="''ey, parad de dizparar¡ ¡Zabeiz cuantoz dientez kuezta ezto?"/>
	<entry name="Orks/Gorkanaut:HurtCount" value="1"/>
	<entry name="Orks/Gorkanaut:Idle#0" value="¿Podemoz darle máz dakka? ¿Por favor?"/>
	<entry name="Orks/Gorkanaut:Idle#1" value="¡Ziento el poder zubir Gork!"/>
	<entry name="Orks/Gorkanaut:Idle#2" value="¿Zi le ponemoz máz chapaz a ezto podemoz hazer un Gargante?"/>
	<entry name="Orks/Gorkanaut:Idle#3" value="Penzaba ke había dejado mi zena aki. La zemana pasada."/>
	<entry name="Orks/Gorkanaut:IdleCount" value="4"/>
	<entry name="Orks/Gorkanaut:Shaken#0" value="¡EZO NO NOZ AZUZTA!"/>
	<entry name="Orks/Gorkanaut:ShakenCount" value="1"/>
	<entry name="Orks/Gorkanaut:Victory#0" value="¡WAAAGH! Loz chicoz lata mandan"/>
	<entry name="Orks/Gorkanaut:VictoryCount" value="1"/>
	<entry name="Orks/Headquarters:Attack#0" value="Por fin le dizparamoz a algo."/>
	<entry name="Orks/Headquarters:AttackCount" value="1"/>
	<entry name="Orks/Headquarters:Broken#0" value="¡Jefe, ayudanoz!"/>
	<entry name="Orks/Headquarters:BrokenCount" value="1"/>
	<entry name="Orks/Headquarters:Hurt#0" value="¡Dizparan a la fortaleza, Jefe!"/>
	<entry name="Orks/Headquarters:HurtCount" value="1"/>
	<entry name="Orks/Headquarters:Idle#0" value="¡Turno de guardia! ¿Dónde ezta el WAAAGH en ezto?"/>
	<entry name="Orks/Headquarters:Idle#1" value="¿Ezta el jefe akí?"/>
	<entry name="Orks/Headquarters:Idle#2" value="¡Hachaz sangrientas reportando sah!"/>
	<entry name="Orks/Headquarters:Idle#3" value="Ezkonderze detráz de murallaz no ez orko."/>
	<entry name="Orks/Headquarters:IdleCount" value="4"/>
	<entry name="Orks/Headquarters:Shaken#0" value="¡Los orkoz no corren!"/>
	<entry name="Orks/Headquarters:ShakenCount" value="1"/>
	<entry name="Orks/Headquarters:Victory#0" value="¡WAAAGH! ¡Victoria!"/>
	<entry name="Orks/Headquarters:VictoryCount" value="1"/>
	<entry name="Orks/KillaKan" value="Orks/Gorkanaut"/>
	<entry name="Orks/Meganob" value="Orks/Boy"/>
	<entry name="Orks/Mek:Attack#0" value="¡Dizparando todoz loz cañonez!"/>
	<entry name="Orks/Mek:AttackCount" value="1"/>
	<entry name="Orks/Mek:Broken#0" value="Algunaz cozaz nezezitan reparazión en eza fortaleza."/>
	<entry name="Orks/Mek:BrokenCount" value="1"/>
	<entry name="Orks/Mek:Hurt#0" value="¡Miz kampos perzonalizadoz eztan rotoz!"/>
	<entry name="Orks/Mek:HurtCount" value="1"/>
	<entry name="Orks/Mek:Idle#0" value="Pienzo ke puedo hacer algo ezpezial…"/>
	<entry name="Orks/Mek:Idle#1" value="'Ere, pazar eza coza… . Nah, ezta viejo pero me lo puedo komer…"/>
	<entry name="Orks/Mek:Idle#2" value="Imagina un akribillador de cinco cañonez. Yeahhhh."/>
	<entry name="Orks/Mek:Idle#3" value="Hmm. La lataz matadora no funziona. Nezezitamoz un kanijo más grande y tornilloz máz pekeñoz."/>
	<entry name="Orks/Mek:IdleCount" value="4"/>
	<entry name="Orks/Mek:Shaken#0" value="¡Morkz ze retuerze, eze no ez el rito!"/>
	<entry name="Orks/Mek:ShakenCount" value="1"/>
	<entry name="Orks/Mek:Victory#0" value="¡WAAAGH! ¡El zerebro mekániko venze ezte día!"/>
	<entry name="Orks/Mek:VictoryCount" value="1"/>
	<entry name="Orks/MekGun:Attack#0" value="¡Dizparando ezta piztola grande, señor orko jefe!"/>
	<entry name="Orks/MekGun:AttackCount" value="1"/>
	<entry name="Orks/MekGun:Broken#0" value="¡Cada kanijo ze teme a zi mizmo!"/>
	<entry name="Orks/MekGun:BrokenCount" value="1"/>
	<entry name="Orks/MekGun:Hurt#0" value="Máz kanijoz muertoz."/>
	<entry name="Orks/MekGun:HurtCount" value="1"/>
	<entry name="Orks/MekGun:Idle#0" value="Loz kanijoz zon loz máz inteligentez y loz mejorez."/>
	<entry name="Orks/MekGun:Idle#1" value="Lejoz de la lucha, y con una piztola grande. Lo mejor."/>
	<entry name="Orks/MekGun:Idle#2" value="¡Moki, sal del barril!"/>
	<entry name="Orks/MekGun:Idle#3" value="Mekániko apeztozo."/>
	<entry name="Orks/MekGun:IdleCount" value="4"/>
	<entry name="Orks/MekGun:Shaken#0" value="¡Deja de zentarnoz, no vamoz a salir corriendo!"/>
	<entry name="Orks/MekGun:ShakenCount" value="1"/>
	<entry name="Orks/MekGun:Victory#0" value="Loz kanijoz, kiero dezir, los orkoz zon los mejorez, verdá Jefe?"/>
	<entry name="Orks/MekGun:VictoryCount" value="1"/>
	<entry name="Orks/Painboy:Attack#0" value="¡Hee-hee, zirugía en el campo de batalla!"/>
	<entry name="Orks/Painboy:AttackCount" value="1"/>
	<entry name="Orks/Painboy:Broken#0" value="¡Matazanoz y pequeñoz snotz primero!"/>
	<entry name="Orks/Painboy:BrokenCount" value="1"/>
	<entry name="Orks/Painboy:Hurt#0" value="Zolo tengo ke volver a atornillar ezto, zujetalo."/>
	<entry name="Orks/Painboy:HurtCount" value="1"/>
	<entry name="Orks/Painboy:Idle#0" value="¿Ahora, Jefe, ke tal un gran y bonito no brazo?"/>
	<entry name="Orks/Painboy:Idle#1" value="Nezezito tuz dientez. Abre bien. ¡Dí WAAAGH!"/>
	<entry name="Orks/Painboy:Idle#2" value="¿Ke ezta 'loko', de todaz formaz?"/>
	<entry name="Orks/Painboy:Idle#3" value="Nezezito un garrapato aguja máz grande."/>
	<entry name="Orks/Painboy:IdleCount" value="4"/>
	<entry name="Orks/Painboy:Shaken#0" value="'ere, eze no ez el rito."/>
	<entry name="Orks/Painboy:ShakenCount" value="1"/>
	<entry name="Orks/Painboy:Victory#0" value="Mira todoz ezoz trozitoz… adorablez."/>
	<entry name="Orks/Painboy:VictoryCount" value="1"/>
	<entry name="Orks/Tankbusta" value="Orks/Boy"/>
	<entry name="Orks/Warboss:Attack#0" value="Ezo ezta mejor orko. ¡A mi el WAAAGH!."/>
	<entry name="Orks/Warboss:AttackCount" value="1"/>
	<entry name="Orks/Warboss:Broken#0" value="Zolo utilizaba tácticas, ezo ez todo."/>
	<entry name="Orks/Warboss:BrokenCount" value="1"/>
	<entry name="Orks/Warboss:Hurt#0" value="¿Ow, no zabes kien zoy yo?"/>
	<entry name="Orks/Warboss:HurtCount" value="1"/>
	<entry name="Orks/Warboss:Idle#0" value="Loz zoggin nezezitan hacerlo. ¡ZOG!"/>
	<entry name="Orks/Warboss:Idle#1" value="¿DONDE EZTA LA PELEA?"/>
	<entry name="Orks/Warboss:Idle#2" value="¿Garrapatoz churrazkadoz para comer OTRA VEZ?"/>
	<entry name="Orks/Warboss:Idle#3" value="¡Nezezito komer kozaz!"/>
	<entry name="Orks/Warboss:Idle#4" value="¿Haz zakado ezoz garrapatoz de mi mega armadura, kanijo?"/>
	<entry name="Orks/Warboss:IdleCount" value="5"/>
	<entry name="Orks/Warboss:Shaken#0" value="Kuanto antez salgamoz de akí, mejor."/>
	<entry name="Orks/Warboss:ShakenCount" value="1"/>
	<entry name="Orks/Warboss:Victory#0" value="¡Por zupuezto ke ganamoz, yo eztaba allí!"/>
	<entry name="Orks/Warboss:VictoryCount" value="1"/>
	<entry name="Orks/Warbuggy" value="Orks/Battlewagon"/>
	<entry name="Orks/Weirdboy:Attack#0" value="Owwwwwwww… lo hize yo."/>
	<entry name="Orks/Weirdboy:AttackCount" value="1"/>
	<entry name="Orks/Weirdboy:Broken#0" value="¡Zakame de akí!"/>
	<entry name="Orks/Weirdboy:BrokenCount" value="1"/>
	<entry name="Orks/Weirdboy:Hurt#0" value="Ez-un-popping!"/>
	<entry name="Orks/Weirdboy:HurtCount" value="1"/>
	<entry name="Orks/Weirdboy:Idle#0" value="Trankilizado."/>
	<entry name="Orks/Weirdboy:Idle#1" value="Mork habla. Gork kamina."/>
	<entry name="Orks/Weirdboy:Idle#2" value="¡La zombra en la diztorzión..!"/>
	<entry name="Orks/Weirdboy:Idle#3" value="Zerka de Gladius dezkanza el Krork y el Zezudoz, ezperando la batalla final."/>
	<entry name="Orks/Weirdboy:IdleCount" value="4"/>
	<entry name="Orks/Weirdboy:Shaken#0" value="¡Gork y Mork ze imponen bien!"/>
	<entry name="Orks/Weirdboy:ShakenCount" value="1"/>
	<entry name="Orks/Weirdboy:Victory#0" value="¡Hur-hur, WAAAGH!"/>
	<entry name="Orks/Weirdboy:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Attack#0" value="Estos no solo sanan."/>
	<entry name="SpaceMarines/Apothecary:AttackCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Broken#0" value="¡No puedo abandonar la semilla genética!"/>
	<entry name="SpaceMarines/Apothecary:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:AstraMilitarum#0" value="Más allá del desprecio."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:Necrons#0" value="Demonios sin sangre."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:Orks#0" value="¡Pielesverdes! Ojala pudiésemos hacer crecer de nuevo las extremidades como ellos."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:SpaceMarines#0" value="Hermanos renegados, pero no traidores. Recuperaré su semilla genética toda a la vez."/>
	<entry name="SpaceMarines/Apothecary:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Hurt#0" value="Remendándome a mi mismo."/>
	<entry name="SpaceMarines/Apothecary:HurtCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Idle#0" value="Apetecario a la orden."/>
	<entry name="SpaceMarines/Apothecary:Idle#1" value="Los doctores no necesitan descanso."/>
	<entry name="SpaceMarines/Apothecary:Idle#2" value="Práctica reductora."/>
	<entry name="SpaceMarines/Apothecary:Idle#3" value="El progenoide está conectado a los Betchers y a los Sus-an…"/>
	<entry name="SpaceMarines/Apothecary:IdleCount" value="4"/>
	<entry name="SpaceMarines/Apothecary:Shaken#0" value="¡Buenas noticias! No estoy muerto."/>
	<entry name="SpaceMarines/Apothecary:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Apothecary:Victory#0" value="¡Están muertos! ¡No estoy seguro de que sea la mejor forma de utilizar mi tiempo!"/>
	<entry name="SpaceMarines/Apothecary:VictoryCount" value="1"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/Captain:Attack#0" value="¡Muere feliz escoria, sabiendo que mueres a mis manos!"/>
	<entry name="SpaceMarines/Captain:Attack#1" value="Come bólter."/>
	<entry name="SpaceMarines/Captain:Attack#2" value="¡Por el Emperador!"/>
	<entry name="SpaceMarines/Captain:Attack#3" value="Te enfrentas a un capitán Marine Espacial. Ríndete ahora para una muerte rápida."/>
	<entry name="SpaceMarines/Captain:Attack#4" value="Guilliman espera."/>
	<entry name="SpaceMarines/Captain:Attack#5" value="¡Mi fe es mi escudo!"/>
	<entry name="SpaceMarines/Captain:AttackCount" value="6"/>
	<entry name="SpaceMarines/Captain:Broken#0" value="¡El Emperador nos guarde!"/>
	<entry name="SpaceMarines/Captain:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:AstraMilitarum#0" value="Guardia traidor. Trivial."/>
	<entry name="SpaceMarines/Captain:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:Necrons#0" value="¡Sacadlos de sus tumbas! ¡Machacad los horrores mecánicos!"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:Orks#0" value="¿No nos librará nunca el Emperador de esta plaga verde?"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Captain:EnemyFaction:SpaceMarines#0" value="Renegados. Marines, preparad vuestras armas, luchamos por honor."/>
	<entry name="SpaceMarines/Captain:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Captain:Hurt#0" value="¿Cómo ha traspasado esto mi armadura?"/>
	<entry name="SpaceMarines/Captain:HurtCount" value="1"/>
	<entry name="SpaceMarines/Captain:Idle#0" value="Debería estar liderando la primera línea."/>
	<entry name="SpaceMarines/Captain:Idle#1" value="Los discursos épicos no se escriben solos."/>
	<entry name="SpaceMarines/Captain:Idle#2" value="Tenemos que salvar a la humanidad de si misma."/>
	<entry name="SpaceMarines/Captain:Idle#3" value="¿Por qué aquí? ¿Por que Gladius? ¿Nos están poniendo a prueba?"/>
	<entry name="SpaceMarines/Captain:IdleCount" value="4"/>
	<entry name="SpaceMarines/Captain:Shaken#0" value="¡No me arrodillo ante nadie!"/>
	<entry name="SpaceMarines/Captain:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Captain:Victory#0" value="¡Así caen todos los enemigos del Trono Dorado!"/>
	<entry name="SpaceMarines/Captain:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Attack#0" value="¡Se bendecido por el Crozius!"/>
	<entry name="SpaceMarines/Chaplain:Attack#1" value="¡Te haré creer!"/>
	<entry name="SpaceMarines/Chaplain:Attack#2" value="¡In nomine Imperator!"/>
	<entry name="SpaceMarines/Chaplain:AttackCount" value="3"/>
	<entry name="SpaceMarines/Chaplain:Broken#0" value="¡Ni un paso atrás!"/>
	<entry name="SpaceMarines/Chaplain:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Hurt#0" value="Aunque sufra sus golpes, no caeré."/>
	<entry name="SpaceMarines/Chaplain:HurtCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Idle#0" value="El único temor a la muerte es que nuestra obra quede incompleta."/>
	<entry name="SpaceMarines/Chaplain:Idle#1" value="Dar nuestra vida por el Emperador no será en vano."/>
	<entry name="SpaceMarines/Chaplain:Idle#2" value="No habrá paz mientras nuestros enemigos vivan."/>
	<entry name="SpaceMarines/Chaplain:Idle#3" value="Escupiremos nuestros desafíos hasta el final."/>
	<entry name="SpaceMarines/Chaplain:IdleCount" value="4"/>
	<entry name="SpaceMarines/Chaplain:Shaken#0" value="Para un guerrero, el único crimen es la cobardía."/>
	<entry name="SpaceMarines/Chaplain:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Chaplain:Victory#0" value="La fe es más fuerte que el adamantio."/>
	<entry name="SpaceMarines/Chaplain:VictoryCount" value="1"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/Dreadnought:Attack#0" value="Cada muerte es fresca, incluso ahora."/>
	<entry name="SpaceMarines/Dreadnought:Attack#1" value="¡Por el Capítulo!"/>
	<entry name="SpaceMarines/Dreadnought:Attack#2" value="Hrm… en guerra otra vez."/>
	<entry name="SpaceMarines/Dreadnought:Attack#3" value="Diez mil años de guerra…"/>
	<entry name="SpaceMarines/Dreadnought:AttackCount" value="4"/>
	<entry name="SpaceMarines/Dreadnought:Broken#0" value="Después de un milenio, se cuando retirarme."/>
	<entry name="SpaceMarines/Dreadnought:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Dreadnought:Hurt#0" value="Mi sarcófago….le han dado."/>
	<entry name="SpaceMarines/Dreadnought:Hurt#1" value="¿La letanía de protección, Capellán??"/>
	<entry name="SpaceMarines/Dreadnought:HurtCount" value="2"/>
	<entry name="SpaceMarines/Dreadnought:Idle#0" value="Una tumba andante…"/>
	<entry name="SpaceMarines/Dreadnought:Idle#1" value="¿Es esta guerra realmente eterna?"/>
	<entry name="SpaceMarines/Dreadnought:Idle#2" value="A dormir… pero a soñar. Este es el problema."/>
	<entry name="SpaceMarines/Dreadnought:Idle#3" value="¿Quién me despierta?"/>
	<entry name="SpaceMarines/Dreadnought:IdleCount" value="4"/>
	<entry name="SpaceMarines/Dreadnought:Shaken#0" value="El miedo nunca envejece."/>
	<entry name="SpaceMarines/Dreadnought:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Dreadnought:Victory#0" value="La historia de nuestro capítulo crece."/>
	<entry name="SpaceMarines/Dreadnought:VictoryCount" value="1"/>
	<entry name="SpaceMarines/FortressOfRedemption" value="SpaceMarines/Headquarters"/>
	<entry name="SpaceMarines/Headquarters:Attack#0" value="¡Estrellaros contra nuestras defensas!"/>
	<entry name="SpaceMarines/Headquarters:Attack#1" value="Idiotas, atacad un baluarte Adeptus Astartes"/>
	<entry name="SpaceMarines/Headquarters:Attack#2" value="El Comandante de la fortaleza aquí. Disparando."/>
	<entry name="SpaceMarines/Headquarters:Attack#3" value="¿Por qué no intentas pasar por delante de la puerta?"/>
	<entry name="SpaceMarines/Headquarters:AttackCount" value="4"/>
	<entry name="SpaceMarines/Headquarters:Broken#0" value="Nuestro capítulo. NO. Caerá."/>
	<entry name="SpaceMarines/Headquarters:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:AstraMilitarum#0" value="Guardias en el horizonte. Hostiles."/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:Necrons#0" value="Aquí vienen lo necrones. Eliminadlos."/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:Orks#0" value="¿Pielesverdes otra vez? ¡Preparad los bólters!"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:SpaceMarines#0" value="Los exploradores están encontrándose con renegados. ¿Traidores idiotas aquí?"/>
	<entry name="SpaceMarines/Headquarters:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Hurt#0" value="La fortaleza tiene una brecha, repito la fortaleza tiene una brecha."/>
	<entry name="SpaceMarines/Headquarters:HurtCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Idle#0" value="Todo tranquilo en el frente."/>
	<entry name="SpaceMarines/Headquarters:Idle#1" value="Comandante, estamos listos para cualquier cosa."/>
	<entry name="SpaceMarines/Headquarters:Idle#2" value="Se están recitando las letanías en el Reclusium."/>
	<entry name="SpaceMarines/Headquarters:Idle#3" value="Los siervos están trabajando duro, señor."/>
	<entry name="SpaceMarines/Headquarters:IdleCount" value="4"/>
	<entry name="SpaceMarines/Headquarters:Shaken#0" value="¡Retirad todas las unidades a la fortaleza!"/>
	<entry name="SpaceMarines/Headquarters:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Headquarters:Victory#0" value="Invasores eliminados. ¿Cómo se atreven?"/>
	<entry name="SpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Attack#0" value="Apuntando a las armas."/>
	<entry name="SpaceMarines/Hunter:AttackCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Broken#0" value="Blindaje cediendo."/>
	<entry name="SpaceMarines/Hunter:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Hurt#0" value="El blindaje ha sido perforado."/>
	<entry name="SpaceMarines/Hunter:Hurt#1" value="Fuego de armas pequeñas."/>
	<entry name="SpaceMarines/Hunter:Hurt#2" value="Orugas alcanzadas."/>
	<entry name="SpaceMarines/Hunter:HurtCount" value="3"/>
	<entry name="SpaceMarines/Hunter:Idle#0" value="Los cogitadores están en orden de trabajo."/>
	<entry name="SpaceMarines/Hunter:Idle#1" value="Invocando los espíritus de la máquina."/>
	<entry name="SpaceMarines/Hunter:Idle#2" value="Llevando a cabo reparaciones."/>
	<entry name="SpaceMarines/Hunter:IdleCount" value="3"/>
	<entry name="SpaceMarines/Hunter:Shaken#0" value="La tripulación está un poco alterada, señor."/>
	<entry name="SpaceMarines/Hunter:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Hunter:Victory#0" value="Blindaje rodante."/>
	<entry name="SpaceMarines/Hunter:VictoryCount" value="1"/>
	<entry name="SpaceMarines/LandRaider" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/LandSpeeder" value="SpaceMarines/ScoutBiker"/>
	<entry name="SpaceMarines/Librarian:Attack#0" value="Déjame que contemple tu futuro… ah. Es corto."/>
	<entry name="SpaceMarines/Librarian:Attack#1" value="Tengo un regalo. Sería grosero no compartirlo…"/>
	<entry name="SpaceMarines/Librarian:Attack#2" value="Un chasquido de mis dedos… un chasquido de tus huesos."/>
	<entry name="SpaceMarines/Librarian:AttackCount" value="3"/>
	<entry name="SpaceMarines/Librarian:Broken#0" value="Puedo oír al demonio aullando en mis oídos."/>
	<entry name="SpaceMarines/Librarian:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Hurt#0" value="El dolor físico no es nada comparado con la condenación eterna."/>
	<entry name="SpaceMarines/Librarian:HurtCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Idle#0" value="Bendita en la mente demasiado pequeña para dudar."/>
	<entry name="SpaceMarines/Librarian:Idle#1" value="La disformidad no alberga temores para mi."/>
	<entry name="SpaceMarines/Librarian:Idle#2" value="Tormenta de disformidad… algunas veces el dolor es demasiado."/>
	<entry name="SpaceMarines/Librarian:Idle#3" value="Cuando esto se acaba… el Bibliotecario llama."/>
	<entry name="SpaceMarines/Librarian:IdleCount" value="4"/>
	<entry name="SpaceMarines/Librarian:Shaken#0" value="El conocimiento no tiene nada de esto…"/>
	<entry name="SpaceMarines/Librarian:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Librarian:Victory#0" value="Su caída estaba prevista."/>
	<entry name="SpaceMarines/Librarian:VictoryCount" value="1"/>
	<entry name="SpaceMarines/Predator" value="SpaceMarines/Hunter"/>	
	<entry name="SpaceMarines/PrimarisAggressor:Attack#0" value="Mi deber es mi armadura."/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#1" value="Tormenta de llamas."/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#2" value="Espera a que se acerquen."/>
	<entry name="SpaceMarines/PrimarisAggressor:Attack#3" value="¡Exploralos!"/>
	<entry name="SpaceMarines/PrimarisAggressor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisAggressor:Broken#0" value="¡Espera, espera!"/>
	<entry name="SpaceMarines/PrimarisAggressor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Hurt#0" value="¡Por su nombre!"/>
	<entry name="SpaceMarines/PrimarisAggressor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#0" value="Esperamos."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#1" value="Estoy a la altura de mi tarea."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#2" value="Fuego purificador."/>
	<entry name="SpaceMarines/PrimarisAggressor:Idle#3" value="Quemaremos a sus enemigos hasta convertirlos en cenizas."/>
	<entry name="SpaceMarines/PrimarisAggressor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisAggressor:Shaken#0" value="¡Fuego de cobertura!"/>
	<entry name="SpaceMarines/PrimarisAggressor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisAggressor:Victory#0" value="¡Purificados con la furia del Emperador!"/>
	<entry name="SpaceMarines/PrimarisAggressor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#0" value="Descarga de plasma."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#1" value="Incineradores de sobrealimentación."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#2" value="Cazadores de armaduras."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Attack#3" value="Derritiendo a sus enemigos."/>
	<entry name="SpaceMarines/PrimarisHellblaster:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Broken#0" value="Una última batalla, entonces."/>
	<entry name="SpaceMarines/PrimarisHellblaster:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Hurt#0" value="Inevitable."/>
	<entry name="SpaceMarines/PrimarisHellblaster:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#0" value="Enfriando las reliquias."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#1" value="Fusión sagrada."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#2" value="No poseerán este mundo."/>
	<entry name="SpaceMarines/PrimarisHellblaster:Idle#3" value="Estabilizando campos de contención."/>
	<entry name="SpaceMarines/PrimarisHellblaster:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Shaken#0" value="Bobinas… desestabilizando."/>
	<entry name="SpaceMarines/PrimarisHellblaster:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisHellblaster:Victory#0" value="Enemigos incinerados."/>
	<entry name="SpaceMarines/PrimarisHellblaster:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#0" value="Apoyo orbital."/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#1" value="Estableciendo cabeza de puente."/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#2" value="¡Intercediendo!"/>
	<entry name="SpaceMarines/PrimarisInceptor:Attack#3" value="Cae una venganza feroz."/>
	<entry name="SpaceMarines/PrimarisInceptor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInceptor:Broken#0" value="A los cielos."/>
	<entry name="SpaceMarines/PrimarisInceptor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Hurt#0" value="¡Exfiltrando!"/>
	<entry name="SpaceMarines/PrimarisInceptor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#0" value="Esperando para caer."/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#1" value="Listo para atacar."/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#2" value="¡Por el Capítulo!"/>
	<entry name="SpaceMarines/PrimarisInceptor:Idle#3" value="No más hijos no numerados."/>
	<entry name="SpaceMarines/PrimarisInceptor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInceptor:Shaken#0" value="¡Concéntrate!"/>
	<entry name="SpaceMarines/PrimarisInceptor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInceptor:Victory#0" value="Envíanos a donde nos necesiten."/>
	<entry name="SpaceMarines/PrimarisInceptor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:AlliedUnit:SpaceMarines/TacticalSpaceMarine#0" value="…antiguos Hermanos…"/>
	<entry name="SpaceMarines/PrimarisIntercessor:AlliedUnit:SpaceMarines/TacticalSpaceMarineCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#0" value="Rifles bolter, hermanos."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#1" value="Patrón de disparo estándar."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#2" value="¡Supervisión!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Attack#3" value="Cuidado con los rezagados."/>
	<entry name="SpaceMarines/PrimarisIntercessor:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Broken#0" value="Retirarse como unidad."/>
	<entry name="SpaceMarines/PrimarisIntercessor:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Hurt#0" value="Un hermano ha caído. Repetir."/>
	<entry name="SpaceMarines/PrimarisIntercessor:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#0" value="En su nombre."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#1" value="Todos fuimos Escudos Grises una vez."/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#2" value="¡A prueba de herejías!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Idle#3" value="¡Por los Primarcas!"/>
	<entry name="SpaceMarines/PrimarisIntercessor:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Shaken#0" value="Aguantad, hermanos."/>
	<entry name="SpaceMarines/PrimarisIntercessor:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisIntercessor:Victory#0" value="Escuadrón solicitando nuevos objetivos."/>
	<entry name="SpaceMarines/PrimarisIntercessor:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#0" value="¡Disparando! ¡Adelante!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#1" value="Antiinfantería."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#2" value="¡Atacando al enemigo!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Attack#3" value="Escoltando."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Broken#0" value="Rompiendo el contacto."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Hurt#0" value="Recibiendo fuego."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#0" value="Comprobando el movimiento de la torreta."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#1" value="Eso requerirá la atención de un marine tecnológico."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#2" value="Explorando en busca de orcos."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Idle#3" value="Está demasiado tranquilo."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Shaken#0" value="¡Sigue moviéndote!"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisInvaderATV:Victory#0" value="Enemigo eliminado. Extrayendo."/>
	<entry name="SpaceMarines/PrimarisInvaderATV:VictoryCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#0" value="Disparando armas principales."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#1" value="¡Ejecutando!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#2" value="Xenos, herejes… ¡todos caen!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Attack#3" value="¡Por el Códice!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:AttackCount" value="4"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Broken#0" value="¡Por el Emperador!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:BrokenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Hurt#0" value="¡Abertura en el casco! Sigue disparando."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:HurtCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#0" value="Comprobando todos los cañones."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#1" value="Almacenando munición."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#2" value="Restaurando blindaje."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Idle#3" value="¡Un tanque apto para un Astartes!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:IdleCount" value="4"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Shaken#0" value="¡Impensable!"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:ShakenCount" value="1"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:Victory#0" value="Inevitable."/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner:VictoryCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#0" value="¡Macro… plasma… incinerador!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#1" value="¡Soy… Su ira!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#2" value="Nuestra hora… está ¡Aquí!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Attack#3" value="¡Enfréntate… a mí!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:AttackCount" value="4"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Broken#0" value="¡Defensa y lucha… hermanos!"/>
	<entry name="SpaceMarines/RedemptorDreadnought:BrokenCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Hurt#0" value="Solo… en la muerte…"/>
	<entry name="SpaceMarines/RedemptorDreadnought:HurtCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#0" value="La redención… 	aguarda."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#1" value="Hace… mucho tiempo."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#2" value="El deber… nunca termina."/>
	<entry name="SpaceMarines/RedemptorDreadnought:Idle#3" value="Adelante… a la batalla."/>
	<entry name="SpaceMarines/RedemptorDreadnought:IdleCount" value="4"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Shaken#0" value="Emperador…"/>
	<entry name="SpaceMarines/RedemptorDreadnought:ShakenCount" value="1"/>
	<entry name="SpaceMarines/RedemptorDreadnought:Victory#0" value="Indomitus… continúa."/>
	<entry name="SpaceMarines/RedemptorDreadnought:VictoryCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Attack#0" value="Golpeando rápido y duro."/>
	<entry name="SpaceMarines/ScoutBiker:AttackCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Broken#0" value="Moviéndonos a una distancia segura"/>
	<entry name="SpaceMarines/ScoutBiker:BrokenCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Hurt#0" value="No tenemos blindaje para esto."/>
	<entry name="SpaceMarines/ScoutBiker:HurtCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Idle#0" value="Un explorador parado es inútil."/>
	<entry name="SpaceMarines/ScoutBiker:IdleCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Shaken#0" value="Tenemos que continuar moviéndonos."/>
	<entry name="SpaceMarines/ScoutBiker:ShakenCount" value="1"/>
	<entry name="SpaceMarines/ScoutBiker:Victory#0" value="Anota otro más al ataque rápido."/>
	<entry name="SpaceMarines/ScoutBiker:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Attack#0" value="Aportando soporte aéreo."/>
	<entry name="SpaceMarines/StormravenGunship:AttackCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Broken#0" value="Volviendo a base."/>
	<entry name="SpaceMarines/StormravenGunship:BrokenCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Hurt#0" value="Recibiendo fuego AA."/>
	<entry name="SpaceMarines/StormravenGunship:Hurt#1" value="¡Motor perdido!"/>
	<entry name="SpaceMarines/StormravenGunship:HurtCount" value="2"/>
	<entry name="SpaceMarines/StormravenGunship:Idle#0" value="¿Órdenes?"/>
	<entry name="SpaceMarines/StormravenGunship:Idle#1" value="Buscando objetivos."/>
	<entry name="SpaceMarines/StormravenGunship:Idle#2" value="Con un ojo en el cielo."/>
	<entry name="SpaceMarines/StormravenGunship:IdleCount" value="3"/>
	<entry name="SpaceMarines/StormravenGunship:Shaken#0" value="Es como volar a través de una montaña, ouch."/>
	<entry name="SpaceMarines/StormravenGunship:ShakenCount" value="1"/>
	<entry name="SpaceMarines/StormravenGunship:Victory#0" value="Ya no puedo ver ninguno más allí abajo."/>
	<entry name="SpaceMarines/StormravenGunship:VictoryCount" value="1"/>
	<entry name="SpaceMarines/StormtalonGunship" value="SpaceMarines/StormravenGunship"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#0" value="Ataque rápido"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#1" value="Atacando desde el sol."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#2" value="No permitas que los xenos o los herejes vivan."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Attack#3" value="Sin piedad."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:AttackCount" value="4"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Broken#0" value="Rompiendo."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:BrokenCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Hurt#0" value="¡Sácanos de su alcance!"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:HurtCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#0" value="Deber hasta la muerte."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#1" value="El honor de ser el primero en la batalla."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#2" value="Su armadura nos teme."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Idle#3" value="La ociosidad es herejía."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:IdleCount" value="4"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Shaken#0" value="¿Qué órdenes, comandante?"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:ShakenCount" value="1"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:Victory#0" value="Ya estamos en el próximo objetivo."/>
	<entry name="SpaceMarines/StormSpeederThunderstrike:VictoryCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Artefact#0" value="¿Qué es este asqueroso aparato alienígena?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:ArtefactCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#0" value="¡Comed bólter!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#1" value="¡Enfrentándonos al enemigo!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#2" value="¡Por la gloria del Imperio!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#3" value="¡Soy tu salvación!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#4" value="¡Marines Espaciales, atacad!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#5" value="¡Prueba la furia del Emperador!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#6" value="Somos el puño de Guilliman."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Attack#7" value="¡Vosotros contamináis este mundo, escoria!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:AttackCount" value="8"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#0" value="Retiraos a las zonas defensivas!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#1" value="¡Luchad hasta el último de los marines!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#2" value="¡Salvaguardad la semilla genética!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#3" value="¡El Emperador nos ha abandonado!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Broken#4" value="¡Hemos traído la vergüenza a nuestro Capítulo!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:BrokenCount" value="5"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Cover#0" value="El Emperador protege a aquellos que se protegen a si mismos."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:CoverCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:AstraMilitarum#0" value="Incluso como aliados, no se puede confiar en la Guardia."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:AstraMilitarumCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:Necrons#0" value="¿Antiguos horrores fantasmales?¡Ja! Cosas más oscuras habitan en el corazón del hombre."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:NecronsCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:Orks#0" value="¿Te atreves a invadir NUESTRO mundo natal, escoria pielverde?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:OrksCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:SpaceMarines#0" value="¿Traidores en nuestro mundo natal?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyFaction:SpaceMarinesCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/CatachanDevil#0" value="¡Demonio! Permaneced a distancia y apuntad a sus piernas"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/CatachanDevilCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/Enslaver#0" value="Titiriteros malditos—¿dónde están los Bibliotecarios?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/EnslaverCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KastelanRobot#0" value="Nuestras propias creaciones se volvieron contra nosotros."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KastelanRobotCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KhorneLordOfSkulls#0" value="¿Caos inmundo, aquí en nuestro planeta? ¡Convoca a la Inquisición!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/KhorneLordOfSkullsCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/Psychneuein#0" value="Marines Espaciales, sellad vuestros trajes para evitar la infección."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:EnemyUnit:Neutral/PsychneueinCount" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Hurt#0" value="Aguantaremos hasta el último Marine."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Hurt#1" value="¡Nosotros… no… caeremos!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:HurtCount" value="2"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#0" value="Quemad al hereje."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#1" value="Tengo hambre de batalla."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#2" value="Dejadlos venir… Estoy impaciente."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#3" value="Tantos Xenos… y tan pocos bólters."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#4" value="Somos el puño del Emperador."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#5" value="Luchamos, ganaremos."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#6" value="No debemos trastear con estos artefactos xenos, mi señor."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Idle#7" value="¿Qué ordenas, mi señor?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:IdleCount" value="8"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory0#0" value="Munición baja… tenemos que aprovechar cada disparo."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory0Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory1#0" value="El Bibliotecario dice que no se puede confiar en la Guardia—haremos lo que debemos hacer."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory1Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory2#0" value="¿Nuestro mundo está… vacío?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory2Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory3#0" value="¿Estudiar a los Xenos? Mi Señor, ¿está esto en el Codex Astartes?"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory3Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory4#0" value="No temo nada, ni a los Exterminatus."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory4Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory5#0" value="La semilla genética nos sobrevivirá a todos nosotros—¡debe hacerlo!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:QuestStory5Count" value="1"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#0" value="…bendita es la mente demasiado pequeña para dudar."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#1" value="Contra su oscuridad, prevaleceremos."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#2" value="Soy un escudo inquebrantable, forjado contra la oscuridad."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#3" value="Yo… creo en el Emperador. ¡Credo!"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Shaken#4" value="Mientras nuestros enemigos respiren, no podemos permitirnos tener miedo."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:ShakenCount" value="5"/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#0" value="Todos los xenos y traidores son plagas que tienen que ser exterminadas de la galaxia."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#1" value="A las ordenes del Comandante Supremo."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#2" value="Morir a nuestras manos no es deshonroso."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#3" value="En el nombre del Emperador."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#4" value="Mi puño, apretado con fuerza en ceramita, golpea con la fuerza de un Capítulo."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#5" value="Así caen todos los enemigos del Imperio."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#6" value="A través de la destrucción de nuestros enemigos ganamos nuestra salvación."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:Victory#7" value="Somos la venganza del Emperador hecha carne."/>
	<entry name="SpaceMarines/TacticalSpaceMarine:VictoryCount" value="8"/>
	<entry name="SpaceMarines/Terminator" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/Vindicator" value="SpaceMarines/Hunter"/>
	<entry name="SpaceMarines/Whirlwind:Attack#0" value="Misiles fuera, Comandante."/>
	<entry name="SpaceMarines/Whirlwind:Attack#1" value="Whirlwind disparando."/>
	<entry name="SpaceMarines/Whirlwind:Attack#2" value="Impacto de andanada indirecta."/>
	<entry name="SpaceMarines/Whirlwind:Attack#3" value="Bombardeo de saturación disponible."/>
	<entry name="SpaceMarines/Whirlwind:AttackCount" value="4"/>
	<entry name="SpaceMarines/Whirlwind:Broken#0" value="Retirada."/>
	<entry name="SpaceMarines/Whirlwind:BrokenCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Hurt#0" value="Dentro del alcance de armas cortas…sufrimos daños."/>
	<entry name="SpaceMarines/Whirlwind:HurtCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Idle#0" value="Mantenimiento del espíritu-máquina."/>
	<entry name="SpaceMarines/Whirlwind:Idle#1" value="Desplegado, esperando órdenes."/>
	<entry name="SpaceMarines/Whirlwind:Idle#2" value="Si el enemigo necesita ser ablandado, artillería disponible."/>
	<entry name="SpaceMarines/Whirlwind:Idle#3" value="Recargando misiles."/>
	<entry name="SpaceMarines/Whirlwind:IdleCount" value="4"/>
	<entry name="SpaceMarines/Whirlwind:Shaken#0" value="Este es el mundo de nuestro Capítulo…¿Cómo puede ser?"/>
	<entry name="SpaceMarines/Whirlwind:ShakenCount" value="1"/>
	<entry name="SpaceMarines/Whirlwind:Victory#0" value="Recibiendo informes, objetivo destruido."/>
	<entry name="SpaceMarines/Whirlwind:VictoryCount" value="1"/>
	<entry name="Tau/BroadsideBattlesuit" value="Tau/ArmaduraCrisis"/>
	<entry name="Tau/BuilderDrone:Attack#0" value="Este es un uso ineficiente de recursos."/>
	<entry name="Tau/BuilderDrone:Attack#1" value="Realizando ataque no aconsejado."/>
	<entry name="Tau/BuilderDrone:Attack#2" value="Realizando daño mínimo."/>
	<entry name="Tau/BuilderDrone:AttackCount" value="3"/>
	<entry name="Tau/BuilderDrone:Broken#0" value="Unidad en peligro."/>
	<entry name="Tau/BuilderDrone:BrokenCount" value="1"/>
	<entry name="Tau/BuilderDrone:Hurt#0" value="Unidad en riesgo."/>
	<entry name="Tau/BuilderDrone:HurtCount" value="1"/>
	<entry name="Tau/BuilderDrone:Idle#0" value="Apagando"/>
	<entry name="Tau/BuilderDrone:Idle#1" value="Evaluando esquemáticas…"/>
	<entry name="Tau/BuilderDrone:Idle#2" value="Sirviendo a la Casta de la Tierra y al Bien Supremo."/>
	<entry name="Tau/BuilderDrone:IdleCount" value="3"/>
	<entry name="Tau/BuilderDrone:Shaken#0" value="Unidad de construcción bajo ataque."/>
	<entry name="Tau/BuilderDrone:ShakenCount" value="1"/>
	<entry name="Tau/BuilderDrone:Victory#0" value="Ingeniería T'au en acción."/>
	<entry name="Tau/BuilderDrone:VictoryCount" value="1"/>
	<entry name="Tau/CadreFireblade:Attack#0" value="¡Soldados, atacad!"/>
	<entry name="Tau/CadreFireblade:Attack#1" value="Guerreros del Fuego, mostradles El Bien."/>
	<entry name="Tau/CadreFireblade:Attack#2" value="¡Sentid el poder de la tecnología de pulso!"/>
	<entry name="Tau/CadreFireblade:Attack#3" value="Esperad hasta que veáis el blanco de sus ojos… con vuestras miras."/>
	<entry name="Tau/CadreFireblade:AttackCount" value="4"/>
	<entry name="Tau/CadreFireblade:Broken#0" value="¿Que pensaría Kais'Por..?"/>
	<entry name="Tau/CadreFireblade:BrokenCount" value="1"/>
	<entry name="Tau/CadreFireblade:Hurt#0" value="¡Hace falta más que eso para derribarme!"/>
	<entry name="Tau/CadreFireblade:HurtCount" value="1"/>
	<entry name="Tau/CadreFireblade:Idle#0" value="¡Práctica de tiro, shas'el, en línea!"/>
	<entry name="Tau/CadreFireblade:Idle#1" value="Lideramos desde el frente."/>
	<entry name="Tau/CadreFireblade:Idle#2" value="Un buen líder mantiene sus pies en el suelo."/>
	<entry name="Tau/CadreFireblade:IdleCount" value="3"/>
	<entry name="Tau/CadreFireblade:Shaken#0" value="Este no es el camino…"/>
	<entry name="Tau/CadreFireblade:ShakenCount" value="1"/>
	<entry name="Tau/CadreFireblade:Victory#0" value="Tácticamente sabio."/>
	<entry name="Tau/CadreFireblade:VictoryCount" value="1"/>
	<entry name="Tau/Commander:Attack#0" value="¡Por Aun'Va!"/>
	<entry name="Tau/Commander:Attack#1" value="Os enseñaremos el camino"/>
	<entry name="Tau/Commander:Attack#2" value="¡Uníos a nosotros!"/>
	<entry name="Tau/Commander:AttackCount" value="3"/>
	<entry name="Tau/Commander:Broken#0" value="La retirada es la única opción…"/>
	<entry name="Tau/Commander:BrokenCount" value="1"/>
	<entry name="Tau/Commander:Hurt#0" value="Armadura en peligro."/>
	<entry name="Tau/Commander:HurtCount" value="1"/>
	<entry name="Tau/Commander:Idle#0" value="Úsame, por el Bien Supremo."/>
	<entry name="Tau/Commander:Idle#1" value="Siempre pensando un paso por delante."/>
	<entry name="Tau/Commander:Idle#2" value="¿Que es una única vida comparado ocn el Bien Supremo?"/>
	<entry name="Tau/Commander:IdleCount" value="3"/>
	<entry name="Tau/Commander:Shaken#0" value="No flaquearé."/>
	<entry name="Tau/Commander:ShakenCount" value="1"/>
	<entry name="Tau/Commander:Victory#0" value="La Casta del Fuego lidera."/>
	<entry name="Tau/Commander:VictoryCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Attack#0" value="Armaduras en acción."/>
	<entry name="Tau/CrisisBattlesuit:Attack#1" value="Disparando armamento de pulso."/>
	<entry name="Tau/CrisisBattlesuit:Attack#2" value="Habilitando tácticas defensivas."/>
	<entry name="Tau/CrisisBattlesuit:Attack#3" value="¡Puedes contar con nosotros!"/>
	<entry name="Tau/CrisisBattlesuit:AttackCount" value="4"/>
	<entry name="Tau/CrisisBattlesuit:Broken#0" value="Élite… retiraos."/>
	<entry name="Tau/CrisisBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Hurt#0" value="¡Estamos perdiendo sistemas!"/>
	<entry name="Tau/CrisisBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Idle#0" value="Siempre listos."/>
	<entry name="Tau/CrisisBattlesuit:Idle#1" value="Lo haremos."/>
	<entry name="Tau/CrisisBattlesuit:Idle#2" value="¿Qué desean los Etéreos?"/>
	<entry name="Tau/CrisisBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/CrisisBattlesuit:Shaken#0" value="No podemos aguantar esta posición."/>
	<entry name="Tau/CrisisBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/CrisisBattlesuit:Victory#0" value="Por eso mandas armaduras."/>
	<entry name="Tau/CrisisBattlesuit:Victory#1" value="Golpe letal, dado."/>
	<entry name="Tau/CrisisBattlesuit:Victory#2" value="¿Tienen algo más… fuerte?"/>
	<entry name="Tau/CrisisBattlesuit:Victory#3" value="Márcanos otro, shas'el"/>
	<entry name="Tau/CrisisBattlesuit:VictoryCount" value="4"/>
	<entry name="Tau/Devilfish:Attack#0" value="Moviéndonos hacia el enemigo."/>
	<entry name="Tau/Devilfish:Attack#1" value="Enseñando a nuestros enemigos a temer a todos los T'au."/>
	<entry name="Tau/Devilfish:Attack#2" value="¡Sentid mi furia!"/>
	<entry name="Tau/Devilfish:Attack#3" value="Disparando todas las armas."/>
	<entry name="Tau/Devilfish:AttackCount" value="4"/>
	<entry name="Tau/Devilfish:Broken#0" value="¡Estamos sobreextendidos!"/>
	<entry name="Tau/Devilfish:BrokenCount" value="1"/>
	<entry name="Tau/Devilfish:Hurt#0" value="No puedo recibir más golpes como ese."/>
	<entry name="Tau/Devilfish:HurtCount" value="1"/>
	<entry name="Tau/Devilfish:Idle#0" value="Listo para moverme a sus órdenes."/>
	<entry name="Tau/Devilfish:Idle#1" value="Esperando."/>
	<entry name="Tau/Devilfish:Idle#2" value="Comprobando chips; este está frito."/>
	<entry name="Tau/Devilfish:IdleCount" value="3"/>
	<entry name="Tau/Devilfish:Shaken#0" value="Le están dando la vuelta a la batalla!"/>
	<entry name="Tau/Devilfish:ShakenCount" value="1"/>
	<entry name="Tau/Devilfish:Victory#0" value="Transportándoles a un lugar mejor."/>
	<entry name="Tau/Devilfish:Victory#1" value="Se han largado."/>
	<entry name="Tau/Devilfish:VictoryCount" value="2"/>
	<entry name="Tau/Ethereal:Attack#0" value="Esto es por vuestro propio bien."/>
	<entry name="Tau/Ethereal:Attack#1" value="Por los T'au."/>
	<entry name="Tau/Ethereal:Attack#2" value="¿Por qué os oponéis a nosotros?"/>
	<entry name="Tau/Ethereal:AttackCount" value="3"/>
	<entry name="Tau/Ethereal:Broken#0" value="Puede que… la paz se encuentre detrás de nuestras propias filas."/>
	<entry name="Tau/Ethereal:BrokenCount" value="1"/>
	<entry name="Tau/Ethereal:Hurt#0" value="Aunque mi carne sea débil, mi espíritu triunfará!"/>
	<entry name="Tau/Ethereal:HurtCount" value="1"/>
	<entry name="Tau/Ethereal:Idle#0" value="Predico el Bien."/>
	<entry name="Tau/Ethereal:Idle#1" value="En la meditación, se encuentra el entendimiento."/>
	<entry name="Tau/Ethereal:Idle#2" value="El bien por si sólo no es suficiente; es el mayor el que buscamos."/>
	<entry name="Tau/Ethereal:IdleCount" value="3"/>
	<entry name="Tau/Ethereal:Shaken#0" value="¡Defendedme!"/>
	<entry name="Tau/Ethereal:ShakenCount" value="1"/>
	<entry name="Tau/Ethereal:Victory#0" value="Difundiendo la palabra."/>
	<entry name="Tau/Ethereal:Victory#1" value="Lamento cada alma que perdemos."/>
	<entry name="Tau/Ethereal:Victory#2" value="Esto, también, ha sido por el bien."/>
	<entry name="Tau/Ethereal:VictoryCount" value="3"/>
	<entry name="Tau/FireWarrior:Attack#0" value="¡Disparando a los objetivos!"/>
	<entry name="Tau/FireWarrior:Attack#1" value="¡En combate!"/>
	<entry name="Tau/FireWarrior:Attack#2" value="¡Somos la Casta de Fuego!"/>
	<entry name="Tau/FireWarrior:Attack#3" value="¡Manteneos a distancia!"/>
	<entry name="Tau/FireWarrior:Attack#4" value="¡Apoyáos entre vosotros!"/>
	<entry name="Tau/FireWarrior:Attack#5" value="¡Por la memoria de Jun'Nami!"/>
	<entry name="Tau/FireWarrior:AttackCount" value="6"/>
	<entry name="Tau/FireWarrior:Broken#0" value="¡Retiraos! ¡Corred!"/>
	<entry name="Tau/FireWarrior:BrokenCount" value="1"/>
	<entry name="Tau/FireWarrior:Hurt#0" value="¡Estamos perdiendo tropas!"/>
	<entry name="Tau/FireWarrior:HurtCount" value="1"/>
	<entry name="Tau/FireWarrior:Idle#0" value="Descansando."/>
	<entry name="Tau/FireWarrior:Idle#1" value="Entrenando, shas'el."/>
	<entry name="Tau/FireWarrior:Idle#2" value="Echo de menos mi sector…"/>
	<entry name="Tau/FireWarrior:Idle#3" value="No hablemos del sub-espacio nunca más…"/>
	<entry name="Tau/FireWarrior:Idle#4" value="Listo para la acción, shas'el."/>
	<entry name="Tau/FireWarrior:IdleCount" value="5"/>
	<entry name="Tau/FireWarrior:Shaken#0" value="¡No dejéis que se acerquen!"/>
	<entry name="Tau/FireWarrior:ShakenCount" value="1"/>
	<entry name="Tau/FireWarrior:Victory#0" value="¡La potencia de fuego superior gana el día!"/>
	<entry name="Tau/FireWarrior:Victory#1" value="¡Demostrando el poder de la Casta de Fuego!"/>
	<entry name="Tau/FireWarrior:VictoryCount" value="2"/>
	<entry name="Tau/GhostkeelBattlesuit" value="Tau/ArmaduraCrisis"/>
	<entry name="Tau/GravInhibitorDrone" value="Tau/DronArmado"/>
	<entry name="Tau/GunDrone:Attack#0" value="Encontrada solución de disparo."/>
	<entry name="Tau/GunDrone:Attack#1" value="Dron en acción."/>
	<entry name="Tau/GunDrone:Attack#2" value="¡Luchando por el bien!"/>
	<entry name="Tau/GunDrone:Attack#3" value="Límites de la lógica alzanzados. Disparos recibidos."/>
	<entry name="Tau/GunDrone:Attack#4" value="Enemigos interceptados con una certeza del 89% . Entablando combate."/>
	<entry name="Tau/GunDrone:AttackCount" value="5"/>
	<entry name="Tau/GunDrone:Broken#0" value="Preservando la integridad de la unidad."/>
	<entry name="Tau/GunDrone:BrokenCount" value="1"/>
	<entry name="Tau/GunDrone:Hurt#0" value="Capacidad operativa reducida al 50%"/>
	<entry name="Tau/GunDrone:HurtCount" value="1"/>
	<entry name="Tau/GunDrone:Idle#0" value="Patrullando."/>
	<entry name="Tau/GunDrone:Idle#1" value="El Bien Supremo es lógico."/>
	<entry name="Tau/GunDrone:Idle#2" value="Construido por la Casta de Tierra."/>
	<entry name="Tau/GunDrone:Idle#3" value="Subrutinas ociosas."/>
	<entry name="Tau/GunDrone:Idle#4" value="Limando glitches g1."/>
	<entry name="Tau/GunDrone:IdleCount" value="5"/>
	<entry name="Tau/GunDrone:Shaken#0" value="Rutinas inadecuadas. Dependiendo del resguardo."/>
	<entry name="Tau/GunDrone:ShakenCount" value="1"/>
	<entry name="Tau/GunDrone:Victory#0" value="Objetivo alcanzado. Siguiendo adelante."/>
	<entry name="Tau/GunDrone:VictoryCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Attack#0" value="Nave disparando, enemigo cayendo."/>
	<entry name="Tau/HammerheadGunship:Attack#1" value="Volando bajo."/>
	<entry name="Tau/HammerheadGunship:Attack#2" value="Fuego de soporte activado."/>
	<entry name="Tau/HammerheadGunship:AttackCount" value="3"/>
	<entry name="Tau/HammerheadGunship:Broken#0" value="Nave retirándose."/>
	<entry name="Tau/HammerheadGunship:BrokenCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Hurt#0" value="Nos han dañado pero podemos seguir."/>
	<entry name="Tau/HammerheadGunship:HurtCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Idle#0" value="Listo para atacar a distancia."/>
	<entry name="Tau/HammerheadGunship:Idle#1" value="Haznos saber si necesitas este pájaro en el cielo."/>
	<entry name="Tau/HammerheadGunship:Idle#2" value="¿Los ky'husa? Son de otra persona, señor."/>
	<entry name="Tau/HammerheadGunship:IdleCount" value="3"/>
	<entry name="Tau/HammerheadGunship:Shaken#0" value="¡No deberíamos estar en primera línea!"/>
	<entry name="Tau/HammerheadGunship:ShakenCount" value="1"/>
	<entry name="Tau/HammerheadGunship:Victory#0" value="Somos los primeros de los Mont'ka."/>
	<entry name="Tau/HammerheadGunship:Victory#1" value="Barrido completado."/>
	<entry name="Tau/HammerheadGunship:Victory#2" value="Buscando nuevos objetivos."/>
	<entry name="Tau/HammerheadGunship:VictoryCount" value="3"/>
	<entry name="Tau/InterceptorDrone" value="Tau/DronArmado"/>
	<entry name="Tau/MarkerDrone" value="Tau/DronArmado"/>
	<entry name="Tau/Pathfinder" value="Tau/GuerreroDeFuego"/>
	<entry name="Tau/Piranha:Attack#0" value="Disparando cañón de ráfagas."/>
	<entry name="Tau/Piranha:Attack#1" value="¿Algún vagabundo necesita apoyo?"/>
	<entry name="Tau/Piranha:Attack#2" value="Golpeando con velocidad."/>
	<entry name="Tau/Piranha:Attack#3" value="Comenzando bombardeo."/>
	<entry name="Tau/Piranha:AttackCount" value="4"/>
	<entry name="Tau/Piranha:Broken#0" value="Armadura exploradora retirándose."/>
	<entry name="Tau/Piranha:BrokenCount" value="1"/>
	<entry name="Tau/Piranha:Hurt#0" value="Evasión inefectiva, daños serios recibidos."/>
	<entry name="Tau/Piranha:HurtCount" value="1"/>
	<entry name="Tau/Piranha:Idle#0" value="¿Sabemos algo de los vagabundos?"/>
	<entry name="Tau/Piranha:Idle#1" value="Reconocimiento aguardando órdenes."/>
	<entry name="Tau/Piranha:IdleCount" value="2"/>
	<entry name="Tau/Piranha:Shaken#0" value="¿Por qué no funciona nada? ¿Por qué-"/>
	<entry name="Tau/Piranha:ShakenCount" value="1"/>
	<entry name="Tau/Piranha:Victory#0" value="Objetivo… destrozado, shas'el."/>
	<entry name="Tau/Piranha:VictoryCount" value="1"/>
	<entry name="Tau/PulseAcceleratorDrone" value="Tau/DronArmado"/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#0" value="Apoyo aéreo en camino."/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#1" value="Casta de Aire, por el Bien Supremo."/>
	<entry name="Tau/RazorsharkStrikeFighter:Attack#2" value="No nos han visto llegar…"/>
	<entry name="Tau/RazorsharkStrikeFighter:AttackCount" value="3"/>
	<entry name="Tau/RazorsharkStrikeFighter:Broken#0" value="Huyendo."/>
	<entry name="Tau/RazorsharkStrikeFighter:BrokenCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Hurt#0" value="El fuego enemigo es efectivo. ¡Necesitamos nuevas órdenes!"/>
	<entry name="Tau/RazorsharkStrikeFighter:HurtCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#0" value="Circunvolando, aguardando órdenes."/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#1" value="¿Deberíamos volver a la base?"/>
	<entry name="Tau/RazorsharkStrikeFighter:Idle#2" value="¿Se sabe algo de esa promoción?"/>
	<entry name="Tau/RazorsharkStrikeFighter:IdleCount" value="3"/>
	<entry name="Tau/RazorsharkStrikeFighter:Shaken#0" value="¡Maniobras evasivas!"/>
	<entry name="Tau/RazorsharkStrikeFighter:ShakenCount" value="1"/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#0" value="Los observadores reportan enemigos eliminados."/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#1" value="Eso es superioridad aérea."/>
	<entry name="Tau/RazorsharkStrikeFighter:Victory#2" value="Nos vamos, misión cumplida."/>
	<entry name="Tau/RazorsharkStrikeFighter:VictoryCount" value="3"/>
	<entry name="Tau/ReconDrone" value="Tau/DronArmado"/>
	<entry name="Tau/RiptideBattlesuit:Attack#0" value="Disparando todo."/>
	<entry name="Tau/RiptideBattlesuit:Attack#1" value="¿Por qué se molestan en disparar de vuelta?"/>
	<entry name="Tau/RiptideBattlesuit:Attack#2" value="Más vale que no arañen el Fio'tak."/>
	<entry name="Tau/RiptideBattlesuit:AttackCount" value="3"/>
	<entry name="Tau/RiptideBattlesuit:Broken#0" value="¡Necesito cobertura de drones escudo!"/>
	<entry name="Tau/RiptideBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Hurt#0" value="Reactor dañado, armadura comprometida. Puedo seguir adelante."/>
	<entry name="Tau/RiptideBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Idle#0" value="Más vale que tengas planes para mi."/>
	<entry name="Tau/RiptideBattlesuit:Idle#1" value="ZZZ…"/>
	<entry name="Tau/RiptideBattlesuit:Idle#2" value="Tanto poder de fuego…"/>
	<entry name="Tau/RiptideBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/RiptideBattlesuit:Shaken#0" value="Lo mejor de lo mejor. Lo. Mejor. De. Lo. Mejor."/>
	<entry name="Tau/RiptideBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/RiptideBattlesuit:Victory#0" value="Están muertos. Seguid disparando."/>
	<entry name="Tau/RiptideBattlesuit:Victory#1" value="¿Alguien ha visto dónde ha ido mi objetivo?"/>
	<entry name="Tau/RiptideBattlesuit:VictoryCount" value="2"/>
	<entry name="Tau/RVarnaBattlesuit:Attack#0" value="Aguantando."/>
	<entry name="Tau/RVarnaBattlesuit:Attack#1" value="Saturación de plasma en proceso."/>
	<entry name="Tau/RVarnaBattlesuit:AttackCount" value="2"/>
	<entry name="Tau/RVarnaBattlesuit:Broken#0" value="Necesitamos apoyo aquí."/>
	<entry name="Tau/RVarnaBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Hurt#0" value="Reactor filtrando…no hay retirada."/>
	<entry name="Tau/RVarnaBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Idle#0" value="¿Qué estamos haciendo en este fu'llasso?"/>
	<entry name="Tau/RVarnaBattlesuit:Idle#1" value="Los ho'or-ata-t'chel—los dolores fantasma—son malos hoy."/>
	<entry name="Tau/RVarnaBattlesuit:Idle#2" value="Shas'vre reportando."/>
	<entry name="Tau/RVarnaBattlesuit:Idle#3" value="¿Cómo hacen este fio'tak? ¡Es inexpugnable!"/>
	<entry name="Tau/RVarnaBattlesuit:IdleCount" value="4"/>
	<entry name="Tau/RVarnaBattlesuit:Shaken#0" value="¡Iur'tae'mont! ¿Dónde está Farsight cuando se le necesita?"/>
	<entry name="Tau/RVarnaBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/RVarnaBattlesuit:Victory#0" value="Nada se mueve por aquí."/>
	<entry name="Tau/RVarnaBattlesuit:VictoryCount" value="1"/>	
	<entry name="Tau/ShieldDrone" value="Tau/DronArmado"/>
	<entry name="Tau/ShieldedMissileDrone" value="Tau/DronArmado"/>
	<entry name="Tau/SkyRayGunship" value="Tau/TanqueCabezamartillo"/>
	<entry name="Tau/StealthBattlesuit:Attack#0" value="No nos han visto llegar."/>
	<entry name="Tau/StealthBattlesuit:Attack#1" value="Desde las sombras…"/>
	<entry name="Tau/StealthBattlesuit:Attack#2" value="Shas'el, les tenemos."/>
	<entry name="Tau/StealthBattlesuit:AttackCount" value="3"/>
	<entry name="Tau/StealthBattlesuit:Broken#0" value="Solicitud de exfiltración enviada."/>
	<entry name="Tau/StealthBattlesuit:BrokenCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Hurt#0" value="¿Cómo nos están dando?"/>
	<entry name="Tau/StealthBattlesuit:HurtCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Idle#0" value="Manteniendo la cobertura."/>
	<entry name="Tau/StealthBattlesuit:Idle#1" value="Equipo de sigilo, silencio de radio."/>
	<entry name="Tau/StealthBattlesuit:Idle#2" value="No puedes estar hablándonos a nosotros, no estamos aquí."/>
	<entry name="Tau/StealthBattlesuit:IdleCount" value="3"/>
	<entry name="Tau/StealthBattlesuit:Shaken#0" value="¿Estamos demasiado adentro?"/>
	<entry name="Tau/StealthBattlesuit:ShakenCount" value="1"/>
	<entry name="Tau/StealthBattlesuit:Victory#0" value="Difundiendo terror."/>
	<entry name="Tau/StealthBattlesuit:Victory#1" value="Objetivo eliminado."/>
	<entry name="Tau/StealthBattlesuit:Victory#2" value="Seguimos nuestro propio camino."/>
	<entry name="Tau/StealthBattlesuit:VictoryCount" value="3"/>
	<entry name="Tau/StealthDrone" value="Tau/DronArmado"/>
	<entry name="Tau/Stormsurge" value="Tau/ArmaduraCataclismo"/>
	<entry name="Tau/SunSharkBomber" value="Tau/CazaTiburónLanza"/>
	<entry name="Tyranids/Carnifex:Attack#0" value="Krrkkk!"/>
	<entry name="Tyranids/Carnifex:Attack#1" value="Rrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#2" value="Nnnnn!"/>
	<entry name="Tyranids/Carnifex:Attack#3" value="Srrr!"/>
	<entry name="Tyranids/Carnifex:Attack#4" value="Krk!"/>
	<entry name="Tyranids/Carnifex:Attack#5" value="Rrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#6" value="Nrrrrr!"/>
	<entry name="Tyranids/Carnifex:Attack#7" value="Vrrrk!"/>
	<entry name="Tyranids/Carnifex:AttackCount" value="8"/>
	<entry name="Tyranids/Carnifex:Broken#0" value="…mmmm…"/>
	<entry name="Tyranids/Carnifex:Broken#1" value="…thhhh…"/>
	<entry name="Tyranids/Carnifex:Broken#2" value="…phhhh…"/>
	<entry name="Tyranids/Carnifex:Broken#3" value="…sssss…"/>
	<entry name="Tyranids/Carnifex:BrokenCount" value="4"/>
	<entry name="Tyranids/Carnifex:EnemyFaction:Necrons#0" value="Nrnnnnn."/>
	<entry name="Tyranids/Carnifex:EnemyFaction:NecronsCount" value="1"/>
	<entry name="Tyranids/Carnifex:Hurt#0" value="Sssssss!"/>
	<entry name="Tyranids/Carnifex:HurtCount" value="1"/>
	<entry name="Tyranids/Carnifex:Idle#0" value="Krrck."/>
	<entry name="Tyranids/Carnifex:Idle#1" value="Nrrrk."/>
	<entry name="Tyranids/Carnifex:Idle#2" value="Rrrc."/>
	<entry name="Tyranids/Carnifex:Idle#3" value="Kckckc."/>
	<entry name="Tyranids/Carnifex:IdleCount" value="4"/>
	<entry name="Tyranids/Carnifex:Shaken#0" value="Rrhhrrr"/>
	<entry name="Tyranids/Carnifex:ShakenCount" value="1"/>
	<entry name="Tyranids/Carnifex:Victory#0" value="Hwlllllll!"/>
	<entry name="Tyranids/Carnifex:Victory#1" value="Iaiaiaia!"/>
	<entry name="Tyranids/Carnifex:Victory#2" value="Ulululu!"/>
	<entry name="Tyranids/Carnifex:Victory#3" value="Phphph!"/>
	<entry name="Tyranids/Carnifex:Victory#4" value="Ulllllla!"/>
	<entry name="Tyranids/Carnifex:VictoryCount" value="5"/>
	<entry name="Tyranids/Exocrine" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Gargoyle" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Haruspex" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Headquarters" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/HiveCrone" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/HiveTyrant" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Hormagaunt" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Lictor" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Malanthrope" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Ravener" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Termagant" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tervigon" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Trygon" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/TyranidPrime" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tyrannocyte" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Tyrannofex" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Warrior" value="Tyranids/Carnifex"/>
	<entry name="Tyranids/Zoanthrope" value="Tyranids/Carnifex"/>
	<entry name="ChaosSpaceMarines/ChaosLord:Attack#0" value="Siente mil años de rabia."/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Attack#1" value="En el nombre de los Dioses Oscuros"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Attack#2" value="¡Arrodillaos ante vuestro señor!"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Attack#3" value="¡Morid, escoria indigna!¡Morid!"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Attack#4" value="¡Por la Gloria del Caos!"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:AttackCount" value="5"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Broken#0" value="¡No moriré aquí!"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Broken#1" value="¡De vuelta al ojo!"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:BrokenCount" value="2"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Hurt#0" value="¡¿Qué?! ¿Que TÚ me dañaste?!"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Idle#0" value="Mientras nosotros estamos ociosos, aún hay leales que viven."/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Idle#1" value="¿Te acuerdas? Su sangre manchó todas las paredes."/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Idle#2" value="Tanto placer, al ver un ángel caído…"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Idle#3" value="Gritos o cantos, son lo mismo para mí."/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Idle#4" value="¿Mereció la pena la condenación eterna?"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:IdleCount" value="5"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Shaken#0" value="¡¿Quién se atreve?!"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Shaken#1" value="Viviré para vengarme."/>
 	<entry name="ChaosSpaceMarines/ChaosLord:ShakenCount" value="2"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Victory#0" value="Dedico estas muertes a los Dioses."/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Victory#1" value="¡¿Esto es todo?! ¡Escoria!"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:Victory#2" value="La victoria sabe a cenizas en mi boca mientras el Imperium sobreviva…"/>
 	<entry name="ChaosSpaceMarines/ChaosLord:VictoryCount" value="3"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#0" value="¿Qué idiota nos manda a la guerra?"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#1" value="Machacad sus huesos y haced polvo de ellos."/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Attack#2" value="¡Conduce más rápido!"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:AttackCount" value="3"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Broken#0" value="¡Huid! Les mataremos más tarde…"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:BrokenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Hurt#0" value="¿Quién me está disparando y dañando mi armadura?"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Idle#0" value="¿Quién conducirá el Rhino?"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Idle#1" value="Dirigiendo al Señor a la guerra,yawwwn."/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:IdleCount" value="2"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Shaken#0" value="Condenación eterna… ¿para esto?"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:Victory#0" value="¿No puedes destruir ni un simple Rhino?"/>
 	<entry name="ChaosSpaceMarines/ChaosRhino:VictoryCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#0" value="¡Gritad! ¡Vuestro fin ha llegado!"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#1" value="Soy puro en mi odio… ¡ven a comprobarlo!"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#2" value="Fui el hijo del emperador… ¡no puedes competir conmigo!"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Attack#3" value="¡Hahahaha! ¡MUERE!"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:AttackCount" value="4"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Broken#0" value="¡Hemos sido derrotados, debemos huír!"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:BrokenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Hurt#0" value="Que los Dioses se apiaden de nosotros…"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#0" value="¿Podré aguantar otros mil años así?"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#1" value="Pásame otro civil, no me queda tinta."/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#2" value="¿Por qué protegen aún a su Emperador-Cadaver?"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Idle#3" value="Guerra eterna, entretenimiento eterno."/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:IdleCount" value="4"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Shaken#0" value="¿Estamos… perdiendo?"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#0" value="¡Así mueran todos los enemigos de Abaddon!"/>
	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#1" value="Otro paso más para retomar el control de la Galaxia… nuestro derecho desde el nacimiento."/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:Victory#2" value="Tu muerte no significa nada para mí."/>
 	<entry name="ChaosSpaceMarines/ChaosSpaceMarine:VictoryCount" value="3"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#0" value="…El giro que se amplía…"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#1" value="*grita incoherentemente*"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Attack#2" value="ahhhhaahhhh"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:AttackCount" value="3"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Broken#0" value="…"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:BrokenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Hurt#0" value="…"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#0" value="¿…una eternidad de locura..?"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#1" value="ehaahahaahaha"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#2" value="mmmm mmmm"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#3" value="…andando de un lado a otro…"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Idle#4" value="nnnhhhrrr."/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:IdleCount" value="5"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Shaken#0" value="eh"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:Victory#0" value="ach"/>
 	<entry name="ChaosSpaceMarines/ChaosSpawn:VictoryCount" value="1"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#0" value="La disformidad os toca, pequeños."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#1" value="¡Siente el poder del Dios que será!"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#2" value="Juguetes, marionetas…¡caed!"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Attack#3" value="Ahahahahaha."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:AttackCount" value="4"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Broken#0" value="No puede ser derrotado… por…-"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Hurt#0" value="¿TE ATREVES A ENFRENTARTE A MÍ?"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#0" value="…¡Qué estúpidos son estos mortales!"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#1" value="El plano físico… lo echaba de menos."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#2" value="Qué extraño, un mundo que no cambia de forma constantemente."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#3" value="Qué extraña perdición se asienta sobre Gladius."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#4" value="Venid, esclavos, y entretened a vuestro Dios."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Idle#5" value="Qué raro, pensar que estos talones una vez fueron manos mortales…"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:IdleCount" value="6"/>
	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#0" value="Arranqué corazones de miles de hombres… ¿y así me lo agradeces?"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#1" value="La disformidad me llama…"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#2" value="…la locura se cobra su precio—debo mantener el control."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Shaken#3" value="¡Tengo un infierno especial para aquellos como tú!"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:ShakenCount" value="4"/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#0" value="Otro craneo para mi colección… Ya puede suplicar Khorne."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#1" value="Cabezas sin ojos… me place."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:Victory#2" value="Quédate quieto, mortal; quiero disfrutar de tu muerte."/>
 	<entry name="ChaosSpaceMarines/DaemonPrince:VictoryCount" value="3"/>
 	<entry name="ChaosSpaceMarines/Defiler:Attack#0" value="Sí, sí, mortales, ¡morid!."/>
 	<entry name="ChaosSpaceMarines/Defiler:Attack#1" value="Más cerca… las garras… quieren hundirse en la carne… ¡venid!"/>
 	<entry name="ChaosSpaceMarines/Defiler:Attack#2" value="Conoce a nuestros dioses, ve a conocerles."/>
 	<entry name="ChaosSpaceMarines/Defiler:Attack#3" value="Por el Caos, sangre, calaveras, por el Caos."/>
 	<entry name="ChaosSpaceMarines/Defiler:Attack#4" value="Arrancar y penetrar… la mortalidad es gozo."/>
 	<entry name="ChaosSpaceMarines/Defiler:Attack#5" value="¡Estirar, cortar, machacar, disfrutar y despedazar!"/>
 	<entry name="ChaosSpaceMarines/Defiler:AttackCount" value="6"/>
 	<entry name="ChaosSpaceMarines/Defiler:Broken#0" value="¡Dioses oscuros!"/>
 	<entry name="ChaosSpaceMarines/Defiler:Broken#1" value="¡Perdiendo, perdiendo, perdiendo, hahaha!"/>
 	<entry name="ChaosSpaceMarines/Defiler:BrokenCount" value="2"/>
 	<entry name="ChaosSpaceMarines/Defiler:Hurt#0" value="Sí, de vuelta a la disformidad, sí, libre de nosotros."/>
 	<entry name="ChaosSpaceMarines/Defiler:Hurt#1" value="Destruye mi cuerpo, libera al demonio-yo."/>
 	<entry name="ChaosSpaceMarines/Defiler:HurtCount" value="2"/>
 	<entry name="ChaosSpaceMarines/Defiler:Idle#0" value="Disformidad, ¡¿dónde estar diformidad?!"/>
 	<entry name="ChaosSpaceMarines/Defiler:Idle#1" value="No he de matar… sujeto por runas, brujería… no poder… querer…"/>
 	<entry name="ChaosSpaceMarines/Defiler:Idle#2" value="¿Por qué no matar matar matar, dónde matar matar?"/>
 	<entry name="ChaosSpaceMarines/Defiler:Idle#3" value="Apesta a los Ancestrales. Apesta a dios muerto, apesta."/>
 	<entry name="ChaosSpaceMarines/Defiler:IdleCount" value="4"/>
 	<entry name="ChaosSpaceMarines/Defiler:Shaken#0" value="Haha, la disformidad me llama. Libertad."/>
	<entry name="ChaosSpaceMarines/Defiler:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Defiler:Victory#0" value="Romper fácil, romper, triste, ¿más?"/>
 	<entry name="ChaosSpaceMarines/Defiler:Victory#1" value="Muerte tan rápida."/>
 	<entry name="ChaosSpaceMarines/Defiler:Victory#2" value="A la disformidad, gloria a los Señores."/>
 	<entry name="ChaosSpaceMarines/Defiler:Victory#3" value="Perqueña alma. La forja es caliente, quema, esperando. A tí."/>
 	<entry name="ChaosSpaceMarines/Defiler:VictoryCount" value="4"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#0" value="¡Khrrrrne!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#1" value="¡Rrrrr!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#2" value="¡Wrrrrr-"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Attack#3" value="¡K-k-k-k!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Broken#0" value="Sklzzz-"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Hurt#0" value="¡Ztt! Rrrr-"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#0" value="Crck."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#1" value="Blddd…"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#2" value="Mhnnn."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Idle#3" value="Mrrrr."/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Shaken#0" value="¡Hnh!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:Victory#0" value="¡RRRRRRR!"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne:VictoryCount" value="1"/>	
 	<entry name="ChaosSpaceMarines/Havoc:Attack#0" value="¡Aniquilación!"/>
 	<entry name="ChaosSpaceMarines/Havoc:Attack#1" value="DISPARAD, COBARDES"/>
 	<entry name="ChaosSpaceMarines/Havoc:Attack#2" value="Una bala, dos balas, tres balas, cuatro…"/>
 	<entry name="ChaosSpaceMarines/Havoc:Attack#3" value="¡Por la Gloria del Caos! ¡Por el Mechanicum Oscuro! ¡Por el placer del disparar!"/>
 	<entry name="ChaosSpaceMarines/Havoc:Attack#4" value="¡Desde el infierno disparamos!"/>
 	<entry name="ChaosSpaceMarines/Havoc:AttackCount" value="5"/>
 	<entry name="ChaosSpaceMarines/Havoc:Broken#0" value="Nos salió mal…"/>
 	<entry name="ChaosSpaceMarines/Havoc:BrokenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Havoc:Hurt#0" value="No estoy muerto, seguiré disparando."/>
 	<entry name="ChaosSpaceMarines/Havoc:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Havoc:Idle#0" value="Cuando hablas con armas, la gente escucha, cuando hablas a las armas, la gente huye."/>
 	<entry name="ChaosSpaceMarines/Havoc:Idle#1" value="Mi sangre es plasma."/>
 	<entry name="ChaosSpaceMarines/Havoc:Idle#2" value="Bendita sea la Disformidad, que nos ofrece tanto."/>
 	<entry name="ChaosSpaceMarines/Havoc:IdleCount" value="3"/>
 	<entry name="ChaosSpaceMarines/Havoc:Shaken#0" value="Nuestras armas, no tuyas. De nuestros fríos dedos muertos…"/>
 	<entry name="ChaosSpaceMarines/Havoc:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Havoc:Victory#0" value="Disparad a la paz."/>
 	<entry name="ChaosSpaceMarines/Havoc:VictoryCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Heldrake:Attack#0" value="HhhrrraaaaaHHHH!"/>
 	<entry name="ChaosSpaceMarines/Heldrake:Attack#1" value="Graaarh."/>
 	<entry name="ChaosSpaceMarines/Heldrake:Attack#2" value="Arrrrrhhhh."/>
 	<entry name="ChaosSpaceMarines/Heldrake:AttackCount" value="3"/>
 	<entry name="ChaosSpaceMarines/Heldrake:Broken#0" value="Eeeeeeeeeee!"/>
 	<entry name="ChaosSpaceMarines/Heldrake:BrokenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Heldrake:Hurt#0" value="Arkh! Arkh!"/>
 	<entry name="ChaosSpaceMarines/Heldrake:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Heldrake:Idle#0" value="Hombre… una vez."/>
 	<entry name="ChaosSpaceMarines/Heldrake:Idle#1" value="Grrrrhhh."/>
 	<entry name="ChaosSpaceMarines/Heldrake:Idle#2" value="Rrrrrrr…"/>
 	<entry name="ChaosSpaceMarines/Heldrake:IdleCount" value="3"/>
 	<entry name="ChaosSpaceMarines/Heldrake:Shaken#0" value="Eeeeeekh."/>
 	<entry name="ChaosSpaceMarines/Heldrake:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Heldrake:Victory#0" value="HhhrrrrrraaaaaaaHHHHHH!"/>
 	<entry name="ChaosSpaceMarines/Heldrake:VictoryCount" value="1"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#0" value="¡Sangre para el Dios de la Sangre!"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#1" value="Khorne quiere tu craneo."/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#2" value="¡Mi hacha desea probar tu sangre!"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#3" value="¡No huyáis y luchad, cobardes!"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Attack#4" value="¡No hay honor en la muerte de un guerrero!"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:AttackCount" value="5"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Broken#0" value="¡Khorne nos maldecirá!"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:BrokenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Hurt#0" value="¡Mi… Mi sangre para el Dios de la sangre!"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Idle#0" value="¿POR QUÉ NO LUCHAS CONTRA MÍ?"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:IdleCount" value="1"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Shaken#0" value="¡No moriremos tan fácilmente!"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#0" value="¡SANGRE!"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#1" value="KHORNE TE LLAMA"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:Victory#2" value="TODO SON CRANEOS"/>
 	<entry name="ChaosSpaceMarines/KhorneBerzerker:VictoryCount" value="3"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#0" value="Por tu nombre verdadero, yo te ordeno."/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#1" value="¿Por qué temes? Una ruptura está cerca."/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Attack#2" value="Mi bastón está hambriento, y tu alma permanece…"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:AttackCount" value="3"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Broken#0" value="¿Qué vive pero no puede morir?"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession:Hurt#0" value="¿Es este mi hueso? Maldición, no hay."/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#0" value="Inútiles hombres y fantasmas… necesitamos una fuerza mejor."/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#1" value="Pronto la Athama, después el nombre real de Fatewalker…"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#2" value="La posesion es nueve partes del saber …"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Idle#3" value="Saborearán el corazón del planeta, antes de que nos acabe nuestro tiempo."/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:IdleCount" value="4"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Shaken#0" value="¿Qué es el miedo, sino el destructor de la mente?"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:Victory#0" value="Mis demonios susurran los unos con los otros. Todos te susurran."/>
 	<entry name="ChaosSpaceMarines/MasterOfPossession:VictoryCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Venomcrawler" value="ChaosSpaceMarines/Defiler"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:Attack#0" value="…presa…"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:AttackCount" value="1"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:Broken#0" value="…miedo…"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:BrokenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:Hurt#0" value="…dolor…"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:Idle#0" value="…"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:IdleCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Shaken#0" value="…miedo…"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/WarpTalon:Victory#0" value="…muerte…"/>
 	<entry name="ChaosSpaceMarines/WarpTalon:VictoryCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Attack#0" value="Diagnóstico del objetivo: tú… no eres perfecto."/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Attack#1" value="Cogitadores, mecatentáculos, armamento: desplegados."/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Attack#2" value="GOTO eliminar objetivo. Ejecutar."/>
 	<entry name="ChaosSpaceMarines/Warpsmith:AttackCount" value="3"/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Broken#0" value="Iniciar programa de limitación de daños."/>
 	<entry name="ChaosSpaceMarines/Warpsmith:BrokenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Hurt#0" value="Diagnóstico: ERROR orgánico/mecánico"/>
	<entry name="ChaosSpaceMarines/Warpsmith:HurtCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Idle#0" value="Ridiculo Herrero de Guerra. Ridiculo."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#1" value="Hombre, máquena, demonio. Todo junto, es la perfección."/>
	<entry name="ChaosSpaceMarines/Warpsmith:Idle#2" value="Queridos mecatentáculos…"/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Idle#3" value="Error: núcleo emocional de recuperación: Eliminar."/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Idle#4" value="Forja del Alma, Dioses, somos vuestras armas."/>
 	<entry name="ChaosSpaceMarines/Warpsmith:IdleCount" value="5"/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Shaken#0" value="Reporte: daño sufrido."/>
 	<entry name="ChaosSpaceMarines/Warpsmith:ShakenCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Warpsmith:Victory#0" value="Situación: oposición desactivada. Predecible."/>
 	<entry name="ChaosSpaceMarines/Warpsmith:VictoryCount" value="1"/>
 	<entry name="ChaosSpaceMarines/Helbrute" value="ChaosSpaceMarines/Defiler"/>
 	<entry name="ChaosSpaceMarines/Maulerfiend" value="ChaosSpaceMarines/Defiler"/>
 	<entry name="ChaosSpaceMarines/Obliterator" value="ChaosSpaceMarines/Havoc"/>
 	<entry name="AstraMilitarum/TempestusScion:Attack#0" value="Y así mueran todos los enemigos del Imperium."/>
 	<entry name="AstraMilitarum/TempestusScion:Attack#1" value="Herejes, xenos: todos morirán."/>
 	<entry name="AstraMilitarum/TempestusScion:Attack#2" value="Rifles infierno listos…"/>
 	<entry name="AstraMilitarum/TempestusScion:Attack#3" value="¡Nuestras vidas por Terra!"/>
 	<entry name="AstraMilitarum/TempestusScion:AttackCount" value="4"/>
 	<entry name="AstraMilitarum/TempestusScion:Broken#0" value="¡Qué verguenza! Las tropas de asalto se retiran."/>
 	<entry name="AstraMilitarum/TempestusScion:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/TempestusScion:Hurt#0" value="Daría encantado la vida por mi Emperador."/>
 	<entry name="AstraMilitarum/TempestusScion:HurtCount" value="1"/>
 	<entry name="AstraMilitarum/TempestusScion:Idle#0" value="Nos llaman los Chicos Gloriosos, pero la única gloria que perseguimos es la del Emperador."/>
 	<entry name="AstraMilitarum/TempestusScion:Idle#1" value="T¿El Regimental Standard? No es obligatorio leerlo, pero merece la pena."/>
 	<entry name="AstraMilitarum/TempestusScion:Idle#2" value="¿Has leído este artículo sobre Marbo? ¿Cómo puede un héroe ignorar así las regulaciones del Munitorum?"/>
 	<entry name="AstraMilitarum/TempestusScion:Idle#3" value="En el barullo, Descansar y Relajarse.Esta es la vida."/>
 	<entry name="AstraMilitarum/TempestusScion:IdleCount" value="4"/>
 	<entry name="AstraMilitarum/TempestusScion:Shaken#0" value="Pasé los Juicios de Sumisión. No huiré."/>
 	<entry name="AstraMilitarum/TempestusScion:ShakenCount" value="1"/>
 	<entry name="AstraMilitarum/TempestusScion:Victory#0" value="Deja a la Guardia celebrar. Esta guerra nuestra no tiene fin."/>
 	<entry name="AstraMilitarum/TempestusScion:VictoryCount" value="1"/>
	<entry name="Necrons/FlayedOne:Attack#0" value="…carne"/>
	<entry name="Necrons/FlayedOne:Attack#1" value="-rasgar-"/>
	<entry name="Necrons/FlayedOne:Attack#2" value="…desgarrar-romper-rasgar…"/>
	<entry name="Necrons/FlayedOne:AttackCount" value="3"/>
	<entry name="Necrons/FlayedOne:Broken#0" value="…essssscapar…"/>
	<entry name="Necrons/FlayedOne:BrokenCount" value="1"/>
	<entry name="Necrons/FlayedOne:Hurt#0" value="…herida superficial…"/>
	<entry name="Necrons/FlayedOne:HurtCount" value="1"/>
	<entry name="Necrons/FlayedOne:Idle#0" value="…hambre…"/>
	<entry name="Necrons/FlayedOne:Idle#1" value="…permaneced…"/>
	<entry name="Necrons/FlayedOne:Idle#2" value="…languidez…"/>
	<entry name="Necrons/FlayedOne:IdleCount" value="3"/>
	<entry name="Necrons/FlayedOne:Shaken#0" value="…Llandu'gor…!"/>
	<entry name="Necrons/FlayedOne:ShakenCount" value="1"/>
	<entry name="Necrons/FlayedOne:Victory#0" value="…vive otra vez…"/>
	<entry name="Necrons/FlayedOne:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Attack#0" value="Apoyando a la infantería con fuego."/>
	<entry name="AstraMilitarum/Chimera:Attack#1" value="Transporte de asalto."/>
	<entry name="AstraMilitarum/Chimera:Attack#2" value="Disparando con todo lo que tenemos."/>
	<entry name="AstraMilitarum/Chimera:AttackCount" value="3"/>
	<entry name="AstraMilitarum/Chimera:Broken#0" value="El blindado rápido se retira rápidamente…"/>
	<entry name="AstraMilitarum/Chimera:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Hurt#0" value="¡Necesitamos blindaje pesado!"/>
	<entry name="AstraMilitarum/Chimera:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Idle#0" value="Esperando órdenes"/>
	<entry name="AstraMilitarum/Chimera:Idle#1" value="¿Alguien a quién llevar?"/>
	<entry name="AstraMilitarum/Chimera:Idle#2" value="¿A quién le toca pulir la lente del multilaser?"/>
	<entry name="AstraMilitarum/Chimera:IdleCount" value="3"/>
	<entry name="AstraMilitarum/Chimera:Shaken#0" value="¡Saca a este tío de aquí!"/>
	<entry name="AstraMilitarum/Chimera:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Chimera:Victory#0" value="Tanque sorprendido."/>
	<entry name="AstraMilitarum/Chimera:VictoryCount" value="1"/>
	<entry name="Orks/Warbiker:Attack#0" value="¡EL KAÑÓN ROJO VA MÁZ RÁPIDO!"/>
	<entry name="Orks/Warbiker:Attack#1" value="Dakka dakka dakka!"/>
	<entry name="Orks/Warbiker:Attack#2" value="¡Dizparalez a todoz!"/>
	<entry name="Orks/Warbiker:AttackCount" value="3"/>
	<entry name="Orks/Warbiker:Broken#0" value="Máz rápido máz rápido máz rápido…"/>
	<entry name="Orks/Warbiker:BrokenCount" value="1"/>
	<entry name="Orks/Warbiker:Hurt#0" value="Oi, Baz, ¿eza era tu rueda o la mia?"/>
	<entry name="Orks/Warbiker:Hurt#1" value="De todoz modoz no nezezitamoz ezaz piezas."/>
	<entry name="Orks/Warbiker:HurtCount" value="2"/>
	<entry name="Orks/Warbiker:Idle#0" value="muvenoz, pezado de zako zog."/>
	<entry name="Orks/Warbiker:Idle#1" value="Vroom, vroom, dakka, dakka. Ke tiempoz."/>
	<entry name="Orks/Warbiker:IdleCount" value="2"/>
	<entry name="Orks/Warbiker:Shaken#0" value="Nezezitamoz algo de pintura azul, ¡rápido!"/>
	<entry name="Orks/Warbiker:ShakenCount" value="1"/>
	<entry name="Orks/Warbiker:Victory#0" value="WAAAGH! ¡kulto de la Velozidad! WAAAGH!"/>
	<entry name="Orks/Warbiker:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#0" value="Buscan la muerte a nuestra manos."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#1" value="¡Machacad sus cráneos bajo nuestra botas!"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Attack#2" value="Buscan la muerte a nuestra manos."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:AttackCount" value="3"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Broken#0" value="Los Dioses Oscuros nos maldecirán por esto…"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Hurt#0" value="No moriremos aquí,¡blandengues!"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Idle#0" value="Aguantando bastante bien durante diez mil años sin un servicio."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Idle#1" value="Los dioses oscuros nos protegen."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:IdleCount" value="2"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Shaken#0" value="Sálvese quien pueda…"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:Victory#0" value="Están muertos. Predecible."/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider:VictoryCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Attack#0" value="Disparando acelerador lineal."/>
	<entry name="Tau/TidewallGunrig:Attack#1" value="Emplazamiento activado."/>
	<entry name="Tau/TidewallGunrig:Attack#2" value="Estabilizado y listo para disparar."/>
	<entry name="Tau/TidewallGunrig:AttackCount" value="3"/>
	<entry name="Tau/TidewallGunrig:Broken#0" value="Soltamos las armas."/>
	<entry name="Tau/TidewallGunrig:BrokenCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Hurt#0" value="El muro de escudos esta fallando, emplazamiento comprometido."/>
	<entry name="Tau/TidewallGunrig:HurtCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Idle#0" value="¿Tienes algún tanque para que le disparemos?"/>
	<entry name="Tau/TidewallGunrig:Idle#1" value="Emplazamiento móvil listo para la acción."/>
	<entry name="Tau/TidewallGunrig:IdleCount" value="2"/>
	<entry name="Tau/TidewallGunrig:Shaken#0" value="¡El Tidewall no puede soportar esta cantidad de fuego!"/>
	<entry name="Tau/TidewallGunrig:ShakenCount" value="1"/>
	<entry name="Tau/TidewallGunrig:Victory#0" value="Objetivo… atomizado."/>
	<entry name="Tau/TidewallGunrig:VictoryCount" value="1"/>
	<entry name="Tyranids/ScythedHierodule" value="Tyranids/Carnifex"/>
	<entry name="SpaceMarines/Razorback" value="SpaceMarines/Hunter"/>
	<entry name="Neutral/Umbra:Attack#0" value="…llll…"/>
	<entry name="Neutral/Umbra:Attack#1" value="(linger)"/>
	<entry name="Neutral/Umbra:Attack#2" value="Linger…"/>
	<entry name="Neutral/Umbra:Attack#3" value="…inger…"/>
	<entry name="Neutral/Umbra:AttackCount" value="4"/>
	<entry name="Neutral/Umbra:Broken#0" value="Linger…"/>
	<entry name="Neutral/Umbra:BrokenCount" value="1"/>
	<entry name="Neutral/Umbra:Hurt#0" value="LIN-"/>
	<entry name="Neutral/Umbra:HurtCount" value="1"/>
	<entry name="Neutral/Umbra:Idle#0" value="…"/>
	<entry name="Neutral/Umbra:Idle#1" value="…linger"/>
	<entry name="Neutral/Umbra:Idle#2" value="linger…"/>
	<entry name="Neutral/Umbra:Idle#3" value="…ng…"/>
	<entry name="Neutral/Umbra:IdleCount" value="4"/>
	<entry name="Neutral/Umbra:Shaken#0" value="lin…ger…"/>
	<entry name="Neutral/Umbra:ShakenCount" value="1"/>
	<entry name="Neutral/Umbra:Victory#0" value="LINGER"/>
	<entry name="Neutral/Umbra:VictoryCount" value="1"/>
	<entry name="Eldar/DarkReaper:Attack#0" value="Se aprecia el arte de la destrucción."/>
	<entry name="Eldar/DarkReaper:Attack#1" value="Trayendo Muerte."/>
	<entry name="Eldar/DarkReaper:Attack#2" value="¡En el nombre de Khaine!"/>
	<entry name="Eldar/DarkReaper:Attack#3" value="No fallamos."/>
	<entry name="Eldar/DarkReaper:AttackCount" value="4"/>
	<entry name="Eldar/DarkReaper:Broken#0" value="Somos nosotros los que hemos de ser destruidos…"/>
	<entry name="Eldar/DarkReaper:BrokenCount" value="1"/>
	<entry name="Eldar/DarkReaper:Hurt#0" value="La destrucción nos alcanza a todos."/>
	<entry name="Eldar/DarkReaper:HurtCount" value="1"/>
	<entry name="Eldar/DarkReaper:Idle#0" value="Recordamos Altansar."/>
	<entry name="Eldar/DarkReaper:Idle#1" value="La destrucción no es más que otro camino en nuestras largas vidas."/>
	<entry name="Eldar/DarkReaper:Idle#2" value="La muerte sin miedo es nuestro terreno."/>
	<entry name="Eldar/DarkReaper:Idle#3" value="El Destructor acabó con el Dios-Herrero hace tiempo."/>
	<entry name="Eldar/DarkReaper:IdleCount" value="4"/>
	<entry name="Eldar/DarkReaper:Shaken#0" value="¡El Destructor se ha vuelto contra nosotros!"/>
	<entry name="Eldar/DarkReaper:ShakenCount" value="1"/>
	<entry name="Eldar/DarkReaper:Victory#0" value="El Destructor ha acabado con ellos."/>
	<entry name="Eldar/DarkReaper:VictoryCount" value="1"/>
	<entry name="Eldar/Wraithblade:Attack#0" value="…el enemigo caerá…"/>
	<entry name="Eldar/Wraithblade:Attack#1" value="…abraza la muerte…"/>
	<entry name="Eldar/Wraithblade:Attack#2" value="…por nuestro mundo astronave…"/>
	<entry name="Eldar/Wraithblade:Attack#3" value="…por los Aeldari…"/>
	<entry name="Eldar/Wraithblade:AttackCount" value="4"/>
	<entry name="Eldar/Wraithblade:Broken#0" value="…tememos a la Sedienta…"/>
	<entry name="Eldar/Wraithblade:BrokenCount" value="1"/>
	<entry name="Eldar/Wraithblade:Hurt#0" value="…morir, ¿de nuevo?"/>
	<entry name="Eldar/Wraithblade:HurtCount" value="1"/>
	<entry name="Eldar/Wraithblade:Idle#0" value="…necesitamos guía…"/>
	<entry name="Eldar/Wraithblade:Idle#1" value="…estamos muertos…"/>
	<entry name="Eldar/Wraithblade:Idle#2" value="…dejanos descansar…"/>
	<entry name="Eldar/Wraithblade:Idle#3" value="…vivimos para la guerra…"/>
	<entry name="Eldar/Wraithblade:IdleCount" value="4"/>
	<entry name="Eldar/Wraithblade:Shaken#0" value="…el temor está más allá de nosotros…"/>
	<entry name="Eldar/Wraithblade:ShakenCount" value="1"/>
	<entry name="Eldar/Wraithblade:Victory#0" value="…se han unido a nosotros…"/>
	<entry name="Eldar/Wraithblade:VictoryCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Attack#0" value="Disparando todos los sistemas"/>
	<entry name="Eldar/WaveSerpent:Attack#1" value="Si, estamos atacando"/>
	<entry name="Eldar/WaveSerpent:Attack#2" value="¿Deberíamos disparar nuestro escudo serpiente?"/>
	<entry name="Eldar/WaveSerpent:Attack#3" value="Transporte en combate."/>
	<entry name="Eldar/WaveSerpent:AttackCount" value="4"/>
	<entry name="Eldar/WaveSerpent:Broken#0" value="¡Sálvese quien pueda!"/>
	<entry name="Eldar/WaveSerpent:BrokenCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Hurt#0" value="Escudo penetrado."/>
	<entry name="Eldar/WaveSerpent:HurtCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Idle#0" value="Preparado para mover."/>
	<entry name="Eldar/WaveSerpent:Idle#1" value="Enrollado, listo para saltar."/>
	<entry name="Eldar/WaveSerpent:Idle#2" value="¿Quién necesita moverse?"/>
	<entry name="Eldar/WaveSerpent:Idle#3" value="Que mundo más maravilloso."/>
	<entry name="Eldar/WaveSerpent:IdleCount" value="4"/>
	<entry name="Eldar/WaveSerpent:Shaken#0" value="¡Sácanos de aquí!"/>
	<entry name="Eldar/WaveSerpent:ShakenCount" value="1"/>
	<entry name="Eldar/WaveSerpent:Victory#0" value="Recarga para el siguiente objetivo."/>
	<entry name="Eldar/WaveSerpent:VictoryCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#0" value="Batería guardián disparando."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#1" value="Tejiendo sombras."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Attack#2" value="Artillería preparada para moverse."/>
	<entry name="Eldar/VaulsWrathSupportBattery:AttackCount" value="3"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Broken#0" value="Podemos mantener la posición solos."/>
	<entry name="Eldar/VaulsWrathSupportBattery:BrokenCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Hurt#0" value="No sobreviviremos."/>
	<entry name="Eldar/VaulsWrathSupportBattery:HurtCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#0" value="Bobinas monofilamento."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#1" value="Comprobando los generadores anti-gravedad."/>
	<entry name="Eldar/VaulsWrathSupportBattery:Idle#2" value="Apoyemos desde la retaguardia."/>
	<entry name="Eldar/VaulsWrathSupportBattery:IdleCount" value="3"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Shaken#0" value="¡Tenemos que romper el enfrentamiento!"/>
	<entry name="Eldar/VaulsWrathSupportBattery:ShakenCount" value="1"/>
	<entry name="Eldar/VaulsWrathSupportBattery:Victory#0" value="Hemos… disuelto… la formación enemiga."/>
	<entry name="Eldar/VaulsWrathSupportBattery:VictoryCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Attack#0" value="Golpeamos más rápido de lo que tu ojo puede captar."/>
	<entry name="Eldar/ShiningSpear:Attack#1" value="¡Por Drastanta!"/>
	<entry name="Eldar/ShiningSpear:Attack#2" value="Palidecen ante nuestra virtud."/>
	<entry name="Eldar/ShiningSpear:Attack#3" value="¡Somos la lanza de Khaine!"/>
	<entry name="Eldar/ShiningSpear:AttackCount" value="4"/>
	<entry name="Eldar/ShiningSpear:Broken#0" value="La lanza está… rota."/>
	<entry name="Eldar/ShiningSpear:BrokenCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Hurt#0" value="Tenemos que ser más rápidos."/>
	<entry name="Eldar/ShiningSpear:HurtCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Idle#0" value="Somos leyenda."/>
	<entry name="Eldar/ShiningSpear:Idle#1" value="Esperamos para cargar."/>
	<entry name="Eldar/ShiningSpear:Idle#2" value="Lanzas listas."/>
	<entry name="Eldar/ShiningSpear:Idle#3" value="Anhelo quitarme esta máscara de guerra."/>
	<entry name="Eldar/ShiningSpear:IdleCount" value="4"/>
	<entry name="Eldar/ShiningSpear:Shaken#0" value="Cabalgamos a través del valle del miedo."/>
	<entry name="Eldar/ShiningSpear:ShakenCount" value="1"/>
	<entry name="Eldar/ShiningSpear:Victory#0" value="No nos han visto venir."/>
	<entry name="Eldar/ShiningSpear:VictoryCount" value="1"/>
	<entry name="Eldar/Ranger:Attack#0" value="Eligiendo objetivos…"/>
	<entry name="Eldar/Ranger:Attack#1" value="Morirán antes de saber que disparamos."/>
	<entry name="Eldar/Ranger:Attack#2" value="Mi rifle susurra muerte."/>
	<entry name="Eldar/Ranger:Attack#3" value="Nosotros. No. Fallamos."/>
	<entry name="Eldar/Ranger:AttackCount" value="4"/>
	<entry name="Eldar/Ranger:Broken#0" value="¡Retroceded! ¡Ya vienen!"/>
	<entry name="Eldar/Ranger:BrokenCount" value="1"/>
	<entry name="Eldar/Ranger:Hurt#0" value="Andamos en la oscuridad…"/>
	<entry name="Eldar/Ranger:HurtCount" value="1"/>
	<entry name="Eldar/Ranger:Idle#0" value="Nuestro exilio está hecho."/>
	<entry name="Eldar/Ranger:Idle#1" value="Nuestra cameleolina nos protege."/>
	<entry name="Eldar/Ranger:Idle#2" value="Es bueno estar en un sitio nuevo."/>
	<entry name="Eldar/Ranger:Idle#3" value="¡ven, escucha nuestros cuentos de la galaxia!"/>
	<entry name="Eldar/Ranger:IdleCount" value="4"/>
	<entry name="Eldar/Ranger:Shaken#0" value="Fuera del frente, rápido."/>
	<entry name="Eldar/Ranger:ShakenCount" value="1"/>
	<entry name="Eldar/Ranger:Victory#0" value="Se han ido y somos libres de seguir explorando."/>
	<entry name="Eldar/Ranger:VictoryCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Attack#0" value="Nuestros gritos anuncian tu muerte."/>
	<entry name="Eldar/HowlingBanshee:Attack#1" value="Caen como hierba bajo nuestros filos."/>
	<entry name="Eldar/HowlingBanshee:Attack#2" value="Los llamamos a un juicio final."/>
	<entry name="Eldar/HowlingBanshee:Attack#3" value="¡Rápidamente!"/>
	<entry name="Eldar/HowlingBanshee:AttackCount" value="4"/>
	<entry name="Eldar/HowlingBanshee:Broken#0" value="'Oíd nuestras lamentaciones!"/>
	<entry name="Eldar/HowlingBanshee:BrokenCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Hurt#0" value="¡Un dolor que es familiar!"/>
	<entry name="Eldar/HowlingBanshee:HurtCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Idle#0" value="Aullamos su condenación."/>
	<entry name="Eldar/HowlingBanshee:Idle#1" value="¡Ojala nuestros enemigos probasen nuestras espadas, ya!"/>
	<entry name="Eldar/HowlingBanshee:Idle#2" value="Somos los hijos de Morai-Heg."/>
	<entry name="Eldar/HowlingBanshee:Idle#3" value="Nuestra canción es la muerte."/>
	<entry name="Eldar/HowlingBanshee:IdleCount" value="4"/>
	<entry name="Eldar/HowlingBanshee:Shaken#0" value="¡Han interrumpido nuestra canción!"/>
	<entry name="Eldar/HowlingBanshee:ShakenCount" value="1"/>
	<entry name="Eldar/HowlingBanshee:Victory#0" value="Y solo los suspiros saludan a su desesperación."/>
	<entry name="Eldar/HowlingBanshee:VictoryCount" value="1"/>
	<entry name="Eldar/Headquarters:Attack#0" value="Defendiendo la Telaraña"/>
	<entry name="Eldar/Headquarters:Attack#1" value="¡No pasarás!"/>
	<entry name="Eldar/Headquarters:Attack#2" value="Reducto bajo ataque."/>
	<entry name="Eldar/Headquarters:Attack#3" value="Guardianes de la Telaraña."/>
	<entry name="Eldar/Headquarters:AttackCount" value="4"/>
	<entry name="Eldar/Headquarters:Broken#0" value="¡Nos están traspasando!"/>
	<entry name="Eldar/Headquarters:BrokenCount" value="1"/>
	<entry name="Eldar/Headquarters:Hurt#0" value="¡Khaine ayúdanos!"/>
	<entry name="Eldar/Headquarters:HurtCount" value="1"/>
	<entry name="Eldar/Headquarters:Idle#0" value="Vigilando la Telaraña."/>
	<entry name="Eldar/Headquarters:Idle#1" value="Esperamos listos, Vidente."/>
	<entry name="Eldar/Headquarters:Idle#2" value="Anhelo volver a mi mundo astronave, y quitarme esta máscara."/>
	<entry name="Eldar/Headquarters:Idle#3" value="¿Por qué estamos aquí?"/>
	<entry name="Eldar/Headquarters:IdleCount" value="4"/>
	<entry name="Eldar/Headquarters:Shaken#0" value="Temo a la muerte y al Gran Enemigo."/>
	<entry name="Eldar/Headquarters:ShakenCount" value="1"/>
	<entry name="Eldar/Headquarters:Victory#0" value="Esta puerta no se cerrará, aún."/>
	<entry name="Eldar/Headquarters:VictoryCount" value="1"/>
	<entry name="Eldar/Guardian:Attack#0" value="El deber nos llama."/>
	<entry name="Eldar/Guardian:Attack#1" value="No caeremos."/>
	<entry name="Eldar/Guardian:Attack#2" value="Los nuestros aguantan como uno solo."/>
	<entry name="Eldar/Guardian:Attack#3" value="Shurikens van."/>
	<entry name="Eldar/Guardian:AttackCount" value="4"/>
	<entry name="Eldar/Guardian:Broken#0" value="¿Es este el final…?"/>
	<entry name="Eldar/Guardian:BrokenCount" value="1"/>
	<entry name="Eldar/Guardian:Hurt#0" value="Nuestra condenación pende sobre nosotros."/>
	<entry name="Eldar/Guardian:HurtCount" value="1"/>
	<entry name="Eldar/Guardian:Idle#0" value="La guerra es… intensa."/>
	<entry name="Eldar/Guardian:Idle#1" value="¿Cuándo regresaremos a los salones de cristal?"/>
	<entry name="Eldar/Guardian:Idle#2" value="Asuryan, protégenos."/>
	<entry name="Eldar/Guardian:Idle#3" value="Vidente, espero que tu visión sea cierta."/>
	<entry name="Eldar/Guardian:IdleCount" value="4"/>
	<entry name="Eldar/Guardian:Shaken#0" value="¡Isha preservanos!"/>
	<entry name="Eldar/Guardian:ShakenCount" value="1"/>
	<entry name="Eldar/Guardian:Victory#0" value="Arrogante, alzarse contra los Aeldari."/>
	<entry name="Eldar/Guardian:VictoryCount" value="1"/>
	<entry name="Eldar/FirePrism:Attack#0" value="Los cortaremos."/>
	<entry name="Eldar/FirePrism:Attack#1" value="No pueden soportar nuestro fuego."/>
	<entry name="Eldar/FirePrism:Attack#2" value="Fundiros."/>
	<entry name="Eldar/FirePrism:Attack#3" value="Armas de los antiguos, no nos falléis."/>
	<entry name="Eldar/FirePrism:AttackCount" value="4"/>
	<entry name="Eldar/FirePrism:Broken#0" value="…no puede ser."/>
	<entry name="Eldar/FirePrism:BrokenCount" value="1"/>
	<entry name="Eldar/FirePrism:Hurt#0" value="Este daño no ha tenido consecuencias."/>
	<entry name="Eldar/FirePrism:HurtCount" value="1"/>
	<entry name="Eldar/FirePrism:Idle#0" value="Utilízanos."/>
	<entry name="Eldar/FirePrism:Idle#1" value="El anciano Vaul diseño esta máquina."/>
	<entry name="Eldar/FirePrism:Idle#2" value="¿Por qué tenemos que utilizar este armamento?"/>
	<entry name="Eldar/FirePrism:Idle#3" value="Los mundos astonave está tan lejos."/>
	<entry name="Eldar/FirePrism:IdleCount" value="4"/>
	<entry name="Eldar/FirePrism:Shaken#0" value="Podemos soportar su fuego."/>
	<entry name="Eldar/FirePrism:ShakenCount" value="1"/>
	<entry name="Eldar/FirePrism:Victory#0" value="Como se esperaba, han sido derrotados."/>
	<entry name="Eldar/FirePrism:VictoryCount" value="1"/>
	<entry name="Eldar/FireDragon:Attack#0" value="¡Arded!"/>
	<entry name="Eldar/FireDragon:Attack#1" value="¡Llamas ancestrales desatadas!"/>
	<entry name="Eldar/FireDragon:Attack#2" value="¡Estamos furiosos, ardientemente furiosos!"/>
	<entry name="Eldar/FireDragon:Attack#3" value="Acercaros, pequeños tanques…"/>
	<entry name="Eldar/FireDragon:AttackCount" value="4"/>
	<entry name="Eldar/FireDragon:Broken#0" value="El sueño del fuego se desvanece…"/>
	<entry name="Eldar/FireDragon:BrokenCount" value="1"/>
	<entry name="Eldar/FireDragon:Hurt#0" value="¡Nos hemos quemado a nosotros mismos!"/>
	<entry name="Eldar/FireDragon:HurtCount" value="1"/>
	<entry name="Eldar/FireDragon:Idle#0" value="Déjanos contar un cuento de tormentas de fuego y ceniza."/>
	<entry name="Eldar/FireDragon:Idle#1" value="Cenizas y furia."/>
	<entry name="Eldar/FireDragon:Idle#2" value="Un mundo envuelto en llamas."/>
	<entry name="Eldar/FireDragon:Idle#3" value="Hay nobleza en la incineración, ¿verdad?"/>
	<entry name="Eldar/FireDragon:IdleCount" value="4"/>
	<entry name="Eldar/FireDragon:Shaken#0" value="Estamos bastante apagados…."/>
	<entry name="Eldar/FireDragon:ShakenCount" value="1"/>
	<entry name="Eldar/FireDragon:Victory#0" value="Su rostro en la muerte es… ceniciento."/>
	<entry name="Eldar/FireDragon:VictoryCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Attack#0" value="¡Kurnous nos guía!"/>
	<entry name="Eldar/CrimsonHunter:Attack#1" value="¡De caza!"/>
	<entry name="Eldar/CrimsonHunter:Attack#2" value="Desde el aire damos… lecciones."/>
	<entry name="Eldar/CrimsonHunter:Attack#3" value="¡Somos los filos cegadores!"/>
	<entry name="Eldar/CrimsonHunter:AttackCount" value="4"/>
	<entry name="Eldar/CrimsonHunter:Broken#0" value="No es una retirada."/>
	<entry name="Eldar/CrimsonHunter:BrokenCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Hurt#0" value="¿Nos han… golpeado?"/>
	<entry name="Eldar/CrimsonHunter:HurtCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Idle#0" value="Tenemos un deseo… de velocidad."/>
	<entry name="Eldar/CrimsonHunter:Idle#1" value="Kurnous desea acción."/>
	<entry name="Eldar/CrimsonHunter:Idle#2" value="Este entorno es rico en objetivos…"/>
	<entry name="Eldar/CrimsonHunter:Idle#3" value="Nada es más mortal que la arrogancia en la batalla."/>
	<entry name="Eldar/CrimsonHunter:IdleCount" value="4"/>
	<entry name="Eldar/CrimsonHunter:Shaken#0" value="¡No hay tiempo para pensar!"/>
	<entry name="Eldar/CrimsonHunter:ShakenCount" value="1"/>
	<entry name="Eldar/CrimsonHunter:Victory#0" value="Su ego los sobrepasó."/>
	<entry name="Eldar/CrimsonHunter:VictoryCount" value="1"/>
	<entry name="Eldar/HemlockWraithfighter" value="Eldar/CrimsonHunter"/>
	<entry name="Eldar/Scorpion" value="Eldar/FirePrism"/>
	<entry name="Eldar/WarWalker" value="Eldar/Guardian"/>
	<entry name="Eldar/Wraithknight" value="Eldar/Wraithblade"/>
	<entry name="Eldar/Autarch:Attack#0" value="¡Enfrentate a todos los aspectos!"/>
	<entry name="Eldar/Autarch:Attack#1" value="Conozco a Khaine!"/>
	<entry name="Eldar/Autarch:Attack#2" value="¡Caído por mi mano"/>
	<entry name="Eldar/Autarch:Attack#3" value="¡Soy la guerra reencarnada!"/>
	<entry name="Eldar/Autarch:AttackCount" value="4"/>
	<entry name="Eldar/Autarch:Broken#0" value="Retirarse es parte de la guerra."/>
	<entry name="Eldar/Autarch:BrokenCount" value="1"/>
	<entry name="Eldar/Autarch:Hurt#0" value="Yo… quizás necesite otra reencarnación."/>
	<entry name="Eldar/Autarch:HurtCount" value="1"/>
	<entry name="Eldar/Autarch:Idle#0" value="Cegorach hace trabajar a los Autarcas ociosos."/>
	<entry name="Eldar/Autarch:Idle#1" value="Ahora recitaré la letanía de la batalla…"/>
	<entry name="Eldar/Autarch:Idle#2" value="Estoy perdido en la guerra."/>
	<entry name="Eldar/Autarch:Idle#3" value="No hay paz en mi mente."/>
	<entry name="Eldar/Autarch:IdleCount" value="4"/>
	<entry name="Eldar/Autarch:Shaken#0" value="¡Ja! ¡Me ponen a prueba!"/>
	<entry name="Eldar/Autarch:ShakenCount" value="1"/>
	<entry name="Eldar/Autarch:Victory#0" value="No volverán a poner a prueba mi paciencia."/>
	<entry name="Eldar/Autarch:VictoryCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Attack#0" value="Doy forma a tu futuro… es corto."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#1" value="Conmigo, lanzas brillantes."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#2" value="Mi arma bruja tiene hambre."/>
	<entry name="Eldar/FarseerSkyrunner:Attack#3" value="Soy el timonel de mi pueblo."/>
	<entry name="Eldar/FarseerSkyrunner:AttackCount" value="4"/>
	<entry name="Eldar/FarseerSkyrunner:Broken#0" value="No veo futuro."/>
	<entry name="Eldar/FarseerSkyrunner:BrokenCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Hurt#0" value="¡Debo continuar!"/>
	<entry name="Eldar/FarseerSkyrunner:HurtCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Idle#0" value="Las madejas se tensan."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#1" value="El futuro siempre está en movimiento."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#2" value="Veo el futuro y también le doy forma."/>
	<entry name="Eldar/FarseerSkyrunner:Idle#3" value="Anhelo el cristal y el circuito."/>
	<entry name="Eldar/FarseerSkyrunner:IdleCount" value="4"/>
	<entry name="Eldar/FarseerSkyrunner:Shaken#0" value="¡No estaba destinado a ser de esta manera!"/>
	<entry name="Eldar/FarseerSkyrunner:ShakenCount" value="1"/>
	<entry name="Eldar/FarseerSkyrunner:Victory#0" value="Caído. Como se preveía."/>
	<entry name="Eldar/FarseerSkyrunner:VictoryCount" value="1"/>
	<entry name="Eldar/Spiritseer:Attack#0" value="No hay paz."/>
	<entry name="Eldar/Spiritseer:Attack#1" value="¡Los muertos te llevan!"/>
	<entry name="Eldar/Spiritseer:Attack#2" value="Teme a mi espada."/>
	<entry name="Eldar/Spiritseer:Attack#3" value="¡Soy de una raza antigua!"/>
	<entry name="Eldar/Spiritseer:AttackCount" value="4"/>
	<entry name="Eldar/Spiritseer:Broken#0" value="Me retiro, no sea que me una a mis cargas."/>
	<entry name="Eldar/Spiritseer:BrokenCount" value="1"/>
	<entry name="Eldar/Spiritseer:Hurt#0" value="Mi vida se drena en el cristal……"/>
	<entry name="Eldar/Spiritseer:HurtCount" value="1"/>
	<entry name="Eldar/Spiritseer:Idle#0" value="Los muertos están a mi cuidado."/>
	<entry name="Eldar/Spiritseer:Idle#1" value="Os hablo a vosotros, mis difuntos amigos."/>
	<entry name="Eldar/Spiritseer:Idle#2" value="Está tranquilo, en los salones de los espíritus."/>
	<entry name="Eldar/Spiritseer:Idle#3" value="Escuchen el circuito infinito del planeta. ¡Susurra!"/>
	<entry name="Eldar/Spiritseer:IdleCount" value="4"/>
	<entry name="Eldar/Spiritseer:Shaken#0" value="Somos tan pocos los que quedamos con vida."/>
	<entry name="Eldar/Spiritseer:ShakenCount" value="1"/>
	<entry name="Eldar/Spiritseer:Victory#0" value="No puedo celebrar la muerte, ni siquiera la del enemigo."/>
	<entry name="Eldar/Spiritseer:VictoryCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#0" value="¡¿TE ATREVES A LUCHAR CON LA GUERRA ENCARNADA?!"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#1" value="LA INMOLACIÓN ES TU DESTINO"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#2" value="¡FUEGO!"/>
	<entry name="Eldar/AvatarOfKhaine:Attack#3" value="VENID, TONTOS, KHAINE OS QUIERE."/>
	<entry name="Eldar/AvatarOfKhaine:AttackCount" value="4"/>
	<entry name="Eldar/AvatarOfKhaine:Broken#0" value="NO HUIRÉ"/>
	<entry name="Eldar/AvatarOfKhaine:BrokenCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Hurt#0" value="¡ALIMENTAS MI FURIA!"/>
	<entry name="Eldar/AvatarOfKhaine:HurtCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#0" value="ESTE PLANETA ME DESGARRA"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#1" value="ESTA MANO EMPUÑA LA ESPADA"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#2" value="VAUL… MUERE"/>
	<entry name="Eldar/AvatarOfKhaine:Idle#3" value="HUNDIDO, SIGO LUCHANDO…"/>
	<entry name="Eldar/AvatarOfKhaine:IdleCount" value="4"/>
	<entry name="Eldar/AvatarOfKhaine:Shaken#0" value="ACERCAROS, MORTALES"/>
	<entry name="Eldar/AvatarOfKhaine:ShakenCount" value="1"/>
	<entry name="Eldar/AvatarOfKhaine:Victory#0" value="KHAINE LOS TIENE AHORA."/>
	<entry name="Eldar/AvatarOfKhaine:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Attack#0" value="Guía nuestra puntería, oh Emperador."/>
	<entry name="AstraMilitarum/Ratling:Attack#1" value="Tiradores de largo alcance."/>
	<entry name="AstraMilitarum/Ratling:Attack#2" value="Si podemos verlo, podemos alcanzarlo."/>
	<entry name="AstraMilitarum/Ratling:Attack#3" value="Como pescar peces en un barril."/>
	<entry name="AstraMilitarum/Ratling:AttackCount" value="4"/>	<entry name="AstraMilitarum/Ratling:Broken#0" value="¡Nos largamos de aquí!"/>
	<entry name="AstraMilitarum/Ratling:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Hurt#0" value="¡Sacadnos del frente!"/>
	<entry name="AstraMilitarum/Ratling:HurtCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Idle#0" value="¿Amasec de segundo? Con permiso."/>
	<entry name="AstraMilitarum/Ratling:Idle#1" value="Un Grox da buena comida, ¿sabes?"/>
	<entry name="AstraMilitarum/Ratling:Idle#2" value="¿Esto? ¡No lo cogí! ¡No se lo digas al Comisario!"/>
	<entry name="AstraMilitarum/Ratling:Idle#3" value="Sube la apuesta y no se lo digas al sargento."/>
	<entry name="AstraMilitarum/Ratling:IdleCount" value="4"/>
	<entry name="AstraMilitarum/Ratling:Shaken#0" value="Mantennos a salvo y te mantendremos a salvo."/>
	<entry name="AstraMilitarum/Ratling:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/Ratling:Victory#0" value="¡En todo el ojo! Eso se merece raciones extra."/>
	<entry name="AstraMilitarum/Ratling:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#0" value="¡En nombre de los Dioses Oscuros!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#1" value="¡Gloria a los cuatro!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#2" value="¡Nuestras almas están perdidas!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Attack#3" value="Sangre para el señor de la sangre."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Broken#0" value="¡Dioses, protegednos!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Hurt#0" value="¡Un sacrificio para el Caos Absoluto!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#0" value="Alguna vez te preguntas…nada, déjalo estar."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#1" value="Nurgle, señor de las moscas."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#2" value="A Tzeentch, cambiador de caminos."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Idle#3" value="Por la gloria del Perfecto Príncipe."/>
	<entry name="ChaosSpaceMarines/ChaosCultist:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Shaken#0" value="¿Dónde están nuestros dioses?"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:Victory#0" value="¡Gloria, gloria, gloria!"/>
	<entry name="ChaosSpaceMarines/ChaosCultist:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#0" value="¡Siente mi latigazo!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#1" value="El abismo llama."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#2" value="Ningún pecado sin cometer."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Attack#3" value="¡Por la gloria de Lorgar!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Broken#0" value="Veo en la disformidad, oh…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Hurt#0" value="¡Tan sagrado éxtasis!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#0" value="Un sacrificio ritual lleva su tiempo."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#1" value="La Humanidad está condenada. El Caos es nuestra salvación."/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#2" value="¡A través del Caos, la vida eterna!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Idle#3" value="¡La verdad del universo está a nuestro alcance!"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Shaken#0" value="Tales misterios aún por descubrir…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:Victory#0" value="Extrañas visiones me asaltan…"/>
	<entry name="ChaosSpaceMarines/DarkDisciple:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#0" value="¡Se atreven a presentarse ante nosotros!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#1" value="¡Idiotas, carne de cañón!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#2" value="¡Haremos un osario con vuestros huesos!"/>
	<entry name="ChaosSpaceMarines/Headquarters:Attack#3" value="¡Guerra interminable!"/>
	<entry name="ChaosSpaceMarines/Headquarters:AttackCount" value="4"/>
	<entry name="ChaosSpaceMarines/Headquarters:Broken#0" value="¡Imposible!"/>
	<entry name="ChaosSpaceMarines/Headquarters:BrokenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Hurt#0" value="¡Han atravesado las murallas!"/>
	<entry name="ChaosSpaceMarines/Headquarters:HurtCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#0" value="Liquidando leales."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#1" value="Ojos sin cráneos."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#2" value="Cráneos sin ojos."/>
	<entry name="ChaosSpaceMarines/Headquarters:Idle#3" value="La gente se inclina ante nosotros."/>
	<entry name="ChaosSpaceMarines/Headquarters:IdleCount" value="4"/>
	<entry name="ChaosSpaceMarines/Headquarters:Shaken#0" value="¡No podemos fallar, necrófilos!"/>
	<entry name="ChaosSpaceMarines/Headquarters:ShakenCount" value="1"/>
	<entry name="ChaosSpaceMarines/Headquarters:Victory#0" value="Así mueren todos los que se nos oponen."/>
	<entry name="ChaosSpaceMarines/Headquarters:VictoryCount" value="1"/>
	<entry name="Eldar/Warlock:Attack#0" value="Lanza cantarina, muerte en duelo."/>
	<entry name="Eldar/Warlock:Attack#1" value="Las llamas de nuestra furia los purifican."/>
	<entry name="Eldar/Warlock:Attack#2" value="Nuestras lanzas cantan."/>
	<entry name="Eldar/Warlock:Attack#3" value="La mente es nuestra mayor arma."/>
	<entry name="Eldar/Warlock:AttackCount" value="4"/>
	<entry name="Eldar/Warlock:Broken#0" value="Volvamos, al Mundo Forja."/>
	<entry name="Eldar/Warlock:BrokenCount" value="1"/>
	<entry name="Eldar/Warlock:Hurt#0" value="Nuestro cuadro de mando cae."/>
	<entry name="Eldar/Warlock:HurtCount" value="1"/>
	<entry name="Eldar/Warlock:Idle#0" value="Guerreros y videntes, ambos."/>
	<entry name="Eldar/Warlock:Idle#1" value="El gran enemigo espera en la disformidad."/>
	<entry name="Eldar/Warlock:Idle#2" value="Añoro las flores de cristal."/>
	<entry name="Eldar/Warlock:Idle#3" value="Añoro las máscaras de mi Mundo Forja."/>
	<entry name="Eldar/Warlock:IdleCount" value="4"/>
	<entry name="Eldar/Warlock:Shaken#0" value="Fortalécenos, flaqueamos."/>
	<entry name="Eldar/Warlock:ShakenCount" value="1"/>
	<entry name="Eldar/Warlock:Victory#0" value="Previmos su caída."/>
	<entry name="Eldar/Warlock:VictoryCount" value="1"/>
	<entry name="Necrons/CanoptekWraith" value="Necrons/CanoptekScarab"/>
	<entry name="Orks/KillBursta" value="Orks/Battlewagon"/>
	<entry name="Orks/KrootoxRider" value="Neutral/KrootHound"/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#0" value="Apuntando al blindaje, Alabado sea el Emperador."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#1" value="Concentrando fuego."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#2" value="Especialistas de asedio en posición."/>
	<entry name="SpaceMarines/DevastatorCenturion:Attack#3" value="Largo alcance, pocas probabilidades."/>
	<entry name="SpaceMarines/DevastatorCenturion:AttackCount" value="4"/>
	<entry name="SpaceMarines/DevastatorCenturion:Broken#0" value="Las armaduras aguantan, pero nos retiramos."/>
	<entry name="SpaceMarines/DevastatorCenturion:BrokenCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Hurt#0" value="Ha penetrado el blindaje…ambos."/>
	<entry name="SpaceMarines/DevastatorCenturion:HurtCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#0" value="Avanzando, lentamente."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#1" value="Quitando una de las armaduras."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#2" value="Ojalá nuestros antepasados hubieran tenido estas armaduras durante la Herejía."/>
	<entry name="SpaceMarines/DevastatorCenturion:Idle#3" value="Preparando armadura de asedio."/>
	<entry name="SpaceMarines/DevastatorCenturion:IdleCount" value="4"/>
	<entry name="SpaceMarines/DevastatorCenturion:Shaken#0" value="Armadura bajo fuego, pero aguantando."/>
	<entry name="SpaceMarines/DevastatorCenturion:ShakenCount" value="1"/>
	<entry name="SpaceMarines/DevastatorCenturion:Victory#0" value="Están…devastados."/>
	<entry name="SpaceMarines/DevastatorCenturion:VictoryCount" value="1"/>
	<entry name="Tau/TigerShark" value="RazorsharkStrikeFighter"/>
	<entry name="Tyranids/Venomthrope" value="Tyranids/Carnifex"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#0" value="Shhhhhh."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#1" value="Aullido: Transónicos."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#2" value="Cubierto: Espectro Neuroestático."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Attack#3" value="Tullido: Enemigo."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:AttackCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Broken#0" value="Coraje: Perdido"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:BrokenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Hurt#0" value="Muriendo: De nuevo"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:HurtCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#0" value="Inactivo: Inactivo."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#1" value="Petición de Datos: Cintas de Relajación."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#2" value="Aprender: Lingua Tecnis."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Idle#3" value="Confesión: Contendiente Potencial."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:IdleCount" value="4"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Shaken#0" value="Peligro: Peligro."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:ShakenCount" value="1"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:Victory#0" value="Orgullo: Triunfo."/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator:VictoryCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#0" value="Ueeeee, takkatakkatakka"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#1" value="Nah, no huyáiz. ¡Sed perforadoz!"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#2" value="¡DAZLE A TODOZ LOS BOTONEZ!"/>
	<entry name="Orks/MegatrakkScrapjet:Attack#3" value="¡WAAAGH LOZ CHIKOZ VOLADOREZ!"/>
	<entry name="Orks/MegatrakkScrapjet:AttackCount" value="4"/>
	<entry name="Orks/MegatrakkScrapjet:Broken#0" value="¡¿Eztrelladoz, otra vez?!"/>
	<entry name="Orks/MegatrakkScrapjet:BrokenCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Hurt#0" value="¡Han reventado laz puertaz!"/>
	<entry name="Orks/MegatrakkScrapjet:HurtCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#0" value="¡El jefe dijo ke eztoy caztigado! ¡Ze va a enterar!"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#1" value="Ojalá eztar ahí arriba, dizparando hacia akí abajo"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#2" value="¡¿POR KÉ NO NOZ MOVEMOZ?!"/>
	<entry name="Orks/MegatrakkScrapjet:Idle#3" value="Hmm. Necezitamoz máz bombaz."/>
	<entry name="Orks/MegatrakkScrapjet:IdleCount" value="4"/>
	<entry name="Orks/MegatrakkScrapjet:Shaken#0" value="¿Fuego antiaéreo? ¿EZO EZ TODO LO QUE TIENEZ?"/>
	<entry name="Orks/MegatrakkScrapjet:ShakenCount" value="1"/>
	<entry name="Orks/MegatrakkScrapjet:Victory#0" value="LOZ ORKOZ ZON LOZ MEJOREZ, taládrazelo."/>
	<entry name="Orks/MegatrakkScrapjet:VictoryCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Attack#0" value="Mirad cómo se derriten."/>
	<entry name="AstraMilitarum/DevilDog:Attack#1" value="Cenizas a las cenizas."/>
	<entry name="AstraMilitarum/DevilDog:Attack#2" value="¡Aquí llegan los Devil Dogs! ¡Hurra!"/>
	<entry name="AstraMilitarum/DevilDog:Attack#3" value="La fusión nunca se sintió tan bien."/>
	<entry name="AstraMilitarum/DevilDog:AttackCount" value="4"/>
	<entry name="AstraMilitarum/DevilDog:Broken#0" value="¡Este trasto va a explotar!"/>
	<entry name="AstraMilitarum/DevilDog:BrokenCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Hurt#0" value="¡Mantenedlos lejos de los tanques de prometio!"/>
	<entry name="AstraMilitarum/DevilDog:HurtCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Idle#0" value="¿Qué se cocina?"/>
	<entry name="AstraMilitarum/DevilDog:Idle#1" value="¿Cuándo llegan los Hellhounds?"/>
	<entry name="AstraMilitarum/DevilDog:Idle#2" value="Bane Wolves…esos tíos me asustan."/>
	<entry name="AstraMilitarum/DevilDog:Idle#3" value="Hace frío cuando los cañones no disparan."/>
	<entry name="AstraMilitarum/DevilDog:IdleCount" value="4"/>
	<entry name="AstraMilitarum/DevilDog:Shaken#0" value="¡Pisa el Vulvanor y sácanos fuera del alcance!"/>
	<entry name="AstraMilitarum/DevilDog:ShakenCount" value="1"/>
	<entry name="AstraMilitarum/DevilDog:Victory#0" value="Aire sobrecalentado…huele a victoria."/>
	<entry name="AstraMilitarum/DevilDog:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="ChaosSpaceMarines/Defiler"/>
	<entry name="Tyranids/HiveGuard" value="Tyranids/Carnifex"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="AdeptusMechanicus/SkitariiVanguard"/>
	<entry name="SpaceMarines/Scout" value="SpaceMarines/TacticalSpaceMarine"/>
	<entry name="Necrons/GhostArk" value="Necrons/Monolith"/>
	<entry name="Eldar/Hornet" value="Eldar/FirePrism"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#0" value="Caza Bombardero Avenger, reportando."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#1" value="Ametrallando al enemigo, Canonesa."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#2" value="Muerte desde los cielos, Señora."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Attack#3" value="Nos quedamos sin munición."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AttackCount" value="4"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Broken#0" value="¡Maniobras evasivas!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Hurt#0" value="Deben de tener AA tras ellos."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:HurtCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#0" value="Tenemos el sol a nuestra espalda."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#1" value="Traslado inusual, señora."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#2" value="¡Volando alto!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Idle#3" value="¡Contento de estar vivo!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:IdleCount" value="4"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Shaken#0" value="Desde aquí arriba…el horror."/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:Victory#0" value="¡Uno más para los ases navales!"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="¿Es…es la Santa?"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Attack#0" value="En nombre del Emperador."/>
	<entry name="SistersOfBattle/BattleSister:Attack#1" value="El Dios-Emperador guía mi bólter."/>
	<entry name="SistersOfBattle/BattleSister:Attack#2" value="¡Liberando Su ira divina!"/>
	<entry name="SistersOfBattle/BattleSister:Attack#3" value="¡No permitáis vivir al hereje!"/>
	<entry name="SistersOfBattle/BattleSister:AttackCount" value="4"/>
	<entry name="SistersOfBattle/BattleSister:Broken#0" value="Sin fe, no somos nada…"/>
	<entry name="SistersOfBattle/BattleSister:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Hurt#0" value="Sí, aunque sus hojas corten, no temo a nada."/>
	<entry name="SistersOfBattle/BattleSister:HurtCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Idle#0" value="Somos los instrumentos de Salvación."/>
	<entry name="SistersOfBattle/BattleSister:Idle#1" value="Oremos, Hermanas."/>
	<entry name="SistersOfBattle/BattleSister:Idle#2" value="No deberíamos descansar mientras obra el mal."/>
	<entry name="SistersOfBattle/BattleSister:Idle#3" value="Tanta, tanta herejía…tanto trabajo."/>
	<entry name="SistersOfBattle/BattleSister:IdleCount" value="4"/>
	<entry name="SistersOfBattle/BattleSister:Shaken#0" value="In nomine Imperia…"/>
	<entry name="SistersOfBattle/BattleSister:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:Victory#0" value="Descansa, enemigo."/>
	<entry name="SistersOfBattle/BattleSister:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/BattleSister:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="¿La Santa…? ¡Donde vaya, la seguiremos!"/>
	<entry name="SistersOfBattle/BattleSister:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Attack#0" value="Otorgadles la paz del Emperador."/>
	<entry name="SistersOfBattle/Canoness:Attack#1" value="¡Herejes!"/>
	<entry name="SistersOfBattle/Canoness:Attack#2" value="Prueba mi redención."/>
	<entry name="SistersOfBattle/Canoness:Attack#3" value="¡Siente mi oración!"/>
	<entry name="SistersOfBattle/Canoness:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Canoness:Broken#0" value="Mis…hermanas…"/>
	<entry name="SistersOfBattle/Canoness:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Hurt#0" value="¡Sigo…luchando!"/>
	<entry name="SistersOfBattle/Canoness:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Idle#0" value="El Caos se aprovecha de las manos ociosas."/>
	<entry name="SistersOfBattle/Canoness:Idle#1" value="No carecemos de fallos. Pero conocemos nuestro pecado."/>
	<entry name="SistersOfBattle/Canoness:Idle#2" value="La sagrada trinidad son mis herramientas…bólter, lanzallamas, arma de fusión."/>
	<entry name="SistersOfBattle/Canoness:Idle#3" value="La redención vale cualquier precio, mis hermanas."/>
	<entry name="SistersOfBattle/Canoness:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Canoness:Shaken#0" value="Yo…no puedo."/>
	<entry name="SistersOfBattle/Canoness:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:Victory#0" value="La absolución es tuya."/>
	<entry name="SistersOfBattle/Canoness:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Canoness:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Yo lidero, pero Ella…Ella inspira."/>
	<entry name="SistersOfBattle/Canoness:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Attack#0" value="…una poderosa fortaleza es el Emperador…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#1" value="…levantaos, oh Adepta Sororitas…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#2" value="…oh, Emperador, que sois nuestra luz y día…"/>
	<entry name="SistersOfBattle/Exorcist:Attack#3" value="…Escucha con atención, el alegre sonido de nuestras armas."/>
	<entry name="SistersOfBattle/Exorcist:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Exorcist:Broken#0" value="…Emperador, ten piedad."/>
	<entry name="SistersOfBattle/Exorcist:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Hurt#0" value="…desde las profundidades de la aflicción, clamo a ti…"/>
	<entry name="SistersOfBattle/Exorcist:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Idle#0" value="¡El Emperador vive!"/>
	<entry name="SistersOfBattle/Exorcist:Idle#1" value="¡Gloria! ¡Gloria! ¡Gloria!"/>
	<entry name="SistersOfBattle/Exorcist:Idle#2" value="El Emperador ha liberado a su rápida hoja."/>
	<entry name="SistersOfBattle/Exorcist:Idle#3" value="Tiembla, hombre, de terror."/>
	<entry name="SistersOfBattle/Exorcist:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Exorcist:Shaken#0" value="Emperador, tu sangre y justicia nos preservarán."/>
	<entry name="SistersOfBattle/Exorcist:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:Victory#0" value="…aplasta a la serpiente y continua…"/>
	<entry name="SistersOfBattle/Exorcist:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Exorcist:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="¡…observad el maravilloso misterio de la Santa en Vida…!"/>
	<entry name="SistersOfBattle/Exorcist:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#0" value="Acercaos, herejes."/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#1" value="¡Sentid la ira del Emperador!"/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#2" value="La fe es nuestro escudo."/>
	<entry name="SistersOfBattle/CelestianSacresant:Attack#3" value="Te enviaremos a tu descanso."/>
	<entry name="SistersOfBattle/CelestianSacresant:AttackCount" value="4"/>
	<entry name="SistersOfBattle/CelestianSacresant:Broken#0" value="No podemos completarlo. Debemos-"/>
	<entry name="SistersOfBattle/CelestianSacresant:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Hurt#0" value="Amenazan nuestra misión…"/>
	<entry name="SistersOfBattle/CelestianSacresant:HurtCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#0" value="Hemos viajado a través de la galaxia para acabar…aquí."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#1" value="Nuestra armadura, impenetrable."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#2" value="Nuestra virtud, indiscutible."/>
	<entry name="SistersOfBattle/CelestianSacresant:Idle#3" value="Somos el modelo al cual todos aspiran."/>
	<entry name="SistersOfBattle/CelestianSacresant:IdleCount" value="4"/>
	<entry name="SistersOfBattle/CelestianSacresant:Shaken#0" value="Ni un paso atrás."/>
	<entry name="SistersOfBattle/CelestianSacresant:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:Victory#0" value="Nuestra misión está completa."/>
	<entry name="SistersOfBattle/CelestianSacresant:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/CelestianSacresant:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="¡Nuestra homónima, nuestra señora! Nuestra búsqueda ha llegado a su fin."/>
	<entry name="SistersOfBattle/CelestianSacresant:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#0" value="¡Por el honor del Questor Mechanicus!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#1" value="No somos más que un arma en la mano derecha del Emperador."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#2" value="¡Temed mi carga!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Attack#3" value="¡Caballeros, adelante!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AttackCount" value="4"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Broken#0" value="¿Qué subterfugio…?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Hurt#0" value="La velocidad será mi armadura."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:HurtCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#0" value="Me oxido, mientras vacilas."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#1" value="¡Canonesa, utilízame!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#2" value="Mi lanza necesita empleo."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Idle#3" value="¡El espíritu-máquina ansía la gloria!"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:IdleCount" value="4"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Shaken#0" value="¿Dónde está la gloria en esto?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:Victory#0" value="No pudieron oponerse a mi furia."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="¿Una…Santa en Vida?"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/CerastusKnightLancer:EnemyUnit:AdeptusMechanicus/KnightCrusader#0" value="¡Un enemigo digno! Ven, deja que me acerque."/>
	<entry name="SistersOfBattle/CerastusKnightLancer:EnemyUnit:AdeptusMechanicus/KnightCrusaderCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Attack#0" value="Y, sí, el Emperador vio sus muertes y fue bueno."/>
	<entry name="SistersOfBattle/Dialogus:Attack#1" value="¡No descanséis mis hermanas, decid que no hay más!"/>
	<entry name="SistersOfBattle/Dialogus:Attack#2" value="No preguntéis al hereje cuando puede hablar un lanzallamas."/>
	<entry name="SistersOfBattle/Dialogus:Attack#3" value="¡Estoy contigo, juntas!"/>
	<entry name="SistersOfBattle/Dialogus:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Dialogus:Broken#0" value="La penitencia es su propio castigo."/>
	<entry name="SistersOfBattle/Dialogus:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Hurt#0" value="¿Qué es una herida más para el fiel?"/>
	<entry name="SistersOfBattle/Dialogus:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Idle#0" value="Voco-transmisores: comprobados. Sensoria: comprobada."/>
	<entry name="SistersOfBattle/Dialogus:Idle#1" value="Descansad hoy, pues mañana estaremos con Él."/>
	<entry name="SistersOfBattle/Dialogus:Idle#2" value="El Emperador os ama a todos."/>
	<entry name="SistersOfBattle/Dialogus:Idle#3" value="No temáis al xenos."/>
	<entry name="SistersOfBattle/Dialogus:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Dialogus:Shaken#0" value="Aunque camino a través de la muerte y las fatigas…"/>
	<entry name="SistersOfBattle/Dialogus:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:Victory#0" value="Túmbate, para que pueda caminar más alto"/>
	<entry name="SistersOfBattle/Dialogus:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Dialogus:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Las palabras…me faltan. ¡Celestine misma!"/>
	<entry name="SistersOfBattle/Dialogus:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Attack#0" value="Escuchad, el canto del coro de los fieles."/>
	<entry name="SistersOfBattle/Headquarters:Attack#1" value="Nuestras murallas están a punto."/>
	<entry name="SistersOfBattle/Headquarters:Attack#2" value="Nuestras capillas no están indefensas."/>
	<entry name="SistersOfBattle/Headquarters:Attack#3" value="Te equivocas si nos consideras débiles."/>
	<entry name="SistersOfBattle/Headquarters:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Headquarters:Broken#0" value="¿Nuestras murallas…caídas?"/>
	<entry name="SistersOfBattle/Headquarters:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Hurt#0" value="La capilla ha sido violada."/>
	<entry name="SistersOfBattle/Headquarters:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Idle#0" value="Ofreciendo plegarias al Dios-Emperador."/>
	<entry name="SistersOfBattle/Headquarters:Idle#1" value="Purgando herejes."/>
	<entry name="SistersOfBattle/Headquarters:Idle#2" value="Dispersando cenizas sagradas."/>
	<entry name="SistersOfBattle/Headquarters:Idle#3" value="Entrenando novicias."/>
	<entry name="SistersOfBattle/Headquarters:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Headquarters:Shaken#0" value="¡Tras las murallas, deprisa!"/>
	<entry name="SistersOfBattle/Headquarters:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:Victory#0" value="No probéis nuestras murallas o nuestra fe."/>
	<entry name="SistersOfBattle/Headquarters:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Headquarters:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Oh, nos visita. ¡Celestine!"/>
	<entry name="SistersOfBattle/Headquarters:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Attack#0" value="Mi instrumental daña igual que cura."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#1" value="Os odiamos, afrentas a la fe."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#2" value="Permaneced firmes en nombre del Emperador."/>
	<entry name="SistersOfBattle/Hospitaller:Attack#3" value="La dicha marcial es nuestra recompensa."/>
	<entry name="SistersOfBattle/Hospitaller:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Hospitaller:Broken#0" value="Debo retirarme, para preservar a las jóvenes."/>
	<entry name="SistersOfBattle/Hospitaller:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Hurt#0" value="Médico…curaos."/>
	<entry name="SistersOfBattle/Hospitaller:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Idle#0" value="En la fuerza, vida."/>
	<entry name="SistersOfBattle/Hospitaller:Idle#1" value="Nuestras heridas son testamento de nuestro buen trabajo."/>
	<entry name="SistersOfBattle/Hospitaller:Idle#2" value="¿Dónde debería erigir nuestras capillas?"/>
	<entry name="SistersOfBattle/Hospitaller:Idle#3" value="El odio no es pecado cuando el universo es tan asqueroso."/>
	<entry name="SistersOfBattle/Hospitaller:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Hospitaller:Shaken#0" value="Mi rol es sanar…¿qué hago aquí?"/>
	<entry name="SistersOfBattle/Hospitaller:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:Victory#0" value="Mi toque curativo llega un poco tarde para este."/>
	<entry name="SistersOfBattle/Hospitaller:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Hospitaller:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Mis heridas…se cierran solas. ¿Cómo?"/>
	<entry name="SistersOfBattle/Hospitaller:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Attack#0" value="Y Celestine los castigó y vió que era bueno."/>
	<entry name="SistersOfBattle/Imagifier:Attack#1" value="¡Y fueron los muertos glorificados!"/>
	<entry name="SistersOfBattle/Imagifier:Attack#2" value="¡Su ira sagrada, oh mis hermanas!"/>
	<entry name="SistersOfBattle/Imagifier:Attack#3" value="¡Desafiantes, se plantaron, ante el enemigo!"/>
	<entry name="SistersOfBattle/Imagifier:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Imagifier:Broken#0" value="Mi canto…se debilita…"/>
	<entry name="SistersOfBattle/Imagifier:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Hurt#0" value="¡Heridas horribles sufrieron, pero avanzaron!"/>
	<entry name="SistersOfBattle/Imagifier:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Idle#0" value="Porto el Simulacrum Imperialis."/>
	<entry name="SistersOfBattle/Imagifier:Idle#1" value="En momentos breves, invocaron el amor del Emperador."/>
	<entry name="SistersOfBattle/Imagifier:Idle#2" value="…Y Celestine supo que su momento había llegado…"/>
	<entry name="SistersOfBattle/Imagifier:Idle#3" value="El architraidor atrapó al ángel caido con una garra…"/>
	<entry name="SistersOfBattle/Imagifier:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Imagifier:Shaken#0" value="Y sellaron la Puerta de la Eternidad y se acobardaron.."/>
	<entry name="SistersOfBattle/Imagifier:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Imagifier:Victory#0" value="¡Y regresaron a la Sagrada Terra como vencedores!"/>
	<entry name="SistersOfBattle/Imagifier:VictoryCount" value="1"/>	
	<entry name="SistersOfBattle/Mortifier:Attack#0" value="dejadmesalirdejadmesalirdejadmesalir"/>
	<entry name="SistersOfBattle/Mortifier:Attack#1" value="losientolosientolosiento"/>
	<entry name="SistersOfBattle/Mortifier:Attack#2" value="mueremueremuere"/>
	<entry name="SistersOfBattle/Mortifier:Attack#3" value="¿Por qué no puedo morir?"/>
	<entry name="SistersOfBattle/Mortifier:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Mortifier:Broken#0" value="Rómpeme más, para escapar de aquí."/>
	<entry name="SistersOfBattle/Mortifier:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Hurt#0" value="Por favor, muerte, ahora, sí."/>
	<entry name="SistersOfBattle/Mortifier:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Idle#0" value="Emperador, libérame."/>
	<entry name="SistersOfBattle/Mortifier:Idle#1" value="Déjame morir."/>
	<entry name="SistersOfBattle/Mortifier:Idle#2" value="Soy una pecadora, pero esto-"/>
	<entry name="SistersOfBattle/Mortifier:Idle#3" value="-tormento-"/>
	<entry name="SistersOfBattle/Mortifier:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Mortifier:Shaken#0" value="¡Traed la muerte más cerca!"/>
	<entry name="SistersOfBattle/Mortifier:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:Victory#0" value="Son libres…para mí, no hay nada."/>
	<entry name="SistersOfBattle/Mortifier:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Mortifier:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Por favor…por favor…"/>
	<entry name="SistersOfBattle/Mortifier:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#0" value="Un día, la humanidad se librará de todo esto."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#1" value="Traigo salvación…a través de la aniquilación."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#2" value="Soy una herramienta del Emperador, para guiarme como Él plazca."/>
	<entry name="SistersOfBattle/SaintCelestine:Attack#3" value="Él conoce mi camino."/>
	<entry name="SistersOfBattle/SaintCelestine:AttackCount" value="4"/>
	<entry name="SistersOfBattle/SaintCelestine:Broken#0" value="No puedo desfallecer."/>
	<entry name="SistersOfBattle/SaintCelestine:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Hurt#0" value="La muerte nunca es el final. ¿Por qué temerla?"/>
	<entry name="SistersOfBattle/SaintCelestine:HurtCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#0" value="Prefiero la oscuridad que la iluminación del indigno."/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#1" value="Doy vida a los corazones de los Justos."/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#2" value="Me seguirían al Ojo del Terror…"/>
	<entry name="SistersOfBattle/SaintCelestine:Idle#3" value="Atrapada. Aquí."/>
	<entry name="SistersOfBattle/SaintCelestine:IdleCount" value="4"/>
	<entry name="SistersOfBattle/SaintCelestine:Shaken#0" value="No temo a la muerte."/>
	<entry name="SistersOfBattle/SaintCelestine:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/SaintCelestine:Victory#0" value="Es tu final, no el mío."/>
	<entry name="SistersOfBattle/SaintCelestine:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#0" value="¡Por la redención!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#1" value="¡Por nuestras obras nos conoceréis!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#2" value="¡La expiación es un buen trabajo!"/>
	<entry name="SistersOfBattle/SisterRepentia:Attack#3" value="Nuestros evisceradores son como ruedas de plegaria."/>
	<entry name="SistersOfBattle/SisterRepentia:AttackCount" value="4"/>
	<entry name="SistersOfBattle/SisterRepentia:Broken#0" value="¡Nos Mortificarán!"/>
	<entry name="SistersOfBattle/SisterRepentia:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Hurt#0" value="¡Nuestra fe es nuestra armadura!"/>
	<entry name="SistersOfBattle/SisterRepentia:HurtCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#0" value="Hemos pecado."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#1" value="Cobardía frente al enemigo."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#2" value="Señor, Emperador, rogamos perdón."/>
	<entry name="SistersOfBattle/SisterRepentia:Idle#3" value="Un momento de paz antes de la muerte."/>
	<entry name="SistersOfBattle/SisterRepentia:IdleCount" value="4"/>
	<entry name="SistersOfBattle/SisterRepentia:Shaken#0" value="Quizá no podamos expiar…"/>
	<entry name="SistersOfBattle/SisterRepentia:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:Victory#0" value="Otro paso hacia la redención o la muerte."/>
	<entry name="SistersOfBattle/SisterRepentia:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/SisterRepentia:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Celestine fue una Arrepentida…nuestro faro de redención."/>
	<entry name="SistersOfBattle/SisterRepentia:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#0" value="¡Fuego y finta!"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#1" value="Ven, arde."/>
	<entry name="SistersOfBattle/Zephyrim:Attack#2" value="¡El Emperador es nuestro guía!"/>
	<entry name="SistersOfBattle/Zephyrim:Attack#3" value="A través de cielos de llamas, eres nuestro."/>
	<entry name="SistersOfBattle/Zephyrim:AttackCount" value="4"/>
	<entry name="SistersOfBattle/Zephyrim:Broken#0" value="¡Volad, el Emperador nos protege!"/>
	<entry name="SistersOfBattle/Zephyrim:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Hurt#0" value="¡Nuestra sangre, nuestras heridas, oraciones para Él!"/>
	<entry name="SistersOfBattle/Zephyrim:HurtCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Idle#0" value="Somos las destructoras divinas."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#1" value="Cazad al malvado, al poderoso."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#2" value="Las lenguas de fuego también son oraciones."/>
	<entry name="SistersOfBattle/Zephyrim:Idle#3" value="Coro angelical esperando órdenes."/>
	<entry name="SistersOfBattle/Zephyrim:IdleCount" value="4"/>
	<entry name="SistersOfBattle/Zephyrim:Shaken#0" value="Reagrupándonos entre nubes."/>
	<entry name="SistersOfBattle/Zephyrim:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:Victory#0" value="¡Que perezcan todos los malvados!"/>
	<entry name="SistersOfBattle/Zephyrim:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/Zephyrim:AlliedUnit:SistersOfBattle/SaintCelestine#0" value="Ojalá fuéramos verdaderos ángeles como Ella…"/>
	<entry name="SistersOfBattle/Zephyrim:AlliedUnit:SistersOfBattle/SaintCelestineCount" value="1"/>
	<entry name="SistersOfBattle/Dominion" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Immolator" value="SistersOfBattle/Exorcist"/>
	<entry name="SistersOfBattle/Lightning" value="SistersOfBattle/Avenger"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Retributor" value="SistersOfBattle/BattleSister"/>
	<entry name="SistersOfBattle/Castigator" value="SistersOfBattle/Exorcist"/>
	<entry name="Drukhari/Archon:Attack#0" value="¡El sufrimiento es tu suerte!"/>
	<entry name="Drukhari/Archon:Attack#1" value="Es una locura oponerse a mí."/>
	<entry name="Drukhari/Archon:Attack#2" value="Apenas vale la pena matarte."/>
	<entry name="Drukhari/Archon:Attack#3" value="Oh, dulce miedo y agonía."/>
	<entry name="Drukhari/Archon:AttackCount" value="4"/>
	<entry name="Drukhari/Archon:Broken#0" value="¡De vuelta a la ciudad de las sombras!"/>
	<entry name="Drukhari/Archon:BrokenCount" value="1"/>
	<entry name="Drukhari/Archon:Hurt#0" value="Un golpe de suerte."/>
	<entry name="Drukhari/Archon:HurtCount" value="1"/>
	<entry name="Drukhari/Archon:Idle#0" value="Tormentos simples, placeres simples."/>
	<entry name="Drukhari/Archon:Idle#1" value="¿Ya los hemos matado a todos? Lástima."/>
	<entry name="Drukhari/Archon:Idle#2" value="Las riquezas de este mundo son simplemente el sufrimiento de su gente."/>
	<entry name="Drukhari/Archon:Idle#3" value="Ahhhh, cómo llenar los eones inactivos."/>
	<entry name="Drukhari/Archon:IdleCount" value="4"/>
	<entry name="Drukhari/Archon:Shaken#0" value="Si esto continúa, será necesario un Haemonculus para despertarme…"/>
	<entry name="Drukhari/Archon:ShakenCount" value="1"/>
	<entry name="Drukhari/Archon:Victory#0" value="¿Qué esperabas? Lamentable."/>
	<entry name="Drukhari/Archon:VictoryCount" value="1"/>
	<entry name="Drukhari/Cronos:Attack#0" value="Hrrrr."/>
	<entry name="Drukhari/Cronos:Attack#1" value="…memoria…"/>
	<entry name="Drukhari/Cronos:Attack#2" value="Cthhh…"/>
	<entry name="Drukhari/Cronos:Attack#3" value="Rrrr…"/>
	<entry name="Drukhari/Cronos:AttackCount" value="4"/>
	<entry name="Drukhari/Cronos:Broken#0" value="…"/>
	<entry name="Drukhari/Cronos:BrokenCount" value="1"/>
	<entry name="Drukhari/Cronos:Hurt#0" value="Hrrr."/>
	<entry name="Drukhari/Cronos:HurtCount" value="1"/>
	<entry name="Drukhari/Cronos:Idle#0" value="Pssssst…"/>
	<entry name="Drukhari/Cronos:Idle#1" value="Thrrrt."/>
	<entry name="Drukhari/Cronos:Idle#2" value="Thffffttt."/>
	<entry name="Drukhari/Cronos:Idle#3" value="Tcht."/>
	<entry name="Drukhari/Cronos:IdleCount" value="4"/>
	<entry name="Drukhari/Cronos:Shaken#0" value="Eeeee!"/>
	<entry name="Drukhari/Cronos:ShakenCount" value="1"/>
	<entry name="Drukhari/Cronos:Victory#0" value="Psst."/>
	<entry name="Drukhari/Cronos:VictoryCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Attack#0" value="¡El arte de la agonía!"/>
	<entry name="Drukhari/Haemonculus:Attack#1" value="¡Sufre, sufre, sufre!"/>
	<entry name="Drukhari/Haemonculus:Attack#2" value="Con el dolor, me renuevo."/>
	<entry name="Drukhari/Haemonculus:Attack#3" value="Déjame jugar con sus nervios."/>
	<entry name="Drukhari/Haemonculus:AttackCount" value="4"/>
	<entry name="Drukhari/Haemonculus:Broken#0" value="¿Quizás en otro momento?"/>
	<entry name="Drukhari/Haemonculus:BrokenCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Hurt#0" value="Mi propio dolor será suficiente."/>	
	<entry name="Drukhari/Haemonculus:HurtCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Idle#0" value="¿Qué dulce sufrimiento toca a continuación?"/>
	<entry name="Drukhari/Haemonculus:Idle#1" value="¿Recuerdo la caída? La recuerdo."/>
	<entry name="Drukhari/Haemonculus:Idle#2" value="Mis experimentos dan frutos."/>
	<entry name="Drukhari/Haemonculus:Idle#3" value="¿Dónde están mis ruinas? ¡Necesito atención!"/>
	<entry name="Drukhari/Haemonculus:IdleCount" value="4"/>
	<entry name="Drukhari/Haemonculus:Shaken#0" value="¡Oh, son poderosos! Debo tenerlos."/>
	<entry name="Drukhari/Haemonculus:ShakenCount" value="1"/>
	<entry name="Drukhari/Haemonculus:Victory#0" value="Ahora. ¿Qué aprendimos?"/>
	<entry name="Drukhari/Haemonculus:VictoryCount" value="1"/>
	<entry name="Drukhari/Headquarters:Attack#0" value="¿Se atreven a atacar nuestra sede de poder?"/>
	<entry name="Drukhari/Headquarters:Attack#1" value="Insensatos!"/>
	<entry name="Drukhari/Headquarters:Attack#2" value="Qué locura asaltar nuestros muros."/>
	<entry name="Drukhari/Headquarters:Attack#3" value="Se vienen abajo en nuestras almenas."/>
	<entry name="Drukhari/Headquarters:AttackCount" value="4"/>
	<entry name="Drukhari/Headquarters:Broken#0" value="¡Arconte, salva a tu lealtad, a tus súbditos!"/>
	<entry name="Drukhari/Headquarters:BrokenCount" value="1"/>
	<entry name="Drukhari/Headquarters:Hurt#0" value="Brecha abierta. Pero sólo mueren los débiles."/>
	<entry name="Drukhari/Headquarters:HurtCount" value="1"/>
	<entry name="Drukhari/Headquarters:Idle#0" value="En tiempos de paz, complots."/>
	<entry name="Drukhari/Headquarters:Idle#1" value="Los jardines de tortura están ocupados esta noche."/>
	<entry name="Drukhari/Headquarters:Idle#2" value="Un eón de ocio…"/>
	<entry name="Drukhari/Headquarters:Idle#3" value="Los miserables lujos de este hogar temporal."/>
	<entry name="Drukhari/Headquarters:IdleCount" value="4"/>
	<entry name="Drukhari/Headquarters:Shaken#0" value="¡Oh, para volver al oscuro Commorragh!"/>
	<entry name="Drukhari/Headquarters:ShakenCount" value="1"/>
	<entry name="Drukhari/Headquarters:Victory#0" value="Asegura uno más"/>
	<entry name="Drukhari/Headquarters:VictoryCount" value="1"/>
	<entry name="Drukhari/Hellion:Attack#0" value="¡La emoción de matar!"/>
	<entry name="Drukhari/Hellion:Attack#1" value="¡La muerte desde arriba!"/>
	<entry name="Drukhari/Hellion:Attack#2" value="¡Picamos, atacamos!"/>
	<entry name="Drukhari/Hellion:Attack#3" value="¡Sensación abrumadora!"/>
	<entry name="Drukhari/Hellion:AttackCount" value="4"/>
	<entry name="Drukhari/Hellion:Broken#0" value="¡Fuera, antes de que nos atrapen!"/>
	<entry name="Drukhari/Hellion:BrokenCount" value="1"/>
	<entry name="Drukhari/Hellion:Hurt#0" value="Los lentos han caído"/>
	<entry name="Drukhari/Hellion:HurtCount" value="1"/>
	<entry name="Drukhari/Hellion:Idle#0" value="Nosotros no esperaremos"/>
	<entry name="Drukhari/Hellion:Idle#1" value="¡La llamada de los cielos! ¡La llamada del relámpago!"/>
	<entry name="Drukhari/Hellion:Idle#2" value="¡La quietud es un tormento!"/>
	<entry name="Drukhari/Hellion:Idle#3" value="¡Libéranos, Arconte!"/>
	<entry name="Drukhari/Hellion:IdleCount" value="4"/>
	<entry name="Drukhari/Hellion:Shaken#0" value="En el suelo, morimos"/>
	<entry name="Drukhari/Hellion:ShakenCount" value="1"/>
	<entry name="Drukhari/Hellion:Victory#0" value="¡Mientras más rápido, más rápido caen!"/>
	<entry name="Drukhari/Hellion:VictoryCount" value="1"/>
	<entry name="Drukhari/Incubi:Attack#0" value="Nosotros somos uno con el espadón."/>
	<entry name="Drukhari/Incubi:Attack#1" value="El golpe mortal… así es."/>
	<entry name="Drukhari/Incubi:Attack#2" value="Ofrecemos los débiles a Khaine"/>
	<entry name="Drukhari/Incubi:Attack#3" value="Pagado para matar."/>
	<entry name="Drukhari/Incubi:AttackCount" value="4"/>
	<entry name="Drukhari/Incubi:Broken#0" value="Khaine no perdona a los cobardes."/>
	<entry name="Drukhari/Incubi:BrokenCount" value="1"/>
	<entry name="Drukhari/Incubi:Hurt#0" value="Un verdadero oponente"/>
	<entry name="Drukhari/Incubi:HurtCount" value="1"/>
	<entry name="Drukhari/Incubi:Idle#0" value="No habrá traición hasta que se cumpla el pacto."/>
	<entry name="Drukhari/Incubi:Idle#1" value="Nuestro tiempo está comprado… sería un desperdicio no utilizarnos."/>
	<entry name="Drukhari/Incubi:Idle#2" value="Hay pureza en el odio."/>
	<entry name="Drukhari/Incubi:Idle#3" value="Sigue a Arha y el Camino de la Condenación."/>
	<entry name="Drukhari/Incubi:IdleCount" value="4"/>
	<entry name="Drukhari/Incubi:Shaken#0" value="Nadie recorrerá el Sendero del Duelo para tales como nosotros."/>
	<entry name="Drukhari/Incubi:ShakenCount" value="1"/>
	<entry name="Drukhari/Incubi:Victory#0" value="Tributo por Khaine."/>
	<entry name="Drukhari/Incubi:VictoryCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#0" value="¡Chattel!"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#1" value="Locos arrogantes!"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#2" value="¿Oponerse a los Legítimos? Jajaja…"/>
	<entry name="Drukhari/KabaliteTrueborn:Attack#3" value="El Arconte ordena…"/>
	<entry name="Drukhari/KabaliteTrueborn:AttackCount" value="4"/>
	<entry name="Drukhari/KabaliteTrueborn:Broken#0" value="¡Atrás, preserva nuestra preciosa carne!"/>
	<entry name="Drukhari/KabaliteTrueborn:BrokenCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Hurt#0" value="Sólo los verdaderos Elegidos siguen en pie."/>
	<entry name="Drukhari/KabaliteTrueborn:HurtCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#0" value="¡Tráenos vino! ¡Víctimas!"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#1" value="Es de sabio el no desperdiciar nuestras preciosas vidas."/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#2" value="Pasaremos milenios en tal lasitud…"/>
	<entry name="Drukhari/KabaliteTrueborn:Idle#3" value="¡Oh, felices días de indolencia!"/>
	<entry name="Drukhari/KabaliteTrueborn:IdleCount" value="4"/>
	<entry name="Drukhari/KabaliteTrueborn:Shaken#0" value="¡No nacimos en cubas desechables!"/>
	<entry name="Drukhari/KabaliteTrueborn:ShakenCount" value="1"/>
	<entry name="Drukhari/KabaliteTrueborn:Victory#0" value="¿Qué esperaban? Somos los Elegidos."/>
	<entry name="Drukhari/KabaliteTrueborn:VictoryCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Attack#0" value="Llamadas traviesas."/>
	<entry name="Drukhari/KabaliteWarrior:Attack#1" value="Acobardaos, mortales."/>
	<entry name="Drukhari/KabaliteWarrior:Attack#2" value="¡Commorrah exige tributo!"/>
	<entry name="Drukhari/KabaliteWarrior:Attack#3" value="¡Por la Cábala!"/>
	<entry name="Drukhari/KabaliteWarrior:AttackCount" value="4"/>
	<entry name="Drukhari/KabaliteWarrior:Broken#0" value="Si huimos, el Arconte será peor que la muerte."/>
	<entry name="Drukhari/KabaliteWarrior:BrokenCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Hurt#0" value="Lamentarán esto …"/>
	<entry name="Drukhari/KabaliteWarrior:HurtCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Idle#0" value="Puede que seamos de Vat, pero somos verdaderos Drukhari."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#1" value="Traed prisioneros, debemos practicar nuestra puntería."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#2" value="Preservad a los de la Cábala, gastad a los mercenarios."/>
	<entry name="Drukhari/KabaliteWarrior:Idle#3" value="Commorragh, nuestro hogar con múltiples ángulos…"/>
	<entry name="Drukhari/KabaliteWarrior:IdleCount" value="4"/>
	<entry name="Drukhari/KabaliteWarrior:Shaken#0" value="¿Tenemos miedo? ¡Ellos deberían temer!"/>
	<entry name="Drukhari/KabaliteWarrior:ShakenCount" value="1"/>
	<entry name="Drukhari/KabaliteWarrior:Victory#0" value="¡Insensatos! La Cábala no envía débiles."/>
	<entry name="Drukhari/KabaliteWarrior:VictoryCount" value="1"/>
	<entry name="Drukhari/Raider:Attack#0" value="Hoja de quilla baja y dulce."/>
	<entry name="Drukhari/Raider:Attack#1" value="Dolor pasajero…"/>
	<entry name="Drukhari/Raider:Attack#2" value="Acérquemonos…"/>
	<entry name="Drukhari/Raider:Attack#3" value="¡El zumbido de la carga!"/>
	<entry name="Drukhari/Raider:AttackCount" value="4"/>
	<entry name="Drukhari/Raider:Broken#0" value="¡Retírada antes de que puedan acercarse a nosotros!"/>
	<entry name="Drukhari/Raider:BrokenCount" value="1"/>
	<entry name="Drukhari/Raider:Hurt#0" value="¡La velocidad debería ser nuestra armadura!"/>
	<entry name="Drukhari/Raider:HurtCount" value="1"/>
	<entry name="Drukhari/Raider:Idle#0" value="Nuestros ganchos de trofeo están vacíos!"/>
	<entry name="Drukhari/Raider:Idle#1" value=""/>
	<entry name="Drukhari/Raider:Idle#2" value="Apila los cuerpos en la parte trasera. Sólo los débiles mueren."/>
	<entry name="Drukhari/Raider:Idle#3" value="Sólo Vect recuerda los yates de recreo de la antigüedad…"/>
	<entry name="Drukhari/Raider:IdleCount" value="4"/>
	<entry name="Drukhari/Raider:Shaken#0" value="Más rápido, sobre ellos!"/>
	<entry name="Drukhari/Raider:ShakenCount" value="1"/>
	<entry name="Drukhari/Raider:Victory#0" value="Pon a los cautivos en los ganchos de trofeo. Vivos o muertos."/>
	<entry name="Drukhari/Raider:VictoryCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#0" value="Rastrillando el suelo, Arconte!"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#1" value="Nuestra gracia trae perdición."/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#2" value="¡Oh, huye, presa, huye!"/>
	<entry name="Drukhari/RazorwingJetfighter:Attack#3" value="¡Masacradlos! Que nadie escape."/>
	<entry name="Drukhari/RazorwingJetfighter:AttackCount" value="4"/>
	<entry name="Drukhari/RazorwingJetfighter:Broken#0" value="¡Salva tu propio pellejo"/>
	<entry name="Drukhari/RazorwingJetfighter:BrokenCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Hurt#0" value="¡No busqué una pelea justa!"/>
	<entry name="Drukhari/RazorwingJetfighter:HurtCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#0" value="Mejor aquí que en las carreras de la muerte en la arena…"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#1" value="Échale la culpa a los Nightwings…"/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#2" value="Somos los mejores de los Guadañas."/>
	<entry name="Drukhari/RazorwingJetfighter:Idle#3" value="La muerte desde tan lejos. La crueldad es que no podemos ver su tormento."/>
	<entry name="Drukhari/RazorwingJetfighter:IdleCount" value="4"/>
	<entry name="Drukhari/RazorwingJetfighter:Shaken#0" value="¿Están contraatacando?"/>
	<entry name="Drukhari/RazorwingJetfighter:ShakenCount" value="1"/>
	<entry name="Drukhari/RazorwingJetfighter:Victory#0" value="Divididos maravillosamente. Nunca nos vio venir."/>
	<entry name="Drukhari/RazorwingJetfighter:VictoryCount" value="1"/>
	<entry name="Drukhari/Scourge:Attack#0" value="Pisar a los moribundos, no a la tierra."/>
	<entry name="Drukhari/Scourge:Attack#1" value="Rapaces en picada…"/>
	<entry name="Drukhari/Scourge:Attack#2" value="¿Escuchas tu muerte en el viento?"/>
	<entry name="Drukhari/Scourge:Attack#3" value="¡Arráncadles los ojos, parientes Vulture!"/>
	<entry name="Drukhari/Scourge:AttackCount" value="4"/>
	<entry name="Drukhari/Scourge:Broken#0" value="¡Al aire! ¡Aiee!"/>
	<entry name="Drukhari/Scourge:BrokenCount" value="1"/>
	<entry name="Drukhari/Scourge:Hurt#0" value="Un valiente que mata a un Azote."/>
	<entry name="Drukhari/Scourge:HurtCount" value="1"/>
	<entry name="Drukhari/Scourge:Idle#0" value="Mensajeros de la Ciudad Oscura."/>
	<entry name="Drukhari/Scourge:Idle#1" value="Portadores de intriga."/>
	<entry name="Drukhari/Scourge:Idle#2" value="Nuestra riqueza está en nuestras alas."/>
	<entry name="Drukhari/Scourge:Idle#3" value="My pinions long to be stretched."/>
	<entry name="Drukhari/Scourge:IdleCount" value="4"/>
	<entry name="Drukhari/Scourge:Shaken#0" value="¡Debemos elevarnos más alto!"/>
	<entry name="Drukhari/Scourge:ShakenCount" value="1"/>
	<entry name="Drukhari/Scourge:Victory#0" value="Escucha esos gritos, ahh…"/>
	<entry name="Drukhari/Scourge:VictoryCount" value="1"/>
	<entry name="Drukhari/Succubus:Attack#0" value="¿No estás entretenido?"/>
	<entry name="Drukhari/Succubus:Attack#1" value="¿Más pretendientes? Eh."/>
	<entry name="Drukhari/Succubus:Attack#2" value="Tú que estás a punto de morir… te saludo."/>
	<entry name="Drukhari/Succubus:Attack#3" value="¡Qué pésimo desafío eres!"/>
	<entry name="Drukhari/Succubus:AttackCount" value="4"/>
	<entry name="Drukhari/Succubus:Broken#0" value="¡No me arriesgaré a manchar mis colores!"/>
	<entry name="Drukhari/Succubus:BrokenCount" value="1"/>
	<entry name="Drukhari/Succubus:Hurt#0" value="¡Me dejaron cicatrices!"/>
	<entry name="Drukhari/Succubus:HurtCount" value="1"/>
	<entry name="Drukhari/Succubus:Idle#0" value="Más correo de fans. Bostezo."/>
	<entry name="Drukhari/Succubus:Idle#1" value="Commorragh es la banda. Y yo los controlo."/>
	<entry name="Drukhari/Succubus:Idle#2" value="Dile al Arconte que necesito más golosinas."/>
	<entry name="Drukhari/Succubus:Idle#3" value="Uf, anhelo volver a la civilización, al verdadero sufrimiento."/>
	<entry name="Drukhari/Succubus:Idle#4" value="Sombras y polvo. Miedo y asombro."/>
	<entry name="Drukhari/Succubus:IdleCount" value="5"/>
	<entry name="Drukhari/Succubus:Shaken#0" value="¡Nosotras, las Novias de la Muerte, no conocemos a nuestro cónyuge!"/>
	<entry name="Drukhari/Succubus:ShakenCount" value="1"/>
	<entry name="Drukhari/Succubus:Victory#0" value="Tengo… talento para sobrevivir."/>
	<entry name="Drukhari/Succubus:VictoryCount" value="1"/>
	<entry name="Drukhari/Tantalus:Attack#0" value="¡Dispara los desintegradores de pulso!"/>
	<entry name="Drukhari/Tantalus:Attack#1" value="¿Ves cómo los enemigos se separan como olas?"/>
	<entry name="Drukhari/Tantalus:Attack#2" value="¡Tensa el velamen etéreo! ¡Persíguelos!"/>
	<entry name="Drukhari/Tantalus:Attack#3" value="Gira y mira a barlovento."/>
	<entry name="Drukhari/Tantalus:AttackCount" value="4"/>
	<entry name="Drukhari/Tantalus:Broken#0" value="¡Conserva la nave!"/>
	<entry name="Drukhari/Tantalus:BrokenCount" value="1"/>
	<entry name="Drukhari/Tantalus:Hurt#0" value="¡El casco está roto! Caemos…"/>
	<entry name="Drukhari/Tantalus:HurtCount" value="1"/>
	<entry name="Drukhari/Tantalus:Idle#0" value="Afila las guadañas."/>
	<entry name="Drukhari/Tantalus:Idle#1" value="¡Cuidado! Un rasguño y el	Arconte…"/>
	<entry name="Drukhari/Tantalus:Idle#2" value="Un diseño de la Cábala del Espejo Oscuro. Hace mucho que desapareció."/>
	<entry name="Drukhari/Tantalus:Idle#3" value="Qué placer navegar."/>
	<entry name="Drukhari/Tantalus:IdleCount" value="4"/>
	<entry name="Drukhari/Tantalus:Shaken#0" value="¡El viento ha amainado!"/>
	<entry name="Drukhari/Tantalus:ShakenCount" value="1"/>
	<entry name="Drukhari/Tantalus:Victory#0" value="Considera a los caídos, que alguna vez fueron altos y orgullosos como tú."/>
	<entry name="Drukhari/Tantalus:VictoryCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Attack#0" value="Bombas fuera. ¡No mires a la luz oscura!"/>
	<entry name="Drukhari/VoidravenBomber:Attack#1" value="Disparando con lanza de Vacío. Mina del Vacío lista."/>
	<entry name="Drukhari/VoidravenBomber:Attack#2" value="El artillero manda."/>
	<entry name="Drukhari/VoidravenBomber:Attack#3" value="En silencio, volamos."/>
	<entry name="Drukhari/VoidravenBomber:AttackCount" value="4"/>
	<entry name="Drukhari/VoidravenBomber:Broken#0" value="¡Fuera, nos derriban!"/>
	<entry name="Drukhari/VoidravenBomber:BrokenCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Hurt#0" value="¡No interrumpas mi trabajo!"/>
	<entry name="Drukhari/VoidravenBomber:HurtCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Idle#0" value="Piloto y artillero."/>
	<entry name="Drukhari/VoidravenBomber:Idle#1" value="Debo componer mi próxima sinfonía de destrucción."/>
	<entry name="Drukhari/VoidravenBomber:Idle#2" value="Silencia los motores. Dales el premio de la sorpresa."/>
	<entry name="Drukhari/VoidravenBomber:Idle#3" value="¡Cuidado con la antimateria, 	idiotas!"/>
	<entry name="Drukhari/VoidravenBomber:IdleCount" value="4"/>
	<entry name="Drukhari/VoidravenBomber:Shaken#0" value="¡Un artista no puede 	trabajar así!"/>
	<entry name="Drukhari/VoidravenBomber:ShakenCount" value="1"/>
	<entry name="Drukhari/VoidravenBomber:Victory#0" value="Un cráter humeante concluye mi pieza."/>
	<entry name="Drukhari/VoidravenBomber:VictoryCount" value="1"/>
	<entry name="Drukhari/Wrack:Attack#0" value="Criaturas del espacio real…"/>
	<entry name="Drukhari/Wrack:Attack#1" value="Tormento en constante cambio."/>
	<entry name="Drukhari/Wrack:Attack#2" value="Muéstrame. ¡Dame tu carne!"/>
	<entry name="Drukhari/Wrack:Attack#3" value="Ven, ven. A la losa."/>
	<entry name="Drukhari/Wrack:AttackCount" value="4"/>
	<entry name="Drukhari/Wrack:Broken#0" value="Oh, al maestro no le gustará esto…"/>
	<entry name="Drukhari/Wrack:BrokenCount" value="1"/>
	<entry name="Drukhari/Wrack:Hurt#0" value="No soy nadie. Nadie está herido."/>
	<entry name="Drukhari/Wrack:HurtCount" value="1"/>
	<entry name="Drukhari/Wrack:Idle#0" value="Elegí esta forma… por aburrimiento."/>
	<entry name="Drukhari/Wrack:Idle#1" value="Hipertoxinas nuevas. ¿Qué deliciosos venenos te esperan?"/>
	<entry name="Drukhari/Wrack:Idle#2" value="Obedece al Mártir, dijeron."/>
	<entry name="Drukhari/Wrack:Idle#3" value="¿No somos admirables?"/>
	<entry name="Drukhari/Wrack:IdleCount" value="4"/>
	<entry name="Drukhari/Wrack:Shaken#0" value="¡Defiende al maestro!"/>
	<entry name="Drukhari/Wrack:ShakenCount" value="1"/>
	<entry name="Drukhari/Wrack:Victory#0" value="Más temas para los experimentos del maestro…"/>
	<entry name="Drukhari/Wrack:VictoryCount" value="1"/>
	<entry name="Drukhari/Wyche:Attack#0" value="¡Entrena con nosotros!"/>
	<entry name="Drukhari/Wyche:Attack#1" value="Observa y aprende… luego muere."/>
	<entry name="Drukhari/Wyche:Attack#2" value="La muerte se acerca…"/>
	<entry name="Drukhari/Wyche:Attack#3" value="Dulce gracia asesina."/>
	<entry name="Drukhari/Wyche:AttackCount" value="4"/>
	<entry name="Drukhari/Wyche:Broken#0" value="No entrené para esto."/>
	<entry name="Drukhari/Wyche:BrokenCount" value="1"/>
	<entry name="Drukhari/Wyche:Hurt#0" value="¡Estamos ensangrentados! Pero la lucha acaba de comenzar."/>
	<entry name="Drukhari/Wyche:HurtCount" value="1"/>
	<entry name="Drukhari/Wyche:Idle#0" value="Debemos practicar."/>
	<entry name="Drukhari/Wyche:Idle#1" value="Nuestras actuaciones alimentan Commorragh."/>
	<entry name="Drukhari/Wyche:Idle#2" value="¡Demostremos nuestra habilidad!"/>
	<entry name="Drukhari/Wyche:Idle#3" value="Artesanos, traednos las herramientas."/>
	<entry name="Drukhari/Wyche:IdleCount" value="4"/>
	<entry name="Drukhari/Wyche:Shaken#0" value="¡¿No aprecian nuestro arte?!"/>
	<entry name="Drukhari/Wyche:ShakenCount" value="1"/>
	<entry name="Drukhari/Wyche:Victory#0" value="Haced una reverencia y sonreid a la 	audiencia, hermanas."/>
	<entry name="Drukhari/Wyche:VictoryCount" value="1"/>
	<entry name="Drukhari/Ravager" value="Drukhari/Raider"/>
	<entry name="Drukhari/Reaver" value="Drukhari/RazorwingJetfighter"/>
	<entry name="Drukhari/Venom" value="Drukhari/Raider"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#0" value="Activando escapes de incienso sagrado."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#1" value="Bendiciendo al servidor y cargando."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#2" value="Destrucción sobre gloria."/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Attack#3" value="Lanzas preparadas."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:AttackCount" value="4"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Broken#0" value="Proteger los motores."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:BrokenCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Hurt#0" value="Daños críticos sufridos."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:HurtCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#0" value="Descansando el servidor"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#1" value="Sydonia está tan lejos…"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#2" value="La duda y la muerte están más allá de mí."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:Idle#3" value="Movimiento sin fin para un imperio eterno."/>
	<entry name="AdeptusMechanicus/SydonianDragoon:IdleCount" value="4"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Shaken#0" value="¡El zancudo tropieza!"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:ShakenCount" value="1"/> 
	<entry name="AdeptusMechanicus/SydonianDragoon:Victory#0" value="Por Vingh y Sydonia"/>
	<entry name="AdeptusMechanicus/SydonianDragoon:VictoryCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#0" value="Marcando objetivos."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#1" value="Permitiendo la deriva del viento."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#2" value="Muy bien, señor."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Attack#3" value="Tenemos una solución de fuego, señor."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:AttackCount" value="4"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Broken#0" value="¡Abandona el cañón!"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:BrokenCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Hurt#0" value="Impacto directo, sobre nosotros, desgraciadamente."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:HurtCount" value="1"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#0" value="La vida en la guardia es terriblemente dura."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#1" value="¿Artillero, qué?"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#2" value="¿Es esto una trinchera o una depresión?"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Idle#3" value="¿Sin objetivos? Perfecto."/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:IdleCount" value="4"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Shaken#0" value="Te dije que estaba enfermo"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:ShakenCount" value="1"/> 
	<entry name="AstraMilitarum/FieldOrdnanceBattery:Victory#0" value="¿Lo hicimos? ¡Creo que los tenemos!"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery:VictoryCount" value="1"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="ChaosSpaceMarines/ChaosSpaceMarine"/>
	<entry name="Eldar/Wraithlord" value="Eldar/Wraithblade"/>
	<entry name="Necrons/SkorpekhDestroyer" value="Necrons/HeavyDestroyer"/>
	<entry name="Orks/BurnaBoyz:Attack#0" value="¡Dadlez fuego!"/>
	<entry name="Orks/BurnaBoyz:Attack#1" value="¡Jajaja! ¡Mira como arden!"/>
	<entry name="Orks/BurnaBoyz:Attack#2" value="Flamin' nora!"/>
	<entry name="Orks/BurnaBoyz:Attack#3" value="Zeiz en fuego!"/>
	<entry name="Orks/BurnaBoyz:AttackCount" value="4"/>
	<entry name="Orks/BurnaBoyz:Broken#0" value="¡Nozotroz no iniciamoz el fuego!"/>
	<entry name="Orks/BurnaBoyz:BrokenCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Hurt#0" value="Urggg, ¡ezo quema!"/>
	<entry name="Orks/BurnaBoyz:HurtCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Idle#0" value="¡No ze juega con fuego!."/>
	<entry name="Orks/BurnaBoyz:Idle#1" value="'Ere, gizza light."/>
	<entry name="Orks/BurnaBoyz:Idle#2" value="No quiero prender fuego al mundo… ezpera, zí que quiero."/>
	<entry name="Orks/BurnaBoyz:Idle#3" value="¡C'mon, baby, light me fire!"/>
	<entry name="Orks/BurnaBoyz:IdleCount" value="4"/>
	<entry name="Orks/BurnaBoyz:Shaken#0" value="Urggg, ¡ezo quema!"/>
	<entry name="Orks/BurnaBoyz:ShakenCount" value="1"/>
	<entry name="Orks/BurnaBoyz:Victory#0" value="Infierno de descuento"/>
	<entry name="Orks/BurnaBoyz:VictoryCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#0" value="Exculpate!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#1" value="Purgatus!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#2" value="In extremis!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Attack#3" value="¡Matameeeeee!"/>
	<entry name="SistersOfBattle/ArcoFlagellant:AttackCount" value="4"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Broken#0" value="Nnnnn-"/>
	<entry name="SistersOfBattle/ArcoFlagellant:BrokenCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Hurt#0" value="Más cerca de ti, 	Emperador"/>	
	<entry name="SistersOfBattle/ArcoFlagellant:HurtCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#0" value="Durmiente"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#1" value="Fe"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#2" value="Desátame."/>
	<entry name="SistersOfBattle/ArcoFlagellant:Idle#3" value="Yo… obedezco."/>
	<entry name="SistersOfBattle/ArcoFlagellant:IdleCount" value="4"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Shaken#0" value="¿Martirizada?"/>
	<entry name="SistersOfBattle/ArcoFlagellant:ShakenCount" value="1"/>
	<entry name="SistersOfBattle/ArcoFlagellant:Victory#0" value=""/>
	<entry name="SistersOfBattle/ArcoFlagellant:VictoryCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#0" value="¡Muere a mis manos!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#1" value="¡Solo hay guerra!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#2" value="¡Nadie sobrevivirá!"/>
	<entry name="SpaceMarines/AssaultTerminator:Attack#3" value="Nosotras limpiamos."/>
	<entry name="SpaceMarines/AssaultTerminator:AttackCount" value="4"/>
	<entry name="SpaceMarines/AssaultTerminator:Broken#0" value="¡Emboscada!"/>
	<entry name="SpaceMarines/AssaultTerminator:BrokenCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Hurt#0" value="Tactical Dreadnought Armour holding."/>
	<entry name="SpaceMarines/AssaultTerminator:HurtCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#0" value="Un momento de laxitud…"/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#1" value="Nuestra armadura está preparada."/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#2" value="Primera Compañía en espera."/>
	<entry name="SpaceMarines/AssaultTerminator:Idle#3" value="Bendiciendo nuestra armadura."/>
	<entry name="SpaceMarines/AssaultTerminator:IdleCount" value="4"/>
	<entry name="SpaceMarines/AssaultTerminator:Shaken#0" value="¡Por su nombre!"/>
	<entry name="SpaceMarines/AssaultTerminator:ShakenCount" value="1"/>
	<entry name="SpaceMarines/AssaultTerminator:Victory#0" value="¡Por el Emperador!"/>
	<entry name="SpaceMarines/AssaultTerminator:VictoryCount" value="1"/>
	<entry name="Tau/FireWarriorBreacher" value="Tau/FireWarrior"/>
	<entry name="Tyranids/Biovore" value="Tyranids/Carnifex"/>
	<entry name="Drukhari/Talos" value="Drukhari/Cronos"/>	
</language>