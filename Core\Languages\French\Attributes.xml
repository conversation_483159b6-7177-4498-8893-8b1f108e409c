<?xml version="1.0" encoding="utf-8"?>
<language>
	<!-- Generic -->
	<entry name="Accuracy" value="Précision"/>
	<entry name="AccuracyDescription" value="Chaque point de précision augmente les chances de toucher d'approximativement 8.3%."/>
	<entry name="AccuracyIcon" value="<icon height='20' texture='Icons/Attributes/Accuracy'/>"/>
	<entry name="ActionPoints" value="Points d'Action"/>
	<entry name="ActionPointsIcon" value="<icon height='20' texture='Icons/Attributes/Actions'/>"/>
	<entry name="ActionPointsMax" value="Points d'Action Max"/>
	<entry name="ActionPointsMaxIcon" value="<icon height='20' texture='Icons/Attributes/Actions'/>"/>
	<entry name="Actions" value="Actions"/>
	<entry name="ActionsIcon" value="<icon height='20' texture='Icons/Attributes/Actions'/>"/>
	<entry name="ActionsDescription" value="La quantité d'actions (incluant les attaques en état d'alerte) que l'unité peut effectuer. Utiliser une action peut consommer tout le mouvement."/>
	<entry name="AdditionalMembersHit" value="Modèles touchés supplémentaires"/>
	<entry name="AdditionalMembersHitIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="Armor" value="Armure"/>
	<entry name="ArmorDescription" value="Réduit la quantité de <icon height='20' texture='Icons/Attributes/Damage'/> dégâts subis.<br/><br/>Chaque point d'armure réduit les dégâts subis d'approximativement 8.3% (jusqu'à 83%), mais les armes et compétences ignorent un nombre de points d'armure égal à leur valeur de pénétration d'armure."/>
	<entry name="ArmorIcon" value="<icon height='20' texture='Icons/Attributes/Armor'/>"/>
	<entry name="ArmorDamageReduction" value="Réduction des dégâts par l'armure"/>
	<entry name="ArmorDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/Armor'/>"/>
	<entry name="ArmorPenetration" value="Pénétration d'armure"/>
	<entry name="ArmorPenetrationDescription" value=""/>
	<entry name="ArmorPenetrationIcon" value="<icon height='20' texture='Icons/Attributes/ArmorPenetration'/>"/>
	<entry name="Attacks" value="Attaques"/>
	<entry name="AttacksDescription" value=""/>
	<entry name="AttacksIcon" value="<icon height='20' texture='Icons/Attributes/Attacks'/>"/>
	<entry name="AttacksPerCharge" value="Attaques par charge"/>
	<entry name="AttacksTaken" value="Attaques subies"/>
 	<entry name="AttacksTakenIcon" value="<icon height='20' texture='Icons/Attributes/Attacks'/>"/>
	<entry name="Biomass" value="Biomasse"/>
	<entry name="BiomassIcon" value="<icon height='20' texture='Icons/Attributes/Biomass'/>"/>
	<entry name="BiomassCost" value="Coût en biomasse"/>
	<entry name="BiomassCostIcon" value="<icon height='20' texture='Icons/Attributes/Biomass'/>"/>
	<entry name="BiomassOnConsume" value="Biomasse"/>
	<entry name="BiomassOnConsumeIcon" value="<icon height='20' texture='Icons/Attributes/Biomass'/>"/>
	<entry name="BiomassUpkeep" value="Coût d'entretien en biomasse"/>
	<entry name="BiomassUpkeepIcon" value="<icon height='20' texture='Icons/Attributes/Biomass'/>"/>
	<entry name="BoonOfChaosChance" value="Chance de Bienfait du Chaos"/>
	<entry name="BoonOfChaosChanceIcon" value="<icon height='20' texture='Icons/Traits/ChaosSpaceMarines/ChampionOfChaos'/>"/>
	<entry name="BuildingSlots" value="Emplacements de bâtiments"/>
	<entry name="BuildingSlotsIcon" value="<icon height='20' texture='Icons/Attributes/BuildingSlots'/>"/>
	<entry name="CargoSlots" value="Emplacements de transport"/>
	<entry name="CargoSlotsIcon" value="<icon height='20' texture='Icons/Attributes/CargoSlots'/>"/>
	<entry name="CargoSlotsRequired" value="Emplacements de transport requis"/>
	<entry name="CargoSlotsRequiredIcon" value="<icon height='20' texture='Icons/Attributes/CargoSlots'/>"/>
	<entry name="CircumstanceMeleeDamage" value="Dégâts de corps à corps supplémentaires"/>
	<entry name="CircumstanceMeleeDamageIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="CityDamageReduction" value="Réduction des dégâts due à la ville"/>
	<entry name="CityDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	<entry name="CityRadius" value="Rayon de la ville"/>
	<entry name="CityRadiusIcon" value="<icon height='20' texture='Icons/Attributes/Radius'/>"/>
	<entry name="ConstructionCost" value="Coût en production"/>
	<entry name="ConstructionCostIcon" value="<icon height='20' texture='Icons/Attributes/ConstructionCost'/>"/>
	<entry name="ConsumedActionPoints" value="Points d'actions consommés"/>
	<entry name="ConsumedActionPointsIcon" value="<icon height='20' texture='Icons/Attributes/Actions'/>"/>
	<entry name="ConsumedMovement" value="Mouvement consommé"/>
	<entry name="ConsumedMovementIcon" value="<icon height='20' texture='Icons/Attributes/Movement'/>"/>
	<entry name="Cooldown" value="Recharge"/>
	<entry name="CooldownIcon" value="<icon height='20' texture='Icons/Attributes/Cooldown'/>"/>
	<entry name="Damage" value="Dégâts"/>
	<entry name="DamageIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="DamageFromHitpoints" value="Dégâts dus aux points de vie de la cible"/>
	<entry name="DamageFromHitpointsIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="DamageReduction" value="Réduction de dégâts"/>
	<entry name="DamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	<entry name="DamageReturnFactor" value="Dégâts renvoyés"/>
	<entry name="DamageReturnFactorIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="DamageSelfFactor" value="Dégâts auto-infligés"/>
	<entry name="DamageSelfFactorIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="DamageSelfFromHitpointsFactor" value="Dégâts auto-infligés en fonction des points de vie"/>
	<entry name="DamageSelfFromHitpointsFactorIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="DamageTaken" value="Dégâts subis"/>
	<entry name="DamageTakenIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="DamageTakenByGroupSizeFactor" value="Dommages subis à cause de la taille du groupe"/>
	<entry name="DamageTakenByGroupSizeFactorIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="DeathExperience" value="Expérience de la mort"/>
	<entry name="DeathExperienceIcon" value="<icon height='20' texture='Icons/Attributes/Level'/>"/>
	<entry name="DeathMorale" value="Morale de la mort"/>
	<entry name="DeathMoraleIcon" value="<icon height='20' texture='Icons/Attributes/Morale'/>"/>	
	<entry name="DuplicateTypeCost" value="Augmentation du coût par exemplaire en plus"/>
	<entry name="Energy" value="Énergie"/>
	<entry name="EnergyIcon" value="<icon height='20' texture='Icons/Attributes/Energy'/>"/>
	<entry name="EnergyCost" value="Coût en énergie"/>
	<entry name="EnergyCostIcon" value="<icon height='20' texture='Icons/Attributes/Energy'/>"/>
	<entry name="EnergyFromAdjacentBuildings" value="Energie par bâtiment adjacent"/>
	<entry name="EnergyFromAdjacentBuildingsIcon" value="<icon height='20' texture='Icons/Attributes/Energy'/>"/>
 	<entry name="EnergyFromExperienceValueFactor" value="Energie par point d'expérience"/>
 	<entry name="EnergyFromExperienceValueFactorIcon" value="<icon height='20' texture='Icons/Attributes/Energy'/>"/>
	<entry name="EnergyUpkeep" value="Coût d'entretien en énergie"/>
	<entry name="EnergyUpkeepIcon" value="<icon height='20' texture='Icons/Attributes/Energy'/>"/>
	<entry name="ExperienceGainRate" value="Multiplicateur du gain en expérience"/>
	<entry name="ExperienceGainRateIcon" value="<icon height='20' texture='Icons/Attributes/Level'/>"/>
	<entry name="FeelNoPainDamageReduction" value="Réduction de dégâts Insensible à la douleur"/>
	<entry name="FeelNoPainDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	<entry name="FlankingDamageFactor" value="Dégâts dus à une attaque de flanc"/>
	<entry name="FlankingDamageFactorIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="FlatResourcesFromFeatures" value="Quantité fixe de ressources provenant des avant-postes"/>
	<entry name="FlatResourcesFromFeaturesIcon" value="<icon height='20' texture='Icons/Features/Grassland'/>"/>
	<entry name="Food" value="Nourriture"/>
	<entry name="FoodIcon" value="<icon height='20' texture='Icons/Attributes/Food'/>"/>
	<entry name="FoodFromAdjacentBuildings" value="Nourriture par bâtiment adjacent"/>
	<entry name="FoodFromAdjacentBuildingsIcon" value="<icon height='20' texture='Icons/Attributes/Food'/>"/>
	<entry name="FoodCost" value="Coût en nourriture"/>
	<entry name="FoodCostIcon" value="<icon height='20' texture='Icons/Attributes/Food'/>"/>
	<entry name="FoodUpkeep" value="Coût d'entretien en nourriture"/>
	<entry name="FoodUpkeepIcon" value="<icon height='20' texture='Icons/Attributes/Food'/>"/>
	<entry name="GroupSize" value="Taille du groupe"/>
	<entry name="GroupSizeIcon" value="<icon height='20' texture='Icons/Attributes/PopulationLimit'/>"/>
	<entry name="GroupSizeMax" value="Taille maximale du groupe"/>
	<entry name="GroupSizeMaxIcon" value="<icon height='20' texture='Icons/Attributes/PopulationLimit'/>"/>
	<entry name="Growth" value="Croissance"/>
	<entry name="GrowthHint" value="<style name='Title'/>Croissance<br/><style name='Default'/>Indique à quelle vitesse la ville se développe. Une fois qu'assez de croissance a été accumulée, la population augmente de un. Le rythme de croissance diminue quand la population approche de la limite de population."/>
	<entry name="GrowthIcon" value="<icon height='20' texture='Icons/Attributes/Growth'/>"/>
	<entry name="GrowthFactor" value="Croissance"/>
	<entry name="GrowthFactorIcon" value="<icon height='20' texture='Icons/Attributes/Growth'/>"/>
	<entry name="HeroDamageReduction" value="Réduction de dégâts subis par les héros"/>
	<entry name="HeroDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	<entry name="HealingRate" value="Facteur de guérison"/>
	<entry name="HealingRateIcon" value="<icon height='20' texture='Icons/Attributes/HealingRate'/>"/>
	<entry name="Hitpoints" value="Points de vie"/>
	<entry name="HitpointsDescription" value="La quantité de <icon height='20' texture='Icons/Attributes/Damage'/> dégâts que l'unité peut encaisser avant de mourir. Les unités se soignent automatiquement si elles n'ont pas subi de dégâts et qu'elles ont tous leurs points d'actions et leur mouvement."/>
	<entry name="HitpointsIcon" value="<icon height='20' texture='Icons/Attributes/Hitpoints'/>"/>
	<entry name="HitpointsFactorFromMax" value="Points de vie"/>
	<entry name="HitpointsFactorFromMaxIcon" value="<icon height='20' texture='Icons/Attributes/Hitpoints'/>"/>
	<entry name="HitpointsFactorFromMoraleDifference" value="Points de vie par différence de moral"/>
	<entry name="HitpointsFactorFromMoraleDifferenceIcon" value="<icon height='20' texture='Icons/Attributes/Hitpoints'/>"/>
	<entry name="HitpointsMax" value="Points de vie maximum"/>
	<entry name="HitpointsMaxIcon" value="<icon height='20' texture='Icons/Attributes/Hitpoints'/>"/>
	<entry name="HitpointsPerMoraleLoss" value="Points de vie par perte de moral"/>
	<entry name="HitpointsPerMoraleLossIcon" value="<icon height='20' texture='Icons/Attributes/Hitpoints'/>"/>
	<entry name="Influence" value="Influence"/>
	<entry name="InfluenceIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InfluenceFromAdjacentBuildings" value="Influence par bâtiment adjacent"/>
	<entry name="InfluenceFromAdjacentBuildingsIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InfluenceCost" value="Coût en influence"/>
	<entry name="InfluenceCostIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InfluencePerCombat" value="Influence par combat"/>
	<entry name="InfluencePerCombatIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InfluencePerCombatFromUpkeepFactor" value="Influence gagnée par combat en fonction du coût d'entretien"/>
	<entry name="InfluencePerCombatFromUpkeepFactorIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InfluencePerDamage" value="Influence par dégâts"/>
	<entry name="InfluencePerDamageIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InfluencePerExperience" value="Influence par expérience"/>
	<entry name="InfluencePerExperienceIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InfluenceUpkeep" value="Coût d'entretien en influence"/>
	<entry name="InfluenceUpkeepIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InfluencePerKillValue" value="Influence par la valeur de tué"/>
 	<entry name="InfluencePerKillValueIcon" value="<icon height='20' texture='Icons/Attributes/Influence'/>"/>
	<entry name="InvulnerableDamageReduction" value="Réduction de dégâts Invulnérable"/>
	<entry name="InvulnerableDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	<entry name="ItemSlots" value="Emplacements d'objets"/>
	<entry name="ItemSlotsHint" value="<style name='Title'/>Emplacement d'objets <br/><style name='Default'/>"/>
	<entry name="ItemSlotsIcon" value="<icon height='20' texture='Icons/Attributes/ItemSlots'/>"/>
	<entry name="Level" value="Niveau"/>
	<entry name="LevelDescription" value="Le niveau d'expérience de l'unité.<br/><br/>Chaque niveau après le premier augmente les <icon height='20' texture='Icons/Attributes/Hitpoints'/> points de vie et les <icon height='20' texture='Icons/Attributes/Damage'/> dégâts de l'unité de 5% et son <icon height='20' texture='Icons/Attributes/Morale'/> moral de 10%."/>
	<entry name="LevelIcon" value="<icon height='20' texture='Icons/Attributes/Level'/>"/>
	<entry name="LevelMax" value="Niveau Max"/>
	<entry name="LevelMaxIcon" value="<icon height='20' texture='Icons/Attributes/Level'/>"/>
	<entry name="LifeSteal" value="Vol de vie"/>
	<entry name="LifeStealIcon" value="<icon height='20' texture='Icons/Attributes/LifeSteal'/>"/>
	<entry name="LifeStealFactor" value="Vol de vie"/>
	<entry name="LifeStealFactorIcon" value="<icon height='20' texture='Icons/Attributes/LifeSteal'/>"/>
	<entry name="LifeStealRadius" value="Rayon de vol de vie"/>
 	<entry name="LifeStealRadiusIcon" value="<icon height='20' texture='Icons/Attributes/LifeSteal'/>"/>	<entry name="Loyalty" value="Loyauté"/>
	<entry name="LoyaltyHint" value="<style name='Title'/>Loyauté<br/><style name='Default'/>Indique le dévouement de la population à votre cause. Chaque point de loyauté positif augmente la production totale de ressources d'une ville de 1% tandis que chaque point de loyauté negatif diminue la production totale de ressources d'une ville de 2% (jusqu'à -50%)."/>
	<entry name="LoyaltyIcon" value="<icon height='20' texture='Icons/Attributes/Loyalty'/>"/>
	<entry name="LoyaltyFromAdjacentBuildings" value="Loyauté par bâtiment adjacent"/>
	<entry name="LoyaltyFromAdjacentBuildingsIcon" value="<icon height='20' texture='Icons/Attributes/Loyalty'/>"/>
	<entry name="LoyaltyFromUtopia" value="Loyauté grâce à Utopie"/>
 	<entry name="LoyaltyFromUtopiaIcon" value="<icon height='20' texture='Icons/Attributes/Loyalty'/>"/>
 	<entry name="LoyaltyFromUtopiaType" value="Type Loyauté grâce à Utopie"/>
 	<entry name="LoyaltyFromUtopiaTypeIcon" value="<icon height='20' texture='Icons/Attributes/Loyalty'/>"/>
	<entry name="LoyaltyPerCity" value="Loyauté par ville"/>
	<entry name="LoyaltyPerCityIcon" value="<icon height='20' texture='Icons/Attributes/Loyalty'/>"/>
	<entry name="LoyaltyUpkeep" value="Coût d'entretien en loyauté"/>
	<entry name="LoyaltyUpkeepIcon" value="<icon height='20' texture='Icons/Attributes/Loyalty'/>"/>
	<entry name="MeleeAccuracy" value="Précision au corps à corps"/>
	<entry name="MeleeAccuracyIcon" value="<icon height='20' texture='Icons/Attributes/Accuracy'/>"/>
	<entry name="MeleeArmorPenetration" value="Pénétration d'armure au corps à corps"/>
	<entry name="MeleeArmorPenetrationIcon" value="<icon height='20' texture='Icons/Attributes/ArmorPenetration'/>"/>
	<entry name="MeleeAttacks" value="Attaques au corps à corps"/>
	<entry name="MeleeAttacksIcon" value="<icon height='20' texture='Icons/Attributes/Attacks'/>"/>
	<entry name="MeleeDamage" value="Dégâts au corps à corps"/>
	<entry name="MeleeDamageIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="MeleeDamageReduction" value="Réduction de dégâts au corps à corps"/>
	<entry name="MeleeDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	<entry name="MeleeDamageTaken" value="Dégâts subis au corps à corps"/>
	<entry name="MeleeDamageTakenIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="MinDamageFromHitpointsFraction" value="Dégâts minimum dus aux points de vie de la cible"/>
	<entry name="MinDamageFromHitpointsFractionIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="MonolithicBuildingsBonus" value="Bonus de constructions monolithiques"/>
	<entry name="MonolithicBuildingsPenalty" value="Malus de constructions monolithiques"/>
	<entry name="Morale" value="Moral"/>
 	<entry name="MoraleDescription" value="L'état psychologique de l'unité. Le moral se régénère si l'unité n'a pas subi de dégâts ce tour-ci. <br/><br/><icon texture='GUI/Bullet'/> En dessous de 66% de moral les unités deviennent <icon height='20' texture='Icons/Traits/Shaken'/> secouées, diminuant la précision et augmentant les dégâts subis de 17%.<br/><icon texture='GUI/Bullet'/>En dessous de 33% de moral les unités deviennent <icon height='20' texture='Icons/Traits/Broken'/> brisées, diminuant la précision et augmentant les dégâts subis de 33%."/>
	<entry name="MoraleIcon" value="<icon height='20' texture='Icons/Attributes/Morale'/>"/>
	<entry name="MoraleRegeneration" value="Régéneration du moral"/>
	<entry name="MoraleRegenerationIcon" value="<icon height='20' texture='Icons/Attributes/Morale'/>"/>
	<entry name="MoraleLossFactor" value="Perte en moral"/>
	<entry name="MoraleLossFactorIcon" value="<icon height='20' texture='Icons/Attributes/Morale'/>"/>
	<entry name="MoraleLossFactorPerAllyInArea" value="Perte de moral par allié proche"/>
	<entry name="MoraleLossFactorPerAllyInAreaIcon" value="<icon height='20' texture='Icons/Attributes/Morale'/>"/>
	<entry name="MoraleMax" value="Moral max"/>
	<entry name="MoraleMaxIcon" value="<icon height='20' texture='Icons/Attributes/Morale'/>"/>
	<entry name="Movement" value="Mouvement"/>
	<entry name="MovementIcon" value="<icon height='20' texture='Icons/Attributes/Movement'/>"/>
	<entry name="MovementDescription" value="Le nombre de cases qu'une unité peut traverser en un tour. Le terrain difficile nécessite plus d'un point de mouvement. Se déplacer sur une case adjacente à un ennemi termine le mouvement."/>
	<entry name="MovementCost" value="Coût en mouvement"/>
	<entry name="MovementCostIcon" value="<icon height='20' texture='Icons/Attributes/Movement'/>"/>
	<entry name="MovementMax" value="Mouvement max"/>
	<entry name="MovementMaxIcon" value="<icon height='20' texture='Icons/Attributes/Movement'/>"/>
	<entry name="OpponentAccuracy" value="Précision de l'adversaire"/>
 	<entry name="OpponentAccuracyIcon" value="<icon height='20' texture='Icons/Attributes/Accuracy'/>"/>
 	<entry name="OpponentDamage" value="Dégâts infligés à l'adversaire"/>
 	<entry name="OpponentDamageIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="OpponentRangedAccuracy" value="Précision à distance de l'ennemi"/>
	<entry name="OpponentRangedAccuracyIcon" value="<icon height='20' texture='Icons/Attributes/Accuracy'/>"/>
	<entry name="Ore" value="Minerai"/>
	<entry name="OreIcon" value="<icon height='20' texture='Icons/Attributes/Ore'/>"/>
	<entry name="OreCost" value="Coût en minerai"/>
	<entry name="OreCostIcon" value="<icon height='20' texture='Icons/Attributes/Ore'/>"/>
	<entry name="OreCostHint" value="<style name='Title'/>Coût en minerai<br/><style name='Default'/>"/>
	<entry name="OreFromAdjacentBuildings" value="Minerai par bâtiment adjacent"/>
	<entry name="OreFromAdjacentBuildingsIcon" value="<icon height='20' texture='Icons/Attributes/Ore'/>"/>
	<entry name="OrePerKillValue" value="Minerai gagné en fonction de la valeur de l'unité tuée"/>
	<entry name="OrePerKillValueIcon" value="<icon height='20' texture='Icons/Attributes/Ore'/>"/>
	<entry name="OreUpkeep" value="Coût d'entretien en minerai"/>
	<entry name="OreUpkeepIcon" value="<icon height='20' texture='Icons/Attributes/Ore'/>"/>
	<entry name="Population" value="Population"/>
	<entry name="PopulationCost" value="Coût en population"/>
	<entry name="PopulationCostIcon" value="<icon height='20' texture='Icons/Attributes/PopulationLimit'/>"/>
	<entry name="PopulationIcon" value="<icon height='20' texture='Icons/Attributes/PopulationLimit'/>"/>
	<entry name="PopulationHint" value="<style name='Title'/>Population<br/><style name='Default'/>Utilisée pour faire fonctionner les bâtiments d'une ville. Chaque bâtiment activé nécessite une unité de population pour fonctionner. Si la population requise dépasse la population actuelle, la production des bâtiments est réduite."/>
	<entry name="PopulationLimit" value="Limite de population"/>
	<entry name="PopulationLimitIcon" value="<icon height='20' texture='Icons/Attributes/PopulationLimit'/>"/>
	<entry name="PopulationLimitHint" value="<style name='Title'/>Limite de population<br/><style name='Default'/>Indique la population maximum qu'une ville peut contenir."/>
	<entry name="Production" value="Production"/>
	<entry name="ProductionIcon" value="<icon height='20' texture='Icons/Attributes/Production'/>"/>
	<entry name="ProductionHint" value="<style name='Title'/>Production<br/><style name='Default'/>Indique à quelle vitesse les bâtiments produisent."/>
	<entry name="ProductionCost" value="Coût en production"/>
	<entry name="ProductionCostIcon" value="<icon height='20' texture='Icons/Attributes/ProductionCost'/>"/>
	<entry name="ProductionFromAdjacentBuildings" value="Production par bâtiment adjacent."/>
	<entry name="ProductionFromAdjacentBuildingsIcon" value="<icon height='20' texture='Icons/Attributes/Production'/>"/>
	<entry name="Radius" value="Rayon"/>
	<entry name="RadiusDescription" value=""/>
	<entry name="RadiusIcon" value="<icon height='20' texture='Icons/Attributes/Radius'/>"/>
	<entry name="Range" value="Portée"/>
	<entry name="RangeIcon" value="<icon height='20' texture='Icons/Attributes/Range'/>"/>
	<entry name="RangeDescription" value=""/>
	<entry name="RangeMax" value="Portée maximale"/>
	<entry name="RangeMaxIcon" value="<icon height='20' texture='Icons/Attributes/Range'/>"/>
	<entry name="RangeMin" value="Portée minimale"/>
	<entry name="RangeMinIcon" value="<icon height='20' texture='Icons/Attributes/Range'/>"/>
	<entry name="RangedAccuracy" value="Précision à distance"/>
	<entry name="RangedAccuracyIcon" value="<icon height='20' texture='Icons/Attributes/Accuracy'/>"/>
	<entry name="RangedArmorPenetration" value="Pénétration d'armure à distance"/>
	<entry name="RangedArmorPenetrationIcon" value="<icon height='20' texture='Icons/Attributes/ArmorPenetration'/>"/>
	<entry name="RangedAttacks" value="Attaques à distance"/>
	<entry name="RangedAttacksIcon" value="<icon height='20' texture='Icons/Attributes/Attacks'/>"/>
	<entry name="RangedDamage" value="Dégâts à distance"/>
	<entry name="RangedDamageIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="RangedDamageReduction" value="Réduction de dégâts à distance"/>
	<entry name="RangedDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	<entry name="RangedDamageReductionBypass" value="Réduction de dégâts à distance ignorée"/>
	<entry name="RangedDamageTaken" value="Dégâts à distance subis"/>
	<entry name="RangedDamageTakenIcon" value="<icon height='20' texture='Icons/Attributes/Damage'/>"/>
	<entry name="RangedInvulnerableDamageReduction" value="Réduction de dégâts à distance Invulnérable"/>
 	<entry name="RangedInvulnerableDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	<entry name="RequiredActionPoints" value="Action requise"/>
	<entry name="RequiredActionPointsIcon" value="<icon height='20' texture='Icons/Attributes/Actions'/>"/>
	<entry name="RequiredMovement" value="Mouvement requis"/>
	<entry name="RequiredMovementIcon" value="<icon height='20' texture='Icons/Attributes/Movement'/>"/>
	<entry name="Requisitions" value="Réquisitions"/>
	<entry name="RequisitionsIcon" value="<icon height='20' texture='Icons/Attributes/Requisitions'/>"/>
	<entry name="RequisitionsCost" value="Coût en réquisitions"/>
	<entry name="RequisitionsCostIcon" value="<icon height='20' texture='Icons/Attributes/Requisitions'/>"/>
	<entry name="RequisitionsUpkeep" value="Coût d'entretien en réquisitions"/>
 	<entry name="RequisitionsUpkeepIcon" value="<icon height='20' texture='Icons/Attributes/Requisitions'/>"/>
	<entry name="Research" value="Recherche"/>
	<entry name="ResearchHint" value="<style name='Title'/>Recherche<br/><style name='Default'/>Utilisée pour découvrir de nouvelles technologies."/>
	<entry name="ResearchIcon" value="<icon height='20' texture='Icons/Attributes/Research'/>"/>
	<entry name="ResearchCost" value="Coût en recherche"/>
	<entry name="ResearchCostIcon" value="<icon height='20' texture='Icons/Attributes/Research'/>"/>
	<entry name="ResearchFromAdjacentBuildings" value="Recherche par bâtiment adjacent"/>
	<entry name="ResearchFromAdjacentBuildingsIcon" value="<icon height='20' texture='Icons/Attributes/Research'/>"/>
	<entry name="ResearchPerExperience" value="Recherche gagnée par expérience reçue"/>
	<entry name="ResearchPerExperienceIcon" value="<icon height='20' texture='Icons/Attributes/Research'/>"/>
	<entry name="ResearchPerKillValue" value="Recherche gagnée en fonction de la valeur de l'unité tuée"/>
	<entry name="ResearchPerKillValueIcon" value="<icon height='20' texture='Icons/Attributes/Research'/>"/>
	<entry name="ResearchUpkeep" value="Coût d'entretien en recherche"/>
	<entry name="ResearchUpkeepIcon" value="<icon height='20' texture='Icons/Attributes/Research'/>"/>
	<entry name="ResourcesFromFeatures" value="Ressources des caractéristiques de terrain"/>
 	<entry name="ResourcesFromFeaturesIcon" value="<icon height='20' texture='Icons/Features/Grassland'/>"/>
	<entry name="Sight" value="Vision"/>
	<entry name="SightIcon" value="<icon height='20' texture='Icons/Attributes/Sight'/>"/>
	<entry name="SightDescription" value="Jusqu'à quelle distance une unité peut voir.<br/><br/>La ligne de vue est réduite par les éléments du terrain comme les forêts, les fumigènes et les falaises."/>
	<entry name="SlotsRequired" value="Emplacements requis"/>
	<entry name="SupportSystemSlots" value="Emplacements pour les systèmes de soutien"/>
 	<entry name="SupportSystemSlotsIcon" value="<icon height='20' texture='Icons/Attributes/SupportSystemSlots'/>"/>
	<entry name="TargetArmor" value="Armure de la cible"/>
	<entry name="TargetArmorIcon" value="<icon height='20' texture='Icons/Attributes/Armor'/>"/>
	<entry name="Turns" value="Tours"/>
	<entry name="TurnsIcon" value="<icon height='20' texture='Icons/Attributes/Turns'/>"/>
	<entry name="TypeLimit" value="Limite du type"/>
	<entry name="WitchfireDamageReduction" value="Réduction de dégâts de type Décharge Psy"/>
	<entry name="WitchfireDamageReductionIcon" value="<icon height='20' texture='Icons/Attributes/DamageReduction'/>"/>
	
	<!-- Faction-specific -->
	<entry name="BiomassHint" value="<style name='Title'/>Biomasse<br/><style name='Default'/>Utilisée pour nourrir la population dans les villes, pour construire des bâtiments ainsi que pour produire et maintenir des unités."/>
	<entry name="EnergyHint" value="<style name='Title'/>Énergie<br/><style name='Default'/>Utilisée pour entretenir les bâtiments ainsi que pour produire et maintenir des unités spéciales."/>
	<entry name="FoodHint" value="<style name='Title'/>Nourriture<br/><style name='Default'/>Utilisée pour nourrir la population dans les villes ainsi que pour produire et maintenir les unités biologiques."/>
	<entry name="OreHint" value="<style name='Title'/>Minerai<br/><style name='Default'/>Utilisé pour construire les bâtiments ainsi que pour produire et maintenir les unités mécaniques."/>
	<entry name="AdeptusMechanicus/InfluenceHint" value="<string name='Attributes/Tau/InfluenceHint'/>"/>
	<entry name="AstraMilitarum/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisée pour acquérir et maintenir les cases des villes, promulguer des décrets ainsi que pour recruter de puissants héros et leur acheter des objets."/>
	<entry name="ChaosSpaceMarines/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisée pour acquérir et maintenir les cases des villes, placer des Marques du Chaos sur les unités ainsi que pour recruter de puissants héros et leur acheter des objets."/>
 	<entry name="Drukhari/InfluenceHint" value="<string name='Attributes/Eldar/InfluenceHint'/>"/>
	<entry name="Eldar/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisée pour acquérir et maintenir les cases des villes, invoquer des capacités spéciales ainsi que recruter de puissants héros et leur acheter des objets."/>
	<entry name="Necrons/EnergyHint" value="<style name='Title'/>Énergie<br/><style name='Default'/>Utilisée pour construire des bâtiments et produire les unités."/>
	<entry name="Necrons/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisée pour acquérir et maintenir les cases des villes, utiliser des compétences spéciales ainsi que pour recruter de puissants héros et leur acheter des objets."/>
	<entry name="Necrons/OreHint" value="<style name='Title'/>Minerai<br/><style name='Default'/>Utilisé pour nourrir la population des villes ainsi que pour maintenir les bâtiments et les unités."/>
	<entry name="Orks/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisée pour acquérir et maintenir les cases des villes, entretenir la Waaagh ! ainsi que pour recruter de puissants héros et leur acheter des objets."/>
	<entry name="SistersOfBattle/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisé pour acquérir et entretenir les cases de la ville, invoquer des rites sacrés ainsi que recruter de puissantes unités de héros et acheter leurs objets."/>
	<entry name="SistersOfBattle/RequisitionsHint" value="<style name='Title'/>Réquisitions<br/><style name='Default'/>Utilisé pour soutenir la population de la ville, construire des bâtiments ainsi que produire et entretenir des unités."/>	
    <entry name="SpaceMarines/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisée pour acquérir et	maintenir les cases de la ville, lancer des tactiques et des opérations ainsi que pour recruter de puissants héros et leur acheter des objets."/>
	<entry name="SpaceMarines/RequisitionsHint" value="<style name='Title'/>Réquisitions<br/><style name='Default'/>Utilisé pour nourrir la population de la ville, construire des bâtiments ainsi que pour produire et maintenir les unités."/>
	<entry name="Tau/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisée pour acquérir et maintenir les cases des villes, utiliser des compétences spéciales ainsi que pour recruter de puissants héros et leur acheter des objets."/>
	<entry name="Tyranids/InfluenceHint" value="<style name='Title'/>Influence<br/><style name='Default'/>Utilisée pour acquérir et maintenir les cases des villes, maintenir les bâtiments, utiliser des compétences spéciales ainsi que pour recruter de puissants héros et leur acheter des objets."/>
</language>