<?xml version="1.0" encoding="utf-8"?>
<unit dlc="Supplement2">
	<model>
		<unit mesh="Units/Tyranids/Exocrine"
				material="Units/Tyranids/Exocrine"
				scale="1.2 1.2 1.2"
				idleAnimation="Units/Tyranids/ExocrineIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				bloodBone="BloodBone"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="BioPlasmicCannon">
			<model>
				<projectileWeapon fireInterval="0.1" muzzleBone=".Muzzle" effectFaceWeight="0"/>
			</model>
		</weapon>
		<weapon name="ScythingTalons">
			<model>
				<weapon fireInterval="0.766666666667"/>
			</model>
		</weapon>
		<weapon name="ThresherScythe" requiredUpgrade="Tyranids/ThresherScythe">
			<model>
				<weapon fireInterval="1.0"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>				
				<armor base="8"/> <!-- %armor base armor=3+ -->
				<biomassUpkeep base="6.0"/> <!-- %biomassUpkeep base tier=9 factor=1 -->
				<biomassCost base="120.0"/> <!-- %biomassCost base tier=9 factor=1 -->
				<hitpointsMax base="36.0"/> <!-- %hitpointsMax base toughness=6 wounds=6 -->
				<meleeAccuracy base="6"/> <!-- %meleeAccuracy base weaponSkill=3 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="3"/> <!-- %strengthDamage base strength=6 -->
				<moraleMax base="6"/> <!-- %moraleMax base leadership=6 -->
				<movementMax base="3"/>
				<productionCost base="54.0"/> <!-- %productionCost base tier=9 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseVehiclesScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/ExocrineAttack"
						beginFire="0.833333333333"
						endFire="2.5"
						chargeAnimation="Units/Tyranids/ExocrineCharge"
						chargeBeginFire="0.1"
						chargeEndFire="1.23333333333"
						meleeAnimation="Units/Tyranids/ExocrineMelee"
						meleeBeginSwing="0.0"
						meleeEndSwing="0.833333333333"/>
			</model>
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/ExocrineDie"
						sound="Units/LargeUnarmoredDie"
						soundCount="4"
						soundDelay="0.5"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>
		</die>
		<move>
			<model>
				<action animation="Units/Tyranids/ExocrineMove"
						sound="Units/Tyranids/TervigonMove"
						soundCount="2"/>
			</model>
		</move>
		<auraOfFear passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" excludeRadius="0" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Fear"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</auraOfFear>
		<hammerOfWrath consumedActionPoints="0" 
				consumedMovement="0"
				cooldown="3"
				requiredActionPoints="0"
				requiredUpgrade="Tyranids/HammerOfWrath">
			<model>
				<action sound="Actions/PsychicBuff"/>
			</model>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="HammerOfWrath" duration="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</hammerOfWrath>
		<instinctiveBehaviourOverride consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/InstinctiveBehaviourOverride"
				requiredActionPoints="0">
			<model>
				<action sound="Actions/HiveMindBuff"/>
			</model>
			<modifiers>
				<modifier requiredUpgrade="Tyranids/InstinctiveBehaviourOverride2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
			</modifiers>
			<conditions>
				<unit>
					<noTrait name="Tyranids/InstinctiveBehaviourOverride"/>
					<noTrait name="Tyranids/SynapseLink"/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/InstinctiveBehaviourOverride" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</instinctiveBehaviourOverride>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="Tyranids/AcidBlood" requiredUpgrade="Tyranids/AcidBlood"/>
		<trait name="Fearless"/>
		<trait name="Fleet" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="FuriousCharge" requiredUpgrade="Tyranids/AdrenalGlands"/>
		<trait name="ExtraMonstrousCreatureArmour" requiredUpgrade="Tyranids/ExtraMonstrousCreatureArmour"/>
		<trait name="Tyranids/InstinctiveBehaviour"/>
		<trait name="MonstrousCreature"/>
		<trait name="MoveThroughCover"/>
		<trait name="Regeneration" requiredUpgrade="Tyranids/Regeneration"/>
		<trait name="Relentless"/>
		<trait name="Smash"/>
		<trait name="Tyranids/SymbioticTargeting"/>
		<trait name="Tyranids/ToxinSacs" requiredUpgrade="Tyranids/ToxinSacs"/>
		<trait name="Tyranids/VehiclesUpkeep" requiredUpgrade="Tyranids/VehiclesUpkeep"/>
	</traits>
</unit>
