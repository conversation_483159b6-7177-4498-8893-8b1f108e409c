<?xml version="1.0" encoding="utf-8"?>
<language>

	<!-- Buildings -->
	<entry name="AdeptusMechanicus/Aircraft" value="<string name='Buildings/AdeptusMechanicus/Aircraft'/>"/>
	<entry name="AdeptusMechanicus/AircraftDescription" value="<string name='Buildings/AdeptusMechanicus/AircraftDescription'/>"/>
	<entry name="AdeptusMechanicus/AircraftFlavor" value="<string name='Buildings/AdeptusMechanicus/AircraftFlavor'/>"/>
	<entry name="AdeptusMechanicus/Construction" value="<string name='Buildings/AdeptusMechanicus/Construction'/>"/>
	<entry name="AdeptusMechanicus/ConstructionDescription" value="<string name='Buildings/AdeptusMechanicus/ConstructionDescription'/>"/>
	<entry name="AdeptusMechanicus/ConstructionFlavor" value="<string name='Buildings/AdeptusMechanicus/ConstructionFlavor'/>"/>
	<entry name="AdeptusMechanicus/Heroes" value="<string name='Buildings/AdeptusMechanicus/Heroes'/>"/>
	<entry name="AdeptusMechanicus/HeroesDescription" value="<string name='Buildings/AdeptusMechanicus/HeroesDescription'/>"/>
	<entry name="AdeptusMechanicus/HeroesFlavor" value="<string name='Buildings/AdeptusMechanicus/HeroesFlavor'/>"/>
	<entry name="AdeptusMechanicus/Housing" value="<string name='Buildings/AdeptusMechanicus/Housing'/>"/>
	<entry name="AdeptusMechanicus/HousingDescription" value="<string name='Buildings/AdeptusMechanicus/HousingDescription'/>"/>
	<entry name="AdeptusMechanicus/HousingFlavor" value="<string name='Buildings/AdeptusMechanicus/HousingFlavor'/>"/>
	<entry name="AdeptusMechanicus/Loyalty" value="<string name='Buildings/AdeptusMechanicus/Loyalty'/>"/>
	<entry name="AdeptusMechanicus/LoyaltyDescription" value="<string name='Buildings/AdeptusMechanicus/LoyaltyDescription'/>"/>
	<entry name="AdeptusMechanicus/LoyaltyFlavor" value="<string name='Buildings/AdeptusMechanicus/LoyaltyFlavor'/>"/>
	<entry name="AdeptusMechanicus/Vehicles" value="<string name='Buildings/AdeptusMechanicus/Vehicles'/>"/>
	<entry name="AdeptusMechanicus/VehiclesDescription" value="<string name='Buildings/AdeptusMechanicus/VehiclesDescription'/>"/>
	<entry name="AdeptusMechanicus/VehiclesFlavor" value="<string name='Buildings/AdeptusMechanicus/VehiclesFlavor'/>"/>
	<entry name="AstraMilitarum/Aircraft" value="<string name='Buildings/AstraMilitarum/Aircraft'/>"/>
	<entry name="AstraMilitarum/AircraftDescription" value="<string name='Buildings/AstraMilitarum/AircraftDescription'/>"/>
	<entry name="AstraMilitarum/AircraftFlavor" value="<string name='Buildings/AstraMilitarum/AircraftFlavor'/>"/>
	<entry name="AstraMilitarum/Construction" value="<string name='Buildings/AstraMilitarum/Construction'/>"/>
	<entry name="AstraMilitarum/ConstructionDescription" value="<string name='Buildings/AstraMilitarum/ConstructionDescription'/>"/>
	<entry name="AstraMilitarum/ConstructionFlavor" value="<string name='Buildings/AstraMilitarum/ConstructionFlavor'/>"/>
	<entry name="AstraMilitarum/Heroes" value="<string name='Buildings/AstraMilitarum/Heroes'/>"/>
	<entry name="AstraMilitarum/HeroesDescription" value="<string name='Buildings/AstraMilitarum/HeroesDescription'/>"/>
	<entry name="AstraMilitarum/HeroesFlavor" value="<string name='Buildings/AstraMilitarum/HeroesFlavor'/>"/>
	<entry name="AstraMilitarum/Housing" value="<string name='Buildings/AstraMilitarum/Housing'/>"/>
	<entry name="AstraMilitarum/HousingDescription" value="<string name='Buildings/AstraMilitarum/HousingDescription'/>"/>
	<entry name="AstraMilitarum/HousingFlavor" value="<string name='Buildings/AstraMilitarum/HousingFlavor'/>"/>
	<entry name="AstraMilitarum/Loyalty" value="<string name='Buildings/AstraMilitarum/Loyalty'/>"/>
	<entry name="AstraMilitarum/LoyaltyDescription" value="<string name='Buildings/AstraMilitarum/LoyaltyDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyFlavor" value="<string name='Buildings/AstraMilitarum/LoyaltyFlavor'/>"/>
	<entry name="AstraMilitarum/Psykers" value="<string name='Buildings/AstraMilitarum/Psykers'/>"/>
	<entry name="AstraMilitarum/PsykersDescription" value="<string name='Buildings/AstraMilitarum/PsykersDescription'/>"/>
	<entry name="AstraMilitarum/PsykersFlavor" value="<string name='Buildings/AstraMilitarum/PsykersFlavor'/>"/>
	<entry name="AstraMilitarum/Upgrades" value="<string name='Buildings/AstraMilitarum/Upgrades'/>"/>
	<entry name="AstraMilitarum/UpgradesDescription" value="<string name='Buildings/AstraMilitarum/UpgradesDescription'/>"/>
	<entry name="AstraMilitarum/UpgradesFlavor" value="<string name='Buildings/AstraMilitarum/UpgradesFlavor'/>"/>
	<entry name="AstraMilitarum/Vehicles" value="<string name='Buildings/AstraMilitarum/Vehicles'/>"/>
	<entry name="AstraMilitarum/VehiclesDescription" value="<string name='Buildings/AstraMilitarum/VehiclesDescription'/>"/>
	<entry name="AstraMilitarum/VehiclesFlavor" value="<string name='Buildings/AstraMilitarum/VehiclesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Aircraft" value="<string name='Buildings/ChaosSpaceMarines/Aircraft'/>"/>
	<entry name="ChaosSpaceMarines/AircraftDescription" value="<string name='Buildings/ChaosSpaceMarines/AircraftDescription'/>"/>
	<entry name="ChaosSpaceMarines/AircraftFlavor" value="<string name='Buildings/ChaosSpaceMarines/AircraftFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Construction" value="<string name='Buildings/ChaosSpaceMarines/Construction'/>"/>
	<entry name="ChaosSpaceMarines/ConstructionDescription" value="<string name='Buildings/ChaosSpaceMarines/ConstructionDescription'/>"/>
	<entry name="ChaosSpaceMarines/ConstructionFlavor" value="<string name='Buildings/ChaosSpaceMarines/ConstructionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Heroes" value="<string name='Buildings/ChaosSpaceMarines/Heroes'/>"/>
	<entry name="ChaosSpaceMarines/HeroesDescription" value="<string name='Buildings/ChaosSpaceMarines/HeroesDescription'/>"/>
	<entry name="ChaosSpaceMarines/HeroesFlavor" value="<string name='Buildings/ChaosSpaceMarines/HeroesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Housing" value="<string name='Buildings/ChaosSpaceMarines/Housing'/>"/>
	<entry name="ChaosSpaceMarines/HousingDescription" value="<string name='Buildings/ChaosSpaceMarines/HousingDescription'/>"/>
	<entry name="ChaosSpaceMarines/HousingFlavor" value="<string name='Buildings/ChaosSpaceMarines/HousingFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Infantry" value="<string name='Buildings/ChaosSpaceMarines/Infantry'/>"/>
	<entry name="ChaosSpaceMarines/InfantryDescription" value="<string name='Buildings/ChaosSpaceMarines/InfantryDescription'/>"/>
	<entry name="ChaosSpaceMarines/InfantryFlavor" value="<string name='Buildings/ChaosSpaceMarines/InfantryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Loyalty" value="<string name='Buildings/ChaosSpaceMarines/Loyalty'/>"/>
	<entry name="ChaosSpaceMarines/LoyaltyDescription" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyDescription'/>"/>
	<entry name="ChaosSpaceMarines/LoyaltyFlavor" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Vehicles" value="<string name='Buildings/ChaosSpaceMarines/Vehicles'/>"/>
	<entry name="ChaosSpaceMarines/VehiclesDescription" value="<string name='Buildings/ChaosSpaceMarines/VehiclesDescription'/>"/>
	<entry name="ChaosSpaceMarines/VehiclesFlavor" value="<string name='Buildings/ChaosSpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Drukhari/Aircraft" value="<string name='Buildings/Drukhari/Aircraft'/>"/>
	<entry name="Drukhari/AircraftDescription" value="<string name='Buildings/Drukhari/AircraftDescription'/>"/>
	<entry name="Drukhari/AircraftFlavor" value="<string name='Buildings/Drukhari/AircraftFlavor'/>"/>
	<entry name="Drukhari/Construction" value="<string name='Buildings/Drukhari/Construction'/>"/>
	<entry name="Drukhari/ConstructionDescription" value="<string name='Buildings/Drukhari/ConstructionDescription'/>"/>
	<entry name="Drukhari/ConstructionFlavor" value="<string name='Buildings/Drukhari/ConstructionFlavor'/>"/>
	<entry name="Drukhari/Heroes" value="<string name='Buildings/Drukhari/Heroes'/>"/>
	<entry name="Drukhari/HeroesDescription" value="<string name='Buildings/Drukhari/HeroesDescription'/>"/>
	<entry name="Drukhari/HeroesFlavor" value="<string name='Buildings/Drukhari/HeroesFlavor'/>"/>
	<entry name="Drukhari/Housing" value="<string name='Buildings/Drukhari/Housing'/>"/>
	<entry name="Drukhari/HousingDescription" value="<string name='Buildings/Drukhari/HousingDescription'/>"/>
	<entry name="Drukhari/HousingFlavor" value="<string name='Buildings/Drukhari/HousingFlavor'/>"/>
	<entry name="Drukhari/Loyalty" value="<string name='Buildings/Drukhari/Loyalty'/>"/>
	<entry name="Drukhari/LoyaltyDescription" value="<string name='Buildings/Drukhari/LoyaltyDescription'/>"/>
	<entry name="Drukhari/LoyaltyFlavor" value="<string name='Buildings/Drukhari/LoyaltyFlavor'/>"/>
	<entry name="Drukhari/Vehicles" value="<string name='Buildings/Drukhari/Vehicles'/>"/>
	<entry name="Drukhari/VehiclesDescription" value="<string name='Buildings/Drukhari/VehiclesDescription'/>"/>
	<entry name="Drukhari/VehiclesFlavor" value="<string name='Buildings/Drukhari/VehiclesFlavor'/>"/>
	<entry name="Eldar/Aircraft" value="<string name='Buildings/Eldar/Aircraft'/>"/>
	<entry name="Eldar/AircraftDescription" value="<string name='Buildings/Eldar/AircraftDescription'/>"/>
	<entry name="Eldar/AircraftFlavor" value="<string name='Buildings/Eldar/AircraftFlavor'/>"/>
	<entry name="Eldar/Construction" value="<string name='Buildings/Eldar/Construction'/>"/>
	<entry name="Eldar/ConstructionDescription" value="<string name='Buildings/Eldar/ConstructionDescription'/>"/>
	<entry name="Eldar/ConstructionFlavor" value="<string name='Buildings/Eldar/ConstructionFlavor'/>"/>
	<entry name="Eldar/Heroes" value="<string name='Buildings/Eldar/Heroes'/>"/>
	<entry name="Eldar/HeroesDescription" value="<string name='Buildings/Eldar/HeroesDescription'/>"/>
	<entry name="Eldar/HeroesFlavor" value="<string name='Buildings/Eldar/HeroesFlavor'/>"/>
	<entry name="Eldar/Housing" value="<string name='Buildings/Eldar/Housing'/>"/>
	<entry name="Eldar/HousingDescription" value="<string name='Buildings/Eldar/HousingDescription'/>"/>
	<entry name="Eldar/HousingFlavor" value="<string name='Buildings/Eldar/HousingFlavor'/>"/>
	<entry name="Eldar/Infantry" value="<string name='Buildings/Eldar/Infantry'/>"/>
	<entry name="Eldar/InfantryDescription" value="<string name='Buildings/Eldar/InfantryDescription'/>"/>
	<entry name="Eldar/InfantryFlavor" value="<string name='Buildings/Eldar/InfantryFlavor'/>"/>
	<entry name="Eldar/Loyalty" value="<string name='Buildings/Eldar/Loyalty'/>"/>
	<entry name="Eldar/LoyaltyDescription" value="<string name='Buildings/Eldar/LoyaltyDescription'/>"/>
	<entry name="Eldar/LoyaltyFlavor" value="<string name='Buildings/Eldar/LoyaltyFlavor'/>"/>
	<entry name="Eldar/Vehicles" value="<string name='Buildings/Eldar/Vehicles'/>"/>
	<entry name="Eldar/VehiclesDescription" value="<string name='Buildings/Eldar/VehiclesDescription'/>"/>
	<entry name="Eldar/VehiclesFlavor" value="<string name='Buildings/Eldar/VehiclesFlavor'/>"/>
	<entry name="Necrons/Aircraft" value="<string name='Buildings/Necrons/Aircraft'/>"/>
	<entry name="Necrons/AircraftDescription" value="<string name='Buildings/Necrons/AircraftDescription'/>"/>
	<entry name="Necrons/AircraftFlavor" value="<string name='Buildings/Necrons/AircraftFlavor'/>"/>
	<entry name="Necrons/Construction" value="<string name='Buildings/Necrons/Construction'/>"/>
	<entry name="Necrons/ConstructionDescription" value="<string name='Buildings/Necrons/ConstructionDescription'/>"/>
	<entry name="Necrons/ConstructionFlavor" value="<string name='Buildings/Necrons/ConstructionFlavor'/>"/>
	<entry name="Necrons/Heroes" value="<string name='Buildings/Necrons/Heroes'/>"/>
	<entry name="Necrons/HeroesDescription" value="<string name='Buildings/Necrons/HeroesDescription'/>"/>
	<entry name="Necrons/HeroesFlavor" value="<string name='Buildings/Necrons/HeroesFlavor'/>"/>
	<entry name="Necrons/Housing" value="<string name='Buildings/Necrons/Housing'/>"/>
	<entry name="Necrons/HousingDescription" value="<string name='Buildings/Necrons/HousingDescription'/>"/>
	<entry name="Necrons/HousingFlavor" value="<string name='Buildings/Necrons/HousingFlavor'/>"/>
	<entry name="Necrons/Loyalty" value="<string name='Buildings/Necrons/Loyalty'/>"/>
	<entry name="Necrons/LoyaltyDescription" value="<string name='Buildings/Necrons/LoyaltyDescription'/>"/>
	<entry name="Necrons/LoyaltyFlavor" value="<string name='Buildings/Necrons/LoyaltyFlavor'/>"/>
	<entry name="Necrons/Vehicles" value="<string name='Buildings/Necrons/Vehicles'/>"/>
	<entry name="Necrons/VehiclesDescription" value="<string name='Buildings/Necrons/VehiclesDescription'/>"/>
	<entry name="Necrons/VehiclesFlavor" value="<string name='Buildings/Necrons/VehiclesFlavor'/>"/>
	<entry name="Orks/Beasts" value="<string name='Buildings/Orks/Beasts'/>"/>
	<entry name="Orks/BeastsDescription" value="<string name='Buildings/Orks/BeastsDescription'/>"/>
	<entry name="Orks/BeastsFlavor" value="<string name='Buildings/Orks/BeastsFlavor'/>"/>
	<entry name="Orks/Colonizers" value="<string name='Buildings/Orks/Colonizers'/>"/>
	<entry name="Orks/ColonizersDescription" value="<string name='Buildings/Orks/ColonizersDescription'/>"/>
	<entry name="Orks/ColonizersFlavor" value="<string name='Buildings/Orks/ColonizersFlavor'/>"/>
	<entry name="Orks/Construction" value="<string name='Buildings/Orks/Construction'/>"/>
	<entry name="Orks/ConstructionDescription" value="<string name='Buildings/Orks/ConstructionDescription'/>"/>
	<entry name="Orks/ConstructionFlavor" value="<string name='Buildings/Orks/ConstructionFlavor'/>"/>
	<entry name="Orks/Heroes" value="<string name='Buildings/Orks/Heroes'/>"/>
	<entry name="Orks/HeroesDescription" value="<string name='Buildings/Orks/HeroesDescription'/>"/>
	<entry name="Orks/HeroesFlavor" value="<string name='Buildings/Orks/HeroesFlavor'/>"/>
	<entry name="Orks/Housing" value="<string name='Buildings/Orks/Housing'/>"/>
	<entry name="Orks/HousingDescription" value="<string name='Buildings/Orks/HousingDescription'/>"/>
	<entry name="Orks/HousingFlavor" value="<string name='Buildings/Orks/HousingFlavor'/>"/>
	<entry name="Orks/Loyalty" value="<string name='Buildings/Orks/Loyalty'/>"/>
	<entry name="Orks/LoyaltyDescription" value="<string name='Buildings/Orks/LoyaltyDescription'/>"/>
	<entry name="Orks/LoyaltyFlavor" value="<string name='Buildings/Orks/LoyaltyFlavor'/>"/>
	<entry name="Orks/Vehicles" value="<string name='Buildings/Orks/Vehicles'/>"/>
	<entry name="Orks/VehiclesDescription" value="<string name='Buildings/Orks/VehiclesDescription'/>"/>
	<entry name="Orks/VehiclesFlavor" value="<string name='Buildings/Orks/VehiclesFlavor'/>"/>
	<entry name="SistersOfBattle/Auxiliaries" value="<string name='Buildings/SistersOfBattle/Auxiliaries'/>"/>
	<entry name="SistersOfBattle/AuxiliariesDescription" value="<string name='Buildings/SistersOfBattle/AuxiliariesDescription'/>"/>
	<entry name="SistersOfBattle/AuxiliariesFlavor" value="<string name='Buildings/SistersOfBattle/AuxiliariesFlavor'/>"/>
	<entry name="SistersOfBattle/Construction" value="<string name='Buildings/SistersOfBattle/Construction'/>"/>
	<entry name="SistersOfBattle/ConstructionDescription" value="<string name='Buildings/SistersOfBattle/ConstructionDescription'/>"/>
	<entry name="SistersOfBattle/ConstructionFlavor" value="<string name='Buildings/SistersOfBattle/ConstructionFlavor'/>"/>
	<entry name="SistersOfBattle/Heroes" value="<string name='Buildings/SistersOfBattle/Heroes'/>"/>
	<entry name="SistersOfBattle/HeroesDescription" value="<string name='Buildings/SistersOfBattle/HeroesDescription'/>"/>
	<entry name="SistersOfBattle/HeroesFlavor" value="<string name='Buildings/SistersOfBattle/HeroesFlavor'/>"/>
	<entry name="SistersOfBattle/Housing" value="<string name='Buildings/SistersOfBattle/Housing'/>"/>
	<entry name="SistersOfBattle/HousingDescription" value="<string name='Buildings/SistersOfBattle/HousingDescription'/>"/>
	<entry name="SistersOfBattle/HousingFlavor" value="<string name='Buildings/SistersOfBattle/HousingFlavor'/>"/>
	<entry name="SistersOfBattle/Loyalty" value="<string name='Buildings/SistersOfBattle/Loyalty'/>"/>
	<entry name="SistersOfBattle/LoyaltyDescription" value="<string name='Buildings/SistersOfBattle/LoyaltyDescription'/>"/>
	<entry name="SistersOfBattle/LoyaltyFlavor" value="<string name='Buildings/SistersOfBattle/LoyaltyFlavor'/>"/>
	<entry name="SistersOfBattle/Vehicles" value="<string name='Buildings/SistersOfBattle/Vehicles'/>"/>
	<entry name="SistersOfBattle/VehiclesDescription" value="<string name='Buildings/SistersOfBattle/VehiclesDescription'/>"/>
	<entry name="SistersOfBattle/VehiclesFlavor" value="<string name='Buildings/SistersOfBattle/VehiclesFlavor'/>"/>
	<entry name="SpaceMarines/Aircraft" value="<string name='Buildings/SpaceMarines/Aircraft'/>"/>
	<entry name="SpaceMarines/AircraftDescription" value="<string name='Buildings/SpaceMarines/AircraftDescription'/>"/>
	<entry name="SpaceMarines/AircraftFlavor" value="<string name='Buildings/SpaceMarines/AircraftFlavor'/>"/>
	<entry name="SpaceMarines/Construction" value="<string name='Buildings/SpaceMarines/Construction'/>"/>
	<entry name="SpaceMarines/ConstructionDescription" value="<string name='Buildings/SpaceMarines/ConstructionDescription'/>"/>
	<entry name="SpaceMarines/ConstructionFlavor" value="<string name='Buildings/SpaceMarines/ConstructionFlavor'/>"/>
	<entry name="SpaceMarines/GeneseedBunker" value="<string name='Buildings/SpaceMarines/GeneseedBunker'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerDescription" value="<string name='Buildings/SpaceMarines/GeneseedBunkerDescription'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerFlavor" value="<string name='Buildings/SpaceMarines/GeneseedBunkerFlavor'/>"/>
	<entry name="SpaceMarines/Heroes" value="<string name='Buildings/SpaceMarines/Heroes'/>"/>
	<entry name="SpaceMarines/HeroesDescription" value="<string name='Buildings/SpaceMarines/HeroesDescription'/>"/>
	<entry name="SpaceMarines/HeroesFlavor" value="<string name='Buildings/SpaceMarines/HeroesFlavor'/>"/>
	<entry name="SpaceMarines/Housing" value="<string name='Buildings/SpaceMarines/Housing'/>"/>
	<entry name="SpaceMarines/HousingDescription" value="<string name='Buildings/SpaceMarines/HousingDescription'/>"/>
	<entry name="SpaceMarines/HousingFlavor" value="<string name='Buildings/SpaceMarines/HousingFlavor'/>"/>
	<entry name="SpaceMarines/Loyalty" value="<string name='Buildings/SpaceMarines/Loyalty'/>"/>
	<entry name="SpaceMarines/LoyaltyDescription" value="<string name='Buildings/SpaceMarines/LoyaltyDescription'/>"/>
	<entry name="SpaceMarines/LoyaltyFlavor" value="<string name='Buildings/SpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="SpaceMarines/Vehicles" value="<string name='Buildings/SpaceMarines/Vehicles'/>"/>
	<entry name="SpaceMarines/VehiclesDescription" value="<string name='Buildings/SpaceMarines/VehiclesDescription'/>"/>
	<entry name="SpaceMarines/VehiclesFlavor" value="<string name='Buildings/SpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Tau/Aircraft" value="<string name='Buildings/Tau/Aircraft'/>"/>
	<entry name="Tau/AircraftDescription" value="<string name='Buildings/Tau/AircraftDescription'/>"/>
	<entry name="Tau/AircraftFlavor" value="<string name='Buildings/Tau/AircraftFlavor'/>"/>
	<entry name="Tau/Construction" value="<string name='Buildings/Tau/Construction'/>"/>
	<entry name="Tau/ConstructionDescription" value="<string name='Buildings/Tau/ConstructionDescription'/>"/>
	<entry name="Tau/ConstructionFlavor" value="<string name='Buildings/Tau/ConstructionFlavor'/>"/>
	<entry name="Tau/Heroes" value="<string name='Buildings/Tau/Heroes'/>"/>
	<entry name="Tau/HeroesDescription" value="<string name='Buildings/Tau/HeroesDescription'/>"/>
	<entry name="Tau/HeroesFlavor" value="<string name='Buildings/Tau/HeroesFlavor'/>"/>
	<entry name="Tau/Housing" value="<string name='Buildings/Tau/Housing'/>"/>
	<entry name="Tau/HousingDescription" value="<string name='Buildings/Tau/HousingDescription'/>"/>
	<entry name="Tau/HousingFlavor" value="<string name='Buildings/Tau/HousingFlavor'/>"/>
	<entry name="Tau/Loyalty" value="<string name='Buildings/Tau/Loyalty'/>"/>
	<entry name="Tau/LoyaltyDescription" value="<string name='Buildings/Tau/LoyaltyDescription'/>"/>
	<entry name="Tau/LoyaltyFlavor" value="<string name='Buildings/Tau/LoyaltyFlavor'/>"/>
	<entry name="Tau/MonstrousCreatures" value="<string name='Buildings/Tau/MonstrousCreatures'/>"/>
	<entry name="Tau/MonstrousCreaturesDescription" value="<string name='Buildings/Tau/MonstrousCreaturesDescription'/>"/>
	<entry name="Tau/MonstrousCreaturesFlavor" value="<string name='Buildings/Tau/MonstrousCreaturesFlavor'/>"/>
	<entry name="Tau/Vehicles" value="<string name='Buildings/Tau/Vehicles'/>"/>
	<entry name="Tau/VehiclesDescription" value="<string name='Buildings/Tau/VehiclesDescription'/>"/>
	<entry name="Tau/VehiclesFlavor" value="<string name='Buildings/Tau/VehiclesFlavor'/>"/>
	<entry name="Tyranids/Aircraft" value="<string name='Buildings/Tyranids/Aircraft'/>"/>
	<entry name="Tyranids/AircraftDescription" value="<string name='Buildings/Tyranids/AircraftDescription'/>"/>
	<entry name="Tyranids/AircraftFlavor" value="<string name='Buildings/Tyranids/AircraftFlavor'/>"/>
	<entry name="Tyranids/Construction" value="<string name='Buildings/Tyranids/Construction'/>"/>
	<entry name="Tyranids/ConstructionDescription" value="<string name='Buildings/Tyranids/ConstructionDescription'/>"/>
	<entry name="Tyranids/ConstructionFlavor" value="<string name='Buildings/Tyranids/ConstructionFlavor'/>"/>
	<entry name="Tyranids/Heroes" value="<string name='Buildings/Tyranids/Heroes'/>"/>
	<entry name="Tyranids/HeroesDescription" value="<string name='Buildings/Tyranids/HeroesDescription'/>"/>
	<entry name="Tyranids/HeroesFlavor" value="<string name='Buildings/Tyranids/HeroesFlavor'/>"/>
	<entry name="Tyranids/Housing" value="<string name='Buildings/Tyranids/Housing'/>"/>
	<entry name="Tyranids/HousingDescription" value="<string name='Buildings/Tyranids/HousingDescription'/>"/>
	<entry name="Tyranids/HousingFlavor" value="<string name='Buildings/Tyranids/HousingFlavor'/>"/>
	<entry name="Tyranids/Loyalty" value="<string name='Buildings/Tyranids/Loyalty'/>"/>
	<entry name="Tyranids/LoyaltyDescription" value="<string name='Buildings/Tyranids/LoyaltyDescription'/>"/>
	<entry name="Tyranids/LoyaltyFlavor" value="<string name='Buildings/Tyranids/LoyaltyFlavor'/>"/>
	<entry name="Tyranids/Thropes" value="<string name='Buildings/Tyranids/Thropes'/>"/>
	<entry name="Tyranids/ThropesDescription" value="<string name='Buildings/Tyranids/ThropesDescription'/>"/>
	<entry name="Tyranids/ThropesFlavor" value="<string name='Buildings/Tyranids/ThropesFlavor'/>"/>
	<entry name="Tyranids/Vehicles" value="<string name='Buildings/Tyranids/Vehicles'/>"/>
	<entry name="Tyranids/VehiclesDescription" value="<string name='Buildings/Tyranids/VehiclesDescription'/>"/>
	<entry name="Tyranids/VehiclesFlavor" value="<string name='Buildings/Tyranids/VehiclesFlavor'/>"/>
	
	<!-- Edicts -->
	<entry name="AstraMilitarum/AircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictDescription" value="<string name='Actions/AstraMilitarumAircraftProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/DefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarum/DefenseEdictDescription" value="<string name='Actions/AstraMilitarumDefenseEdictDescription'/>"/>
	<entry name="AstraMilitarum/DefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarum/EnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarum/EnergyEdictDescription" value="<string name='Actions/AstraMilitarumEnergyEdictDescription'/>"/>
	<entry name="AstraMilitarum/EnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/FoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarum/FoodEdictDescription" value="<string name='Actions/AstraMilitarumFoodEdictDescription'/>"/>
	<entry name="AstraMilitarum/FoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarum/GrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarum/GrowthEdictDescription" value="<string name='Actions/AstraMilitarumGrowthEdictDescription'/>"/>
	<entry name="AstraMilitarum/GrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictDescription" value="<string name='Actions/AstraMilitarumInfantryProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictDescription" value="<string name='Actions/AstraMilitarumLoyaltyEdictDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/OreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarum/OreEdictDescription" value="<string name='Actions/AstraMilitarumOreEdictDescription'/>"/>
	<entry name="AstraMilitarum/OreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictDescription" value="<string name='Actions/AstraMilitarumPsykerProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/ResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarum/ResearchEdictDescription" value="<string name='Actions/AstraMilitarumResearchEdictDescription'/>"/>
	<entry name="AstraMilitarum/ResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictDescription" value="<string name='Actions/AstraMilitarumVehicleProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>
		
	<!-- Units -->
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptor'/>"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorDescription" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorDescription'/>"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorFlavor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorFlavor'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriest'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestDescription" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestDescription'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestFlavor" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestFlavor'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobot" value="<string name='Units/Neutral/KastelanRobot'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobotDescription" value="<string name='Units/Neutral/KastelanRobotDescription'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobotFlavor" value="<string name='Units/Neutral/KastelanRobotFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacher" value="<string name='Units/AdeptusMechanicus/KataphronBreacher'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherDescription" value="<string name='Units/AdeptusMechanicus/KataphronBreacherDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherFlavor" value="<string name='Units/AdeptusMechanicus/KataphronBreacherFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer" value="<string name='Units/AdeptusMechanicus/KataphronDestroyer'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerDescription" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerFlavor" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerFlavor'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusader" value="<string name='Units/AdeptusMechanicus/KnightCrusader'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusaderDescription" value="<string name='Units/AdeptusMechanicus/KnightCrusaderDescription'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusaderFlavor" value="<string name='Units/AdeptusMechanicus/KnightCrusaderFlavor'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawler'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerDescription" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerDescription'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerFlavor" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerFlavor'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizor'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorDescription" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorDescription'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorFlavor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhound'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundDescription" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundDescription'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundFlavor" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator" value="<string name='Units/AdeptusMechanicus/SicarianInfiltrator'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorDescription" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorDescription'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorFlavor" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker" value="<string name='Units/AdeptusMechanicus/SicarianRuststalker'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerDescription" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerDescription'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalkerFlavor" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal" value="<string name='Units/AdeptusMechanicus/SkitariiMarshal'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalDescription" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="<string name='Units/AdeptusMechanicus/SkitariiRanger'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerDescription" value="<string name='Units/AdeptusMechanicus/SkitariiRangerDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiRangerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegrator'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorDescription'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDunerider" value="<string name='Units/AdeptusMechanicus/SkorpiusDunerider'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderDescription'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderFlavor'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoon" value="<string name='Units/AdeptusMechanicus/SydonianDragoon'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoonDescription" value="<string name='Units/AdeptusMechanicus/SydonianDragoonDescription'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoonFlavor" value="<string name='Units/AdeptusMechanicus/SydonianDragoonFlavor'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominus" value="<string name='Units/AdeptusMechanicus/TechPriestDominus'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestDominusDescription'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestDominusFlavor'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus" value="<string name='Units/AdeptusMechanicus/TechPriestManipulus'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusDescription'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusFlavor'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRider" value="<string name='Units/AstraMilitarum/AttilanRoughRider'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderDescription" value="<string name='Units/AstraMilitarum/AttilanRoughRiderDescription'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderFlavor" value="<string name='Units/AstraMilitarum/AttilanRoughRiderFlavor'/>"/>
	<entry name="AstraMilitarum/Baneblade" value="<string name='Units/AstraMilitarum/Baneblade'/>"/>
	<entry name="AstraMilitarum/BanebladeDescription" value="<string name='Units/AstraMilitarum/BanebladeDescription'/>"/>
	<entry name="AstraMilitarum/BanebladeFlavor" value="<string name='Units/AstraMilitarum/BanebladeFlavor'/>"/>
	<entry name="AstraMilitarum/Basilisk" value="<string name='Units/AstraMilitarum/Basilisk'/>"/>
	<entry name="AstraMilitarum/BasiliskDescription" value="<string name='Units/AstraMilitarum/BasiliskDescription'/>"/>
	<entry name="AstraMilitarum/BasiliskFlavor" value="<string name='Units/AstraMilitarum/BasiliskFlavor'/>"/>
	<entry name="AstraMilitarum/Bullgryn" value="<string name='Units/AstraMilitarum/Bullgryn'/>"/>
	<entry name="AstraMilitarum/BullgrynDescription" value="<string name='Units/AstraMilitarum/BullgrynDescription'/>"/>
	<entry name="AstraMilitarum/BullgrynFlavor" value="<string name='Units/AstraMilitarum/BullgrynFlavor'/>"/>
	<entry name="AstraMilitarum/Chimera" value="<string name='Units/AstraMilitarum/Chimera'/>"/>
	<entry name="AstraMilitarum/ChimeraDescription" value="<string name='Units/AstraMilitarum/ChimeraDescription'/>"/>
	<entry name="AstraMilitarum/ChimeraFlavor" value="<string name='Units/AstraMilitarum/ChimeraFlavor'/>"/>
	<entry name="AstraMilitarum/DevilDog" value="<string name='Units/AstraMilitarum/DevilDog'/>"/>
	<entry name="AstraMilitarum/DevilDogDescription" value="<string name='Units/AstraMilitarum/DevilDogDescription'/>"/>
	<entry name="AstraMilitarum/DevilDogFlavor" value="<string name='Units/AstraMilitarum/DevilDogFlavor'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery" value="<string name='Units/AstraMilitarum/FieldOrdnanceBattery'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryDescription" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryDescription'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBatteryFlavor" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryFlavor'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquad'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadDescription" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadDescription'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadFlavor" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadFlavor'/>"/>
	<entry name="AstraMilitarum/Hydra" value="<string name='Units/AstraMilitarum/Hydra'/>"/>
	<entry name="AstraMilitarum/HydraDescription" value="<string name='Units/AstraMilitarum/HydraDescription'/>"/>
	<entry name="AstraMilitarum/HydraFlavor" value="<string name='Units/AstraMilitarum/HydraFlavor'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTank" value="<string name='Units/AstraMilitarum/LemanRussBattleTank'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankDescription" value="<string name='Units/AstraMilitarum/LemanRussBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankFlavor" value="<string name='Units/AstraMilitarum/LemanRussBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/LordCommissar" value="<string name='Units/AstraMilitarum/LordCommissar'/>"/>
	<entry name="AstraMilitarum/LordCommissarDescription" value="<string name='Units/AstraMilitarum/LordCommissarDescription'/>"/>
	<entry name="AstraMilitarum/LordCommissarFlavor" value="<string name='Units/AstraMilitarum/LordCommissarFlavor'/>"/>
	<entry name="AstraMilitarum/MarauderBomber" value="<string name='Units/AstraMilitarum/MarauderBomber'/>"/>
	<entry name="AstraMilitarum/MarauderBomberDescription" value="<string name='Units/AstraMilitarum/MarauderBomberDescription'/>"/>
	<entry name="AstraMilitarum/MarauderBomberFlavor" value="<string name='Units/AstraMilitarum/MarauderBomberFlavor'/>"/>
	<entry name="AstraMilitarum/PrimarisPsyker" value="<string name='Units/AstraMilitarum/PrimarisPsyker'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerDescription" value="<string name='Units/AstraMilitarum/PrimarisPsykerDescription'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerFlavor" value="<string name='Units/AstraMilitarum/PrimarisPsykerFlavor'/>"/>
	<entry name="AstraMilitarum/Ratling" value="<string name='Units/AstraMilitarum/Ratling'/>"/>
	<entry name="AstraMilitarum/RatlingDescription" value="<string name='Units/AstraMilitarum/RatlingDescription'/>"/>
	<entry name="AstraMilitarum/RatlingFlavor" value="<string name='Units/AstraMilitarum/RatlingFlavor'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTank" value="<string name='Units/AstraMilitarum/RogalDornBattleTank'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTankDescription" value="<string name='Units/AstraMilitarum/RogalDornBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTankFlavor" value="<string name='Units/AstraMilitarum/RogalDornBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/ScoutSentinel" value="<string name='Units/AstraMilitarum/ScoutSentinel'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelDescription" value="<string name='Units/AstraMilitarum/ScoutSentinelDescription'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelFlavor" value="<string name='Units/AstraMilitarum/ScoutSentinelFlavor'/>"/>
	<entry name="AstraMilitarum/TankCommander" value="<string name='Units/AstraMilitarum/TankCommander'/>"/>
	<entry name="AstraMilitarum/TankCommanderDescription" value="<string name='Units/AstraMilitarum/TankCommanderDescription'/>"/>
	<entry name="AstraMilitarum/TankCommanderFlavor" value="<string name='Units/AstraMilitarum/TankCommanderFlavor'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseer" value="<string name='Units/AstraMilitarum/TechpriestEnginseer'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerDescription" value="<string name='Units/AstraMilitarum/TechpriestEnginseerDescription'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerFlavor" value="<string name='Units/AstraMilitarum/TechpriestEnginseerFlavor'/>"/>
	<entry name="AstraMilitarum/TempestusScion" value="<string name='Units/AstraMilitarum/TempestusScion'/>"/>
	<entry name="AstraMilitarum/TempestusScionDescription" value="<string name='Units/AstraMilitarum/TempestusScionDescription'/>"/>
	<entry name="AstraMilitarum/TempestusScionFlavor" value="<string name='Units/AstraMilitarum/TempestusScionFlavor'/>"/>
	<entry name="AstraMilitarum/Thunderbolt" value="<string name='Units/AstraMilitarum/Thunderbolt'/>"/>
	<entry name="AstraMilitarum/ThunderboltDescription" value="<string name='Units/AstraMilitarum/ThunderboltDescription'/>"/>
	<entry name="AstraMilitarum/ThunderboltFlavor" value="<string name='Units/AstraMilitarum/ThunderboltFlavor'/>"/>
	<entry name="AstraMilitarum/Valkyrie" value="<string name='Units/AstraMilitarum/Valkyrie'/>"/>
	<entry name="AstraMilitarum/ValkyrieDescription" value="<string name='Units/AstraMilitarum/ValkyrieDescription'/>"/>
	<entry name="AstraMilitarum/ValkyrieFlavor" value="<string name='Units/AstraMilitarum/ValkyrieFlavor'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsyker" value="<string name='Units/AstraMilitarum/WyrdvanePsyker'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerDescription" value="<string name='Units/AstraMilitarum/WyrdvanePsykerDescription'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerFlavor" value="<string name='Units/AstraMilitarum/WyrdvanePsykerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaider'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderDescription" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn" value="<string name='Units/ChaosSpaceMarines/ChaosSpawn'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnDescription" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="<string name='Units/ChaosSpaceMarines/ChaosTerminator'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorDescription" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminatorFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrince" value="<string name='Units/ChaosSpaceMarines/DaemonPrince'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceDescription" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceDescription'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceFlavor" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DarkDisciple" value="<string name='Units/ChaosSpaceMarines/DarkDisciple'/>"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleDescription" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleDescription'/>"/>
	<entry name="ChaosSpaceMarines/DarkDiscipleFlavor" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Defiler" value="<string name='Units/ChaosSpaceMarines/Defiler'/>"/>
	<entry name="ChaosSpaceMarines/DefilerDescription" value="<string name='Units/ChaosSpaceMarines/DefilerDescription'/>"/>
	<entry name="ChaosSpaceMarines/DefilerFlavor" value="<string name='Units/ChaosSpaceMarines/DefilerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Havoc" value="<string name='Units/ChaosSpaceMarines/Havoc'/>"/>
	<entry name="ChaosSpaceMarines/HavocDescription" value="<string name='Units/ChaosSpaceMarines/HavocDescription'/>"/>
	<entry name="ChaosSpaceMarines/HavocFlavor" value="<string name='Units/ChaosSpaceMarines/HavocFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Helbrute" value="<string name='Units/ChaosSpaceMarines/Helbrute'/>"/>
	<entry name="ChaosSpaceMarines/HelbruteDescription" value="<string name='Units/ChaosSpaceMarines/HelbruteDescription'/>"/>
	<entry name="ChaosSpaceMarines/HelbruteFlavor" value="<string name='Units/ChaosSpaceMarines/HelbruteFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerker'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerDescription" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerDescription'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerFlavor" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="<string name='Units/ChaosSpaceMarines/Forgefiend'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendDescription" value="<string name='Units/ChaosSpaceMarines/ForgefiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendFlavor" value="<string name='Units/ChaosSpaceMarines/ForgefiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession" value="<string name='Units/ChaosSpaceMarines/MasterOfPossession'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionDescription" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionDescription'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionFlavor" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Maulerfiend" value="<string name='Units/ChaosSpaceMarines/Maulerfiend'/>"/>
	<entry name="ChaosSpaceMarines/MaulerfiendDescription" value="<string name='Units/ChaosSpaceMarines/MaulerfiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/MaulerfiendFlavor" value="<string name='Units/ChaosSpaceMarines/MaulerfiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrown" value="<string name='Units/ChaosSpaceMarines/NoctilithCrown'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrownDescription" value="使混沌狂热者获得建造堡垒的能力."/>
	<entry name="ChaosSpaceMarines/NoctilithCrownFlavor" value="<string name='Units/ChaosSpaceMarines/NoctilithCrownFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Obliterator" value="<string name='Units/ChaosSpaceMarines/Obliterator'/>"/>
	<entry name="ChaosSpaceMarines/ObliteratorDescription" value="<string name='Units/ChaosSpaceMarines/ObliteratorDescription'/>"/>
	<entry name="ChaosSpaceMarines/ObliteratorFlavor" value="<string name='Units/ChaosSpaceMarines/ObliteratorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarine" value="<string name='Units/ChaosSpaceMarines/RubricMarine'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineDescription" value="<string name='Units/ChaosSpaceMarines/RubricMarineDescription'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineFlavor" value="<string name='Units/ChaosSpaceMarines/RubricMarineFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Venomcrawler" value="<string name='Units/ChaosSpaceMarines/Venomcrawler'/>"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerDescription" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerDescription'/>"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerFlavor" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Warpsmith" value="<string name='Units/ChaosSpaceMarines/Warpsmith'/>"/>
	<entry name="ChaosSpaceMarines/WarpsmithDescription" value="<string name='Units/ChaosSpaceMarines/WarpsmithDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpsmithFlavor" value="<string name='Units/ChaosSpaceMarines/WarpsmithFlavor'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalon" value="<string name='Units/ChaosSpaceMarines/WarpTalon'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonDescription" value="<string name='Units/ChaosSpaceMarines/WarpTalonDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonFlavor" value="<string name='Units/ChaosSpaceMarines/WarpTalonFlavor'/>"/>
	<entry name="Drukhari/Cronos" value="<string name='Units/Drukhari/Cronos'/>"/>
	<entry name="Drukhari/CronosDescription" value="<string name='Units/Drukhari/CronosDescription'/>"/>
	<entry name="Drukhari/CronosFlavor" value="<string name='Units/Drukhari/CronosFlavor'/>"/>
	<entry name="Drukhari/Haemonculus" value="<string name='Units/Drukhari/Haemonculus'/>"/>
	<entry name="Drukhari/HaemonculusDescription" value="<string name='Units/Drukhari/HaemonculusDescription'/>"/>
	<entry name="Drukhari/HaemonculusFlavor" value="<string name='Units/Drukhari/HaemonculusFlavor'/>"/>
	<entry name="Drukhari/Hellion" value="<string name='Units/Drukhari/Hellion'/>"/>
	<entry name="Drukhari/HellionDescription" value="<string name='Units/Drukhari/HellionDescription'/>"/>
	<entry name="Drukhari/HellionFlavor" value="<string name='Units/Drukhari/HellionFlavor'/>"/>
	<entry name="Drukhari/Incubi" value="<string name='Units/Drukhari/Incubi'/>"/>
	<entry name="Drukhari/IncubiDescription" value="<string name='Units/Drukhari/IncubiDescription'/>"/>
	<entry name="Drukhari/IncubiFlavor" value="<string name='Units/Drukhari/IncubiFlavor'/>"/>
	<entry name="Drukhari/KabaliteTrueborn" value="<string name='Units/Drukhari/KabaliteTrueborn'/>"/>
	<entry name="Drukhari/KabaliteTruebornDescription" value="<string name='Units/Drukhari/KabaliteTruebornDescription'/>"/>
	<entry name="Drukhari/KabaliteTruebornFlavor" value="<string name='Units/Drukhari/KabaliteTruebornFlavor'/>"/>
	<entry name="Drukhari/Mandrake" value="<string name='Units/Drukhari/Mandrake'/>"/>
	<entry name="Drukhari/MandrakeDescription" value="<string name='Units/Drukhari/MandrakeDescription'/>"/>
	<entry name="Drukhari/MandrakeFlavor" value="<string name='Units/Drukhari/MandrakeFlavor'/>"/>
	<entry name="Drukhari/Raider" value="<string name='Units/Drukhari/Raider'/>"/>
	<entry name="Drukhari/RaiderDescription" value="<string name='Units/Drukhari/RaiderDescription'/>"/>
	<entry name="Drukhari/RaiderFlavor" value="<string name='Units/Drukhari/RaiderFlavor'/>"/>
	<entry name="Drukhari/Ravager" value="<string name='Units/Drukhari/Ravager'/>"/>
	<entry name="Drukhari/RavagerDescription" value="<string name='Units/Drukhari/RavagerDescription'/>"/>
	<entry name="Drukhari/RavagerFlavor" value="<string name='Units/Drukhari/RavagerFlavor'/>"/>
	<entry name="Drukhari/Reaver" value="<string name='Units/Drukhari/Reaver'/>"/>
	<entry name="Drukhari/ReaverDescription" value="<string name='Units/Drukhari/ReaverDescription'/>"/>
	<entry name="Drukhari/ReaverFlavor" value="<string name='Units/Drukhari/ReaverFlavor'/>"/>
	<entry name="Drukhari/Scourge" value="<string name='Units/Drukhari/Scourge'/>"/>
	<entry name="Drukhari/ScourgeDescription" value="<string name='Units/Drukhari/ScourgeDescription'/>"/>
	<entry name="Drukhari/ScourgeFlavor" value="<string name='Units/Drukhari/ScourgeFlavor'/>"/>
	<entry name="Drukhari/Succubus" value="<string name='Units/Drukhari/Succubus'/>"/>
	<entry name="Drukhari/SuccubusDescription" value="<string name='Units/Drukhari/SuccubusDescription'/>"/>
	<entry name="Drukhari/SuccubusFlavor" value="<string name='Units/Drukhari/SuccubusFlavor'/>"/>
	<entry name="Drukhari/Talos" value="<string name='Units/Drukhari/Talos'/>"/>
	<entry name="Drukhari/TalosDescription" value="<string name='Units/Drukhari/TalosDescription'/>"/>
	<entry name="Drukhari/TalosFlavor" value="<string name='Units/Drukhari/TalosFlavor'/>"/>
	<entry name="Drukhari/Tantalus" value="<string name='Units/Drukhari/Tantalus'/>"/>
	<entry name="Drukhari/TantalusDescription" value="<string name='Units/Drukhari/TantalusDescription'/>"/>
	<entry name="Drukhari/TantalusFlavor" value="<string name='Units/Drukhari/TantalusFlavor'/>"/>
	<entry name="Drukhari/VoidravenBomber" value="<string name='Units/Drukhari/VoidravenBomber'/>"/>
	<entry name="Drukhari/VoidravenBomberDescription" value="<string name='Units/Drukhari/VoidravenBomberDescription'/>"/>
	<entry name="Drukhari/VoidravenBomberFlavor" value="<string name='Units/Drukhari/VoidravenBomberFlavor'/>"/>
	<entry name="Drukhari/Wrack" value="<string name='Units/Drukhari/Wrack'/>"/>
	<entry name="Drukhari/WrackDescription" value="<string name='Units/Drukhari/WrackDescription'/>"/>
	<entry name="Drukhari/WrackFlavor" value="<string name='Units/Drukhari/WrackFlavor'/>"/>
	<entry name="Drukhari/Wyche" value="<string name='Units/Drukhari/Wyche'/>"/>
	<entry name="Drukhari/WycheDescription" value="<string name='Units/Drukhari/WycheDescription'/>"/>
	<entry name="Drukhari/WycheFlavor" value="<string name='Units/Drukhari/WycheFlavor'/>"/>
	<entry name="Eldar/AvatarOfKhaine" value="<string name='Units/Eldar/AvatarOfKhaine'/>"/>
	<entry name="Eldar/AvatarOfKhaineDescription" value="<string name='Units/Eldar/AvatarOfKhaineDescription'/>"/>
	<entry name="Eldar/AvatarOfKhaineFlavor" value="<string name='Units/Eldar/AvatarOfKhaineFlavor'/>"/>
	<entry name="Eldar/DarkReaper" value="<string name='Units/Eldar/DarkReaper'/>"/>
	<entry name="Eldar/DarkReaperDescription" value="<string name='Units/Eldar/DarkReaperDescription'/>"/>
	<entry name="Eldar/DarkReaperFlavor" value="<string name='Units/Eldar/DarkReaperFlavor'/>"/>
	<entry name="Eldar/FarseerSkyrunner" value="<string name='Units/Eldar/FarseerSkyrunner'/>"/>
	<entry name="Eldar/FarseerSkyrunnerDescription" value="<string name='Units/Eldar/FarseerSkyrunnerDescription'/>"/>
	<entry name="Eldar/FarseerSkyrunnerFlavor" value="<string name='Units/Eldar/FarseerSkyrunnerFlavor'/>"/>
	<entry name="Eldar/FireDragon" value="<string name='Units/Eldar/FireDragon'/>"/>
	<entry name="Eldar/FireDragonDescription" value="<string name='Units/Eldar/FireDragonDescription'/>"/>
	<entry name="Eldar/FireDragonFlavor" value="<string name='Units/Eldar/FireDragonFlavor'/>"/>
	<entry name="Eldar/FirePrism" value="<string name='Units/Eldar/FirePrism'/>"/>
	<entry name="Eldar/FirePrismDescription" value="<string name='Units/Eldar/FirePrismDescription'/>"/>
	<entry name="Eldar/FirePrismFlavor" value="<string name='Units/Eldar/FirePrismFlavor'/>"/>
	<entry name="Eldar/HemlockWraithfighter" value="<string name='Units/Eldar/HemlockWraithfighter'/>"/>
	<entry name="Eldar/HemlockWraithfighterDescription" value="<string name='Units/Eldar/HemlockWraithfighterDescription'/>"/>
	<entry name="Eldar/HemlockWraithfighterFlavor" value="<string name='Units/Eldar/HemlockWraithfighterFlavor'/>"/>
	<entry name="Eldar/Hornet" value="<string name='Units/Eldar/Hornet'/>"/>
	<entry name="Eldar/HornetDescription" value="<string name='Units/Eldar/HornetDescription'/>"/>
	<entry name="Eldar/HornetFlavor" value="<string name='Units/Eldar/HornetFlavor'/>"/>
	<entry name="Eldar/HowlingBanshee" value="<string name='Units/Eldar/HowlingBanshee'/>"/>
	<entry name="Eldar/HowlingBansheeDescription" value="<string name='Units/Eldar/HowlingBansheeDescription'/>"/>
	<entry name="Eldar/HowlingBansheeFlavor" value="<string name='Units/Eldar/HowlingBansheeFlavor'/>"/>
	<entry name="Eldar/Ranger" value="<string name='Units/Eldar/Ranger'/>"/>
	<entry name="Eldar/RangerDescription" value="<string name='Units/Eldar/RangerDescription'/>"/>
	<entry name="Eldar/RangerFlavor" value="<string name='Units/Eldar/RangerFlavor'/>"/>
	<entry name="Eldar/Scorpion" value="<string name='Units/Eldar/Scorpion'/>"/>
	<entry name="Eldar/ScorpionDescription" value="<string name='Units/Eldar/ScorpionDescription'/>"/>
	<entry name="Eldar/ScorpionFlavor" value="<string name='Units/Eldar/ScorpionFlavor'/>"/>
	<entry name="Eldar/Spiritseer" value="<string name='Units/Eldar/Spiritseer'/>"/>
	<entry name="Eldar/SpiritseerDescription" value="<string name='Units/Eldar/SpiritseerDescription'/>"/>
	<entry name="Eldar/SpiritseerFlavor" value="<string name='Units/Eldar/SpiritseerFlavor'/>"/>
	<entry name="Eldar/VaulsWrathSupportBattery" value="<string name='Units/Eldar/VaulsWrathSupportBattery'/>"/>
	<entry name="Eldar/VaulsWrathSupportBatteryDescription" value="<string name='Units/Eldar/VaulsWrathSupportBatteryDescription'/>"/>
	<entry name="Eldar/VaulsWrathSupportBatteryFlavor" value="<string name='Units/Eldar/VaulsWrathSupportBatteryFlavor'/>"/>
	<entry name="Eldar/Vyper" value="<string name='Units/Eldar/Vyper'/>"/>
	<entry name="Eldar/VyperDescription" value="<string name='Units/Eldar/VyperDescription'/>"/>
	<entry name="Eldar/VyperFlavor" value="<string name='Units/Eldar/VyperFlavor'/>"/>
	<entry name="Eldar/WarWalker" value="<string name='Units/Eldar/WarWalker'/>"/>
	<entry name="Eldar/WarWalkerDescription" value="<string name='Units/Eldar/WarWalkerDescription'/>"/>
	<entry name="Eldar/WarWalkerFlavor" value="<string name='Units/Eldar/WarWalkerFlavor'/>"/>
	<entry name="Eldar/Warlock" value="<string name='Units/Eldar/Warlock'/>"/>
	<entry name="Eldar/WarlockDescription" value="<string name='Units/Eldar/WarlockDescription'/>"/>
	<entry name="Eldar/WarlockFlavor" value="<string name='Units/Eldar/WarlockFlavor'/>"/>
	<entry name="Eldar/WaveSerpent" value="<string name='Units/Eldar/WaveSerpent'/>"/>
	<entry name="Eldar/WaveSerpentDescription" value="<string name='Units/Eldar/WaveSerpentDescription'/>"/>
	<entry name="Eldar/WaveSerpentFlavor" value="<string name='Units/Eldar/WaveSerpentFlavor'/>"/>
	<entry name="Eldar/Wraithblade" value="<string name='Units/Eldar/Wraithblade'/>"/>
	<entry name="Eldar/WraithbladeDescription" value="<string name='Units/Eldar/WraithbladeDescription'/>"/>
	<entry name="Eldar/WraithbladeFlavor" value="<string name='Units/Eldar/WraithbladeFlavor'/>"/>
	<entry name="Eldar/Wraithknight" value="<string name='Units/Eldar/Wraithknight'/>"/>
	<entry name="Eldar/WraithknightDescription" value="<string name='Units/Eldar/WraithknightDescription'/>"/>
	<entry name="Eldar/WraithknightFlavor" value="<string name='Units/Eldar/WraithknightFlavor'/>"/>
	<entry name="Eldar/Wraithlord" value="<string name='Units/Eldar/Wraithlord'/>"/>
	<entry name="Eldar/WraithlordDescription" value="<string name='Units/Eldar/WraithlordDescription'/>"/>
	<entry name="Eldar/WraithlordFlavor" value="<string name='Units/Eldar/WraithlordFlavor'/>"/>
	<entry name="Necrons/AnnihilationBarge" value="<string name='Units/Necrons/AnnihilationBarge'/>"/>
	<entry name="Necrons/AnnihilationBargeDescription" value="<string name='Units/Necrons/AnnihilationBargeDescription'/>"/>
	<entry name="Necrons/AnnihilationBargeFlavor" value="<string name='Units/Necrons/AnnihilationBargeFlavor'/>"/>
	<entry name="Necrons/CanoptekReanimator" value="<string name='Units/Necrons/CanoptekReanimator'/>"/>
	<entry name="Necrons/CanoptekReanimatorDescription" value="<string name='Units/Necrons/CanoptekReanimatorDescription'/>"/>
	<entry name="Necrons/CanoptekReanimatorFlavor" value="<string name='Units/Necrons/CanoptekReanimatorFlavor'/>"/>
	<entry name="Necrons/CanoptekSpyder" value="<string name='Units/Necrons/CanoptekSpyder'/>"/>
	<entry name="Necrons/CanoptekSpyderDescription" value="<string name='Units/Necrons/CanoptekSpyderDescription'/>"/>
	<entry name="Necrons/CanoptekSpyderFlavor" value="<string name='Units/Necrons/CanoptekSpyderFlavor'/>"/>
	<entry name="Necrons/CanoptekWraith" value="<string name='Units/Necrons/CanoptekWraith'/>"/>
	<entry name="Necrons/CanoptekWraithDescription" value="<string name='Units/Necrons/CanoptekWraithDescription'/>"/>
	<entry name="Necrons/CanoptekWraithFlavor" value="<string name='Units/Necrons/CanoptekWraithFlavor'/>"/>
	<entry name="Necrons/Cryptek" value="<string name='Units/Necrons/Cryptek'/>"/>
	<entry name="Necrons/CryptekDescription" value="<string name='Units/Necrons/CryptekDescription'/>"/>
	<entry name="Necrons/CryptekFlavor" value="<string name='Units/Necrons/CryptekFlavor'/>"/>
	<entry name="Necrons/Deathmark" value="<string name='Units/Necrons/Deathmark'/>"/>
	<entry name="Necrons/DeathmarkDescription" value="<string name='Units/Necrons/DeathmarkDescription'/>"/>
	<entry name="Necrons/DeathmarkFlavor" value="<string name='Units/Necrons/DeathmarkFlavor'/>"/>
	<entry name="Necrons/DestroyerLord" value="<string name='Units/Necrons/DestroyerLord'/>"/>
	<entry name="Necrons/DestroyerLordDescription" value="<string name='Units/Necrons/DestroyerLordDescription'/>"/>
	<entry name="Necrons/DestroyerLordFlavor" value="<string name='Units/Necrons/DestroyerLordFlavor'/>"/>
	<entry name="Necrons/DoomScythe" value="<string name='Units/Necrons/DoomScythe'/>"/>
	<entry name="Necrons/DoomScytheDescription" value="<string name='Units/Necrons/DoomScytheDescription'/>"/>
	<entry name="Necrons/DoomScytheFlavor" value="<string name='Units/Necrons/DoomScytheFlavor'/>"/>
	<entry name="Necrons/DoomsdayArk" value="<string name='Units/Necrons/DoomsdayArk'/>"/>
	<entry name="Necrons/DoomsdayArkDescription" value="<string name='Units/Necrons/DoomsdayArkDescription'/>"/>
	<entry name="Necrons/DoomsdayArkFlavor" value="<string name='Units/Necrons/DoomsdayArkFlavor'/>"/>
	<entry name="Necrons/FlayedOne" value="<string name='Units/Necrons/FlayedOne'/>"/>
	<entry name="Necrons/FlayedOneDescription" value="<string name='Units/Necrons/FlayedOneDescription'/>"/>
	<entry name="Necrons/FlayedOneFlavor" value="<string name='Units/Necrons/FlayedOneFlavor'/>"/>
	<entry name="Necrons/GhostArk" value="<string name='Units/Necrons/GhostArk'/>"/>
	<entry name="Necrons/GhostArkDescription" value="<string name='Units/Necrons/GhostArkDescription'/>"/>
	<entry name="Necrons/GhostArkFlavor" value="<string name='Units/Necrons/GhostArkFlavor'/>"/>
	<entry name="Necrons/HeavyDestroyer" value="<string name='Units/Necrons/HeavyDestroyer'/>"/>
	<entry name="Necrons/HeavyDestroyerDescription" value="<string name='Units/Necrons/HeavyDestroyerDescription'/>"/>
	<entry name="Necrons/HeavyDestroyerFlavor" value="<string name='Units/Necrons/HeavyDestroyerFlavor'/>"/>
	<entry name="Necrons/Immortal" value="<string name='Units/Necrons/Immortal'/>"/>
	<entry name="Necrons/ImmortalDescription" value="<string name='Units/Necrons/ImmortalDescription'/>"/>
	<entry name="Necrons/ImmortalFlavor" value="<string name='Units/Necrons/ImmortalFlavor'/>"/>
	<entry name="Necrons/Lord" value="<string name='Units/Necrons/Lord'/>"/>
	<entry name="Necrons/LordDescription" value="<string name='Units/Necrons/LordDescription'/>"/>
	<entry name="Necrons/LordFlavor" value="<string name='Units/Necrons/LordFlavor'/>"/>
	<entry name="Necrons/Monolith" value="<string name='Units/Necrons/Monolith'/>"/>
	<entry name="Necrons/MonolithDescription" value="<string name='Units/Necrons/MonolithDescription'/>"/>
	<entry name="Necrons/MonolithFlavor" value="<string name='Units/Necrons/MonolithFlavor'/>"/>
	<entry name="Necrons/NightScythe" value="<string name='Units/Necrons/NightScythe'/>"/>
	<entry name="Necrons/NightScytheDescription" value="<string name='Units/Necrons/NightScytheDescription'/>"/>
	<entry name="Necrons/NightScytheFlavor" value="<string name='Units/Necrons/NightScytheFlavor'/>"/>
	<entry name="Necrons/Obelisk" value="<string name='Units/Necrons/Obelisk'/>"/>
	<entry name="Necrons/ObeliskDescription" value="<string name='Units/Necrons/ObeliskDescription'/>"/>
	<entry name="Necrons/ObeliskFlavor" value="<string name='Units/Necrons/ObeliskFlavor'/>"/>
	<entry name="Necrons/SkorpekhDestroyer" value="<string name='Units/Necrons/SkorpekhDestroyer'/>"/>
	<entry name="Necrons/SkorpekhDestroyerDescription" value="<string name='Units/Necrons/SkorpekhDestroyerDescription'/>"/>
	<entry name="Necrons/SkorpekhDestroyerFlavor" value="<string name='Units/Necrons/SkorpekhDestroyerFlavor'/>"/>
	<entry name="Necrons/TombBlade" value="<string name='Units/Necrons/TombBlade'/>"/>
	<entry name="Necrons/TombBladeDescription" value="<string name='Units/Necrons/TombBladeDescription'/>"/>
	<entry name="Necrons/TombBladeFlavor" value="<string name='Units/Necrons/TombBladeFlavor'/>"/>
	<entry name="Necrons/TranscendentCtan" value="<string name='Units/Necrons/TranscendentCtan'/>"/>
	<entry name="Necrons/TranscendentCtanDescription" value="<string name='Units/Necrons/TranscendentCtanDescription'/>"/>
	<entry name="Necrons/TranscendentCtanFlavor" value="<string name='Units/Necrons/TranscendentCtanFlavor'/>"/>
	<entry name="Necrons/TriarchPraetorian" value="<string name='Units/Necrons/TriarchPraetorian'/>"/>
	<entry name="Necrons/TriarchPraetorianDescription" value="<string name='Units/Necrons/TriarchPraetorianDescription'/>"/>
	<entry name="Necrons/TriarchPraetorianFlavor" value="<string name='Units/Necrons/TriarchPraetorianFlavor'/>"/>
	<entry name="Necrons/TriarchStalker" value="<string name='Units/Necrons/TriarchStalker'/>"/>
	<entry name="Necrons/TriarchStalkerDescription" value="<string name='Units/Necrons/TriarchStalkerDescription'/>"/>
	<entry name="Necrons/TriarchStalkerFlavor" value="<string name='Units/Necrons/TriarchStalkerFlavor'/>"/>
	<entry name="Orks/Battlewagon" value="<string name='Units/Orks/Battlewagon'/>"/>
	<entry name="Orks/BattlewagonDescription" value="<string name='Units/Orks/BattlewagonDescription'/>"/>
	<entry name="Orks/BattlewagonFlavor" value="<string name='Units/Orks/BattlewagonFlavor'/>"/>
	<entry name="Orks/BigMek" value="<string name='Units/Orks/BigMek'/>"/>
	<entry name="Orks/BigMekDescription" value="<string name='Units/Orks/BigMekDescription'/>"/>
	<entry name="Orks/BigMekFlavor" value="<string name='Units/Orks/BigMekFlavor'/>"/>
	<entry name="Orks/BurnaBommer" value="<string name='Units/Orks/BurnaBommer'/>"/>
	<entry name="Orks/BurnaBommerDescription" value="<string name='Units/Orks/BurnaBommerDescription'/>"/>
	<entry name="Orks/BurnaBommerFlavor" value="<string name='Units/Orks/BurnaBommerFlavor'/>"/>
	<entry name="Orks/BurnaBoy" value="<string name='Units/Orks/BurnaBoy'/>"/>
	<entry name="Orks/BurnaBoyDescription" value="<string name='Units/Orks/BurnaBoyDescription'/>"/>
	<entry name="Orks/BurnaBoyFlavor" value="<string name='Units/Orks/BurnaBoyFlavor'/>"/>
	<entry name="Orks/Dakkajet" value="<string name='Units/Orks/Dakkajet'/>"/>
	<entry name="Orks/DakkajetDescription" value="<string name='Units/Orks/DakkajetDescription'/>"/>
	<entry name="Orks/DakkajetFlavor" value="<string name='Units/Orks/DakkajetFlavor'/>"/>
	<entry name="Orks/DeffDread" value="<string name='Units/Orks/DeffDread'/>"/>
	<entry name="Orks/DeffDreadDescription" value="<string name='Units/Orks/DeffDreadDescription'/>"/>
	<entry name="Orks/DeffDreadFlavor" value="<string name='Units/Orks/DeffDreadFlavor'/>"/>
	<entry name="Orks/Deffkopta" value="<string name='Units/Orks/Deffkopta'/>"/>
	<entry name="Orks/DeffkoptaDescription" value="<string name='Units/Orks/DeffkoptaDescription'/>"/>
	<entry name="Orks/DeffkoptaFlavor" value="<string name='Units/Orks/DeffkoptaFlavor'/>"/>
	<entry name="Orks/FlashGitz" value="<string name='Units/Orks/FlashGitz'/>"/>
	<entry name="Orks/FlashGitzDescription" value="<string name='Units/Orks/FlashGitzDescription'/>"/>
	<entry name="Orks/FlashGitzFlavor" value="<string name='Units/Orks/FlashGitzFlavor'/>"/>
	<entry name="Orks/GargantuanSquiggoth" value="<string name='Units/Orks/GargantuanSquiggoth'/>"/>
	<entry name="Orks/GargantuanSquiggothDescription" value="<string name='Units/Orks/GargantuanSquiggothDescription'/>"/>
	<entry name="Orks/GargantuanSquiggothFlavor" value="<string name='Units/Orks/GargantuanSquiggothFlavor'/>"/>
	<entry name="Orks/Gorkanaut" value="<string name='Units/Orks/Gorkanaut'/>"/>
	<entry name="Orks/GorkanautDescription" value="<string name='Units/Orks/GorkanautDescription'/>"/>
	<entry name="Orks/GorkanautFlavor" value="<string name='Units/Orks/GorkanautFlavor'/>"/>
	<entry name="Orks/KillBursta" value="<string name='Units/Orks/KillBursta'/>"/>
	<entry name="Orks/KillBurstaDescription" value="<string name='Units/Orks/KillBurstaDescription'/>"/>
	<entry name="Orks/KillBurstaFlavor" value="<string name='Units/Orks/KillBurstaFlavor'/>"/>
	<entry name="Orks/KillaKan" value="<string name='Units/Orks/KillaKan'/>"/>
	<entry name="Orks/KillaKanDescription" value="<string name='Units/Orks/KillaKanDescription'/>"/>
	<entry name="Orks/KillaKanFlavor" value="<string name='Units/Orks/KillaKanFlavor'/>"/>
	<entry name="Orks/Meganob" value="<string name='Units/Orks/Meganob'/>"/>
	<entry name="Orks/MeganobDescription" value="<string name='Units/Orks/MeganobDescription'/>"/>
	<entry name="Orks/MeganobFlavor" value="<string name='Units/Orks/MeganobFlavor'/>"/>
	<entry name="Orks/MegatrakkScrapjet" value="<string name='Units/Orks/MegatrakkScrapjet'/>"/>
	<entry name="Orks/MegatrakkScrapjetDescription" value="<string name='Units/Orks/MegatrakkScrapjetDescription'/>"/>
	<entry name="Orks/MegatrakkScrapjetFlavor" value="<string name='Units/Orks/MegatrakkScrapjetFlavor'/>"/>
	<entry name="Orks/Mek" value="<string name='Units/Orks/Mek'/>"/>
	<entry name="Orks/MekDescription" value="<string name='Units/Orks/MekDescription'/>"/>
	<entry name="Orks/MekFlavor" value="<string name='Units/Orks/MekFlavor'/>"/>
	<entry name="Orks/MekGun" value="<string name='Units/Orks/MekGun'/>"/>
	<entry name="Orks/MekGunDescription" value="<string name='Units/Orks/MekGunDescription'/>"/>
	<entry name="Orks/MekGunFlavor" value="<string name='Units/Orks/MekGunFlavor'/>"/>
	<entry name="Orks/Painboy" value="<string name='Units/Orks/Painboy'/>"/>
	<entry name="Orks/PainboyDescription" value="<string name='Units/Orks/PainboyDescription'/>"/>
	<entry name="Orks/PainboyFlavor" value="<string name='Units/Orks/PainboyFlavor'/>"/>
	<entry name="Orks/SquighogBoy" value="<string name='Units/Orks/SquighogBoy'/>"/>
	<entry name="Orks/SquighogBoyDescription" value="<string name='Units/Orks/SquighogBoyDescription'/>"/>
	<entry name="Orks/SquighogBoyFlavor" value="<string name='Units/Orks/SquighogBoyFlavor'/>"/>
	<entry name="Orks/Tankbusta" value="<string name='Units/Orks/Tankbusta'/>"/>
	<entry name="Orks/TankbustaDescription" value="<string name='Units/Orks/TankbustaDescription'/>"/>
	<entry name="Orks/TankbustaFlavor" value="<string name='Units/Orks/TankbustaFlavor'/>"/>
	<entry name="Orks/Warbiker" value="<string name='Units/Orks/Warbiker'/>"/>
	<entry name="Orks/WarbikerDescription" value="<string name='Units/Orks/WarbikerDescription'/>"/>
	<entry name="Orks/WarbikerFlavor" value="<string name='Units/Orks/WarbikerFlavor'/>"/>
	<entry name="Orks/Warboss" value="<string name='Units/Orks/Warboss'/>"/>
	<entry name="Orks/WarbossDescription" value="<string name='Units/Orks/WarbossDescription'/>"/>
	<entry name="Orks/WarbossFlavor" value="<string name='Units/Orks/WarbossFlavor'/>"/>
	<entry name="Orks/Warbuggy" value="<string name='Units/Orks/Warbuggy'/>"/>
	<entry name="Orks/WarbuggyDescription" value="<string name='Units/Orks/WarbuggyDescription'/>"/>
	<entry name="Orks/WarbuggyFlavor" value="<string name='Units/Orks/WarbuggyFlavor'/>"/>
	<entry name="Orks/Weirdboy" value="<string name='Units/Orks/Weirdboy'/>"/>
	<entry name="Orks/WeirdboyDescription" value="<string name='Units/Orks/WeirdboyDescription'/>"/>
	<entry name="Orks/WeirdboyFlavor" value="<string name='Units/Orks/WeirdboyFlavor'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellant" value="<string name='Units/SistersOfBattle/ArcoFlagellant'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellantDescription" value="<string name='Units/SistersOfBattle/ArcoFlagellantDescription'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellantFlavor" value="<string name='Units/SistersOfBattle/ArcoFlagellantFlavor'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter" value="<string name='Units/SistersOfBattle/AvengerStrikeFighter'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterDescription" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterDescription'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterFlavor" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterFlavor'/>"/>
	<entry name="SistersOfBattle/Castigator" value="<string name='Units/SistersOfBattle/Castigator'/>"/>
	<entry name="SistersOfBattle/CastigatorDescription" value="<string name='Units/SistersOfBattle/CastigatorDescription'/>"/>
	<entry name="SistersOfBattle/CastigatorFlavor" value="<string name='Units/SistersOfBattle/CastigatorFlavor'/>"/>
	<entry name="SistersOfBattle/CelestianSacresant" value="<string name='Units/SistersOfBattle/CelestianSacresant'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantDescription" value="<string name='Units/SistersOfBattle/CelestianSacresantDescription'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantFlavor" value="<string name='Units/SistersOfBattle/CelestianSacresantFlavor'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancer" value="<string name='Units/SistersOfBattle/CerastusKnightLancer'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerDescription" value="<string name='Units/SistersOfBattle/CerastusKnightLancerDescription'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerFlavor" value="<string name='Units/SistersOfBattle/CerastusKnightLancerFlavor'/>"/>
	<entry name="SistersOfBattle/Dialogus" value="<string name='Units/SistersOfBattle/Dialogus'/>"/>
	<entry name="SistersOfBattle/DialogusDescription" value="<string name='Units/SistersOfBattle/DialogusDescription'/>"/>
	<entry name="SistersOfBattle/DialogusFlavor" value="<string name='Units/SistersOfBattle/DialogusFlavor'/>"/>
	<entry name="SistersOfBattle/Dominion" value="<string name='Units/SistersOfBattle/Dominion'/>"/>
	<entry name="SistersOfBattle/DominionDescription" value="<string name='Units/SistersOfBattle/DominionDescription'/>"/>
	<entry name="SistersOfBattle/DominionFlavor" value="<string name='Units/SistersOfBattle/DominionFlavor'/>"/>
	<entry name="SistersOfBattle/Exorcist" value="<string name='Units/SistersOfBattle/Exorcist'/>"/>
	<entry name="SistersOfBattle/ExorcistDescription" value="<string name='Units/SistersOfBattle/ExorcistDescription'/>"/>
	<entry name="SistersOfBattle/ExorcistFlavor" value="<string name='Units/SistersOfBattle/ExorcistFlavor'/>"/>
	<entry name="SistersOfBattle/Headquarters" value="<string name='Buildings/SistersOfBattle/Headquarters'/>"/>
	<entry name="SistersOfBattle/HeadquartersDescription" value="<string name='Units/SistersOfBattle/HeadquartersDescription'/>"/>
	<entry name="SistersOfBattle/HeadquartersFlavor" value="<string name='Buildings/SistersOfBattle/HeadquartersFlavor'/>"/>
	<entry name="SistersOfBattle/Hospitaller" value="<string name='Units/SistersOfBattle/Hospitaller'/>"/>
	<entry name="SistersOfBattle/HospitallerDescription" value="<string name='Units/SistersOfBattle/HospitallerDescription'/>"/>
	<entry name="SistersOfBattle/HospitallerFlavor" value="<string name='Units/SistersOfBattle/HospitallerFlavor'/>"/>
	<entry name="SistersOfBattle/Imagifier" value="<string name='Units/SistersOfBattle/Imagifier'/>"/>
	<entry name="SistersOfBattle/ImagifierDescription" value="<string name='Units/SistersOfBattle/ImagifierDescription'/>"/>
	<entry name="SistersOfBattle/ImagifierFlavor" value="<string name='Units/SistersOfBattle/ImagifierFlavor'/>"/>
	<entry name="SistersOfBattle/Mortifier" value="<string name='Units/SistersOfBattle/Mortifier'/>"/>
	<entry name="SistersOfBattle/MortifierDescription" value="<string name='Units/SistersOfBattle/MortifierDescription'/>"/>
	<entry name="SistersOfBattle/MortifierFlavor" value="<string name='Units/SistersOfBattle/MortifierFlavor'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="<string name='Units/SistersOfBattle/ParagonWarsuit'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="<string name='Units/SistersOfBattle/ParagonWarsuitDescription'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="<string name='Units/SistersOfBattle/ParagonWarsuitFlavor'/>"/>
	<entry name="SistersOfBattle/Retributor" value="<string name='Units/SistersOfBattle/Retributor'/>"/>
	<entry name="SistersOfBattle/RetributorDescription" value="<string name='Units/SistersOfBattle/RetributorDescription'/>"/>
	<entry name="SistersOfBattle/RetributorFlavor" value="<string name='Units/SistersOfBattle/RetributorFlavor'/>"/>
	<entry name="SistersOfBattle/SaintCelestine" value="<string name='Units/SistersOfBattle/SaintCelestine'/>"/>
	<entry name="SistersOfBattle/SaintCelestineDescription" value="<string name='Units/SistersOfBattle/SaintCelestineDescription'/>"/>
	<entry name="SistersOfBattle/SaintCelestineFlavor" value="<string name='Units/SistersOfBattle/SaintCelestineFlavor'/>"/>
	<entry name="SistersOfBattle/SisterRepentia" value="<string name='Units/SistersOfBattle/SisterRepentia'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaDescription" value="<string name='Units/SistersOfBattle/SisterRepentiaDescription'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaFlavor" value="<string name='Units/SistersOfBattle/SisterRepentiaFlavor'/>"/>
	<entry name="SistersOfBattle/Zephyrim" value="<string name='Units/SistersOfBattle/Zephyrim'/>"/>
	<entry name="SistersOfBattle/ZephyrimDescription" value="<string name='Units/SistersOfBattle/ZephyrimDescription'/>"/>
	<entry name="SistersOfBattle/ZephyrimFlavor" value="<string name='Units/SistersOfBattle/ZephyrimFlavor'/>"/>
	<entry name="SpaceMarines/Apothecary" value="<string name='Units/SpaceMarines/Apothecary'/>"/>
	<entry name="SpaceMarines/ApothecaryDescription" value="<string name='Units/SpaceMarines/ApothecaryDescription'/>"/>
	<entry name="SpaceMarines/ApothecaryFlavor" value="<string name='Units/SpaceMarines/ApothecaryFlavor'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannon" value="<string name='Units/SpaceMarines/AquilaMacroCannon'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonDescription" value="<string name='Units/SpaceMarines/AquilaMacroCannonDescription'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonFlavor" value="<string name='Units/SpaceMarines/AquilaMacroCannonFlavor'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="<string name='Units/SpaceMarines/AssaultSpaceMarine'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineDescription" value="<string name='Units/SpaceMarines/AssaultSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineFlavor" value="<string name='Units/SpaceMarines/AssaultSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/AssaultTerminator" value="<string name='Units/SpaceMarines/AssaultTerminator'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorDescription" value="<string name='Units/SpaceMarines/AssaultTerminatorDescription'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorFlavor" value="<string name='Units/SpaceMarines/AssaultTerminatorFlavor'/>"/>
	<entry name="SpaceMarines/Captain" value="<string name='Units/SpaceMarines/Captain'/>"/>
	<entry name="SpaceMarines/CaptainDescription" value="<string name='Units/SpaceMarines/CaptainDescription'/>"/>
	<entry name="SpaceMarines/CaptainFlavor" value="<string name='Units/SpaceMarines/CaptainFlavor'/>"/>
	<entry name="SpaceMarines/Chaplain" value="<string name='Units/SpaceMarines/Chaplain'/>"/>
	<entry name="SpaceMarines/ChaplainDescription" value="<string name='Units/SpaceMarines/ChaplainDescription'/>"/>
	<entry name="SpaceMarines/ChaplainFlavor" value="<string name='Units/SpaceMarines/ChaplainFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorCenturion" value="<string name='Units/SpaceMarines/DevastatorCenturion'/>"/>
	<entry name="SpaceMarines/DevastatorCenturionDescription" value="<string name='Units/SpaceMarines/DevastatorCenturionDescription'/>"/>
	<entry name="SpaceMarines/DevastatorCenturionFlavor" value="<string name='Units/SpaceMarines/DevastatorCenturionFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="<string name='Units/SpaceMarines/DevastatorSpaceMarine'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineDescription" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineFlavor" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/Dreadnought" value="<string name='Units/SpaceMarines/Dreadnought'/>"/>
	<entry name="SpaceMarines/DreadnoughtDescription" value="<string name='Units/SpaceMarines/DreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/DreadnoughtFlavor" value="<string name='Units/SpaceMarines/DreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Hunter" value="<string name='Units/SpaceMarines/Hunter'/>"/>
	<entry name="SpaceMarines/HunterDescription" value="<string name='Units/SpaceMarines/HunterDescription'/>"/>
	<entry name="SpaceMarines/HunterFlavor" value="<string name='Units/SpaceMarines/HunterFlavor'/>"/>
	<entry name="SpaceMarines/LandRaider" value="<string name='Units/SpaceMarines/LandRaider'/>"/>
	<entry name="SpaceMarines/LandRaiderDescription" value="<string name='Units/SpaceMarines/LandRaiderDescription'/>"/>
	<entry name="SpaceMarines/LandRaiderFlavor" value="<string name='Units/SpaceMarines/LandRaiderFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeeder" value="<string name='Units/SpaceMarines/LandSpeeder'/>"/>
	<entry name="SpaceMarines/LandSpeederDescription" value="<string name='Units/SpaceMarines/LandSpeederDescription'/>"/>
	<entry name="SpaceMarines/LandSpeederFlavor" value="<string name='Units/SpaceMarines/LandSpeederFlavor'/>"/>
	<entry name="SpaceMarines/Librarian" value="<string name='Units/SpaceMarines/Librarian'/>"/>
	<entry name="SpaceMarines/LibrarianDescription" value="<string name='Units/SpaceMarines/LibrarianDescription'/>"/>
	<entry name="SpaceMarines/LibrarianFlavor" value="<string name='Units/SpaceMarines/LibrarianFlavor'/>"/>	
	<entry name="SpaceMarines/Predator" value="<string name='Units/SpaceMarines/Predator'/>"/>
	<entry name="SpaceMarines/PredatorDescription" value="<string name='Units/SpaceMarines/PredatorDescription'/>"/>
	<entry name="SpaceMarines/PredatorFlavor" value="<string name='Units/SpaceMarines/PredatorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressor" value="<string name='Units/SpaceMarines/PrimarisAggressor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressorDescription" value="<string name='Units/SpaceMarines/PrimarisAggressorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisAggressorFlavor" value="<string name='Units/SpaceMarines/PrimarisAggressorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisHellblaster" value="<string name='Units/SpaceMarines/PrimarisHellblaster'/>"/>
	<entry name="SpaceMarines/PrimarisHellblasterDescription" value="<string name='Units/SpaceMarines/PrimarisHellblasterDescription'/>"/>
	<entry name="SpaceMarines/PrimarisHellblasterFlavor" value="<string name='Units/SpaceMarines/PrimarisHellblasterFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisInceptor" value="<string name='Units/SpaceMarines/PrimarisInceptor'/>"/>
	<entry name="SpaceMarines/PrimarisInceptorDescription" value="<string name='Units/SpaceMarines/PrimarisInceptorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisInceptorFlavor" value="<string name='Units/SpaceMarines/PrimarisInceptorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessor" value="<string name='Units/SpaceMarines/PrimarisIntercessor'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessorDescription" value="<string name='Units/SpaceMarines/PrimarisIntercessorDescription'/>"/>
	<entry name="SpaceMarines/PrimarisIntercessorFlavor" value="<string name='Units/SpaceMarines/PrimarisIntercessorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATV" value="<string name='Units/SpaceMarines/PrimarisInvaderATV'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATVDescription" value="<string name='Units/SpaceMarines/PrimarisInvaderATVDescription'/>"/>
	<entry name="SpaceMarines/PrimarisInvaderATVFlavor" value="<string name='Units/SpaceMarines/PrimarisInvaderATVFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutioner" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutioner'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerDescription" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerDescription'/>"/>
	<entry name="SpaceMarines/PrimarisRepulsorExecutionerFlavor" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerFlavor'/>"/>
	<entry name="SpaceMarines/Razorback" value="<string name='Units/SpaceMarines/Razorback'/>"/>
	<entry name="SpaceMarines/RazorbackDescription" value="<string name='Units/SpaceMarines/RazorbackDescription'/>"/>
	<entry name="SpaceMarines/RazorbackFlavor" value="<string name='Units/SpaceMarines/RazorbackFlavor'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnought" value="<string name='Units/SpaceMarines/RedemptorDreadnought'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtDescription" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtFlavor" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Scout" value="<string name='Units/SpaceMarines/Scout'/>"/>
	<entry name="SpaceMarines/ScoutDescription" value="<string name='Units/SpaceMarines/ScoutDescription'/>"/>
	<entry name="SpaceMarines/ScoutFlavor" value="<string name='Units/SpaceMarines/ScoutFlavor'/>"/>
	<entry name="SpaceMarines/ScoutBiker" value="<string name='Units/SpaceMarines/ScoutBiker'/>"/>
	<entry name="SpaceMarines/ScoutBikerDescription" value="<string name='Units/SpaceMarines/ScoutBikerDescription'/>"/>
	<entry name="SpaceMarines/ScoutBikerFlavor" value="<string name='Units/SpaceMarines/ScoutBikerFlavor'/>"/>
	<entry name="SpaceMarines/StormravenGunship" value="<string name='Units/SpaceMarines/StormravenGunship'/>"/>
	<entry name="SpaceMarines/StormravenGunshipDescription" value="<string name='Units/SpaceMarines/StormravenGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormravenGunshipFlavor" value="<string name='Units/SpaceMarines/StormravenGunshipFlavor'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike" value="<string name='Units/SpaceMarines/StormSpeederThunderstrike'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeDescription" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeDescription'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrikeFlavor" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeFlavor'/>"/>
	<entry name="SpaceMarines/StormtalonGunship" value="<string name='Units/SpaceMarines/StormtalonGunship'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipDescription" value="<string name='Units/SpaceMarines/StormtalonGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipFlavor" value="<string name='Units/SpaceMarines/StormtalonGunshipFlavor'/>"/>
	<entry name="SpaceMarines/Terminator" value="<string name='Units/SpaceMarines/Terminator'/>"/>
	<entry name="SpaceMarines/TerminatorDescription" value="<string name='Units/SpaceMarines/TerminatorDescription'/>"/>
	<entry name="SpaceMarines/TerminatorFlavor" value="<string name='Units/SpaceMarines/TerminatorFlavor'/>"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="<string name='Units/SpaceMarines/ThunderfireCannon'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonDescription" value="<string name='Units/SpaceMarines/ThunderfireCannonDescription'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonFlavor" value="<string name='Units/SpaceMarines/ThunderfireCannonFlavor'/>"/>
	<entry name="SpaceMarines/Vindicator" value="<string name='Units/SpaceMarines/Vindicator'/>"/>
	<entry name="SpaceMarines/VindicatorDescription" value="<string name='Units/SpaceMarines/VindicatorDescription'/>"/>
	<entry name="SpaceMarines/VindicatorFlavor" value="<string name='Units/SpaceMarines/VindicatorFlavor'/>"/>
	<entry name="SpaceMarines/Whirlwind" value="<string name='Units/SpaceMarines/Whirlwind'/>"/>
	<entry name="SpaceMarines/WhirlwindDescription" value="<string name='Units/SpaceMarines/WhirlwindDescription'/>"/>
	<entry name="SpaceMarines/WhirlwindFlavor" value="<string name='Units/SpaceMarines/WhirlwindFlavor'/>"/>
	<entry name="Tau/BroadsideBattlesuit" value="<string name='Units/Tau/BroadsideBattlesuit'/>"/>
	<entry name="Tau/BroadsideBattlesuitDescription" value="<string name='Units/Tau/BroadsideBattlesuitDescription'/>"/>
	<entry name="Tau/BroadsideBattlesuitFlavor" value="<string name='Units/Tau/BroadsideBattlesuitFlavor'/>"/>
	<entry name="Tau/BuilderDrone" value="<string name='Units/Tau/BuilderDrone'/>"/>
	<entry name="Tau/BuilderDroneDescription" value="<string name='Units/Tau/BuilderDroneDescription'/>"/>
	<entry name="Tau/BuilderDroneFlavor" value="<string name='Units/Tau/BuilderDroneFlavor'/>"/>
	<entry name="Tau/Commander" value="<string name='Units/Tau/Commander'/>"/>
	<entry name="Tau/CommanderDescription" value="<string name='Units/Tau/CommanderDescription'/>"/>
	<entry name="Tau/CommanderFlavor" value="<string name='Units/Tau/CommanderFlavor'/>"/>
	<entry name="Tau/CrisisBattlesuit" value="<string name='Units/Tau/CrisisBattlesuit'/>"/>
	<entry name="Tau/CrisisBattlesuitDescription" value="<string name='Units/Tau/CrisisBattlesuitDescription'/>"/>
	<entry name="Tau/CrisisBattlesuitFlavor" value="<string name='Units/Tau/CrisisBattlesuitFlavor'/>"/>
	<entry name="Tau/Devilfish" value="<string name='Units/Tau/Devilfish'/>"/>
	<entry name="Tau/DevilfishDescription" value="<string name='Units/Tau/DevilfishDescription'/>"/>
	<entry name="Tau/DevilfishFlavor" value="<string name='Units/Tau/DevilfishFlavor'/>"/>
	<entry name="Tau/Ethereal" value="<string name='Units/Tau/Ethereal'/>"/>
	<entry name="Tau/EtherealDescription" value="<string name='Units/Tau/EtherealDescription'/>"/>
	<entry name="Tau/EtherealFlavor" value="<string name='Units/Tau/EtherealFlavor'/>"/>
	<entry name="Tau/FireWarriorBreacher" value="<string name='Units/Tau/FireWarriorBreacher'/>"/>
	<entry name="Tau/FireWarriorBreacherDescription" value="<string name='Units/Tau/FireWarriorBreacherDescription'/>"/>
	<entry name="Tau/FireWarriorBreacherFlavor" value="<string name='Units/Tau/FireWarriorBreacherFlavor'/>"/>
	<entry name="Tau/GunDrone" value="<string name='Units/Tau/GunDrone'/>"/>
	<entry name="Tau/GunDroneDescription" value="使火族战士, 火族战士突破者, XV8危机战斗装甲, XV25隐身战斗装甲, XV88炮击战斗装甲, 火刃队长, 乙太和指挥官获得临时部署基础战斗无人机的能力."/>
	<entry name="Tau/GunDroneFlavor" value="<string name='Units/Tau/GunDroneFlavor'/>"/>
	<entry name="Tau/HammerheadGunship" value="<string name='Units/Tau/HammerheadGunship'/>"/>
	<entry name="Tau/HammerheadGunshipDescription" value="<string name='Units/Tau/HammerheadGunshipDescription'/>"/>
	<entry name="Tau/HammerheadGunshipFlavor" value="<string name='Units/Tau/HammerheadGunshipFlavor'/>"/>
	<entry name="Tau/KrootoxRider" value="<string name='Units/Tau/KrootoxRider'/>"/>
	<entry name="Tau/KrootoxRiderDescription" value="<string name='Units/Tau/KrootoxRiderDescription'/>"/>
	<entry name="Tau/KrootoxRiderFlavor" value="<string name='Units/Tau/KrootoxRiderFlavor'/>"/>
	<entry name="Tau/MarkerDrone" value="<string name='Units/Tau/MarkerDrone'/>"/>
	<entry name="Tau/MarkerDroneDescription" value="使火族战士, 火族战士突破者, XV8危机战斗装甲, XV25隐身战斗装甲, XV88炮击战斗装甲, 火刃队长, 乙太和指挥官临时部署能够提高敌人伤害的无人机."/>
	<entry name="Tau/MarkerDroneFlavor" value="<string name='Units/Tau/MarkerDroneFlavor'/>"/>
	<entry name="Tau/Pathfinder" value="<string name='Units/Tau/Pathfinder'/>"/>
	<entry name="Tau/PathfinderDescription" value="<string name='Units/Tau/PathfinderDescription'/>"/>
	<entry name="Tau/PathfinderFlavor" value="<string name='Units/Tau/PathfinderFlavor'/>"/>
	<entry name="Tau/RiptideBattlesuit" value="<string name='Units/Tau/RiptideBattlesuit'/>"/>
	<entry name="Tau/RiptideBattlesuitDescription" value="<string name='Units/Tau/RiptideBattlesuitDescription'/>"/>
	<entry name="Tau/RiptideBattlesuitFlavor" value="<string name='Units/Tau/RiptideBattlesuitFlavor'/>"/>
	<entry name="Tau/RVarnaBattlesuit" value="<string name='Units/Tau/RVarnaBattlesuit'/>"/>
	<entry name="Tau/RVarnaBattlesuitDescription" value="<string name='Units/Tau/RVarnaBattlesuitDescription'/>"/>
	<entry name="Tau/RVarnaBattlesuitFlavor" value="<string name='Units/Tau/RVarnaBattlesuitFlavor'/>"/>
	<entry name="Tau/ShieldDrone" value="<string name='Units/Tau/ShieldDrone'/>"/>
	<entry name="Tau/ShieldDroneDescription" value="使火族战士, 火族战士突破者, XV8危机战斗装甲, XV25隐身战斗装甲, XV88炮击战斗装甲, 火刃队长, 乙太和指挥官临时获得部署护盾无人机的能力."/>
	<entry name="Tau/ShieldDroneFlavor" value="<string name='Units/Tau/ShieldDroneFlavor'/>"/>
	<entry name="Tau/SkyRayGunship" value="<string name='Units/Tau/SkyRayGunship'/>"/>
	<entry name="Tau/SkyRayGunshipDescription" value="<string name='Units/Tau/SkyRayGunshipDescription'/>"/>
	<entry name="Tau/SkyRayGunshipFlavor" value="<string name='Units/Tau/SkyRayGunshipFlavor'/>"/>
	<entry name="Tau/StealthBattlesuit" value="<string name='Units/Tau/StealthBattlesuit'/>"/>
	<entry name="Tau/StealthBattlesuitDescription" value="<string name='Units/Tau/StealthBattlesuitDescription'/>"/>
	<entry name="Tau/StealthBattlesuitFlavor" value="<string name='Units/Tau/StealthBattlesuitFlavor'/>"/>
	<entry name="Tau/Stormsurge" value="<string name='Units/Tau/Stormsurge'/>"/>
	<entry name="Tau/StormsurgeDescription" value="<string name='Units/Tau/StormsurgeDescription'/>"/>
	<entry name="Tau/StormsurgeFlavor" value="<string name='Units/Tau/StormsurgeFlavor'/>"/>
	<entry name="Tau/SunSharkBomber" value="<string name='Units/Tau/SunSharkBomber'/>"/>
	<entry name="Tau/SunSharkBomberDescription" value="<string name='Units/Tau/SunSharkBomberDescription'/>"/>
	<entry name="Tau/SunSharkBomberFlavor" value="<string name='Units/Tau/SunSharkBomberFlavor'/>"/>
	<entry name="Tau/TidewallGunrig" value="<string name='Units/Tau/TidewallGunrig'/>"/>
	<entry name="Tau/TidewallGunrigDescription" value="使建造者无人机建造重型装甲堡垒的能力. 该堡垒能够通过运输单位进行移动."/>
	<entry name="Tau/TidewallGunrigFlavor" value="<string name='Units/Tau/TidewallGunrigFlavor'/>"/>
	<entry name="Tau/TigerShark" value="<string name='Units/Tau/TigerShark'/>"/>
	<entry name="Tau/TigerSharkDescription" value="<string name='Units/Tau/TigerSharkDescription'/>"/>
	<entry name="Tau/TigerSharkFlavor" value="<string name='Units/Tau/TigerSharkFlavor'/>"/>
	<entry name="Tyranids/Biovore" value="<string name='Units/Tyranids/Biovore'/>"/>
	<entry name="Tyranids/BiovoreDescription" value="<string name='Units/Tyranids/BiovoreDescription'/>"/>
	<entry name="Tyranids/BiovoreFlavor" value="<string name='Units/Tyranids/BiovoreFlavor'/>"/>
	<entry name="Tyranids/Carnifex" value="<string name='Units/Tyranids/Carnifex'/>"/>
	<entry name="Tyranids/CarnifexDescription" value="<string name='Units/Tyranids/CarnifexDescription'/>"/>
	<entry name="Tyranids/CarnifexFlavor" value="<string name='Units/Tyranids/CarnifexFlavor'/>"/>
	<entry name="Tyranids/Exocrine" value="<string name='Units/Tyranids/Exocrine'/>"/>
	<entry name="Tyranids/ExocrineDescription" value="<string name='Units/Tyranids/ExocrineDescription'/>"/>
	<entry name="Tyranids/ExocrineFlavor" value="<string name='Units/Tyranids/ExocrineFlavor'/>"/>
	<entry name="Tyranids/Gargoyle" value="<string name='Units/Tyranids/Gargoyle'/>"/>
	<entry name="Tyranids/GargoyleDescription" value="<string name='Units/Tyranids/GargoyleDescription'/>"/>
	<entry name="Tyranids/GargoyleFlavor" value="<string name='Units/Tyranids/GargoyleFlavor'/>"/>
	<entry name="Tyranids/Haruspex" value="<string name='Units/Tyranids/Haruspex'/>"/>
	<entry name="Tyranids/HaruspexDescription" value="<string name='Units/Tyranids/HaruspexDescription'/>"/>
	<entry name="Tyranids/HaruspexFlavor" value="<string name='Units/Tyranids/HaruspexFlavor'/>"/>
	<entry name="Tyranids/HiveTyrant" value="<string name='Units/Tyranids/HiveTyrant'/>"/>
	<entry name="Tyranids/HiveTyrantDescription" value="<string name='Units/Tyranids/HiveTyrantDescription'/>"/>
	<entry name="Tyranids/HiveTyrantFlavor" value="<string name='Units/Tyranids/HiveTyrantFlavor'/>"/>
	<entry name="Tyranids/HiveGuard" value="<string name='Units/Tyranids/HiveGuard'/>"/>
	<entry name="Tyranids/HiveGuardDescription" value="<string name='Units/Tyranids/HiveGuardDescription'/>"/>
	<entry name="Tyranids/HiveGuardFlavor" value="<string name='Units/Tyranids/HiveGuardFlavor'/>"/>
	<entry name="Tyranids/Hormagaunt" value="<string name='Units/Tyranids/Hormagaunt'/>"/>
	<entry name="Tyranids/HormagauntDescription" value="<string name='Units/Tyranids/HormagauntDescription'/>"/>
	<entry name="Tyranids/HormagauntFlavor" value="<string name='Units/Tyranids/HormagauntFlavor'/>"/>
	<entry name="Tyranids/Lictor" value="<string name='Units/Tyranids/Lictor'/>"/>
	<entry name="Tyranids/LictorDescription" value="<string name='Units/Tyranids/LictorDescription'/>"/>
	<entry name="Tyranids/LictorFlavor" value="<string name='Units/Tyranids/LictorFlavor'/>"/>
	<entry name="Tyranids/Maleceptor" value="<string name='Units/Tyranids/Maleceptor'/>"/>
	<entry name="Tyranids/MaleceptorDescription" value="<string name='Units/Tyranids/MaleceptorDescription'/>"/>
	<entry name="Tyranids/MaleceptorFlavor" value="<string name='Units/Tyranids/MaleceptorFlavor'/>"/>
	<entry name="Tyranids/NornEmissary" value="<string name='Units/Tyranids/NornEmissary'/>"/>
	<entry name="Tyranids/NornEmissaryDescription" value="<string name='Units/Tyranids/NornEmissaryDescription'/>"/>
	<entry name="Tyranids/NornEmissaryFlavor" value="<string name='Units/Tyranids/NornEmissaryFlavor'/>"/>
	<entry name="Tyranids/Ravener" value="<string name='Units/Tyranids/Ravener'/>"/>
	<entry name="Tyranids/RavenerDescription" value="<string name='Units/Tyranids/RavenerDescription'/>"/>
	<entry name="Tyranids/RavenerFlavor" value="<string name='Units/Tyranids/RavenerFlavor'/>"/>
	<entry name="Tyranids/ScythedHierodule" value="<string name='Units/Tyranids/ScythedHierodule'/>"/>
	<entry name="Tyranids/ScythedHieroduleDescription" value="<string name='Units/Tyranids/ScythedHieroduleDescription'/>"/>
	<entry name="Tyranids/ScythedHieroduleFlavor" value="<string name='Units/Tyranids/ScythedHieroduleFlavor'/>"/>
	<entry name="Tyranids/Tervigon" value="<string name='Units/Tyranids/Tervigon'/>"/>
	<entry name="Tyranids/TervigonDescription" value="<string name='Units/Tyranids/TervigonDescription'/>"/>
	<entry name="Tyranids/TervigonFlavor" value="<string name='Units/Tyranids/TervigonFlavor'/>"/>
	<entry name="Tyranids/Trygon" value="<string name='Units/Tyranids/Trygon'/>"/>
	<entry name="Tyranids/TrygonDescription" value="<string name='Units/Tyranids/TrygonDescription'/>"/>
	<entry name="Tyranids/TrygonFlavor" value="<string name='Units/Tyranids/TrygonFlavor'/>"/>
	<entry name="Tyranids/Tyrannofex" value="<string name='Units/Tyranids/Tyrannofex'/>"/>
	<entry name="Tyranids/TyrannofexDescription" value="<string name='Units/Tyranids/TyrannofexDescription'/>"/>
	<entry name="Tyranids/TyrannofexFlavor" value="<string name='Units/Tyranids/TyrannofexFlavor'/>"/>
	<entry name="Tyranids/Venomthrope" value="<string name='Units/Tyranids/Venomthrope'/>"/>
	<entry name="Tyranids/VenomthropeDescription" value="<string name='Units/Tyranids/VenomthropeDescription'/>"/>
	<entry name="Tyranids/VenomthropeFlavor" value="<string name='Units/Tyranids/VenomthropeFlavor'/>"/>
	<entry name="Tyranids/Warrior" value="<string name='Units/Tyranids/Warrior'/>"/>
	<entry name="Tyranids/WarriorDescription" value="<string name='Units/Tyranids/WarriorDescription'/>"/>
	<entry name="Tyranids/WarriorFlavor" value="<string name='Units/Tyranids/WarriorFlavor'/>"/>
	<entry name="Tyranids/Zoanthrope" value="<string name='Units/Tyranids/Zoanthrope'/>"/>
	<entry name="Tyranids/ZoanthropeDescription" value="<string name='Units/Tyranids/ZoanthropeDescription'/>"/>
	<entry name="Tyranids/ZoanthropeFlavor" value="<string name='Units/Tyranids/ZoanthropeFlavor'/>"/>

	<!-- Other -->
	<entry name="SmokeLauncher" value="烟雾发射器"/>
	<entry name="SmokeLauncherDescription" value="使地面载具单位获得发射烟雾屏障的能力, 提高远程伤害减免效果."/>
	<entry name="SmokeLauncherFlavor" value="一些载具拥有小型发射器, 能够携带烟雾罐. 这些装备能够制造烟雾来掩护载具, 允许其更加安全地通过开阔地带—当然这样做的代价就是需要发射自己携带的武器."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegration'/>"/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="提高临近位置每个建筑的研究产出."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegrationFlavor'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="提高攻击武器的护甲穿透效果."/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceDescription'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
	<entry name="AdeptusMechanicus/BlessedConduits" value="祝福管道"/>
	<entry name="AdeptusMechanicus/BlessedConduitsDescription" value="降低动力之涌的冷却时间."/>
	<entry name="AdeptusMechanicus/BlessedConduitsFlavor" value="“当最后登上车辆时, 他察觉到了引擎故障并立即击打了符文. 结果很好. 引擎启动, 瞬间充满了力量…“<br/>—引擎领主, 第16卷, 第2001节"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="铁骑弩兵, 塞多尼安龙骑兵, 毒蝎沙漠掠袭者, 沙丘行者以及毒蝎瓦解者专属技能. 能够降低临近机械神教单位的士气损失."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistDescription'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier2" value="<string name='Traits/AdeptusMechanicus/CityTier2'/>"/>
	<entry name="AdeptusMechanicus/CityTier2Description" value="<string name='Traits/AdeptusMechanicus/CityTier2Description'/>"/>
	<entry name="AdeptusMechanicus/CityTier2Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier2Flavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier3" value="<string name='Traits/AdeptusMechanicus/CityTier3'/>"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="<string name='Traits/AdeptusMechanicus/CityTier3Description'/>"/>
	<entry name="AdeptusMechanicus/CityTier3Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier3Flavor'/>"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubber" value="<string name='Weapons/CognisHeavyStubber'/>"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberDescription" value="使沙丘爬行者获得认知重型伐木枪."/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberFlavor" value="<string name='Weapons/CognisHeavyStubberFlavor'/>"/>
	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Traits/AdeptusMechanicus/CommandUplink'/>"/>
	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="始祖鸟矢量型运输机, 猛禽型战斗机专属技能. 能够降低临近机械神教单位的士气损失."/>
	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Traits/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTether" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTether'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="提高护教军先锋和护教军游侠的士气."/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmour" value="格莱亚协议"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourDescription" value="提高步兵的护甲."/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourFlavor" value="将格莱亚协议集成到护教军的命令电路中能够进一步增强他们本已令人印象深刻的生存能力. 格莱亚铸造世界以拒绝屈服而闻名, 这样的逻辑确实是无可辩驳的."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmour" value="阿格里皮纳消融"/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourDescription" value="提高载具的护甲."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourFlavor" value="卡迪亚陷落之后, 在阿格里皮纳以恐惧之眼为生的机械神教成员各个都是防御专家—尤其是当他们的机器受到攻击时."/>
	<entry name="AdeptusMechanicus/FidorumVossPrime" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrime'/>"/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="提高临近位置每座建筑的影响力产出."/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrimeFlavor'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrathDescription" value="使铁骑弩兵, 塞多尼安龙骑兵, 雷岩电僧, 沙丘行者, 铁翼火龙和远征骑士施展更具毁灭性攻击的能力."/>
	<entry name="AdeptusMechanicus/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="提高重型武器的护甲穿透效果."/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiah'/>"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="提高临近位置每座建筑的忠诚度产出."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiahFlavor'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulDescription'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightDescription'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancer" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancer'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerDescription" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerDescription'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerFlavor" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerFlavor'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisation" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisation'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationDescription'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationFlavor'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/Omnispex" value="<string name='Traits/AdeptusMechanicus/Omnispex'/>"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="使护教军先锋和护教军游侠无视远程伤害减免效果."/>
	<entry name="AdeptusMechanicus/OmnispexFlavor" value="<string name='Traits/AdeptusMechanicus/OmnispexFlavor'/>"/>
	<entry name="AdeptusMechanicus/OptateRestrictions" value="<string name='Traits/AdeptusMechanicus/OptateRestrictions'/>"/>
	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="提高哈布神庙的人口上限."/>
	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="<string name='Traits/AdeptusMechanicus/OptateRestrictionsFlavor'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocols'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="提高城市增长率."/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocolsFlavor'/>"/>
	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmDescription'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SolarReflectors" value="<string name='Traits/AdeptusMechanicus/SolarReflectors'/>"/>
	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="提高临近位置每座建筑的能量产出."/>
	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="<string name='Traits/AdeptusMechanicus/SolarReflectorsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeams'/>"/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="提高临近位置每座建筑的食物产出."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeamsFlavor'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenment" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenment'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentDescription'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentFlavor'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralism" value="<string name='Traits/AdeptusMechanicus/TerranGeneralism'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismDescription'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismFlavor'/>"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiency" value="热交换效能"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyDescription" value="提高动力之涌的产出加成."/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyFlavor" value="矛盾的是, 降低热交换器的输入并通过向第二组热耦合器冷凝器输送等离子体可以延长其运行时间, 并超出原有设计指标—或者至少降低生命损失和所需的维修量."/>
	<entry name="AdeptusMechanicus/TriplexNecessity" value="<string name='Traits/AdeptusMechanicus/TriplexNecessity'/>"/>
	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="提高临近位置每座建筑的矿石产出."/>
	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="<string name='Traits/AdeptusMechanicus/TriplexNecessityFlavor'/>"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannon" value="<string name='Weapons/TwinLinkedIcarusAutocannon'/>"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonDescription" value="使远征骑士获得双联伊卡洛斯自动加农炮."/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonFlavor" value="<string name='Weapons/TwinLinkedIcarusAutocannonFlavor'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="提高西卡里安渗透者临近位置敌方单位的士气损失."/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
	<entry name="AdeptusMechanicus/XenariteAcceptance" value="异种之邀"/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceDescription" value="提高从瓦尔遗迹获得的固定研究产出."/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceFlavor" value="似乎来自来自黄泉八号的异种学教科技神甫也开始踏足格雷迪厄斯—在每个前哨站设置观察单位以及无人机能够更方便的去了解异种. 针对研究的实际应用其前景是显而易见的…"/>
	<entry name="AstraMilitarum/AdditionalHeavyBolters" value="额外重型爆矢枪"/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersDescription" value="使毒刃坦克, 黎曼鲁斯战斗坦克, 罗格多恩战斗坦克以及瓦尔基里获得额外的重型爆矢枪."/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachine" value="<string name='Actions/AwakenTheMachine'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachineDescription" value="使科技神甫工程师获得提高载具单位伤害的能力."/>
	<entry name="AstraMilitarum/AwakenTheMachineFlavor" value="<string name='Actions/AwakenTheMachineFlavor'/>"/>
	<entry name="AstraMilitarum/BanebladeLascannon" value="毒刃坦克激光加农炮"/>
	<entry name="AstraMilitarum/BanebladeLascannonDescription" value="使毒刃坦克获得额外的激光加农炮."/>
	<entry name="AstraMilitarum/BanebladeLascannonFlavor" value="<string name='Weapons/LascannonFlavor'/>"/>
	<entry name="AstraMilitarum/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="提高手雷, 导弹和爆破武器的护甲穿透效果."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="提高爆矢武器的护甲穿透效果."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BruteShield" value="<string name='Traits/BruteShield'/>"/>
	<entry name="AstraMilitarum/BruteShieldDescription" value="使牛格林获得伤害减免效果."/>
	<entry name="AstraMilitarum/BruteShieldFlavor" value="<string name='Traits/BruteShieldFlavor'/>"/>
	<entry name="AstraMilitarum/CamoNetting" value="<string name='Traits/CamoNetting'/>"/>
	<entry name="AstraMilitarum/CamoNettingDescription" value="提高地面载具的远程伤害减免效果."/>
	<entry name="AstraMilitarum/CamoNettingFlavor" value="<string name='Traits/CamoNettingFlavor'/>"/>
	<entry name="AstraMilitarum/ChaffLauncher" value="诱饵弹发射器"/>
	<entry name="AstraMilitarum/ChaffLauncherDescription" value="使雷霆箭战斗机和劫掠者轰炸机获得发射诱饵弹的能力, 提高远程伤害减免效果."/>
	<entry name="AstraMilitarum/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="AstraMilitarum/CityTier2" value="<string name='Traits/AstraMilitarum/CityTier2'/>"/>
	<entry name="AstraMilitarum/CityTier2Description" value="提高城市能够占领的土地半径."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="<string name='Traits/AstraMilitarum/CityTier2Flavor'/>"/>
	<entry name="AstraMilitarum/CityTier3" value="<string name='Traits/AstraMilitarum/CityTier3'/>"/>
	<entry name="AstraMilitarum/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="<string name='Traits/AstraMilitarum/CityTier3Flavor'/>"/>
	<entry name="AstraMilitarum/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="AstraMilitarum/DozerBladeDescription" value="降低坦克单位在森林和帝国废墟中的移动惩罚效果."/>
	<entry name="AstraMilitarum/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmourDescription" value="提高步兵单位的护甲."/>
	<entry name="AstraMilitarum/ExtraInfantryArmourFlavor" value="在第41千年中的绝大多数步兵都装备了防弹衣或者同等类型的装备. 如果指挥官希望他的部队拥有更多的生存机会, 那么士兵们将有机会穿上更加接近于帝国卫队的高等级护甲."/>
	<entry name="AstraMilitarum/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="AstraMilitarum/ExtraVehicleArmourDescription" value="提高载具单位的护甲."/>
	<entry name="AstraMilitarum/ExtraVehicleArmourFlavor" value="为坦克添加额外的护甲对于科技神甫而言称得上是歪门邪道, 但对于帝国卫队的士兵来说却很普通. 用于应对特定武器而专门打造的烧蚀镀层或定制装甲并不是什么前所未闻的事情."/>
	<entry name="AstraMilitarum/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="AstraMilitarum/FragGrenadeDescription" value="使牛格林, 野战炮群, 卫兵, 重武器小队, 总政委, 高阶灵能者, 科技神甫工程师以及风暴军获得投掷反步兵手雷的能力."/>
	<entry name="AstraMilitarum/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AstraMilitarum/HammerOfWrathDescription" value="使侦察哨兵和牛格林获得施展更具破坏性攻击的能力."/>
	<entry name="AstraMilitarum/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissileDescription" value="使地面载具单位获得发射猎人杀手导弹的能力."/>
	<entry name="AstraMilitarum/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialSplendour" value="<string name='Traits/ImperialSplendour'/>"/>
	<entry name="AstraMilitarum/ImperialSplendourDescription" value="提高帝国卫队城市的影响力."/>
	<entry name="AstraMilitarum/ImperialSplendourFlavor" value="<string name='Traits/ImperialSplendourFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpoint" value="<string name='Units/AstraMilitarum/ImperialStrongpoint'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpointDescription" value="使科技神甫工程师能够修建配备重型爆矢枪堡垒的能力."/>
	<entry name="AstraMilitarum/ImperialStrongpointFlavor" value="<string name='Units/AstraMilitarum/ImperialStrongpointFlavor'/>"/>
	<entry name="AstraMilitarum/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="AstraMilitarum/KrakGrenadeDescription" value="使野战炮群, 卫兵, 重武器小队, 总政委, 高阶灵能者, 科技神甫工程师以及风暴军获得投掷反步兵手雷的能力."/>
	<entry name="AstraMilitarum/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="提高激光武器的护甲穿透效果."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="AstraMilitarum/MediPack" value="<string name='Actions/MediPack'/>"/>
	<entry name="AstraMilitarum/MediPackDescription" value="使卫兵和风暴军获得在战斗中治疗自己的能力."/>
	<entry name="AstraMilitarum/MediPackFlavor" value="<string name='Actions/MediPackFlavor'/>"/>
	<entry name="AstraMilitarum/Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="AstraMilitarum/MisfortuneDescription" value="使玄标灵能者获得诅咒敌方单位的能力, 提高他们受到的伤害."/>
	<entry name="AstraMilitarum/MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="AstraMilitarum/RecoveryGear" value="<string name='Traits/RecoveryGear'/>"/>
	<entry name="AstraMilitarum/RecoveryGearDescription" value="提高地面载具单位的治疗速率."/>
	<entry name="AstraMilitarum/RecoveryGearFlavor" value="<string name='Traits/RecoveryGearFlavor'/>"/>
	<entry name="AstraMilitarum/RelicPlating" value="<string name='Traits/RelicPlating'/>"/>
	<entry name="AstraMilitarum/RelicPlatingDescription" value="使地面载具获得巫火伤害减免效果."/>
	<entry name="AstraMilitarum/RelicPlatingFlavor" value="<string name='Traits/RelicPlatingFlavor'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissile" value="<string name='Weapons/SkystrikeMissile'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissileDescription" value="使雷霆箭战斗机获得发射防空导弹的能力."/>
	<entry name="AstraMilitarum/SkystrikeMissileFlavor" value="<string name='Weapons/SkystrikeMissileFlavor'/>"/>
	<entry name="AstraMilitarum/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilots" value="<string name='Traits/TrainedSentinelPilots'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsDescription" value="提高侦察哨兵的伤害."/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsFlavor" value="<string name='Traits/TrainedSentinelPilotsFlavor'/>"/>
	<entry name="AstraMilitarum/VoidShieldGenerator" value="<string name='Units/AstraMilitarum/VoidShieldGenerator'/>"/>
	<entry name="AstraMilitarum/VoidShieldGeneratorDescription" value="使科技神甫获得建造护盾生成器的能力, 为范围内的单位提供远程伤害减免效果."/>
	<entry name="AstraMilitarum/VoidShieldGeneratorFlavor" value="<string name='Units/AstraMilitarum/VoidShieldGeneratorFlavor'/>"/>
	<entry name="AstraMilitarum/VoxCaster" value="<string name='Traits/VoxCaster'/>"/>
	<entry name="AstraMilitarum/VoxCasterDescription" value="降低卫兵和风暴军的士气损失."/>
	<entry name="AstraMilitarum/VoxCasterFlavor" value="<string name='Traits/VoxCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculum'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="使混沌勇士在杀死一名敌人后有几率获得永久准确度提升效果(混沌之赐)."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculumFlavor'/>"/>
	<entry name="ChaosSpaceMarines/BlastDamage" value="<string name='Traits/ChaosSpaceMarines/BlastDamage'/>"/>
	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="提高手雷, 飞弹和爆炸武器的护甲穿透效果."/>
	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Bloated" value="<string name='Traits/ChaosSpaceMarines/Bloated'/>"/>
	<entry name="ChaosSpaceMarines/BloatedDescription" value="使混沌勇士在杀死一名敌人时有几率恢复其生命值."/>
	<entry name="ChaosSpaceMarines/BloatedFlavor" value="<string name='Traits/ChaosSpaceMarines/BloatedFlavor'/>"/>
	<entry name="ChaosSpaceMarines/BoltDamage" value="<string name='Traits/ChaosSpaceMarines/BoltDamage'/>"/>
	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="提高爆矢武器的护甲穿透效果."/>
	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosRising" value="混沌升腾"/>
	<entry name="ChaosSpaceMarines/ChaosRisingDescription" value="降低建立新城市的花费."/>
	<entry name="ChaosSpaceMarines/ChaosRisingFlavor" value="格雷迪格斯的帝国居民对于混沌几乎一无所知, 但在异种的炼狱里, 它们对于遥远帝皇的信仰早已开始动摇. 对于你的信徒和使者们来说很容易就能够将它们先给神祇—然后把灾厄降临在它们身上."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="<string name='Traits/ChaosSpaceMarines/CityTier2'/>"/>
	<entry name="ChaosSpaceMarines/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier2Flavor'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3" value="<string name='Traits/ChaosSpaceMarines/CityTier3'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier3Flavor'/>"/>
	<entry name="ChaosSpaceMarines/CrystallineBody" value="<string name='Traits/ChaosSpaceMarines/CrystallineBody'/>"/>
	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="使混沌勇士在杀死一名敌人后有几率获得永久性生命值提高效果(混沌之赐)."/>
	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="<string name='Traits/ChaosSpaceMarines/CrystallineBodyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="使混沌犀牛和玷污者获得无视掩护的能力."/>
	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourDescription" value="提高步兵单位的护甲."/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourFlavor" value="第41千年中绝大多数步兵都拥有防弹衣或者类似的装备. 如果指挥官希望他的部队拥有更高的生存机会, 那么就会给士兵们准备更高级的战甲."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourDescription" value="提高载具单位的护甲."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourFlavor" value="对于科技神甫而言, 为坦克增加额外的装甲板无异于是异端邪说, 但对于帝国卫队的士兵而言这却是常态. 使用经过特殊处理的镀层或者定制护甲并非闻所未闻."/>
	<entry name="ChaosSpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="ChaosSpaceMarines/FragGrenadeDescription" value="使混沌领主, 混沌星际战士, 浩劫小队, 恐虐狂战士, 混沌星际战士巫师和亚空间铁匠获得投掷反步兵手雷的能力."/>
	<entry name="ChaosSpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutation" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutation'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="使混沌领主, 混沌星际战士, 混沌终结者, 浩劫小队, 恐虐狂战士, 混沌星际战士巫师, 亚空间之爪和亚空间铁匠随机获得一种已解锁的混沌之赐效果."/>
	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutationFlavor'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrathDescription" value="使恶魔王子, 玷污者, 铸造魔, 巨型黄铜魔蝎, 地狱兽, 重拳魔, 剧毒爬行者以及亚空间之爪获得施展更致命攻击的能力."/>
	<entry name="ChaosSpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/HavocLauncher" value="<string name='Weapons/HavocLauncher'/>"/>
	<entry name="ChaosSpaceMarines/HavocLauncherDescription" value="使混沌犀牛和混沌兰德袭击者获得一件中等射程爆炸武器."/>
	<entry name="ChaosSpaceMarines/HavocLauncherFlavor" value="<string name='Weapons/HavocLauncherFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlame" value="<string name='Actions/ChaosSpaceMarines/IconOfFlame'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlameDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlameFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeance" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeance'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenadeDescription" value="使混沌领主, 混沌星际战士, 浩劫小队, 恐虐狂战士, 混沌星际战士巫师和亚空间铁匠获得投掷反步兵手雷的能力."/>
	<entry name="ChaosSpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LasDamage" value="<string name='Traits/ChaosSpaceMarines/LasDamage'/>"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="提高激光和等离子武器的护甲穿透效果."/>
	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/LasDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Mechanoid" value="<string name='Traits/ChaosSpaceMarines/Mechanoid'/>"/>
	<entry name="ChaosSpaceMarines/MechanoidDescription" value="使混沌勇士在杀死一名敌人后有几率获得永久护甲提升效果(混沌之赐)."/>
	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="<string name='Traits/ChaosSpaceMarines/MechanoidFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MeleeDamage" value="<string name='Traits/ChaosSpaceMarines/MeleeDamage'/>"/>
	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="ChaosSpaceMarines/MeltaBombDescription" value="使混沌领主, 混沌星际战士, 浩劫小队以及恐虐狂战士获得部署热熔炸弹的能力. 非常适合用来对付重型载具和堡垒."/>
	<entry name="ChaosSpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGods" value="把生命献给诸神"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsDescription" value="提高狂热牺牲的增长率."/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsFlavor" value="在格雷迪厄斯, 无数生命殒落在狂热者的刀刃之下. 每一次死亡都是毫无意义, 空虚而又肤浅的. 但这也增加了牺牲的效果, 让这个世界更加靠近黑暗之神的炼狱领域."/>
	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
	<entry name="ChaosSpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="ChaosSpaceMarines/SmokeLauncherDescription" value="使混沌犀牛, 玷污者和混沌兰德袭击者获得发射烟雾屏障的能力, 提高远程伤害减免效果."/>
	<entry name="ChaosSpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="ChaosSpaceMarines/TemporalDistortion" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortion'/>"/>
	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="使混沌勇士在杀死一名敌人后有几率获得永久移动提升效果(混沌之赐)."/>
	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWar'/>"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="降低士气损失, 提高步兵单位对星际战士阵营单位的近战准确度."/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWarFlavor'/>"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoyles" value="亚空间烈焰石像鬼"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesDescription" value="使混沌犀牛, 玷污者和混沌兰德袭击者的武器获得持续性伤害效果."/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesFlavor" value="这辆载具的枪口闪耀着奇怪的火焰."/>
	<entry name="ChaosSpaceMarines/WarpFrenzy" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzy'/>"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="使混沌勇士在杀死一名敌人后有几率获得永久攻击提升效果(混沌之赐)."/>
	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzyFlavor'/>"/>
	<entry name="CompendiumFlavor" value="每个阵营都会以不同的方式来扩大城市. 比如有时候, 欧克兽人战争首领会和兽人小子们一起把防护墙举起来然后向外移开, 或者太空死灵领主会命令奴隶在远古坟墓中挖出更多的空间. 他们之所以会这样做, 都是为了能够建造更多的新建筑以扩大阵营的实力."/>
	<entry name="Drukhari/AssaultWeaponBonus" value="<string name='Traits/Drukhari/AssaultWeaponBonus'/>"/>
	<entry name="Drukhari/AssaultWeaponBonusDescription" value="<string name='Upgrades/Eldar/AssaultWeaponBonusDescription'/>"/>
	<entry name="Drukhari/AssaultWeaponBonusFlavor" value="<string name='Traits/Drukhari/AssaultWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgrade" value="定点暗杀"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="提高黑暗灵族默认主城的忠诚度."/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="在黑暗灵族都市的街道上, 死亡是一件再寻常不过的事情. 尸体散落在每个角落, 等待着乌古尔将他们拖走… 不过德拉古和卡巴尔贵族的死亡实属罕见. 否则至少要等到执政官的梦魇剑客发现了某些阴谋. 现在, 即使是最轻微的不忠迹象也会导致另一个高贵的尸体出现在执政官休眠区上方的尖刺上…"/>
	<entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
	<entry name="Drukhari/BonusResourcesDescription" value="<string name='Actions/Drukhari/BonusResourcesDescription'/>"/>
	<entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
	<entry name="Drukhari/CityTier2" value="<string name='Traits/Drukhari/CityTier2'/>"/>
	<entry name="Drukhari/CityTier2Description" value="<string name='Traits/Drukhari/CityTier2Description'/>"/>
	<entry name="Drukhari/CityTier2Flavor" value="<string name='Traits/Drukhari/CityTier2Flavor'/>"/>
	<entry name="Drukhari/CityTier3" value="<string name='Traits/Drukhari/CityTier3'/>"/>
	<entry name="Drukhari/CityTier3Description" value="<string name='Traits/Drukhari/CityTier3Description'/>"/>
	<entry name="Drukhari/CityTier3Flavor" value="<string name='Traits/Drukhari/CityTier3Flavor'/>"/>
	<entry name="Drukhari/CombatDrugsUpgrade" value="邪教冷酷商人"/>
	<entry name="Drukhari/CombatDrugsUpgradeDescription" value="使所有黑暗灵族步兵获得使用战斗药剂的能力."/>
	<entry name="Drukhari/CombatDrugsUpgradeFlavor" value="尽管战斗药剂在黑暗灵族社会中被广泛使用, 但巫灵教派最乐意使用它们, 尽管这会对他们的生理和寿命带来有害影响. 巫灵教派联络者能够接触到大量可怕化合物, 这对于阴谋集团进行现实空间袭击来说是一个巨大的福音."/>
	<entry name="Drukhari/CorsairOutpostsUpgrade" value="灵魂冲刺"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="提高默认黑暗灵族城市的增长率."/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="谣言就如同科摩罗的瘟疫一样蔓延: 关于格雷迪厄斯的财富; 关于数十亿受苦受难人类被困在亚空间风暴之下; 还有这颗星球奇怪的灵骨核心, 里面充满了失落的艾达灵族之魂… 无论真假如何, 黑暗灵族来了. 但这些谣言是从何而来的呢..?"/>
	<entry name="Drukhari/EnergyBuildingBonus" value="<string name='Traits/Drukhari/EnergyBuildingBonus'/>"/>
	<entry name="Drukhari/EnergyBuildingBonusDescription" value="提高能够产生能量建筑的能量产出."/>
	<entry name="Drukhari/EnergyBuildingBonusFlavor" value="<string name='Traits/Drukhari/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
	<entry name="Drukhari/EnhancedAethersailsDescription" value="提高劫掠者, 蹂躏者和坦塔罗斯的移动."/>
	<entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
	<entry name="Drukhari/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Drukhari/ExtraInfantryArmourDescription" value="提高步兵的护甲."/>
	<entry name="Drukhari/ExtraInfantryArmourFlavor" value="为整支军队配备幽灵板甲代价极其昂贵. 但对于少数受青睐的人来说, 执政官已为其挥霍一空. 幽灵板甲由奇怪的树脂材料制成并充满了比空气还轻的气体, 在提供强大保护的情况夏几乎没有任何重量."/>
	<entry name="Drukhari/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Drukhari/ExtraVehicleArmourDescription" value="<string name='Traits/ExtraVehicleArmourDescription'/>"/>
	<entry name="Drukhari/ExtraVehicleArmourFlavor" value="无论配备闪烁场还是暗夜护盾, 黑暗灵族的车辆都很难被发现, 更难被瞄准. 将幽灵板甲应用在更珍贵的机械中成本高昂, 但可以减轻它们的重量并提高速度, 从而进一步降低被击中的可能性."/>
	<entry name="Drukhari/FieldRepairs" value="<string name='Actions/Drukhari/FieldRepairs'/>"/>
	<entry name="Drukhari/FieldRepairsDescription" value="使所有黑暗灵族载具能够恢复生命值."/>
	<entry name="Drukhari/FieldRepairsFlavor" value="<string name='Actions/Drukhari/FieldRepairsFlavor'/>"/>
	<entry name="Drukhari/GraveLotus" value="墓地莲"/>
	<entry name="Drukhari/GraveLotusDescription" value="战斗药剂将提供额外的近战伤害."/>
	<entry name="Drukhari/GraveLotusFlavor" value="在魔鬼果园中, 令人作呕的墓地莲从死者的遗骸中生根发芽. 这是一种鲜艳的紫色真菌, 能够窃取刚刚逝去之人的残余力量以促进自身生长. 巫灵教派成员会吸收墓地莲的液体以强化自己的力量."/>
	<entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
	<entry name="Drukhari/GrislyTrophiesDescription" value="使毒灾, 劫掠者, 蹂躏者和坦塔罗斯获得光环效果, 降低临近友方黑暗灵族单位的士气损失."/>
	<entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
	<entry name="Drukhari/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Drukhari/HammerOfWrathDescription" value="使克洛诺斯, 恶狼, 掠袭者, 天灾和痛苦引擎获得施展更具破坏性攻击的能力."/>
	<entry name="Drukhari/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Drukhari/HaywireGrenade" value="<string name='Weapons/HaywireGrenade'/>"/>
	<entry name="Drukhari/HaywireGrenadeDescription" value="使阴谋团战士, 魅魔和巫灵获得投掷反装甲手雷的能力."/>
	<entry name="Drukhari/HaywireGrenadeFlavor" value="<string name='Weapons/HaywireGrenadeFlavor'/>"/>
	<entry name="Drukhari/HeavyWeaponBonus" value="<string name='Traits/Drukhari/HeavyWeaponBonus'/>"/>
	<entry name="Drukhari/HeavyWeaponBonusDescription" value="提高重型武器的护甲穿透效果."/>
	<entry name="Drukhari/HeavyWeaponBonusFlavor" value="<string name='Traits/Drukhari/HeavyWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/Hypex" value="海佩克斯"/>
	<entry name="Drukhari/HypexDescription" value="该战斗药剂能够提供穿过掩护效果."/>
	<entry name="Drukhari/HypexFlavor" value="捕获食灵怪是一项危险的任务, 但能够出售给巫灵教派以换取高昂的价格. 从类昆虫生物的脑液中提取的药物海佩克斯能够将黑暗灵族本就敏锐的反应速度提高到真正惊人的水平."/>
	<entry name="Drukhari/MeleeWeaponBonus" value="<string name='Traits/Drukhari/MeleeWeaponBonus'/>"/>
	<entry name="Drukhari/MeleeWeaponBonusDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="Drukhari/MeleeWeaponBonusFlavor" value="<string name='Traits/Drukhari/MeleeWeaponBonusFlavor'/>"/>
	<entry name="Drukhari/NightShields" value="<string name='Traits/Drukhari/NightShields'/>"/>
	<entry name="Drukhari/NightShieldsDescription" value="提高劫掠者, 蹂躏者, 刃翼喷气战斗机, 坦塔罗斯和虚空鸦轰炸机的远程伤害减免效果."/>
	<entry name="Drukhari/NightShieldsFlavor" value="<string name='Traits/Drukhari/NightShieldsFlavor'/>"/>
	<entry name="Drukhari/Painbringer" value="痛苦使者"/>
	<entry name="Drukhari/PainbringerDescription" value="该战斗药剂能够提供无视痛苦伤害减免效果."/>
	<entry name="Drukhari/PainbringerFlavor" value="只有被流放的史利斯库斯公爵才能获得充足的痛苦使者. 它是所有增强药剂中最稀有的一种, 能够使饮用者的皮肤硬化, 就如同固化的皮革一样富有韧性. 这个过程当然也是极其痛苦的, 尽管它的倡导者认为这样的痛苦不足挂齿."/>
	<entry name="Drukhari/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
	<entry name="Drukhari/PlasmaGrenadeDescription" value="使执政官, 天灾, 魅魔和巫灵获得投掷通用手雷的能力."/>
	<entry name="Drukhari/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
	<entry name="Drukhari/RaiderFortress" value="<string name='Actions/Drukhari/RaiderFortress'/>"/>
	<entry name="Drukhari/RaiderFortressDescription" value="允许在占领的网道传送门上建立新的城市."/>
	<entry name="Drukhari/RaiderFortressFlavor" value="<string name='Actions/Drukhari/RaiderFortressFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsDamage" value="<string name='Traits/Drukhari/RaidersTacticsDamage'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageDescription" value="当从载具卸载时, 提高黑暗灵族步兵单位的伤害."/>
	<entry name="Drukhari/RaidersTacticsDamageFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageReduction" value="<string name='Traits/Drukhari/RaidersTacticsDamageReduction'/>"/>
	<entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="当从载具卸载时, 提高黑暗灵族步兵单位的无敌伤害减免效果."/>
	<entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageReductionFlavor'/>"/>
	<entry name="Drukhari/RaidersTacticsHealingRate" value="<string name='Traits/Drukhari/RaidersTacticsHealingRate'/>"/>
	<entry name="Drukhari/RaidersTacticsHealingRateDescription" value="当从载具卸载时, 提高黑暗灵族步兵单位的治疗速率."/>
	<entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="<string name='Traits/Drukhari/RaidersTacticsHealingRateFlavor'/>"/>
	<entry name="Drukhari/SacrificeToKhaine" value="<string name='Traits/Drukhari/SacrificeToKhaine'/>"/>
	<entry name="Drukhari/SacrificeToKhaineDescription" value="<string name='Actions/Drukhari/SacrificeToKhaineDescription'/>"/>
	<entry name="Drukhari/SacrificeToKhaineFlavor" value="<string name='Traits/Drukhari/SacrificeToKhaineFlavor'/>"/>
	<entry name="Drukhari/ShroudGate" value="<string name='Traits/Drukhari/ShroudGate'/>"/>
	<entry name="Drukhari/ShroudGateDescription" value="穿过网道门或网道传送门时提高单位的远程伤害减免效果."/>
	<entry name="Drukhari/ShroudGateFlavor" value="<string name='Traits/Drukhari/ShroudGateFlavor'/>"/>
	<entry name="Drukhari/SoulHungerCost" value="灵魂核心"/>
	<entry name="Drukhari/SoulHungerCostDescription" value="降低灵魂之饥的消耗."/>
	<entry name="Drukhari/SoulHungerCostFlavor" value="随着黑暗灵族对现实空间的袭击变得愈发深入, 这个世界和科摩罗之间的联系也变得越来越频繁. 即便是在物理上被封锁, 阴谋团仍然有可能从这些连结中汲取力量, 并通过它们来回馈被吸取的生命."/>
	<entry name="Drukhari/SoulHungerLoyalty" value="<string name='Traits/Drukhari/SoulHungerLoyalty'/>"/>
	<entry name="Drukhari/SoulHungerLoyaltyDescription" value="<string name='Actions/Drukhari/SoulHungerLoyaltyDescription'/>"/>
	<entry name="Drukhari/SoulHungerLoyaltyFlavor" value="<string name='Traits/Drukhari/SoulHungerLoyaltyFlavor'/>"/>
	<entry name="Drukhari/SoulHungerOutposts" value="<string name='Traits/Drukhari/SoulHungerOutposts'/>"/>
	<entry name="Drukhari/SoulHungerOutpostsDescription" value="<string name='Actions/Drukhari/SoulHungerOutpostsDescription'/>"/>
	<entry name="Drukhari/SoulHungerOutpostsFlavor" value="<string name='Traits/Drukhari/SoulHungerOutpostsFlavor'/>"/>
	<entry name="Drukhari/SoulHungerUpgrade" value="虐魂狂徒"/>
	<entry name="Drukhari/SoulHungerUpgradeDescription" value="在黑暗灵族单位消灭一个敌人时提高其获得的影响力."/>
	<entry name="Drukhari/SoulHungerUpgradeFlavor" value="“你必须给他们下药. 杀死守卫. 打开大门. 然后轻声叫醒他们. 带领他们走向自由. 给他们逃生的希望. 认真寻求. 伪装死亡. 为他们寻找出口. 然后揭晓谜底. 接下来把他们送回刑讯室, 邪恶地大笑. 你根本没有触碰到他们, 但却给他们带来了一种永不磨灭的精神痛苦.” – 吉提纽斯·罗什, 前终末之刃执政官"/>
	<entry name="Drukhari/FeastOfTorment" value="<string name='Traits/Drukhari/FeastOfTorment'/>"/>
	<entry name="Drukhari/FeastOfTormentDescription" value="<string name='Actions/Drukhari/FeastOfTormentDescription'/>"/>
	<entry name="Drukhari/FeastOfTormentFlavor" value="<string name='Traits/Drukhari/FeastOfTormentFlavor'/>"/>
	<entry name="Drukhari/SoulShelling" value="<string name='Actions/Drukhari/SoulShelling'/>"/>
	<entry name="Drukhari/SoulShellingDescription" value="<string name='Actions/Drukhari/SoulShellingDescription'/>"/>
	<entry name="Drukhari/SoulShellingFlavor" value="<string name='Actions/Drukhari/SoulShellingFlavor'/>"/>
	<entry name="Drukhari/Splintermind" value="毒晶之心"/>
	<entry name="Drukhari/SplintermindDescription" value="该战斗药剂能够降低士气损失."/>
	<entry name="Drukhari/SplintermindFlavor" value="毒晶之心是由死去的艾达灵族先知的地面水晶残片所制成的. 虽然它不能预示未来, 但这种尘埃状的物质可以让服用它的人同时进行多个维度的思考—这是一种无价的资产, 因为混乱的战斗必然会影响最缜密的作战计划."/>
	<entry name="Drukhari/TormentGrenadeLaunchers" value="<string name='Weapons/TormentGrenadeLaunchers'/>"/>
	<entry name="Drukhari/TormentGrenadeLaunchersDescription" value="使劫掠者, 蹂躏者和坦塔罗斯获得痛苦榴弹发射器."/>
	<entry name="Drukhari/TormentGrenadeLaunchersFlavor" value="<string name='Weapons/TormentGrenadeLaunchersFlavor'/>"/>
	<entry name="Drukhari/WealthPlunder" value="<string name='Actions/Drukhari/WealthPlunder'/>"/>
	<entry name="Drukhari/WealthPlunderDescription" value="<string name='Actions/Drukhari/WealthPlunderDescription'/>"/>
	<entry name="Drukhari/WealthPlunderFlavor" value="<string name='Actions/Drukhari/WealthPlunderFlavor'/>"/>
	<entry name="Drukhari/WeaponRacks" value="<string name='Traits/Drukhari/WeaponRacks'/>"/>
	<entry name="Drukhari/WeaponRacksDescription" value="使从劫掠者或坦塔罗斯卸载的单位获得双联远程武器."/>
	<entry name="Drukhari/WeaponRacksFlavor" value="<string name='Traits/Drukhari/WeaponRacksFlavor'/>"/>
	<entry name="Drukhari/WebwayTravelAction" value="现实空间入侵"/>
	<entry name="Drukhari/WebwayTravelActionDescription" value="网道穿越不再有消耗."/>
	<entry name="Drukhari/WebwayTravelActionFlavor" value="虽然穿过网道门本身很简单, 但协调大型部队的编组行动可能会让他们变得脆弱. 对于黑暗灵族来说, 这个问题可以通过在任何永久传送门周围建立临时网道门来解决, 以确保整个团队能够快速通过."/>
	<entry name="Eldar/AircraftBuildingBonus" value="<string name='Traits/Eldar/AircraftBuildingBonus'/>"/>
	<entry name="Eldar/AircraftBuildingBonusDescription" value="提高传送门尖塔的生产产出."/>
	<entry name="Eldar/AircraftBuildingBonusFlavor" value="<string name='Traits/Eldar/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Eldar/AssaultWeaponBonus" value="<string name='Traits/Eldar/AssaultWeaponBonus'/>"/>
	<entry name="Eldar/AssaultWeaponBonusDescription" value="提高突击武器的护甲穿透效果."/>
	<entry name="Eldar/AssaultWeaponBonusFlavor" value="<string name='Traits/Eldar/AssaultWeaponBonusFlavor'/>"/>
	<entry name="Eldar/AsuryaniArrivalsBonus" value="先知的召唤"/>
	<entry name="Eldar/AsuryaniArrivalsBonusDescription" value="降低阿苏焉降临的消耗."/>
	<entry name="Eldar/AsuryaniArrivalsBonusFlavor" value="我的话不仅仅是建议: 它抛去了传说中的亡者意识所过滤掉的不道德行为, 是努力为混乱的宇宙带来新的秩序. 我们的种族需要明白这一点, 并听取我的话. 我不是独裁者, 而是一名专心的学生.”<br/>  — 先知凯泰蒙"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2" value="方舟世界誓约"/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Description" value="降低阿苏焉降临的冷却时间."/>
	<entry name="Eldar/AsuryaniArrivalsBonus2Flavor" value="“当多个方舟世界的战争领主和先知分享同一个愿景—即未来—的时候, 他们和彼此所在的方舟世界便成为了一个整体. 他们会竭尽所有的力量, 以实现对未来的承诺.”<br/>  — 演讲手稿, 吉格蒙 德尔, 侠盗商人及业余异种学家"/>
	<entry name="Eldar/CityTier2" value="<string name='Traits/Eldar/CityTier2'/>"/>
	<entry name="Eldar/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Eldar/CityTier2Flavor" value="<string name='Traits/Eldar/CityTier2Flavor'/>"/>
	<entry name="Eldar/CityTier3" value="<string name='Traits/Eldar/CityTier3'/>"/>
	<entry name="Eldar/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Eldar/CityTier3Flavor" value="<string name='Traits/Eldar/CityTier3Flavor'/>"/>
	<entry name="Eldar/CleansingFlame" value="<string name='Actions/CleansingFlame'/>"/>
	<entry name="Eldar/CleansingFlameDescription" value="术士将能够使用灵能烈焰轰击临近的敌人."/>
	<entry name="Eldar/CleansingFlameFlavor" value="<string name='Actions/CleansingFlameFlavor'/>"/>
	<entry name="Eldar/ConstructionBuildingBonus" value="<string name='Traits/Eldar/ConstructionBuildingBonus'/>"/>
	<entry name="Eldar/ConstructionBuildingBonusDescription" value="提高吟骨者祈堂的生产产出."/>
	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="<string name='Traits/Eldar/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Eldar/CrackShot" value="<string name='Traits/Eldar/CrackShot'/>"/>
	<entry name="Eldar/CrackShotDescription" value="提高烈焰之龙的准确度和护甲穿透效果."/>
	<entry name="Eldar/CrackShotFlavor" value="<string name='Traits/Eldar/CrackShotFlavor'/>"/>
	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Traits/Eldar/CrystalTargetingMatrix'/>"/>
	<entry name="Eldar/CrystalTargetingMatrixDescription" value="使炎晶坦克, 大黄蜂, 海蛇运输艇和战争行者暂时提升其准确度."/>
	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Traits/Eldar/CrystalTargetingMatrixFlavor'/>"/>
	<entry name="Eldar/Dominate" value="<string name='Actions/Dominate'/>"/>
	<entry name="Eldar/DominateDescription" value="使毒堇冥灵战机能够眩晕除载具和堡垒以外的敌方单位."/>
	<entry name="Eldar/DominateFlavor" value="<string name='Actions/DominateFlavor'/>"/>
	<entry name="Eldar/ExpertHunter" value="<string name='Traits/Eldar/ExpertHunter'/>"/>
	<entry name="Eldar/ExpertHunterDescription" value="提高闪耀之矛对巨兽生物, 载具和堡垒单位的伤害."/>
	<entry name="Eldar/ExpertHunterFlavor" value="<string name='Traits/Eldar/ExpertHunterFlavor'/>"/>
	<entry name="Eldar/ExtraInfantryArmour" value="迷踪网状护甲"/>
	<entry name="Eldar/ExtraInfantryArmourDescription" value="提高步兵的护甲."/>
	<entry name="Eldar/ExtraInfantryArmourFlavor" value="艾达灵族的热等离子网状护甲并非只着眼于防御而设计—它还能够让使用者自由地在战场上移动. 然而, 通过进一步扩展其复杂的耗散协议, 该护甲还能够在不损失移动性的情况下进一步提高其韧性."/>
	<entry name="Eldar/ExtraVehicleArmour" value="灵骨灌注"/>
	<entry name="Eldar/ExtraVehicleArmourDescription" value="提高载具的护甲."/>
	<entry name="Eldar/ExtraVehicleArmourFlavor" value="所有的艾达灵族载具和建筑都是从'吟唱'中诞生, 吟骨者的声音和灵能范围所带来的建造能力恰好与狂嚎女巫战吼的毁灭能力相反. 然而, 随着格雷迪厄斯的资源与和平的改变, 吟骨者能够将更大的能量灌注至其造物之中, 从而进一步提高其韧性."/>
	<entry name="Eldar/FoodBuildingBonus" value="<string name='Traits/Eldar/FoodBuildingBonus'/>"/>
	<entry name="Eldar/FoodBuildingBonusDescription" value="提高艾莎花园的食物产出."/>
	<entry name="Eldar/FoodBuildingBonusFlavor" value="<string name='Traits/Eldar/FoodBuildingBonusFlavor'/>"/>
	<entry name="Eldar/GhostwalkMatrix" value="幽魂步矩阵"/>
	<entry name="Eldar/GhostwalkMatrixDescription" value="允许炎晶坦克, 大黄蜂, 海蛇运输艇和战争行者越过掩体."/>
	<entry name="Eldar/GhostwalkMatrixFlavor" value="幽魂步矩阵利用灵魂之石所蕴含的知识和智慧以引导载具的前进."/>
	<entry name="Eldar/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Eldar/HammerOfWrathDescription" value="使闪耀之矛, 先知御空者, 卡恩化身, 战争行者, 幽冥领主和冥灵骑士施展更具毁灭性攻击的能力."/>
	<entry name="Eldar/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Eldar/HeavyWeaponBonus" value="<string name='Traits/Eldar/HeavyWeaponBonus'/>"/>
	<entry name="Eldar/HeavyWeaponBonusDescription" value="提高重型武器的护甲穿透效果."/>
	<entry name="Eldar/HeavyWeaponBonusFlavor" value="<string name='Traits/Eldar/HeavyWeaponBonusFlavor'/>"/>
	<entry name="Eldar/HoloFields" value="<string name='Traits/Eldar/HoloFields'/>"/>
	<entry name="Eldar/HoloFieldsDescription" value="使炎晶坦克, 大黄蜂和海蛇运输艇在移动后获得无敌伤害减免效果."/>
	<entry name="Eldar/HoloFieldsFlavor" value="<string name='Traits/Eldar/HoloFieldsFlavor'/>"/>
	<entry name="Eldar/InfantryBuildingBonus" value="<string name='Traits/Eldar/InfantryBuildingBonus'/>"/>
	<entry name="Eldar/InfantryBuildingBonusDescription" value="提高阿苏焉坩埚的生产产出."/>
	<entry name="Eldar/InfantryBuildingBonusFlavor" value="<string name='Traits/Eldar/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Eldar/InfluenceBuildingBonus" value="<string name='Traits/Eldar/InfluenceBuildingBonus'/>"/>
	<entry name="Eldar/InfluenceBuildingBonusDescription" value="提高先知穹顶的影响力产出."/>
	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="<string name='Traits/Eldar/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Eldar/MarksmansEye" value="<string name='Traits/Eldar/MarksmansEye'/>"/>
	<entry name="Eldar/MarksmansEyeDescription" value="提高绯红猎手的准确度."/>
	<entry name="Eldar/MarksmansEyeFlavor" value="<string name='Traits/Eldar/MarksmansEyeFlavor'/>"/>
	<entry name="Eldar/MeleeWeaponBonus" value="<string name='Traits/Eldar/MeleeWeaponBonus'/>"/>
	<entry name="Eldar/MeleeWeaponBonusDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="Eldar/MeleeWeaponBonusFlavor" value="<string name='Traits/Eldar/MeleeWeaponBonusFlavor'/>"/>
	<entry name="Eldar/OreBuildingBonus" value="<string name='Traits/Eldar/OreBuildingBonus'/>"/>
	<entry name="Eldar/OreBuildingBonusDescription" value="提高瓦尔祭坛的矿石产出."/>
	<entry name="Eldar/OreBuildingBonusFlavor" value="<string name='Traits/Eldar/OreBuildingBonusFlavor'/>"/>
	<entry name="Eldar/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
	<entry name="Eldar/PlasmaGrenadeDescription" value="使守护者获得投掷多用途手雷的能力."/>
	<entry name="Eldar/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
	<entry name="Eldar/ResearchBuildingBonus" value="<string name='Traits/Eldar/ResearchBuildingBonus'/>"/>
	<entry name="Eldar/ResearchBuildingBonusDescription" value="提高灵魂骨瓮的研究产出."/>
	<entry name="Eldar/ResearchBuildingBonusFlavor" value="<string name='Traits/Eldar/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Eldar/SpiritPreservationBonus" value="孔罩底座"/>
	<entry name="Eldar/SpiritPreservationBonusDescription" value="当艾达灵族单位死亡时提高获得的能量."/>
	<entry name="Eldar/SpiritPreservationBonusFlavor" value="出于时间的考虑, 我们将灵魂之石安装在简单的外壳中, 相信其持有者能够很好地保护它们. 然而, 根据我们在格雷迪厄斯所发现的远古网道结构表明, 我们可以在任何灵骨外壳中设计复杂的防护罩, 从而更好地避免大量战士的死亡."/>
	<entry name="Eldar/SpiritStones" value="<string name='Traits/Eldar/SpiritStones'/>"/>
	<entry name="Eldar/SpiritStonesDescription" value="降低炎晶坦克, 大黄蜂, 战争行者和海蛇运输艇的士气损失."/>
	<entry name="Eldar/SpiritStonesFlavor" value="<string name='Traits/Eldar/SpiritStonesFlavor'/>"/>
	<entry name="Eldar/StarEngines" value="<string name='Traits/Eldar/StarEngines'/>"/>
	<entry name="Eldar/StarEnginesDescription" value="提高炎晶坦克, 大黄蜂, 突击蝎, 战争行者和海蛇运输艇的移动."/>
	<entry name="Eldar/StarEnginesFlavor" value="<string name='Traits/Eldar/StarEnginesFlavor'/>"/>
	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
	<entry name="Eldar/TranscendentBlissDescription" value="使城市能够花费影响力以暂时提高忠诚度."/>
	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
	<entry name="Eldar/TranscendentBlissBonus" value="艾达灵族的诅咒"/>
	<entry name="Eldar/TranscendentBlissBonusDescription" value="提高从卓越福佑获得的忠诚度."/>
	<entry name="Eldar/TranscendentBlissBonusFlavor" value="“在一个不受束缚的艾达灵族心中, 既没有理智也没有疯狂, 平静而完美.”<br/>  — 纳拉迈 蒙格, 异形审判庭"/>
	<entry name="Eldar/VectoredEngines" value="<string name='Traits/Eldar/VectoredEngines'/>"/>
	<entry name="Eldar/VectoredEnginesDescription" value="使炎晶坦克, 大黄蜂, 战争行者, 海蛇运输艇和突击蝎在应对敌方远程武器时短暂提高护甲."/>
	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Traits/Eldar/VectoredEnginesFlavor'/>"/>
	<entry name="Eldar/VehicleBuildingBonus" value="<string name='Traits/Eldar/VehicleBuildingBonus'/>"/>
	<entry name="Eldar/VehicleBuildingBonusDescription" value="提高大型网道入口的生产产出."/>
	<entry name="Eldar/VehicleBuildingBonusFlavor" value="<string name='Traits/Eldar/VehicleBuildingBonusFlavor'/>"/>
	<entry name="Eldar/WarShout" value="<string name='Actions/Eldar/WarShout'/>"/>
	<entry name="Eldar/WarShoutDescription" value="使狂嚎女妖能够降低临近敌方单位的士气."/>
	<entry name="Eldar/WarShoutFlavor" value="<string name='Actions/Eldar/WarShoutFlavor'/>"/>
	<entry name="Eldar/WebwayGateBonus" value="网道制图学"/>
	<entry name="Eldar/WebwayGateBonusDescription" value="激活网道门不再有消耗."/>
	<entry name="Eldar/WebwayGateBonusFlavor" value="只有丑角剧团和黑暗图书馆的监护人才能够真正了解网道的跨纬度布局. 但通过将只属于这个世界的一部分知识赠与我们, 我们就能够再次打开这些古老的传送门."/>
	<entry name="Eldar/WebwayGateBonus2" value="网道约束"/>
	<entry name="Eldar/WebwayGateBonus2Description" value="网道穿越不再消耗动作点数."/>
	<entry name="Eldar/WebwayGateBonus2Flavor" value="在从黑暗图书馆获得了有关格雷迪厄斯网道布局的指导之后, 灵魂先知找到了两个传送门之间的最短路径, 以使艾达灵族能够更加迅速的移动."/>
	<entry name="Eldar/WebwayRedoubt" value="网道要塞"/>
	<entry name="Eldar/WebwayRedoubtDescription" value="在一处占领的网道门建立城市."/>
	<entry name="Eldar/WebwayRedoubtFlavor" value="<string name='Actions/Eldar/WebwayRedoubtFlavor'/>"/>
	<entry name="Eldar/WraithknightStarcannon" value="冥灵骑士星辰加农炮"/>
	<entry name="Eldar/WraithknightStarcannonDescription" value="使冥灵骑士能够使用星辰加农炮."/>
	<entry name="Eldar/WraithknightStarcannonFlavor" value="<string name='Weapons/StarcannonFlavor'/>"/>
	<entry name="Necrons/AircraftBuildingBonus" value="<string name='Traits/Necrons/AircraftBuildingBonus'/>"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="提高无名堤道的生产产出."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="<string name='Traits/Necrons/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Necrons/AttackCityBonus" value="<string name='Traits/Necrons/AttackCityBonus'/>"/>
	<entry name="Necrons/AttackCityBonusDescription" value="提高单位攻击城市内敌方单位的准确度."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="<string name='Traits/Necrons/AttackCityBonusFlavor'/>"/>
	<entry name="Necrons/BlastDamage" value="<string name='Traits/Necrons/BlastDamage'/>"/>
	<entry name="Necrons/BlastDamageDescription" value="提高爆破和模块武器的护甲穿透效果."/>
	<entry name="Necrons/BlastDamageFlavor" value="<string name='Traits/Necrons/BlastDamageFlavor'/>"/>
	<entry name="Necrons/CityDefenseBonus" value="无法跨越的障碍"/>
	<entry name="Necrons/CityDefenseBonusDescription" value="提高城市中单位的伤害减免效果."/>
	<entry name="Necrons/CityDefenseBonusFlavor" value="向墓穴之城发起的进攻神秘地失败了—墓穴高耸的巨石城墙让能量攻击消失, 重力武器偏转, 投射的飞弹也飞向了天空消失不见."/>
	<entry name="Necrons/CityTier2" value="<string name='Traits/Necrons/CityTier2'/>"/>
	<entry name="Necrons/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier2Flavor" value="<string name='Traits/Necrons/CityTier2Flavor'/>"/>
	<entry name="Necrons/CityTier3" value="<string name='Traits/Necrons/CityTier3'/>"/>
	<entry name="Necrons/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier3Flavor" value="<string name='Traits/Necrons/CityTier3Flavor'/>"/>
	<entry name="Necrons/ConstructionBuildingBonus" value="<string name='Traits/Necrons/ConstructionBuildingBonus'/>"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="提高奴隶石墓的生产产出."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="<string name='Traits/Necrons/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor" value="<string name='Actions/Necrons/DimensionalCorridor'/>"/>
	<entry name="Necrons/DimensionalCorridorDescription" value="使步兵单位获得传送至城市和符石巨塔的能力."/>
	<entry name="Necrons/DimensionalCorridorFlavor" value="<string name='Actions/Necrons/DimensionalCorridorFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor2" value="稳定纬度"/>
	<entry name="Necrons/DimensionalCorridor2Description" value="降低维度通道的影响力消耗."/>
	<entry name="Necrons/DimensionalCorridor2Flavor" value="利用古贤者所放弃的科技, 太空死灵能够强化它们的召唤技术, 极大地降低战士们通过永恒之门的消耗."/>
	<entry name="Necrons/DimensionalCorridor3" value="维度约束"/>
	<entry name="Necrons/DimensionalCorridor3Description" value="移除维度通道的动作和移动消耗."/>
	<entry name="Necrons/DimensionalCorridor3Flavor" value="在地宫技师的协助下, 太空死灵们精心调整了它们的召唤技术, 能够极大地提高获取的准确性."/>
	<entry name="Necrons/EnergyBuildingBonus" value="<string name='Traits/Necrons/EnergyBuildingBonus'/>"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="提高能量核心的能量产出."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="<string name='Traits/Necrons/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Necrons/ExtraInfantryArmourDescription" value="提高步兵单位的护甲."/>
	<entry name="Necrons/ExtraInfantryArmourFlavor" value="即使经历了千年的战争和沉眠, 太空死灵的地宫技师们也并没有失去对创新的渴望. 通过一些微小的调整, 它们依然能够提高抵抗现今武器的生存能力."/>
	<entry name="Necrons/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Necrons/ExtraVehicleArmourDescription" value="提高载具和墓穴单位的护甲."/>
	<entry name="Necrons/ExtraVehicleArmourFlavor" value="对于外界而言, 这些太空死灵的载具与它们祖先所使用的并没有太大区别. 然而对于地宫技师或者是拥有学识的艾达灵族来说, 它们能够发现载具材料拥有更高的弹性, 同时又没有影响其密度或重量."/>
	<entry name="Necrons/GaussDamage" value="<string name='Traits/GaussDamage'/>"/>
	<entry name="Necrons/GaussDamageDescription" value="提高高斯武器的护甲穿透效果."/>
	<entry name="Necrons/GaussDamageFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="Necrons/GaussPylon" value="<string name='Units/Necrons/GaussPylon'/>"/>
	<entry name="Necrons/GaussPylonDescription" value="使城市能够建造强大的高斯堡垒."/>
	<entry name="Necrons/GaussPylonFlavor" value="<string name='Units/Necrons/GaussPylonFlavor'/>"/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="使墓穴蜘蛛及其临近的友方单位获得巫火伤害减免效果."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GrowthBonus" value="<string name='Traits/Necrons/GrowthBonus'/>"/>
	<entry name="Necrons/GrowthBonusDescription" value="提高太空死灵城市的增长率."/>
	<entry name="Necrons/GrowthBonusFlavor" value="<string name='Traits/Necrons/GrowthBonusFlavor'/>"/>
	<entry name="Necrons/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Necrons/HammerOfWrathDescription" value="使毁灭领主, 墓穴之刃, 墓穴蜘蛛, 超然星神和三圣卫队获得施展更具破坏性攻击的能力."/>
	<entry name="Necrons/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Necrons/HousingBuildingBonus" value="<string name='Traits/Necrons/HousingBuildingBonus'/>"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="提高庇护所的人口上限."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="<string name='Traits/Necrons/HousingBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfantryBuildingBonus" value="<string name='Traits/Necrons/InfantryBuildingBonus'/>"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="提高召唤核心的生产产出."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="<string name='Traits/Necrons/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfluenceBuildingBonus" value="<string name='Traits/Necrons/InfluenceBuildingBonus'/>"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="提高石碑的影响力产出."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="<string name='Traits/Necrons/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Necrons/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="Necrons/LivingMetal2" value="永生形态"/>
	<entry name="Necrons/LivingMetal2Description" value="提高活体金属的治疗."/>
	<entry name="Necrons/LivingMetal2Flavor" value="从某种深奥的角度来看, 太空死灵的机器似乎能够记住昔日的荣光并不断尝试着重现当时的辉煌. 它们的活体金属能够使任何损伤迅速修复."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="<string name='Traits/Necrons/LoyaltyBuildingBonus'/>"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="提高巴洛克神殿的忠诚度产出."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="<string name='Traits/Necrons/LoyaltyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/MeleeDamage" value="<string name='Traits/Necrons/MeleeDamage'/>"/>
	<entry name="Necrons/MeleeDamageDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="Necrons/MeleeDamageFlavor" value="<string name='Traits/Necrons/MeleeDamageFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="<string name='Traits/Necrons/Nebuloscope'/>"/>
	<entry name="Necrons/NebuloscopeDescription" value="允许墓穴之刃无视远程伤害减免效果."/>
	<entry name="Necrons/NebuloscopeFlavor" value="<string name='Traits/Necrons/NebuloscopeFlavor'/>"/>
	<entry name="Necrons/NecrodermisRepair2" value="加速再增长"/>
	<entry name="Necrons/NecrodermisRepair2Description" value="提高骸体修复恢复的生命值."/>
	<entry name="Necrons/NecrodermisRepair2Flavor" value="地宫技师再次改进了太空死灵的活体金属结构, 使得其能够从几乎任何伤害中恢复过来."/>
	<entry name="Necrons/NecrodermisRepair3" value="恐怖骸体"/>
	<entry name="Necrons/NecrodermisRepair3Description" value="移除骸体修复的冷却时间."/>
	<entry name="Necrons/NecrodermisRepair3Flavor" value="组成太空死灵躯体和载具的活体金属不断翻腾着, 时刻都在进行自我重建."/>
	<entry name="Necrons/OreBuildingBonus" value="<string name='Traits/Necrons/OreBuildingBonus'/>"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="提高炼金采石场的矿石产出."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="<string name='Traits/Necrons/OreBuildingBonusFlavor'/>"/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="提高歼灭者炮艇, 鬼灵方舟, 末日方舟以及三圣追猎者的无敌伤害减免效果, 如果受到伤害则在下一轮开始时进入冷却时间."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/RapidRiseBonus" value="领主之令"/>
	<entry name="Necrons/RapidRiseBonusDescription" value="降低快速增长的消耗."/>
	<entry name="Necrons/RapidRiseBonusFlavor" value="领主强化了协议, 他的命令将在没有思考的情况下以更快的速度执行—如果会有人思考的话."/>
	<entry name="Necrons/ReanimationProtocols2" value="能效鼓舞协议"/>
	<entry name="Necrons/ReanimationProtocols2Description" value="提高鼓舞协议的治疗效果."/>
	<entry name="Necrons/ReanimationProtocols2Flavor" value="凭借着这些地宫技师所创造的修理和再生系统, 太空死灵现在能够更加轻松地摆脱看似致命的伤害."/>
	<entry name="Necrons/ResearchBuildingBonus" value="<string name='Traits/Necrons/ResearchBuildingBonus'/>"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="提高禁忌档案室的研究产出."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="<string name='Traits/Necrons/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ScarabHive" value="<string name='Actions/Necrons/ScarabHive'/>"/>
	<entry name="Necrons/ScarabHiveDescription" value="使墓穴蜘蛛获得建造墓穴圣甲虫的能力."/>
	<entry name="Necrons/ScarabHiveFlavor" value="<string name='Actions/Necrons/ScarabHiveFlavor'/>"/>
	<entry name="Necrons/SeismicAssault" value="地震突袭"/>
	<entry name="Necrons/SeismicAssaultDescription" value="使超然星神和超正方体墓穴获得施展更具破坏性攻击的能力."/>
	<entry name="Necrons/SeismicAssaultFlavor" value="<string name='Weapons/SeismicAssaultTranscendentFlavor'/>"/>
	<entry name="Necrons/ShieldVane" value="<string name='Traits/Necrons/ShieldVane'/>"/>
	<entry name="Necrons/ShieldVaneDescription" value="提高墓穴之刃的护甲."/>
	<entry name="Necrons/ShieldVaneFlavor" value="<string name='Traits/Necrons/ShieldVaneFlavor'/>"/>
	<entry name="Necrons/TeslaDamage" value="<string name='Traits/TeslaDamage'/>"/>
	<entry name="Necrons/TeslaDamageDescription" value="提高特斯拉武器的护甲穿透效果."/>
	<entry name="Necrons/TeslaDamageFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="Necrons/TheBoundCoalescent" value="<string name='Actions/Necrons/TheBoundCoalescent'/>"/>
	<entry name="Necrons/TheBoundCoalescentDescription" value="使超然星神获得与方尖碑合体, 形成超正方体墓穴的能力."/>
	<entry name="Necrons/TheBoundCoalescentFlavor" value="<string name='Actions/Necrons/TheBoundCoalescentFlavor'/>"/>
	<entry name="Necrons/VehiclesBuildingBonus" value="<string name='Traits/Necrons/VehiclesBuildingBonus'/>"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="提高多柱神庙的生产产出."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="<string name='Traits/Necrons/VehiclesBuildingBonusFlavor'/>"/>
	<entry name="Orks/AmmoRunt" value="<string name='Actions/AmmoRunt'/>"/>
	<entry name="Orks/AmmoRuntDescription" value="提高大技师, 怪枪小子和屁精大炮的远程准确度."/>
	<entry name="Orks/AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Orks/BattlewagonBigShootas" value="战斗堡垒大机枪"/>
	<entry name="Orks/BattlewagonBigShootasDescription" value="使战斗堡垒获得大机枪."/>
	<entry name="Orks/BattlewagonBigShootasFlavor" value="<string name='Weapons/BigShootaFlavor'/>"/>
	<entry name="Orks/BattlewagonRokkitLaunchas" value="战斗堡垒火箭发射器"/>
	<entry name="Orks/BattlewagonRokkitLaunchasDescription" value="使战斗堡垒获得火箭发射器."/>
	<entry name="Orks/BattlewagonRokkitLaunchasFlavor" value="<string name='Weapons/RokkitLaunchaFlavor'/>"/>
	<entry name="Orks/Bigbomm" value="<string name='Weapons/Bigbomm'/>"/>
	<entry name="Orks/BigbommDescription" value="使兽人直升机获得投掷反步兵炸弹的能力."/>
	<entry name="Orks/BigbommFlavor" value="<string name='Weapons/BigbommFlavor'/>"/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/Orks/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="提高手雷, 导弹和爆破武器的护甲穿透效果."/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/Orks/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="<string name='Traits/Orks/BoltDamage'/>"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="<string name='Traits/Orks/BoltDamageFlavor'/>"/>
	<entry name="Orks/BonusBeastsProduction" value="<string name='Traits/Orks/BonusBeastsProduction'/>"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="<string name='Traits/Orks/BonusBeastsProductionDescription'/>"/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="<string name='Traits/Orks/BonusBeastsProductionFlavor'/>"/>
	<entry name="Orks/BonusColonizersProduction" value="<string name='Traits/Orks/BonusColonizersProduction'/>"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="<string name='Traits/Orks/BonusColonizersProductionDescription'/>"/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="<string name='Traits/Orks/BonusColonizersProductionFlavor'/>"/>
	<entry name="Orks/BonusInfantryProduction" value="<string name='Traits/Orks/BonusInfantryProduction'/>"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="<string name='Traits/Orks/BonusInfantryProductionDescription'/>"/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="<string name='Traits/Orks/BonusInfantryProductionFlavor'/>"/>
	<entry name="Orks/BonusVehiclesProduction" value="<string name='Traits/Orks/BonusVehiclesProduction'/>"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="<string name='Traits/Orks/BonusVehiclesProductionDescription'/>"/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="<string name='Traits/Orks/BonusVehiclesProductionFlavor'/>"/>
	<entry name="Orks/Bosspole" value="兽人旗帜"/>
	<entry name="Orks/BosspoleDescription" value="降低大技师, 兽人小子, 超重装老大, 坦克破坏者, 兽人摩托以及战争首领的士气损失."/>
	<entry name="Orks/BosspoleFlavor" value="欧克兽人老大经常带着一只奖杯杆, 并且希望它不会被弄乱. 拥有兽人旗帜的老大经常发现, 如果需要从战斗中恢复某些秩序, 那么它会派上很大的作用"/>
	<entry name="Orks/CityEnergy" value="<string name='Traits/Orks/CityEnergy'/>"/>
	<entry name="Orks/CityEnergyDescription" value="提高欧克兽人城市的能量产出."/>
	<entry name="Orks/CityEnergyFlavor" value="<string name='Traits/Orks/CityEnergyFlavor'/>"/>
	<entry name="Orks/CityGrowth" value="<string name='Traits/Orks/CityGrowth'/>"/>
	<entry name="Orks/CityGrowthDescription" value="<string name='Traits/Orks/CityGrowthDescription'/>"/>
	<entry name="Orks/CityGrowthFlavor" value="<string name='Traits/Orks/CityGrowthFlavor'/>"/>
	<entry name="Orks/CityInfluence" value="<string name='Traits/Orks/CityInfluence'/>"/>
	<entry name="Orks/CityInfluenceDescription" value="提高欧克兽人城市的影响力产出."/>
	<entry name="Orks/CityInfluenceFlavor" value="<string name='Traits/Orks/CityInfluenceFlavor'/>"/>
	<entry name="Orks/CityLoyalty" value="<string name='Traits/Orks/CityLoyalty'/>"/>
	<entry name="Orks/CityLoyaltyDescription" value="提高欧克兽人城市的忠诚度产出."/>
	<entry name="Orks/CityLoyaltyFlavor" value="<string name='Traits/Orks/CityLoyaltyFlavor'/>"/>
	<entry name="Orks/CityPopulationLimit" value="<string name='Traits/Orks/CityPopulationLimit'/>"/>
	<entry name="Orks/CityPopulationLimitDescription" value="提高欧克兽人城市的人口上限."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="<string name='Traits/Orks/CityPopulationLimitFlavor'/>"/>
	<entry name="Orks/CityResearch" value="<string name='Traits/Orks/CityResearch'/>"/>
	<entry name="Orks/CityResearchDescription" value="提高欧克兽人城市的研究产出."/>
	<entry name="Orks/CityResearchFlavor" value="<string name='Traits/Orks/CityResearchFlavor'/>"/>
	<entry name="Orks/CityTier2" value="<string name='Traits/Orks/CityTier2'/>"/>
	<entry name="Orks/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier2Flavor" value="<string name='Traits/Orks/CityTier2Flavor'/>"/>
	<entry name="Orks/CityTier3" value="<string name='Traits/Orks/CityTier3'/>"/>
	<entry name="Orks/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier3Flavor" value="<string name='Traits/Orks/CityTier3Flavor'/>"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeath" value="永久腐化"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathDescription" value="使单位在死亡后创造出永久性的兽人真菌."/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathFlavor" value="<string name='Traits/Orks/CreateOrkoidFungusOnDeathFlavor'/>"/>
	<entry name="Orks/DakkajetSupaShoota" value="大枪攻击机超级机枪"/>
	<entry name="Orks/DakkajetSupaShootaDescription" value="使大枪攻击机获得一座超级机枪."/>
	<entry name="Orks/DakkajetSupaShootaFlavor" value="<string name='Weapons/TwinLinkedSupaShootaFlavor'/>"/>
	<entry name="Orks/EavyArmour" value="<string name='Traits/EavyArmour'/>"/>
	<entry name="Orks/EavyArmourDescription" value="提高兽人小子和战争首领的护甲."/>
	<entry name="Orks/EavyArmourFlavor" value="<string name='Traits/EavyArmourFlavor'/>"/>
	<entry name="Orks/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Orks/ExtraInfantryArmourDescription" value="提高步兵单位的护甲."/>
	<entry name="Orks/ExtraInfantryArmourFlavor" value="对于其他种族来说, 欧克兽人们的护甲看上去非常荒谬: 巨大的厚重金属板上装置着各种电缆, 钉子以及其他的设备, 愚笨的兽人技师和屁精还会为其涂上金色. 但欧克兽人的信仰却认为, 这东西充满了魅力."/>
	<entry name="Orks/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Orks/ExtraVehicleArmourDescription" value="提高载具单位的护甲."/>
	<entry name="Orks/ExtraVehicleArmourFlavor" value="当一名兽人技师面对着一位战争首领, 因为它的战车不断被炸毁而感到十分愤怒时, 兽人技师会迅速命令屁精将不知道从哪里挖出的废料作为额外护甲安装在载具上. 毕竟它知道这些高速移动的东西很快就会玩完."/>
	<entry name="Orks/Flyboss" value="<string name='Traits/Orks/Flyboss'/>"/>
	<entry name="Orks/FlybossDescription" value="提高大枪攻击机对飞行器, 喷气摩托以及掠行艇单位的远程准确度."/>
	<entry name="Orks/FlybossFlavor" value="<string name='Traits/Orks/FlybossFlavor'/>"/>
	<entry name="Orks/GrabbinKlaw" value="<string name='Actions/GrabbinKlaw'/>"/>
	<entry name="Orks/GrabbinKlawDescription" value="使战斗堡垒获得让敌方地面载具单位无法移动的能力."/>
	<entry name="Orks/GrabbinKlawFlavor" value="<string name='Actions/GrabbinKlawFlavor'/>"/>
	<entry name="Orks/GrotRiggers" value="屁精操作员"/>
	<entry name="Orks/GrotRiggersDescription" value="使战斗越野车, 杀人罐头, 战斗堡垒, 兽人大铁罐, 搞哥机甲以及兽人坦克获得被动生命恢复的能力."/>
	<entry name="Orks/GrotRiggersFlavor" value="<string name='Traits/GrotRiggersFlavor'/>"/>
	<entry name="Orks/HealingRate" value="绿色洪流"/>
	<entry name="Orks/HealingRateDescription" value="提高单位的治疗速率."/>
	<entry name="Orks/HealingRateFlavor" value="在兽人监工和兽人医师的合作下, 你的头领开始了一项全新的, 更加'兽人'的饮食计划, 能够让兽人小子变得更加更大, 更快, 更强. 至于早餐冰冷的史奎格油脂还是晚餐的食面者史奎格到底有什么作用已经不重要了—关键是兽人小子们相信, 那就行了."/>
	<entry name="Orks/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Orks/HammerOfWrathDescription" value="使兽人大铁罐, 兽人直升机, 史格古巨兽, 搞哥机甲, 杀人罐头和兽人摩托获得施展更具破坏性攻击的能力."/>
	<entry name="Orks/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="<string name='Traits/Orks/MeleeDamage'/>"/>
	<entry name="Orks/MeleeDamageDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="Orks/MeleeDamageFlavor" value="<string name='Traits/Orks/MeleeDamageFlavor'/>"/>
	<entry name="Orks/MightMakesRight2" value="恐怖兽人!"/>
	<entry name="Orks/MightMakesRight2Description" value="提高单位造成伤害时获得的影响力."/>
	<entry name="Orks/MightMakesRight2Flavor" value="当哇! 足够强大时, 它能够出现类似的增幅效应, 每一次打击都能够强化泄漏出的灵能场, 形成了一种极具兽人风格的超暴力回环效果."/>
	<entry name="Orks/OrkoidFungusBonusHealingRate" value="<string name='Traits/OrkoidFungusBonusHealingRate'/>"/>
	<entry name="Orks/OrkoidFungusBonusHealingRateDescription" value="提高受到来自兽人真菌的治疗效果."/>
	<entry name="Orks/OrkoidFungusBonusHealingRateFlavor" value="<string name='Traits/OrkoidFungusBonusHealingRateFlavor'/>"/>
	<entry name="Orks/OrkoidFungusFood" value="<string name='Traits/OrkoidFungusFood'/>"/>
	<entry name="Orks/OrkoidFungusFoodDescription" value="提高拥有兽人真菌地点的食物产出."/>
	<entry name="Orks/OrkoidFungusFoodFlavor" value="<string name='Traits/OrkoidFungusFoodFlavor'/>"/>
	<entry name="Orks/RedPaintJob" value="红色涂装"/>
	<entry name="Orks/RedPaintJobDescription" value="提高战斗越野车, 竞速火箭车, 战斗堡垒, 大枪攻击机和焚燃轰炸机的伤害."/>
	<entry name="Orks/RedPaintJobFlavor" value="<string name='Traits/RedPaintJobFlavor'/>"/>
	<entry name="Orks/Scavenger2" value="屁精拾荒者"/>
	<entry name="Orks/Scavenger2Description" value="提高击杀敌方单位所回收的矿石数量."/>
	<entry name="Orks/Scavenger2Flavor" value="有组织观念的兽人监工会训练它的屁精收集废料并进行分类, 同时避开那些危险的炮弹外壳和军需品."/>
	<entry name="Orks/SkorchaMissile" value="<string name='Weapons/SkorchaMissile'/>"/>
	<entry name="Orks/SkorchaMissileDescription" value="使焚燃轰炸机获得能够发射反步兵导弹的能力. 该导弹能够无视敌方的掩护."/>
	<entry name="Orks/SkorchaMissileFlavor" value="<string name='Weapons/SkorchaMissileFlavor'/>"/>
	<entry name="Orks/Stikkbomb" value="<string name='Weapons/Stikkbomb'/>"/>
	<entry name="Orks/StikkbombDescription" value="使步兵获得投掷反步兵手雷的能力."/>
	<entry name="Orks/StikkbombFlavor" value="<string name='Weapons/StikkbombFlavor'/>"/>
	<entry name="Orks/TankbustaBomb" value="<string name='Weapons/TankbustaBomb'/>"/>
	<entry name="Orks/TankbustaBombDescription" value="使坦克破坏者获得投掷反装甲炸弹的能力."/>
	<entry name="Orks/TankbustaBombFlavor" value="<string name='Weapons/TankbustaBombFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolter" value="额外重型爆矢枪"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterDescription" value="使献祭者, 驱魔者和苛罚者获得一个额外的重型爆矢枪."/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalSacredRite" value="双重信仰"/>
	<entry name="SistersOfBattle/AdditionalSacredRiteDescription" value="可以同时激活两个神圣仪式."/>
	<entry name="SistersOfBattle/AdditionalSacredRiteFlavor" value="“曾经, 我们的仪式合唱是独一无二的—以神圣之声进行独奏或和鸣. 今天, 我们将我们的赞美诗编织在一起, 以便我们能够以尽可能多的方式赞美帝皇.”<br/>  — 未知记忆者, 网道福音"/>
	<entry name="SistersOfBattle/AircraftsMissiles" value="飞行器导弹"/>
	<entry name="SistersOfBattle/AircraftsMissilesDescription" value="为闪电战机配备天击导弹, 复仇者突击战斗机配备地狱之击导弹."/>
	<entry name="SistersOfBattle/AircraftsMissilesFlavor" value="这些导弹能够使战斗机有效地打击各种目标; 地狱之击导弹用于针对装甲车辆, 天击导弹则用于敌人的空中力量."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagus'/>"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="提高谴罪机甲的护甲."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagusFlavor'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonus'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="提高突击武器的护甲穿透效果."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/AvengeTheMartyrs" value="殉道者复仇"/>
	<entry name="SistersOfBattle/AvengeTheMartyrsDescription" value="提高复仇狂热的士气损失减免效果."/>
	<entry name="SistersOfBattle/AvengeTheMartyrsFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/ChaffLauncher" value="诱饵弹发射器"/>
	<entry name="SistersOfBattle/ChaffLauncherDescription" value="使闪电战机和复仇者突击战斗机能够发射诱饵弹, 提高远程伤害减免效果."/>
	<entry name="SistersOfBattle/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfane" value="<string name='Actions/SistersOfBattle/ChaseTheProfane'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneDescription" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneDescription'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneFlavor" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="<string name='Traits/SistersOfBattle/CityGrowth'/>"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="提高城市的增长率."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="<string name='Traits/SistersOfBattle/CityGrowthFlavor'/>"/>
	<entry name="SistersOfBattle/CityTier2" value="<string name='Traits/SistersOfBattle/CityTier2'/>"/>
	<entry name="SistersOfBattle/CityTier2Description" value="提高主要城市能够占领的土地半径."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="<string name='Traits/SistersOfBattle/CityTier2Flavor'/>"/>
	<entry name="SistersOfBattle/CityTier3" value="<string name='Traits/SistersOfBattle/CityTier3'/>"/>
	<entry name="SistersOfBattle/CityTier3Description" value="提高主要城市能够占领的土地半径."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="<string name='Traits/SistersOfBattle/CityTier3Flavor'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaith" value="<string name='Actions/SistersOfBattle/ConvictionOfFaith'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithDescription" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithFlavor" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SistersOfBattle/DozerBladeDescription" value="降低献祭者, 驱魔者和苛罚者在森林和帝国废墟中的移动惩罚效果."/>
	<entry name="SistersOfBattle/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="<string name='Actions/SistersOfBattle/EternalCrusade'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="<string name='Actions/SistersOfBattle/EternalCrusadeDescription'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="<string name='Actions/SistersOfBattle/EternalCrusadeFlavor'/>"/>
	<entry name="SistersOfBattle/ExpertFighters" value="王牌战斗机"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="提高闪电战机和复仇者突击战斗机的准确度."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="<string name='Traits/SistersOfBattle/ExpertFightersFlavor'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmourDescription" value="提高步兵的护甲."/>
	<entry name="SistersOfBattle/ExtraInfantryArmourFlavor" value="修女们身穿着的动力装甲由厚重的陶瓷板制成, 与阿斯塔特战会兄弟们所穿的盔甲拥有相同的古老锻造系统. 它提供类似的装甲防护, 但放弃了更先进的支持系统和强化能力, 因为战斗姐妹不具备像星际战士那样能够直接与自己的装甲交互的能力."/>
	<entry name="SistersOfBattle/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SistersOfBattle/ExtraVehicleArmourDescription" value="提高载具的护甲."/>
	<entry name="SistersOfBattle/ExtraVehicleArmourFlavor" value="战斗修女的车辆是信仰的体现, 因此维护和保护它们所携带的遗物至关重要. 在拥有如此防护能力的情况下, 这些车辆将更轻松地协助武装修会前进."/>
	<entry name="SistersOfBattle/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SistersOfBattle/FragGrenadeDescription" value="使战斗姐妹, 修女长, 天神圣卫, 文书修女, 主天使, 医疗修女, 圣像旗手, 楷模战甲, 仇天使, 圣赛勒斯汀, 忏悔修女, 风天使投掷反步兵手雷的能力."/>
	<entry name="SistersOfBattle/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/HammerOfWrath" value="<string name='Traits/HammerOfWrath'/>"/>
	<entry name="SistersOfBattle/HammerOfWrathDescription" value="使塞拉斯图斯枪骑士, 谴罪机甲, 楷模战甲, 圣赛勒斯汀和风天使获得施展更具破坏性攻击的能力."/>
	<entry name="SistersOfBattle/HammerOfWrathFlavor" value="<string name='Traits/HammerOfWrathFlavor'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonus'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="提高重型武器护甲穿透效果."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SistersOfBattle/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SistersOfBattle/KrakGrenadeDescription" value="使战斗姐妹, 修女长, 文书修女, 主天使, 医疗修女, 圣像旗手, 仇天使, 忏悔修女和风天使获得投掷反装甲手雷的能力."/>
	<entry name="SistersOfBattle/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/LaudHailer" value="<string name='Traits/SistersOfBattle/LaudHailer'/>"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="使苛罚者, 驱魔者和献祭者获得光环, 允许临近处于动摇状态的单位施展信仰法令."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Traits/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Traits/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="使位于医疗修女单位临近位置的友方步兵单位获得无视痛苦伤害减免效果."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Traits/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonus'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SistersOfBattle/MeltaBombDescription" value="使战斗姐妹, 修女长, 文书修女, 主天使, 仇天使, 忏悔修女和风天使获得部署热熔炸弹的能力. 该炸弹特别适用于重型战车和堡垒单位."/>
	<entry name="SistersOfBattle/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SistersOfBattle/MinistorumIndoctrination" value="国教会训诫"/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationDescription" value="使闪电战机, 复仇者突击战斗机和塞拉斯图斯枪骑士获得殉道者之魂."/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationFlavor" value="“通过长期接触我们的仪式和祈祷, 我们幸运的盟友—帝国海军和少数幸存的帝国枪骑士开始参与我们的仪式并接受我们的信仰. 显然, 信仰是会传染的.”<br/>  — 未知记忆者, 网道福音"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisation'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationDescription'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationFlavor'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="<string name='Actions/SistersOfBattle/PurifyingRecitations'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsDescription'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsFlavor'/>"/>
	<entry name="SistersOfBattle/RagingFervour" value="<string name='Actions/SistersOfBattle/RagingFervour'/>"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="<string name='Actions/SistersOfBattle/RagingFervourDescription'/>"/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="<string name='Actions/SistersOfBattle/RagingFervourFlavor'/>"/>
	<entry name="SistersOfBattle/RitualizedCeremonies" value="标准仪式"/>
	<entry name="SistersOfBattle/RitualizedCeremoniesDescription" value="降低神圣仪式的消耗."/>
	<entry name="SistersOfBattle/RitualizedCeremoniesFlavor" value="“起初, 我们一边前进一边祈祷, 无论是在废墟或是洞穴之中. 随着冲突逐渐转变为无休止的激烈战争, 我们的仪式和祈祷也稳定了下来, 形成了一种制度和惯例.”<br/>  — 未知记忆者, 网道福音"/>
	<entry name="SistersOfBattle/SacralVigor" value="<string name='Actions/SistersOfBattle/SacralVigor'/>"/>
	<entry name="SistersOfBattle/SacralVigorDescription" value="<string name='Actions/SistersOfBattle/SacralVigorDescription'/>"/>
	<entry name="SistersOfBattle/SacralVigorFlavor" value="<string name='Actions/SistersOfBattle/SacralVigorFlavor'/>"/>
	<entry name="SistersOfBattle/SanctifiedWorld" value="圣化世界"/>
	<entry name="SistersOfBattle/SanctifiedWorldDescription" value="提高信仰修道院的忠诚度加成."/>
	<entry name="SistersOfBattle/SanctifiedWorldFlavor" value="大多数战会将帝皇的光辉传播到远离他们圣地的世界, 建立了广泛的传教附属教堂, 以扩大他们修会和国教会的影响力."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="<string name='Traits/SistersOfBattle/SimulacrumImperialis'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisDescription'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisFlavor'/>"/>
	<entry name="SistersOfBattle/SisterSuperior" value="<string name='Traits/SistersOfBattle/SisterSuperior'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="<string name='Traits/SistersOfBattle/SisterSuperiorDescription'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="<string name='Traits/SistersOfBattle/SisterSuperiorFlavor'/>"/>
	<entry name="SistersOfBattle/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SistersOfBattle/UniversalCatechism" value="普适教理"/>
	<entry name="SistersOfBattle/UniversalCatechismDescription" value="使闪电战机, 复仇者突击战斗机和塞拉斯图斯枪骑士获得信仰之盾."/>
	<entry name="SistersOfBattle/UniversalCatechismFlavor" value="为信仰之战而征召的帝国部队往往最终与战斗修女们并肩祈祷, 在他们的信念中寻求指引."/>
	<entry name="SistersOfBattle/VengefulSpirit" value="<string name='Actions/SistersOfBattle/VengefulSpirit'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="<string name='Actions/SistersOfBattle/VengefulSpiritDescription'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="<string name='Actions/SistersOfBattle/VengefulSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrder" value="武装修会之誓"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderDescription" value="处于破碎状态时单位依然能够保留其信仰之盾."/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderFlavor" value="<string name='Traits/SistersOfBattle/ShieldOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="<string name='Actions/SistersOfBattle/WarmachinesWrath'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="<string name='Actions/SistersOfBattle/WarmachinesWrathDescription'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="<string name='Actions/SistersOfBattle/WarmachinesWrathFlavor'/>"/>
	<entry name="SpaceMarines/AssaultDoctrine" value="<string name='Traits/AssaultDoctrine'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineDescription" value="<string name='Actions/AssaultDoctrineDescription'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineFlavor" value="<string name='Traits/AssaultDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/SpaceMarines/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="提高手雷, 导弹和爆破武器的护甲穿透效果."/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/SpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="SpaceMarines/BolsterDefencesDescription" value="使雷霆之火加农炮获得提供远程伤害减免效果的能力."/>
	<entry name="SpaceMarines/BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/SpaceMarines/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/SpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDrill" value="<string name='Traits/BolterDrill'/>"/>
	<entry name="SpaceMarines/BolterDrillDescription" value="<string name='Actions/BolterDrillDescription'/>"/>
	<entry name="SpaceMarines/BolterDrillFlavor" value="<string name='Traits/BolterDrillFlavor'/>"/>
	<entry name="SpaceMarines/CeramitePlating" value="<string name='Traits/CeramitePlating'/>"/>
	<entry name="SpaceMarines/CeramitePlatingDescription" value="提高风暴渡鸦炮艇和风暴之爪炮艇的护甲."/>
	<entry name="SpaceMarines/CeramitePlatingFlavor" value="<string name='Traits/CeramitePlatingFlavor'/>"/>
	<entry name="SpaceMarines/ChapterUnity" value="<string name='Traits/ChapterUnity'/>"/>
	<entry name="SpaceMarines/ChapterUnityDescription" value="提高大礼堂的忠诚度产出."/>
	<entry name="SpaceMarines/ChapterUnityFlavor" value="<string name='Traits/ChapterUnityFlavor'/>"/>
	<entry name="SpaceMarines/CityTier2" value="<string name='Traits/SpaceMarines/CityTier2'/>"/>
	<entry name="SpaceMarines/CityTier2Description" value="提高城市能够占领的土地半径."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="<string name='Traits/SpaceMarines/CityTier2Flavor'/>"/>
	<entry name="SpaceMarines/CityTier3" value="<string name='Traits/SpaceMarines/CityTier3'/>"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="<string name='Traits/SpaceMarines/CityTier3Flavor'/>"/>
	<entry name="SpaceMarines/CityTier4" value="<string name='Traits/SpaceMarines/CityTier4'/>"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="<string name='Traits/SpaceMarines/CityTier4Flavor'/>"/>
	<entry name="SpaceMarines/ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="SpaceMarines/ClusterMinesDescription" value="使摩托侦察兵获得放置集束地雷的能力."/>
	<entry name="SpaceMarines/ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="SpaceMarines/CombatShield" value="<string name='Traits/CombatShield'/>"/>
	<entry name="SpaceMarines/CombatShieldDescription" value="提高突击者小队的伤害减免效果."/>
	<entry name="SpaceMarines/CombatShieldFlavor" value="<string name='Traits/CombatShieldFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrine" value="<string name='Traits/DevastatorDoctrine'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineDescription" value="<string name='Actions/DevastatorDoctrineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineFlavor" value="<string name='Traits/DevastatorDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SpaceMarines/DozerBladeDescription" value="降低猎手, 掠食者, 剃刀鲸和旋风火箭炮在森林和帝国废墟中的移动惩罚效果."/>
	<entry name="SpaceMarines/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourFlavor" value="尽管纯粹的信念能够让阿斯塔特战会在战场上坚持更长时间, 但也并不能阻挡所有的爆矢武器. 通过对盔甲的升级, 星际战士们的生存能力得到了极大提高."/>
	<entry name="SpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraVehicleArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourFlavor" value="当祝福降临之时, 技术军士将进行一些微小的改装, 正如在阿斯塔特法典中所记录的那样, 以提高生存性能."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="要塞护盾"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="使赎罪要塞获得无敌伤害减免效果."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="<string name='Traits/SpaceMarines/FortressOfRedemptionDamageReductionFlavor'/>"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSilo" value="赎罪要塞导弹发射井"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloDescription" value="使赎罪要塞获得碎裂风暴导弹发射井."/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloFlavor" value="<string name='Weapons/KrakstormMissileSiloFlavor'/>"/>
	<entry name="SpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SpaceMarines/FragGrenadeDescription" value="使药剂师, 突击者小队, 连长, 毁灭者小队, 智库, 侦察兵, 摩托侦察兵, 战术小队和雷霆之火加农炮获得投掷反步兵手雷的能力."/>
	<entry name="SpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="SpaceMarines/HammerOfWrathDescription" value="使突击者小队, 无畏机甲和摩托侦察兵获得施展更具破坏性攻击的能力."/>
	<entry name="SpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="SpaceMarines/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SpaceMarines/HurricaneBolter" value="飓风爆矢枪"/>
	<entry name="SpaceMarines/HurricaneBolterDescription" value="使风暴渡鸦炮艇获得飓风爆矢枪."/>
	<entry name="SpaceMarines/HurricaneBolterFlavor" value="飓风爆矢枪由黑暗圣堂战团率先使用, 它将多个双联爆矢枪结合在一起, 创造出了真正狂风暴雨般的凶残武器."/>
	<entry name="SpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SpaceMarines/KrakGrenadeDescription" value="使药剂师, 突击者小队, 连长, 毁灭者小队, 智库, 侦察兵, 摩托侦察兵, 战术小队和雷霆之火加农炮获得投掷反装甲手雷的能力."/>
	<entry name="SpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMelta" value="<string name='Weapons/MultiMelta'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaDescription" value="使兰德速攻艇和兰德袭击者获得一挺多重热熔枪."/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaFlavor" value="<string name='Weapons/MultiMeltaFlavor'/>"/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/SpaceMarines/LasDamage'/>"/>
	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Upgrades/AstraMilitarum/LasDamageDescription'/>"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/SpaceMarines/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/LastStand" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LastStandDescription" value="提高所有星际战士单位的士气."/>
	<entry name="SpaceMarines/LastStandFlavor" value="<string name='Traits/LastStandFlavor'/>"/>
	<entry name="SpaceMarines/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LocatorBeacon" value="<string name='Traits/LocatorBeacon'/>"/>
	<entry name="SpaceMarines/LocatorBeaconDescription" value="当通过轨道部署将单位部署在摩托侦察兵或者风暴渡鸦炮艇旁边时, 不消耗动作点数."/>
	<entry name="SpaceMarines/LocatorBeaconFlavor" value="<string name='Traits/LocatorBeaconFlavor'/>"/>
	<entry name="SpaceMarines/MachineEmpathy" value="<string name='Traits/MachineEmpathy'/>"/>
	<entry name="SpaceMarines/MachineEmpathyDescription" value="<string name='Actions/MachineEmpathyDescription'/>"/>
	<entry name="SpaceMarines/MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="<string name='Traits/SpaceMarines/MeleeDamage'/>"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="<string name='Traits/SpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SpaceMarines/MeltaBombDescription" value="使战术小队, 突击者小队, 毁灭者小队, 侦察兵以及摩托侦察兵获得放置热熔炸弹的能力, 该炸弹能够有效应对重型载具单位."/>
	<entry name="SpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
	<entry name="SpaceMarines/OmniscopeDescription" value="使毁灭者百夫长能够无视敌人的远程伤害减免效果."/>
	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalBombardment" value="<string name='Actions/OrbitalBombardment'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentDescription" value="<string name='Actions/OrbitalBombardmentDescription'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentFlavor" value="<string name='Actions/OrbitalBombardmentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalDeployment" value="<string name='Actions/OrbitalDeployment'/>"/>
	<entry name="SpaceMarines/OrbitalDeploymentDescription" value="使单位能够通过空降舱部署至战场任意位置."/>
	<entry name="SpaceMarines/OrbitalDeploymentFlavor" value="<string name='Actions/OrbitalDeploymentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalScan" value="<string name='Actions/OrbitalScan'/>"/>
	<entry name="SpaceMarines/OrbitalScanDescription" value="<string name='Actions/OrbitalScanDescription'/>"/>
	<entry name="SpaceMarines/OrbitalScanFlavor" value="<string name='Actions/OrbitalScanFlavor'/>"/>
	<entry name="SpaceMarines/PredatorLascannon" value="掠食者重型爆矢枪"/>
	<entry name="SpaceMarines/PredatorLascannonDescription" value="使赎罪要塞, 掠食者和天鹰巨炮获得额外的重型爆矢枪."/>
	<entry name="SpaceMarines/PredatorLascannonFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SpaceMarines/SiegeMasters" value="<string name='Traits/SiegeMasters'/>"/>
	<entry name="SpaceMarines/SiegeMastersDescription" value="<string name='Actions/SiegeMastersDescription'/>"/>
	<entry name="SpaceMarines/SiegeMastersFlavor" value="<string name='Actions/SiegeMastersFlavor'/>"/>
	<entry name="SpaceMarines/SiegeShield" value="<string name='Traits/SiegeShield'/>"/>
	<entry name="SpaceMarines/SiegeShieldDescription" value="提高维护者的护甲并降低其在森林和帝国废墟中的移动惩罚效果."/>
	<entry name="SpaceMarines/SiegeShieldFlavor" value="<string name='Traits/SiegeShieldFlavor'/>"/>
	<entry name="SpaceMarines/Signum" value="<string name='Actions/Signum'/>"/>
	<entry name="SpaceMarines/SignumDescription" value="使毁灭者小队能够无视重型, 大炮和齐射武器的惩罚效果."/>
	<entry name="SpaceMarines/SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SpaceMarines/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SpaceMarines/TacticalDoctrine" value="<string name='Traits/TacticalDoctrine'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineDescription" value="<string name='Actions/TacticalDoctrineDescription'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/TeleportHomer" value="<string name='Traits/TeleportHomer'/>"/>
	<entry name="SpaceMarines/TeleportHomerDescription" value="当通过轨道部署将牧师, 终结者突击小队和终结者传送至战术小队或侦察兵身边时, 不消耗动作点数."/>
	<entry name="SpaceMarines/TeleportHomerFlavor" value="<string name='Traits/TeleportHomerFlavor'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeak" value="<string name='Traits/TheFleshIsWeak'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakDescription" value="<string name='Actions/TheFleshIsWeakDescription'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="Tau/AdvancedTargetingSystem" value="<string name='Traits/Tau/AdvancedTargetingSystem'/>"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="战斗装甲支援系统及载具升级能够提高远程准确度."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="<string name='Traits/Tau/AdvancedTargetingSystemFlavor'/>"/>
	<entry name="Tau/AutomatedRepairSystem" value="<string name='Traits/Tau/AutomatedRepairSystem'/>"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="使载具每回合恢复生命值."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="<string name='Traits/Tau/AutomatedRepairSystemFlavor'/>"/>
	<entry name="Tau/BlacksunFilter" value="<string name='Traits/Tau/BlacksunFilter'/>"/>
	<entry name="Tau/BlacksunFilterDescription" value="提高载具, 乙太, 指挥官, 探路者和战斗装甲的视野范围."/>
	<entry name="Tau/BlacksunFilterFlavor" value="<string name='Traits/Tau/BlacksunFilterFlavor'/>"/>
	<entry name="Tau/BlastDamage" value="<string name='Traits/Tau/BlastDamage'/>"/>
	<entry name="Tau/BlastDamageDescription" value="提高烈焰武器和导弹的护甲穿透效果."/>
	<entry name="Tau/BlastDamageFlavor" value="<string name='Traits/Tau/BlastDamageFlavor'/>"/>
	<entry name="Tau/BoltDamage" value="<string name='Traits/Tau/BoltDamage'/>"/>
	<entry name="Tau/BoltDamageDescription" value="提高爆炸和轨道武器的护甲穿透效果."/>
	<entry name="Tau/BoltDamageFlavor" value="<string name='Traits/Tau/BoltDamageFlavor'/>"/>
	<entry name="Tau/BondingKnifeRitual" value="<string name='Actions/Tau/BondingKnifeRitual'/>"/>
	<entry name="Tau/BondingKnifeRitualDescription" value="使火族战士, 火族战士突破者, 探路者, XV25隐身战斗装甲, XV8危机战斗装甲, XV88炮击战斗装甲, XV95魂骨战斗装甲以及XV104激潮战斗装甲获得恢复士气的能力."/>
	<entry name="Tau/BondingKnifeRitualFlavor" value="<string name='Actions/Tau/BondingKnifeRitualFlavor'/>"/>
	<entry name="Tau/CityTier2" value="<string name='Traits/Tau/CityTier2'/>"/>
	<entry name="Tau/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier2Flavor" value="<string name='Traits/Tau/CityTier2Flavor'/>"/>
	<entry name="Tau/CityTier3" value="<string name='Traits/Tau/CityTier3'/>"/>
	<entry name="Tau/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier3Flavor" value="<string name='Traits/Tau/CityTier3Flavor'/>"/>
	<entry name="Tau/CounterfireDefenceSystem" value="<string name='Traits/Tau/CounterfireDefenceSystem'/>"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="战斗装甲支援系统, 能够提高掩护的准确度."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Traits/Tau/CounterfireDefenceSystemFlavor'/>"/>
	<entry name="Tau/DisruptionPod" value="<string name='Traits/Tau/DisruptionPod'/>"/>
	<entry name="Tau/DisruptionPodDescription" value="提高载具远程伤害减免效果."/>
	<entry name="Tau/DisruptionPodFlavor" value="<string name='Traits/Tau/DisruptionPodFlavor'/>"/>
	<entry name="Tau/DroneController" value="<string name='Traits/Tau/DroneController'/>"/>
	<entry name="Tau/DroneControllerDescription" value="战斗装甲支援系统, 提高临近无人机的准确度."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Traits/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/EMPGrenade" value="<string name='Weapons/EMPGrenade'/>"/>
	<entry name="Tau/EMPGrenadeDescription" value="使火族战士, 火族战士突破者和探路者获得投掷反装甲手雷的能力."/>
	<entry name="Tau/EMPGrenadeFlavor" value="<string name='Weapons/EMPGrenadeFlavor'/>"/>
	<entry name="Tau/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tau/ExtraInfantryArmourDescription" value="提高步兵和巨兽生物的护甲."/>
	<entry name="Tau/ExtraInfantryArmourFlavor" value="通过进一步研究萨息安种族的甲壳表明我们能够进一步对战斗装甲进行实质性的改进."/>
	<entry name="Tau/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Tau/ExtraVehicleArmourDescription" value="提高载具的护甲."/>
	<entry name="Tau/ExtraVehicleArmourFlavor" value="与停滞不前的火星科技神甫而言, 地族正不断的创新和创造. 非奥塔克是当前他们创造力的顶峰, 一种坚硬, 超致密的纳米金属合金, 只有在最强大的造物身上才会使用."/>
	<entry name="Tau/FlechetteDischarger" value="<string name='Traits/Tau/FlechetteDischarger'/>"/>
	<entry name="Tau/FlechetteDischargerDescription" value="使载具获得伤害近战攻击者的能力."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="<string name='Traits/Tau/FlechetteDischargerFlavor'/>"/>
	<entry name="Tau/ForTheGreaterGoodBonus" value="卡帕丁孢子菌"/>
	<entry name="Tau/ForTheGreaterGoodBonusDescription" value="降低上上善道的影响力消耗."/>
	<entry name="Tau/ForTheGreaterGoodBonusFlavor" value="当我们的水族外交官在战场上时, 有时候会发现我们的卡帕丁盟友非常有价值. 这些盟友频繁闪烁的紫外线几乎对所有种族都具有镇定作用, 甚至是催眠效果."/>
	<entry name="Tau/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tau/HammerOfWrathDescription" value="使XV95魂骨战斗装甲, XV104激潮战斗装甲, XV107日瓦尔纳战斗装甲和KV128风暴之涌获得施展更致命攻击的能力."/>
	<entry name="Tau/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tau/LasDamage" value="<string name='Traits/Tau/LasDamage'/>"/>
	<entry name="Tau/LasDamageDescription" value="提高聚变, 离子, 熔岩和脉冲武器的护甲穿透效果."/>
	<entry name="Tau/LasDamageFlavor" value="<string name='Traits/Tau/LasDamageFlavor'/>"/>
	<entry name="Tau/PhotonGrenade" value="<string name='Weapons/PhotonGrenade'/>"/>
	<entry name="Tau/PhotonGrenadeDescription" value="使火族战士, 火族战士突破者, 探路者, 火刃队长获得投掷致盲炸弹的能力."/>
	<entry name="Tau/PhotonGrenadeFlavor" value="<string name='Weapons/PhotonGrenadeFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="<string name='Traits/Tau/PointDefenceTargetingRelay'/>"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="使载具提高对位于其他友方单位旁边敌方单位的掩护伤害."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="<string name='Traits/Tau/PointDefenceTargetingRelayFlavor'/>"/>
	<entry name="Tau/PurchaseEnergy" value="<string name='Actions/Tau/PurchaseEnergy'/>"/>
	<entry name="Tau/PurchaseEnergyDescription" value="允许使用影响力购买能量."/>
	<entry name="Tau/PurchaseEnergyFlavor" value="<string name='Actions/Tau/PurchaseEnergyFlavor'/>"/>
	<entry name="Tau/PurchaseFood" value="<string name='Actions/Tau/PurchaseFood'/>"/>
	<entry name="Tau/PurchaseFoodDescription" value="允许使用影响力购买食物."/>
	<entry name="Tau/PurchaseFoodFlavor" value="<string name='Actions/Tau/PurchaseFoodFlavor'/>"/>
	<entry name="Tau/PurchaseOre" value="<string name='Actions/Tau/PurchaseOre'/>"/>
	<entry name="Tau/PurchaseOreDescription" value="允许使用影响力购买矿石."/>
	<entry name="Tau/PurchaseOreFlavor" value="<string name='Actions/Tau/PurchaseOreFlavor'/>"/>
	<entry name="Tau/PurchasePopulation" value="<string name='Actions/Tau/PurchasePopulation'/>"/>
	<entry name="Tau/PurchasePopulationDescription" value="允许使用影响力购买人口."/>
	<entry name="Tau/PurchasePopulationFlavor" value="<string name='Actions/Tau/PurchasePopulationFlavor'/>"/>
	<entry name="Tau/PurchaseResearch" value="<string name='Actions/Tau/PurchaseResearch'/>"/>
	<entry name="Tau/PurchaseResearchDescription" value="允许使用影响力购买研究."/>
	<entry name="Tau/PurchaseResearchFlavor" value="<string name='Actions/Tau/PurchaseResearchFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="<string name='Traits/Tau/RipykaVa'/>"/>
	<entry name="Tau/RipykaVaDescription" value="降低指挥官元策略的冷却时间."/>
	<entry name="Tau/RipykaVaFlavor" value="<string name='Traits/Tau/RipykaVaFlavor'/>"/>
	<entry name="Tau/SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="Tau/SeekerMissileDescription" value="使载具和XV88炮击战斗装甲获得导弹武器."/>
	<entry name="Tau/SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Tau/SensorSpines" value="传感器之脊"/>
	<entry name="Tau/SensorSpinesDescription" value="允许载具穿过掩护."/>
	<entry name="Tau/SensorSpinesFlavor" value="传感器之脊被用于将数据传输到先进的地面追踪飞行控制系统, 通过在地形上绘制安全路线从而避开可能隐藏的陷阱和地雷."/>
	<entry name="Tau/ShieldGenerator" value="<string name='Traits/Tau/ShieldGenerator'/>"/>
	<entry name="Tau/ShieldGeneratorDescription" value="战斗装甲支援系统, 获得无敌伤害减免效果."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Traits/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StimulantInjector" value="<string name='Traits/Tau/StimulantInjector'/>"/>
	<entry name="Tau/StimulantInjectorDescription" value="战斗装甲支援系统, 提高无视痛苦减免效果."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Traits/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/SubversionBonus" value="煽动研究"/>
	<entry name="Tau/SubversionBonusDescription" value="提高颠覆城市的忠诚度惩罚."/>
	<entry name="Tau/SubversionBonusFlavor" value="在进行对立研究的时候, 钛星人发现人类在军事教科书中留下了“像敌人一样思考”这样的字眼. 水族成员铭记于心, 并彻底调查了定居点居民们的需求和欲望—不论是愤怒的太空死灵奴隶或是饥饿的兽人小子—在他们展开叛乱之前."/>
	<entry name="Tau/TacticalSupportTurret" value="<string name='Weapons/TacticalSupportTurret'/>"/>
	<entry name="Tau/TacticalSupportTurretDescription" value="使火族战士在静止时获得一把额外的武器."/>
	<entry name="Tau/TacticalSupportTurretFlavor" value="<string name='Weapons/TacticalSupportTurretFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="<string name='Traits/Tau/UtopiaBonus'/>"/>
	<entry name="Tau/UtopiaBonusDescription" value="<string name='Traits/Tau/UtopiaBonusDescription'/>"/>
	<entry name="Tau/UtopiaBonusFlavor" value="<string name='Traits/Tau/UtopiaBonusFlavor'/>"/>
	<entry name="Tau/VectoredRetroThrusters" value="<string name='Traits/Tau/VectoredRetroThrusters'/>"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="战斗装甲支援系统, 提高移动并允许单位无视敌人区域控制效果."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Traits/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="<string name='Traits/Tau/VelocityTracker'/>"/>
	<entry name="Tau/VelocityTrackerDescription" value="战斗装甲支援系统, 提高对飞行单位的远程准确度."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Traits/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tyranids/AcidBlood" value="酸性血液"/>
	<entry name="Tyranids/AcidBloodDescription" value="使泰伦虫族巨兽生物和首席武士获得能够伤害所有近战攻击者的能力."/>
	<entry name="Tyranids/AcidBloodFlavor" value="<string name='Traits/Tyranids/AcidBloodFlavor'/>"/>
	<entry name="Tyranids/AdrenalGlands" value="肾上腺"/>
	<entry name="Tyranids/AdrenalGlandsDescription" value="提高单位的移动和近战伤害."/>
	<entry name="Tyranids/AdrenalGlandsFlavor" value="肾上腺能够使宿主的身体分泌大量化学物质, 使得生物的新陈代谢提高到一个近乎于疯狂的状态."/>
	<entry name="Tyranids/BiomorphDamage" value="<string name='Traits/Tyranids/BiomorphDamage'/>"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="提高生物拟态的护甲穿透效果."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="<string name='Traits/Tyranids/BiomorphDamageFlavor'/>"/>
	<entry name="Tyranids/BioPlasma" value="刽子手生物等离子体"/>
	<entry name="Tyranids/BioPlasmaDescription" value="使刽子手获得一件额外的远程武器."/>
	<entry name="Tyranids/BioPlasmaFlavor" value="<string name='Weapons/BioPlasmaFlavor'/>"/>
	<entry name="Tyranids/BoneMace" value="刽子手骨锤"/>
	<entry name="Tyranids/BoneMaceDescription" value="使刽子手获得一件额外的近战武器."/>
	<entry name="Tyranids/BoneMaceFlavor" value="<string name='Weapons/BoneMaceFlavor'/>"/>
	<entry name="Tyranids/CityCost" value="变异马兰脑虫"/>
	<entry name="Tyranids/CityCostDescription" value="降低建立新城市所需要的消耗."/>
	<entry name="Tyranids/CityCostFlavor" value="帝国基因专家对于马兰脑虫在建立新的泰伦虫族虫巢时所起作用的研究可以说是非常之少, 因为在大多数情况下, 当虫巢建立时马兰脑虫就已经死亡或者逃走. 然而, 据说马兰脑虫会将城市的种子运送到需要建立城市的地方. 有时会观察到一些不同寻常的马兰脑虫, 它们所拥有的特殊物理结构能够让其更加有效地完成这个任务."/>
	<entry name="Tyranids/CityDamage" value="<string name='Traits/Tyranids/CityDamage'/>"/>
	<entry name="Tyranids/CityDamageDescription" value="位于泰伦虫族城市中的敌方单位每回合将受到伤害."/>
	<entry name="Tyranids/CityDamageFlavor" value="<string name='Traits/Tyranids/CityDamageFlavor'/>"/>
	<entry name="Tyranids/CityGrowth" value="<string name='Traits/Tyranids/CityGrowth'/>"/>
	<entry name="Tyranids/CityGrowthDescription" value="提高泰伦虫族城市的增长率."/>
	<entry name="Tyranids/CityGrowthFlavor" value="<string name='Traits/Tyranids/CityGrowthFlavor'/>"/>
	<entry name="Tyranids/CityLoyalty" value="<string name='Traits/Tyranids/CityLoyalty'/>"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="降低拥有多个城市时的忠诚度惩罚."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="<string name='Traits/Tyranids/CityLoyaltyFlavor'/>"/>
	<entry name="Tyranids/CityPopulationLimit" value="<string name='Traits/Tyranids/CityPopulationLimit'/>"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="提高泰伦虫族城市的人口上限."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="<string name='Traits/Tyranids/CityPopulationLimitFlavor'/>"/>
	<entry name="Tyranids/CityProduction" value="<string name='Traits/Tyranids/CityProduction'/>"/>
	<entry name="Tyranids/CityProductionDescription" value="提高泰伦虫族城市的生产产出."/>
	<entry name="Tyranids/CityProductionFlavor" value="<string name='Traits/Tyranids/CityProductionFlavor'/>"/>
	<entry name="Tyranids/CityTier2" value="<string name='Traits/Tyranids/CityTier2'/>"/>
	<entry name="Tyranids/CityTier2Description" value="提高城市能够占领的土地半径."/>
	<entry name="Tyranids/CityTier2Flavor" value="<string name='Traits/Tyranids/CityTier2Flavor'/>"/>
	<entry name="Tyranids/ConsumeTile2" value="迅速消化"/>
	<entry name="Tyranids/ConsumeTile2Description" value="降低消耗领地时的影响力消耗."/>
	<entry name="Tyranids/ConsumeTile2Flavor" value="虽然马兰脑虫或者撕裂虫剃刀般的下颚吞噬血肉, 骨骼或者塑钢不会有任何问题, 但它们却被大量用来消耗土壤和岩石, 因为虫巢意志所需要的是规模和速度. 据推测已经有专门的有机体负责吞食这些—当然了, 毕竟没有人真的能够活着看到这些情景."/>
	<entry name="Tyranids/Deathspitter" value="贪食死亡喷吐者"/>
	<entry name="Tyranids/DeathspitterDescription" value="使贪食虫获得一件远程武器."/>
	<entry name="Tyranids/DeathspitterFlavor" value="<string name='Weapons/DeathspitterFlavor'/>"/>
	<entry name="Tyranids/DesiccatorLarvae" value="<string name='Weapons/DesiccatorLarvae'/>"/>
	<entry name="Tyranids/DesiccatorLarvaeDescription" value="使虫巢暴君, 恶妇兽和暴虐兽获得一件模块武器."/>
	<entry name="Tyranids/DesiccatorLarvaeFlavor" value="<string name='Weapons/DesiccatorLarvaeFlavor'/>"/>
	<entry name="Tyranids/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourFlavor" value="虫巢意志明明总是怀着节约的心态. 为什么还要牺牲资源去强化那些迟早会被回收的部队身上的盔甲? 而只有在赌博变得有意义之时, 它才会为自己的造物投入更多的几丁质和骨骼."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmour" value="<string name='Traits/ExtraMonstrousCreatureArmour'/>"/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourDescription" value="提高巨兽生物的护甲."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourFlavor" value="通过硬化甲壳, 破坏性领域和神经末梢组合, 虫巢意志可以轻松改变大型生物的韧性."/>
	<entry name="Tyranids/FleshHooks" value="<string name='Weapons/FleshHooks'/>"/>
	<entry name="Tyranids/FleshHooksDescription" value="使泰伦武士, 首席武士和利卡特获得一件额外的远程武器."/>
	<entry name="Tyranids/FleshHooksFlavor" value="<string name='Weapons/FleshHooksFlavor'/>"/>
	<entry name="Tyranids/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tyranids/HammerOfWrathDescription" value="使离子炮虫, 石像鬼, 巨嘴兽, 天巫, 虫巢暴君, 恶馈虫, 镰圣奴虫, 恶妇兽, 掘蟒, 孢子空投囊和暴虐兽获得施展更具毁灭性攻击的能力."/>
	<entry name="Tyranids/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="<string name='Traits/Tyranids/InfantryUpkeep'/>"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="降低步兵的生物质维护费."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="<string name='Traits/Tyranids/InfantryUpkeepFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2" value="狂野压制"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Description" value="降低克服本能行为的影响力消耗."/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Flavor" value="维持对部队的控制是虫巢意志的主要关注点, 同时它也需要通过各种同化来进行克服, 比如突触生物. 一个更简单的方法是降低单位的自然行为, 这样只需要少量的灵能就能够重新将其控制."/>
	<entry name="Tyranids/LongRangedDamage" value="<string name='Traits/Tyranids/LongRangedDamage'/>"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="提高远距离武器的护甲穿透效果."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="<string name='Traits/Tyranids/LongRangedDamageFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="<string name='Traits/Tyranids/MeleeDamage'/>"/>
	<entry name="Tyranids/MeleeDamageDescription" value="提高近战武器的护甲穿透效果."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="<string name='Traits/Tyranids/MeleeDamageFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="使脑虫获得诅咒敌方单位的能力, 降低其准确度."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation2" value="脑虫进化"/>
	<entry name="Tyranids/PreyAdaptation2Description" value="敌人在马兰脑虫附近死亡时提高所获得的研究."/>
	<entry name="Tyranids/PreyAdaptation2Flavor" value="马兰脑虫能够拥有一种更加特殊的同化形态, 更长的卷须上围绕着肉体一般的物质, 并且还在不断地蠕动着. 据推测, 这样的结构能够让马兰脑虫从刚刚死亡的敌人身体中获得和保留更多的基因数据."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="<string name='Traits/Tyranids/ProductionBuildingUpkeep'/>"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="降低生产类建筑的影响力维护费."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ProductionBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="<string name='Traits/Tyranids/Reclamation2'/>"/>
	<entry name="Tyranids/Reclamation2Description" value="降低开垦的影响力消耗."/>
	<entry name="Tyranids/Reclamation2Flavor" value="<string name='Traits/Tyranids/Reclamation2Flavor'/>"/>
	<entry name="Tyranids/Reclamation3" value="<string name='Traits/Tyranids/Reclamation3'/>"/>
	<entry name="Tyranids/Reclamation3Description" value="移除开垦的冷却时间."/>
	<entry name="Tyranids/Reclamation3Flavor" value="<string name='Traits/Tyranids/Reclamation3Flavor'/>"/>
	<entry name="Tyranids/Regeneration" value="恢复"/>
	<entry name="Tyranids/RegenerationDescription" value="每回合使泰伦虫族巨兽生物和首席武士恢复一定的生命值."/>
	<entry name="Tyranids/RegenerationFlavor" value="<string name='Traits/RegenerationFlavor'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="<string name='Traits/Tyranids/ResourceBuildingUpkeep'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="降低资源类建筑的影响力维护费."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ResourceBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="<string name='Traits/Tyranids/ShortRangedDamage'/>"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="提高短距离远程武器的护甲穿透效果."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="<string name='Traits/Tyranids/ShortRangedDamageFlavor'/>"/>
	<entry name="Tyranids/StingerSalvo" value="<string name='Weapons/StingerSalvo'/>"/>
	<entry name="Tyranids/StingerSalvoDescription" value="使天巫获得一件额外的远程武器."/>
	<entry name="Tyranids/StingerSalvoFlavor" value="<string name='Weapons/StingerSalvoFlavor'/>"/>
	<entry name="Tyranids/ThresherScythe" value="<string name='Weapons/ThresherScythe'/>"/>
	<entry name="Tyranids/ThresherScytheDescription" value="使离子炮虫和巨嘴兽获得一件额外的近战武器."/>
	<entry name="Tyranids/ThresherScytheFlavor" value="<string name='Weapons/ThresherScytheFlavor'/>"/>
	<entry name="Tyranids/ToxinSacs" value="<string name='Traits/Tyranids/ToxinSacs'/>"/>
	<entry name="Tyranids/ToxinSacsDescription" value="提高近战武器对步兵和巨兽单位的伤害."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="<string name='Traits/Tyranids/ToxinSacsFlavor'/>"/>
	<entry name="Tyranids/Toxinspike" value="掘蟒毒刺"/>
	<entry name="Tyranids/ToxinspikeDescription" value="使掘蟒获得一件额外的近战武器."/>
	<entry name="Tyranids/ToxinspikeFlavor" value="<string name='Weapons/ToxinspikeFlavor'/>"/>
	<entry name="Tyranids/Tunnel2" value="<string name='Traits/Tyranids/Tunnel2'/>"/>
	<entry name="Tyranids/Tunnel2Description" value="提高母巢的生命值."/>
	<entry name="Tyranids/Tunnel2Flavor" value="<string name='Traits/Tyranids/Tunnel2Flavor'/>"/>
	<entry name="Tyranids/VehiclesUpkeep" value="<string name='Traits/Tyranids/VehiclesUpkeep'/>"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="降低巨兽生物的生物质维护费."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="<string name='Traits/Tyranids/VehiclesUpkeepFlavor'/>"/>
	
	<entry name="Missing" value="未命中"/>
</language>
