<?xml version="1.0" encoding="utf-8"?>
<unit productionWeight="0.5">
	<model>
		<unit mesh="Units/Tyranids/Malanthrope"
				material="Units/Tyranids/Malanthrope"
				scale="1.2 1.2 1.2"
				idleAnimation="Units/Tyranids/MalanthropeIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				bloodBone="BloodBone"
				walker="1"/>
	</model>
	<weapons>
		<weapon name="CloseCombatWeapon">
			<model>
				<weapon fireInterval="0.47"
						fireSoundCount="0"/>
			</model>
		</weapon>
		<weapon name="ToxicMiasma" slotName="ToxicMiasma" enabled="0">
			<model>
				<flamerWeapon muzzleBone="Bone004"
						effectFaceWeight="1"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="4"/> <!-- %armor base armor=5+ -->
				<biomassUpkeep base="2.0"/> <!-- %biomassUpkeep base tier=6 factor=1 -->
				<biomassCost base="40.0"/> <!-- %biomassCost base tier=6 factor=1 -->
				<hitpointsMax base="16.0"/> <!-- %hitpointsMax base toughness=5 wounds=4 -->
				<meleeAccuracy base="6"/> <!-- %meleeAccuracy base weaponSkill=3 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="2"/> <!-- %strengthDamage base strength=5 -->
				<moraleMax base="12"/> <!-- %moraleMax base leadership=9 -->
				<movementMax base="3"/>
				<productionCost base="36.0"/> <!-- %productionCost base tier=6 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseColonizersScore base="1.0"/>
				<increaseSupportScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action chargeAnimation="Units/Tyranids/MalanthropeCharge"
						meleeAnimation="Units/Tyranids/MalanthropeMelee"
						meleeBeginSwing="0.166666666667"
						meleeEndSwing="0.833333333333"/>
			</model>
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/MalanthropeDie"
						sound="Units/MediumUnarmoredDie"
						soundCount="4"
						soundDelay="1.1"
						voiceSound="Units/Tyranids/MediumDie"
						voiceSoundCount="4"/>
			</model>
		</die>
		<move>
			<model>
				<action animation="Units/Tyranids/MalanthropeMove"
						sound="Units/Tyranids/MalanthropeMove"
						soundCount="2"/>
			</model>		
		</move>
		<foundCity>
			<model>
				<action sound="Actions/TyranidsFoundCity"
						animation=""/>
			</model>
			<modifiers>
				<modifier visible="0">
					<effects>
						<biomassCost base="40"/>
						<influenceCost base="40"/>
					</effects>
				</modifier>

			</modifiers>
		</foundCity>

	</actions>
	<traits>
		<trait name="Poisoned" rank="4"/>
		<trait name="MoveThroughCover"/>
		<trait name="Regeneration"/>
		<trait name="VeryBulky"/>
	</traits>
</unit>
