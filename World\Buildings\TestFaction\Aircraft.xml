<?xml version="1.0" encoding="utf-8"?>
<building>
	<modifiers>
		<modifier visible="0">
			<effects>
				<influenceUpkeep add="3"/>
				<biomassCost base="80"/>
				<productionCost base="48"/>
				<populationRequired base="1"/>
				<slotsRequired base="1"/>
			</effects>
		</modifier>
		<modifier>
			<effects>
				<loyalty add="1"/>
				<populationLimit add="1"/>
				<production add="6"/>
				<research add="2"/>
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseAircraftProductionScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<reclaimUnit cooldown="1"
				name="TyranidsClone/ReclaimUnit"
				interfaceSound="Interface/ReclaimUnit">
			<modifiers>
				<modifier visible="0">
					<effects>
						<influenceCost add="10"/>
					</effects>
				</modifier>
				<modifier requiredUpgrade="TyranidsClone/Reclamation2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
				<modifier requiredUpgrade="TyranidsClone/Reclamation3">
					<effects>
						<cooldown max="0"/>
					</effects>
				</modifier>
			</modifiers>
		</reclaimUnit>
	</actions>
	<traits>
		<trait name="TyranidsClone/ProductionBuildingUpkeep" requiredUpgrade="TyranidsClone/ProductionBuildingUpkeep"/>
	</traits>
</building>
