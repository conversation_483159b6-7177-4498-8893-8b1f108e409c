<?xml version="1.0" encoding="utf-8"?>
<unit dlc="Supplement2">
	<model>
		<unit mesh="Units/Tyranids/Ravener"
				material="Units/Tyranids/Ravener"
				idleAnimation="Units/Tyranids/RavenerIdle"
				idleAnimationCount="3"
				idleContinuously="1"
				normalWeight="0.1"
				scale="0.8 0.8 0.8"
				position="0 0 -2"
				bloodBone="BloodBone"
				walker="1"/>
	</model>
	<group size="3" rowSize="3" memberDeltaX="70" memberDeltaY="70"/>
	<weapons>
		<weapon name="Deathspitter" requiredUpgrade="Tyranids/Deathspitter">
			<model>
				<projectileWeapon fireInterval="0.733333333333"
						effectFaceWeight="1.0"
						muzzleBone="Bone024"/>
			</model>
		</weapon>
		<weapon name="RendingClaws">
			<model>
				<weapon fireInterval="0.5"/>
			</model>
		</weapon>
		<weapon name="ScythingTalons">
			<model>
				<weapon fireInterval="0.5"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>				
				<armor base="4"/> <!-- %armor base armor=5+ -->
				<biomassUpkeep base="3.0"/> <!-- %biomassUpkeep base tier=7 factor=1 -->
				<biomassCost base="60.0"/> <!-- %biomassCost base tier=7 factor=1 -->
				<hitpointsMax base="9.0"/> <!-- %hitpointsMax base toughness=4 wounds=3 -->
				<meleeAccuracy base="10"/> <!-- %meleeAccuracy base weaponSkill=5 -->
				<meleeAttacks base="2"/>
				<strengthDamage base="1.5"/> <!-- %strengthDamage base strength=4 -->
				<moraleMax base="4"/> <!-- %moraleMax base leadership=5 -->
				<movementMax base="5"/>
				<productionCost base="42.0"/> <!-- %productionCost base tier=7 factor=1 -->
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
		<modifier>
			<effects>
				<increaseInfantryScore base="1.0"/>
			</effects>
		</modifier>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action animation="Units/Tyranids/RavenerAttack"
						beginFire="0.666666666667"
						endFire="2.4"
						chargeAnimation="Units/Tyranids/RavenerCharge"
						chargeBeginFire="0.333333333333"
						chargeEndFire="1.23333333333"
						meleeAnimation="Units/Tyranids/RavenerMelee"
						meleeBeginSwing="0"
						meleeEndSwing="0.666666666667"/>
			</model>				
		</attack>
		<die>
			<model>
				<action animation="Units/Tyranids/RavenerDie"
						animationCount="2"
						sound="Units/MediumArmoredDie"
						soundCount="4"
						soundDelay="0.75"
						voiceSound="Units/Tyranids/SmallDie"
						voiceSoundCount="4"/>
			</model>		
		</die>
		<idle>
			<model>
				<action/>
			</model>
		</idle>
		<move>
			<model>
				<action animation="Units/Tyranids/RavenerMove"
						sound="Units/Tyranids/RavenerMove"
						soundCount="2"/>
			</model>
		</move>
		<instinctiveBehaviourOverride consumedActionPoints="0"
				consumedMovement="0"
				cooldown="3"
				name="Tyranids/InstinctiveBehaviourOverride"
				requiredActionPoints="0">
			<model>
				<action sound="Actions/HiveMindBuff"/>
			</model>
			<modifiers>
				<modifier requiredUpgrade="Tyranids/InstinctiveBehaviourOverride2">
					<effects>
						<influenceCost mul="-0.5"/>
					</effects>
				</modifier>
			</modifiers>
			<conditions>
				<unit>
					<noTrait name="Tyranids/InstinctiveBehaviourOverride"/>
					<noTrait name="Tyranids/SynapseLink"/>
				</unit>
			</conditions>
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Unit">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="Tyranids/InstinctiveBehaviourOverride" duration="3"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</instinctiveBehaviourOverride>
		<subterraneanAssault name="Tyranids/SubterraneanAssault"/>
	</actions>
	<traits>
		<trait name="ExtraInfantryArmour" requiredUpgrade="Tyranids/ExtraInfantryArmour"/>
		<trait name="Tyranids/InfantryUpkeep" requiredUpgrade="Tyranids/InfantryUpkeep"/>
		<trait name="Tyranids/InstinctiveBehaviour"/>
		<trait name="MoveThroughCover"/>
		<trait name="VeryBulky"/>
	</traits>
</unit>
