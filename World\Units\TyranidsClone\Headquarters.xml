<?xml version="1.0" encoding="utf-8"?>
<unit icon="Buildings/Tyranids/Headquarters">
	<model>
		<structureUnit mesh="Units/Tyranids/Headquarters"
				material="Buildings/Tyranids/Buildings"
				idleAnimationCount="0"
				heatImpactSound="Impacts/HeatConcrete"
				heatImpactSoundCount="4"
				kineticImpactSound="Impacts/KineticConcrete"
				kineticImpactSoundCount="5"
				scale="1.0 1.0 1.0"
				explosionsBone="HQBone"/>
	</model>
	<weapons>
		<weapon name="Devourer" count="5">
			<model>
				<projectileWeapon mesh="Weapons/Tyranids/HeadquartersDevourers"
						material="Buildings/Tyranids/Buildings"
						bone="HQBone"
						fireInterval="0.25"
						muzzleBone=".Muzzle"
						muzzleCount="5"/>
			</model>
		</weapon>
		<weapon name="HeavyVenomCannon" count="2">
			<model>
				<missileWeapon mesh="Weapons/Tyranids/HeadquartersHeavyVenomCannon"
						material="Buildings/Tyranids/Buildings"
						bone="HQBone"
						fireInterval="0.5"
						muzzleBone=".Muzzle"/>
			</model>
		</weapon>
	</weapons>
	<modifiers>
		<modifier visible="0">
			<effects>
				<armor base="6"/> <!-- %armor base armor=10/10/10 -->
				<cargoSlots base="6"/> <!-- %cargoSlots base capacity=20 -->
				<moraleMax base="10"/> <!-- %moraleMax base leadership=8 -->
				<movementMax max="0"/>
				<rangedAccuracy base="6"/> <!-- %rangedAccuracy base ballisticSkill=3 -->
			</effects>
		</modifier>
	</modifiers>
	<strategyModifiers>
	</strategyModifiers>
	<actions>
		<attack>
			<model>
				<action beginFire="0.15" endFire="2.6"/>
			</model>
		</attack>
		<die>
			<model>
				<action sound="Units/LightBuildingDie"
						soundCount="3"
						voiceSound="Units/Tyranids/LargeDie"
						voiceSoundCount="4"/>
			</model>
		</die>
		<genericUnitAbility name="TyranidsClone/SynapseCreature" passive="1">
			<beginTargets>
				<selfTarget>
					<areas>
						<area affects="Tile" radius="1">
							<modifiers>
								<modifier>
									<effects>
										<addTrait name="TyranidsClone/SynapseLink"/>
									</effects>
								</modifier>
								<modifier requiredUpgrade="TyranidsClone/CityTier2">
									<effects>
										<radius add="1"/>
									</effects>
								</modifier>
							</modifiers>
						</area>
					</areas>
				</selfTarget>
			</beginTargets>
		</genericUnitAbility>
		<setRallyPoint/>
	</actions>
	<traits>
		<trait name="Fortification"/>
		<trait name="Headquarters"/>
		<trait name="Transport"/>
	</traits>	
</unit>
