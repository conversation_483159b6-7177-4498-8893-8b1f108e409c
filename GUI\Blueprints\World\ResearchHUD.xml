<?xml version="1.0" encoding="utf-8"?>
<world:researchHUD extends="HUD" layout="Flow" layout.alignment="MiddleCenter" layout.direction="TopToBottom" minSize="0 768">
	<contentContainer surface.texture="GUI/TopBar" name="topContainer" showEffect="FadeInTop" hideEffect="FadeOutTop" content.layout.alignment="BottomCenter" content.layout.gap="0 0" content.layout.collapseInvisible="1" content.layout.direction="TopToBottom" preferredSize="FillParent FillParent" weights="FillAll 1" content.margin="16 0; 16 16">
		<label caption="<style name='MenuTitle'/><string name='Attributes/Research'/>" preferredSize="WrapContent 38"/>
		<image name="headlineBarImage" texture="GUI/HeadlineBar" color="GUI/HeadlineBar"/>
	</contentContainer>
	<scrollableContainer name="contentContainer" showEffect="FadeInLeft" hideEffect="FadeOutRight" preferredSize="FillParent WrapContent" weights="FillAll FillAll"
			scrollableContent.layout.direction="LeftToRight" scrollableContent.layout.alignment="MiddleLeft" scrollableContent.layout.gap="64" visibleContentContainer.content.margin="0 8" scrollableContent.preferredSize="WrapContent WrapContent"
			wheelScroll="Horizontal" scrollBarMargin="16 0"/>
	<contentContainer surface.texture="GUI/BottomBar" name="bottomContainer" showEffect="FadeInBottom" hideEffect="FadeOutBottom" content.layout.alignment="TopCenter" preferredSize="FillParent FillParent" weights="FillAll 1" content.margin="16 22; 16 0">
		<navigationOKButton/>
	</contentContainer>
</world:researchHUD>