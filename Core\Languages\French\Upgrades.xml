<?xml version="1.0" encoding="utf-8"?>
<language>

	<!-- Buildings -->
	<entry name="AdeptusMechanicus/Aircraft" value="<string name='Buildings/AdeptusMechanicus/Aircraft'/>"/>
	<entry name="AdeptusMechanicus/AircraftDescription" value="<string name='Buildings/AdeptusMechanicus/AircraftDescription'/>"/>
	<entry name="AdeptusMechanicus/AircraftFlavor" value="<string name='Buildings/AdeptusMechanicus/AircraftFlavor'/>"/>
	<entry name="AdeptusMechanicus/Construction" value="<string name='Buildings/AdeptusMechanicus/Construction'/>"/>
	<entry name="AdeptusMechanicus/ConstructionDescription" value="<string name='Buildings/AdeptusMechanicus/ConstructionDescription'/>"/>
	<entry name="AdeptusMechanicus/ConstructionFlavor" value="<string name='Buildings/AdeptusMechanicus/ConstructionFlavor'/>"/>
	<entry name="AdeptusMechanicus/Heroes" value="<string name='Buildings/AdeptusMechanicus/Heroes'/>"/>
	<entry name="AdeptusMechanicus/HeroesDescription" value="<string name='Buildings/AdeptusMechanicus/HeroesDescription'/>"/>
	<entry name="AdeptusMechanicus/HeroesFlavor" value="<string name='Buildings/AdeptusMechanicus/HeroesFlavor'/>"/>
	<entry name="AdeptusMechanicus/Housing" value="<string name='Buildings/AdeptusMechanicus/Housing'/>"/>
	<entry name="AdeptusMechanicus/HousingDescription" value="<string name='Buildings/AdeptusMechanicus/HousingDescription'/>"/>
	<entry name="AdeptusMechanicus/HousingFlavor" value="<string name='Buildings/AdeptusMechanicus/HousingFlavor'/>"/>
	<entry name="AdeptusMechanicus/Loyalty" value="<string name='Buildings/AdeptusMechanicus/Loyalty'/>"/>
	<entry name="AdeptusMechanicus/LoyaltyDescription" value="<string name='Buildings/AdeptusMechanicus/LoyaltyDescription'/>"/>
	<entry name="AdeptusMechanicus/LoyaltyFlavor" value="<string name='Buildings/AdeptusMechanicus/LoyaltyFlavor'/>"/>
	<entry name="AdeptusMechanicus/Vehicles" value="<string name='Buildings/AdeptusMechanicus/Vehicles'/>"/>
	<entry name="AdeptusMechanicus/VehiclesDescription" value="<string name='Buildings/AdeptusMechanicus/VehiclesDescription'/>"/>
	<entry name="AdeptusMechanicus/VehiclesFlavor" value="<string name='Buildings/AdeptusMechanicus/VehiclesFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacher" value="<string name='Units/AdeptusMechanicus/KataphronBreacher'/>"/>
 	<entry name="AdeptusMechanicus/KataphronBreacherDescription" value="<string name='Units/AdeptusMechanicus/KataphronBreacherDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronBreacherFlavor" value="<string name='Units/AdeptusMechanicus/KataphronBreacherFlavor'/>"/>
	<entry name="AstraMilitarum/Aircraft" value="<string name='Buildings/AstraMilitarum/Aircraft'/>"/>
	<entry name="AstraMilitarum/AircraftDescription" value="<string name='Buildings/AstraMilitarum/AircraftDescription'/>"/>
	<entry name="AstraMilitarum/AircraftFlavor" value="<string name='Buildings/AstraMilitarum/AircraftFlavor'/>"/>
	<entry name="AstraMilitarum/Construction" value="<string name='Buildings/AstraMilitarum/Construction'/>"/>
	<entry name="AstraMilitarum/ConstructionDescription" value="<string name='Buildings/AstraMilitarum/ConstructionDescription'/>"/>
	<entry name="AstraMilitarum/ConstructionFlavor" value="<string name='Buildings/AstraMilitarum/ConstructionFlavor'/>"/>
	<entry name="AstraMilitarum/Heroes" value="<string name='Buildings/AstraMilitarum/Heroes'/>"/>
	<entry name="AstraMilitarum/HeroesDescription" value="<string name='Buildings/AstraMilitarum/HeroesDescription'/>"/>
	<entry name="AstraMilitarum/HeroesFlavor" value="<string name='Buildings/AstraMilitarum/HeroesFlavor'/>"/>
	<entry name="AstraMilitarum/Housing" value="<string name='Buildings/AstraMilitarum/Housing'/>"/>
	<entry name="AstraMilitarum/HousingDescription" value="<string name='Buildings/AstraMilitarum/HousingDescription'/>"/>
	<entry name="AstraMilitarum/HousingFlavor" value="<string name='Buildings/AstraMilitarum/HousingFlavor'/>"/>
	<entry name="AstraMilitarum/Loyalty" value="<string name='Buildings/AstraMilitarum/Loyalty'/>"/>
	<entry name="AstraMilitarum/LoyaltyDescription" value="<string name='Buildings/AstraMilitarum/LoyaltyDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyFlavor" value="<string name='Buildings/AstraMilitarum/LoyaltyFlavor'/>"/>
	<entry name="AstraMilitarum/Psykers" value="<string name='Buildings/AstraMilitarum/Psykers'/>"/>
	<entry name="AstraMilitarum/PsykersDescription" value="<string name='Buildings/AstraMilitarum/PsykersDescription'/>"/>
	<entry name="AstraMilitarum/PsykersFlavor" value="<string name='Buildings/AstraMilitarum/PsykersFlavor'/>"/>
	<entry name="AstraMilitarum/Upgrades" value="<string name='Buildings/AstraMilitarum/Upgrades'/>"/>
	<entry name="AstraMilitarum/UpgradesDescription" value="<string name='Buildings/AstraMilitarum/UpgradesDescription'/>"/>
	<entry name="AstraMilitarum/UpgradesFlavor" value="<string name='Buildings/AstraMilitarum/UpgradesFlavor'/>"/>
	<entry name="AstraMilitarum/Vehicles" value="<string name='Buildings/AstraMilitarum/Vehicles'/>"/>
	<entry name="AstraMilitarum/VehiclesDescription" value="<string name='Buildings/AstraMilitarum/VehiclesDescription'/>"/>
	<entry name="AstraMilitarum/VehiclesFlavor" value="<string name='Buildings/AstraMilitarum/VehiclesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Aircraft" value="<string name='Buildings/ChaosSpaceMarines/Aircraft'/>"/>
	<entry name="ChaosSpaceMarines/AircraftDescription" value="<string name='Buildings/ChaosSpaceMarines/AircraftDescription'/>"/>
	<entry name="ChaosSpaceMarines/AircraftFlavor" value="<string name='Buildings/ChaosSpaceMarines/AircraftFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Construction" value="<string name='Buildings/ChaosSpaceMarines/Construction'/>"/>
	<entry name="ChaosSpaceMarines/ConstructionDescription" value="<string name='Buildings/ChaosSpaceMarines/ConstructionDescription'/>"/>
	<entry name="ChaosSpaceMarines/ConstructionFlavor" value="<string name='Buildings/ChaosSpaceMarines/ConstructionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Heroes" value="<string name='Buildings/ChaosSpaceMarines/Heroes'/>"/>
	<entry name="ChaosSpaceMarines/HeroesDescription" value="<string name='Buildings/ChaosSpaceMarines/HeroesDescription'/>"/>
	<entry name="ChaosSpaceMarines/HeroesFlavor" value="<string name='Buildings/ChaosSpaceMarines/HeroesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Housing" value="<string name='Buildings/ChaosSpaceMarines/Housing'/>"/>
	<entry name="ChaosSpaceMarines/HousingDescription" value="<string name='Buildings/ChaosSpaceMarines/HousingDescription'/>"/>
	<entry name="ChaosSpaceMarines/HousingFlavor" value="<string name='Buildings/ChaosSpaceMarines/HousingFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Infantry" value="<string name='Buildings/ChaosSpaceMarines/Infantry'/>"/>
	<entry name="ChaosSpaceMarines/InfantryDescription" value="<string name='Buildings/ChaosSpaceMarines/InfantryDescription'/>"/>
	<entry name="ChaosSpaceMarines/InfantryFlavor" value="<string name='Buildings/ChaosSpaceMarines/InfantryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Loyalty" value="<string name='Buildings/ChaosSpaceMarines/Loyalty'/>"/>
	<entry name="ChaosSpaceMarines/LoyaltyDescription" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyDescription'/>"/>
	<entry name="ChaosSpaceMarines/LoyaltyFlavor" value="<string name='Buildings/ChaosSpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Vehicles" value="<string name='Buildings/ChaosSpaceMarines/Vehicles'/>"/>
	<entry name="ChaosSpaceMarines/VehiclesDescription" value="<string name='Buildings/ChaosSpaceMarines/VehiclesDescription'/>"/>
	<entry name="ChaosSpaceMarines/VehiclesFlavor" value="<string name='Buildings/ChaosSpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Drukhari/Aircraft" value="<string name='Buildings/Drukhari/Aircraft'/>"/>
    <entry name="Drukhari/AircraftDescription" value="<string name='Buildings/Drukhari/AircraftDescription'/>"/>
    <entry name="Drukhari/AircraftFlavor" value="<string name='Buildings/Drukhari/AircraftFlavor'/>"/>
    <entry name="Drukhari/Construction" value="<string name='Buildings/Drukhari/Construction'/>"/>
    <entry name="Drukhari/ConstructionDescription" value="<string name='Buildings/Drukhari/ConstructionDescription'/>"/>
    <entry name="Drukhari/ConstructionFlavor" value="<string name='Buildings/Drukhari/ConstructionFlavor'/>"/>
    <entry name="Drukhari/Heroes" value="<string name='Buildings/Drukhari/Heroes'/>"/>
    <entry name="Drukhari/HeroesDescription" value="<string name='Buildings/Drukhari/HeroesDescription'/>"/>
    <entry name="Drukhari/HeroesFlavor" value="<string name='Buildings/Drukhari/HeroesFlavor'/>"/>
    <entry name="Drukhari/Housing" value="<string name='Buildings/Drukhari/Housing'/>"/>
    <entry name="Drukhari/HousingDescription" value="<string name='Buildings/Drukhari/HousingDescription'/>"/>
    <entry name="Drukhari/HousingFlavor" value="<string name='Buildings/Drukhari/HousingFlavor'/>"/>
    <entry name="Drukhari/Loyalty" value="<string name='Buildings/Drukhari/Loyalty'/>"/>
    <entry name="Drukhari/LoyaltyDescription" value="<string name='Buildings/Drukhari/LoyaltyDescription'/>"/>
    <entry name="Drukhari/LoyaltyFlavor" value="<string name='Buildings/Drukhari/LoyaltyFlavor'/>"/>
    <entry name="Drukhari/Vehicles" value="<string name='Buildings/Drukhari/Vehicles'/>"/>
    <entry name="Drukhari/VehiclesDescription" value="<string name='Buildings/Drukhari/VehiclesDescription'/>"/>
    <entry name="Drukhari/VehiclesFlavor" value="<string name='Buildings/Drukhari/VehiclesFlavor'/>"/>
    <entry name="Eldar/Aircraft" value="<string name='Buildings/Eldar/Aircraft'/>"/>
 	<entry name="Eldar/AircraftDescription" value="<string name='Buildings/Eldar/AircraftDescription'/>"/>
 	<entry name="Eldar/AircraftFlavor" value="<string name='Buildings/Eldar/AircraftFlavor'/>"/>
 	<entry name="Eldar/Construction" value="<string name='Buildings/Eldar/Construction'/>"/>
 	<entry name="Eldar/ConstructionDescription" value="<string name='Buildings/Eldar/ConstructionDescription'/>"/>
 	<entry name="Eldar/ConstructionFlavor" value="<string name='Buildings/Eldar/ConstructionFlavor'/>"/>
 	<entry name="Eldar/Heroes" value="<string name='Buildings/Eldar/Heroes'/>"/>
 	<entry name="Eldar/HeroesDescription" value="<string name='Buildings/Eldar/HeroesDescription'/>"/>
 	<entry name="Eldar/HeroesFlavor" value="<string name='Buildings/Eldar/HeroesFlavor'/>"/>
 	<entry name="Eldar/Housing" value="<string name='Buildings/Eldar/Housing'/>"/>
 	<entry name="Eldar/HousingDescription" value="<string name='Buildings/Eldar/HousingDescription'/>"/>
 	<entry name="Eldar/HousingFlavor" value="<string name='Buildings/Eldar/HousingFlavor'/>"/>
 	<entry name="Eldar/Infantry" value="<string name='Buildings/Eldar/Infantry'/>"/>
 	<entry name="Eldar/InfantryDescription" value="<string name='Buildings/Eldar/InfantryDescription'/>"/>
 	<entry name="Eldar/InfantryFlavor" value="<string name='Buildings/Eldar/InfantryFlavor'/>"/>
 	<entry name="Eldar/Loyalty" value="<string name='Buildings/Eldar/Loyalty'/>"/>
 	<entry name="Eldar/LoyaltyDescription" value="<string name='Buildings/Eldar/LoyaltyDescription'/>"/>
 	<entry name="Eldar/LoyaltyFlavor" value="<string name='Buildings/Eldar/LoyaltyFlavor'/>"/>
 	<entry name="Eldar/Vehicles" value="<string name='Buildings/Eldar/Vehicles'/>"/>
 	<entry name="Eldar/VehiclesDescription" value="<string name='Buildings/Eldar/VehiclesDescription'/>"/>
 	<entry name="Eldar/VehiclesFlavor" value="<string name='Buildings/Eldar/VehiclesFlavor'/>"/>
	<entry name="Necrons/Aircraft" value="<string name='Buildings/Necrons/Aircraft'/>"/>
	<entry name="Necrons/AircraftDescription" value="<string name='Buildings/Necrons/AircraftDescription'/>"/>
	<entry name="Necrons/AircraftFlavor" value="<string name='Buildings/Necrons/AircraftFlavor'/>"/>
	<entry name="Necrons/Construction" value="<string name='Buildings/Necrons/Construction'/>"/>
	<entry name="Necrons/ConstructionDescription" value="<string name='Buildings/Necrons/ConstructionDescription'/>"/>
	<entry name="Necrons/ConstructionFlavor" value="<string name='Buildings/Necrons/ConstructionFlavor'/>"/>
	<entry name="Necrons/Heroes" value="<string name='Buildings/Necrons/Heroes'/>"/>
	<entry name="Necrons/HeroesDescription" value="<string name='Buildings/Necrons/HeroesDescription'/>"/>
	<entry name="Necrons/HeroesFlavor" value="<string name='Buildings/Necrons/HeroesFlavor'/>"/>
	<entry name="Necrons/Housing" value="<string name='Buildings/Necrons/Housing'/>"/>
	<entry name="Necrons/HousingDescription" value="<string name='Buildings/Necrons/HousingDescription'/>"/>
	<entry name="Necrons/HousingFlavor" value="<string name='Buildings/Necrons/HousingFlavor'/>"/>
	<entry name="Necrons/Loyalty" value="<string name='Buildings/Necrons/Loyalty'/>"/>
	<entry name="Necrons/LoyaltyDescription" value="<string name='Buildings/Necrons/LoyaltyDescription'/>"/>
	<entry name="Necrons/LoyaltyFlavor" value="<string name='Buildings/Necrons/LoyaltyFlavor'/>"/>
	<entry name="Necrons/Vehicles" value="<string name='Buildings/Necrons/Vehicles'/>"/>
	<entry name="Necrons/VehiclesDescription" value="<string name='Buildings/Necrons/VehiclesDescription'/>"/>
	<entry name="Necrons/VehiclesFlavor" value="<string name='Buildings/Necrons/VehiclesFlavor'/>"/>
	<entry name="Orks/Beasts" value="<string name='Buildings/Orks/Beasts'/>"/>
	<entry name="Orks/BeastsDescription" value="<string name='Buildings/Orks/BeastsDescription'/>"/>
	<entry name="Orks/BeastsFlavor" value="<string name='Buildings/Orks/BeastsFlavor'/>"/>
	<entry name="Orks/Colonizers" value="<string name='Buildings/Orks/Colonizers'/>"/>
	<entry name="Orks/ColonizersDescription" value="<string name='Buildings/Orks/ColonizersDescription'/>"/>
	<entry name="Orks/ColonizersFlavor" value="<string name='Buildings/Orks/ColonizersFlavor'/>"/>
	<entry name="Orks/Construction" value="<string name='Buildings/Orks/Construction'/>"/>
	<entry name="Orks/ConstructionDescription" value="<string name='Buildings/Orks/ConstructionDescription'/>"/>
	<entry name="Orks/ConstructionFlavor" value="<string name='Buildings/Orks/ConstructionFlavor'/>"/>
	<entry name="Orks/Heroes" value="<string name='Buildings/Orks/Heroes'/>"/>
	<entry name="Orks/HeroesDescription" value="<string name='Buildings/Orks/HeroesDescription'/>"/>
	<entry name="Orks/HeroesFlavor" value="<string name='Buildings/Orks/HeroesFlavor'/>"/>
	<entry name="Orks/Housing" value="<string name='Buildings/Orks/Housing'/>"/>
	<entry name="Orks/HousingDescription" value="<string name='Buildings/Orks/HousingDescription'/>"/>
	<entry name="Orks/HousingFlavor" value="<string name='Buildings/Orks/HousingFlavor'/>"/>
	<entry name="Orks/Loyalty" value="<string name='Buildings/Orks/Loyalty'/>"/>
	<entry name="Orks/LoyaltyDescription" value="<string name='Buildings/Orks/LoyaltyDescription'/>"/>
	<entry name="Orks/LoyaltyFlavor" value="<string name='Buildings/Orks/LoyaltyFlavor'/>"/>
	<entry name="Orks/Vehicles" value="<string name='Buildings/Orks/Vehicles'/>"/>
	<entry name="Orks/VehiclesDescription" value="<string name='Buildings/Orks/VehiclesDescription'/>"/>
	<entry name="Orks/VehiclesFlavor" value="<string name='Buildings/Orks/VehiclesFlavor'/>"/>
	<entry name="SistersOfBattle/Auxiliaries" value="<string name='Buildings/SistersOfBattle/Auxiliaries'/>"/>
	<entry name="SistersOfBattle/AuxiliariesDescription" value="<string name='Buildings/SistersOfBattle/AuxiliariesDescription'/>"/>
	<entry name="SistersOfBattle/AuxiliariesFlavor" value="<string name='Buildings/SistersOfBattle/AuxiliariesFlavor'/>"/>
	<entry name="SistersOfBattle/Construction" value="<string name='Buildings/SistersOfBattle/Construction'/>"/>
	<entry name="SistersOfBattle/ConstructionDescription" value="<string name='Buildings/SistersOfBattle/ConstructionDescription'/>"/>
	<entry name="SistersOfBattle/ConstructionFlavor" value="<string name='Buildings/SistersOfBattle/ConstructionFlavor'/>"/>
	<entry name="SistersOfBattle/Heroes" value="<string name='Buildings/SistersOfBattle/Heroes'/>"/>
	<entry name="SistersOfBattle/HeroesDescription" value="<string name='Buildings/SistersOfBattle/HeroesDescription'/>"/>
	<entry name="SistersOfBattle/HeroesFlavor" value="<string name='Buildings/SistersOfBattle/HeroesFlavor'/>"/>
	<entry name="SistersOfBattle/Housing" value="<string name='Buildings/SistersOfBattle/Housing'/>"/>
	<entry name="SistersOfBattle/HousingDescription" value="<string name='Buildings/SistersOfBattle/HousingDescription'/>"/>
	<entry name="SistersOfBattle/HousingFlavor" value="<string name='Buildings/SistersOfBattle/HousingFlavor'/>"/>
	<entry name="SistersOfBattle/Loyalty" value="<string name='Buildings/SistersOfBattle/Loyalty'/>"/>
	<entry name="SistersOfBattle/LoyaltyDescription" value="<string name='Buildings/SistersOfBattle/LoyaltyDescription'/>"/>
	<entry name="SistersOfBattle/LoyaltyFlavor" value="<string name='Buildings/SistersOfBattle/LoyaltyFlavor'/>"/>
	<entry name="SistersOfBattle/Vehicles" value="<string name='Buildings/SistersOfBattle/Vehicles'/>"/>
	<entry name="SistersOfBattle/VehiclesDescription" value="<string name='Buildings/SistersOfBattle/VehiclesDescription'/>"/>
	<entry name="SistersOfBattle/VehiclesFlavor" value="<string name='Buildings/SistersOfBattle/VehiclesFlavor'/>"/>	
	<entry name="SpaceMarines/Aircraft" value="<string name='Buildings/SpaceMarines/Aircraft'/>"/>
	<entry name="SpaceMarines/AircraftDescription" value="<string name='Buildings/SpaceMarines/AircraftDescription'/>"/>
	<entry name="SpaceMarines/AircraftFlavor" value="<string name='Buildings/SpaceMarines/AircraftFlavor'/>"/>
	<entry name="SpaceMarines/Construction" value="<string name='Buildings/SpaceMarines/Construction'/>"/>
	<entry name="SpaceMarines/ConstructionDescription" value="<string name='Buildings/SpaceMarines/ConstructionDescription'/>"/>
	<entry name="SpaceMarines/ConstructionFlavor" value="<string name='Buildings/SpaceMarines/ConstructionFlavor'/>"/>
	<entry name="SpaceMarines/GeneseedBunker" value="<string name='Buildings/SpaceMarines/GeneseedBunker'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerDescription" value="<string name='Buildings/SpaceMarines/GeneseedBunkerDescription'/>"/>
	<entry name="SpaceMarines/GeneseedBunkerFlavor" value="<string name='Buildings/SpaceMarines/GeneseedBunkerFlavor'/>"/>
	<entry name="SpaceMarines/Heroes" value="<string name='Buildings/SpaceMarines/Heroes'/>"/>
	<entry name="SpaceMarines/HeroesDescription" value="<string name='Buildings/SpaceMarines/HeroesDescription'/>"/>
	<entry name="SpaceMarines/HeroesFlavor" value="<string name='Buildings/SpaceMarines/HeroesFlavor'/>"/>
	<entry name="SpaceMarines/Housing" value="<string name='Buildings/SpaceMarines/Housing'/>"/>
	<entry name="SpaceMarines/HousingDescription" value="<string name='Buildings/SpaceMarines/HousingDescription'/>"/>
	<entry name="SpaceMarines/HousingFlavor" value="<string name='Buildings/SpaceMarines/HousingFlavor'/>"/>
	<entry name="SpaceMarines/Loyalty" value="<string name='Buildings/SpaceMarines/Loyalty'/>"/>
	<entry name="SpaceMarines/LoyaltyDescription" value="<string name='Buildings/SpaceMarines/LoyaltyDescription'/>"/>
	<entry name="SpaceMarines/LoyaltyFlavor" value="<string name='Buildings/SpaceMarines/LoyaltyFlavor'/>"/>
	<entry name="SpaceMarines/Vehicles" value="<string name='Buildings/SpaceMarines/Vehicles'/>"/>
	<entry name="SpaceMarines/VehiclesDescription" value="<string name='Buildings/SpaceMarines/VehiclesDescription'/>"/>
	<entry name="SpaceMarines/VehiclesFlavor" value="<string name='Buildings/SpaceMarines/VehiclesFlavor'/>"/>
	<entry name="Tau/Aircraft" value="<string name='Buildings/Tau/Aircraft'/>"/>
	<entry name="Tau/AircraftDescription" value="<string name='Buildings/Tau/AircraftDescription'/>"/>
	<entry name="Tau/AircraftFlavor" value="<string name='Buildings/Tau/AircraftFlavor'/>"/>
	<entry name="Tau/Construction" value="<string name='Buildings/Tau/Construction'/>"/>
	<entry name="Tau/ConstructionDescription" value="<string name='Buildings/Tau/ConstructionDescription'/>"/>
	<entry name="Tau/ConstructionFlavor" value="<string name='Buildings/Tau/ConstructionFlavor'/>"/>
	<entry name="Tau/Heroes" value="<string name='Buildings/Tau/Heroes'/>"/>
	<entry name="Tau/HeroesDescription" value="<string name='Buildings/Tau/HeroesDescription'/>"/>
	<entry name="Tau/HeroesFlavor" value="<string name='Buildings/Tau/HeroesFlavor'/>"/>
	<entry name="Tau/Housing" value="<string name='Buildings/Tau/Housing'/>"/>
	<entry name="Tau/HousingDescription" value="<string name='Buildings/Tau/HousingDescription'/>"/>
	<entry name="Tau/HousingFlavor" value="<string name='Buildings/Tau/HousingFlavor'/>"/>
	<entry name="Tau/Loyalty" value="<string name='Buildings/Tau/Loyalty'/>"/>
	<entry name="Tau/LoyaltyDescription" value="<string name='Buildings/Tau/LoyaltyDescription'/>"/>
	<entry name="Tau/LoyaltyFlavor" value="<string name='Buildings/Tau/LoyaltyFlavor'/>"/>
	<entry name="Tau/MonstrousCreatures" value="<string name='Buildings/Tau/MonstrousCreatures'/>"/>
	<entry name="Tau/MonstrousCreaturesDescription" value="<string name='Buildings/Tau/MonstrousCreaturesDescription'/>"/>
	<entry name="Tau/MonstrousCreaturesFlavor" value="<string name='Buildings/Tau/MonstrousCreaturesFlavor'/>"/>
	<entry name="Tau/Vehicles" value="<string name='Buildings/Tau/Vehicles'/>"/>
	<entry name="Tau/VehiclesDescription" value="<string name='Buildings/Tau/VehiclesDescription'/>"/>
	<entry name="Tau/VehiclesFlavor" value="<string name='Buildings/Tau/VehiclesFlavor'/>"/>
	<entry name="Tyranids/Aircraft" value="<string name='Buildings/Tyranids/Aircraft'/>"/>
	<entry name="Tyranids/AircraftDescription" value="<string name='Buildings/Tyranids/AircraftDescription'/>"/>
	<entry name="Tyranids/AircraftFlavor" value="<string name='Buildings/Tyranids/AircraftFlavor'/>"/>
	<entry name="Tyranids/Construction" value="<string name='Buildings/Tyranids/Construction'/>"/>
	<entry name="Tyranids/ConstructionDescription" value="<string name='Buildings/Tyranids/ConstructionDescription'/>"/>
	<entry name="Tyranids/ConstructionFlavor" value="<string name='Buildings/Tyranids/ConstructionFlavor'/>"/>
	<entry name="Tyranids/Heroes" value="<string name='Buildings/Tyranids/Heroes'/>"/>
	<entry name="Tyranids/HeroesDescription" value="<string name='Buildings/Tyranids/HeroesDescription'/>"/>
	<entry name="Tyranids/HeroesFlavor" value="<string name='Buildings/Tyranids/HeroesFlavor'/>"/>
	<entry name="Tyranids/Housing" value="<string name='Buildings/Tyranids/Housing'/>"/>
	<entry name="Tyranids/HousingDescription" value="<string name='Buildings/Tyranids/HousingDescription'/>"/>
	<entry name="Tyranids/HousingFlavor" value="<string name='Buildings/Tyranids/HousingFlavor'/>"/>
	<entry name="Tyranids/Loyalty" value="<string name='Buildings/Tyranids/Loyalty'/>"/>
	<entry name="Tyranids/LoyaltyDescription" value="<string name='Buildings/Tyranids/LoyaltyDescription'/>"/>
	<entry name="Tyranids/LoyaltyFlavor" value="<string name='Buildings/Tyranids/LoyaltyFlavor'/>"/>
	<entry name="Tyranids/Thropes" value="<string name='Buildings/Tyranids/Thropes'/>"/>
	<entry name="Tyranids/ThropesDescription" value="<string name='Buildings/Tyranids/ThropesDescription'/>"/>
	<entry name="Tyranids/ThropesFlavor" value="<string name='Buildings/Tyranids/ThropesFlavor'/>"/>
	<entry name="Tyranids/Vehicles" value="<string name='Buildings/Tyranids/Vehicles'/>"/>
	<entry name="Tyranids/VehiclesDescription" value="<string name='Buildings/Tyranids/VehiclesDescription'/>"/>
	<entry name="Tyranids/VehiclesFlavor" value="<string name='Buildings/Tyranids/VehiclesFlavor'/>"/>
	
	<!-- Edicts -->
	<entry name="AstraMilitarum/AircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictDescription" value="<string name='Actions/AstraMilitarumAircraftProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/AircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/DefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarum/DefenseEdictDescription" value="<string name='Actions/AstraMilitarumDefenseEdictDescription'/>"/>
	<entry name="AstraMilitarum/DefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarum/EnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarum/EnergyEdictDescription" value="<string name='Actions/AstraMilitarumEnergyEdictDescription'/>"/>
	<entry name="AstraMilitarum/EnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/FoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarum/FoodEdictDescription" value="<string name='Actions/AstraMilitarumFoodEdictDescription'/>"/>
	<entry name="AstraMilitarum/FoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarum/GrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarum/GrowthEdictDescription" value="<string name='Actions/AstraMilitarumGrowthEdictDescription'/>"/>
	<entry name="AstraMilitarum/GrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictDescription" value="<string name='Actions/AstraMilitarumInfantryProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/InfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictDescription" value="<string name='Actions/AstraMilitarumLoyaltyEdictDescription'/>"/>
	<entry name="AstraMilitarum/LoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarum/OreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarum/OreEdictDescription" value="<string name='Actions/AstraMilitarumOreEdictDescription'/>"/>
	<entry name="AstraMilitarum/OreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictDescription" value="<string name='Actions/AstraMilitarumPsykerProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/PsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/ResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarum/ResearchEdictDescription" value="<string name='Actions/AstraMilitarumResearchEdictDescription'/>"/>
	<entry name="AstraMilitarum/ResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictDescription" value="<string name='Actions/AstraMilitarumVehicleProductionEdictDescription'/>"/>
	<entry name="AstraMilitarum/VehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarum/RogalDornBattleTank" value="<string name='Units/AstraMilitarum/RogalDornBattleTank'/>"/>
 	<entry name="AstraMilitarum/RogalDornBattleTankDescription" value="<string name='Units/AstraMilitarum/RogalDornBattleTankDescription'/>"/>
 	<entry name="AstraMilitarum/RogalDornBattleTankFlavor" value="<string name='Units/AstraMilitarum/RogalDornBattleTankFlavor'/>"/>
		
	<!-- Units -->
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptor'/>"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorDescription" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorDescription'/>"/>
	<entry name="AdeptusMechanicus/ArchaeopterStratoraptorFlavor" value="<string name='Units/AdeptusMechanicus/ArchaeopterStratoraptorFlavor'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriest" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriest'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestDescription" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestDescription'/>"/>
	<entry name="AdeptusMechanicus/FulguriteElectroPriestFlavor" value="<string name='Units/AdeptusMechanicus/FulguriteElectroPriestFlavor'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobot" value="<string name='Units/Neutral/KastelanRobot'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobotDescription" value="<string name='Units/Neutral/KastelanRobotDescription'/>"/>
	<entry name="AdeptusMechanicus/KastelanRobotFlavor" value="<string name='Units/Neutral/KastelanRobotFlavor'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyer" value="<string name='Units/AdeptusMechanicus/KataphronDestroyer'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerDescription" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerDescription'/>"/>
	<entry name="AdeptusMechanicus/KataphronDestroyerFlavor" value="<string name='Units/AdeptusMechanicus/KataphronDestroyerFlavor'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusader" value="<string name='Units/AdeptusMechanicus/KnightCrusader'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusaderDescription" value="<string name='Units/AdeptusMechanicus/KnightCrusaderDescription'/>"/>
	<entry name="AdeptusMechanicus/KnightCrusaderFlavor" value="<string name='Units/AdeptusMechanicus/KnightCrusaderFlavor'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawler" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawler'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerDescription" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerDescription'/>"/>
	<entry name="AdeptusMechanicus/OnagerDunecrawlerFlavor" value="<string name='Units/AdeptusMechanicus/OnagerDunecrawlerFlavor'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizor'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorDescription" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorDescription'/>"/>
	<entry name="AdeptusMechanicus/PteraxiiSterylizorFlavor" value="<string name='Units/AdeptusMechanicus/PteraxiiSterylizorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhound" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhound'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundDescription" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundDescription'/>"/>
	<entry name="AdeptusMechanicus/SerberysSulphurhoundFlavor" value="<string name='Units/AdeptusMechanicus/SerberysSulphurhoundFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltrator" value="<string name='Units/AdeptusMechanicus/SicarianInfiltrator'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorDescription" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorDescription'/>"/>
	<entry name="AdeptusMechanicus/SicarianInfiltratorFlavor" value="<string name='Units/AdeptusMechanicus/SicarianInfiltratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SicarianRuststalker" value="<string name='Units/AdeptusMechanicus/SicarianRuststalker'/>"/>
 	<entry name="AdeptusMechanicus/SicarianRuststalkerDescription" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerDescription'/>"/>
 	<entry name="AdeptusMechanicus/SicarianRuststalkerFlavor" value="<string name='Units/AdeptusMechanicus/SicarianRuststalkerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshal" value="<string name='Units/AdeptusMechanicus/SkitariiMarshal'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalDescription" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiMarshalFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiMarshalFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRanger" value="<string name='Units/AdeptusMechanicus/SkitariiRanger'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerDescription" value="<string name='Units/AdeptusMechanicus/SkitariiRangerDescription'/>"/>
	<entry name="AdeptusMechanicus/SkitariiRangerFlavor" value="<string name='Units/AdeptusMechanicus/SkitariiRangerFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegrator" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegrator'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorDescription'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDisintegratorFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDisintegratorFlavor'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDunerider" value="<string name='Units/AdeptusMechanicus/SkorpiusDunerider'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderDescription" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderDescription'/>"/>
	<entry name="AdeptusMechanicus/SkorpiusDuneriderFlavor" value="<string name='Units/AdeptusMechanicus/SkorpiusDuneriderFlavor'/>"/>
	<entry name="AdeptusMechanicus/SydonianDragoon" value="<string name='Units/AdeptusMechanicus/SydonianDragoon'/>"/>
 	<entry name="AdeptusMechanicus/SydonianDragoonDescription" value="<string name='Units/AdeptusMechanicus/SydonianDragoonDescription'/>"/>
 	<entry name="AdeptusMechanicus/SydonianDragoonFlavor" value="<string name='Units/AdeptusMechanicus/SydonianDragoonFlavor'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominus" value="<string name='Units/AdeptusMechanicus/TechPriestDominus'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestDominusDescription'/>"/>
	<entry name="AdeptusMechanicus/TechPriestDominusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestDominusFlavor'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulus" value="<string name='Units/AdeptusMechanicus/TechPriestManipulus'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusDescription" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusDescription'/>"/>
	<entry name="AdeptusMechanicus/TechPriestManipulusFlavor" value="<string name='Units/AdeptusMechanicus/TechPriestManipulusFlavor'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRider" value="<string name='Units/AstraMilitarum/AttilanRoughRider'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderDescription" value="<string name='Units/AstraMilitarum/AttilanRoughRiderDescription'/>"/>
	<entry name="AstraMilitarum/AttilanRoughRiderFlavor" value="<string name='Units/AstraMilitarum/AttilanRoughRiderFlavor'/>"/>
	<entry name="AstraMilitarum/Baneblade" value="<string name='Units/AstraMilitarum/Baneblade'/>"/>
	<entry name="AstraMilitarum/BanebladeDescription" value="<string name='Units/AstraMilitarum/BanebladeDescription'/>"/>
	<entry name="AstraMilitarum/BanebladeFlavor" value="<string name='Units/AstraMilitarum/BanebladeFlavor'/>"/>
	<entry name="AstraMilitarum/Basilisk" value="<string name='Units/AstraMilitarum/Basilisk'/>"/>
	<entry name="AstraMilitarum/BasiliskDescription" value="<string name='Units/AstraMilitarum/BasiliskDescription'/>"/>
	<entry name="AstraMilitarum/BasiliskFlavor" value="<string name='Units/AstraMilitarum/BasiliskFlavor'/>"/>
	<entry name="AstraMilitarum/Bullgryn" value="<string name='Units/AstraMilitarum/Bullgryn'/>"/>
	<entry name="AstraMilitarum/BullgrynDescription" value="<string name='Units/AstraMilitarum/BullgrynDescription'/>"/>
	<entry name="AstraMilitarum/BullgrynFlavor" value="<string name='Units/AstraMilitarum/BullgrynFlavor'/>"/>
	<entry name="AstraMilitarum/Chimera" value="<string name='Units/AstraMilitarum/Chimera'/>"/>
	<entry name="AstraMilitarum/ChimeraDescription" value="<string name='Units/AstraMilitarum/ChimeraDescription'/>"/>
	<entry name="AstraMilitarum/ChimeraFlavor" value="<string name='Units/AstraMilitarum/ChimeraFlavor'/>"/>
	<entry name="AstraMilitarum/DevilDog" value="<string name='Units/AstraMilitarum/DevilDog'/>"/>
	<entry name="AstraMilitarum/DevilDogDescription" value="<string name='Units/AstraMilitarum/DevilDogDescription'/>"/>
	<entry name="AstraMilitarum/DevilDogFlavor" value="<string name='Units/AstraMilitarum/DevilDogFlavor'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquad" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquad'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadDescription" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadDescription'/>"/>
	<entry name="AstraMilitarum/HeavyWeaponsSquadFlavor" value="<string name='Units/AstraMilitarum/HeavyWeaponsSquadFlavor'/>"/>
	<entry name="AstraMilitarum/Hydra" value="<string name='Units/AstraMilitarum/Hydra'/>"/>
	<entry name="AstraMilitarum/HydraDescription" value="<string name='Units/AstraMilitarum/HydraDescription'/>"/>
	<entry name="AstraMilitarum/HydraFlavor" value="<string name='Units/AstraMilitarum/HydraFlavor'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTank" value="<string name='Units/AstraMilitarum/LemanRussBattleTank'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankDescription" value="<string name='Units/AstraMilitarum/LemanRussBattleTankDescription'/>"/>
	<entry name="AstraMilitarum/LemanRussBattleTankFlavor" value="<string name='Units/AstraMilitarum/LemanRussBattleTankFlavor'/>"/>
	<entry name="AstraMilitarum/LordCommissar" value="<string name='Units/AstraMilitarum/LordCommissar'/>"/>
	<entry name="AstraMilitarum/LordCommissarDescription" value="<string name='Units/AstraMilitarum/LordCommissarDescription'/>"/>
	<entry name="AstraMilitarum/LordCommissarFlavor" value="<string name='Units/AstraMilitarum/LordCommissarFlavor'/>"/>
	<entry name="AstraMilitarum/MarauderBomber" value="<string name='Units/AstraMilitarum/MarauderBomber'/>"/>
	<entry name="AstraMilitarum/MarauderBomberDescription" value="<string name='Units/AstraMilitarum/MarauderBomberDescription'/>"/>
	<entry name="AstraMilitarum/MarauderBomberFlavor" value="<string name='Units/AstraMilitarum/MarauderBomberFlavor'/>"/>
	<entry name="AstraMilitarum/PrimarisPsyker" value="<string name='Units/AstraMilitarum/PrimarisPsyker'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerDescription" value="<string name='Units/AstraMilitarum/PrimarisPsykerDescription'/>"/>
	<entry name="AstraMilitarum/PrimarisPsykerFlavor" value="<string name='Units/AstraMilitarum/PrimarisPsykerFlavor'/>"/>
	<entry name="AstraMilitarum/Ratling" value="<string name='Units/AstraMilitarum/Ratling'/>"/>
 	<entry name="AstraMilitarum/RatlingDescription" value="<string name='Units/AstraMilitarum/RatlingDescription'/>"/>
 	<entry name="AstraMilitarum/RatlingFlavor" value="<string name='Units/AstraMilitarum/RatlingFlavor'/>"/>
	<entry name="AstraMilitarum/ScoutSentinel" value="<string name='Units/AstraMilitarum/ScoutSentinel'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelDescription" value="<string name='Units/AstraMilitarum/ScoutSentinelDescription'/>"/>
	<entry name="AstraMilitarum/ScoutSentinelFlavor" value="<string name='Units/AstraMilitarum/ScoutSentinelFlavor'/>"/>
	<entry name="AstraMilitarum/FieldOrdnanceBattery" value="<string name='Units/AstraMilitarum/FieldOrdnanceBattery'/>"/>
 	<entry name="AstraMilitarum/FieldOrdnanceBatteryDescription" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryDescription'/>"/>
 	<entry name="AstraMilitarum/FieldOrdnanceBatteryFlavor" value="<string name='Units/AstraMilitarum/FieldOrdnanceBatteryFlavor'/>"/>
	<entry name="AstraMilitarum/TankCommander" value="<string name='Units/AstraMilitarum/TankCommander'/>"/>
	<entry name="AstraMilitarum/TankCommanderDescription" value="<string name='Units/AstraMilitarum/TankCommanderDescription'/>"/>
	<entry name="AstraMilitarum/TankCommanderFlavor" value="<string name='Units/AstraMilitarum/TankCommanderFlavor'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseer" value="<string name='Units/AstraMilitarum/TechpriestEnginseer'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerDescription" value="<string name='Units/AstraMilitarum/TechpriestEnginseerDescription'/>"/>
	<entry name="AstraMilitarum/TechpriestEnginseerFlavor" value="<string name='Units/AstraMilitarum/TechpriestEnginseerFlavor'/>"/>
	<entry name="AstraMilitarum/TempestusScion" value="<string name='Units/AstraMilitarum/TempestusScion'/>"/>
	<entry name="AstraMilitarum/TempestusScionDescription" value="<string name='Units/AstraMilitarum/TempestusScionDescription'/>"/>
	<entry name="AstraMilitarum/TempestusScionFlavor" value="<string name='Units/AstraMilitarum/TempestusScionFlavor'/>"/>
	<entry name="AstraMilitarum/Thunderbolt" value="<string name='Units/AstraMilitarum/Thunderbolt'/>"/>
	<entry name="AstraMilitarum/ThunderboltDescription" value="<string name='Units/AstraMilitarum/ThunderboltDescription'/>"/>
	<entry name="AstraMilitarum/ThunderboltFlavor" value="<string name='Units/AstraMilitarum/ThunderboltFlavor'/>"/>
	<entry name="AstraMilitarum/Valkyrie" value="<string name='Units/AstraMilitarum/Valkyrie'/>"/>
	<entry name="AstraMilitarum/ValkyrieDescription" value="<string name='Units/AstraMilitarum/ValkyrieDescription'/>"/>
	<entry name="AstraMilitarum/ValkyrieFlavor" value="<string name='Units/AstraMilitarum/ValkyrieFlavor'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsyker" value="<string name='Units/AstraMilitarum/WyrdvanePsyker'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerDescription" value="<string name='Units/AstraMilitarum/WyrdvanePsykerDescription'/>"/>
	<entry name="AstraMilitarum/WyrdvanePsykerFlavor" value="<string name='Units/AstraMilitarum/WyrdvanePsykerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaider" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaider'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderDescription" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosLandRaiderFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosLandRaiderFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawn" value="<string name='Units/ChaosSpaceMarines/ChaosSpawn'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnDescription" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnDescription'/>"/>
	<entry name="ChaosSpaceMarines/ChaosSpawnFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosSpawnFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosTerminator" value="<string name='Units/ChaosSpaceMarines/ChaosTerminator'/>"/>
 	<entry name="ChaosSpaceMarines/ChaosTerminatorDescription" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorDescription'/>"/>
 	<entry name="ChaosSpaceMarines/ChaosTerminatorFlavor" value="<string name='Units/ChaosSpaceMarines/ChaosTerminatorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrince" value="<string name='Units/ChaosSpaceMarines/DaemonPrince'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceDescription" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceDescription'/>"/>
	<entry name="ChaosSpaceMarines/DaemonPrinceFlavor" value="<string name='Units/ChaosSpaceMarines/DaemonPrinceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DarkDisciple" value="<string name='Units/ChaosSpaceMarines/DarkDisciple'/>"/>
 	<entry name="ChaosSpaceMarines/DarkDiscipleDescription" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleDescription'/>"/>
 	<entry name="ChaosSpaceMarines/DarkDiscipleFlavor" value="<string name='Units/ChaosSpaceMarines/DarkDiscipleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Defiler" value="<string name='Units/ChaosSpaceMarines/Defiler'/>"/>
	<entry name="ChaosSpaceMarines/DefilerDescription" value="<string name='Units/ChaosSpaceMarines/DefilerDescription'/>"/>
	<entry name="ChaosSpaceMarines/DefilerFlavor" value="<string name='Units/ChaosSpaceMarines/DefilerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Forgefiend" value="<string name='Units/ChaosSpaceMarines/Forgefiend'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendDescription" value="<string name='Units/ChaosSpaceMarines/ForgefiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/ForgefiendFlavor" value="<string name='Units/ChaosSpaceMarines/ForgefiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Havoc" value="<string name='Units/ChaosSpaceMarines/Havoc'/>"/>
	<entry name="ChaosSpaceMarines/HavocDescription" value="<string name='Units/ChaosSpaceMarines/HavocDescription'/>"/>
	<entry name="ChaosSpaceMarines/HavocFlavor" value="<string name='Units/ChaosSpaceMarines/HavocFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Helbrute" value="<string name='Units/ChaosSpaceMarines/Helbrute'/>"/>
	<entry name="ChaosSpaceMarines/HelbruteDescription" value="<string name='Units/ChaosSpaceMarines/HelbruteDescription'/>"/>
	<entry name="ChaosSpaceMarines/HelbruteFlavor" value="<string name='Units/ChaosSpaceMarines/HelbruteFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerker" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerker'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerDescription" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerDescription'/>"/>
	<entry name="ChaosSpaceMarines/KhorneBerzerkerFlavor" value="<string name='Units/ChaosSpaceMarines/KhorneBerzerkerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossession" value="<string name='Units/ChaosSpaceMarines/MasterOfPossession'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionDescription" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionDescription'/>"/>
	<entry name="ChaosSpaceMarines/MasterOfPossessionFlavor" value="<string name='Units/ChaosSpaceMarines/MasterOfPossessionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Maulerfiend" value="<string name='Units/ChaosSpaceMarines/Maulerfiend'/>"/>
	<entry name="ChaosSpaceMarines/MaulerfiendDescription" value="<string name='Units/ChaosSpaceMarines/MaulerfiendDescription'/>"/>
	<entry name="ChaosSpaceMarines/MaulerfiendFlavor" value="<string name='Units/ChaosSpaceMarines/MaulerfiendFlavor'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrown" value="<string name='Units/ChaosSpaceMarines/NoctilithCrown'/>"/>
	<entry name="ChaosSpaceMarines/NoctilithCrownDescription" value="Confère aux Cultistes du Chaos la capacité de construire une fortification."/>
	<entry name="ChaosSpaceMarines/NoctilithCrownFlavor" value="<string name='Units/ChaosSpaceMarines/NoctilithCrownFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Obliterator" value="<string name='Units/ChaosSpaceMarines/Obliterator'/>"/>
	<entry name="ChaosSpaceMarines/ObliteratorDescription" value="<string name='Units/ChaosSpaceMarines/ObliteratorDescription'/>"/>
	<entry name="ChaosSpaceMarines/ObliteratorFlavor" value="<string name='Units/ChaosSpaceMarines/ObliteratorFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarine" value="<string name='Units/ChaosSpaceMarines/RubricMarine'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineDescription" value="<string name='Units/ChaosSpaceMarines/RubricMarineDescription'/>"/>
	<entry name="ChaosSpaceMarines/RubricMarineFlavor" value="<string name='Units/ChaosSpaceMarines/RubricMarineFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Venomcrawler" value="<string name='Units/ChaosSpaceMarines/Venomcrawler'/>"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerDescription" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerDescription'/>"/>
	<entry name="ChaosSpaceMarines/VenomcrawlerFlavor" value="<string name='Units/ChaosSpaceMarines/VenomcrawlerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Warpsmith" value="<string name='Units/ChaosSpaceMarines/Warpsmith'/>"/>
	<entry name="ChaosSpaceMarines/WarpsmithDescription" value="<string name='Units/ChaosSpaceMarines/WarpsmithDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpsmithFlavor" value="<string name='Units/ChaosSpaceMarines/WarpsmithFlavor'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalon" value="<string name='Units/ChaosSpaceMarines/WarpTalon'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonDescription" value="<string name='Units/ChaosSpaceMarines/WarpTalonDescription'/>"/>
	<entry name="ChaosSpaceMarines/WarpTalonFlavor" value="<string name='Units/ChaosSpaceMarines/WarpTalonFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorne" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorne'/>"/>
 	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneDescription'/>"/>
 	<entry name="ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor" value="<string name='Units/ChaosSpaceMarines/GreaterBrassScorpionOfKhorneFlavor'/>"/>
	<entry name="Drukhari/Cronos" value="<string name='Units/Drukhari/Cronos'/>"/>
    <entry name="Drukhari/CronosDescription" value="<string name='Units/Drukhari/CronosDescription'/>"/>
    <entry name="Drukhari/CronosFlavor" value="<string name='Units/Drukhari/CronosFlavor'/>"/>
    <entry name="Drukhari/Haemonculus" value="<string name='Units/Drukhari/Haemonculus'/>"/>
    <entry name="Drukhari/HaemonculusDescription" value="<string name='Units/Drukhari/HaemonculusDescription'/>"/>
    <entry name="Drukhari/HaemonculusFlavor" value="<string name='Units/Drukhari/HaemonculusFlavor'/>"/>
    <entry name="Drukhari/Hellion" value="<string name='Units/Drukhari/Hellion'/>"/>
    <entry name="Drukhari/HellionDescription" value="<string name='Units/Drukhari/HellionDescription'/>"/>
    <entry name="Drukhari/HellionFlavor" value="<string name='Units/Drukhari/HellionFlavor'/>"/>
    <entry name="Drukhari/Incubi" value="<string name='Units/Drukhari/Incubi'/>"/>
    <entry name="Drukhari/IncubiDescription" value="<string name='Units/Drukhari/IncubiDescription'/>"/>
    <entry name="Drukhari/IncubiFlavor" value="<string name='Units/Drukhari/IncubiFlavor'/>"/>
    <entry name="Drukhari/KabaliteTrueborn" value="<string name='Units/Drukhari/KabaliteTrueborn'/>"/>
    <entry name="Drukhari/KabaliteTruebornDescription" value="<string name='Units/Drukhari/KabaliteTruebornDescription'/>"/>
    <entry name="Drukhari/KabaliteTruebornFlavor" value="<string name='Units/Drukhari/KabaliteTruebornFlavor'/>"/>
    <entry name="Drukhari/Mandrake" value="<string name='Units/Drukhari/Mandrake'/>"/>
 	<entry name="Drukhari/MandrakeDescription" value="<string name='Units/Drukhari/MandrakeDescription'/>"/>
 	<entry name="Drukhari/MandrakeFlavor" value="<string name='Units/Drukhari/MandrakeFlavor'/>"/>
	<entry name="Drukhari/NightShieldsDescription" value="Augmente la réduction des dégâts à distance des raiders, des ravageurs, des chasseurs Razorwing, des bombardiers Tantalus et Voidraven."/>
	<entry name="Drukhari/PainbringerDescription" value="Combat drugs grant feel no pain damage reduction."/>
	<entry name="Drukhari/Raider" value="<string name='Units/Drukhari/Raider'/>"/>
    <entry name="Drukhari/RaiderDescription" value="<string name='Units/Drukhari/RaiderDescription'/>"/>
    <entry name="Drukhari/RaiderFlavor" value="<string name='Units/Drukhari/RaiderFlavor'/>"/>
    <entry name="Drukhari/Ravager" value="<string name='Units/Drukhari/Ravager'/>"/>
    <entry name="Drukhari/RavagerDescription" value="<string name='Units/Drukhari/RavagerDescription'/>"/>
    <entry name="Drukhari/RavagerFlavor" value="<string name='Units/Drukhari/RavagerFlavor'/>"/>
    <entry name="Drukhari/Reaver" value="<string name='Units/Drukhari/Reaver'/>"/>
    <entry name="Drukhari/ReaverDescription" value="<string name='Units/Drukhari/ReaverDescription'/>"/>
    <entry name="Drukhari/ReaverFlavor" value="<string name='Units/Drukhari/ReaverFlavor'/>"/>
	<entry name="Drukhari/Scourge" value="<string name='Units/Drukhari/Scourge'/>"/>
    <entry name="Drukhari/ScourgeDescription" value="<string name='Units/Drukhari/ScourgeDescription'/>"/>
    <entry name="Drukhari/ScourgeFlavor" value="<string name='Units/Drukhari/ScourgeFlavor'/>"/>
    <entry name="Drukhari/Succubus" value="<string name='Units/Drukhari/Succubus'/>"/>
    <entry name="Drukhari/SuccubusDescription" value="<string name='Units/Drukhari/SuccubusDescription'/>"/>
    <entry name="Drukhari/SuccubusFlavor" value="<string name='Units/Drukhari/SuccubusFlavor'/>"/>
    <entry name="Drukhari/Talos" value="<string name='Units/Drukhari/Talos'/>"/>
 	<entry name="Drukhari/TalosDescription" value="<string name='Units/Drukhari/TalosDescription'/>"/>
 	<entry name="Drukhari/TalosFlavor" value="<string name='Units/Drukhari/TalosFlavor'/>"/>
	<entry name="Drukhari/Tantalus" value="<string name='Units/Drukhari/Tantalus'/>"/>
    <entry name="Drukhari/TantalusDescription" value="<string name='Units/Drukhari/TantalusDescription'/>"/>
    <entry name="Drukhari/TantalusFlavor" value="<string name='Units/Drukhari/TantalusFlavor'/>"/>
    <entry name="Drukhari/VoidravenBomber" value="<string name='Units/Drukhari/VoidravenBomber'/>"/>
    <entry name="Drukhari/VoidravenBomberDescription" value="<string name='Units/Drukhari/VoidravenBomberDescription'/>"/>
    <entry name="Drukhari/VoidravenBomberFlavor" value="<string name='Units/Drukhari/VoidravenBomberFlavor'/>"/>
    <entry name="Drukhari/Wrack" value="<string name='Units/Drukhari/Wrack'/>"/>
    <entry name="Drukhari/WrackDescription" value="<string name='Units/Drukhari/WrackDescription'/>"/>
    <entry name="Drukhari/WrackFlavor" value="<string name='Units/Drukhari/WrackFlavor'/>"/>
    <entry name="Drukhari/Wyche" value="<string name='Units/Drukhari/Wyche'/>"/>
    <entry name="Drukhari/WycheDescription" value="<string name='Units/Drukhari/WycheDescription'/>"/>
    <entry name="Drukhari/WycheFlavor" value="<string name='Units/Drukhari/WycheFlavor'/>"/>
	<entry name="Eldar/AvatarOfKhaine" value="<string name='Units/Eldar/AvatarOfKhaine'/>"/>
 	<entry name="Eldar/AvatarOfKhaineDescription" value="<string name='Units/Eldar/AvatarOfKhaineDescription'/>"/>
 	<entry name="Eldar/AvatarOfKhaineFlavor" value="<string name='Units/Eldar/AvatarOfKhaineFlavor'/>"/>
 	<entry name="Eldar/FarseerSkyrunner" value="<string name='Units/Eldar/FarseerSkyrunner'/>"/>
 	<entry name="Eldar/FarseerSkyrunnerDescription" value="<string name='Units/Eldar/FarseerSkyrunnerDescription'/>"/>
 	<entry name="Eldar/FarseerSkyrunnerFlavor" value="<string name='Units/Eldar/FarseerSkyrunnerFlavor'/>"/>
 	<entry name="Eldar/FireDragon" value="<string name='Units/Eldar/FireDragon'/>"/>
 	<entry name="Eldar/FireDragonDescription" value="<string name='Units/Eldar/FireDragonDescription'/>"/>
 	<entry name="Eldar/FireDragonFlavor" value="<string name='Units/Eldar/FireDragonFlavor'/>"/>
 	<entry name="Eldar/FirePrism" value="<string name='Units/Eldar/FirePrism'/>"/>
 	<entry name="Eldar/FirePrismDescription" value="<string name='Units/Eldar/FirePrismDescription'/>"/>
 	<entry name="Eldar/FirePrismFlavor" value="<string name='Units/Eldar/FirePrismFlavor'/>"/>
 	<entry name="Eldar/HemlockWraithfighter" value="<string name='Units/Eldar/HemlockWraithfighter'/>"/>
 	<entry name="Eldar/HemlockWraithfighterDescription" value="<string name='Units/Eldar/HemlockWraithfighterDescription'/>"/>
 	<entry name="Eldar/HemlockWraithfighterFlavor" value="<string name='Units/Eldar/HemlockWraithfighterFlavor'/>"/>
	<entry name="Eldar/Hornet" value="<string name='Units/Eldar/Hornet'/>"/>
	<entry name="Eldar/HornetDescription" value="<string name='Units/Eldar/HornetDescription'/>"/>
	<entry name="Eldar/HornetFlavor" value="<string name='Units/Eldar/HornetFlavor'/>"/>
 	<entry name="Eldar/HowlingBanshee" value="<string name='Units/Eldar/HowlingBanshee'/>"/>
 	<entry name="Eldar/HowlingBansheeDescription" value="<string name='Units/Eldar/HowlingBansheeDescription'/>"/>
 	<entry name="Eldar/HowlingBansheeFlavor" value="<string name='Units/Eldar/HowlingBansheeFlavor'/>"/>
 	<entry name="Eldar/Ranger" value="<string name='Units/Eldar/Ranger'/>"/>
 	<entry name="Eldar/RangerDescription" value="<string name='Units/Eldar/RangerDescription'/>"/>
 	<entry name="Eldar/RangerFlavor" value="<string name='Units/Eldar/RangerFlavor'/>"/>
 	<entry name="Eldar/Scorpion" value="<string name='Units/Eldar/Scorpion'/>"/>
 	<entry name="Eldar/ScorpionDescription" value="<string name='Units/Eldar/ScorpionDescription'/>"/>
 	<entry name="Eldar/ScorpionFlavor" value="<string name='Units/Eldar/ScorpionFlavor'/>"/>
 	<entry name="Eldar/Spiritseer" value="<string name='Units/Eldar/Spiritseer'/>"/>
 	<entry name="Eldar/SpiritseerDescription" value="<string name='Units/Eldar/SpiritseerDescription'/>"/>
 	<entry name="Eldar/SpiritseerFlavor" value="<string name='Units/Eldar/SpiritseerFlavor'/>"/>
 	<entry name="Eldar/VaulsWrathSupportBattery" value="<string name='Units/Eldar/VaulsWrathSupportBattery'/>"/>
 	<entry name="Eldar/VaulsWrathSupportBatteryDescription" value="<string name='Units/Eldar/VaulsWrathSupportBatteryDescription'/>"/>
 	<entry name="Eldar/VaulsWrathSupportBatteryFlavor" value="<string name='Units/Eldar/VaulsWrathSupportBatteryFlavor'/>"/>
 	<entry name="Eldar/Vyper" value="<string name='Units/Eldar/Vyper'/>"/>
 	<entry name="Eldar/VyperDescription" value="<string name='Units/Eldar/VyperDescription'/>"/>
 	<entry name="Eldar/VyperFlavor" value="<string name='Units/Eldar/VyperFlavor'/>"/>
	<entry name="Eldar/WarWalker" value="<string name='Units/Eldar/WarWalker'/>"/>
 	<entry name="Eldar/WarWalkerDescription" value="<string name='Units/Eldar/WarWalkerDescription'/>"/>
 	<entry name="Eldar/WarWalkerFlavor" value="<string name='Units/Eldar/WarWalkerFlavor'/>"/>
	<entry name="Eldar/Warlock" value="<string name='Units/Eldar/Warlock'/>"/>
 	<entry name="Eldar/WarlockDescription" value="<string name='Units/Eldar/WarlockDescription'/>"/>
 	<entry name="Eldar/WarlockFlavor" value="<string name='Units/Eldar/WarlockFlavor'/>"/>
 	<entry name="Eldar/WaveSerpent" value="<string name='Units/Eldar/WaveSerpent'/>"/>
 	<entry name="Eldar/WaveSerpentDescription" value="<string name='Units/Eldar/WaveSerpentDescription'/>"/>
 	<entry name="Eldar/WaveSerpentFlavor" value="<string name='Units/Eldar/WaveSerpentFlavor'/>"/>
 	<entry name="Eldar/Wraithblade" value="<string name='Units/Eldar/Wraithblade'/>"/>
 	<entry name="Eldar/WraithbladeDescription" value="<string name='Units/Eldar/WraithbladeDescription'/>"/>
 	<entry name="Eldar/WraithbladeFlavor" value="<string name='Units/Eldar/WraithbladeFlavor'/>"/>
 	<entry name="Eldar/Wraithknight" value="<string name='Units/Eldar/Wraithknight'/>"/>
 	<entry name="Eldar/WraithknightDescription" value="<string name='Units/Eldar/WraithknightDescription'/>"/>
 	<entry name="Eldar/WraithknightFlavor" value="<string name='Units/Eldar/WraithknightFlavor'/>"/>
	<entry name="Eldar/Wraithlord" value="<string name='Units/Eldar/Wraithlord'/>"/>
 	<entry name="Eldar/WraithlordDescription" value="<string name='Units/Eldar/WraithlordDescription'/>"/>
 	<entry name="Eldar/WraithlordFlavor" value="<string name='Units/Eldar/WraithlordFlavor'/>"/>
	<entry name="Eldar/DarkReaper" value="<string name='Units/Eldar/DarkReaper'/>"/>
 	<entry name="Eldar/DarkReaperDescription" value="<string name='Units/Eldar/DarkReaperDescription'/>"/>
 	<entry name="Eldar/DarkReaperFlavor" value="<string name='Units/Eldar/DarkReaperFlavor'/>"/>
	<entry name="Necrons/AnnihilationBarge" value="<string name='Units/Necrons/AnnihilationBarge'/>"/>
	<entry name="Necrons/AnnihilationBargeDescription" value="<string name='Units/Necrons/AnnihilationBargeDescription'/>"/>
	<entry name="Necrons/AnnihilationBargeFlavor" value="<string name='Units/Necrons/AnnihilationBargeFlavor'/>"/>
	<entry name="Necrons/CanoptekReanimator" value="<string name='Units/Necrons/CanoptekReanimator'/>"/>
 	<entry name="Necrons/CanoptekReanimatorDescription" value="<string name='Units/Necrons/CanoptekReanimatorDescription'/>"/>
 	<entry name="Necrons/CanoptekReanimatorFlavor" value="<string name='Units/Necrons/CanoptekReanimatorFlavor'/>"/>
	<entry name="Necrons/CanoptekSpyder" value="<string name='Units/Necrons/CanoptekSpyder'/>"/>
	<entry name="Necrons/CanoptekSpyderDescription" value="<string name='Units/Necrons/CanoptekSpyderDescription'/>"/>
	<entry name="Necrons/CanoptekSpyderFlavor" value="<string name='Units/Necrons/CanoptekSpyderFlavor'/>"/>
	<entry name="Necrons/CanoptekWraith" value="<string name='Units/Necrons/CanoptekWraith'/>"/>
 	<entry name="Necrons/CanoptekWraithDescription" value="<string name='Units/Necrons/CanoptekWraithDescription'/>"/>
 	<entry name="Necrons/CanoptekWraithFlavor" value="<string name='Units/Necrons/CanoptekWraithFlavor'/>"/>
	<entry name="Necrons/Cryptek" value="<string name='Units/Necrons/Cryptek'/>"/>
	<entry name="Necrons/CryptekDescription" value="<string name='Units/Necrons/CryptekDescription'/>"/>
	<entry name="Necrons/CryptekFlavor" value="<string name='Units/Necrons/CryptekFlavor'/>"/>
	<entry name="Necrons/DestroyerLord" value="<string name='Units/Necrons/DestroyerLord'/>"/>
	<entry name="Necrons/DestroyerLordDescription" value="<string name='Units/Necrons/DestroyerLordDescription'/>"/>
	<entry name="Necrons/DestroyerLordFlavor" value="<string name='Units/Necrons/DestroyerLordFlavor'/>"/>
	<entry name="Necrons/DoomScythe" value="<string name='Units/Necrons/DoomScythe'/>"/>
	<entry name="Necrons/DoomScytheDescription" value="<string name='Units/Necrons/DoomScytheDescription'/>"/>
	<entry name="Necrons/DoomScytheFlavor" value="<string name='Units/Necrons/DoomScytheFlavor'/>"/>
	<entry name="Necrons/DoomsdayArk" value="<string name='Units/Necrons/DoomsdayArk'/>"/>
	<entry name="Necrons/DoomsdayArkDescription" value="<string name='Units/Necrons/DoomsdayArkDescription'/>"/>
	<entry name="Necrons/DoomsdayArkFlavor" value="<string name='Units/Necrons/DoomsdayArkFlavor'/>"/>
	<entry name="Necrons/FlayedOne" value="<string name='Units/Necrons/FlayedOne'/>"/>
	<entry name="Necrons/FlayedOneDescription" value="<string name='Units/Necrons/FlayedOneDescription'/>"/>
	<entry name="Necrons/FlayedOneFlavor" value="<string name='Units/Necrons/FlayedOneFlavor'/>"/>
	<entry name="Necrons/GhostArk" value="<string name='Units/Necrons/GhostArk'/>"/>
	<entry name="Necrons/GhostArkDescription" value="<string name='Units/Necrons/GhostArkDescription'/>"/>
	<entry name="Necrons/GhostArkFlavor" value="<string name='Units/Necrons/GhostArkFlavor'/>"/>
	<entry name="Necrons/HeavyDestroyer" value="<string name='Units/Necrons/HeavyDestroyer'/>"/>
	<entry name="Necrons/HeavyDestroyerDescription" value="<string name='Units/Necrons/HeavyDestroyerDescription'/>"/>
	<entry name="Necrons/HeavyDestroyerFlavor" value="<string name='Units/Necrons/HeavyDestroyerFlavor'/>"/>
	<entry name="Necrons/Immortal" value="<string name='Units/Necrons/Immortal'/>"/>
	<entry name="Necrons/ImmortalDescription" value="<string name='Units/Necrons/ImmortalDescription'/>"/>
	<entry name="Necrons/ImmortalFlavor" value="<string name='Units/Necrons/ImmortalFlavor'/>"/>
	<entry name="Necrons/Lord" value="<string name='Units/Necrons/Lord'/>"/>
	<entry name="Necrons/LordDescription" value="<string name='Units/Necrons/LordDescription'/>"/>
	<entry name="Necrons/LordFlavor" value="<string name='Units/Necrons/LordFlavor'/>"/>
	<entry name="Necrons/Monolith" value="<string name='Units/Necrons/Monolith'/>"/>
	<entry name="Necrons/MonolithDescription" value="<string name='Units/Necrons/MonolithDescription'/>"/>
	<entry name="Necrons/MonolithFlavor" value="<string name='Units/Necrons/MonolithFlavor'/>"/>
	<entry name="Necrons/NightScythe" value="<string name='Units/Necrons/NightScythe'/>"/>
	<entry name="Necrons/NightScytheDescription" value="<string name='Units/Necrons/NightScytheDescription'/>"/>
	<entry name="Necrons/NightScytheFlavor" value="<string name='Units/Necrons/NightScytheFlavor'/>"/>
	<entry name="Necrons/Obelisk" value="<string name='Units/Necrons/Obelisk'/>"/>
	<entry name="Necrons/ObeliskDescription" value="<string name='Units/Necrons/ObeliskDescription'/>"/>
	<entry name="Necrons/ObeliskFlavor" value="<string name='Units/Necrons/ObeliskFlavor'/>"/>
	<entry name="Necrons/SkorpekhDestroyer" value="<string name='Units/Necrons/SkorpekhDestroyer'/>"/>
 	<entry name="Necrons/SkorpekhDestroyerDescription" value="<string name='Units/Necrons/SkorpekhDestroyerDescription'/>"/>
 	<entry name="Necrons/SkorpekhDestroyerFlavor" value="<string name='Units/Necrons/SkorpekhDestroyerFlavor'/>"/>
	<entry name="Necrons/TombBlade" value="<string name='Units/Necrons/TombBlade'/>"/>
	<entry name="Necrons/TombBladeDescription" value="<string name='Units/Necrons/TombBladeDescription'/>"/>
	<entry name="Necrons/TombBladeFlavor" value="<string name='Units/Necrons/TombBladeFlavor'/>"/>
	<entry name="Necrons/TranscendentCtan" value="<string name='Units/Necrons/TranscendentCtan'/>"/>
	<entry name="Necrons/TranscendentCtanDescription" value="<string name='Units/Necrons/TranscendentCtanDescription'/>"/>
	<entry name="Necrons/TranscendentCtanFlavor" value="<string name='Units/Necrons/TranscendentCtanFlavor'/>"/>
	<entry name="Necrons/TriarchPraetorian" value="<string name='Units/Necrons/TriarchPraetorian'/>"/>
	<entry name="Necrons/TriarchPraetorianDescription" value="<string name='Units/Necrons/TriarchPraetorianDescription'/>"/>
	<entry name="Necrons/TriarchPraetorianFlavor" value="<string name='Units/Necrons/TriarchPraetorianFlavor'/>"/>
	<entry name="Necrons/TriarchStalker" value="<string name='Units/Necrons/TriarchStalker'/>"/>
	<entry name="Necrons/TriarchStalkerDescription" value="<string name='Units/Necrons/TriarchStalkerDescription'/>"/>
	<entry name="Necrons/TriarchStalkerFlavor" value="<string name='Units/Necrons/TriarchStalkerFlavor'/>"/>
	<entry name="Necrons/Deathmark" value="<string name='Units/Necrons/Deathmark'/>"/>
 	<entry name="Necrons/DeathmarkDescription" value="<string name='Units/Necrons/DeathmarkDescription'/>"/>
 	<entry name="Necrons/DeathmarkFlavor" value="<string name='Units/Necrons/DeathmarkFlavor'/>"/>
	<entry name="Orks/Battlewagon" value="<string name='Units/Orks/Battlewagon'/>"/>
	<entry name="Orks/BattlewagonDescription" value="<string name='Units/Orks/BattlewagonDescription'/>"/>
	<entry name="Orks/BattlewagonFlavor" value="<string name='Units/Orks/BattlewagonFlavor'/>"/>
	<entry name="Orks/BigMek" value="<string name='Units/Orks/BigMek'/>"/>
	<entry name="Orks/BigMekDescription" value="<string name='Units/Orks/BigMekDescription'/>"/>
	<entry name="Orks/BigMekFlavor" value="<string name='Units/Orks/BigMekFlavor'/>"/>
	<entry name="Orks/BurnaBommer" value="<string name='Units/Orks/BurnaBommer'/>"/>
	<entry name="Orks/BurnaBommerDescription" value="<string name='Units/Orks/BurnaBommerDescription'/>"/>
	<entry name="Orks/BurnaBommerFlavor" value="<string name='Units/Orks/BurnaBommerFlavor'/>"/>
	<entry name="Orks/BurnaBoy" value="<string name='Units/Orks/BurnaBoy'/>"/>
 	<entry name="Orks/BurnaBoyDescription" value="<string name='Units/Orks/BurnaBoyDescription'/>"/>
 	<entry name="Orks/BurnaBoyFlavor" value="<string name='Units/Orks/BurnaBoyFlavor'/>"/>
	<entry name="Orks/Dakkajet" value="<string name='Units/Orks/Dakkajet'/>"/>
	<entry name="Orks/DakkajetDescription" value="<string name='Units/Orks/DakkajetDescription'/>"/>
	<entry name="Orks/DakkajetFlavor" value="<string name='Units/Orks/DakkajetFlavor'/>"/>
	<entry name="Orks/Deffkopta" value="<string name='Units/Orks/Deffkopta'/>"/>
	<entry name="Orks/DeffkoptaDescription" value="<string name='Units/Orks/DeffkoptaDescription'/>"/>
	<entry name="Orks/DeffkoptaFlavor" value="<string name='Units/Orks/DeffkoptaFlavor'/>"/>
	<entry name="Orks/FlashGitz" value="<string name='Units/Orks/FlashGitz'/>"/>
	<entry name="Orks/FlashGitzDescription" value="<string name='Units/Orks/FlashGitzDescription'/>"/>
	<entry name="Orks/FlashGitzFlavor" value="<string name='Units/Orks/FlashGitzFlavor'/>"/>
	<entry name="Orks/GargantuanSquiggoth" value="<string name='Units/Orks/GargantuanSquiggoth'/>"/>
	<entry name="Orks/GargantuanSquiggothDescription" value="<string name='Units/Orks/GargantuanSquiggothDescription'/>"/>
	<entry name="Orks/GargantuanSquiggothFlavor" value="<string name='Units/Orks/GargantuanSquiggothFlavor'/>"/>
	<entry name="Orks/Gorkanaut" value="<string name='Units/Orks/Gorkanaut'/>"/>
	<entry name="Orks/GorkanautDescription" value="<string name='Units/Orks/GorkanautDescription'/>"/>
	<entry name="Orks/GorkanautFlavor" value="<string name='Units/Orks/GorkanautFlavor'/>"/>
	<entry name="Orks/KillBursta" value="<string name='Units/Orks/KillBursta'/>"/>
 	<entry name="Orks/KillBurstaDescription" value="<string name='Units/Orks/KillBurstaDescription'/>"/>
 	<entry name="Orks/KillBurstaFlavor" value="<string name='Units/Orks/KillBurstaFlavor'/>"/>
	<entry name="Orks/KillaKan" value="<string name='Units/Orks/KillaKan'/>"/>
	<entry name="Orks/KillaKanDescription" value="<string name='Units/Orks/KillaKanDescription'/>"/>
	<entry name="Orks/KillaKanFlavor" value="<string name='Units/Orks/KillaKanFlavor'/>"/>
	<entry name="Orks/Meganob" value="<string name='Units/Orks/Meganob'/>"/>
	<entry name="Orks/MeganobDescription" value="<string name='Units/Orks/MeganobDescription'/>"/>
	<entry name="Orks/MeganobFlavor" value="<string name='Units/Orks/MeganobFlavor'/>"/>
	<entry name="Orks/MegatrakkScrapjet" value="<string name='Units/Orks/MegatrakkScrapjet'/>"/>
	<entry name="Orks/MegatrakkScrapjetDescription" value="<string name='Units/Orks/MegatrakkScrapjetDescription'/>"/>
	<entry name="Orks/MegatrakkScrapjetFlavor" value="<string name='Units/Orks/MegatrakkScrapjetFlavor'/>"/>
	<entry name="Orks/Mek" value="<string name='Units/Orks/Mek'/>"/>
	<entry name="Orks/MekDescription" value="<string name='Units/Orks/MekDescription'/>"/>
	<entry name="Orks/MekFlavor" value="<string name='Units/Orks/MekFlavor'/>"/>
	<entry name="Orks/MekGun" value="<string name='Units/Orks/MekGun'/>"/>
	<entry name="Orks/MekGunDescription" value="<string name='Units/Orks/MekGunDescription'/>"/>
	<entry name="Orks/MekGunFlavor" value="<string name='Units/Orks/MekGunFlavor'/>"/>
	<entry name="Orks/Painboy" value="<string name='Units/Orks/Painboy'/>"/>
	<entry name="Orks/PainboyDescription" value="<string name='Units/Orks/PainboyDescription'/>"/>
	<entry name="Orks/PainboyFlavor" value="<string name='Units/Orks/PainboyFlavor'/>"/>
	<entry name="Orks/SquighogBoy" value="<string name='Units/Orks/SquighogBoy'/>"/>
 	<entry name="Orks/SquighogBoyDescription" value="<string name='Units/Orks/SquighogBoyDescription'/>"/>
 	<entry name="Orks/SquighogBoyFlavor" value="<string name='Units/Orks/SquighogBoyFlavor'/>"/>
	<entry name="Orks/Tankbusta" value="<string name='Units/Orks/Tankbusta'/>"/>
	<entry name="Orks/TankbustaDescription" value="<string name='Units/Orks/TankbustaDescription'/>"/>
	<entry name="Orks/TankbustaFlavor" value="<string name='Units/Orks/TankbustaFlavor'/>"/>
	<entry name="Orks/Warbiker" value="<string name='Units/Orks/Warbiker'/>"/>
	<entry name="Orks/WarbikerDescription" value="<string name='Units/Orks/WarbikerDescription'/>"/>
	<entry name="Orks/WarbikerFlavor" value="<string name='Units/Orks/WarbikerFlavor'/>"/>
	<entry name="Orks/Warboss" value="<string name='Units/Orks/Warboss'/>"/>
	<entry name="Orks/WarbossDescription" value="<string name='Units/Orks/WarbossDescription'/>"/>
	<entry name="Orks/WarbossFlavor" value="<string name='Units/Orks/WarbossFlavor'/>"/>
	<entry name="Orks/Warbuggy" value="<string name='Units/Orks/Warbuggy'/>"/>
	<entry name="Orks/WarbuggyDescription" value="<string name='Units/Orks/WarbuggyDescription'/>"/>
	<entry name="Orks/WarbuggyFlavor" value="<string name='Units/Orks/WarbuggyFlavor'/>"/>
	<entry name="Orks/Weirdboy" value="<string name='Units/Orks/Weirdboy'/>"/>
	<entry name="Orks/WeirdboyDescription" value="<string name='Units/Orks/WeirdboyDescription'/>"/>
	<entry name="Orks/WeirdboyFlavor" value="<string name='Units/Orks/WeirdboyFlavor'/>"/>
	<entry name="Orks/DeffDread" value="<string name='Units/Orks/DeffDread'/>"/>
	<entry name="Orks/DeffDreadDescription" value="<string name='Units/Orks/DeffDreadDescription'/>"/>
 	<entry name="Orks/DeffDreadFlavor" value="<string name='Units/Orks/DeffDreadFlavor'/>"/>
	<entry name="SistersOfBattle/ArcoFlagellant" value="<string name='Units/SistersOfBattle/ArcoFlagellant'/>"/>
 	<entry name="SistersOfBattle/ArcoFlagellantDescription" value="<string name='Units/SistersOfBattle/ArcoFlagellantDescription'/>"/>
 	<entry name="SistersOfBattle/ArcoFlagellantFlavor" value="<string name='Units/SistersOfBattle/ArcoFlagellantFlavor'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighter" value="<string name='Units/SistersOfBattle/AvengerStrikeFighter'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterDescription" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterDescription'/>"/>
	<entry name="SistersOfBattle/AvengerStrikeFighterFlavor" value="<string name='Units/SistersOfBattle/AvengerStrikeFighterFlavor'/>"/>
	<entry name="SistersOfBattle/Castigator" value="<string name='Units/SistersOfBattle/Castigator'/>"/>
	<entry name="SistersOfBattle/CastigatorDescription" value="<string name='Units/SistersOfBattle/CastigatorDescription'/>"/>
	<entry name="SistersOfBattle/CastigatorFlavor" value="<string name='Units/SistersOfBattle/CastigatorFlavor'/>"/>
	<entry name="SistersOfBattle/CelestianSacresant" value="<string name='Units/SistersOfBattle/CelestianSacresant'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantDescription" value="<string name='Units/SistersOfBattle/CelestianSacresantDescription'/>"/>
	<entry name="SistersOfBattle/CelestianSacresantFlavor" value="<string name='Units/SistersOfBattle/CelestianSacresantFlavor'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancer" value="<string name='Units/SistersOfBattle/CerastusKnightLancer'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerDescription" value="<string name='Units/SistersOfBattle/CerastusKnightLancerDescription'/>"/>
	<entry name="SistersOfBattle/CerastusKnightLancerFlavor" value="<string name='Units/SistersOfBattle/CerastusKnightLancerFlavor'/>"/>
	<entry name="SistersOfBattle/Dialogus" value="<string name='Units/SistersOfBattle/Dialogus'/>"/>
	<entry name="SistersOfBattle/DialogusDescription" value="<string name='Units/SistersOfBattle/DialogusDescription'/>"/>
	<entry name="SistersOfBattle/DialogusFlavor" value="<string name='Units/SistersOfBattle/DialogusFlavor'/>"/>
	<entry name="SistersOfBattle/Dominion" value="<string name='Units/SistersOfBattle/Dominion'/>"/>
	<entry name="SistersOfBattle/DominionDescription" value="<string name='Units/SistersOfBattle/DominionDescription'/>"/>
	<entry name="SistersOfBattle/DominionFlavor" value="<string name='Units/SistersOfBattle/DominionFlavor'/>"/>
	<entry name="SistersOfBattle/Exorcist" value="<string name='Units/SistersOfBattle/Exorcist'/>"/>
	<entry name="SistersOfBattle/ExorcistDescription" value="<string name='Units/SistersOfBattle/ExorcistDescription'/>"/>
	<entry name="SistersOfBattle/ExorcistFlavor" value="<string name='Units/SistersOfBattle/ExorcistFlavor'/>"/>
	<entry name="SistersOfBattle/Headquarters" value="<string name='Buildings/SistersOfBattle/Headquarters'/>"/>
	<entry name="SistersOfBattle/HeadquartersDescription" value="<string name='Units/SistersOfBattle/HeadquartersDescription'/>"/>
	<entry name="SistersOfBattle/HeadquartersFlavor" value="<string name='Buildings/SistersOfBattle/HeadquartersFlavor'/>"/>
	<entry name="SistersOfBattle/Hospitaller" value="<string name='Units/SistersOfBattle/Hospitaller'/>"/>
	<entry name="SistersOfBattle/HospitallerDescription" value="<string name='Units/SistersOfBattle/HospitallerDescription'/>"/>
	<entry name="SistersOfBattle/HospitallerFlavor" value="<string name='Units/SistersOfBattle/HospitallerFlavor'/>"/>
	<entry name="SistersOfBattle/Mortifier" value="<string name='Units/SistersOfBattle/Mortifier'/>"/>
	<entry name="SistersOfBattle/MortifierDescription" value="<string name='Units/SistersOfBattle/MortifierDescription'/>"/>
	<entry name="SistersOfBattle/MortifierFlavor" value="<string name='Units/SistersOfBattle/MortifierFlavor'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="<string name='Units/SistersOfBattle/ParagonWarsuit'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="<string name='Units/SistersOfBattle/ParagonWarsuitDescription'/>"/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="<string name='Units/SistersOfBattle/ParagonWarsuitFlavor'/>"/>
	<entry name="SistersOfBattle/Retributor" value="<string name='Units/SistersOfBattle/Retributor'/>"/>
	<entry name="SistersOfBattle/RetributorDescription" value="<string name='Units/SistersOfBattle/RetributorDescription'/>"/>
	<entry name="SistersOfBattle/RetributorFlavor" value="<string name='Units/SistersOfBattle/RetributorFlavor'/>"/>
	<entry name="SistersOfBattle/SaintCelestine" value="<string name='Units/SistersOfBattle/SaintCelestine'/>"/>
	<entry name="SistersOfBattle/SaintCelestineDescription" value="<string name='Units/SistersOfBattle/SaintCelestineDescription'/>"/>
	<entry name="SistersOfBattle/SaintCelestineFlavor" value="<string name='Units/SistersOfBattle/SaintCelestineFlavor'/>"/>
	<entry name="SistersOfBattle/SisterRepentia" value="<string name='Units/SistersOfBattle/SisterRepentia'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaDescription" value="<string name='Units/SistersOfBattle/SisterRepentiaDescription'/>"/>
	<entry name="SistersOfBattle/SisterRepentiaFlavor" value="<string name='Units/SistersOfBattle/SisterRepentiaFlavor'/>"/>
	<entry name="SistersOfBattle/Zephyrim" value="<string name='Units/SistersOfBattle/Zephyrim'/>"/>
	<entry name="SistersOfBattle/ZephyrimDescription" value="<string name='Units/SistersOfBattle/ZephyrimDescription'/>"/>
	<entry name="SistersOfBattle/ZephyrimFlavor" value="<string name='Units/SistersOfBattle/ZephyrimFlavor'/>"/>
	<entry name="SistersOfBattle/Imagifier" value="<string name='Units/SistersOfBattle/Imagifier'/>"/>
 	<entry name="SistersOfBattle/ImagifierDescription" value="<string name='Units/SistersOfBattle/ImagifierDescription'/>"/>
 	<entry name="SistersOfBattle/ImagifierFlavor" value="<string name='Units/SistersOfBattle/ImagifierFlavor'/>"/>	
	<entry name="SpaceMarines/Apothecary" value="<string name='Units/SpaceMarines/Apothecary'/>"/>
	<entry name="SpaceMarines/ApothecaryDescription" value="<string name='Units/SpaceMarines/ApothecaryDescription'/>"/>
	<entry name="SpaceMarines/ApothecaryFlavor" value="<string name='Units/SpaceMarines/ApothecaryFlavor'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannon" value="<string name='Units/SpaceMarines/AquilaMacroCannon'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonDescription" value="<string name='Units/SpaceMarines/AquilaMacroCannonDescription'/>"/>
	<entry name="SpaceMarines/AquilaMacroCannonFlavor" value="<string name='Units/SpaceMarines/AquilaMacroCannonFlavor'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarine" value="<string name='Units/SpaceMarines/AssaultSpaceMarine'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineDescription" value="<string name='Units/SpaceMarines/AssaultSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/AssaultSpaceMarineFlavor" value="<string name='Units/SpaceMarines/AssaultSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/AssaultTerminator" value="<string name='Units/SpaceMarines/AssaultTerminator'/>"/>
 	<entry name="SpaceMarines/AssaultTerminatorDescription" value="<string name='Units/SpaceMarines/AssaultTerminatorDescription'/>"/>
	<entry name="SpaceMarines/AssaultTerminatorFlavor" value="<string name='Units/SpaceMarines/AssaultTerminatorFlavor'/>"/>
	<entry name="SpaceMarines/Captain" value="<string name='Units/SpaceMarines/Captain'/>"/>
	<entry name="SpaceMarines/CaptainDescription" value="<string name='Units/SpaceMarines/CaptainDescription'/>"/>
	<entry name="SpaceMarines/CaptainFlavor" value="<string name='Units/SpaceMarines/CaptainFlavor'/>"/>
	<entry name="SpaceMarines/Chaplain" value="<string name='Units/SpaceMarines/Chaplain'/>"/>
	<entry name="SpaceMarines/ChaplainDescription" value="<string name='Units/SpaceMarines/ChaplainDescription'/>"/>
	<entry name="SpaceMarines/ChaplainFlavor" value="<string name='Units/SpaceMarines/ChaplainFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorCenturion" value="<string name='Units/SpaceMarines/DevastatorCenturion'/>"/>
 	<entry name="SpaceMarines/DevastatorCenturionDescription" value="<string name='Units/SpaceMarines/DevastatorCenturionDescription'/>"/>
 	<entry name="SpaceMarines/DevastatorCenturionFlavor" value="<string name='Units/SpaceMarines/DevastatorCenturionFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarine" value="<string name='Units/SpaceMarines/DevastatorSpaceMarine'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineDescription" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorSpaceMarineFlavor" value="<string name='Units/SpaceMarines/DevastatorSpaceMarineFlavor'/>"/>
	<entry name="SpaceMarines/Dreadnought" value="<string name='Units/SpaceMarines/Dreadnought'/>"/>
	<entry name="SpaceMarines/DreadnoughtDescription" value="<string name='Units/SpaceMarines/DreadnoughtDescription'/>"/>
	<entry name="SpaceMarines/DreadnoughtFlavor" value="<string name='Units/SpaceMarines/DreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Hunter" value="<string name='Units/SpaceMarines/Hunter'/>"/>
	<entry name="SpaceMarines/HunterDescription" value="<string name='Units/SpaceMarines/HunterDescription'/>"/>
	<entry name="SpaceMarines/HunterFlavor" value="<string name='Units/SpaceMarines/HunterFlavor'/>"/>
	<entry name="SpaceMarines/LandRaider" value="<string name='Units/SpaceMarines/LandRaider'/>"/>
	<entry name="SpaceMarines/LandRaiderDescription" value="<string name='Units/SpaceMarines/LandRaiderDescription'/>"/>
	<entry name="SpaceMarines/LandRaiderFlavor" value="<string name='Units/SpaceMarines/LandRaiderFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeeder" value="<string name='Units/SpaceMarines/LandSpeeder'/>"/>
	<entry name="SpaceMarines/LandSpeederDescription" value="<string name='Units/SpaceMarines/LandSpeederDescription'/>"/>
	<entry name="SpaceMarines/LandSpeederFlavor" value="<string name='Units/SpaceMarines/LandSpeederFlavor'/>"/>
	<entry name="SpaceMarines/Librarian" value="<string name='Units/SpaceMarines/Librarian'/>"/>
	<entry name="SpaceMarines/LibrarianDescription" value="<string name='Units/SpaceMarines/LibrarianDescription'/>"/>
	<entry name="SpaceMarines/LibrarianFlavor" value="<string name='Units/SpaceMarines/LibrarianFlavor'/>"/>
	<entry name="SpaceMarines/Predator" value="<string name='Units/SpaceMarines/Predator'/>"/>
	<entry name="SpaceMarines/PredatorDescription" value="<string name='Units/SpaceMarines/PredatorDescription'/>"/>
	<entry name="SpaceMarines/PredatorFlavor" value="<string name='Units/SpaceMarines/PredatorFlavor'/>"/>
	<entry name="SpaceMarines/PrimarisAggressor" value="<string name='Units/SpaceMarines/PrimarisAggressor'/>"/>
 	<entry name="SpaceMarines/PrimarisAggressorDescription" value="<string name='Units/SpaceMarines/PrimarisAggressorDescription'/>"/>
 	<entry name="SpaceMarines/PrimarisAggressorFlavor" value="<string name='Units/SpaceMarines/PrimarisAggressorFlavor'/>"/>
 	<entry name="SpaceMarines/PrimarisHellblaster" value="<string name='Units/SpaceMarines/PrimarisHellblaster'/>"/>
 	<entry name="SpaceMarines/PrimarisHellblasterDescription" value="<string name='Units/SpaceMarines/PrimarisHellblasterDescription'/>"/>
 	<entry name="SpaceMarines/PrimarisHellblasterFlavor" value="<string name='Units/SpaceMarines/PrimarisHellblasterFlavor'/>"/>
 	<entry name="SpaceMarines/PrimarisInceptor" value="<string name='Units/SpaceMarines/PrimarisInceptor'/>"/>
 	<entry name="SpaceMarines/PrimarisInceptorDescription" value="<string name='Units/SpaceMarines/PrimarisInceptorDescription'/>"/>
 	<entry name="SpaceMarines/PrimarisInceptorFlavor" value="<string name='Units/SpaceMarines/PrimarisInceptorFlavor'/>"/>
 	<entry name="SpaceMarines/PrimarisIntercessor" value="<string name='Units/SpaceMarines/PrimarisIntercessor'/>"/>
 	<entry name="SpaceMarines/PrimarisIntercessorDescription" value="<string name='Units/SpaceMarines/PrimarisIntercessorDescription'/>"/>
 	<entry name="SpaceMarines/PrimarisIntercessorFlavor" value="<string name='Units/SpaceMarines/PrimarisIntercessorFlavor'/>"/>
 	<entry name="SpaceMarines/PrimarisInvaderATV" value="<string name='Units/SpaceMarines/PrimarisInvaderATV'/>"/>
 	<entry name="SpaceMarines/PrimarisInvaderATVDescription" value="<string name='Units/SpaceMarines/PrimarisInvaderATVDescription'/>"/>
 	<entry name="SpaceMarines/PrimarisInvaderATVFlavor" value="<string name='Units/SpaceMarines/PrimarisInvaderATVFlavor'/>"/>
 	<entry name="SpaceMarines/PrimarisRepulsorExecutioner" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutioner'/>"/>
 	<entry name="SpaceMarines/PrimarisRepulsorExecutionerDescription" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerDescription'/>"/>
 	<entry name="SpaceMarines/PrimarisRepulsorExecutionerFlavor" value="<string name='Units/SpaceMarines/PrimarisRepulsorExecutionerFlavor'/>"/>
	<entry name="SpaceMarines/Razorback" value="<string name='Units/SpaceMarines/Razorback'/>"/>
	<entry name="SpaceMarines/RazorbackDescription" value="<string name='Units/SpaceMarines/RazorbackDescription'/>"/>
	<entry name="SpaceMarines/RazorbackFlavor" value="<string name='Units/SpaceMarines/RazorbackFlavor'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnought" value="<string name='Units/SpaceMarines/RedemptorDreadnought'/>"/>
	<entry name="SpaceMarines/RedemptorDreadnoughtDescription" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtDescription'/>"/>
 	<entry name="SpaceMarines/RedemptorDreadnoughtFlavor" value="<string name='Units/SpaceMarines/RedemptorDreadnoughtFlavor'/>"/>
	<entry name="SpaceMarines/Scout" value="<string name='Units/SpaceMarines/Scout'/>"/>
	<entry name="SpaceMarines/ScoutDescription" value="<string name='Units/SpaceMarines/ScoutDescription'/>"/>
	<entry name="SpaceMarines/ScoutFlavor" value="<string name='Units/SpaceMarines/ScoutFlavor'/>"/>
	<entry name="SpaceMarines/ScoutBiker" value="<string name='Units/SpaceMarines/ScoutBiker'/>"/>
	<entry name="SpaceMarines/ScoutBikerDescription" value="<string name='Units/SpaceMarines/ScoutBikerDescription'/>"/>
	<entry name="SpaceMarines/ScoutBikerFlavor" value="<string name='Units/SpaceMarines/ScoutBikerFlavor'/>"/>
	<entry name="SpaceMarines/StormravenGunship" value="<string name='Units/SpaceMarines/StormravenGunship'/>"/>
	<entry name="SpaceMarines/StormravenGunshipDescription" value="<string name='Units/SpaceMarines/StormravenGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormravenGunshipFlavor" value="<string name='Units/SpaceMarines/StormravenGunshipFlavor'/>"/>
	<entry name="SpaceMarines/StormSpeederThunderstrike" value="<string name='Units/SpaceMarines/StormSpeederThunderstrike'/>"/>
 	<entry name="SpaceMarines/StormSpeederThunderstrikeDescription" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeDescription'/>"/>
 	<entry name="SpaceMarines/StormSpeederThunderstrikeFlavor" value="<string name='Units/SpaceMarines/StormSpeederThunderstrikeFlavor'/>"/>
	<entry name="SpaceMarines/StormtalonGunship" value="<string name='Units/SpaceMarines/StormtalonGunship'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipDescription" value="<string name='Units/SpaceMarines/StormtalonGunshipDescription'/>"/>
	<entry name="SpaceMarines/StormtalonGunshipFlavor" value="<string name='Units/SpaceMarines/StormtalonGunshipFlavor'/>"/>
	<entry name="SpaceMarines/Terminator" value="<string name='Units/SpaceMarines/Terminator'/>"/>
	<entry name="SpaceMarines/TerminatorDescription" value="<string name='Units/SpaceMarines/TerminatorDescription'/>"/>
	<entry name="SpaceMarines/TerminatorFlavor" value="<string name='Units/SpaceMarines/TerminatorFlavor'/>"/>
	<entry name="SpaceMarines/ThunderfireCannon" value="<string name='Units/SpaceMarines/ThunderfireCannon'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonDescription" value="<string name='Units/SpaceMarines/ThunderfireCannonDescription'/>"/>
	<entry name="SpaceMarines/ThunderfireCannonFlavor" value="<string name='Units/SpaceMarines/ThunderfireCannonFlavor'/>"/>
	<entry name="SpaceMarines/Vindicator" value="<string name='Units/SpaceMarines/Vindicator'/>"/>
	<entry name="SpaceMarines/VindicatorDescription" value="<string name='Units/SpaceMarines/VindicatorDescription'/>"/>
	<entry name="SpaceMarines/VindicatorFlavor" value="<string name='Units/SpaceMarines/VindicatorFlavor'/>"/>
	<entry name="SpaceMarines/Whirlwind" value="<string name='Units/SpaceMarines/Whirlwind'/>"/>
 	<entry name="SpaceMarines/WhirlwindDescription" value="<string name='Units/SpaceMarines/WhirlwindDescription'/>"/>
 	<entry name="SpaceMarines/WhirlwindFlavor" value="<string name='Units/SpaceMarines/WhirlwindFlavor'/>"/>
	<entry name="Tau/BroadsideBattlesuit" value="<string name='Units/Tau/BroadsideBattlesuit'/>"/>
	<entry name="Tau/BroadsideBattlesuitDescription" value="<string name='Units/Tau/BroadsideBattlesuitDescription'/>"/>
	<entry name="Tau/BroadsideBattlesuitFlavor" value="<string name='Units/Tau/BroadsideBattlesuitFlavor'/>"/>
	<entry name="Tau/BuilderDrone" value="<string name='Units/Tau/BuilderDrone'/>"/>
	<entry name="Tau/BuilderDroneDescription" value="<string name='Units/Tau/BuilderDroneDescription'/>"/>
	<entry name="Tau/BuilderDroneFlavor" value="<string name='Units/Tau/BuilderDroneFlavor'/>"/>
	<entry name="Tau/Commander" value="<string name='Units/Tau/Commander'/>"/>
	<entry name="Tau/CommanderDescription" value="<string name='Units/Tau/CommanderDescription'/>"/>
	<entry name="Tau/CommanderFlavor" value="<string name='Units/Tau/CommanderFlavor'/>"/>
	<entry name="Tau/CrisisBattlesuit" value="<string name='Units/Tau/CrisisBattlesuit'/>"/>
	<entry name="Tau/CrisisBattlesuitDescription" value="<string name='Units/Tau/CrisisBattlesuitDescription'/>"/>
	<entry name="Tau/CrisisBattlesuitFlavor" value="<string name='Units/Tau/CrisisBattlesuitFlavor'/>"/>
	<entry name="Tau/Devilfish" value="<string name='Units/Tau/Devilfish'/>"/>
	<entry name="Tau/DevilfishDescription" value="<string name='Units/Tau/DevilfishDescription'/>"/>
	<entry name="Tau/DevilfishFlavor" value="<string name='Units/Tau/DevilfishFlavor'/>"/>
	<entry name="Tau/Ethereal" value="<string name='Units/Tau/Ethereal'/>"/>
	<entry name="Tau/EtherealDescription" value="<string name='Units/Tau/EtherealDescription'/>"/>
	<entry name="Tau/EtherealFlavor" value="<string name='Units/Tau/EtherealFlavor'/>"/>
	<entry name="Tau/FireWarriorBreacher" value="<string name='Units/Tau/FireWarriorBreacher'/>"/>
 	<entry name="Tau/FireWarriorBreacherDescription" value="<string name='Units/Tau/FireWarriorBreacherDescription'/>"/>
 	<entry name="Tau/FireWarriorBreacherFlavor" value="<string name='Units/Tau/FireWarriorBreacherFlavor'/>"/>
	<entry name="Tau/GunDrone" value="<string name='Units/Tau/GunDrone'/>"/> 
	<entry name="Tau/GunDroneDescription" value="Confère aux Guerriers de Feu, Guerriers du feu brécheurs, Exo-Armures XV8 Crisis, Exo-Armures XV25 Stealth, Exo-Armures XV88 Broadside, Sabres de Feu, Ethérés et Commandeurs la capacités de déployer des drones de combats basiques temporaires."/>
	<entry name="Tau/GunDroneFlavor" value="<string name='Units/Tau/GunDroneFlavor'/>"/>
	<entry name="Tau/HammerheadGunship" value="<string name='Units/Tau/HammerheadGunship'/>"/>
	<entry name="Tau/HammerheadGunshipDescription" value="<string name='Units/Tau/HammerheadGunshipDescription'/>"/>
	<entry name="Tau/HammerheadGunshipFlavor" value="<string name='Units/Tau/HammerheadGunshipFlavor'/>"/>
	<entry name="Tau/KrootoxRider" value="<string name='Units/Tau/KrootoxRider'/>"/>
 	<entry name="Tau/KrootoxRiderDescription" value="<string name='Units/Tau/KrootoxRiderDescription'/>"/>
 	<entry name="Tau/KrootoxRiderFlavor" value="<string name='Units/Tau/KrootoxRiderFlavor'/>"/>
	<entry name="Tau/MarkerDrone" value="<string name='Units/Tau/MarkerDrone'/>"/>
	<entry name="Tau/MarkerDroneDescription" value="Confère aux Guerriers de Feu, Guerriers du feu brécheurs, Exo-Armures XV8 Crisis, Exo-Armures XV25 Stealth, Exo-Armures XV88 Broadside, Sabres de Feu, Ethérés et Commandeurs la capacités de déployer des drones temporaires qui peuvent marquer l'ennemi pour augmenter les dégâts."/>
	<entry name="Tau/MarkerDroneFlavor" value="<string name='Units/Tau/MarkerDroneFlavor'/>"/>
	<entry name="Tau/Pathfinder" value="<string name='Units/Tau/Pathfinder'/>"/>
	<entry name="Tau/PathfinderDescription" value="<string name='Units/Tau/PathfinderDescription'/>"/>
	<entry name="Tau/PathfinderFlavor" value="<string name='Units/Tau/PathfinderFlavor'/>"/>
	<entry name="Tau/RiptideBattlesuit" value="<string name='Units/Tau/RiptideBattlesuit'/>"/>
	<entry name="Tau/RiptideBattlesuitDescription" value="<string name='Units/Tau/RiptideBattlesuitDescription'/>"/>
	<entry name="Tau/RiptideBattlesuitFlavor" value="<string name='Units/Tau/RiptideBattlesuitFlavor'/>"/>
	<entry name="Tau/ShieldDrone" value="<string name='Units/Tau/ShieldDrone'/>"/>
	<entry name="Tau/ShieldDroneDescription" value="Confère aux Guerriers de Feu, Guerriers du feu brécheurs, Exo-Armures XV8 Crisis, Exo-Armures XV25 Stealth, Exo-Armures XV88 Broadside, Sabres de Feu, Ethérés et Commandeurs la capacités de déployer des drones temporaires qui protègent les alliés."/>
	<entry name="Tau/ShieldDroneFlavor" value="<string name='Units/Tau/ShieldDroneFlavor'/>"/>
	<entry name="Tau/SkyRayGunship" value="<string name='Units/Tau/SkyRayGunship'/>"/>
	<entry name="Tau/SkyRayGunshipDescription" value="<string name='Units/Tau/SkyRayGunshipDescription'/>"/>
	<entry name="Tau/SkyRayGunshipFlavor" value="<string name='Units/Tau/SkyRayGunshipFlavor'/>"/>
	<entry name="Tau/StealthBattlesuit" value="<string name='Units/Tau/StealthBattlesuit'/>"/>
	<entry name="Tau/StealthBattlesuitDescription" value="<string name='Units/Tau/StealthBattlesuitDescription'/>"/>
	<entry name="Tau/StealthBattlesuitFlavor" value="<string name='Units/Tau/StealthBattlesuitFlavor'/>"/>
	<entry name="Tau/Stormsurge" value="<string name='Units/Tau/Stormsurge'/>"/>
	<entry name="Tau/StormsurgeDescription" value="<string name='Units/Tau/StormsurgeDescription'/>"/>
	<entry name="Tau/StormsurgeFlavor" value="<string name='Units/Tau/StormsurgeFlavor'/>"/>
	<entry name="Tau/SunSharkBomber" value="<string name='Units/Tau/SunSharkBomber'/>"/>
	<entry name="Tau/SunSharkBomberDescription" value="<string name='Units/Tau/SunSharkBomberDescription'/>"/>
	<entry name="Tau/SunSharkBomberFlavor" value="<string name='Units/Tau/SunSharkBomberFlavor'/>"/>
	<entry name="Tau/TidewallGunrig" value="<string name='Units/Tau/TidewallGunrig'/>"/>
	<entry name="Tau/TidewallGunrigDescription" value="Confère aux Drones constructeurs la capacité de construire une fortification lourdement armée qui peut se déplacer si elle transporte des troupes."/>
	<entry name="Tau/TidewallGunrigFlavor" value="<string name='Units/Tau/TidewallGunrigFlavor'/>"/>
	<entry name="Tau/TigerShark" value="<string name='Units/Tau/TigerShark'/>"/>
	<entry name="Tau/TigerSharkDescription" value="<string name='Units/Tau/TigerSharkDescription'/>"/>
	<entry name="Tau/TigerSharkFlavor" value="<string name='Units/Tau/TigerSharkFlavor'/>"/>
	<entry name="Tau/RVarnaBattlesuit" value="<string name='Units/Tau/RVarnaBattlesuit'/>"/>
	<entry name="Tau/RVarnaBattlesuitDescription" value="<string name='Units/Tau/RVarnaBattlesuitDescription'/>"/>
	<entry name="Tau/RVarnaBattlesuitFlavor" value="<string name='Units/Tau/RVarnaBattlesuitFlavor'/>"/>
	<entry name="Tyranids/Biovore" value="<string name='Units/Tyranids/Biovore'/>"/>
	<entry name="Tyranids/BiovoreDescription" value="<string name='Units/Tyranids/BiovoreDescription'/>"/>
	<entry name="Tyranids/BiovoreFlavor" value="<string name='Units/Tyranids/BiovoreFlavor'/>"/>
	<entry name="Tyranids/Exocrine" value="<string name='Units/Tyranids/Exocrine'/>"/>
	<entry name="Tyranids/ExocrineDescription" value="<string name='Units/Tyranids/ExocrineDescription'/>"/>
	<entry name="Tyranids/ExocrineFlavor" value="<string name='Units/Tyranids/ExocrineFlavor'/>"/>
	<entry name="Tyranids/Gargoyle" value="<string name='Units/Tyranids/Gargoyle'/>"/>
	<entry name="Tyranids/GargoyleDescription" value="<string name='Units/Tyranids/GargoyleDescription'/>"/>
	<entry name="Tyranids/GargoyleFlavor" value="<string name='Units/Tyranids/GargoyleFlavor'/>"/>
	<entry name="Tyranids/Haruspex" value="<string name='Units/Tyranids/Haruspex'/>"/>
	<entry name="Tyranids/HaruspexDescription" value="<string name='Units/Tyranids/HaruspexDescription'/>"/>
	<entry name="Tyranids/HaruspexFlavor" value="<string name='Units/Tyranids/HaruspexFlavor'/>"/>
	<entry name="Tyranids/HiveGuard" value="<string name='Units/Tyranids/HiveGuard'/>"/>
	<entry name="Tyranids/HiveGuardDescription" value="<string name='Units/Tyranids/HiveGuardDescription'/>"/>
	<entry name="Tyranids/HiveGuardFlavor" value="<string name='Units/Tyranids/HiveGuardFlavor'/>"/>
	<entry name="Tyranids/HiveTyrant" value="<string name='Units/Tyranids/HiveTyrant'/>"/>
	<entry name="Tyranids/HiveTyrantDescription" value="<string name='Units/Tyranids/HiveTyrantDescription'/>"/>
	<entry name="Tyranids/HiveTyrantFlavor" value="<string name='Units/Tyranids/HiveTyrantFlavor'/>"/>
	<entry name="Tyranids/Hormagaunt" value="<string name='Units/Tyranids/Hormagaunt'/>"/>
	<entry name="Tyranids/HormagauntDescription" value="<string name='Units/Tyranids/HormagauntDescription'/>"/>
	<entry name="Tyranids/HormagauntFlavor" value="<string name='Units/Tyranids/HormagauntFlavor'/>"/>
	<entry name="Tyranids/Lictor" value="<string name='Units/Tyranids/Lictor'/>"/>
	<entry name="Tyranids/LictorDescription" value="<string name='Units/Tyranids/LictorDescription'/>"/>
	<entry name="Tyranids/LictorFlavor" value="<string name='Units/Tyranids/LictorFlavor'/>"/>
	<entry name="Tyranids/Maleceptor" value="<string name='Units/Tyranids/Maleceptor'/>"/>
 	<entry name="Tyranids/MaleceptorDescription" value="<string name='Units/Tyranids/MaleceptorDescription'/>"/>
 	<entry name="Tyranids/MaleceptorFlavor" value="<string name='Units/Tyranids/MaleceptorFlavor'/>"/>
	<entry name="Tyranids/NornEmissary" value="<string name='Units/Tyranids/NornEmissary'/>"/>
 	<entry name="Tyranids/NornEmissaryDescription" value="<string name='Units/Tyranids/NornEmissaryDescription'/>"/>
 	<entry name="Tyranids/NornEmissaryFlavor" value="<string name='Units/Tyranids/NornEmissaryFlavor'/>"/>
	<entry name="Tyranids/Ravener" value="<string name='Units/Tyranids/Ravener'/>"/>
	<entry name="Tyranids/RavenerDescription" value="<string name='Units/Tyranids/RavenerDescription'/>"/>
	<entry name="Tyranids/RavenerFlavor" value="<string name='Units/Tyranids/RavenerFlavor'/>"/>
	<entry name="Tyranids/ScythedHierodule" value="<string name='Units/Tyranids/ScythedHierodule'/>"/>
	<entry name="Tyranids/ScythedHieroduleDescription" value="<string name='Units/Tyranids/ScythedHieroduleDescription'/>"/>
	<entry name="Tyranids/ScythedHieroduleFlavor" value="<string name='Units/Tyranids/ScythedHieroduleFlavor'/>"/>
	<entry name="Tyranids/Tervigon" value="<string name='Units/Tyranids/Tervigon'/>"/>
	<entry name="Tyranids/TervigonDescription" value="<string name='Units/Tyranids/TervigonDescription'/>"/>
	<entry name="Tyranids/TervigonFlavor" value="<string name='Units/Tyranids/TervigonFlavor'/>"/>
	<entry name="Tyranids/Trygon" value="<string name='Units/Tyranids/Trygon'/>"/>
	<entry name="Tyranids/TrygonDescription" value="<string name='Units/Tyranids/TrygonDescription'/>"/>
	<entry name="Tyranids/TrygonFlavor" value="<string name='Units/Tyranids/TrygonFlavor'/>"/>
	<entry name="Tyranids/Tyrannofex" value="<string name='Units/Tyranids/Tyrannofex'/>"/>
	<entry name="Tyranids/TyrannofexDescription" value="<string name='Units/Tyranids/TyrannofexDescription'/>"/>
	<entry name="Tyranids/TyrannofexFlavor" value="<string name='Units/Tyranids/TyrannofexFlavor'/>"/>
	<entry name="Tyranids/Venomthrope" value="<string name='Units/Tyranids/Venomthrope'/>"/>
 	<entry name="Tyranids/VenomthropeDescription" value="<string name='Units/Tyranids/VenomthropeDescription'/>"/>
 	<entry name="Tyranids/VenomthropeFlavor" value="<string name='Units/Tyranids/VenomthropeFlavor'/>"/>
	<entry name="Tyranids/Warrior" value="<string name='Units/Tyranids/Warrior'/>"/>
	<entry name="Tyranids/WarriorDescription" value="<string name='Units/Tyranids/WarriorDescription'/>"/>
	<entry name="Tyranids/WarriorFlavor" value="<string name='Units/Tyranids/WarriorFlavor'/>"/>
	<entry name="Tyranids/Zoanthrope" value="<string name='Units/Tyranids/Zoanthrope'/>"/>
	<entry name="Tyranids/ZoanthropeDescription" value="<string name='Units/Tyranids/ZoanthropeDescription'/>"/>
	<entry name="Tyranids/ZoanthropeFlavor" value="<string name='Units/Tyranids/ZoanthropeFlavor'/>"/>
	<entry name="Tyranids/Carnifex" value="<string name='Units/Tyranids/Carnifex'/>"/>
 	<entry name="Tyranids/CarnifexDescription" value="<string name='Units/Tyranids/CarnifexDescription'/>"/>
 	<entry name="Tyranids/CarnifexFlavor" value="<string name='Units/Tyranids/CarnifexFlavor'/>"/>

	<!-- Other -->
	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegration'/>"/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Augmente la production de recherche des bâtiments pour chaque bâtiment produisant de la recherche sur une case adjacente."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="<string name='Traits/AdeptusMechanicus/AdjacencyIntegrationFlavor'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Augmente la pénétration d'armure des armes d'assaut."/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/AssaultWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceDescription'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
	<entry name="AdeptusMechanicus/BlessedConduits" value="Conduits bénis"/>
	<entry name="AdeptusMechanicus/BlessedConduitsDescription" value="Réduit le temps de recharge de Pic énergétique."/>
	<entry name="AdeptusMechanicus/BlessedConduitsFlavor" value="“Et quand enfin il arriva au véhicule, il perçut la détresse de son moteur et frappa la rune et tout fut bien. Après quoi, le moteur s'alluma et s'emplit de force…“<br/>—Seigneur des moteurs, Tome 16, Vers 2001"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Capacité pour les Ferro-échassiers Ballistarii, Dragons Sydoniens, Glisseurs Skorpius, Onagres des dunes et Désintégrateurs de Skorpius qui réduit la perte de moral des untiés adjacentes de l'Adeptus Mechanicus."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistDescription'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier2" value="<string name='Traits/AdeptusMechanicus/CityTier2'/>"/>
	<entry name="AdeptusMechanicus/CityTier2Description" value="<string name='Traits/AdeptusMechanicus/CityTier2Description'/>"/>
	<entry name="AdeptusMechanicus/CityTier2Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier2Flavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier3" value="<string name='Traits/AdeptusMechanicus/CityTier3'/>"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="<string name='Traits/AdeptusMechanicus/CityTier3Description'/>"/>
	<entry name="AdeptusMechanicus/CityTier3Flavor" value="<string name='Traits/AdeptusMechanicus/CityTier3Flavor'/>"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubber" value="<string name='Weapons/CognisHeavyStubber'/>"/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberDescription" value="Confère une mitrailleuse Cognis aux Onagres des dunes."/>
	<entry name="AdeptusMechanicus/CognisHeavyStubberFlavor" value="<string name='Weapons/CognisHeavyStubberFlavor'/>"/>
	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Traits/AdeptusMechanicus/CommandUplink'/>"/>
	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Ajoute une capacité aux Archéoptères Transvector et aux Archéoptères Stratoraptor qui réduit la perte de moral des unités adjacentes de l'Adeptus Mechanicus."/>
	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Traits/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTether" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTether'/>"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Augmente le moral des Patrouilleurs skitarii et des Rangers skitarii."/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="<string name='Traits/AdeptusMechanicus/EnhancedDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmour" value="Protocoles graïens"/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie."/>
	<entry name="AdeptusMechanicus/ExtraInfantryArmourFlavor" value="L'intégration des protocoles graïens dans les circuits de commande d'un skitarii améliore davantage leur survivabilité déjà impressionnante. Le monde forge de Graia est réputé pour refuser de céder, car leur logique est simplement irréfutable."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmour" value="Ablation agripinienne"/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourDescription" value="Augmente l'armure des véhicules."/>
	<entry name="AdeptusMechanicus/ExtraVehicleArmourFlavor" value="Vivre près de l'Œil de la Terreur après la chute de Cadia a transformé l'Adeptus Mechanicus sur Agripinaa en experts de la défense—particulièrement quand il s'agit de garder leurs machines en état de fonctionner sous les tirs ennemis."/>
	<entry name="AdeptusMechanicus/FidorumVossPrime" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrime'/>"/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Augmente la production d'influence des bâtiments pour chaque bâtiment produisant de l'influence sur une case adjacente."/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="<string name='Traits/AdeptusMechanicus/FidorumVossPrimeFlavor'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AdeptusMechanicus/HammerOfWrathDescription" value="Confère aux Ballistarius Ironstrider, aux Électro-prêtres Fulgurite, Dragons Sydoniens, aux Rampants des dunes Onagre, aux Sterylizors Pteraxii et aux Chevaliers Croisés la capacité d'effectuer des attaques plus dévastatrices."/>
	<entry name="AdeptusMechanicus/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Augmente la pénétration d'armure des armes lourdes."/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/HeavyWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiah'/>"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Augmente la production de loyauté des bâtiments pour chaque bâtiment produisant de la loyauté sur une case adjacente."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="<string name='Traits/AdeptusMechanicus/IconsOfTheOmnissiahFlavor'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulDescription'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightDescription'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancer" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancer'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerDescription" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerDescription'/>"/>
	<entry name="AdeptusMechanicus/LitanyOfTheElectromancerFlavor" value="<string name='Actions/AdeptusMechanicus/LitanyOfTheElectromancerFlavor'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisation" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisation'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationDescription'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="<string name='Traits/AdeptusMechanicus/LucianSpecialisationFlavor'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonus'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Augmente la pénétration d'armure des armes de corps à corps."/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="<string name='Traits/AdeptusMechanicus/MeleeWeaponBonusFlavor'/>"/>
	<entry name="AdeptusMechanicus/Omnispex" value="<string name='Traits/AdeptusMechanicus/Omnispex'/>"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="Permet aux Patrouilleurs skitarii et Rangers skitarii d'ignorer une partie des réductions de dégâts à distance."/>
	<entry name="AdeptusMechanicus/OmnispexFlavor" value="<string name='Traits/AdeptusMechanicus/OmnispexFlavor'/>"/>
	<entry name="AdeptusMechanicus/OptateRestrictions" value="<string name='Traits/AdeptusMechanicus/OptateRestrictions'/>"/>
	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Augmente la limite de population des Hab-sanctuaires."/>
	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="<string name='Traits/AdeptusMechanicus/OptateRestrictionsFlavor'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeDescription'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocols'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Augmente le taux de croissance des villes"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="<string name='Traits/AdeptusMechanicus/ReclaimatorProtocolsFlavor'/>"/>
	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmDescription'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SolarReflectors" value="<string name='Traits/AdeptusMechanicus/SolarReflectors'/>"/>
	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Augmente la production d'énergie des bâtiments pour chaque bâtiment produisant de l'énergie sur une case adjacente."/>
	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="<string name='Traits/AdeptusMechanicus/SolarReflectorsFlavor'/>"/>
 	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeams'/>"/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Augmente la production de nourriture des bâtiments pour chaque bâtiment produisant de la nourriture sur une case adjacente."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="<string name='Traits/AdeptusMechanicus/SoylensAcquisitionTeamsFlavor'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenment" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenment'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentDescription'/>"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="<string name='Traits/AdeptusMechanicus/StygianEnlightenmentFlavor'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralism" value="<string name='Traits/AdeptusMechanicus/TerranGeneralism'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismDescription'/>"/>
	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="<string name='Traits/AdeptusMechanicus/TerranGeneralismFlavor'/>"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiency" value="Efficacité du thermo-échange"/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyDescription" value="Augmente le bonus de Pic énergétique."/>
	<entry name="AdeptusMechanicus/ThermoExchangeEfficiencyFlavor" value="Paradoxalement, le fait de ralentir l'alimentation de l'échangeur thermique et de faire passer le plasma par une deuxième série de couplages thermiques et de condenseurs peut prolonger la durée de fonctionnement au-delà des objectifs nominaux ou, du moins, réduire le nombre de vies perdues et de réparations nécessaires."/>
	<entry name="AdeptusMechanicus/TriplexNecessity" value="<string name='Traits/AdeptusMechanicus/TriplexNecessity'/>"/>
	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Augmente la production de minerai des bâtiments pour chaque bâtiment produisant de la minerai sur une case adjacente."/>
	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="<string name='Traits/AdeptusMechanicus/TriplexNecessityFlavor'/>"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannon" value="<string name='Weapons/TwinLinkedIcarusAutocannon'/>"/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonDescription" value="Confère aux Chevaliers Croisé un Autocanon jumelé Icarus."/>
	<entry name="AdeptusMechanicus/TwinLinkedIcarusAutocannonFlavor" value="<string name='Weapons/TwinLinkedIcarusAutocannonFlavor'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Augmente la perte de morale des ennemis adjacents à un Infiltrateur sicarien."/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
	<entry name="AdeptusMechanicus/XenariteAcceptance" value="Acceptation Xenarite"/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceDescription" value="Augmente le montant forfaitaire de recherche obtenu avec Ruines de Vaul."/>
	<entry name="AdeptusMechanicus/XenariteAcceptanceFlavor" value="Il semble que les technoprêtres Xenarites de Stygies VIII aient trouvé leur chemin vers Gladius Prime—mettant en places des unités d'observation et des drones à chaque avant-poste pour mieux comprendre les Xenos. Les applications pratiques en terme de recherche sont immédiatement évidentes…"/>
	<entry name="SmokeLauncher" value="Fumigène"/>
	<entry name="SmokeLauncherDescription" value="Confère aux véhicules terrestres la capacité de lancer un écran de fumée qui augmente la réduction de dégâts à distance."/>
	<entry name="SmokeLauncherFlavor" value="Certains véhicules sont équipés de petits lanceurs qui contiennent des cartouches de fumigènes. Ils servent à masquer temporairement un véhicule derrière des nuages de fumée, ce qui lui permet de progresser sans danger.—bien que cela gêne aussi l'utilisation des armes du véhicule."/>
	<entry name="AstraMilitarum/AdditionalHeavyBolters" value="Bolters lourds additionnels"/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersDescription" value="Confère aux Baneblades, aux chars de combat Rogal Dorn et aux Valkyries des bolters lourds supplémentaires."/>
	<entry name="AstraMilitarum/AdditionalHeavyBoltersFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachine" value="<string name='Actions/AwakenTheMachine'/>"/>
	<entry name="AstraMilitarum/AwakenTheMachineDescription" value="Confère aux Technaugures la capacité d'augmenter la précision des véhicules."/>
	<entry name="AstraMilitarum/AwakenTheMachineFlavor" value="<string name='Actions/AwakenTheMachineFlavor'/>"/>
	<entry name="AstraMilitarum/BanebladeLascannon" value="Canons laser du Baneblade"/>
	<entry name="AstraMilitarum/BanebladeLascannonDescription" value="Confère aux Baneblades des canons lasers additionnels."/>
	<entry name="AstraMilitarum/BanebladeLascannonFlavor" value="<string name='Weapons/LascannonFlavor'/>"/>
	<entry name="AstraMilitarum/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Augmente la pénétration d'armure des grenades, missiles et armes à explosion."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Augmente la pénétration d'armure des armes à bolt."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="AstraMilitarum/BruteShield" value="<string name='Traits/BruteShield'/>"/>
	<entry name="AstraMilitarum/BruteShieldDescription" value="Augmente les dégâts et la réduction de dégâts des Bullgryns."/>
	<entry name="AstraMilitarum/BruteShieldFlavor" value="<string name='Traits/BruteShieldFlavor'/>"/>
	<entry name="AstraMilitarum/CamoNetting" value="<string name='Traits/CamoNetting'/>"/>
	<entry name="AstraMilitarum/CamoNettingDescription" value="<string name='Traits/CamoNettingDescription'/>"/>
	<entry name="AstraMilitarum/CamoNettingFlavor" value="<string name='Traits/CamoNettingFlavor'/>"/>
	<entry name="AstraMilitarum/ChaffLauncher" value="Lanceur de paillettes"/>
	<entry name="AstraMilitarum/ChaffLauncherDescription" value="Confère aux Thunderbolts et Bombardiers Marauders la capacité de lancer des paillettes pour augmente la réduction de dégâts à distance."/>
	<entry name="AstraMilitarum/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="AstraMilitarum/CityTier2" value="<string name='Traits/AstraMilitarum/CityTier2'/>"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Augmente le rayon d'acquisition des cases des villes."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="<string name='Traits/AstraMilitarum/CityTier2Flavor'/>"/>
	<entry name="AstraMilitarum/CityTier3" value="<string name='Traits/AstraMilitarum/CityTier3'/>"/>
	<entry name="AstraMilitarum/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="<string name='Traits/AstraMilitarum/CityTier3Flavor'/>"/>
	<entry name="AstraMilitarum/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="AstraMilitarum/DozerBladeDescription" value="Réduit la pénéalité de mouvement des chars en forêt et dans les ruines impériales."/>
	<entry name="AstraMilitarum/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="AstraMilitarum/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie."/>
	<entry name="AstraMilitarum/ExtraInfantryArmourFlavor" value="La plupart de l'infanterie du 41° millénaire est équipée avec l'armure pare-balles ou ses équivalents. Si un commandant veut que ses troupes aient plus qu'une maigre chance de survie, il les équipera avec quelque chose de plus proche de l'armure Carapace de l'Astra Militarum dérivée de l'armaplast."/>
	<entry name="AstraMilitarum/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="AstraMilitarum/ExtraVehicleArmourDescription" value="Augmente l'armure des véhicules."/>
	<entry name="AstraMilitarum/ExtraVehicleArmourFlavor" value="Ajouter des plaques de blindages supplémentaires aux chars est peut-être le summum de l'hérésie pour les Techno-Prêtres mais pour les soldats de l'Astra Militarum c'est la norme. Des plaques ablatives ou un blindage sur-mesure pour contrer une arme particulière n'est pas rare."/>
	<entry name="AstraMilitarum/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="AstraMilitarum/FragGrenadeDescription" value="Confère aux Gardes, Equipes d'armes lourdes, Batteries d'artillerie de campagne, Seigneur-Commissaires, Psykers Primaris, Technaugures et Tempestus Scions la capacité de lancer des grenades antipersonnel."/>
	<entry name="AstraMilitarum/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="AstraMilitarum/HammerOfWrathDescription" value="Confère aux Sentinelles Scouts et aux Bullgryns la capacité d'effectuer des attaques plus puissantes."/>
	<entry name="AstraMilitarum/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="AstraMilitarum/HunterKillerMissileDescription" value="Confère aux véhicules terrestres la capacité de tirer des missiles traqueurs."/>
	<entry name="AstraMilitarum/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialSplendour" value="<string name='Traits/ImperialSplendour'/>"/>
	<entry name="AstraMilitarum/ImperialSplendourDescription" value="Augmente l'influence des villes de l'Astra Militarum."/>
	<entry name="AstraMilitarum/ImperialSplendourFlavor" value="<string name='Traits/ImperialSplendourFlavor'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpoint" value="<string name='Units/AstraMilitarum/ImperialStrongpoint'/>"/>
	<entry name="AstraMilitarum/ImperialStrongpointDescription" value="Confère aux Technaugures la capacité de construire des fortifications avec des bolters lourds."/>
	<entry name="AstraMilitarum/ImperialStrongpointFlavor" value="<string name='Units/AstraMilitarum/ImperialStrongpointFlavor'/>"/>
	<entry name="AstraMilitarum/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="AstraMilitarum/KrakGrenadeDescription" value="Confère aux Gardes, Equipes d'armes lourdes, Batteries d'artillerie de campagne, Seigneur-Commissaires, Psykers Primaris, Technaugures et Tempestus Scions la capacité de lancer des grenade anti-blindés."/>
	<entry name="AstraMilitarum/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Augmente la pénétration d'armure des armes laser."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="AstraMilitarum/MediPack" value="<string name='Actions/MediPack'/>"/>
	<entry name="AstraMilitarum/MediPackDescription" value="Confère aux Gardes et Tempestus Scions la capacité de se soigner au combat."/>
	<entry name="AstraMilitarum/MediPackFlavor" value="<string name='Actions/MediPackFlavor'/>"/>
	<entry name="AstraMilitarum/Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="AstraMilitarum/MisfortuneDescription" value="Confère aux psykers Wyrdvane la capacité de maudir les ennemis pour qu'ils subissent davantage de dégâts."/>
	<entry name="AstraMilitarum/MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="AstraMilitarum/RecoveryGear" value="<string name='Traits/RecoveryGear'/>"/>
	<entry name="AstraMilitarum/RecoveryGearDescription" value="Augmente la vitesse de récupération des véhicules terrestres."/>
	<entry name="AstraMilitarum/RecoveryGearFlavor" value="<string name='Traits/RecoveryGearFlavor'/>"/>
	<entry name="AstraMilitarum/RelicPlating" value="<string name='Traits/RelicPlating'/>"/>
	<entry name="AstraMilitarum/RelicPlatingDescription" value="Augmente la réduction des dégâts des décharges psy pour les véhicules terrestres."/>
	<entry name="AstraMilitarum/RelicPlatingFlavor" value="<string name='Traits/RelicPlatingFlavor'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissile" value="<string name='Weapons/SkystrikeMissile'/>"/>
	<entry name="AstraMilitarum/SkystrikeMissileDescription" value="Confère des missiles antiaériens aux Thunderbolts."/>
	<entry name="AstraMilitarum/SkystrikeMissileFlavor" value="<string name='Weapons/SkystrikeMissileFlavor'/>"/>
	<entry name="AstraMilitarum/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="AstraMilitarum/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilots" value="<string name='Traits/TrainedSentinelPilots'/>"/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsDescription" value="Augmente les dégâts des Sentinelles Scouts."/>
	<entry name="AstraMilitarum/TrainedSentinelPilotsFlavor" value="<string name='Traits/TrainedSentinelPilotsFlavor'/>"/>
	<entry name="AstraMilitarum/VoidShieldGenerator" value="<string name='Units/AstraMilitarum/VoidShieldGenerator'/>"/>
	<entry name="AstraMilitarum/VoidShieldGeneratorDescription" value="Confère aux Technaugures la capacité de construire des générateurs de bouclier qui donne une réduction de dégâts à distance aux unités à portée."/>
	<entry name="AstraMilitarum/VoidShieldGeneratorFlavor" value="<string name='Units/AstraMilitarum/VoidShieldGeneratorFlavor'/>"/>
	<entry name="AstraMilitarum/VoxCaster" value="<string name='Traits/VoxCaster'/>"/>
	<entry name="AstraMilitarum/VoxCasterDescription" value="Réduit les pertes de morale pour les Gardes et les Tempestus Scions."/>
	<entry name="AstraMilitarum/VoxCasterFlavor" value="<string name='Traits/VoxCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculum'/>"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Confère aux Champions du Chaos une chance de gagner définitivement une augmentation de précision lorsqu'ils tuent un ennemi (Bienfait du Chaos)."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="<string name='Traits/ChaosSpaceMarines/ArcaneOcculumFlavor'/>"/>
	<entry name="ChaosSpaceMarines/BlastDamage" value="<string name='Traits/ChaosSpaceMarines/BlastDamage'/>"/>
	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Augmente la pénétration d'armure des grenades, missiles et armes à explosion."/>
	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Bloated" value="<string name='Traits/ChaosSpaceMarines/Bloated'/>"/>
	<entry name="ChaosSpaceMarines/BloatedDescription" value="Confère aux Champions du Chaos une chance de restaurer leurs points de vie lorsqu'ils tuent un ennemi."/>
	<entry name="ChaosSpaceMarines/BloatedFlavor" value="<string name='Traits/ChaosSpaceMarines/BloatedFlavor'/>"/>
	<entry name="ChaosSpaceMarines/BoltDamage" value="<string name='Traits/ChaosSpaceMarines/BoltDamage'/>"/>
	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Augmente la pénétration d'armure des armes à bolt."/>
	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ChaosRising" value="Montée en force du Chaos"/>
	<entry name="ChaosSpaceMarines/ChaosRisingDescription" value="Réduit le coût de création des nouvelles villes."/>
	<entry name="ChaosSpaceMarines/ChaosRisingFlavor" value="La population Impériale de Gladius ne sait pas grand chose du Chaos, mais avec l'enfer des invasions Xénos, leur foi en un Empereur lointain a été secouée. Il est facile pour vos cultistes et apôtres de tourner leurs esprits vers les Dieux—et leur fin."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="<string name='Traits/ChaosSpaceMarines/CityTier2'/>"/>
	<entry name="ChaosSpaceMarines/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier2Flavor'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3" value="<string name='Traits/ChaosSpaceMarines/CityTier3'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="<string name='Traits/ChaosSpaceMarines/CityTier3Flavor'/>"/>
	<entry name="ChaosSpaceMarines/CrystallineBody" value="<string name='Traits/ChaosSpaceMarines/CrystallineBody'/>"/>
	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Confère aux Champions du Chaos une chance de gagner une augmentation permanente des points de vie lorsqu'ils tuent un ennemi (Bienfait du Chaos)."/>
	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="<string name='Traits/ChaosSpaceMarines/CrystallineBodyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Confère aux Rhinos du Chaos, Defilers et Land Raiders du Chaos la capacité d'empêcher les tirs en état d'alerte."/>
	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie."/>
	<entry name="ChaosSpaceMarines/ExtraInfantryArmourFlavor" value="La plupart de l'infanterie du 41° millénaire est équipée avec l'armure pare-balles ou ses équivalents. Si un commandant veut que ses troupes aient plus qu'une maigre chance de survie, il les équipera avec quelque chose de plus proche de l'armure Carapace de l'Astra Militarum dérivée de l'armaplast."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourDescription" value="Augmente l'armure des véhicules."/>
	<entry name="ChaosSpaceMarines/ExtraVehicleArmourFlavor" value="Ajouter des plaques de blindages supplémentaires aux chars est peut-être le summum de l'hérésie pour les Techno-Prêtres mais pour les soldats de l'Astra Militarum c'est la norme. Des plaques ablatives ou un blindage sur-mesure pour contrer une arme particulière n'est pas rare."/>
	<entry name="ChaosSpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="ChaosSpaceMarines/FragGrenadeDescription" value="Confère aux Seigneurs du Chaos, Chaos Terminators, Space Marines du Chaos, Havocs, Berserks de Khorne, Maîtres de Possession et Techmanciens la capacité de lancer des grenades anti-personnel."/>
	<entry name="ChaosSpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutation" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutation'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="Confère aux Seigneurs du Chaos, les Space Marines du Chaos, Terminators du Chaos, Havocs et Berserks de Khorne, Maître de la possession, Serres de guerre et Forgeron de guerre, une Bénédiction du chaos déverrouillé au hasard."/>
	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="<string name='Traits/ChaosSpaceMarines/GiftOfMutationFlavor'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/HammerOfWrathDescription" value="Confère aux Princes Démons, Defilers, Forgefiends, Les Scorpions d'airain, les Helbrutes, les Maulerfiends, les Venomcrawlers et les Warp Talons la capacité de réaliser des attaques plus dévastatrices."/>
	<entry name="ChaosSpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/HavocLauncher" value="<string name='Weapons/HavocLauncher'/>"/>
	<entry name="ChaosSpaceMarines/HavocLauncherDescription" value="Confère aux Rhinos du Chaos et Land Raiders du Chaos une arme à explosion à moyenne portée."/>
	<entry name="ChaosSpaceMarines/HavocLauncherFlavor" value="<string name='Weapons/HavocLauncherFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlame" value="<string name='Actions/ChaosSpaceMarines/IconOfFlame'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlameDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfFlameFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfFlameFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeance" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeance'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfVengeanceFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfVengeanceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathDescription'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="ChaosSpaceMarines/KrakGrenadeDescription" value="Confère aux Seigneurs du Chaos, Space Marines du Chaos, Havocs, Berserks de Khornes, Maîtres de Possession et Techmanciens la capacité de lancer des grenades anti-blindés."/>
	<entry name="ChaosSpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LasDamage" value="<string name='Traits/ChaosSpaceMarines/LasDamage'/>"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Augmente la pénétration d'armure des armes lasers et à plasma."/>
	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/LasDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchDescription'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Mechanoid" value="<string name='Traits/ChaosSpaceMarines/Mechanoid'/>"/>
	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Confère aux Champions du Chaos une chance de gagner une augmentation permanente d'armure lorsqu'ils tuent un ennemi (Bienfait du Chaos)"/>
	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="<string name='Traits/ChaosSpaceMarines/MechanoidFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MeleeDamage" value="<string name='Traits/ChaosSpaceMarines/MeleeDamage'/>"/>
	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Augmente la pénétration d'armure des armes de corps à corps."/>
	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="<string name='Traits/ChaosSpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="ChaosSpaceMarines/MeltaBombDescription" value="Confère aux Seigneurs du Chaos, Space Marines du Chaos, Havocs et Berserks de Khorne la capacité d'utiliser une bombe à fusion qui est très efficace contre les véhicules lourds et les fortifications."/>
	<entry name="ChaosSpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGods" value="Nos vies pour les Dieux"/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsDescription" value="Augmente le rythme de croissance de la population du Sacrifice de Cultiste."/>
	<entry name="ChaosSpaceMarines/OurLivesForTheGodsFlavor" value="Tant sont morts sous les lames des cultistes sur Gladius que chaque mort est vide de sens, creuse. Mais cela augmente aussi l'effet de leurs sacrifices, puisqu'ils attirent le monde plus près de la dimension déchue infernale des Dieux Sombres, et il devient plus facile pour leurs bienfaits sombres et franchir la barrière avec l'Immaterium.."/>
	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureDescription'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
	<entry name="ChaosSpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="ChaosSpaceMarines/SmokeLauncherDescription" value="Confère aux Rhinos du Chaos, Defilers et Land Raiders du Chaos la capacité de lancer un écran de fumée qui augmente la réduction de dégâts à distance."/>
	<entry name="ChaosSpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="ChaosSpaceMarines/TemporalDistortion" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortion'/>"/>
	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Confère aux Champions du Chaos une chance de gagner une augmentation permanente de mouvement lorsqu'ils tuent un ennemi (Bienfait du Chaos)."/>
	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="<string name='Traits/ChaosSpaceMarines/TemporalDistortionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWar'/>"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Réduit la perte de moral, et augmente la précision au corps à corps de l'infanterie contre les unités Space Marines."/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="<string name='Traits/ChaosSpaceMarines/VeteransOfTheLongWarFlavor'/>"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoyles" value="Gargouilles de feu Warp"/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesDescription" value="Confère des dégâts de durée aux armes des Rhinos du Chaos, Defilers et Land Raiders du Chaos."/>
	<entry name="ChaosSpaceMarines/WarpflameGargoylesFlavor" value="La gueule des armes de ce véhicule clignote d'un feu surnaturel."/>
	<entry name="ChaosSpaceMarines/WarpFrenzy" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzy'/>"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Confère aux Champions du Chaos une chance de gagner une augmentation permanente de leurs attaques lorsqu'ils tuent un ennemi (Bienfait du Chaos)"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="<string name='Traits/ChaosSpaceMarines/WarpFrenzyFlavor'/>"/>
	<entry name="CompendiumFlavor" value="Chaque faction étend ses villes d'une façon différente. Parfois c'est un Big Boss Ork qui va aller litérallement soulever et déplacer les murs protecteurs avec ses boys, ou peut-être un Seigneur Nécron qui va ordonner à ses esclaves de déterrer davantage de son ancienne tombe. Cependant, ils le font, c'est necessaire pour faire de la place pour de nouveaux bâtiments ingénieux qui étendent les capacités de leur faction."/>
    <entry name="Drukhari/AssaultWeaponBonus" value="<string name='Traits/Drukhari/AssaultWeaponBonus'/>"/>
    <entry name="Drukhari/AssaultWeaponBonusDescription" value="<string name='Upgrades/Eldar/AssaultWeaponBonusDescription'/>"/>
    <entry name="Drukhari/AssaultWeaponBonusFlavor" value="<string name='Traits/Drukhari/AssaultWeaponBonusFlavor'/>"/>
    <entry name="Drukhari/BetrayalCultureUpgrade" value="Assassinats ciblés"/>
    <entry name="Drukhari/BetrayalCultureUpgradeDescription" value="Accroît la loyauté des villes drukhari."/>
    <entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="La mort est une évidence dans les rues d'une cité drukhari, où les cadavres gisent à chaque coin de rue, attendant que les Ur-Ghuls les emportent… mais la mort des Dracons et des nobles de la kabales est plus rare. Du moins, c'était le cas jusqu'à ce que les incubes de l'Archon découvrent un nouveau complot. Désormais, le moindre soupçon de déloyauté se traduira par la mort d'un autre noble sur une pique au-dessus des quartiers de sommeil de l'Archon…"/>
    <entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
    <entry name="Drukhari/BonusResourcesDescription" value="<string name='Actions/Drukhari/BonusResourcesDescription'/>"/>
    <entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
    <entry name="Drukhari/CityTier2" value="<string name='Traits/Drukhari/CityTier2'/>"/>
    <entry name="Drukhari/CityTier2Description" value="<string name='Traits/Drukhari/CityTier2Description'/>"/>
    <entry name="Drukhari/CityTier2Flavor" value="<string name='Traits/Drukhari/CityTier2Flavor'/>"/>
    <entry name="Drukhari/CityTier3" value="<string name='Traits/Drukhari/CityTier3'/>"/>
    <entry name="Drukhari/CityTier3Description" value="<string name='Traits/Drukhari/CityTier3Description'/>"/>
    <entry name="Drukhari/CityTier3Flavor" value="<string name='Traits/Drukhari/CityTier3Flavor'/>"/>
    <entry name="Drukhari/CombatDrugsUpgrade" value="Culte du commerce froid"/>
    <entry name="Drukhari/CombatDrugsUpgradeDescription" value="Accorde à tous les fantassins drukhari la capacité d'utiliser des drogues de combat."/>
    <entry name="Drukhari/CombatDrugsUpgradeFlavor" value="Bien que les drogues de combat soient très répandues dans la société drukhari, ce sont les cultes wych qui les utilisent le plus volontiers, malgré leurs effets délétères sur leur physiologie et leur durée de vie. Disposer d'un agent de liaison du culte Wych ayant accès à leur immense réserve de composés horribles est un sérieux atout pour un Kabaliste effectuant des raids dans l'espace réel."/>
    <entry name="Drukhari/CorsairOutpostsUpgrade" value="Une ruée d'âme"/>
    <entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="Augmente la croissance par défaut des villes drukhari."/>
    <entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="Les rumeurs se répandent comme la peste à Commorragh : elles parlent de la richesse de Gladius Prime, de son milliard d'humains souffrants piégés sous la tempête Warp, et de l'étrange épine dorsale de la planète, remplie d'âmes perdues d'Aeldari… qu'elles soient vraies ou non, les Drukhari sont là. Mais qui a bien pu lancer ces rumeurs… ?"/>
    <entry name="Drukhari/EnergyBuildingBonus" value="<string name='Traits/Drukhari/EnergyBuildingBonus'/>"/>
    <entry name="Drukhari/EnergyBuildingBonusDescription" value="Augmente le rendement énergétique des bâtiments de production d'énergie."/>
    <entry name="Drukhari/EnergyBuildingBonusFlavor" value="<string name='Traits/Drukhari/EnergyBuildingBonusFlavor'/>"/>
    <entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
    <entry name="Drukhari/EnhancedAethersailsDescription" value="Confère au Raider, au Ravager et au Tantale la capacité d'augmenter leur mouvement."/>
    <entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
    <entry name="Drukhari/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
 	<entry name="Drukhari/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie."/>
    <entry name="Drukhari/ExtraInfantryArmourFlavor" value="Équiper une armée entière de plaques fantôme serait très coûteux. Mais pour les quelques privilégiés, l'Archon a dépensé sans compter. Fabriquée à partir d'étranges matériaux résineux et traversée par des poches de gaz plus léger que l'air, les plaques fantômes offrent une protection considérable tout en ne pesant presque rien."/>
    <entry name="Drukhari/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
    <entry name="Drukhari/ExtraVehicleArmourDescription" value="<string name='Traits/ExtraVehicleArmourDescription'/>"/>
    <entry name="Drukhari/ExtraVehicleArmourFlavor" value="Qu'ils soient équipés de champs de scintillement ou de boucliers nocturnes, les véhicules du Drukhari sont difficiles à repérer et à cibler. L'intégration de plaques fantômes dans les machines les plus prisées est coûteuse, mais elle réduit leur poids et augmente leur vitesse, réduisant encore la probabilité qu'elles soient touchées."/>
    <entry name="Drukhari/FieldRepairs" value="<string name='Actions/Drukhari/FieldRepairs'/>"/>
    <entry name="Drukhari/FieldRepairsDescription" value="Confère à tous les véhicules drukhari la capacité de restaurer les points de vie."/>
    <entry name="Drukhari/FieldRepairsFlavor" value="<string name='Actions/Drukhari/FieldRepairsFlavor'/>"/>
    <entry name="Drukhari/GraveLotus" value="Lotus tumulaire"/>
    <entry name="Drukhari/GraveLotusDescription" value="Les drogues de combat confèrent des dégâts supplémentaires en mêlée."/>
    <entry name="Drukhari/GraveLotusFlavor" value="Dans le Verger du Diable, de bruyants jardins suspendus de Lotus tumulaire jaillissent d'une mosaïque de morts. Champignon d'un violet éclatant, le lotus vole la force déclinante des morts récents pour favoriser sa propre croissance. Les Cultes Wych volent à leur tour, s'imprégnant du lotus sous forme liquide pour accroître leurs propres pouvoirs physiques."/>
    <entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
    <entry name="Drukhari/GrislyTrophiesDescription" value="Confère aux Venoms, Raiders, Ravagers et Tantales une aura qui réduit la perte de moral des unités drukhari alliées adjacentes."/>
    <entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
    <entry name="Drukhari/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
    <entry name="Drukhari/HammerOfWrathDescription" value="Confère à Cronos, aux Hellions, aux Reavers, aux Scourges et aux Talos la capacité d'effectuer des attaques plus dévastatrices."/>
    <entry name="Drukhari/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
    <entry name="Drukhari/HaywireGrenade" value="<string name='Weapons/HaywireGrenade'/>"/>
    <entry name="Drukhari/HaywireGrenadeDescription" value="Confère aux guerriers kabalites, aux succubes et aux wyches la capacité de lancer des grenades anti-véhicules."/>
    <entry name="Drukhari/HaywireGrenadeFlavor" value="<string name='Weapons/HaywireGrenadeFlavor'/>"/>
    <entry name="Drukhari/HeavyWeaponBonus" value="<string name='Traits/Drukhari/HeavyWeaponBonus'/>"/>
    <entry name="Drukhari/HeavyWeaponBonusDescription" value="Increases the armour penetration of heavy weapons."/>
    <entry name="Drukhari/HeavyWeaponBonusFlavor" value="<string name='Traits/Drukhari/HeavyWeaponBonusFlavor'/>"/>
    <entry name="Drukhari/Hypex" value="Hypex"/>
    <entry name="Drukhari/HypexDescription" value="Les drogues de combat permettent de se déplacer à couvert."/>
    <entry name="Drukhari/HypexFlavor" value="La capture d'un Psychneuein est une quête périlleuse, mais celui qui y parvient peut le vendre aux Cultes Wych pour un prix très élevé. La drogue Hypex, distillée à partir des fluides cérébraux de la créature insectoïde, augmente la vitesse de réaction des Drukhari, déjà très rapide, à des niveaux tout à fait étonnants."/>
    <entry name="Drukhari/MeleeWeaponBonus" value="<string name='Traits/Drukhari/MeleeWeaponBonus'/>"/>
    <entry name="Drukhari/MeleeWeaponBonusDescription" value="Augmente la pénétration de l'armure des armes de mêlée."/>
    <entry name="Drukhari/MeleeWeaponBonusFlavor" value="<string name='Traits/Drukhari/MeleeWeaponBonusFlavor'/>"/>
    <entry name="Drukhari/NightShields" value="<string name='Traits/Drukhari/NightShields'/>"/>
    <entry name="Drukhari/NightShieldsDescription" value="Augmente la réduction des dégâts à distance des raiders, ravageurs, chasseurs Razorwing, Tantale et bombardiers Voidraven."/>
    <entry name="Drukhari/NightShieldsFlavor" value="<string name='Traits/Drukhari/NightShieldsFlavor'/>"/>
    <entry name="Drukhari/Painbringer" value="Porteur de douleur"/>
    <entry name="Drukhari/PainbringerDescription" value="Les drogues de combat accordent une réduction des dégâts à Insensible à la souffrance ."/>
    <entry name="Drukhari/PainbringerFlavor" value="Seul le duc Sliscus, en exil, peut prétendre à un approvisionnement régulier en Porteur de douleur. Parmi les élixirs d'augmentation les plus rares, il durcit la peau de celui qui s'en imprègne pour en faire une gaine souple aussi résistante que du cuir durci. Ce processus est extrêmement douloureux, mais ses partisans considèrent que la douleur n'est qu'un prix insignifiant à payer."/>
    <entry name="Drukhari/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
    <entry name="Drukhari/PlasmaGrenadeDescription" value="Confère à l'Archonte, aux Fléaux, aux Succubes et aux Wyches la capacité de lancer des grenades polyvalentes."/>
    <entry name="Drukhari/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
    <entry name="Drukhari/RaiderFortress" value="<string name='Actions/Drukhari/RaiderFortress'/>"/>
    <entry name="Drukhari/RaiderFortressDescription" value="Permet de fonder de nouvelles villes sur les portails de la toile revendiqués."/>
    <entry name="Drukhari/RaiderFortressFlavor" value="<string name='Actions/Drukhari/RaiderFortressFlavor'/>"/>
    <entry name="Drukhari/RaidersTacticsDamage" value="<string name='Traits/Drukhari/RaidersTacticsDamage'/>"/>
    <entry name="Drukhari/RaidersTacticsDamageDescription" value="Augmente les dégâts des unités d'infanterie Drukhari lorsqu'elles débarquent d'un transport."/>
    <entry name="Drukhari/RaidersTacticsDamageFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageFlavor'/>"/>
    <entry name="Drukhari/RaidersTacticsDamageReduction" value="<string name='Traits/Drukhari/RaidersTacticsDamageReduction'/>"/>
    <entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Augmente la réduction des dégâts invulnérables des unités d'infanterie Drukhari lorsqu'elles débarquent d'un transport."/>
    <entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="<string name='Traits/Drukhari/RaidersTacticsDamageReductionFlavor'/>"/>
    <entry name="Drukhari/RaidersTacticsHealingRate" value="<string name='Traits/Drukhari/RaidersTacticsHealingRate'/>"/>
    <entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Augmente le taux de guérison des unités d'infanterie embarquées dans les transports."/>
    <entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="<string name='Traits/Drukhari/RaidersTacticsHealingRateFlavor'/>"/>
    <entry name="Drukhari/SacrificeToKhaine" value="<string name='Traits/Drukhari/SacrificeToKhaine'/>"/>
    <entry name="Drukhari/SacrificeToKhaineDescription" value="<string name='Actions/Drukhari/SacrificeToKhaineDescription'/>"/>
    <entry name="Drukhari/SacrificeToKhaineFlavor" value="<string name='Traits/Drukhari/SacrificeToKhaineFlavor'/>"/>
    <entry name="Drukhari/ShroudGate" value="<string name='Traits/Drukhari/ShroudGate'/>"/>
    <entry name="Drukhari/ShroudGateDescription" value="Augmente la réduction des dégâts à distance des unités qui traversent un portail de la toile."/>
    <entry name="Drukhari/ShroudGateFlavor" value="<string name='Traits/Drukhari/ShroudGateFlavor'/>"/>
    <entry name="Drukhari/SoulHungerCost" value="Locus d'âme"/>
    <entry name="Drukhari/SoulHungerCostDescription" value="Réduit le coût des capacités de Dévoreur d'âme."/>
    <entry name="Drukhari/SoulHungerCostFlavor" value="Au fur et à mesure que le raid des Drukhari dans l'espace réel s'intensifie, les liens entre ce monde et Commorragh deviennent de plus en plus nombreux. Même s'ils sont physiquement scellés, il est toujours possible pour le Kabaliste de puiser de l'énergie dans ces liens et d'y réinjecter la vie volée."/>
    <entry name="Drukhari/SoulHungerLoyalty" value="<string name='Traits/Drukhari/SoulHungerLoyalty'/>"/>
    <entry name="Drukhari/SoulHungerLoyaltyDescription" value="<string name='Actions/Drukhari/SoulHungerLoyaltyDescription'/>"/>
    <entry name="Drukhari/SoulHungerLoyaltyFlavor" value="<string name='Traits/Drukhari/SoulHungerLoyaltyFlavor'/>"/>
    <entry name="Drukhari/SoulHungerOutposts" value="<string name='Traits/Drukhari/SoulHungerOutposts'/>"/>
    <entry name="Drukhari/SoulHungerOutpostsDescription" value="<string name='Actions/Drukhari/SoulHungerOutpostsDescription'/>"/>
    <entry name="Drukhari/SoulHungerOutpostsFlavor" value="<string name='Traits/Drukhari/SoulHungerOutpostsFlavor'/>"/>
    <entry name="Drukhari/SoulHungerUpgrade" value="Sadiques de l'âme"/>
    <entry name="Drukhari/SoulHungerUpgradeDescription" value="Augmente l'influence accordée par les unités Drukhari lorsqu'elles tuent un ennemi."/>
    <entry name="Drukhari/SoulHungerUpgradeFlavor" value="“Vous devez les droguer. Tuez les gardes. Ouvrez la porte. Réveillez-les avec soin. Conduisez-les vers la liberté. Donnez-leur l'espoir de s'échapper. Commencez la poursuite pour de bon. Simulez votre mort. Montrez-leur une sortie. Puis dévoilez la mascarade. Et renvoyez-les dans la chambre de torture, avec un sourire malicieux. Sans les toucher, vous les avez imprégnés d'une douleur riche et spirituelle qui ne les quittera jamais.” – Gyrthineus Roche, Archon de la dernière lame."/>
    <entry name="Drukhari/FeastOfTorment" value="<string name='Traits/Drukhari/FeastOfTorment'/>"/>
    <entry name="Drukhari/FeastOfTormentDescription" value="<string name='Actions/Drukhari/FeastOfTormentDescription'/>"/>
    <entry name="Drukhari/FeastOfTormentFlavor" value="<string name='Traits/Drukhari/FeastOfTormentFlavor'/>"/>
    <entry name="Drukhari/SoulShelling" value="<string name='Actions/Drukhari/SoulShelling'/>"/>
    <entry name="Drukhari/SoulShellingDescription" value="<string name='Actions/Drukhari/SoulShellingDescription'/>"/>
    <entry name="Drukhari/SoulShellingFlavor" value="<string name='Actions/Drukhari/SoulShellingFlavor'/>"/>
    <entry name="Drukhari/Splintermind" value="Esprit éclaté"/>
    <entry name="Drukhari/SplintermindDescription" value="Les drogues de combat permettent de réduire les pertes de moral."/>
    <entry name="Drukhari/SplintermindFlavor" value="L'Esprit éclaté est fabriqué à partir des restes de cristal de terre d'un farseer Eldar décédé. Bien qu'elle ne garantisse pas la prescience, cette substance semblable à de la poussière permet à ceux qui l'absorbent de penser dans plusieurs directions à la fois—un atout inestimable, car la confusion de la bataille met à mal même le plan de bataille le plus rigoureux."/>
    <entry name="Drukhari/TormentGrenadeLaunchers" value="<string name='Weapons/TormentGrenadeLaunchers'/>"/>
    <entry name="Drukhari/TormentGrenadeLaunchersDescription" value="Confère aux Raiders, Ravagers et Tantales du tourment des lances grenades Tantalus."/>
    <entry name="Drukhari/TormentGrenadeLaunchersFlavor" value="<string name='Weapons/TormentGrenadeLaunchersFlavor'/>"/>
    <entry name="Drukhari/WealthPlunder" value="<string name='Actions/Drukhari/WealthPlunder'/>"/>
    <entry name="Drukhari/WealthPlunderDescription" value="<string name='Actions/Drukhari/WealthPlunderDescription'/>"/>
    <entry name="Drukhari/WealthPlunderFlavor" value="<string name='Actions/Drukhari/WealthPlunderFlavor'/>"/>
    <entry name="Drukhari/WeaponRacks" value="<string name='Traits/Drukhari/WeaponRacks'/>"/>
    <entry name="Drukhari/WeaponRacksDescription" value="Confère une double liaison aux armes à distance des unités débarquant d'un Raider ou d'un Tantale."/>
    <entry name="Drukhari/WeaponRacksFlavor" value="<string name='Traits/Drukhari/WeaponRacksFlavor'/>"/>
    <entry name="Drukhari/WebwayTravelAction" value="Irruption de l'espace réel"/>
    <entry name="Drukhari/WebwayTravelActionDescription" value="Supprime le coût de l'action du voyage par le portail de la toile."/>
    <entry name="Drukhari/WebwayTravelActionFlavor" value="Bien que le passage d'une passerelle soit simple en soi, la coordination des mouvements d'une grande armée ou d'une formation peut la rendre vulnérable. Pour les Drukhari, ce problème est résolu par la création de portes temporaires autour des portails permanents, ce qui permet à l'ensemble du groupe de se déplacer rapidement en une seule fois."/>
	<entry name="Eldar/AircraftBuildingBonus" value="<string name='Traits/Eldar/AircraftBuildingBonus'/>"/>
 	<entry name="Eldar/AircraftBuildingBonusDescription" value="Augmente la production fournie par les Portails dans les cimes."/>
 	<entry name="Eldar/AircraftBuildingBonusFlavor" value="<string name='Traits/Eldar/AircraftBuildingBonusFlavor'/>"/>
 	<entry name="Eldar/AssaultWeaponBonus" value="<string name='Traits/Eldar/AssaultWeaponBonus'/>"/>
 	<entry name="Eldar/AssaultWeaponBonusDescription" value="Augmente la pénétration d'armure des armes d'assaut."/>
 	<entry name="Eldar/AssaultWeaponBonusFlavor" value="<string name='Traits/Eldar/AssaultWeaponBonusFlavor'/>"/>
 	<entry name="Eldar/AsuryaniArrivalsBonus" value="Invocations du Grand prophète"/>
 	<entry name="Eldar/AsuryaniArrivalsBonusDescription" value="Réduit le coût d'Arrivées d'Asuryanis"/>
 	<entry name="Eldar/AsuryaniArrivalsBonusFlavor" value="Ma parole est plus qu'un simple avis: c'est une imprécation filtrée par la conscience de nos morts illustres, s'efforçant d'apporter un brin d'ordre à un univers désordonné. Notre race comprend ça et écoute mes propos, non pas comme ceux d'un dictateur mais comme ceux d'un élève attentif.”<br/>  — Grand prophète Kataimon de Malantai"/>
 	<entry name="Eldar/AsuryaniArrivalsBonus2" value="Promesse des Vaisseaux-mondes"/>
 	<entry name="Eldar/AsuryaniArrivalsBonus2Description" value="Réduit le temps de recharge d'Arrivées d'Asuryanis."/>
 	<entry name="Eldar/AsuryaniArrivalsBonus2Flavor" value="“Quand les Archontes et les Grand prophètes de plusieurs Vaisseaux-mondes partagent une vision—littéralement une vision du futur, notez—alors eux et leurs Vaisseaux-mondes travaillent en unisson. Ils vont vider leurs propres halls de vie et d'esprit pour accomplit cette promesse dans le futur..”<br/>  — Notes de cours, Grigomen Delr, Libre-marchand et xénologiste amateur"/>
 	<entry name="Eldar/CityTier2" value="<string name='Traits/Eldar/CityTier2'/>"/>
 	<entry name="Eldar/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
 	<entry name="Eldar/CityTier2Flavor" value="<string name='Traits/Eldar/CityTier2Flavor'/>"/>
 	<entry name="Eldar/CityTier3" value="<string name='Traits/Eldar/CityTier3'/>"/>
 	<entry name="Eldar/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
 	<entry name="Eldar/CityTier3Flavor" value="<string name='Traits/Eldar/CityTier3Flavor'/>"/>
	<entry name="Eldar/CleansingFlame" value="<string name='Actions/CleansingFlame'/>"/>
 	<entry name="Eldar/CleansingFlameDescription" value="Confère aux Prescients la capacité d'attaquer les ennemis à proximité avec une flamme psychique incandescente."/>
 	<entry name="Eldar/CleansingFlameFlavor" value="<string name='Actions/CleansingFlameFlavor'/>"/>
 	<entry name="Eldar/ConstructionBuildingBonus" value="<string name='Traits/Eldar/ConstructionBuildingBonus'/>"/>
 	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Augmente la production fournie par les Chapelles des chanteurs de moelle."/>
 	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="<string name='Traits/Eldar/ConstructionBuildingBonusFlavor'/>"/>
 	<entry name="Eldar/CrackShot" value="<string name='Traits/Eldar/CrackShot'/>"/>
 	<entry name="Eldar/CrackShotDescription" value="Augmente la précision et la pénétration d'armure des Dragons flamboyants."/>
 	<entry name="Eldar/CrackShotFlavor" value="<string name='Traits/Eldar/CrackShotFlavor'/>"/>
 	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Traits/Eldar/CrystalTargetingMatrix'/>"/>
 	<entry name="Eldar/CrystalTargetingMatrixDescription" value="Confère aux Hornets, Serpents ondoyants, Prismes de feu et Marcheurs de guerre la capacité d'augmenter temporairement leur précision."/>
 	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Traits/Eldar/CrystalTargetingMatrixFlavor'/>"/>
 	<entry name="Eldar/Dominate" value="<string name='Actions/Dominate'/>"/>
 	<entry name="Eldar/DominateDescription" value="Confère aux Chasseurs fantômes Hemlock la capacité d'incapaciter les unités ennemies qui ne sont ni des véhicules ni des fortifications."/>
 	<entry name="Eldar/DominateFlavor" value="<string name='Actions/DominateFlavor'/>"/>
 	<entry name="Eldar/ExpertHunter" value="<string name='Traits/Eldar/ExpertHunter'/>"/>
 	<entry name="Eldar/ExpertHunterDescription" value="Augmente les dégâts des Lances étincelantes contre les créatures monstrueuses, les véhicules et les fortifications."/>
 	<entry name="Eldar/ExpertHunterFlavor" value="<string name='Traits/Eldar/ExpertHunterFlavor'/>"/>
 	<entry name="Eldar/ExtraInfantryArmour" value="Armure composite labyrinthique"/>
 	<entry name="Eldar/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie."/>
 	<entry name="Eldar/ExtraInfantryArmourFlavor" value="Les armures composite Aeldari en thermoplastique ne sont pas conçues uniquement pour protéger—elles permettent aussi au porteur de se déplacer aussi facilement que si elles n'étaient pas là. Cependant, en rendant plus complexes leurs protocoles de dissipation, il est possible d'en augmenter la résilience sans perte de mobilité."/>
 	<entry name="Eldar/ExtraVehicleArmour" value="Infusion de moelle spectrale"/>
 	<entry name="Eldar/ExtraVehicleArmourDescription" value="Augmente l'armure des véhicules."/>
 	<entry name="Eldar/ExtraVehicleArmourFlavor" value="Tous les véhicules Aeldari et toutes leurs structures sont littéralement le fruit d'un chant. Les Chanteurs de moelle dont la maîtrise de leur voix et de leur expression psychique sont la contrepartie constructive au cri de guerre des Banshess huantes. Pourtant, avec les ressources de Gladius Prime et un changement de leurs harmonies internes, les Chanteurs de moelle peuvent infuser encore davantage de puissance dans leurs créations, augmentant davantage leur résistance."/>
 	<entry name="Eldar/FoodBuildingBonus" value="<string name='Traits/Eldar/FoodBuildingBonus'/>"/>
 	<entry name="Eldar/FoodBuildingBonusDescription" value="Augmente la production de nourriture des Jardins d'Isha."/>
 	<entry name="Eldar/FoodBuildingBonusFlavor" value="<string name='Traits/Eldar/FoodBuildingBonusFlavor'/>"/>
 	<entry name="Eldar/GhostwalkMatrix" value="Matrice de marche spectrale"/>
 	<entry name="Eldar/GhostwalkMatrixDescription" value="Permet aux Hornets, Serpents ondoyants, Prismes de feu et Marcheurs de guerre de se déplacer à couvert sans pénalité."/>
 	<entry name="Eldar/GhostwalkMatrixFlavor" value="La matrice de marche spectrale utilise la sagesse contenue dans une Pierre-Esprit pour guider les mouvements du véhicule."/>
 	<entry name="Eldar/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
 	<entry name="Eldar/HammerOfWrathDescription" value="Confère aux Lances étincelantes, Grands prophètes à motojet, Avatars de Khaine, Marcheurs de guerre, Wraithlords et aux Chevaliers fantômes la capacité d'exécuter des attaques plus puissantes."/>
 	<entry name="Eldar/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
 	<entry name="Eldar/HeavyWeaponBonus" value="<string name='Traits/Eldar/HeavyWeaponBonus'/>"/>
 	<entry name="Eldar/HeavyWeaponBonusDescription" value="Augmente la pénétration d'armure des armes lourdes."/>
 	<entry name="Eldar/HeavyWeaponBonusFlavor" value="<string name='Traits/Eldar/HeavyWeaponBonusFlavor'/>"/>
 	<entry name="Eldar/HoloFields" value="<string name='Traits/Eldar/HoloFields'/>"/>
 	<entry name="Eldar/HoloFieldsDescription" value="Confère aux Hornets, Serpents ondoyants et Prismes de feu une réduction invulnérable après s'être déplacé."/>
 	<entry name="Eldar/HoloFieldsFlavor" value="<string name='Traits/Eldar/HoloFieldsFlavor'/>"/>
 	<entry name="Eldar/InfantryBuildingBonus" value="<string name='Traits/Eldar/InfantryBuildingBonus'/>"/>
 	<entry name="Eldar/InfantryBuildingBonusDescription" value="Augmente la production fournie par les Creusets d'Asuryan."/>
 	<entry name="Eldar/InfantryBuildingBonusFlavor" value="<string name='Traits/Eldar/InfantryBuildingBonusFlavor'/>"/>
 	<entry name="Eldar/InfluenceBuildingBonus" value="<string name='Traits/Eldar/InfluenceBuildingBonus'/>"/>
 	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Augmente la production d'influence des Dômes de prophètes."/>
 	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="<string name='Traits/Eldar/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Eldar/MarksmansEye" value="<string name='Traits/Eldar/MarksmansEye'/>"/>
 	<entry name="Eldar/MarksmansEyeDescription" value="Augmente la précision des Chasseurs écarlates."/>
 	<entry name="Eldar/MarksmansEyeFlavor" value="<string name='Traits/Eldar/MarksmansEyeFlavor'/>"/>
 	<entry name="Eldar/MeleeWeaponBonus" value="<string name='Traits/Eldar/MeleeWeaponBonus'/>"/>
 	<entry name="Eldar/MeleeWeaponBonusDescription" value="Augmente la pénétration d'armure des armes de corps à corps."/>
 	<entry name="Eldar/MeleeWeaponBonusFlavor" value="<string name='Traits/Eldar/MeleeWeaponBonusFlavor'/>"/>
 	<entry name="Eldar/OreBuildingBonus" value="<string name='Traits/Eldar/OreBuildingBonus'/>"/>
 	<entry name="Eldar/OreBuildingBonusDescription" value="Augmente la production de minerai des Altmarls de Vaul."/>
 	<entry name="Eldar/OreBuildingBonusFlavor" value="<string name='Traits/Eldar/OreBuildingBonusFlavor'/>"/>
 	<entry name="Eldar/PlasmaGrenade" value="<string name='Weapons/PlasmaGrenade'/>"/>
 	<entry name="Eldar/PlasmaGrenadeDescription" value="Permet aux Gardiens de lancer des grenades versatiles."/>
 	<entry name="Eldar/PlasmaGrenadeFlavor" value="<string name='Weapons/PlasmaGrenadeFlavor'/>"/>
 	<entry name="Eldar/ResearchBuildingBonus" value="<string name='Traits/Eldar/ResearchBuildingBonus'/>"/>
 	<entry name="Eldar/ResearchBuildingBonusDescription" value="Augmente la production de recherche des Ossuaires des spirites."/>
 	<entry name="Eldar/ResearchBuildingBonusFlavor" value="<string name='Traits/Eldar/ResearchBuildingBonusFlavor'/>"/>
 	<entry name="Eldar/SpiritPreservationBonus" value="Monture en écusson"/>
 	<entry name="Eldar/SpiritPreservationBonusDescription" value="Augmente le gain d'énergie quand les unités Aeldari meurent."/>
 	<entry name="Eldar/SpiritPreservationBonusFlavor" value="Depuis des temps immémoriaux, nous avons monté nos pierres-esprits dans de simples enveloppes, confiants dans la capacité des porteurs à les préserver. Mais, les anciennes structures de la Toile que nous avons rencontrées sur Gladius Prime démontrent qu'un bouclier complexe peut-être construit dans l'enveloppe en moelle spectrale, permettant de sauver un plus grand nombre de nos guerriers morts des mâchoirs de Celle qui attend."/>
 	<entry name="Eldar/SpiritStones" value="<string name='Traits/Eldar/SpiritStones'/>"/>
 	<entry name="Eldar/SpiritStonesDescription" value="Réduit la perte de moral des Hornets, Serpents ondoyants, Prismes de feu et Marcheurs de guerre."/>
 	<entry name="Eldar/SpiritStonesFlavor" value="<string name='Traits/Eldar/SpiritStonesFlavor'/>"/>
 	<entry name="Eldar/StarEngines" value="<string name='Traits/Eldar/StarEngines'/>"/>
 	<entry name="Eldar/StarEnginesDescription" value="Augmente le mouvement des Hornets, Serpents ondoyants, Prismes de feu, Marcheurs de guerre et Scorpions."/>
 	<entry name="Eldar/StarEnginesFlavor" value="<string name='Traits/Eldar/StarEnginesFlavor'/>"/>
 	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
 	<entry name="Eldar/TranscendentBlissDescription" value="Confère aux villes la capacité de dépenser de l'influence pour une augmentation temporaire de loyauté."/>
 	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
 	<entry name="Eldar/TranscendentBlissBonus" value="La fatalité des Aeldari"/>
 	<entry name="Eldar/TranscendentBlissBonusDescription" value="Augmente le gain de loyauté de Bonheur transcendant."/>
	<entry name="Eldar/TranscendentBlissBonusFlavor" value="“Pour un esprit libre d'Aeldari, il n'y a ni raison ni folie, mais simplement une vague d'existence parfaite, remplie de son propre élan sauvage.”<br/>  — Ralamine Mung, Ordo Xenos"/>
 	<entry name="Eldar/VectoredEngines" value="<string name='Traits/Eldar/VectoredEngines'/>"/>
 	<entry name="Eldar/VectoredEnginesDescription" value="Confère aux Hornets, Serpent ondoyants, Prismes de feu, Marcheurs de guerre et Scorpions la capacité d'augmenter temporairement leur armure contre les attaques à distance."/>
 	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Traits/Eldar/VectoredEnginesFlavor'/>"/>
 	<entry name="Eldar/VehicleBuildingBonus" value="<string name='Traits/Eldar/VehicleBuildingBonus'/>"/>
 	<entry name="Eldar/VehicleBuildingBonusDescription" value="Augmente la production fournie par les Grandes entrées."/>
 	<entry name="Eldar/VehicleBuildingBonusFlavor" value="<string name='Traits/Eldar/VehicleBuildingBonusFlavor'/>"/>
 	<entry name="Eldar/WarShout" value="<string name='Actions/Eldar/WarShout'/>"/>
 	<entry name="Eldar/WarShoutDescription" value="Confère aux Banshees huantes la capacité de démoraliser les ennemis adjacents."/>
 	<entry name="Eldar/WarShoutFlavor" value="<string name='Actions/Eldar/WarShoutFlavor'/>"/>
 	<entry name="Eldar/WebwayGateBonus" value="Cartographie de la Toile"/>
 	<entry name="Eldar/WebwayGateBonusDescription" value="Supprime le coût d'activation des Portails vers la Toile."/>
 	<entry name="Eldar/WebwayGateBonusFlavor" value="Seuls les Arlequins et les gardiens de la Bibliothèque Interdite connaissent vraiment le plan transdimensionnel de la Toile. Mais en nous donnant une partie de ces connaissances, seulement à propos de ce monde, les Archivistes se sont assurés que nous puissions réouvrir ces anciens portails avec facilité."/>
 	<entry name="Eldar/WebwayGateBonus2" value="Constriction de la Toile"/>
 	<entry name="Eldar/WebwayGateBonus2Description" value="Enlève le coût en action du Voyage à travers la Toile."/>
 	<entry name="Eldar/WebwayGateBonus2Flavor" value="Après avoir reçu les conseils de la Bibliothèque Interdite sur le plan unique de la Toile sur Gladius, les spirites ont planifié les chemins les plus courts entre deux portails, rendant le chemin entre eux aussi facile que n'importe quel pas."/>
	<entry name="Eldar/WebwayRedoubt" value="Forteresse de la Toile"/>
	<entry name="Eldar/WebwayRedoubtDescription" value="Permet de créer de nouvelles villes sur les Portails vers la Toile contrôlés."/>
	<entry name="Eldar/WebwayRedoubtFlavor" value="<string name='Actions/Eldar/WebwayRedoubtFlavor'/>"/>
 	<entry name="Eldar/WraithknightStarcannon" value="Canons stellaires de Chevalier fantôme"/>
 	<entry name="Eldar/WraithknightStarcannonDescription" value="Confère aux Chevaliers fantômes deux canons stellaires."/>
 	<entry name="Eldar/WraithknightStarcannonFlavor" value="<string name='Weapons/StarcannonFlavor'/>"/>
	<entry name="Necrons/AircraftBuildingBonus" value="<string name='Traits/Necrons/AircraftBuildingBonus'/>"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Augmente la production fournie par les Pistes sans nom."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="<string name='Traits/Necrons/AircraftBuildingBonusFlavor'/>"/>
	<entry name="Necrons/AttackCityBonus" value="<string name='Traits/Necrons/AttackCityBonus'/>"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Augmente la précision contre les unités ennemies dans les villes."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="<string name='Traits/Necrons/AttackCityBonusFlavor'/>"/>
	<entry name="Necrons/BlastDamage" value="<string name='Traits/Necrons/BlastDamage'/>"/>
	<entry name="Necrons/BlastDamageDescription" value="Augmente la pénétration d'armure des armes à explosion et souffle."/>
	<entry name="Necrons/BlastDamageFlavor" value="<string name='Traits/Necrons/BlastDamageFlavor'/>"/>
	<entry name="Necrons/CityDefenseBonus" value="Obstacles indicibles"/>
	<entry name="Necrons/CityDefenseBonusDescription" value="Augmente la réduction de dégâts des unités dans les villes."/>
	<entry name="Necrons/CityDefenseBonusFlavor" value="Les attaques contre les défenseurs de la ville-tombe échouent désormais mystérieusement—les tirs d'énergie disparaissent, les armes à gravité sont déviées, et les projectiles physiques semblent être attrapés par les murs cyclopéens des tombes."/>
	<entry name="Necrons/CityTier2" value="<string name='Traits/Necrons/CityTier2'/>"/>
	<entry name="Necrons/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier2Flavor" value="<string name='Traits/Necrons/CityTier2Flavor'/>"/>
	<entry name="Necrons/CityTier3" value="<string name='Traits/Necrons/CityTier3'/>"/>
	<entry name="Necrons/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Necrons/CityTier3Flavor" value="<string name='Traits/Necrons/CityTier3Flavor'/>"/>
	<entry name="Necrons/ConstructionBuildingBonus" value="<string name='Traits/Necrons/ConstructionBuildingBonus'/>"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Augmente la productrion fournie par les Mastabas des esclaves."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="<string name='Traits/Necrons/ConstructionBuildingBonusFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor" value="<string name='Actions/Necrons/DimensionalCorridor'/>"/>
	<entry name="Necrons/DimensionalCorridorDescription" value="Permet à l'infanterie de se téléporter près des villes Nécrons alliées et des Monolithes alliés."/>
	<entry name="Necrons/DimensionalCorridorFlavor" value="<string name='Actions/Necrons/DimensionalCorridorFlavor'/>"/>
	<entry name="Necrons/DimensionalCorridor2" value="Stabilité dimensionnelle"/>
	<entry name="Necrons/DimensionalCorridor2Description" value="Réduit le coût en influence de Corridor Dimensionnel."/>
	<entry name="Necrons/DimensionalCorridor2Flavor" value="Utiliser la puissance de la technologie Ancienne abandonnée a permis aux Nécrons de stabiliser leur technologie de récupération de troupes, réduisant rgandement le coût de déphasage des guerriers vers les Portails d'Eternité."/>
	<entry name="Necrons/DimensionalCorridor3" value="Sanction dimensionnelle"/>
	<entry name="Necrons/DimensionalCorridor3Description" value="Supprime le coût en action et mouvement du Corridor Dimensionnel."/>
	<entry name="Necrons/DimensionalCorridor3Flavor" value="La sanction d'un Cryptek rusé a permis aux Nécrons de régler précisément leur technologie de rappel des torupes, réduisant de manière importante la déviation lors du déphasage."/>
	<entry name="Necrons/EnergyBuildingBonus" value="<string name='Traits/Necrons/EnergyBuildingBonus'/>"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Augmente la production d'énergie des Noyaux énergétiques."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="<string name='Traits/Necrons/EnergyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Necrons/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie."/>
	<entry name="Necrons/ExtraInfantryArmourFlavor" value="Même après des millénaires de guerre et de sommeil, les Crypteks Nécrons n'ont pas perdu leur désir d'innovation. En faisant des adaptations mineurs au nécroderme, ils sont capables d'augmenter la survivabilité de leurs troupes face aux armes d'aujourd'hui."/>
	<entry name="Necrons/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Necrons/ExtraVehicleArmourDescription" value="Augmente le blindage des véhicules et des unités Canoptek."/>
	<entry name="Necrons/ExtraVehicleArmourFlavor" value="Pour un béotien, ces véhicules Nécrons sont identiques à leurs prédecesseurs. Pour un Cryptek, ou même un Aeldari érudit, les matériaux du véhicule ont augmenté sa résistance, sans affecter sa densité ou son poids."/>
	<entry name="Necrons/GaussDamage" value="<string name='Traits/GaussDamage'/>"/>
	<entry name="Necrons/GaussDamageDescription" value="Augmente la pénétration d'armure des armes à fission."/>
	<entry name="Necrons/GaussDamageFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="Necrons/GaussPylon" value="<string name='Units/Necrons/GaussPylon'/>"/>
	<entry name="Necrons/GaussPylonDescription" value="Confère aux villes la capacité de créer de puissantes fortifications à fission."/>
	<entry name="Necrons/GaussPylonFlavor" value="<string name='Units/Necrons/GaussPylonFlavor'/>"/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="Augmente la réduction des dégâts de décharge psy des Mécarachnides et des unités alliées adjacentes."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GrowthBonus" value="<string name='Traits/Necrons/GrowthBonus'/>"/>
	<entry name="Necrons/GrowthBonusDescription" value="Augmente la vitesse de croissance de la population des villes Nécrons."/>
	<entry name="Necrons/GrowthBonusFlavor" value="<string name='Traits/Necrons/GrowthBonusFlavor'/>"/>
	<entry name="Necrons/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Necrons/HammerOfWrathDescription" value="Confère aux Seigneurs Destroyers, Mécanoptères, Mécarachnides, C'tan Transcendants, Prétoriens du Triarcat, et Rôdeurs du Triarcat la capacité d'effectuer des attaques plus puissantes."/>
	<entry name="Necrons/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Necrons/HousingBuildingBonus" value="<string name='Traits/Necrons/HousingBuildingBonus'/>"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Augmente la limite de population des Abris."/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="<string name='Traits/Necrons/HousingBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfantryBuildingBonus" value="<string name='Traits/Necrons/InfantryBuildingBonus'/>"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Augmente la production fournie par les Noyaux d'invocation."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="<string name='Traits/Necrons/InfantryBuildingBonusFlavor'/>"/>
	<entry name="Necrons/InfluenceBuildingBonus" value="<string name='Traits/Necrons/InfluenceBuildingBonus'/>"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Augmente la production d'influence des Stèles."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="<string name='Traits/Necrons/InfluenceBuildingBonusFlavor'/>"/>
	<entry name="Necrons/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="Necrons/LivingMetal2" value="Formes Immortelles"/>
	<entry name="Necrons/LivingMetal2Description" value="Augmente le soin fourni par Métal Vivant."/>
	<entry name="Necrons/LivingMetal2Flavor" value="Dans un certain sens obscur, les machines Nécrons se souviennent désormais de leur ancienne gloire et s'efforcent tout le temps de la regagner, faisant couler leur métal vivant plus vite pour réparer tout dégât."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="<string name='Traits/Necrons/LoyaltyBuildingBonus'/>"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Augmente la production de loyauté des Autels baroques."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="<string name='Traits/Necrons/LoyaltyBuildingBonusFlavor'/>"/>
	<entry name="Necrons/MeleeDamage" value="<string name='Traits/Necrons/MeleeDamage'/>"/>
	<entry name="Necrons/MeleeDamageDescription" value="Augmente la pénétration d'armure des armes de corps à corps."/>
	<entry name="Necrons/MeleeDamageFlavor" value="<string name='Traits/Necrons/MeleeDamageFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="<string name='Traits/Necrons/Nebuloscope'/>"/>
	<entry name="Necrons/NebuloscopeDescription" value="Permet aux Mécanoptères d'ignorer les réductions de dégât à distance de leurs cibles."/>
	<entry name="Necrons/NebuloscopeFlavor" value="<string name='Traits/Necrons/NebuloscopeFlavor'/>"/>
	<entry name="Necrons/NecrodermisRepair2" value="Croissance accélérée"/>
	<entry name="Necrons/NecrodermisRepair2Description" value="Augmente la récupération de points de vie de Réparation du Nécroderme."/>
	<entry name="Necrons/NecrodermisRepair2Flavor" value="Les Crypteks ont de nouveau amélioré la structure nanomécanique des corps de métal vivant des Nécrons, leur permettant de récupérer de presque n'importe quel dégât."/>
	<entry name="Necrons/NecrodermisRepair3" value="Terrible Nécroderme"/>
	<entry name="Necrons/NecrodermisRepair3Description" value="Supprime le temps de rechargement de Réparation du Nécroderme."/>
	<entry name="Necrons/NecrodermisRepair3Flavor" value="Le métal vivant qui constitue les corps des Nécrons et leurs véhicules s'agitent constamment, se reconstruisant à chaque instant."/>
	<entry name="Necrons/OreBuildingBonus" value="<string name='Traits/Necrons/OreBuildingBonus'/>"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Augmente la production de minerai des Carrières Al-Khemic."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="<string name='Traits/Necrons/OreBuildingBonusFlavor'/>"/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Accorde aux barges d'anéantissement, aux arches fantômes, aux arches du Jugement dernier et aux traqueurs de triarques une réduction des dégâts invulnérable qui est mise en veilleuse au début du tour suivant si des dégâts sont infligés."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/RapidRiseBonus" value="L'ordre du tétrarque"/>
	<entry name="Necrons/RapidRiseBonusDescription" value="Réduit le coût de Réveil Rapide."/>
	<entry name="Necrons/RapidRiseBonusFlavor" value="Le Seigneur renforce les protocoles maître-esclave, pour que ses ordres soient exécutés avec davantage de vitesse et moins de réflexiont—et moins de liberté."/>
	<entry name="Necrons/ReanimationProtocols2" value="Protocoles de réanimation efficaces"/>
	<entry name="Necrons/ReanimationProtocols2Description" value="Augmente le soin fourni par Protocoles de réanimation."/>
	<entry name="Necrons/ReanimationProtocols2Flavor" value="Avec ces systèmes de résurrection et réparation dérivés des Crypteks, les Nécrons ont plus de chance d'ignorer des dégâts en apparence fatals."/>
	<entry name="Necrons/ResearchBuildingBonus" value="<string name='Traits/Necrons/ResearchBuildingBonus'/>"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Augmente la production de recherche des Archives interdites."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="<string name='Traits/Necrons/ResearchBuildingBonusFlavor'/>"/>
	<entry name="Necrons/ScarabHive" value="<string name='Actions/Necrons/ScarabHive'/>"/>
	<entry name="Necrons/ScarabHiveDescription" value="Confère aux Mécarachnides la capacité de construire des Scarabées Canopteks."/>
	<entry name="Necrons/ScarabHiveFlavor" value="<string name='Actions/Necrons/ScarabHiveFlavor'/>"/>
	<entry name="Necrons/SeismicAssault" value="Assaut sismique"/>
	<entry name="Necrons/SeismicAssaultDescription" value="Confère aux C'tans transcendants et aux Cryptes tesseracts la capacité d'effectuer une attaque dévastatrice."/>
	<entry name="Necrons/SeismicAssaultFlavor" value="<string name='Weapons/SeismicAssaultTranscendentFlavor'/>"/>
	<entry name="Necrons/ShieldVane" value="<string name='Traits/Necrons/ShieldVane'/>"/>
	<entry name="Necrons/ShieldVaneDescription" value="Augmente l'armure des Mécanoptères."/>
	<entry name="Necrons/ShieldVaneFlavor" value="<string name='Traits/Necrons/ShieldVaneFlavor'/>"/>
	<entry name="Necrons/TeslaDamage" value="<string name='Traits/TeslaDamage'/>"/>
	<entry name="Necrons/TeslaDamageDescription" value="Augmente la pénétration d'armure des armes tesla."/>
	<entry name="Necrons/TeslaDamageFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="Necrons/TheBoundCoalescent" value="<string name='Actions/Necrons/TheBoundCoalescent'/>"/>
	<entry name="Necrons/TheBoundCoalescentDescription" value="Confère aux C'tans transcendants la capacité de fusionner avec les Obélisques pour créer des Cryptes tesseracts."/>
	<entry name="Necrons/TheBoundCoalescentFlavor" value="<string name='Actions/Necrons/TheBoundCoalescentFlavor'/>"/>
	<entry name="Necrons/VehiclesBuildingBonus" value="<string name='Traits/Necrons/VehiclesBuildingBonus'/>"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Augmente la production fournie par les Temples Hypostyles."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="<string name='Traits/Necrons/VehiclesBuildingBonusFlavor'/>"/>
	<entry name="Orks/AmmoRunt" value="<string name='Actions/AmmoRunt'/>"/>
	<entry name="Orks/AmmoRuntDescription" value="Confère aux Gros meks, Frimeurs et Mek gunz la capacité d'augmenter leur précision à distance."/>
	<entry name="Orks/AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Orks/BattlewagonBigShootas" value="Gros fling' de Chariots de guerre"/>
	<entry name="Orks/BattlewagonBigShootasDescription" value="Confère aux Chariots de guerre des Gros fling'."/>
	<entry name="Orks/BattlewagonBigShootasFlavor" value="<string name='Weapons/BigShootaFlavor'/>"/>
	<entry name="Orks/BattlewagonRokkitLaunchas" value="Lance-rokettes de Chariots de guerre"/>
	<entry name="Orks/BattlewagonRokkitLaunchasDescription" value="Confère aux Chariots de guerre des Lance-rokettes."/>
	<entry name="Orks/BattlewagonRokkitLaunchasFlavor" value="<string name='Weapons/RokkitLaunchaFlavor'/>"/>
	<entry name="Orks/Bigbomm" value="<string name='Weapons/Bigbomm'/>"/>
	<entry name="Orks/BigbommDescription" value="Confère aux Kopters la capacité de larguer des bombes anti-infanterie."/>
	<entry name="Orks/BigbommFlavor" value="<string name='Weapons/BigbommFlavor'/>"/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/Orks/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="Augmente la pénétration d'armure des grenades, missiles et armes à explosion."/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/Orks/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="<string name='Traits/Orks/BoltDamage'/>"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="<string name='Traits/Orks/BoltDamageFlavor'/>"/>
	<entry name="Orks/BonusBeastsProduction" value="<string name='Traits/Orks/BonusBeastsProduction'/>"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="<string name='Traits/Orks/BonusBeastsProductionDescription'/>"/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="<string name='Traits/Orks/BonusBeastsProductionFlavor'/>"/>
	<entry name="Orks/BonusColonizersProduction" value="<string name='Traits/Orks/BonusColonizersProduction'/>"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="<string name='Traits/Orks/BonusColonizersProductionDescription'/>"/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="<string name='Traits/Orks/BonusColonizersProductionFlavor'/>"/>
	<entry name="Orks/BonusInfantryProduction" value="<string name='Traits/Orks/BonusInfantryProduction'/>"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="<string name='Traits/Orks/BonusInfantryProductionDescription'/>"/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="<string name='Traits/Orks/BonusInfantryProductionFlavor'/>"/>
	<entry name="Orks/BonusVehiclesProduction" value="<string name='Traits/Orks/BonusVehiclesProduction'/>"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="<string name='Traits/Orks/BonusVehiclesProductionDescription'/>"/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="<string name='Traits/Orks/BonusVehiclesProductionFlavor'/>"/>
	<entry name="Orks/Bosspole" value="Ikon' de Boss"/>
	<entry name="Orks/BosspoleDescription" value="Réduit davantage les pertes de moral de Mouvement de Foule pour les Gros Meks, les Boys, les Meganobs, les Kasseurs de tanks, les Motos de guerre et les Big boss."/>
	<entry name="Orks/BosspoleFlavor" value="Les Nobz arborent souvent leurs trophées sur un totem pour montrer qu'on ne leut cherche pas des noises impunément. Cette Ikon' sert aussi de trique pour ramener un semblant d'ordre dans le feu de l'action."/>
 	<entry name="Orks/CityEnergy" value="<string name='Traits/Orks/CityEnergy'/>"/>
 	<entry name="Orks/CityEnergyDescription" value="Increases the energy output of Ork cities."/>
 	<entry name="Orks/CityEnergyFlavor" value="<string name='Traits/Orks/CityEnergyFlavor'/>"/>
	<entry name="Orks/CityGrowth" value="<string name='Traits/Orks/CityGrowth'/>"/>
	<entry name="Orks/CityGrowthDescription" value="<string name='Traits/Orks/CityGrowthDescription'/>"/>
	<entry name="Orks/CityGrowthFlavor" value="<string name='Traits/Orks/CityGrowthFlavor'/>"/>
	<entry name="Orks/CityInfluence" value="<string name='Traits/Orks/CityInfluence'/>"/>
	<entry name="Orks/CityInfluenceDescription" value="Augmente la production d'influence des villes Orks."/>
	<entry name="Orks/CityInfluenceFlavor" value="<string name='Traits/Orks/CityInfluenceFlavor'/>"/>
	<entry name="Orks/CityLoyalty" value="<string name='Traits/Orks/CityLoyalty'/>"/>
	<entry name="Orks/CityLoyaltyDescription" value="Augmente la production de loyauté des villes Orks."/>
	<entry name="Orks/CityLoyaltyFlavor" value="<string name='Traits/Orks/CityLoyaltyFlavor'/>"/>
	<entry name="Orks/CityPopulationLimit" value="<string name='Traits/Orks/CityPopulationLimit'/>"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Augmente la limite de population dans les villes Orks."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="<string name='Traits/Orks/CityPopulationLimitFlavor'/>"/>
	<entry name="Orks/CityResearch" value="<string name='Traits/Orks/CityResearch'/>"/>
	<entry name="Orks/CityResearchDescription" value="Augmente la production de recherche dans les villes Orks."/>
	<entry name="Orks/CityResearchFlavor" value="<string name='Traits/Orks/CityResearchFlavor'/>"/>
	<entry name="Orks/CityTier2" value="<string name='Traits/Orks/CityTier2'/>"/>
	<entry name="Orks/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier2Flavor" value="<string name='Traits/Orks/CityTier2Flavor'/>"/>
	<entry name="Orks/CityTier3" value="<string name='Traits/Orks/CityTier3'/>"/>
	<entry name="Orks/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Orks/CityTier3Flavor" value="<string name='Traits/Orks/CityTier3Flavor'/>"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeath" value="Décomposition permanente"/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathDescription" value="Permet aux unités de créer des champignons orkoïdes permanents lorsqu'elles meurent."/>
	<entry name="Orks/CreatePermanentOrkoidFungusOnDeathFlavor" value="<string name='Traits/Orks/CreateOrkoidFungusOnDeathFlavor'/>"/>
	<entry name="Orks/DakkajetSupaShoota" value="Super Fling' de Dakkajet"/>
	<entry name="Orks/DakkajetSupaShootaDescription" value="Confère aux Dakkajets un Super fling'."/>
	<entry name="Orks/DakkajetSupaShootaFlavor" value="<string name='Weapons/TwinLinkedSupaShootaFlavor'/>"/>
	<entry name="Orks/EavyArmour" value="<string name='Traits/EavyArmour'/>"/>
	<entry name="Orks/EavyArmourDescription" value="Augmente l'armure des Boys et Big Boss."/>
	<entry name="Orks/EavyArmourFlavor" value="<string name='Traits/EavyArmourFlavor'/>"/>
	<entry name="Orks/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Orks/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie."/>
	<entry name="Orks/ExtraInfantryArmourFlavor" value="Pour n'importe quelle autre race, l'armure Ork semble ridicule : des gros paquets de plaques de métal épaisses, attachés par des câbles, des clous et d'autres trucs sans queue ni tête, peintes en doré par des Mékanos et des grots. Mais avec un peu de foi Ork, elles marchent du tonnerre."/>
	<entry name="Orks/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Orks/ExtraVehicleArmourDescription" value="Augmente l'armure des véhicules."/>
	<entry name="Orks/ExtraVehicleArmourFlavor" value="Quand un Mékano fait face à un Big boss en colère parce que ses buggies et chariots de guerre se font exploser, il ordonne rapidement à ses grots de déterrer de la feraille et fixe du blindage supplémentaire partout où il peut. Il sait que les Fondus d'la Vitesse vont rapidement le détacher de toute façon."/>
	<entry name="Orks/Flyboss" value="<string name='Traits/Orks/Flyboss'/>"/>
	<entry name="Orks/FlybossDescription" value="Augmente la précision à distance des Dakkajets contre les volants, les motogravs et les antigravs."/>
	<entry name="Orks/FlybossFlavor" value="<string name='Traits/Orks/FlybossFlavor'/>"/>
	<entry name="Orks/GrabbinKlaw" value="<string name='Actions/GrabbinKlaw'/>"/>
	<entry name="Orks/GrabbinKlawDescription" value="Confère aux Chariots de guerre la capacité d'immobiliser les véhicules terrestres ennemis."/>
	<entry name="Orks/GrabbinKlawFlavor" value="<string name='Actions/GrabbinKlawFlavor'/>"/>
	<entry name="Orks/GrotRiggers" value="Grot bidouilleurs"/>
	<entry name="Orks/GrotRiggersDescription" value="Confère aux Warbuggies, Killa Kans, Battlewagons, Deff Dreads, Gorkanauts et Kill Burstas une régénération passive des points de vie."/>
	<entry name="Orks/GrotRiggersFlavor" value="<string name='Traits/GrotRiggersFlavor'/>"/>
	<entry name="Orks/HealingRate" value="La marée verte"/>
	<entry name="Orks/HealingRateDescription" value="Augmente la vitesse de soin des unités."/>
	<entry name="Orks/HealingRateFlavor" value="Grâce à la coopération d'une équipe de Doks mabouls et de Fouettards, votre Seigneur de guerre a prescrit une nouvelle diète, 'plus Orkesque', pour rendre les boys plus grands et plus fort, plus vite. Que les Squigs huileux au petit-déjeuner et les Mange-Face au dîner aient réellement un effet physique n'est pas important—les boys y croient, et ça le fait marcher."/>
	<entry name="Orks/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Orks/HammerOfWrathDescription" value="Confère aux Kopters, Deff Dreads, Squiggoths gargantuan, Gorkanauts, Boit'kitu et warbikers la capacité d'effectuer des attaques plus puissantes."/>
	<entry name="Orks/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="<string name='Traits/Orks/MeleeDamage'/>"/>
	<entry name="Orks/MeleeDamageDescription" value="Augmente la pénétration d'armure des armes de corps à corps."/>
	<entry name="Orks/MeleeDamageFlavor" value="<string name='Traits/Orks/MeleeDamageFlavor'/>"/>
	<entry name="Orks/MightMakesRight2" value="Krégnez lé Orks!"/>
	<entry name="Orks/MightMakesRight2Description" value="Augmente l'influence gagnée lorsqu'une unité inflige des dégâts."/>
	<entry name="Orks/MightMakesRight2Flavor" value="Quand une Waaagh! est suffisament puissante, elle a une sorte d'effet multipliant, où chaque coup rend plus puissant le champ psychique, en une sorte de cercle vicieux ultra-violent à la sauce Ork."/>
	<entry name="Orks/OrkoidFungusBonusHealingRate" value="<string name='Traits/OrkoidFungusBonusHealingRate'/>"/>
	<entry name="Orks/OrkoidFungusBonusHealingRateDescription" value="Augmente le soin fourni par les Champignons orkoïdes."/>
	<entry name="Orks/OrkoidFungusBonusHealingRateFlavor" value="<string name='Traits/OrkoidFungusBonusHealingRateFlavor'/>"/>
	<entry name="Orks/OrkoidFungusFood" value="<string name='Traits/OrkoidFungusFood'/>"/>
	<entry name="Orks/OrkoidFungusFoodDescription" value="Augmente la production de nourriture sur les cases avec des Champignons orkoïdes."/>
	<entry name="Orks/OrkoidFungusFoodFlavor" value="<string name='Traits/OrkoidFungusFoodFlavor'/>"/>
	<entry name="Orks/RedPaintJob" value="Peintur' rouj"/>
	<entry name="Orks/RedPaintJobDescription" value="Augmente les dégâts des Buggies, Brikojets Mégach'nillés, Chariots de guerre, Dakkajets et Krama-bombas."/>
	<entry name="Orks/RedPaintJobFlavor" value="<string name='Traits/RedPaintJobFlavor'/>"/>
	<entry name="Orks/Scavenger2" value="Grots ramasseurs"/>
	<entry name="Orks/Scavenger2Description" value="Augmente la quantité de minerai récupéré lorsque vous tuez une unité ennemie."/>
	<entry name="Orks/Scavenger2Flavor" value="Un Fouettard bien organisé aura entraîné ses grots à rassembler et trier la ferraille pour les Orks, et aussi à éviter les obus et munitions plus dangereuses."/>
	<entry name="Orks/SkorchaMissile" value="<string name='Weapons/SkorchaMissile'/>"/>
	<entry name="Orks/SkorchaMissileDescription" value="Confère aux Krama-bombas des missiles anti-infanterie qui ignore le couvert."/>
	<entry name="Orks/SkorchaMissileFlavor" value="<string name='Weapons/SkorchaMissileFlavor'/>"/>
	<entry name="Orks/Stikkbomb" value="<string name='Weapons/Stikkbomb'/>"/>
	<entry name="Orks/StikkbombDescription" value="Confère à l'infanterie la capacité de lancer des grenades anti-infanterie."/>
	<entry name="Orks/StikkbombFlavor" value="<string name='Weapons/StikkbombFlavor'/>"/>
	<entry name="Orks/TankbustaBomb" value="<string name='Weapons/TankbustaBomb'/>"/>
	<entry name="Orks/TankbustaBombDescription" value="Confère aux Kasseurs de tanks la capacité de lancer des bombes anti-blindés."/>
	<entry name="Orks/TankbustaBombFlavor" value="<string name='Weapons/TankbustaBombFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolter" value="Bolter lourd supplémentaire"/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterDescription" value="Accorde aux Immolators, aux Exorcists et aux Castigators un Bolter lourd supplémentaire."/>
	<entry name="SistersOfBattle/AdditionalHeavyBolterFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SistersOfBattle/AdditionalSacredRite" value="Foi doublée"/>
	<entry name="SistersOfBattle/AdditionalSacredRiteDescription" value="Deux rites sacrés peuvent être simultanément actifs."/>
	<entry name="SistersOfBattle/AdditionalSacredRiteFlavor" value="'Autrefois, nos chorals rituels étaient des solos singuliers ou des harmonies chantées en voix sacrée. Aujourd'hui, nous tissons nos hymnes ensemble, pour amplifier leurs doubles thèmes, afin de louer le Dieu-Empereur de toutes les manières possibles.'<br/>  —Souvenir inconnu, Évangile de la Toile."/>
	<entry name="SistersOfBattle/AircraftsMissiles" value="Missiles d'aéronefs"/>
	<entry name="SistersOfBattle/AircraftsMissilesDescription" value="Accorde des missiles Skystrike aux Chasseurs Lightning et des missiles Hellstrike aux Chasseurs d'attaque Avenger."/>
	<entry name="SistersOfBattle/AircraftsMissilesFlavor" value="Ces missiles permettent aux avions d'engager efficacement une variété de cibles ; les missiles Hellstrike pour les missions visant les véhicules blindés, les missiles Skystrike pour les forces aériennes ennemies."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagus'/>"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Augmente l'armure des Mortificatrices"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="<string name='Traits/SistersOfBattle/AnchoriteSarcophagusFlavor'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonus'/>"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Augmente la pénétration du blindage des armes d'assaut."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/AssaultWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/AvengeTheMartyrs" value="Venger les martyrs"/>
	<entry name="SistersOfBattle/AvengeTheMartyrsDescription" value="Augmente la réduction de perte de moral du Zèle vengeur."/>
	<entry name="SistersOfBattle/AvengeTheMartyrsFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/ChaffLauncher" value="Lanceur de leurres"/>
	<entry name="SistersOfBattle/ChaffLauncherDescription" value="Confère aux Chasseurs Lightning et aux Chasseurs d'attaque Avenger la capacité de lancer des leurres qui augmentent la réduction des dégâts à distance."/>
	<entry name="SistersOfBattle/ChaffLauncherFlavor" value="<string name='Actions/DispenseChaffFlavor'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfane" value="<string name='Actions/SistersOfBattle/ChaseTheProfane'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneDescription" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneDescription'/>"/>
	<entry name="SistersOfBattle/ChaseTheProfaneFlavor" value="<string name='Actions/SistersOfBattle/ChaseTheProfaneFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="<string name='Traits/SistersOfBattle/CityGrowth'/>"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Augmente le taux de croissance des villes."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="<string name='Traits/SistersOfBattle/CityGrowthFlavor'/>"/>
	<entry name="SistersOfBattle/CityTier2" value="<string name='Traits/SistersOfBattle/CityTier2'/>"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Augmente le rayon d'acquisition des cases de la ville principale."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="<string name='Traits/SistersOfBattle/CityTier2Flavor'/>"/>
	<entry name="SistersOfBattle/CityTier3" value="<string name='Traits/SistersOfBattle/CityTier3'/>"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Augmente le rayon d'acquisition des cases de la ville principale."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="<string name='Traits/SistersOfBattle/CityTier3Flavor'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaith" value="<string name='Actions/SistersOfBattle/ConvictionOfFaith'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithDescription" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/ConvictionOfFaithFlavor" value="<string name='Actions/SistersOfBattle/ConvictionOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SistersOfBattle/DozerBladeDescription" value="Réduit la pénalité des Immolators, des Exorcists et des Castigators dans les forêts et les ruines impériales."/>
	<entry name="SistersOfBattle/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="<string name='Actions/SistersOfBattle/EternalCrusade'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="<string name='Actions/SistersOfBattle/EternalCrusadeDescription'/>"/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="<string name='Actions/SistersOfBattle/EternalCrusadeFlavor'/>"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Chasseurs experts"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Augmente la précision des Chasseurs Lightning et des Chasseurs d'attaque Avenger."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="<string name='Traits/SistersOfBattle/ExpertFightersFlavor'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SistersOfBattle/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie."/>
	<entry name="SistersOfBattle/ExtraInfantryArmourFlavor" value="Construite à partir d'épaisses plaques de céramite, l'armure de puissance portée par les Adepta Sororitas est basée sur les mêmes systèmes archaïques que celle portée par les frères de l'Adeptus Astartes. Elle fournit le même degré de protection blindée, mais doit renoncer aux systèmes de soutien plus avancés et aux capacités d'amélioration de la force, car les Soeurs de bataille ne possèdent pas la capacité d'un Space Marine à s'interfacer directement avec leur propre armure."/>
	<entry name="SistersOfBattle/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SistersOfBattle/ExtraVehicleArmourDescription" value="Augmente le blindage des véhicules."/>
	<entry name="SistersOfBattle/ExtraVehicleArmourFlavor" value="Les véhicules de l'Adepta Sororitas sont des démonstrations de foi, il est donc crucial d'entretenir et de protéger les artéfacts exposés qu'ils portent. Ainsi préservés, ces véhicules couvriront plus facilement l'avancée de l'ordre des Militants de l'Adepta Sororitas."/>
	<entry name="SistersOfBattle/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SistersOfBattle/FragGrenadeDescription" value="Accorde aux Sœurs de bataille, aux Chanoinesses, aux célestes sacro-saintes, aux Dialogus, aux Dominions, aux Hospitalières, Imagifiers, aux Exo-harnais Parangon, aux Retributors, à Sainte Célestine, aux Sœurs Repentia, aux Zéphyrines la capacité de lancer des grenades anti-infanterie."/>
	<entry name="SistersOfBattle/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/HammerOfWrath" value="<string name='Traits/HammerOfWrath'/>"/>
	<entry name="SistersOfBattle/HammerOfWrathDescription" value="Confère aux Chevaliers-lanciers Cerastus, aux Mortificatrices, aux Exo-harnais Parangon, à Sainte Célestine et aux Zéphyrines la capacité d'effectuer des attaques plus dévastatrices."/>
	<entry name="SistersOfBattle/HammerOfWrathFlavor" value="<string name='Traits/HammerOfWrathFlavor'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonus'/>"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Augmente la pénétration du blindage des armes lourdes."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/HeavyWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SistersOfBattle/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SistersOfBattle/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SistersOfBattle/KrakGrenadeDescription" value="Confère aux Sœurs de bataille, aux Chanoinesses, aux Dialogus, aux Dominions, Hospitalières, Imagifiers, aux Retributors, aux Sœurs Repentia et aux Zéphyrines la capacité de lancer des grenades anti-blindage."/>
	<entry name="SistersOfBattle/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SistersOfBattle/LaudHailer" value="<string name='Traits/SistersOfBattle/LaudHailer'/>"/>
    <entry name="SistersOfBattle/LaudHailerDescription" value="Confère aux Castigateurs, Exorcistes et Immolateurs une aura permettant aux unités adjacentes secouées d'effectuer des actes de foi."/> 
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Traits/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Traits/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Accorde une réduction des dégâts de type 'ne pas ressentir de douleur' aux unités d'infanterie alliées adjacentes à une unité d'Hospitaliers."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Traits/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonus'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Augmente la pénétration de l'armure des armes de mêlée."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="<string name='Traits/SistersOfBattle/MeleeWeaponBonusFlavor'/>"/>
	<entry name="SistersOfBattle/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SistersOfBattle/MeltaBombDescription" value="Permet aux sœurs de combat, aux chanoinesses, aux Dialogus, aux Dominions, aux Retributeurs, aux sœurs Repentia et aux Zéphyrims de déployer une bombe melta très efficace contre les véhicules lourds et les fortifications."/>
	<entry name="SistersOfBattle/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SistersOfBattle/MinistorumIndoctrination" value="L'endoctrinement Ministorum"/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationDescription" value="Accorde l'esprit de martyr aux Chasseurs Lightning, aux Chasseurs d'attaque Avenger et aux Chevaliers-lanciers Cerastus."/>
	<entry name="SistersOfBattle/MinistorumIndoctrinationFlavor" value="'Grâce à une longue exposition à nos rituels et à nos prières, nos alliés de fortune—la marine impériale et la poignée de Chevaliers-lanciers impériaux qui ont survécu—ont commencé à prendre part à nos rites et à intérioriser nos croyances. La foi, semble-t-il, est contagieuse.'<br/>  — Souvenir inconnu, Évangile de la Toile"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisation'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationDescription'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="<string name='Actions/SistersOfBattle/NonMilitantMobilisationFlavor'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="<string name='Actions/SistersOfBattle/PurifyingRecitations'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsDescription'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="<string name='Actions/SistersOfBattle/PurifyingRecitationsFlavor'/>"/>
	<entry name="SistersOfBattle/RagingFervour" value="<string name='Actions/SistersOfBattle/RagingFervour'/>"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="<string name='Actions/SistersOfBattle/RagingFervourDescription'/>"/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="<string name='Actions/SistersOfBattle/RagingFervourFlavor'/>"/>
	<entry name="SistersOfBattle/RitualizedCeremonies" value="Cérémonies ritualisées"/>
	<entry name="SistersOfBattle/RitualizedCeremoniesDescription" value="Réduit le coût des Rites Sacrés."/>
	<entry name="SistersOfBattle/RitualizedCeremoniesFlavor" value="'Au début, nous priions en nous déplaçant, dans des moments de paix dans des ruines ou des grottes. Avec le temps, lorsque le conflit s'est stabilisé en une guerre sans fin, nos rituels et nos prières se sont également stabilisés, pour former un régime et une routine.'<br/>  — Souvenir inconnu, Évangile de la Toile"/>
	<entry name="SistersOfBattle/SacralVigor" value="<string name='Actions/SistersOfBattle/SacralVigor'/>"/>
	<entry name="SistersOfBattle/SacralVigorDescription" value="<string name='Actions/SistersOfBattle/SacralVigorDescription'/>"/>
	<entry name="SistersOfBattle/SacralVigorFlavor" value="<string name='Actions/SistersOfBattle/SacralVigorFlavor'/>"/>
	<entry name="SistersOfBattle/SanctifiedWorld" value="Monde sanctifié"/>
	<entry name="SistersOfBattle/SanctifiedWorldDescription" value="Augmente le bonus de fidélité accordé par le Couvent de la Foi."/>
	<entry name="SistersOfBattle/SanctifiedWorldFlavor" value="La plupart des Ordres répandent l'illumination du Dieu-Empereur sur des mondes bien au-delà de leurs sanctuaires primaires, établissant des missions lointaines et des chapelles subsidiaires pour étendre l'influence de leur Ordre et de l'Ecclésiarchie."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="<string name='Traits/SistersOfBattle/SimulacrumImperialis'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisDescription'/>"/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="<string name='Traits/SistersOfBattle/SimulacrumImperialisFlavor'/>"/>
	<entry name="SistersOfBattle/SisterSuperior" value="<string name='Traits/SistersOfBattle/SisterSuperior'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="<string name='Traits/SistersOfBattle/SisterSuperiorDescription'/>"/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="<string name='Traits/SistersOfBattle/SisterSuperiorFlavor'/>"/>
	<entry name="SistersOfBattle/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SistersOfBattle/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SistersOfBattle/UniversalCatechism" value="Catéchisme universel"/>
	<entry name="SistersOfBattle/UniversalCatechismDescription" value="Octroie Bouclier de la foi aux combattants de la foudre, aux combattants de la grève des vengeurs et aux chevaliers lanciers de Cerastus."/>
	<entry name="SistersOfBattle/UniversalCatechismFlavor" value="Les troupes impériales réquisitionnées pour les guerres de la foi finissent souvent par prier côte à côte avec les sœurs de l'Adepta Sororitas avant chaque bataille, trouvant des conseils dans leurs convictions."/>
	<entry name="SistersOfBattle/VengefulSpirit" value="<string name='Actions/SistersOfBattle/VengefulSpirit'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="<string name='Actions/SistersOfBattle/VengefulSpiritDescription'/>"/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="<string name='Actions/SistersOfBattle/VengefulSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrder" value="Vœu de l'Ordre Militant"/>
 	<entry name="SistersOfBattle/VowOfTheMilitantOrderDescription" value="Les unités conservent leur bouclier de la foi s'il est brisé."/>
	<entry name="SistersOfBattle/VowOfTheMilitantOrderFlavor" value="<string name='Traits/SistersOfBattle/ShieldOfFaithFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="<string name='Actions/SistersOfBattle/WarmachinesWrath'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="<string name='Actions/SistersOfBattle/WarmachinesWrathDescription'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="<string name='Actions/SistersOfBattle/WarmachinesWrathFlavor'/>"/>	
	<entry name="SpaceMarines/AssaultDoctrine" value="<string name='Traits/AssaultDoctrine'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineDescription" value="<string name='Actions/AssaultDoctrineDescription'/>"/>
	<entry name="SpaceMarines/AssaultDoctrineFlavor" value="<string name='Traits/AssaultDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/SpaceMarines/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="Augmente la pénétration d'armure des grenades, missiles et armes à explosion."/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/SpaceMarines/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="SpaceMarines/BolsterDefencesDescription" value="Confère au Canon Thunderfire la capacité d'augmenter la réduction de dégâts à distance de la case ciblée."/>
	<entry name="SpaceMarines/BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/SpaceMarines/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Upgrades/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/SpaceMarines/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDrill" value="<string name='Traits/BolterDrill'/>"/>
	<entry name="SpaceMarines/BolterDrillDescription" value="<string name='Actions/BolterDrillDescription'/>"/>
	<entry name="SpaceMarines/BolterDrillFlavor" value="<string name='Traits/BolterDrillFlavor'/>"/>
	<entry name="SpaceMarines/CeramitePlating" value="<string name='Traits/CeramitePlating'/>"/>
	<entry name="SpaceMarines/CeramitePlatingDescription" value="Augmente l'armure des Stormravens et Stormtalons."/>
	<entry name="SpaceMarines/CeramitePlatingFlavor" value="<string name='Traits/CeramitePlatingFlavor'/>"/>
	<entry name="SpaceMarines/ChapterUnity" value="<string name='Traits/ChapterUnity'/>"/>
	<entry name="SpaceMarines/ChapterUnityDescription" value="Augmente la production de loyauté du Grand Hall"/>
	<entry name="SpaceMarines/ChapterUnityFlavor" value="<string name='Traits/ChapterUnityFlavor'/>"/>
	<entry name="SpaceMarines/CityTier2" value="<string name='Traits/SpaceMarines/CityTier2'/>"/>
	<entry name="SpaceMarines/CityTier2Description" value="Augmente le rayon d'acquisitions des cases de la ville."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="<string name='Traits/SpaceMarines/CityTier2Flavor'/>"/>
	<entry name="SpaceMarines/CityTier3" value="<string name='Traits/SpaceMarines/CityTier3'/>"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="<string name='Traits/SpaceMarines/CityTier3Flavor'/>"/>
	<entry name="SpaceMarines/CityTier4" value="<string name='Traits/SpaceMarines/CityTier4'/>"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="<string name='Traits/SpaceMarines/CityTier4Flavor'/>"/>
	<entry name="SpaceMarines/ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="SpaceMarines/ClusterMinesDescription" value="Confère aux Motards scouts la capacité de placer des mines à sous-munitions."/>
	<entry name="SpaceMarines/ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="SpaceMarines/CombatShield" value="<string name='Traits/CombatShield'/>"/>
	<entry name="SpaceMarines/CombatShieldDescription" value="Augmente la réduction des dégâts pour les Escouades d'assaut."/>
	<entry name="SpaceMarines/CombatShieldFlavor" value="<string name='Traits/CombatShieldFlavor'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrine" value="<string name='Traits/DevastatorDoctrine'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineDescription" value="<string name='Actions/DevastatorDoctrineDescription'/>"/>
	<entry name="SpaceMarines/DevastatorDoctrineFlavor" value="<string name='Traits/DevastatorDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/DozerBlade" value="<string name='Traits/DozerBlade'/>"/>
	<entry name="SpaceMarines/DozerBladeDescription" value="Réduit la pénalité de mouvement dans les forêts et les ruines impériales pour les Hunters, les Predators et Razorbacks, et les Whirlwinds."/>
	<entry name="SpaceMarines/DozerBladeFlavor" value="<string name='Traits/DozerBladeFlavor'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraInfantryArmourFlavor" value="Bien que les sceaux de pureté puissent garder un Adeptus Astartes au combat plus longtemps, la foi n'arrête pas tous les bolts. En utilisant des versions plus récentes de leurs armures énergétiques, l'infanterie Space Marine améliore sa survivabilité de manière importante."/>
	<entry name="SpaceMarines/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraVehicleArmourDescription'/>"/>
	<entry name="SpaceMarines/ExtraVehicleArmourFlavor" value="Quand les bons onguents ont été répandus et les bénédictions exécutées, un Techmarine peut faire quelques changements mineurs sur ses protégés, en accord avec le Codex Astartes bien entendu, pour augmenter leur survivabilité."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Protection de la forteresse"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Confère aux forteresses de la rédemption une réduction de dégâts invulnérable."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="<string name='Traits/SpaceMarines/FortressOfRedemptionDamageReductionFlavor'/>"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSilo" value="Silos à missiles de la Forteresse de la rédemption"/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloDescription" value="Confère un silos à missile krakstorm aux Forteresses de la rédemption."/>
	<entry name="SpaceMarines/FortressOfRedemptionMissileSiloFlavor" value="<string name='Weapons/KrakstormMissileSiloFlavor'/>"/>
	<entry name="SpaceMarines/FragGrenade" value="<string name='Weapons/FragGrenade'/>"/>
	<entry name="SpaceMarines/FragGrenadeDescription" value="Confère aux Apothicaires, Escouades d'assaut, Capitaines, Escouades devastator, Archivistes, Motards scouts, Escouades tactiques, Scouts et Canons Thunderfire la capacité de lancer des grenades anti-personnel."/>
	<entry name="SpaceMarines/FragGrenadeFlavor" value="<string name='Weapons/FragGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="SpaceMarines/HammerOfWrathDescription" value="Confère aux Escouades d'assaut, Dreadnoughts et Motards scouts la capacité d'effectuer des attaques plus puissantes."/>
	<entry name="SpaceMarines/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="SpaceMarines/HunterKillerMissile" value="<string name='Weapons/HunterKillerMissile'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileDescription" value="<string name='Upgrades/AstraMilitarum/HunterKillerMissileDescription'/>"/>
	<entry name="SpaceMarines/HunterKillerMissileFlavor" value="<string name='Weapons/HunterKillerMissileFlavor'/>"/>
	<entry name="SpaceMarines/HurricaneBolter" value="Bolter Ouragan"/>
	<entry name="SpaceMarines/HurricaneBolterDescription" value="Confère aux Stormravens un Bolter Ouragan."/>
	<entry name="SpaceMarines/HurricaneBolterFlavor" value="Inventé par les Techmarines du chapitre des Black Templars, le système de Bolters Ouragan combine la puissance de feu d’une demi-douzaine de Bolters afin de produire une grêle de tirs."/>
	<entry name="SpaceMarines/KrakGrenade" value="<string name='Weapons/KrakGrenade'/>"/>
	<entry name="SpaceMarines/KrakGrenadeDescription" value="Confère aux Apothicaires, Escouades d'assaut, Capitaines, Escouades devastator, Archivistes, Motards scouts, Escouades tactiques, Scouts et Canons Thunderfire la capacité de lancer des grenades anti-blindés."/>
	<entry name="SpaceMarines/KrakGrenadeFlavor" value="<string name='Weapons/KrakGrenadeFlavor'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMelta" value="<string name='Weapons/MultiMelta'/>"/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaDescription" value="Confère aux Land Speeders et Land Raiders un multi-fuseur."/>
	<entry name="SpaceMarines/LandSpeederMultiMeltaFlavor" value="<string name='Weapons/MultiMeltaFlavor'/>"/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/SpaceMarines/LasDamage'/>"/>
	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Upgrades/AstraMilitarum/LasDamageDescription'/>"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/SpaceMarines/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/LastStand" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LastStandDescription" value="Augmente le moral de toutes les unités Space Marine."/>
	<entry name="SpaceMarines/LastStandFlavor" value="<string name='Traits/LastStandFlavor'/>"/>
	<entry name="SpaceMarines/LastStandReady" value="<string name='Traits/LastStand'/>"/>
	<entry name="SpaceMarines/LocatorBeacon" value="<string name='Traits/LocatorBeacon'/>"/>
	<entry name="SpaceMarines/LocatorBeaconDescription" value="Permet au déploiement orbital de ne plus consommer les points d'actions lorsqu'utilisé sur une case adjacente à des Motards scouts ou un Stormraven."/>
	<entry name="SpaceMarines/LocatorBeaconFlavor" value="<string name='Traits/LocatorBeaconFlavor'/>"/>
	<entry name="SpaceMarines/MachineEmpathy" value="<string name='Traits/MachineEmpathy'/>"/>
	<entry name="SpaceMarines/MachineEmpathyDescription" value="<string name='Actions/MachineEmpathyDescription'/>"/>
	<entry name="SpaceMarines/MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="<string name='Traits/SpaceMarines/MeleeDamage'/>"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="Augmente la pénétration d'armure des armes de corps à corps."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="<string name='Traits/SpaceMarines/MeleeDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeltaBomb" value="<string name='Weapons/MeltaBomb'/>"/>
	<entry name="SpaceMarines/MeltaBombDescription" value="Confère aux Escouades tactiques, Escouades d'assaut, Escouades Devastators, Scouts et Motards scouts la capacité d'utiliser une bombe à fusion qui est très efficace contre les véhicules lourds et les fortifications."/>
	<entry name="SpaceMarines/MeltaBombFlavor" value="<string name='Weapons/MeltaBombFlavor'/>"/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
 	<entry name="SpaceMarines/OmniscopeDescription" value="Confère aux escouades devastator Centurions la capacité d'ignorer temporairement la réduction de dégâts à distance des ennemis."/>
 	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalBombardment" value="<string name='Actions/OrbitalBombardment'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentDescription" value="<string name='Actions/OrbitalBombardmentDescription'/>"/>
	<entry name="SpaceMarines/OrbitalBombardmentFlavor" value="<string name='Actions/OrbitalBombardmentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalDeployment" value="<string name='Actions/OrbitalDeployment'/>"/>
	<entry name="SpaceMarines/OrbitalDeploymentDescription" value="Permet aux unités de se déployer via un module d'atterrissage n'importe où sur le champ de bataille."/>
	<entry name="SpaceMarines/OrbitalDeploymentFlavor" value="<string name='Actions/OrbitalDeploymentFlavor'/>"/>
	<entry name="SpaceMarines/OrbitalScan" value="<string name='Actions/OrbitalScan'/>"/>
	<entry name="SpaceMarines/OrbitalScanDescription" value="<string name='Actions/OrbitalScanDescription'/>"/>
	<entry name="SpaceMarines/OrbitalScanFlavor" value="<string name='Actions/OrbitalScanFlavor'/>"/>
	<entry name="SpaceMarines/PredatorLascannon" value="Bolters lourds supplémentaires"/>
	<entry name="SpaceMarines/PredatorLascannonDescription" value="Confère aux Forteresses de la rédemption, Predators et Macro-canons Aquila des Bolters lourd supplémentaires."/>
	<entry name="SpaceMarines/PredatorLascannonFlavor" value="<string name='Weapons/HeavyBolterFlavor'/>"/>
	<entry name="SpaceMarines/SiegeMasters" value="<string name='Traits/SiegeMasters'/>"/>
	<entry name="SpaceMarines/SiegeMastersDescription" value="<string name='Actions/SiegeMastersDescription'/>"/>
	<entry name="SpaceMarines/SiegeMastersFlavor" value="<string name='Actions/SiegeMastersFlavor'/>"/>
	<entry name="SpaceMarines/SiegeShield" value="<string name='Traits/SiegeShield'/>"/>
	<entry name="SpaceMarines/SiegeShieldDescription" value="Augmente l'armure des Vindicators et réduit la pénalité de mouvement dans les forêts et les ruines impériales."/>
	<entry name="SpaceMarines/SiegeShieldFlavor" value="<string name='Traits/SiegeShieldFlavor'/>"/>
	<entry name="SpaceMarines/Signum" value="<string name='Actions/Signum'/>"/>
	<entry name="SpaceMarines/SignumDescription" value="Confère aux Escouades Devastators la capacité d'ignorer la pénalité pour les armes lourdes, d'artillerie et à salve temporairement."/>
	<entry name="SpaceMarines/SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SpaceMarines/SmokeLauncher" value="<string name='Upgrades/SmokeLauncher'/>"/>
	<entry name="SpaceMarines/SmokeLauncherDescription" value="<string name='Upgrades/SmokeLauncherDescription'/>"/>
	<entry name="SpaceMarines/SmokeLauncherFlavor" value="<string name='Upgrades/SmokeLauncherFlavor'/>"/>
	<entry name="SpaceMarines/TacticalDoctrine" value="<string name='Traits/TacticalDoctrine'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineDescription" value="<string name='Actions/TacticalDoctrineDescription'/>"/>
	<entry name="SpaceMarines/TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="SpaceMarines/TeleportHomer" value="<string name='Traits/TeleportHomer'/>"/>
	<entry name="SpaceMarines/TeleportHomerDescription" value="Le déploiement orbital ne consomme pas de points d'action lorsque le Chapelain, les terminators d'assaut et les terminators sont adjacents à des Space Marines tactiques ou à des scouts."/>
	<entry name="SpaceMarines/TeleportHomerFlavor" value="<string name='Traits/TeleportHomerFlavor'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeak" value="<string name='Traits/TheFleshIsWeak'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakDescription" value="<string name='Actions/TheFleshIsWeakDescription'/>"/>
	<entry name="SpaceMarines/TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="Tau/AdvancedTargetingSystem" value="<string name='Traits/Tau/AdvancedTargetingSystem'/>"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Système de soutien pour exo-armure et amélioration de vehicule qui augmente la précision à distance."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="<string name='Traits/Tau/AdvancedTargetingSystemFlavor'/>"/>
	<entry name="Tau/AutomatedRepairSystem" value="<string name='Traits/Tau/AutomatedRepairSystem'/>"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Restaure des points de vie aux véhicules tous les tours."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="<string name='Traits/Tau/AutomatedRepairSystemFlavor'/>"/>
	<entry name="Tau/BlacksunFilter" value="<string name='Traits/Tau/BlacksunFilter'/>"/>
	<entry name="Tau/BlacksunFilterDescription" value="Augmente la visibilité des véhicules, Éthérés, Commandants, Éclaireurs et Combattants."/>
	<entry name="Tau/BlacksunFilterFlavor" value="<string name='Traits/Tau/BlacksunFilterFlavor'/>"/>
	<entry name="Tau/BlastDamage" value="<string name='Traits/Tau/BlastDamage'/>"/>
	<entry name="Tau/BlastDamageDescription" value="Augmente la pénétration d'armure des grenades, des lance-flammes et des missiles."/>
	<entry name="Tau/BlastDamageFlavor" value="<string name='Traits/Tau/BlastDamageFlavor'/>"/>
	<entry name="Tau/BoltDamage" value="<string name='Traits/Tau/BoltDamage'/>"/>
	<entry name="Tau/BoltDamageDescription" value="Augmente la pénétration d'armure des armes à induction et rail."/>
	<entry name="Tau/BoltDamageFlavor" value="<string name='Traits/Tau/BoltDamageFlavor'/>"/> 
	<entry name="Tau/BondingKnifeRitual" value="<string name='Actions/Tau/BondingKnifeRitual'/>"/>
	<entry name="Tau/BondingKnifeRitualDescription" value="Accorde aux guerriers du feu, aux éclaireurs, Guerriers du feu brécheurs, aux unités de combat furtives XV25, aux unités de combat de crise XV8, aux unités de combat Broadside XV88, aux unités de combat Ghostkeel XV95 et aux unités de combat Riptide XV104 la capacité de restaurer leur moral."/>
	<entry name="Tau/BondingKnifeRitualFlavor" value="<string name='Actions/Tau/BondingKnifeRitualFlavor'/>"/>
	<entry name="Tau/CityTier2" value="<string name='Traits/Tau/CityTier2'/>"/>
	<entry name="Tau/CityTier2Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier2Flavor" value="<string name='Traits/Tau/CityTier2Flavor'/>"/>
	<entry name="Tau/CityTier3" value="<string name='Traits/Tau/CityTier3'/>"/>
	<entry name="Tau/CityTier3Description" value="<string name='Upgrades/AstraMilitarum/CityTier2Description'/>"/>
	<entry name="Tau/CityTier3Flavor" value="<string name='Traits/Tau/CityTier3Flavor'/>"/>
	<entry name="Tau/CounterfireDefenceSystem" value="<string name='Traits/Tau/CounterfireDefenceSystem'/>"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Système de soutien pour exo-armure qui augmente la précision des tirs en état d'alerte."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Traits/Tau/CounterfireDefenceSystemFlavor'/>"/>
	<entry name="Tau/DisruptionPod" value="<string name='Traits/Tau/DisruptionPod'/>"/>
	<entry name="Tau/DisruptionPodDescription" value="Augmente la réduction de dégât à distance des véhicules."/>
	<entry name="Tau/DisruptionPodFlavor" value="<string name='Traits/Tau/DisruptionPodFlavor'/>"/>
	<entry name="Tau/DroneController" value="<string name='Traits/Tau/DroneController'/>"/>
	<entry name="Tau/DroneControllerDescription" value="Système de soutien pour exo-armure qui augmente la précision des drones adjacents."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Traits/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/EMPGrenade" value="<string name='Weapons/EMPGrenade'/>"/>
	<entry name="Tau/EMPGrenadeDescription" value="Confère aux Guerriers de Feu, Guerriers du feu brécheurs, et Cibleurs la capacité de lancer des grenades anti-véhicules."/>
	<entry name="Tau/EMPGrenadeFlavor" value="<string name='Weapons/EMPGrenadeFlavor'/>"/>
	<entry name="Tau/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tau/ExtraInfantryArmourDescription" value="Augmente l'armure de l'infanterie et des créatures monstrueuses."/>
	<entry name="Tau/ExtraInfantryArmourFlavor" value="Etudier davantage la carapace de chitine de nos camarades Thraxiens a révélé des améliorations structurelles que nous pourrions faire à la composition des armures de nos Cadres et Exo-Armures."/>
	<entry name="Tau/ExtraVehicleArmour" value="<string name='Traits/ExtraVehicleArmour'/>"/>
	<entry name="Tau/ExtraVehicleArmourDescription" value="Augmente l'armure des véhicules."/>
	<entry name="Tau/ExtraVehicleArmourFlavor" value="Contrairement aux Technoprêtres stagnants de Mars, la Caste de la Terre innove et crée sans fin. Le Fio'tak est un matériau au sommet actuel de leur créativité, un alliage métallique nano-crystallin dur et extrêmement dense utilisé avec économie dans leurs meilleurs créations."/>
	<entry name="Tau/FlechetteDischarger" value="<string name='Traits/Tau/FlechetteDischarger'/>"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Permet aux véhicules d'endommager ceux qui les attaquent au corps à corps."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="<string name='Traits/Tau/FlechetteDischargerFlavor'/>"/>
	<entry name="Tau/ForTheGreaterGoodBonus" value="Sporémissaires Charpactins"/>
	<entry name="Tau/ForTheGreaterGoodBonusDescription" value="Réduit le coût en influence de Pour le Bien Suprême."/>
	<entry name="Tau/ForTheGreaterGoodBonusFlavor" value="Quand les diplomates de la Caste de l'Eau sont sur le terrain, ils trouvent parfois utilses de se faire accompagner de nos alliés fongoïdes les Charpactins, dont les communications ultraviolettes irrégulières ont un effet sédatif presque hypnotiques sur quasiment toutes les races."/>
	<entry name="Tau/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tau/HammerOfWrathDescription" value="Confère aux Exo-Armures XV95 Ghostkeel, Exo-Armures XV104 Riptide, Exo-armure XV107 R'Varna et KV128 Stormsurges la capacité d'effectuer des attaques plus dévastatrices."/>
	<entry name="Tau/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tau/LasDamage" value="<string name='Traits/Tau/LasDamage'/>"/>
	<entry name="Tau/LasDamageDescription" value="Augmente la pénétration des armes à impulsion, ion, plasma et fusion."/>
	<entry name="Tau/LasDamageFlavor" value="<string name='Traits/Tau/LasDamageFlavor'/>"/>
	<entry name="Tau/PhotonGrenade" value="<string name='Weapons/PhotonGrenade'/>"/>
	<entry name="Tau/PhotonGrenadeDescription" value="Confère aux Guerriers de Feu, Guerriers du feu brécheurs, Cibleurs et Sabres de feu la capacité de lancer des grenades aveuglantes."/>
	<entry name="Tau/PhotonGrenadeFlavor" value="<string name='Weapons/PhotonGrenadeFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="<string name='Traits/Tau/PointDefenceTargetingRelay'/>"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Augmente les dégâts infligés par les tirs en état d'alerte des véhicules contre les ennemis adjacents à d'autres alliés."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="<string name='Traits/Tau/PointDefenceTargetingRelayFlavor'/>"/>
	<entry name="Tau/PurchaseEnergy" value="<string name='Actions/Tau/PurchaseEnergy'/>"/>
	<entry name="Tau/PurchaseEnergyDescription" value="Confère la capacité d'acheter de l'énergie avec l'influence."/>
	<entry name="Tau/PurchaseEnergyFlavor" value="<string name='Actions/Tau/PurchaseEnergyFlavor'/>"/>
	<entry name="Tau/PurchaseFood" value="<string name='Actions/Tau/PurchaseFood'/>"/>
	<entry name="Tau/PurchaseFoodDescription" value="Confère la capacité d'acheter de la nourriture avec l'influence."/>
	<entry name="Tau/PurchaseFoodFlavor" value="<string name='Actions/Tau/PurchaseFoodFlavor'/>"/>
	<entry name="Tau/PurchaseOre" value="<string name='Actions/Tau/PurchaseOre'/>"/>
	<entry name="Tau/PurchaseOreDescription" value="Confère la capacité d'acheter du minerai avec l'influence."/>
	<entry name="Tau/PurchaseOreFlavor" value="<string name='Actions/Tau/PurchaseOreFlavor'/>"/>
	<entry name="Tau/PurchasePopulation" value="<string name='Actions/Tau/PurchasePopulation'/>"/>
	<entry name="Tau/PurchasePopulationDescription" value="Confère la capacité d'acheter de la population avec l'influence."/>
	<entry name="Tau/PurchasePopulationFlavor" value="<string name='Actions/Tau/PurchasePopulationFlavor'/>"/>
	<entry name="Tau/PurchaseResearch" value="<string name='Actions/Tau/PurchaseResearch'/>"/>
	<entry name="Tau/PurchaseResearchDescription" value="Confère la capacité d'acheter de la recherche avec l'influence."/>
	<entry name="Tau/PurchaseResearchFlavor" value="<string name='Actions/Tau/PurchaseResearchFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="<string name='Traits/Tau/RipykaVa'/>"/>
	<entry name="Tau/RipykaVaDescription" value="Réduit le temps de rechargement des métastratégies du Commandeur."/>
	<entry name="Tau/RipykaVaFlavor" value="<string name='Traits/Tau/RipykaVaFlavor'/>"/>
	<entry name="Tau/SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="Tau/SeekerMissileDescription" value="Confère aux véhicules et aux Exo-Armures XV88 Broadside un missile."/>
	<entry name="Tau/SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Tau/SensorSpines" value="Senseurs topométriques"/>
	<entry name="Tau/SensorSpinesDescription" value="Confère déplacement à couvert aux véhicules."/>
	<entry name="Tau/SensorSpinesFlavor" value="Les senseurs topométriques sont utilisés pour fournir des données à un système de contrôle de survol avancé, dessinant des trajectoires sûres à travers des terrains traîtres et évitant les pièges et mines qui pourraient être cachés."/>
	<entry name="Tau/ShieldGenerator" value="<string name='Traits/Tau/ShieldGenerator'/>"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Système de soutien pour exo-armures qui augmente la réduction invulnérable de dégâts."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Traits/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StimulantInjector" value="<string name='Traits/Tau/StimulantInjector'/>"/>
	<entry name="Tau/StimulantInjectorDescription" value="Système de soutien pour exo-armures qui augmente la réduction de dégâts insensible à la douleur."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Traits/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/SubversionBonus" value="Recherche séditieuse"/>
	<entry name="Tau/SubversionBonusDescription" value="Augmente la pénalité de loyauté de Corrompre une ville."/>
	<entry name="Tau/SubversionBonusFlavor" value="Alors qu'ils menaient une recherche oppositionnelle, les T'au ont découvert la phrase “pense comme ton ennemi” dans un manuel militaire humain. La Caste de l'Eau l'a prise à coeur et a enquêté de manière approfondie les besoins et désirs inassouvis d'une colonie spécifique—que ce soient des esclaves tremblants des Nécrons ou des boys Orks ayant faim de squig—avant de planifier leur rébellion."/>
	<entry name="Tau/TacticalSupportTurret" value="<string name='Weapons/TacticalSupportTurret'/>"/>
	<entry name="Tau/TacticalSupportTurretDescription" value="Confère aux Guerriers de Feu une arme supplémentaire lorsqu'ils n'ont pas bougé."/>
	<entry name="Tau/TacticalSupportTurretFlavor" value="<string name='Weapons/TacticalSupportTurretFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="<string name='Traits/Tau/UtopiaBonus'/>"/>
	<entry name="Tau/UtopiaBonusDescription" value="<string name='Traits/Tau/UtopiaBonusDescription'/>"/>
	<entry name="Tau/UtopiaBonusFlavor" value="<string name='Traits/Tau/UtopiaBonusFlavor'/>"/>
	<entry name="Tau/VectoredRetroThrusters" value="<string name='Traits/Tau/VectoredRetroThrusters'/>"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Système de soutien pour exo-armure qui augmente le mouvement et permet à l'unité d'ignorer les zones de contrôles ennemies."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Traits/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="<string name='Traits/Tau/VelocityTracker'/>"/>
	<entry name="Tau/VelocityTrackerDescription" value="Système de soutien pour exo-armure qui augmente la précision à distance contre les volants."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Traits/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tyranids/AcidBlood" value="Sang acide"/>
	<entry name="Tyranids/AcidBloodDescription" value="Confère aux créatures monstrueuses Tyranides et au Primat Tyranide la capacité d'endommager ceux qui les attaquent au corps à corps"/>
	<entry name="Tyranids/AcidBloodFlavor" value="<string name='Traits/Tyranids/AcidBloodFlavor'/>"/>
	<entry name="Tyranids/AdrenalGlands" value="Glandes surrénales"/>
	<entry name="Tyranids/AdrenalGlandsDescription" value="Augmente le mouvement et les dégâts au corps à corps des unités."/>
	<entry name="Tyranids/AdrenalGlandsFlavor" value="Les glandes surrénales saturent le corps de leur hôte avec des hormones qui mettent dans un état de frénésie hyperactive le métabolisme de la créature."/>
	<entry name="Tyranids/BiomorphDamage" value="<string name='Traits/Tyranids/BiomorphDamage'/>"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Augmente la pénétration d'armure des biomorphes."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="<string name='Traits/Tyranids/BiomorphDamageFlavor'/>"/>
	<entry name="Tyranids/BioPlasma" value="Bio-plasma du Carnifex"/>
	<entry name="Tyranids/BioPlasmaDescription" value="Confère aux Carnifex une arme à distance supplémentaire."/>
	<entry name="Tyranids/BioPlasmaFlavor" value="<string name='Weapons/BioPlasmaFlavor'/>"/>
	<entry name="Tyranids/BoneMace" value="Masse caudale du Carnifex"/>
	<entry name="Tyranids/BoneMaceDescription" value="Confère aux Carnifex une arme de corps à corps supplémentaire."/>
	<entry name="Tyranids/BoneMaceFlavor" value="<string name='Weapons/BoneMaceFlavor'/>"/>
	<entry name="Tyranids/CityCost" value="Malanthropes métamorphes"/>
	<entry name="Tyranids/CityCostDescription" value="Réduit le coût de fondation des nouvelles villes."/>
	<entry name="Tyranids/CityCostFlavor" value="Le rôle des Malanthropes dans la fondation de nouvelles ruches Tyranides a été peu étudié par les Génétors impériaux, étant donné que dans la plupart de cas ils sont morts ou en fuite quand la ruche est fondée. Cependant on pense qu'ils transportent une graine à un nouvel endroit lorsqu'une ruche est nécessaire à une position plus avancée. Des Malanthropes inhabituels ont été observés parfois, avec des caractéristiques physiques spéciales qui leur permettent d'exécuter cette tâche plus efficacement."/>
	<entry name="Tyranids/CityDamage" value="<string name='Traits/Tyranids/CityDamage'/>"/>
	<entry name="Tyranids/CityDamageDescription" value="Les unités ennemies dans les villes Tyranides subissent des dégâts tous les tours"/>
	<entry name="Tyranids/CityDamageFlavor" value="<string name='Traits/Tyranids/CityDamageFlavor'/>"/>
	<entry name="Tyranids/CityGrowth" value="<string name='Traits/Tyranids/CityGrowth'/>"/>
	<entry name="Tyranids/CityGrowthDescription" value="Augmente la vitesse de croissance de population des villes Tyranides."/>
	<entry name="Tyranids/CityGrowthFlavor" value="<string name='Traits/Tyranids/CityGrowthFlavor'/>"/>
	<entry name="Tyranids/CityLoyalty" value="<string name='Traits/Tyranids/CityLoyalty'/>"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Diminue la pénalité de loyauté due à la quantité de villes Tyranides."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="<string name='Traits/Tyranids/CityLoyaltyFlavor'/>"/>
	<entry name="Tyranids/CityPopulationLimit" value="<string name='Traits/Tyranids/CityPopulationLimit'/>"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Augmente la limite de population des villes Tyranides."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="<string name='Traits/Tyranids/CityPopulationLimitFlavor'/>"/>
	<entry name="Tyranids/CityProduction" value="<string name='Traits/Tyranids/CityProduction'/>"/>
	<entry name="Tyranids/CityProductionDescription" value="Augmente la production fournie par les villes Tyranides."/>
	<entry name="Tyranids/CityProductionFlavor" value="<string name='Traits/Tyranids/CityProductionFlavor'/>"/>
	<entry name="Tyranids/CityTier2" value="<string name='Traits/Tyranids/CityTier2'/>"/>
	<entry name="Tyranids/CityTier2Description" value="Augmente le rayon d'acquisition des cases de la ville."/>
	<entry name="Tyranids/CityTier2Flavor" value="<string name='Traits/Tyranids/CityTier2Flavor'/>"/>
	<entry name="Tyranids/ConsumeTile2" value="Digestion efficace"/>
	<entry name="Tyranids/ConsumeTile2Description" value="Diminue le coût en influence pour consommer les cases."/>
	<entry name="Tyranids/ConsumeTile2Flavor" value="Bien qu'un Malanthrope ou les mâchoires acérées d'un Vorace n'ont aucun problème pour dévorer la chair, les os ou même le plastacier, ils sont ralentis par la nécessité de dévorer l'énorme quantité de terre et de pierre que l'Esprit de la Ruche demande en grande quantité et rapidement. On présume que des organismes spécialisés sont déployés pour ce faire—mais, de nouveau, personne n'a survécu pour confirmer ça."/>
	<entry name="Tyranids/Deathspitter" value="Crache-mort de Rôdeurs"/>
	<entry name="Tyranids/DeathspitterDescription" value="Confère aux Rôdeurs une arme à distance."/>
	<entry name="Tyranids/DeathspitterFlavor" value="<string name='Weapons/DeathspitterFlavor'/>"/>
	<entry name="Tyranids/DesiccatorLarvae" value="<string name='Weapons/DesiccatorLarvae'/>"/>
	<entry name="Tyranids/DesiccatorLarvaeDescription" value="Confère aux Tyrans des Ruches, Tervigons et Tyrannofexs une arme à souffle."/>
	<entry name="Tyranids/DesiccatorLarvaeFlavor" value="<string name='Weapons/DesiccatorLarvaeFlavor'/>"/>
	<entry name="Tyranids/ExtraInfantryArmour" value="<string name='Traits/ExtraInfantryArmour'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourDescription" value="<string name='Upgrades/AstraMilitarum/ExtraInfantryArmourDescription'/>"/>
	<entry name="Tyranids/ExtraInfantryArmourFlavor" value="L'Esprit de la Ruche a une mentalité frugale. Pourquoi sacrifier des ressources pour protéger des troupes qui seront dévorées de toute façon? Il n'investira dans de la chitine et des os plus épais pour ses créatures que quand le jeu en vaut la chandelle."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmour" value="<string name='Traits/ExtraMonstrousCreatureArmour'/>"/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourDescription" value="Augmente l'armure des créatures monstrueuses."/>
	<entry name="Tyranids/ExtraMonstrousCreatureArmourFlavor" value="Grâce à une combinaison de carapace durcie, champs disruptifs et terminaisons nerveuses endormies, l'Esprit de la Ruche peut altérer la résistance de ses plus grandes créatures facilement."/>
	<entry name="Tyranids/FleshHooks" value="<string name='Weapons/FleshHooks'/>"/>
	<entry name="Tyranids/FleshHooksDescription" value="Confère aux Guerriers Tyranides, Primats Tyranides et Lictors une arme à distance supplémentaire."/>
	<entry name="Tyranids/FleshHooksFlavor" value="<string name='Weapons/FleshHooksFlavor'/>"/>
	<entry name="Tyranids/HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="Tyranids/HammerOfWrathDescription" value="Confère aux Exocrines, Gargouilles, Haruspexs, Viragos des Ruches, Tyrans des Ruches, Maleceptors, Hiérodules faucheurs, Tervigons, Trygons et les Tyrannofex la possibilité d'effectuer des attaques plus dévastatrices."/>
	<entry name="Tyranids/HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="<string name='Traits/Tyranids/InfantryUpkeep'/>"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Réduit le coût d'entretien en biomase de l'infanterie."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="<string name='Traits/Tyranids/InfantryUpkeepFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2" value="Réducteur de sauvagerie"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Description" value="Réduit le coûte en influence pour outrepasser le comportement instinctif.."/>
	<entry name="Tyranids/InstinctiveBehaviourOverride2Flavor" value="Maintenir le contrôle de ses troupes est un problème majeur pour l'Esprit de la Ruche, un problème qu'il cherche à résoudre par différentes adaptations, comme les créatures synaptiques. Une solution plus simple est de réduire la sauvagerie naturelle des unités lorsqu'elles ne sont plus contrôlées, pour qu'elles nécessitent moins d'efforts psychiques pour les contrôler de nouveau."/>
	<entry name="Tyranids/LongRangedDamage" value="<string name='Traits/Tyranids/LongRangedDamage'/>"/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Augmente la pénétration d'armure des armes à longue portée."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="<string name='Traits/Tyranids/LongRangedDamageFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="<string name='Traits/Tyranids/MeleeDamage'/>"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Augmente la pénétration d'armure des armes de corps à corps."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="<string name='Traits/Tyranids/MeleeDamageFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="Confère aux Zoanthropes la capacité de maudir les ennemis pour que leur précision diminue."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation2" value="Gustarflagelli malanthropique"/>
	<entry name="Tyranids/PreyAdaptation2Description" value="Augmente la recherche gagnée lorsque des ennemis meurent près du Malanthrope."/>
	<entry name="Tyranids/PreyAdaptation2Flavor" value="Une adaptation inhabituelle de la forme malnthropique, les gustarflagelli sont des vrilles extrêmement fines qui entourent le Malanthrope comme un nuage de chair frétillante. On suppose que cela permet au Malanthrope de rassembler et retenir davantage d'information génétique des ennemis récemment décédés."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="<string name='Traits/Tyranids/ProductionBuildingUpkeep'/>"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Réduit le coût d'entretien en influence des bâtiments de production."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ProductionBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="<string name='Traits/Tyranids/Reclamation2'/>"/>
	<entry name="Tyranids/Reclamation2Description" value="Réduit le coût en influence pour dévorer une unité."/>
	<entry name="Tyranids/Reclamation2Flavor" value="<string name='Traits/Tyranids/Reclamation2Flavor'/>"/>
	<entry name="Tyranids/Reclamation3" value="<string name='Traits/Tyranids/Reclamation3'/>"/>
	<entry name="Tyranids/Reclamation3Description" value="Supprime le temps de rechargement de la récupération."/>
	<entry name="Tyranids/Reclamation3Flavor" value="<string name='Traits/Tyranids/Reclamation3Flavor'/>"/>
	<entry name="Tyranids/Regeneration" value="Régénération"/>
	<entry name="Tyranids/RegenerationDescription" value="Restaure des points de vie aux créatures monstrueuses Tyranides et au Primat Tyranide tous les tours."/>
	<entry name="Tyranids/RegenerationFlavor" value="<string name='Traits/RegenerationFlavor'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="<string name='Traits/Tyranids/ResourceBuildingUpkeep'/>"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Réduit le coût d'entretien en influence des bâtiments de ressource."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="<string name='Traits/Tyranids/ResourceBuildingUpkeepFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="<string name='Traits/Tyranids/ShortRangedDamage'/>"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Augmente la pénétration d'armure des armes à courte portée"/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="<string name='Traits/Tyranids/ShortRangedDamageFlavor'/>"/>
	<entry name="Tyranids/StingerSalvo" value="<string name='Weapons/StingerSalvo'/>"/>
	<entry name="Tyranids/StingerSalvoDescription" value="Confère aux Viragos des ruches une arme à distance supplémentaire."/>
	<entry name="Tyranids/StingerSalvoFlavor" value="<string name='Weapons/StingerSalvoFlavor'/>"/>
	<entry name="Tyranids/ThresherScythe" value="<string name='Weapons/ThresherScythe'/>"/>
	<entry name="Tyranids/ThresherScytheDescription" value="Confère aux Exocrines et aux Haruspexs une arme de corps à corps supplémentaire."/>
	<entry name="Tyranids/ThresherScytheFlavor" value="<string name='Weapons/ThresherScytheFlavor'/>"/>
	<entry name="Tyranids/ToxinSacs" value="<string name='Traits/Tyranids/ToxinSacs'/>"/>
	<entry name="Tyranids/ToxinSacsDescription" value="Augmente les dégâts des armes de corps à corps contre l'infanterie et les créatures monstrueuses."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="<string name='Traits/Tyranids/ToxinSacsFlavor'/>"/>
	<entry name="Tyranids/Toxinspike" value="Dard"/>
	<entry name="Tyranids/ToxinspikeDescription" value="Confère aux Trygons une arme de corps à corps supplémentaire."/>
	<entry name="Tyranids/ToxinspikeFlavor" value="<string name='Weapons/ToxinspikeFlavor'/>"/>
	<entry name="Tyranids/Tunnel2" value="<string name='Traits/Tyranids/Tunnel2'/>"/>
	<entry name="Tyranids/Tunnel2Description" value="Augmente les points de vie des Ruches creuses."/>
	<entry name="Tyranids/Tunnel2Flavor" value="<string name='Traits/Tyranids/Tunnel2Flavor'/>"/>
	<entry name="Tyranids/VehiclesUpkeep" value="<string name='Traits/Tyranids/VehiclesUpkeep'/>"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Réduit le coût d'entretien en biomasse des créatures monstrueuses."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="<string name='Traits/Tyranids/VehiclesUpkeepFlavor'/>"/>
	<entry name="Missing" value="Manquant"/>
</language>
