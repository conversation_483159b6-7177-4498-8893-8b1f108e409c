<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="Audio" value="Audio"/>
	<entry name="Audio/AmbientVolume" value="Ambient Volume"/>
	<entry name="Audio/AmbientVolumeHint" value="Regulates the ambient volume."/>
	<entry name="Audio/CinematicVolume" value="Cinematic Volume"/>
	<entry name="Audio/CinematicVolumeHint" value="Regulates the volume of cinematic cutscenes."/>
	<entry name="Audio/EffectVolume" value="Effect Volume"/>
	<entry name="Audio/EffectVolumeHint" value="Regulates the volume of effects."/>
	<entry name="Audio/HRTF" value="HRTF"/>
	<entry name="Audio/HRTFHint" value="Request mixing using a Head-Related Transfer Function, or HRTF.<br/><br/>HRTF can provide better spatial acuity when using headphones by using special filters designed to replicate how sounds are affected by the shape of the listener's head as they come in from a given direction.<br/><br/>Requesting HRTF mixing may cause the AL to reconfigure the device for a specific output mode and restrict the usable frequency values.<br/><br/><style color='GUI/Yellow'/>A word of warning: although HRTF can sound great with headphones, it may result in increased resource usage and it may not sound very good with ordinary speakers, particularly if the user has surround sound output."/>
	<entry name="Audio/InterfaceVolume" value="Interface Volume"/>
	<entry name="Audio/InterfaceVolumeHint" value="Regulates the volume of the interface."/>
	<entry name="Audio/MasterVolume" value="Master Volume"/>
	<entry name="Audio/MasterVolumeHint" value="Regulates the master volume."/>
	<entry name="Audio/MusicVolume" value="Music Volume"/>
	<entry name="Audio/MusicVolumeHint" value="Regulates the volume of music."/>
	<entry name="Audio/OutputDevice" value="Output Device"/>
	<entry name="Audio/OutputDeviceHint" value=""/>
	<entry name="Audio/VoiceVolume" value="Voice Volume"/>
	<entry name="Audio/VoiceVolumeHint" value="Regulates the volume of voices."/>
	<entry name="Controls" value="Controls"/>
	<entry name="Controls/Cancel" value="Cancel"/>
	<entry name="Controls/Cargo0" value="Cargo 1"/>
	<entry name="Controls/Cargo1" value="Cargo 2"/>
	<entry name="Controls/Cargo2" value="Cargo 3"/>
	<entry name="Controls/Cargo3" value="Cargo 4"/>
	<entry name="Controls/Cargo4" value="Cargo 5"/>
	<entry name="Controls/Cargo5" value="Cargo 6"/>
	<entry name="Controls/Cargo6" value="Cargo 7"/>
	<entry name="Controls/Cargo7" value="Cargo 8"/>
	<entry name="Controls/Cargo8" value="Cargo 9"/>
	<entry name="Controls/Cargo9" value="Cargo 10"/>
	<entry name="Controls/Chat" value="Chat"/>
	<entry name="Controls/ForceEndTurn" value="Force End Turn"/>
	<entry name="Controls/MarkTile" value="Mark Tile"/>
	<entry name="Controls/NextMusicTrack" value="Next Music Track"/>
	<entry name="Controls/NextTask" value="Next Task"/>
	<entry name="Controls/OK" value="OK"/>
	<entry name="Controls/OpenCompendiumScreen" value="Open Compendium Screen"/>
	<entry name="Controls/OpenQuestsScreen" value="Open Quests Screen"/>
	<entry name="Controls/OpenResearchScreen" value="Open Research Screen"/>
	<entry name="Controls/PlayerAction0" value="Player Action 1"/>
	<entry name="Controls/PlayerAction1" value="Player Action 2"/>
	<entry name="Controls/PlayerAction2" value="Player Action 3"/>
	<entry name="Controls/PlayerAction3" value="Player Action 4"/>
	<entry name="Controls/PlayerAction4" value="Player Action 5"/>
	<entry name="Controls/PlayerAction5" value="Player Action 6"/>
	<entry name="Controls/PlayerAction6" value="Player Action 7"/>
	<entry name="Controls/PlayerAction7" value="Player Action 8"/>
	<entry name="Controls/PlayerAction8" value="Player Action 9"/>
	<entry name="Controls/PlayerAction9" value="Player Action 10"/>
	<entry name="Controls/QuickLoad" value="Quick Load"/>
	<entry name="Controls/QuickSave" value="Quick Save"/>
	<entry name="Controls/ReloadModifiedResources" value="Reload Modified Resources"/>
	<entry name="Controls/SaveJPGScreenShot" value="Save JPG Screen Shot"/>
	<entry name="Controls/SaveJPGScreenShotWithoutHUD" value="Save JPG Screen Shot Without HUD"/>
	<entry name="Controls/SavePNGScreenShot" value="Save PNG Screen Shot"/>
	<entry name="Controls/SavePNGScreenShotWithoutHUD" value="Save PNG Screen Shot Without HUD"/>
	<entry name="Controls/ScrollDown" value="Scroll Down"/>
	<entry name="Controls/ScrollLeft" value="Scroll Left"/>
	<entry name="Controls/ScrollRight" value="Scroll Right"/>
	<entry name="Controls/ScrollUp" value="Scroll Up"/>
	<entry name="Controls/SelectNext" value="Select Next"/>
	<entry name="Controls/SelectPrevious" value="Select Previous"/>
	<entry name="Controls/ToggleDebugPanel" value="Toggle Debug Panel"/>
	<entry name="Controls/ToggleDisplayMode" value="Toggle Display Mode"/>
	<entry name="Controls/ToggleFPSDisplay" value="Toggle FPS Display"/>
	<entry name="Controls/ToggleHUD" value="Toggle HUD"/>
	<entry name="Controls/ToggleLoadScreen" value="Toggle Load Screen"/>
	<entry name="Controls/ToggleMixerPanel" value="Toggle Mixer Panel"/>
	<entry name="Controls/ToggleSaveScreen" value="Toggle Save Screen"/>
	<entry name="Controls/ToggleSettingsScreen" value="Toggle Settings Screen"/>
	<entry name="Controls/ToggleStatsPanel" value="Toggle Stats Panel"/>
	<entry name="Controls/ToggleUnitOverlay" value="Toggle Unit Overlay"/>
	<entry name="Controls/ToggleYieldOverlay" value="Toggle Yield Overlay"/>
	<entry name="Controls/UnitAction0" value="Unit Action 1"/>
	<entry name="Controls/UnitAction1" value="Unit Action 2"/>
	<entry name="Controls/UnitAction2" value="Unit Action 3"/>
	<entry name="Controls/UnitAction3" value="Unit Action 4"/>
	<entry name="Controls/UnitAction4" value="Unit Action 5"/>
	<entry name="Controls/UnitAction5" value="Unit Action 6"/>
	<entry name="Controls/UnitAction6" value="Unit Action 7"/>
	<entry name="Controls/UnitAction7" value="Unit Action 8"/>
	<entry name="Controls/UnitAction8" value="Unit Action 9"/>
	<entry name="Controls/UnitAction9" value="Unit Action 10"/>
	<entry name="Controls/UnitAction10" value="Unit Action 11"/>
	<entry name="Controls/UnitAction11" value="Unit Action 12"/>
	<entry name="Controls/UnitAction12" value="Unit Action 13"/>
	<entry name="Controls/UnitAction13" value="Unit Action 14"/>
	<entry name="Controls/UnitAction14" value="Unit Action 15"/>
	<entry name="Controls/UnitItem0" value="Unit Item 1"/>
	<entry name="Controls/UnitItem1" value="Unit Item 2"/>
	<entry name="Controls/UnitItem2" value="Unit Item 3"/>
	<entry name="Controls/UnitItem3" value="Unit Item 4"/>
	<entry name="Controls/UnitItem4" value="Unit Item 5"/>
	<entry name="Controls/UnitItem5" value="Unit Item 6"/>
	<entry name="Controls/UnitItem6" value="Unit Item 7"/>
	<entry name="Controls/UnitItem7" value="Unit Item 8"/>
	<entry name="Controls/UnitItem8" value="Unit Item 9"/>
	<entry name="Controls/UnitItem9" value="Unit Item 10"/>
	<entry name="General" value="General"/>
	<entry name="General/AnimationSpeed" value="Animation Speed"/>
	<entry name="General/AnimationSpeedHint" value=""/>
	<entry name="General/AutoCycleUnits" value="Auto-Cycle Units"/>
	<entry name="General/AutoCycleUnitsHint" value="Automatically cycle to the next idle unit."/>
	<entry name="General/AutoSendReports" value="Send Usage Statistics and Crash Reports"/>
	<entry name="General/AutoSendReportsHint" value="Automatically send usage statistics and crash reports to Proxy Studios to help improve the game's features, stability and performance.<br/><br/><style color='GUI/Yellow'/>Privacy is important to us. We strive to collect only what we need to improve the game for everyone. The data collected includes anonymized interactions with the game (such as the number of multiplayer connection attempts and failures, types of games started, features used, buttons clicked, and playtime) as well as anonymized technical information (such as game version and language, operating system and hardware configuration, and basic information about crashes and errors). This data is processed in accordance with the legitimate interest of providing a high-quality, reliable product; it is stored for up to 1 year and never shared with any third-parties."/>
	<entry name="General/Language" value="Language"/>
	<entry name="General/LanguageHint" value="Specifies the written language.<br/><br/><style color='GUI/Red'/>Changing this setting requires a game restart to take effect."/>
	<entry name="General/ResetTips" value="Reset Tips"/>
	<entry name="General/ResetTipsHint" value="Reset game tips so that they will be displayed again."/>
	<entry name="General/ScrollAcceleration" value="Scroll Acceleration"/>
	<entry name="General/ScrollAccelerationHint" value=""/>
	<entry name="General/ScrollSpeed" value="Scroll Speed"/>
	<entry name="General/ScrollSpeedHint" value=""/>
	<entry name="General/ScrollToAction" value="Auto-Scroll to Action"/>
	<entry name="General/ScrollToActionHint" value="The camera automatically scrolls to any action going on during the AI turn."/>
	<entry name="General/ShowBarks" value="Show Barks"/>
	<entry name="General/ShowBarksHint" value="Display unit barks."/>
	<entry name="General/ShowCinematics" value="Show Cinematics"/>
	<entry name="General/ShowCinematicsHint" value="Display cinematic cutscenes."/>
	<entry name="General/ShowPerformance" value="Show Performance"/>
	<entry name="General/ShowPerformanceHint" value="Display performance info."/>
	<entry name="General/ShowSubtitles" value="Show Subtitles"/>
	<entry name="General/ShowSubtitlesHint" value="Display captions at the bottom of cinematic cutscenes that translate or transcribe the dialogue and narrative."/>
	<entry name="General/ShowTips" value="Show Tips"/>
	<entry name="General/ShowTipsHint" value="Display helpful game tips."/>
	<entry name="Network" value="Network"/>
	<entry name="Network/ListenPort" value="Listen Port"/>
	<entry name="Network/ListenPortHint" value="The local TCP port to use when hosting a multiplayer game.<br/><br/><style color='GUI/Yellow'/>If you experience connection issues, make sure to allow this port on your firewall and forward it to the machine running the game on your router. Search the internet for 'Port Forwarding' for instructions for your router."/>
	<entry name="Network/PreferRelayedData" value="Prefer Relayed Data"/>
	<entry name="Network/PreferRelayedDataHint" value="Instead of connecting directly to the host, relay all data through a relay server if one is available.<br/><br/>This will increase the network latency.<br/><br/>When turned off, data is still relayed if the initial direct connection fails.<br/><br/><style color='GUI/Yellow'/>Only use if you are experiencing connection issues and cannot resolve them otherwise."/>
	<entry name="Network/RemoteAddress" value="Remote Address"/>
	<entry name="Network/RemotePort" value="Remote Port"/>
	<entry name="Video" value="Video"/>
	<entry name="Video/AnisotropicFiltering" value="Anisotropic Filtering"/>
	<entry name="Video/AnisotropicFilteringHint" value=""/>
	<entry name="Video/Brightness" value="Brightness"/>
	<entry name="Video/BrightnessHint" value=""/>
	<entry name="Video/CloudShadows" value="Cloud Shadows"/>
	<entry name="Video/CloudShadowsHint" value=""/>
	<entry name="Video/Contrast" value="Contrast"/>
	<entry name="Video/ContrastHint" value=""/>
	<entry name="Video/DepthFog" value="Depth Fog"/>
	<entry name="Video/DepthFogHint" value=""/>
	<entry name="Video/DepthOfField" value="Depth of Field"/>
	<entry name="Video/DepthOfFieldHint" value=""/>
	<entry name="Video/Device" value="Device"/>
	<entry name="Video/DeviceHint" value=""/>
	<entry name="Video/DisplayMode" value="Display Mode"/>
	<entry name="Video/DisplayMode0" value="Fullscreen"/>
	<entry name="Video/DisplayMode1" value="Windowed"/>
	<entry name="Video/DisplayMode2" value="Windowed Undecorated"/>
	<entry name="Video/DisplayModeHint" value="Allows you to change the game's display mode. Windowed modes may cause a drop in performance.<br/><br/><style color='GUI/Yellow'/>Windowed mode may work best for recording in the native resolution."/>
	<entry name="Video/FixedTimeOfDay" value="Fixed Time of Day"/>
	<entry name="Video/FixedTimeOfDayHint" value=""/>
	<entry name="Video/FixedTimeOfDayAt" value="Fixed Time of Day at"/>
	<entry name="Video/FixedTimeOfDayAtHint" value=""/>
	<entry name="Video/FXAA" value="Anti-Aliasing"/>
	<entry name="Video/FXAAHint" value="Reduces aliasing by smoothing edges with FXAA, but may reduce performance. Relies on your graphics card's computing power."/>
	<entry name="Video/Gamma" value="Gamma"/>
	<entry name="Video/GammaHint" value=""/>
	<entry name="Video/GUIScale" value="GUI Scale"/>
	<entry name="Video/GUIScaleCustom" value="Custom: %1%%%"/>
	<entry name="Video/GUIScaleHint" value="Scales the graphical user interface."/>
	<entry name="Video/MaxFocusedTickrate" value="Max Focused FPS"/>
	<entry name="Video/MaxFocusedTickrateHint" value=""/>
	<entry name="Video/MaxMinimizedTickrate" value="Max Minimized FPS"/>
	<entry name="Video/MaxMinimizedTickrateHint" value=""/>
	<entry name="Video/MaxUnfocusedTickrate" value="Max Unfocused FPS"/>
	<entry name="Video/MaxUnfocusedTickrateHint" value=""/>
	<entry name="Video/Monitor" value="Monitor"/>
	<entry name="Video/MonitorHint" value=""/>
	<entry name="Video/MultiThreadedRendering" value="Multi-Threaded Rendering"/>
	<entry name="Video/MultiThreadedRenderingHint" value="Uses multiple CPU cores for rendering, which can increase performance. Disabling this setting might remove potential visual artefacts."/>
	<entry name="Video/Resolution" value="Resolution"/>
	<entry name="Video/ResolutionHint" value="Allows you to change the game's display resolution. Higher resolutions will result in increased clarity, but greatly affect performance."/>
	<entry name="Video/ShadowQuality" value="Shadow Quality"/>
	<entry name="Video/ShadowQuality0" value="Low"/>
	<entry name="Video/ShadowQuality1" value="Medium"/>
	<entry name="Video/ShadowQuality2" value="High"/>
	<entry name="Video/ShadowQualityHint" value="Determines the size of the shadow map used for rendering shadows. A higher shadow quality will result in more detailed shadows, but requires a more powerful graphics card."/>
	<entry name="Video/ShowCliffHighlights" value="Show Cliff Highlights"/>
	<entry name="Video/ShowCliffHighlightsHint" value="Display dashed lines to highlight cliffs."/>
	<entry name="Video/ShowGrid" value="Show Grid"/>
	<entry name="Video/ShowGridHint" value="Display the ground grid."/>
	<entry name="Video/TextureQuality" value="Texture Quality"/>
	<entry name="Video/TextureQuality0" value="Low"/>
	<entry name="Video/TextureQuality1" value="Medium"/>
	<entry name="Video/TextureQuality2" value="High"/>
	<entry name="Video/TextureQualityHint" value="Determines the size of textures used throughout the game. Higher texture quality will result in more detailed terrain and units, but requires more video memory (VRAM).<br/><br/>Low: 1 GB<br/>Medium: 2 GB<br/>High: 3 GB"/>
	<entry name="Video/VerticalSynchronization" value="Vertical Synchronization"/>
	<entry name="Video/VerticalSynchronization-1" value="Adaptive"/> <!-- TODO: Hints -->
	<entry name="Video/VerticalSynchronization0" value="None"/>
	<entry name="Video/VerticalSynchronization1" value="Regular"/>
	<entry name="Video/VerticalSynchronizationHint" value="Synchronizes your frame rate to your monitor's refresh rate."/>
	<entry name="World" value="World"/>
</language>
