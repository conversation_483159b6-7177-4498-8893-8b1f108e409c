<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="(Turn)" value="(%1% turn)"/>
	<entry name="(Turns)" value="(%1% turns)"/>
	<entry name="AI" value="[AI]"/>
	<entry name="AITurn" value="Enemy Turn"/>
	<entry name="AchievementsDisabled" value="Achievements disabled."/>
	<entry name="Actions" value="Actions"/>
	<entry name="AddPlayerHint" value="Add a player."/>
	<entry name="AdditionalContentAvailable" value="Additional content available"/>
	<entry name="Advanced" value="Advanced"/>
	<entry name="AdeptusMechanicusPlayerPowers" value="Glory of the Machine God"/>
	<entry name="Agree" value="Agree"/>
	<entry name="AgreeTermsAndConditionsHint" value="I have read and agree to the terms and conditions."/>
	<entry name="All" value="All"/>
	<entry name="Artefact" value="Artefact"/>
	<entry name="AttackEstimate" value="Attack Estimate"/>
	<entry name="Attributes" value="Attributes"/>
	<entry name="AvailableNow" value="Available Now"/>
	<entry name="Back" value="Back"/>
	<entry name="Basic" value="Basic"/>
	<entry name="Battle" value="Battle"/>
	<entry name="BindingHint" value="[Mouse Left] to change binding.<br/><style color='GUI/Yellow'/>[Mouse Middle] to add new binding.<br/><style color='GUI/Red'/>[Mouse Right] to unbind."/>
	<entry name="Buildings" value="Buildings"/>
	<entry name="BuildingsConstructed" value="Buildings Constructed"/>
	<entry name="CannotDisembarkThisTurn" value="Cannot disembark this turn."/>
	<entry name="Cancel" value="Cancel"/>
	<entry name="Cargo" value="Cargo"/>
	<entry name="Charges" value="Charges"/>
	<entry name="Charging" value="Charging"/>
	<entry name="Chat" value="Chat"/>
	<entry name="ChooseResearch" value="Choose Research"/>
	<entry name="ChooseResearchHint" value="Choose your research focus [<control name='Controls/NextTask'/>]."/>
	<entry name="CityResources" value="City Resources"/>
	<entry name="ClaimItemOnlyWithHero" value="Select hero unit before claiming item."/>
	<entry name="ClearNotifications" value="Clear Notifications"/>
	<entry name="ClearNotificationsHint" value="Clear all notifications [<control name='Controls/NextTask'/>]."/>
	<entry name="Community" value="Community"/>
	<entry name="Compendium" value="Compendium"/>
	<entry name="CompendiumFlavor" value="The Compendium stores a collection of useful game information. Here you can read up on factions, buildings, units, re-read tips and more."/>
	<entry name="Connect" value="Connect"/>
	<entry name="ConnectEpicAccount" value="Connect Epic Account"/>
	<entry name="ConsumesAction" value="<icon height='20' texture='Icons/Attributes/Actions'/> <style color='GUI/Yellow'/>Consumes action<style color='Default'/>"/>
	<entry name="ConsumesMovement" value="<icon height='20' texture='Icons/Attributes/Movement'/> <style color='GUI/Yellow'/>Consumes movement<style color='Default'/>"/>
	<entry name="Continue" value="Continue"/>
	<entry name="Cooldown" value="Cooldown"/>
	<entry name="Copy" value="Copy"/>
	<entry name="CopyInviteCode" value="Copy Invite Code"/>
	<entry name="CopyInviteCodeHint" value="Copy the invite code which can be pasted to other players to easily join this game.<br/><br/>Please note that your public IP address can be extracted from the code."/>
	<entry name="Cost" value="Cost"/>
	<entry name="Create" value="Create"/>
	<entry name="Credits" value="Credits"/>
	<entry name="DLC" value="DLC"/>
	<entry name="DLCNotEnabled" value="<style color='GUI/Red'/>%1% not enabled for this game. Enable it in game lobby advanced settings before starting a new game."/>
	<entry name="DLCNotInstalled" value="<style color='GUI/Red'/>%1% not installed."/>
	<entry name="DamageDealt" value="Damage Dealt"/>
	<entry name="DamageTaken" value="Damage Taken"/>
	<entry name="Debug" value="Debug"/>
	<entry name="Default" value="Default"/>
	<entry name="Defeated" value="Defeated"/>
	<entry name="Delete" value="Delete"/>
	<entry name="Details" value="Details"/>
	<entry name="DirectConnect" value="Direct Connect"/>
	<entry name="DisableBuildingHint" value="Disable a building of this type, freeing its allocated population for other buildings."/>
	<entry name="DisableSimultaneousTurns" value="Disable Simultaneous Turns"/>
	<entry name="DisableTips" value="Disable Tips"/>
	<entry name="DisableTipsHint" value="Stop showing pop-up tips. You may re-enable them in the settings panel."/>
	<entry name="DisconnectEpicAccount" value="Disconnect Epic Account"/>
	<entry name="Disconnected" value="Disconnected"/>
	<entry name="DismissReports" value="Dismiss All (%1%)"/>
	<entry name="DragToChangeLoadOrder" value="Drag to change load priority."/>
	<entry name="DrukhariPlayerPowers" value="Dark Disciplines"/>
	<entry name="Economy" value="Economy"/>
	<entry name="Edicts" value="Edicts"/>
	<entry name="EldarCityPowers" value="The Asuryani Path"/>
	<entry name="EldarPlayerPowers" value="Webway Control"/>
	<entry name="Elite" value="Elite"/>
	<entry name="EnableBuildingHint" value="Enable a building of this type, allocating population to it."/>
	<entry name="EnableMod" value="Enable mod."/>
	<entry name="EnableSimultaneousTurns" value="Enable Simultaneous Turns"/>
	<entry name="EndTurn" value="End Turn"/>
	<entry name="EndTurnHint" value="End your turn [<control name='Controls/NextTask'/>]."/>
	<entry name="Enter" value="Enter"/>
	<entry name="EstimatedCasualties" value="estimated %1%/%2% casualties"/>
	<entry name="Events" value="Events"/>
	<entry name="ExecuteQueuedOrders" value="Execute Queued Orders (%1%)"/>
	<entry name="ExecuteQueuedOrdersHint" value="Execute orders queued in the previous turn [<control name='Controls/NextTask'/>]."/>
	<entry name="Extra" value="Extra"/>
	<entry name="ForceEndTurnHint" value="End Your Turn [<control name='Controls/ForceEndTurn'/>]."/>
	<entry name="ExitCity" value="Exit City"/>
	<entry name="ExitCityHint" value="Exit the city screen [<control name='Controls/NextTask'/>]."/>
	<entry name="ExitToMainMenu" value="Exit to Main Menu"/>
	<entry name="ExitToDesktop" value="Exit to Desktop"/>
	<entry name="Explored" value="Explored"/>
	<entry name="Factions" value="Factions"/>
	<entry name="Features" value="Features"/>
	<entry name="FreeAction" value="<icon height='20' texture='Icons/Attributes/Actions'/> Free action"/>
	<entry name="Full" value="Full"/>
	<entry name="GameBrowser" value="Game Browser"/>
	<entry name="GameLobby" value="Game Lobby"/>
	<entry name="Games" value="Games"/>
	<entry name="Hint" value="<style name='Title'/>%1%%2%<style name='Default'/><br/>%3%%4%%5%%6%%7%%8%<style name='Italic'/>%9%"/>
	<entry name="HoldKeyForMeleeAttack" value="Hold Ctrl for max damage."/>
	<entry name="HoldKeyToKeepVisible" value="Hold Shift to keep visible."/>
	<entry name="HoldKeyToShowOpponentAttack" value="Hold Alt to show opponent attack."/>
	<entry name="HotseatNotAvailable" value="<style color='GUI/Red'/>Please disable simultaneous turns under advanced settings to allow hotseat."/>
	<entry name="Host" value="Host"/>
	<entry name="Inaccessible" value="Inaccessible"/>
	<entry name="IncompatibleVersion" value="Incompatible Version"/>
	<entry name="InfoFormat" value="%3%%4%<style name='Italic'/>%5%<style name='Default'/>%9%%6%%7%%8%"/>
	<entry name="Installed" value="Installed"/>
	<entry name="IntroductionGame" value="Introduction"/>
	<entry name="IntroductionGameHint" value="Start an introduction game with Space Marines on the easiest difficulty."/>
	<entry name="InvalidName" value="Invalid name."/>
	<entry name="InviteEpicFriend" value="Invite Epic Friend"/>
	<entry name="InviteSteamFriend" value="Invite Steam Friend"/>
	<entry name="InviteSteamFriendHint" value="<style color='GUI/Yellow'/>Steam is only able to send friend invites if your profile privacy settings (game details) are not set to private."/>
	<entry name="InviteSteamFriendDisabledHint" value="Host not running Steam version or Steam matchmaking unavailable."/>
	<entry name="Invulnerable" value="Invulnerable"/>
	<entry name="ItemShop" value="Item Shop"/>
	<entry name="Items" value="Items"/>
	<entry name="Join" value="Join"/>
	<entry name="JoinDiscord" value="Join Discord"/>
	<entry name="JustOneMoreTurn" value="Wait! Just… one… more… turn…"/>
	<entry name="LevelHint" value="<icon texture='GUI/Bullet'/>%1%/%2% experience<br/><icon texture='GUI/Bullet'/>%3% death experience value"/>
	<entry name="LimitReached" value="Limit reached."/>
	<entry name="Load" value="Load"/>
	<entry name="LoadGame" value="Load Game"/>
	<entry name="MainMenu" value="Main Menu"/>
	<entry name="MarkTileHint" value="<style name='Heading'/>Mark Tile [<control name='Controls/MarkTile'/>]<br/><style name='Default'/>Add or remove a marker for allies. AI allies will tend to move their units towards markers."/>
	<entry name="MarksOfChaos" value="Marks of Chaos"/>
	<entry name="Max" value="max"/>
	<entry name="Menu" value="Menu"/>
	<entry name="Military" value="Military"/>
	<entry name="Min" value="min"/>
	<entry name="Minimap" value="Minimap"/>
	<entry name="Missing" value="?"/>
	<entry name="Mixer" value="Mixer"/>
	<entry name="ModDetailsHint" value="Open mod details."/>
	<entry name="ModFolderHint" value="Open mod folder."/>
	<entry name="Mods" value="Mods"/>
	<entry name="More" value="More"/>
	<entry name="Multiplayer" value="Multiplayer"/>
	<entry name="Name" value="Name"/>
	<entry name="New" value="New"/>
	<entry name="NewGame" value="New Game"/>
	<entry name="NewGameHint" value="Choose your faction and customize world parameters."/>
	<entry name="NewSavedGame" value="New Saved Game"/>
	<entry name="NoChargesRemaining" value="No charges remaining."/>
	<entry name="NoFreeItemSlots" value="No free item slots."/>
	<entry name="NoResearchSelected" value="No Research Selected"/>
	<entry name="NoValidTargets" value="No valid targets."/>
	<entry name="NotEnoughResources" value="Not enough resources."/>
	<entry name="NotReady" value="Not Ready"/>
	<entry name="NotUsable" value="Not usable."/>
	<entry name="OK" value="OK"/>
	<entry name="Objectives" value="Objectives"/>
	<entry name="ObjectivesCompleted" value="Objectives Completed"/>
	<entry name="OnCooldown" value="On cooldown."/>
	<entry name="OpenStore" value="Click to open store."/>
	<entry name="Operations" value="Operations and Tactics"/>
	<entry name="Or" value="or"/>
	<entry name="OrderCity" value="Order Cities (%1%)"/>
	<entry name="OrderCityHint" value="Select next idle city [<control name='Controls/NextTask'/>]."/>
	<entry name="OrderUnits" value="Order Units (%1%)"/>
	<entry name="OrderUnitsHint" value="Select next idle unit [<control name='Controls/NextTask'/>]."/>
	<entry name="Overwrite" value="Overwrite"/>
	<entry name="PauseTurnTimer" value="Pause Timer"/>
	<entry name="Parameters" value="Parameters"/>
	<entry name="Passive" value="Passive"/>
	<entry name="PassiveAction" value="Passive action"/>
	<entry name="Paste" value="Paste"/>
	<entry name="PasteInviteCode" value="Paste Invite Code"/>
	<entry name="PasteInviteCodeHint" value="Join the game from the invite code in your clipboard."/>
	<entry name="PasteInviteCodeInvalidHint" value="<style color='GUI/Red'/>Invalid invite code.<style color='Default'/><br/><br/><string name='GUI/PasteInviteCodeHint'/>"/>
	<entry name="PatchNotes" value="Patch Notes"/>
	<entry name="Ping" value="Ping"/>
	<entry name="Play" value="Play"/>
	<entry name="Player" value="Player"/>
	<entry name="PlayerXTurn" value="%1%'s Turn"/>
	<entry name="Players" value="Players"/>
	<entry name="PleaseWait" value="Please Wait …"/>
	<entry name="PleaseWaitHint" value="Please wait for other players."/>
	<entry name="Quests" value="Quests"/>
	<entry name="QuickSelectFactionHint" value="Quick-select faction."/>
	<entry name="Quit" value="Quit"/>
	<entry name="RadiusTile" value="<icon height='20' texture='Icons/Attributes/Radius'/> Radius: %1% tile"/>
	<entry name="RadiusTiles" value="<icon height='20' texture='Icons/Attributes/Radius'/> Radius: %1% tiles"/>
	<entry name="Random" value="Random"/>
	<entry name="Randomize" value="Randomize"/>
	<entry name="RangeSelf" value="<icon height='20' texture='Icons/Attributes/Range'/> Range: self"/>
	<entry name="RangeTilesMax" value="<icon height='20' texture='Icons/Attributes/Range'/> Range: %1% tiles"/>
	<entry name="RangeTilesMinMax" value="<icon height='20' texture='Icons/Attributes/Range'/> Range: %1%—%2% tiles"/>
	<entry name="Ready" value="Ready"/>
	<entry name="Refresh" value="Refresh"/>
	<entry name="RemovePlayerHint" value="Remove player."/>
	<entry name="RenameHint" value="Rename."/>
	<entry name="(Required)" value="(%1% required)"/>
	<entry name="RequiredLevel" value="Required Level: %1%"/>
	<entry name="Requires" value="Requires"/>
	<entry name="RequiresAction" value="<icon height='20' texture='Icons/Attributes/Actions'/> <style color='GUI/Yellow'/>Requires action<style color='Default'/>"/>
	<entry name="RequiresMovement" value="<icon height='20' texture='Icons/Attributes/Movement'/> <style color='GUI/Yellow'/>Requires movement<style color='Default'/>"/>
	<entry name="Research" value="Research"/>
	<entry name="Resource" value="<icon texture='GUI/Bullet'/>%1%"/>
	<entry name="ResourceAccumulated" value="<icon texture='GUI/Bullet'/>%1% accumulated"/>
	<entry name="ResourceFromAdjacentBuildings" value="    <icon texture='GUI/Bullet'/>%1% from adjacent buildings"/>
	<entry name="ResourceFromBuildings" value="    <icon texture='GUI/Bullet'/>%1% from buildings"/>
	<entry name="ResourceFromCities" value="    <icon texture='GUI/Bullet'/>%1% from city amount"/>
	<entry name="ResourceFromDestruction" value="    <icon texture='GUI/Bullet'/>%1% from destruction"/>
	<entry name="ResourceFromDifficulty" value="    <icon texture='GUI/Bullet'/>%1% from difficulty"/>
	<entry name="ResourceFromFeatures" value="    <icon texture='GUI/Bullet'/>%1% from features"/>
	<entry name="ResourceFromLoyalty" value="    <icon texture='GUI/Bullet'/>%1% from loyalty"/>
	<entry name="ResourceFromPopulation" value="    <icon texture='GUI/Bullet'/>%1% from population"/>
	<entry name="ResourceFromShortage" value="    <icon texture='GUI/Bullet'/>%1% from resource shortage"/>
	<entry name="ResourceFromTiles" value="    <icon texture='GUI/Bullet'/>%1% from tiles"/>
	<entry name="ResourceFromUnits" value="    <icon texture='GUI/Bullet'/>%1% from units"/>
	<entry name="ResourceFromUtopia" value="    <icon texture='GUI/Bullet'/>%1% from utopia"/>
	<entry name="ResourceLimit" value="<icon texture='GUI/Bullet'/>%1% limit"/>
	<entry name="ResourcePerTurn" value="<icon texture='GUI/Bullet'/>%1% per turn"/>
	<entry name="ResourceShortageTitle" value="Resource Shortage"/>
	<entry name="ResourceShortage" value="<icon texture='GUI/Bullet'/><style color='GUI/Red'/>%1%%%<style color='Default'/> %2% shortage"/>
	<entry name="ResourceTotal" value="<icon texture='GUI/Bullet'/>%1% total"/>
	<entry name="ResourcesAccumulated" value="Resources Accumulated"/>
	<entry name="Restart" value="Restart"/>
	<entry name="RestartScenario" value="Restart Scenario"/>
	<entry name="RestoreDefaults" value="Restore Defaults"/>
	<entry name="Rewards" value="Rewards"/>
	<entry name="RewardsReceived" value="Rewards Received"/>
	<entry name="SacredRites" value="Sacred Rites"/>
	<entry name="Save" value="Save"/>
	<entry name="SaveGame" value="Save Game"/>
	<entry name="Scenarios" value="Scenarios"/>
	<entry name="Scores" value="Scores"/>
	<entry name="SelectCityHint" value="Select city."/>
	<entry name="SelectColorHint" value="Select color."/>
	<entry name="SelectDifficultyHint" value="Select difficulty."/>
	<entry name="SelectFaction" value="Select Faction"/>
	<entry name="SelectFactionHint" value="Select faction."/>
	<entry name="SelectNextCityHint" value="Select next city."/>
	<entry name="SelectPreviousCityHint" value="Select previous city."/>
	<entry name="SelectTeamHint" value="Select team."/>
	<entry name="SelectUnitHint" value="Select unit."/>
	<entry name="Settings" value="Settings"/>
	<entry name="SinglePlayer" value="Single Player"/>
	<entry name="SkipCity" value="Skip City (1/%1%)"/>
	<entry name="SkipCityHint" value="Skip this city [<control name='Controls/NextTask'/>].<br/>[<control name='Controls/SelectNext'/>]: select next idle city.<br/>[<control name='Controls/SelectPrevious'/>]: select previous idle city."/>
	<entry name="SkipUnit" value="Skip Unit (1/%1%)"/>
	<entry name="SkipUnitHint" value="Skip this unit [<control name='Controls/NextTask'/>].<br/>[<control name='Controls/SelectNext'/>]: select next idle unit.<br/>[<control name='Controls/SelectPrevious'/>]: select previous idle unit."/>
	<entry name="Social" value="Social"/>
	<entry name="Spectating" value="Spectating"/>
	<entry name="Start" value="Start"/>
	<entry name="Stats" value="Stats"/>
	<entry name="Steam" value="Steam"/>
	<entry name="SteamWorkshop" value="Steam Workshop"/>
	<entry name="Subscribe" value="Subscribe"/>
	<entry name="Surrender" value="Surrender"/>
	<entry name="TauPowers" value="Enlightenment and Unity"/>
	<entry name="Team" value="Team"/>
	<entry name="Team0" value="No Team"/>
	<entry name="Team0Hint" value="No Team: plays alone; not part of an alliance."/>
	<entry name="Team1" value="Team A"/>
	<entry name="Team2" value="Team B"/>
	<entry name="Team3" value="Team C"/>
	<entry name="Team4" value="Team D"/>
	<entry name="Team5" value="Team E"/>
	<entry name="Team6" value="Team F"/>
	<entry name="Team7" value="Team G"/>
	<entry name="Team8" value="Team H"/>
	<entry name="Technology" value="Technology"/>
	<entry name="Tier" value="Tier"/>
	<entry name="Tip" value="Tip"/>
	<entry name="Tips" value="Tips"/>
	<entry name="ToggleHotseatHint" value="Toggle hotseat."/>
	<entry name="Total" value="Total"/>
	<entry name="Traits" value="Traits"/>
	<entry name="Turn" value="Turn"/>
	<entry name="UnclaimedItems" value="Unclaimed Items"/>
	<entry name="Unexplored" value="Unexplored"/>
	<entry name="UnholyRites" value="Unholy Rites"/>
	<entry name="Units" value="Units"/>
	<entry name="UnitsCreated" value="Units Created"/>
	<entry name="UnitsLost" value="Units Lost"/>
	<entry name="UnitsKilled" value="Units Killed"/>
	<entry name="UnitsKilledByPlayer" value="You killed: %2%"/>
	<entry name="UnitsKilledOnGladius" value="%2% %1% killed on Gladius"/> <!-- E.g. "142 Tyranids killed on Gladius" -->
	<entry name="UnpauseTurnTimer" value="Unpause Timer"/>
	<entry name="Unready" value="Unready"/>
	<entry name="Upgrades" value="Upgrades"/>
	<entry name="Upkeep" value="Upkeep"/>
	<entry name="UploadModHint" value="Upload mod."/>
	<entry name="UserData" value="User Data"/>
	<entry name="VS" value="VS"/>
	<entry name="Version" value="Version"/>
	<entry name="VersionRevisionsDiffer" value="Version revisions differ (local = %1%, server = %2%)."/>
	<entry name="Victorious" value="Victorious"/>
	<entry name="WaaaghLevelHint" value="<style name='Title'/>Waaagh! Level<style name='Default'/><br/>Each level of Waaagh! increases or decreases Ork unit attacks and building loyalty by 2%% (up to +/-20%%). Waaagh! level is based on accumulated influence and unit influence upkeep, and it refreshes at the start of the turn.<br/><br/><icon texture='GUI/Bullet'/>%1%/%2% (%3%%% attacks and loyalty)<br/><br/><style name='Italic'/><string name='Traits/WaaaghFlavor'/>"/>
	<entry name="Weapons" value="Weapons"/>
	<entry name="WorldDataChecksumsDiffer" value="World data differs (local = %1%, server = %2%)."/>
	<entry name="WorldSeedHint" value="World seed, click to copy."/>
	<entry name="XTurns" value="%1% turns"/>
	<entry name="You" value="You"/>
	<entry name="YourName" value="Your Name"/>
</language>
