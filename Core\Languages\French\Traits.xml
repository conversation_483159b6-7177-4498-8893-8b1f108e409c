<?xml version="1.0" encoding="utf-8"?>
<language>
	<entry name="Artefacts/Accuracy" value="<string name='Units/Neutral/Artefacts/Accuracy'/>"/>
	<entry name="Artefacts/AccuracyDescription" value="Augmente la précision."/>
	<entry name="Artefacts/Armor" value="<string name='Units/Neutral/Artefacts/Armor'/>"/>
	<entry name="Artefacts/ArmorDescription" value="Augmente l'armure."/>
	<entry name="Artefacts/ArmorPenetration" value="<string name='Units/Neutral/Artefacts/ArmorPenetration'/>"/>
	<entry name="Artefacts/ArmorPenetrationDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Artefacts/Damage" value="<string name='Units/Neutral/Artefacts/Damage'/>"/>
	<entry name="Artefacts/DamageDescription" value="Augmente les dégâts"/>
	<entry name="Artefacts/Healing" value="<string name='Units/Neutral/Artefacts/Healing'/>"/>
	<entry name="Artefacts/HealingDescription" value="Restaure des points de vie tous les tours."/>
	<entry name="Artefacts/Hitpoints" value="<string name='Units/Neutral/Artefacts/Hitpoints'/>"/>
	<entry name="Artefacts/HitpointsDescription" value="Augmente les points de vie."/>
	<entry name="Artefacts/Loyalty" value="<string name='Units/Neutral/Artefacts/Loyalty'/>"/>
	<entry name="Artefacts/LoyaltyDescription" value="Augmente la loyauté."/>
	<entry name="Artefacts/Movement" value="<string name='Units/Neutral/Artefacts/Movement'/>"/>
	<entry name="Artefacts/MovementDescription" value="Augmente les points de mouvement."/>
	<entry name="Artefacts/Sight" value="<string name='Units/Neutral/Artefacts/Sight'/>"/>
	<entry name="Artefacts/SightDescription" value="Améliore la vision"/>
	
	<entry name="Items/AdamantiumWeaveVest" value="<string name='Items/AdamantiumWeaveVest'/>"/>
	<entry name="Items/AdamantiumWeaveVestDescription" value="<string name='Items/AdamantiumWeaveVestDescription'/>"/>
	<entry name="Items/AdamantiumWeaveVestFlavor" value="<string name='Items/AdamantiumWeaveVestFlavor'/>"/>
	<entry name="Items/ArmaplasBracers" value="<string name='Items/ArmaplasBracers'/>"/>
	<entry name="Items/ArmaplasBracersDescription" value="<string name='Items/ArmaplasBracersDescription'/>"/>
	<entry name="Items/ArmaplasBracersFlavor" value="<string name='Items/ArmaplasBracersFlavor'/>"/>
	<entry name="Items/AxeOfBlindFury" value="<string name='Items/AxeOfBlindFury'/>"/>
	<entry name="Items/AxeOfBlindFuryDescription" value="<string name='Items/AxeOfBlindFuryDescription'/>"/>
	<entry name="Items/AxeOfBlindFuryFlavor" value="<string name='Items/AxeOfBlindFuryFlavor'/>"/>
	<entry name="Items/CombatStimulant" value="<string name='Items/CombatStimulant'/>"/>
	<entry name="Items/CombatStimulantDescription" value="<string name='Items/CombatStimulantDescription'/>"/>
	<entry name="Items/CombatStimulantFlavor" value="<string name='Items/CombatStimulantFlavor'/>"/>
	<entry name="Items/ConcealedWeaponSystem" value="<string name='Items/ConcealedWeaponSystem'/>"/>
	<entry name="Items/ConcealedWeaponSystemDescription" value="<string name='Items/ConcealedWeaponSystemDescription'/>"/>
	<entry name="Items/ConcealedWeaponSystemFlavor" value="<string name='Items/ConcealedWeaponSystemFlavor'/>"/>
	<entry name="Items/DuskBlade" value="<string name='Items/DuskBlade'/>"/>
	<entry name="Items/DuskBladeDescription" value="<string name='Items/DuskBladeDescription'/>"/>
	<entry name="Items/DuskBladeFlavor" value="<string name='Items/DuskBladeFlavor'/>"/>
	<entry name="Items/EnduranceImplant" value="<string name='Items/EnduranceImplant'/>"/>
	<entry name="Items/EnduranceImplantDescription" value="<string name='Items/EnduranceImplantDescription'/>"/>
	<entry name="Items/EnduranceImplantFlavor" value="<string name='Items/EnduranceImplantFlavor'/>"/>
	<entry name="Items/EntropicLocum" value="<string name='Items/EntropicLocum'/>"/>
 	<entry name="Items/EntropicLocumDescription" value="<string name='Items/EntropicLocumDescription'/>"/>
 	<entry name="Items/EntropicLocumFlavor" value="<string name='Items/EntropicLocumFlavor'/>"/>
 	<entry name="Items/FaolchusWing" value="<string name='Items/FaolchusWing'/>"/>
 	<entry name="Items/FaolchusWingDescription" value="<string name='Items/FaolchusWingDescription'/>"/>
 	<entry name="Items/FaolchusWingFlavor" value="<string name='Items/FaolchusWingFlavor'/>"/>
	<entry name="Items/LightningGauntlet" value="<string name='Items/LightningGauntlet'/>"/>
	<entry name="Items/LightningGauntletDescription" value="<string name='Items/LightningGauntletDescription'/>"/>
	<entry name="Items/LightningGauntletFlavor" value="<string name='Items/LightningGauntletFlavor'/>"/>
	<entry name="Items/MourningBladeOfLazaerek" value="<string name='Items/MourningBladeOfLazaerek'/>"/>
	<entry name="Items/MourningBladeOfLazaerekDescription" value="<string name='Items/MourningBladeOfLazaerekDescription'/>"/>
	<entry name="Items/MourningBladeOfLazaerekFlavor" value="<string name='Items/MourningBladeOfLazaerekFlavor'/>"/>
	<entry name="Items/OmniScope" value="<string name='Items/OmniScope'/>"/>
	<entry name="Items/OmniScopeDescription" value="<string name='Items/OmniScopeDescription'/>"/>
	<entry name="Items/OmniScopeFlavor" value="<string name='Items/OmniScopeFlavor'/>"/>
	<entry name="Items/PoweredGauntlet" value="<string name='Items/PoweredGauntlet'/>"/>
	<entry name="Items/PoweredGauntletDescription" value="<string name='Items/PoweredGauntletDescription'/>"/>
	<entry name="Items/PoweredGauntletFlavor" value="<string name='Items/PoweredGauntletFlavor'/>"/>
	<entry name="Items/ScrollsOfMagnus" value="<string name='Items/ScrollsOfMagnus'/>"/>
	<entry name="Items/ScrollsOfMagnusDescription" value="<string name='Items/ScrollsOfMagnusDescription'/>"/>
	<entry name="Items/ScrollsOfMagnusFlavor" value="<string name='Items/ScrollsOfMagnusFlavor'/>"/>
    <entry name="Items/SightlessHelm" value="<string name='Items/SightlessHelm'/>"/>
 	<entry name="Items/SightlessHelmDescription" value="<string name='Items/SightlessHelmDescription'/>"/>
 	<entry name="Items/SightlessHelmFlavor" value="<string name='Items/SightlessHelmFlavor'/>"/>
	<entry name="Items/TantalisingIcon" value="<string name='Items/TantalisingIcon'/>"/>
	<entry name="Items/TantalisingIconDescription" value="<string name='Items/TantalisingIconDescription'/>"/>
	<entry name="Items/TantalisingIconFlavor" value="<string name='Items/TantalisingIconFlavor'/>"/>
	<entry name="Items/TemporaryShield" value="<string name='Items/TemporaryShield'/>"/>
	<entry name="Items/TemporaryShieldDescription" value="<string name='Items/TemporaryShieldDescription'/>"/>
	<entry name="Items/TemporaryShieldFlavor" value="<string name='Items/TemporaryShieldFlavor'/>"/>
	<entry name="Items/UltraWidebandAuspex" value="<string name='Items/UltraWidebandAuspex'/>"/>
	<entry name="Items/UltraWidebandAuspexDescription" value="<string name='Items/UltraWidebandAuspexDescription'/>"/>
	<entry name="Items/UltraWidebandAuspexFlavor" value="<string name='Items/UltraWidebandAuspexFlavor'/>"/>	
	<entry name="Items/VolcanisShroud" value="<string name='Items/VolcanisShroud'/>"/>
	<entry name="Items/VolcanisShroudDescription" value="<string name='Items/VolcanisShroudDescription'/>"/>
	<entry name="Items/VolcanisShroudFlavor" value="<string name='Items/VolcanisShroudFlavor'/>"/>
	<entry name="Items/ZoatHideJerkin" value="<string name='Items/ZoatHideJerkin'/>"/>
	<entry name="Items/ZoatHideJerkinDescription" value="<string name='Items/ZoatHideJerkinDescription'/>"/>
	<entry name="Items/ZoatHideJerkinFlavor" value="<string name='Items/ZoatHideJerkinFlavor'/>"/>

	
	<entry name="AdeptusMechanicus/AdjacencyIntegration" value="Intégration contiguë"/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationDescription" value="Augmente la production de recherche des bâtiments pour chaque bâtiment produisant de la recherche sur une case adjacente."/>
	<entry name="AdeptusMechanicus/AdjacencyIntegrationFlavor" value="La redécouverte de connaissance est un rite si sacré que des rituels de soutien ont été ordonnés à travers la ruche. Alors que la densité de population augmente, les fidèles versent constamment des libations et supplient les esprits de la machine intégrés à la ville de dévoiler leurs secrets aux technoprêtres dans le Librarium Omnis."/>
	<entry name="AdeptusMechanicus/AggressionOverride" value="<string name='Actions/AdeptusMechanicus/AggressionOverride'/>"/>
	<entry name="AdeptusMechanicus/AggressionOverrideDescription" value="Augmente les attaques."/>
	<entry name="AdeptusMechanicus/AggressionOverrideFlavor" value="<string name='Actions/AdeptusMechanicus/AggressionOverrideFlavor'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperative" value="<string name='Actions/AdeptusMechanicus/AggressorImperative'/>"/>
	<entry name="AdeptusMechanicus/AggressorImperativeDescription" value="Augmente le mouvement mais réduit l'armure."/>
	<entry name="AdeptusMechanicus/AggressorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/AggressorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonus" value="Dogma Metalica"/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusDescription" value="Augmente la pénétration d'armure."/>
	<entry name="AdeptusMechanicus/AssaultWeaponBonusFlavor" value="Les fanatiques de Metalica sont réputés pour apporter avec eux une clameur assourdissante, rappelant l'industrie incessante de leur monde forge—et pour ne pas s'arrêter dans leur avancée implacable, annihilant leurs ennemis tout en marchant."/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscience" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscience'/>"/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceDescription" value="Augmente la précision à distance."/>
	<entry name="AdeptusMechanicus/BenedictionOfOmniscienceFlavor" value="<string name='Actions/AdeptusMechanicus/BenedictionOfOmniscienceFlavor'/>"/>
	<entry name="AdeptusMechanicus/Bionics" value="Bioniques"/>
	<entry name="AdeptusMechanicus/BionicsDescription" value="Augmente la réduction de dégâts invulnérable."/>
	<entry name="AdeptusMechanicus/BionicsFlavor" value="Il est rare qu'un Magos de l'Adeptus Mechanicus soit complètement dépourvu d'augmentations cybernétiques, depuis l'époque d'Arkhan Land. Certains, plus vains, cachent leurs augmentations, mais la plupart des augmentations binoques sont volontairement grossières et inhumaines, agissant comme une manifestation physique de la bénédiction de l'Omnimessie."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTether" value="<string name='Actions/AdeptusMechanicus/BroadSpectrumDataTether'/>"/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherDescription" value="Réduit la perte de moral."/>
	<entry name="AdeptusMechanicus/BroadSpectrumDataTetherFlavor" value="<string name='Actions/AdeptusMechanicus/BroadSpectrumDataTetherFlavor'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperative" value="<string name='Actions/AdeptusMechanicus/BulwarkImperative'/>"/>
	<entry name="AdeptusMechanicus/BulwarkImperativeDescription" value="Augmente l'armure mais réduit le mouvement."/>
	<entry name="AdeptusMechanicus/BulwarkImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/BulwarkImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiah" value="Cantiques de l'Omnimessie"/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiahDescription" value="Classification."/>
	<entry name="AdeptusMechanicus/CanticlesOfTheOmnissiahFlavor" value="En temps de guerre, les disciples de l'Omnimessie entonne des bénédictions martiales complexes. Il y a autant de subroutines d'optimisation qu'il y a d'expressions de foi dans leur divinité omnisciente."/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFist" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFist'/>"/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistDescription" value="Augmente la précision au corps à corps."/>
	<entry name="AdeptusMechanicus/ChantOfTheRemorselessFistFlavor" value="<string name='Actions/AdeptusMechanicus/ChantOfTheRemorselessFistFlavor'/>"/>
	<entry name="AdeptusMechanicus/CityTier2" value="Mavoraformation"/>
	<entry name="AdeptusMechanicus/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="AdeptusMechanicus/CityTier2Flavor" value="La terraformation est le processus, au nom ironique, qui rend une planète plus vivable pour les humains, exactement à l'opposé de l'énorme superficie ravagée de la Terre, cachée sous le Palais Impérial à la taille continentale. La mavoraformation n'a pas de telles prétentions, se contentant de transformer la planète en une terre exploitée et empoisonnée recouverte de villes incroyablement productives, comme à la maison."/>
	<entry name="AdeptusMechanicus/CityTier3" value="Approbation de monde ruche"/>
	<entry name="AdeptusMechanicus/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="AdeptusMechanicus/CityTier3Flavor" value="La dernière conversion d'une planète en monde ruche est visible depuis l'espace. Les collines ondoyantes, les vallées, les arbres, tous sont partis. Les lacs, les mers, l'atmosphère respirable, disparus eux aussi. Un sable mordant souffle au-dessus de déserts de poussière où quelques impériaux récalcitrants survivent tant bien que mal avec des fermes de subsistance, dans l'ombre des flèches gigantesques de la ruche, remplie de milliards d'âmes."/>
	<entry name="AdeptusMechanicus/Cognis" value="Cognis"/>
	<entry name="AdeptusMechanicus/CognisDescription" value="Limite la perte de précision."/>
	<entry name="AdeptusMechanicus/CognisFlavor" value="<string name='Weapons/CognisFlavor'/>"/>
	<entry name="AdeptusMechanicus/CommandUplink" value="<string name='Actions/AdeptusMechanicus/CommandUplink'/>"/>
	<entry name="AdeptusMechanicus/CommandUplinkDescription" value="Réduit la perte de morale."/>
	<entry name="AdeptusMechanicus/CommandUplinkFlavor" value="<string name='Actions/AdeptusMechanicus/CommandUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperative" value="<string name='Actions/AdeptusMechanicus/ConquerorImperative'/>"/>
	<entry name="AdeptusMechanicus/ConquerorImperativeDescription" value="Augmente la précision au corps à corps mais réduit la précision à distance."/>
	<entry name="AdeptusMechanicus/ConquerorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ConquerorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ControlEdict" value="<string name='Actions/AdeptusMechanicus/ControlEdict'/>"/>
	<entry name="AdeptusMechanicus/ControlEdictDescription" value="Enlève les malus des impératifs doctrinaires."/>
	<entry name="AdeptusMechanicus/ControlEdictFlavor" value="<string name='Actions/AdeptusMechanicus/ControlEdictFlavor'/>"/>
	<entry name="AdeptusMechanicus/DartingHunters" value="Chasseurs plongeants"/>
	<entry name="AdeptusMechanicus/DartingHuntersDescription" value="Les actions ne consomment pas le mouvement."/>
	<entry name="AdeptusMechanicus/DartingHuntersFlavor" value="Les réflexes des Pteraxii sont accentués en réduisant les éléments de réflexion qui nuisent à leur fonction principale."/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermon" value="<string name='Actions/AdeptusMechanicus/DataBlessedAutosermon'/>"/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermonDescription" value="Accorde de la recherche lorsqu'un ennemi est tué."/>
	<entry name="AdeptusMechanicus/DataBlessedAutosermonFlavor" value="<string name='Actions/AdeptusMechanicus/DataBlessedAutosermonFlavor'/>"/>
	<entry name="AdeptusMechanicus/DoctrinaImperatives" value="Impératifs doctrinaires"/>
	<entry name="AdeptusMechanicus/DoctrinaImperativesDescription" value="Classification."/>
	<entry name="AdeptusMechanicus/DoctrinaImperativesFlavor" value="Les skitarii sont des ennemis terrifiants, implacables dans leur poursuite des objectifs de l'Omnimessie et équipés avec les armes les plus avancées de l'Imperium. Au final, cependant, chacun n'est qu'un réceptacle cybernétique pour la volonté des technoprêtres. Au cœur de la bataille, les skitariis seront pilotés à la distance par des impératifs qui renforceront leurs corps et leurs esprits à des niveaux inhumains.."/>
	<entry name="AdeptusMechanicus/Dunestrider" value="Marchedune"/>
	<entry name="AdeptusMechanicus/DunestriderDescription" value="Augmente le mouvement."/>
	<entry name="AdeptusMechanicus/DunestriderFlavor" value="Certains skitarii sont capables de traverser le terrain le plus hostile à un rythme implacable, leurs membres augmentés ne se fatiguant jamais."/>
	<entry name="AdeptusMechanicus/EmanatusForceField" value="Champ de force Emanatus"/>
	<entry name="AdeptusMechanicus/EmanatusForceFieldDescription" value="Augmente la réduction de dégâts invulnérable."/>
	<entry name="AdeptusMechanicus/EmanatusForceFieldFlavor" value="Les champs de force superposés générés par les onagres sont des merveilles de science militaire. Comme les champs réfracteurs communs aux ordres inférieur des prêtres martiens, ils dispersent les énergies hostiles dans l’atmosphère, chaque balle les visant étant transformée en rien de plus qu'un éclat de lumière bleue et une forte odeur d'ozone."/>
	<entry name="AdeptusMechanicus/EnhancedDataTether" value="Datacâble amélioré"/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherDescription" value="Augmente le moral."/>
	<entry name="AdeptusMechanicus/EnhancedDataTetherFlavor" value="Vus comme les porte-paroles des technoprêtres, qui sont les prophètes du dieu machine lui-même, ceux qui ont l'honneur de porter des data-câbles améliorés voient leurs ordre suivis sans hésitation et avec révérence par leurs camarades skitariis."/>
	<entry name="AdeptusMechanicus/EnrichedRounds" value="<string name='Actions/AdeptusMechanicus/EnrichedRounds'/>"/>
	<entry name="AdeptusMechanicus/EnrichedRoundsDescription" value="Augmente les dégâts."/>
	<entry name="AdeptusMechanicus/EnrichedRoundsFlavor" value="<string name='Actions/AdeptusMechanicus/EnrichedRoundsFlavor'/>"/>
	<entry name="AdeptusMechanicus/EnslavedToThePast" value="Enchaînés au passé"/>
	<entry name="AdeptusMechanicus/EnslavedToThePastDescription" value="Augmente le coût de la recherche."/>
	<entry name="AdeptusMechanicus/EnslavedToThePastFlavor" value="Au final, les citadelles de connaissance du culte sont construites sur une fondation de mensonges. La capacité de réellement innover a été perdue il y a longtemps, remplacée par une révérence pour les temps où l'humanité était l'architecte de son propre destin. Le culte Mechanicus n'est plus le maître de ses créations et est enchaîné au passé. Il maintient sa gloire d'antan avec des rites, des dogmes et des décrets au lieu de discernement et de compréhension. Même le procédé théoriquement simple d'activer une arme est précédé par l'application d'huiles rituels, du brûlage de résines sacrées et du chant d'hymnes longs et complexes. Et pourtant, tant que le processus fonctionne – ou plutôt, tant que les armées du culte peuvent annihiler ceux qui leur déplaisent – les technoprêtres se satisfont de marcher le chemin glissant vers l'entropie et l'ignorance."/>
	<entry name="AdeptusMechanicus/GuldiresOrison" value="Oraison de Guldire"/>
	<entry name="AdeptusMechanicus/GuldiresOrisonDescription" value="Réduit la précision."/>
	<entry name="AdeptusMechanicus/GuldiresOrisonFlavor" value="Guldie était un techmancien en chef des Word Bearers sous les ordres d'Erebus. Son ‘oraison’ est une prière de pure code machine corrompu, contenant des murmures démoniaques provenant du cœur du Warp. Bien que rendue non-létale par les défenses impériales, elle est quand même incroyablement déconcentrante…"/>
	<entry name="AdeptusMechanicus/FidorumVossPrime" value="Fidorum Voss Prime"/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeDescription" value="Augmente la production d'influence des bâtiments pour chaque bâtiment produisant de l'influence sur une case adjacente."/>
	<entry name="AdeptusMechanicus/FidorumVossPrimeFlavor" value="Le culte Mechanicus vénère le monde forge de Voss Prime pour sa loyauté avant tout—sa loyauté à l'Imperium n'est inférieure à celle de personne. Même quand Mars elle-même est tombée aux mains du Mechanicus Noir, Voss Prime a continué à produire des armes en masse et a été décisive pour la défaite de l'hérésie d'Horus."/>
	<entry name="AdeptusMechanicus/GalvanicField" value="<string name='Actions/AdeptusMechanicus/GalvanicField'/>"/>
	<entry name="AdeptusMechanicus/GalvanicFieldDescription" value="Augmente la pénétration d'armure."/>
	<entry name="AdeptusMechanicus/GalvanicFieldFlavor" value="<string name='Actions/AdeptusMechanicus/GalvanicFieldFlavor'/>"/>
	<entry name="AdeptusMechanicus/EntropicDestabilisation" value="Déstabilisation entropique"/>
	<entry name="AdeptusMechanicus/EntropicDestabilisationDescription" value="Accorde une réduction de dégâts au corps à corps et à distance."/>
	<entry name="AdeptusMechanicus/EntropicDestabilisationFlavor" value="Vous êtes renforcé par la bénédiction de l'Omnimessie! Rien ne peut se dresser sur votre chemin (est-ce un rêve? Avez-vous l'Omnimessie?!))"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonus" value="Scriptorum Ordinatus"/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusDescription" value="Augmente la pénétration d'armure."/>
	<entry name="AdeptusMechanicus/HeavyWeaponBonusFlavor" value="Les ordinati sont des engins uniques de la taille d'un titan, chacun construit pour un but unique et protégé de près. Vos recherches ont déterré des enseignements approuvés écrits lors de la création d'Ordinatus Oberon, un canon stellaire monté sur rails utilisés lors des guerres pour Armageddon."/>
	<entry name="AdeptusMechanicus/IncenseCloud" value="Nuage d'encens"/>
 	<entry name="AdeptusMechanicus/IncenseCloudDescription" value="Augmente la réduction des dégâts à distance."/>
 	<entry name="AdeptusMechanicus/IncenseCloudFlavor" value="Les secrets de la création par Aldebrac Vingh des moteurs à mouvement perpétuel qui propulsent les anciens Chevaucheurs de Fer ont été perdus, c'est pourquoi ils sont vénérés par les Cultistes de Mars. L'encens sacré qui les enveloppe est à la fois un symbole de leur vénération et une couverture efficace contre les tirs ennemis."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiah" value="Icônes de l'Omnimessie"/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahDescription" value="Augmente la production de loyauté des bâtiments pour chaque bâtiment produisant de la loyauté sur une case adjacente."/>
	<entry name="AdeptusMechanicus/IconsOfTheOmnissiahFlavor" value="Protégez-nous, maître. Libérez-nous, dieu machine, L'Omnimessie arrive. L'Omnimessie arrive. L'OMNIMESSIE ARRIVE. Châtiez la chair."/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoul" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoul'/>"/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulDescription" value="Réduit la perte de morale."/>
	<entry name="AdeptusMechanicus/IncantationOfTheIronSoulFlavor" value="<string name='Actions/AdeptusMechanicus/IncantationOfTheIronSoulFlavor'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMight" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMight'/>"/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightDescription" value="Augmente les dégâts au corps à corps."/>
	<entry name="AdeptusMechanicus/InvocationOfMachineMightFlavor" value="<string name='Actions/AdeptusMechanicus/InvocationOfMachineMightFlavor'/>"/>
	<entry name="AdeptusMechanicus/IonShield" value="Bouclier ionique"/>
	<entry name="AdeptusMechanicus/IonShieldDescription" value="Augmente la réduction de dégâts invulnérable."/>
	<entry name="AdeptusMechanicus/IonShieldFlavor" value="Les chevaliers transportent des générateurs de champ puissants appelés des boucliers ioniques. Ces engins utilisent une technologie antique pour projeter un champ d'énergie dans un angle étroit. En bougeant la position du bouclier pour qu'il intercepte les attaques ennemis, un chevalier est capable de survivre même aux tirs les plus puissants, tout en étant capable d'utiliser ses propres armes pour tirer en retour. La configuration et la position exactes du bouclier sont essentielles, car le bouclier ionique est uniquement conçu pour dévier et ralentir des tirs et pas les absorber comme les boucliers void utilisés sur les Titans. Ce qui veut dire que l'efficacité du bouclier est dépendante des compétences et de l'expérience de son opérateur."/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCult" value="<string name='Actions/AdeptusMechanicus/LordOfTheMachineCult'/>"/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCultDescription" value="Augmente la précision."/>
	<entry name="AdeptusMechanicus/LordOfTheMachineCultFlavor" value="<string name='Actions/AdeptusMechanicus/LordOfTheMachineCultFlavor'/>"/>
	<entry name="AdeptusMechanicus/LucianSpecialisation" value="Spécialisation du luciun"/>
	<entry name="AdeptusMechanicus/LucianSpecialisationDescription" value="Augmente la production de ressources des bâtiments non-QG identiques sur la même case."/>
	<entry name="AdeptusMechanicus/LucianSpecialisationFlavor" value="A l'intérieur du Monde forge creux de Lucius se trouve un soleil piégé, mais pour l'Adeptus Mechanicus c'est moins intéressant que les spécialisations de la planète — un métal unique le “Luciun” et le voyage dans le warp. Les Magi de Lucius encouragent la spécialisation à l'extrême. Et le voyage dans le warp."/>
	<entry name="AdeptusMechanicus/MechanicusLocum" value="<string name='Actions/AdeptusMechanicus/MechanicusLocum'/>"/>
	<entry name="AdeptusMechanicus/MechanicusLocumDescription" value="<string name='Actions/AdeptusMechanicus/MechanicusLocumDescription'/>"/>
	<entry name="AdeptusMechanicus/MechanicusLocumFlavor" value="<string name='Actions/AdeptusMechanicus/MechanicusLocumFlavor'/>"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonus" value="Sauvagerie Ryzienne"/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusDescription" value="Augmente la pénétration d'armure."/>
	<entry name="AdeptusMechanicus/MeleeWeaponBonusFlavor" value="Bien que le planète soit spécialisée dans le plasma et les engins défensifs, les invasions Orks sont si fréquentes sur le monde forge de Ryza, que ses défenseurs sont réputés pour leur enthousiasme Orkesque pour le combat au corps à corps."/>
	<entry name="AdeptusMechanicus/MonolithicBuildings" value="Constructions monolithiques"/>
	<entry name="AdeptusMechanicus/MonolithicBuildingsDescription" value="Augmente la production de ressources des bâtiments non-QG identiques sur la même case. Réduit la production de ressources des bâtiments non-QG différents sur la même case."/>
	<entry name="AdeptusMechanicus/MonolithicBuildingsFlavor" value="“Dans l'uniformité, la spécialisation. Dans la spécialisation, l'efficacité. Dans l'efficacité, l'extase.”<br/>—Kelphor Zhuko-Dim, Arco-flagellant"/>
	<entry name="AdeptusMechanicus/NeurostaticInterface" value="Interférence neurostatique"/>
	<entry name="AdeptusMechanicus/NeurostaticInterfaceDescription" value="Augmente la réduction de dégâts invulnérable lorsque l'unité est attaquée par des unités ennemies adjacentes."/>
	<entry name="AdeptusMechanicus/NeurostaticInterfaceFlavor" value="Parmi la cacophonie audio-visuelle accompagnée par l'attaque d'infiltrateurs sicariens se trouve des interférences électromagnétiques à large spectre, conçues pour handicaper les systèmes ennemis et déstabiliser leurs opposants."/>
	<entry name="AdeptusMechanicus/Omnispex" value="Omnispex"/>
	<entry name="AdeptusMechanicus/OmnispexDescription" value="Ignore une partie de la réduction de dégâts à distance des ennemis."/>
	<entry name="AdeptusMechanicus/OmnispexFlavor" value="L'omnispex comporte un esprit de la machine de classe raptor qui peut lire les émissions de chaleur, les signatures de donées et les ondes biologiques même à très longue distance. S'il est maintenu pendant une période de temps prolongé, il déterminera les points faibles de ceux qu'il observe et transmettra ces informations à son maître."/>
	<entry name="AdeptusMechanicus/OptateRestrictions" value="Restriction des optates"/>
	<entry name="AdeptusMechanicus/OptateRestrictionsDescription" value="Augmente la limite de population."/>
	<entry name="AdeptusMechanicus/OptateRestrictionsFlavor" value="Les optates qui contrôlent la population de la ville ruche ont accepté de réduire les restrictions sur la reproduction. En pratique, ça veut dire la suppression des anaphrodisiaques dans les rations alimentaires plutôt que la construction de nouveaux bâtiments—les lieux de vie existants sont juste davantage remplis…"/>
	<entry name="AdeptusMechanicus/PowerSurge" value="<string name='Actions/AdeptusMechanicus/PowerSurge'/>"/>
	<entry name="AdeptusMechanicus/PowerSurgeDescription" value="Augmente la production de ressource des bâtiments non-QG."/>
	<entry name="AdeptusMechanicus/PowerSurgeFlavor" value="<string name='Actions/AdeptusMechanicus/PowerSurgeFlavor'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperative" value="<string name='Actions/AdeptusMechanicus/ProtectorImperative'/>"/>
	<entry name="AdeptusMechanicus/ProtectorImperativeDescription" value="Augmente la précision à distance mais réduit la précision au corps à corps."/>
	<entry name="AdeptusMechanicus/ProtectorImperativeFlavor" value="<string name='Actions/AdeptusMechanicus/ProtectorImperativeFlavor'/>"/>
	<entry name="AdeptusMechanicus/RadPoisoning" value="Radiations"/>
	<entry name="AdeptusMechanicus/RadPoisoningDescription" value="Augmente les dégâts contre l'infanterie et les créatures monstrueuses."/>
	<entry name="AdeptusMechanicus/RadSaturation" value="<string name='Actions/AdeptusMechanicus/RadSaturation'/>"/>
	<entry name="AdeptusMechanicus/RadSaturationDescription" value="Inflige des dégâts à chaque tour."/>
	<entry name="AdeptusMechanicus/RadSaturationFlavor" value="<string name='Actions/AdeptusMechanicus/RadSaturationFlavor'/>"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocols" value="Protocoles des récupérateurs"/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsDescription" value="Augmente le rythme de croissance de la population."/>
	<entry name="AdeptusMechanicus/ReclaimatorProtocolsFlavor" value="Rien ne doit être gâché—pas une miette de nourriture, pas un watt d'énergie, pas un morceau de chair. Les exécutions sont réduites au minimum en faveur de la conversion en serviteurs, causant une augmentation de la vigueur dans toutes les autres activités…"/>
	<entry name="AdeptusMechanicus/ServoSkullUplink" value="<string name='Actions/AdeptusMechanicus/ServoSkullUplink'/>"/>
	<entry name="AdeptusMechanicus/ServoSkullUplinkDescription" value="Augmente les dégâts."/>
	<entry name="AdeptusMechanicus/ServoSkullUplinkFlavor" value="<string name='Actions/AdeptusMechanicus/ServoSkullUplinkFlavor'/>"/>
	<entry name="AdeptusMechanicus/Shroudpsalm" value="<string name='Actions/AdeptusMechanicus/Shroudpsalm'/>"/>
	<entry name="AdeptusMechanicus/ShroudpsalmDescription" value="Augment la réduction de dégâts à distance."/>
	<entry name="AdeptusMechanicus/ShroudpsalmFlavor" value="<string name='Actions/AdeptusMechanicus/ShroudpsalmFlavor'/>"/>
	<entry name="AdeptusMechanicus/SiphonVigour" value="Drain de vigueur"/>
	<entry name="AdeptusMechanicus/SiphonVigourDescription" value="Tuer une unité ennemie augmente la réduction de dégâts invulnérable."/>
	<entry name="AdeptusMechanicus/SiphonVigourFlavor" value="<string name='Traits/AdeptusMechanicus/SiphonedVigourFlavor'/>"/>
	<entry name="AdeptusMechanicus/SiphonedVigour" value="Vigueur drainée"/>
	<entry name="AdeptusMechanicus/SiphonedVigourDescription" value="Augmente la réduction de dégâts invulnérable."/>
	<entry name="AdeptusMechanicus/SiphonedVigourFlavor" value="Ils sont peut-être obsédés du gâchis d'énergie, mais les électro-prêtres ne sont pas des hypocrites. Quand un ennemi meurt d'un coup de leur bâton électroclaste, le champ Voltagheist protecteur est renforcé avec de l'énergie bio-électrique, repoussant même les attaques les plus puissantes."/>
	<entry name="AdeptusMechanicus/SolarReflectors" value="Réflecteurs solaires"/>
	<entry name="AdeptusMechanicus/SolarReflectorsDescription" value="Augmente la production d'énergie des bâtiments pour chaque bâtiment produisant de l'énergie sur une case adjacente."/>
	<entry name="AdeptusMechanicus/SolarReflectorsFlavor" value="Planification, planification, planification. Les magi de l'Adeptus Mechanicus conçoivent chaque ville pour qu'elle fonctionne en tant qu'ensemble, chaque partie soutenant la suivante. Des réflecteurs solaires sur toutes les structures dirigent la petite quantité d'énergie qu'ils capturent à travers les tempêtes de Gladius Prime jusqu'à l'autel d'échange thermique le plus proche."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeams" value="Équipes d'acquisition de soylens"/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsDescription" value="Augmente la production de nourriture des bâtiments pour chaque bâtiment produisant de la nourriture sur une case adjacente."/>
	<entry name="AdeptusMechanicus/SoylensAcquisitionTeamsFlavor" value="Les récupérateurs emploient quelques membres de la population de la ville pour remplir un rôle peu agréable. Ces équipes d'acquisition de soylens sont plus connus comme voleurs de corps, et ils chassent agressivement les morts (ou presque morts) pour s'assurer que les rations futures ont toujours les protéines de soylens les plus fraîches…"/>
	<entry name="AdeptusMechanicus/StygianEnlightenment" value="Illumination stygienne"/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentDescription" value="Réduit le coût des recherches."/>
	<entry name="AdeptusMechanicus/StygianEnlightenmentFlavor" value="Certains techno-prêtres sont plus ouverts à l'étude de la technologie xenos que d'autres. Seule le monde forge de Stygies VIII reste impuni pour ces tendances xenarites, en partie à cause de l'importance de leur monde, en partie parce que ça estropie leurs agresseurs. Suivre leur voie est certainement un moyen rapide d'accélérer l'accès à des technologies interdites."/>
	<entry name="AdeptusMechanicus/TerranGeneralism" value="Généralisme terrien"/>
	<entry name="AdeptusMechanicus/TerranGeneralismDescription" value="Augmente la production générée par les bâtiments pour chaque bâtiment générant de la production sur une case adjacente."/>
	<entry name="AdeptusMechanicus/TerranGeneralismFlavor" value="Bien que de nombreux monde forges se sont spécialisés vers des besoins particuliers et agissent comme des empires indépendant dans la sphère humaine, il est bon de se rappeler que la plupart des technoprêtres sont des généralistes, soutenant loyalement l'Imperium dans son expansion stellaire—et qu'ils suivent les ordres de la Terre autant que de Mars."/>
	<entry name="AdeptusMechanicus/Transonic" value="Transsonique"/>
 	<entry name="AdeptusMechanicus/TransonicDescription" value="Augmente les dégâts et augmente temporairement la pénétration d'armure après l'attaque."/>
 	<entry name="AdeptusMechanicus/TransonicFlavor" value="<string name='Weapons/TransonicBladeFlavor'/>"/>
 	<entry name="AdeptusMechanicus/TransonicEffect" value="Résonance transsonique"/>
 	<entry name="AdeptusMechanicus/TransonicEffectDescription" value="Augmente la pénétration de l'armure."/>
 	<entry name="AdeptusMechanicus/TransonicEffectFlavor" value="<string name='Weapons/TransonicBladeFlavor'/>"/>
	<entry name="AdeptusMechanicus/TriplexNecessity" value="Nécessité de Triplex"/>
	<entry name="AdeptusMechanicus/TriplexNecessityDescription" value="Augmente la production de minerai des bâtiments pour chaque bâtiment produisant du minerai sur une case adjacente."/>
	<entry name="AdeptusMechanicus/TriplexNecessityFlavor" value="En suivant les nouveaux schémas découverts sur Triplex Phall, lors de la construction de nouvelles structures, des précautions sont prises pour explorer les zones souterraines et cartographier des veines de minerai potentielles. Avec un réacteur haemotrope, son plasma sera dirigé vers ces routes pré-cartographiées pour extraire plus efficacement le minerai."/>
	<entry name="AdeptusMechanicus/VoltagheistField" value="Champ Voltagheist"/>
	<entry name="AdeptusMechanicus/VoltagheistFieldDescription" value="Augmente la réduction de dégâts invulnérable."/>
	<entry name="AdeptusMechanicus/VoltagheistFieldFlavor" value="Des nuages d'énergie pure entourent tous les électro-prêtres, crépitant depuis leur peau pour s'assembler en des tas électromagnétiques brillants qui flottent comme des feux follet au-dessus d'un corps noyé. Quand des projectiles ou des rayons d'énergie menacent un électro-prêtre, ces minuscules fantômes voltaïques s'interposeront fréquemment, brisant ou dissipant les menaces en une bouffée d'ozone brûlant. Quand le porteur de ce champ charge l'ennemi, ces mêmes voltagheists se mettent à la terre sur les ennemis proches dans des explosions de force électrique."/>
	<entry name="AdeptusMechanicus/VoicesInTheCode" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCode'/>"/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeDescription" value="Augmente la perte de moral."/>
	<entry name="AdeptusMechanicus/VoicesInTheCodeFlavor" value="<string name='Actions/AdeptusMechanicus/VoicesInTheCodeFlavor'/>"/>
	<entry name="AdeptusMechanicus/WrathOfMars" value="<string name='Actions/AdeptusMechanicus/WrathOfMars'/>"/>
	<entry name="AdeptusMechanicus/WrathOfMarsDescription" value="Augmente les dégâts."/>
	<entry name="AdeptusMechanicus/WrathOfMarsFlavor" value="<string name='Actions/AdeptusMechanicus/WrathOfMarsFlavor'/>"/>
	
	<entry name="AerialAttack" value="Attaque aérienne"/>
	<entry name="AerialAttackDescription" value="Ne peut viser que les unités volantes."/>
	<entry name="Agile" value="<string name='Actions/Agile'/>"/>
	<entry name="AgileDescription" value="<string name='Actions/AgileDescription'/>"/>
	<entry name="AmmoRunt" value="Grot bastos"/>
	<entry name="AmmoRuntDescription" value="Augment la précision à distance."/>
	<entry name="AmmoRuntFlavor" value="<string name='Actions/AmmoRuntFlavor'/>"/>
	<entry name="Amphibious" value="Amphibie"/>
	<entry name="AmphibiousDescription" value="Cette unité peut bouger dans l'eau et ignore les pénalités des rivières."/>
	<entry name="AmphibiousFlavor" value="Peu de véhicules militaire standards du 41° millénaire sont désignés pour être utilisés lors d'assaut amphibie—réduire le poids en éliminant des munitions et du blindage est souvent vu comme exagéré. Cependant, la Chimère Impériale a été conçue il y a de nombreux millénaires pour être le char léger le plus flexible et est tout à fait capables de traverser des rivières."/>
	<entry name="AndTheyShallKnowNoFear" value="Et ils ne connaîtront pas la peur"/>
	<entry name="AndTheyShallKnowNoFearDescription" value="Réduit les pertes de moral et immunise à la peur."/>
	<entry name="AndTheyShallKnowNoFearFlavor" value="Certains guerriers refusent de se rendre, se battant quelque soient leurs chances."/>
	<entry name="Animosity" value="Animosité"/>
	<entry name="AnimosityDescription" value="Réduit les attaques."/>
	<entry name="AnimosityFlavor" value="Quand les courants de Waaagh! affluent, les Orks sont visiblement plus gros et plus forts—mais quand ces courants refluent, les Orks sont aussi affaiblis."/>
	<entry name="AntiGravUpwash" value="Poussée antigrav"/>
	<entry name="AntiGravUpwashDescription" value="Augmente le mouvement quand les points de vie sont au-dessus de 66%."/>
	<entry name="ApocalypticBarrage" value="Barrage apocalyptique"/>
	<entry name="ApocalypticBarrageDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticBarrageFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ApocalypticBlast" value="Explosion apocalyptique"/>
	<entry name="ApocalypticBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ApocalypticMegaBlast" value="Mega-explosion apocalyptique"/>
	<entry name="ApocalypticMegaBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="ApocalypticMegaBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="ArmoriumCherub" value="<string name='Actions/ArmoriumCherub'/>"/>
	<entry name="ArmoriumCherubDescription" value="Augmente la précision"/>
	<entry name="ArmoriumCherubFlavor" value="<string name='Actions/ArmoriumCherubFlavor'/>"/>
	<entry name="Armourbane" value="Fléau des blindages"/>
	<entry name="ArmourbaneDescription" value="Augmente la pénétration d'armure"/>
	<entry name="ArmourbaneFlavor" value="Cette arme a été créée avec un seul but en tête : percer le revêtement des véhicules blindés."/>
	<entry name="Artefact" value="Artefact"/>
	<entry name="ArtefactDescription" value="Classification."/>
	<entry name="ArtefactFlavor" value="Au cours des millénaires, quelque chose dans les structures Anciennes parsemant Gladius a attiré de nombreuses races sur cette planète. Elles ont apporté avec elles des objets aux pouvoirs extraordinaires, ce qui signifie que la surface est recouverte de technologies extra-terrestres de tous types et de toutes tailles."/>
	<entry name="Assault" value="Assaut"/>
	<entry name="AssaultDescription" value="Classification."/>
	<entry name="AssaultDoctrine" value="Doctrine d'assaut"/>
	<entry name="AssaultDoctrineDescription" value="Augmente la précision."/>
	<entry name="AssaultDoctrineFlavor" value="<string name='Actions/AssaultDoctrineFlavor'/>"/>
	<entry name="AssaultVehicle" value="Véhicule d'assaut"/>
	<entry name="AssaultVehicleDescription" value="Désembarquer ne consomme pas le mouvement."/>
	<entry name="AssaultVehicleFlavor" value="Ce véhicule est spécifiquement conçu pour déverser des troupes au cœur de la mêlée."/>
	
	<entry name="AstraMilitarum/BlastDamage" value="Enveloppe à shrapnel améliorée"/>
	<entry name="AstraMilitarum/BlastDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="AstraMilitarum/BlastDamageFlavor" value="En utilisant un protocole de gravure plus avancé sur l'enveloppe, la léthalité des explosifs est nettement améliorée."/>
	<entry name="AstraMilitarum/BoltDamage" value="Bolts Kraken"/>
	<entry name="AstraMilitarum/BoltDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="AstraMilitarum/BoltDamageFlavor" value="Une amélioration du bolter standard venant des Astartes, ce kit permet au bolter de tirer des cartouches pénétrantes Kraken, des projectiles à l'extrémité plus dure et avec une charge explosive plus importante. Ces cartouches plus lourdes sont très appréciées par les Space Marines chasseurs de xenos de la Deathwatch."/>
	<entry name="AstraMilitarum/CityTier2" value="Expansion de l'infrastructure"/>
	<entry name="AstraMilitarum/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="AstraMilitarum/CityTier2Flavor" value="Un jour, ce sera une Ville-Ruche—un milliard de personnes dans une seule structure, dominant un monde empoisonné—mais d'abord, cette colonie doit s'étendre un peu, pour placer les fondations d'une des plus grandes agglomérations de la galaxie."/>
	<entry name="AstraMilitarum/CityTier3" value="Tunnels de ravitaillement pour l’infrastructure"/>
	<entry name="AstraMilitarum/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="AstraMilitarum/CityTier3Flavor" value="Le Departmento Munitorum est peut-être inefficace, mais il peut prévoir et les Technoprêtres Explorateurs ainsi que les serviteurs peuvent construire. Ces tunnels souterrains permettent à un commandant de l'Astra Militarum de superviser une zone urbaine bien plus grande, sans perdre le contrôle des défenses."/>
	<entry name="AstraMilitarum/ShootSharpAndScarper" value="On tire et on décampe"/>
	<entry name="AstraMilitarum/ShootSharpAndScarperDescription" value="Les actions ne consomment pas le mouvement."/>
	<entry name="AstraMilitarum/ShootSharpAndScarperFlavor" value="Ce dont les Ratlings manquent au niveau militaire, c'est-à-dire tout, ils le compensent en sachant quand tirer et quand fuir."/>
	<entry name="AstraMilitarumAircraftProductionEdict" value="<string name='Actions/AstraMilitarumAircraftProductionEdict'/>"/>
	<entry name="AstraMilitarumAircraftProductionEdictDescription" value="Augmente la production fournie."/>
	<entry name="AstraMilitarumAircraftProductionEdictFlavor" value="<string name='Actions/AstraMilitarumAircraftProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumDefenseEdict" value="<string name='Actions/AstraMilitarumDefenseEdict'/>"/>
	<entry name="AstraMilitarumDefenseEdictDescription" value="Augmente l'armure."/>
	<entry name="AstraMilitarumDefenseEdictFlavor" value="<string name='Actions/AstraMilitarumDefenseEdictFlavor'/>"/>
	<entry name="AstraMilitarumEnergyEdict" value="<string name='Actions/AstraMilitarumEnergyEdict'/>"/>
	<entry name="AstraMilitarumEnergyEdictDescription" value="Augmente la production d'énergie."/>
	<entry name="AstraMilitarumEnergyEdictFlavor" value="<string name='Actions/AstraMilitarumEnergyEdictFlavor'/>"/>
	<entry name="AstraMilitarumFoodEdict" value="<string name='Actions/AstraMilitarumFoodEdict'/>"/>
	<entry name="AstraMilitarumFoodEdictDescription" value="Augmente la production de nourriture."/>
	<entry name="AstraMilitarumFoodEdictFlavor" value="<string name='Actions/AstraMilitarumFoodEdictFlavor'/>"/>
	<entry name="AstraMilitarumGrowthEdict" value="<string name='Actions/AstraMilitarumGrowthEdict'/>"/>
	<entry name="AstraMilitarumGrowthEdictDescription" value="Augmente le rythme de croissance de la population."/>
	<entry name="AstraMilitarumGrowthEdictFlavor" value="<string name='Actions/AstraMilitarumGrowthEdictFlavor'/>"/>
	<entry name="AstraMilitarumInfantryProductionEdict" value="<string name='Actions/AstraMilitarumInfantryProductionEdict'/>"/>
	<entry name="AstraMilitarumInfantryProductionEdictDescription" value="Augmente la production fournie."/>
	<entry name="AstraMilitarumInfantryProductionEdictFlavor" value="<string name='Actions/AstraMilitarumInfantryProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumInfluenceEdict" value="<string name='Actions/AstraMilitarumInfluenceEdict'/>"/>
	<entry name="AstraMilitarumInfluenceEdictDescription" value="Augmente la production d'influence"/>
	<entry name="AstraMilitarumInfluenceEdictFlavor" value="<string name='Actions/AstraMilitarumInfluenceEdictFlavor'/>"/>
	<entry name="AstraMilitarum/LasDamage" value="Batteries Hotshot"/>
	<entry name="AstraMilitarum/LasDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="AstraMilitarum/LasDamageFlavor" value="Ces batteries modulaires supérieures convertissent un fusil laser standard en 'fusil radiant', avec une portée et une puissance améliorée, mais avec des batteries à capacité réduite et une fiabilité moindre, demandant une maintenance constante. Même les soldats qui ont déjà des fusils radiants bénéficient d'un approvisionnement plus important en batteries Hotshot."/>
	<entry name="AstraMilitarumLoyaltyEdict" value="<string name='Actions/AstraMilitarumLoyaltyEdict'/>"/>
	<entry name="AstraMilitarumLoyaltyEdictDescription" value="Augmente la production de loyauté."/>
	<entry name="AstraMilitarumLoyaltyEdictFlavor" value="<string name='Actions/AstraMilitarumLoyaltyEdictFlavor'/>"/>
	<entry name="AstraMilitarumOreEdict" value="<string name='Actions/AstraMilitarumOreEdict'/>"/>
	<entry name="AstraMilitarumOreEdictDescription" value="Augmente la production de minerai."/>
	<entry name="AstraMilitarumOreEdictFlavor" value="<string name='Actions/AstraMilitarumOreEdictFlavor'/>"/>
	<entry name="AstraMilitarumPsykerProductionEdict" value="<string name='Actions/AstraMilitarumPsykerProductionEdict'/>"/>
	<entry name="AstraMilitarumPsykerProductionEdictDescription" value="Augmente la production fournie."/>
	<entry name="AstraMilitarumPsykerProductionEdictFlavor" value="<string name='Actions/AstraMilitarumPsykerProductionEdictFlavor'/>"/>
	<entry name="AstraMilitarumResearchEdict" value="<string name='Actions/AstraMilitarumResearchEdict'/>"/>
	<entry name="AstraMilitarumResearchEdictDescription" value="Augmente la production de recherche."/>
	<entry name="AstraMilitarumResearchEdictFlavor" value="<string name='Actions/AstraMilitarumResearchEdictFlavor'/>"/>
	<entry name="AstraMilitarumVehicleProductionEdict" value="<string name='Actions/AstraMilitarumVehicleProductionEdict'/>"/>
	<entry name="AstraMilitarumVehicleProductionEdictDescription" value="Augmente la production fournie."/>
	<entry name="AstraMilitarumVehicleProductionEdictFlavor" value="<string name='Actions/AstraMilitarumVehicleProductionEdictFlavor'/>"/>
 	<entry name="AversionToLight" value="Aversion à la lumière"/>
 	<entry name="AversionToLightDescription" value="Augmente les dégâts subis par les lance-flammes et les armes à fusion."/>
 	<entry name="AversionToLightFlavor" value="Les horreurs des fragments Umbra se révèlent la nuit, dans les coins sombres de Gladius. Mais apportez de la lumière près d'elle, et elles rétrécissent, les cauchemars s'enfuyant. Et apportez un lance-flamme…"/>
	<entry name="Barrage" value="Barrage"/>
	<entry name="BarrageDescription" value="Ne requiert pas de ligne de vue, mais ne peut pas exécuter de tirs en état d'alerte."/>
	<entry name="BarrageFlavor" value="<string name='Weapons/BarrageFlavor'/>"/>
	<entry name="Beam" value="Arme à faisceau"/>
	<entry name="BeamDescription" value="A une précision fixe, et blesse plusieurs membres de l'unité ciblée."/>
	<entry name="Bike" value="Moto"/>
	<entry name="BikeDescription" value="Classification."/>
	<entry name="BikeFlavor" value="Les soldats à moto excellent lors des attaques d'avant-garde. Ils sont capables d'utiliser leur grande vitesse pour frapper loin dans le territoire ennemi, compléter leur mission et s'échapper avant que l'ennemi ne puisse réagir. Ces guerriers sont souvent considérés comme des casse-cous et des têtes brûlées, mais leur efficacité est indéniable."/>
 	<entry name="Bladestorm" value="Tempête de lames"/>
 	<entry name="BladestormDescription" value="Augmente les dégâts et la pénétration d'armure."/>
	<entry name="Blast" value="Explosion"/>
	<entry name="BlastDescription" value="Blesse plusieurs membres de l'unité ciblée."/>
	<entry name="BlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="Blighted" value="Empoisonné"/>
	<entry name="BlightedDescription" value="Inflige des dégâts tous les tours."/>
	<entry name="Blind" value="Aveuglé"/>
	<entry name="BlindDescription" value="Réduit la précision."/>
	<entry name="BlindFlavor" value="Cette attaque provoque un éclat lumineux très brillant, brûlant la vision des victimes et les forçant à se battre aveuglé pendant quelques moments."/>
	<entry name="Blinding" value="Aveuglant"/>
	<entry name="BlindingDescription" value="Réduit la précision de l'infanterie ou de la créature monstrueuse ciblée."/>
	<entry name="BloodBlessing" value="Bénédiction sanguine"/>
	<entry name="BloodBlessingDescription" value="Augmente les attaques."/>
	<entry name="BloodBlessingFlavor" value="<string name='Features/SkullAltarFlavor'/>"/>
	<entry name="BolsterDefences" value="<string name='Actions/BolsterDefences'/>"/>
	<entry name="BolsterDefencesDescription" value="Augmente la réduction des dégâts à distance."/>
	<entry name="BolsterDefencesFlavor" value="<string name='Actions/BolsterDefencesFlavor'/>"/>
	<entry name="BoltWeapon" value="Arme à bolts"/>
	<entry name="BoltWeaponFlavor" value="<string name='Weapons/BoltFlavor'/>"/>
	<entry name="BolterDrill" value="Entraînement aux bolters"/>
	<entry name="BolterDrillDescription" value="Augmente la précision."/>
	<entry name="BolterDrillFlavor" value="<string name='Actions/BolterDrillFlavor'/>"/>
	<entry name="Bomb" value="Bombe"/>
	<entry name="BombDescription" value="Précision fixe."/>
	<entry name="Bosspole" value="Ikon' de Boss"/>
	<entry name="BosspoleDescription" value="Réduit la perte de moral selon le nombre d'alliés présents aux alentours."/>
	<entry name="BosspoleFlavor" value="Les Nobz arborent souvent leurs trophées sur un totem pour montrer qu'on ne leur cherche pas des noises impunément. Cette Ikon' sert aussi de trique pour ramener un semblant d'ordre dans le feu de l'action."/>
	<entry name="BringItDown" value="“Abattez-le!”"/>
	<entry name="BringItDownDescription" value="Augmente la pénétration d'armure"/>
	<entry name="Broken" value="Brisé"/>
	<entry name="BrokenDescription" value="Réduit la précision et augmente les dégâts subis."/>
	<entry name="BruteShield" value="Bouclier de brute"/>
	<entry name="BruteShieldDescription" value="Augmente les dégâts la réduction de dégâts invulnérable."/>
	<entry name="BruteShieldFlavor" value="Ces boucliers ressemblent à de grands boucles énergétiques et résistants. Ils sont portés par certains Bullgryns qui s'en serven combat aussi bien comme mesure défensive que comme matraque de secours."/>
	<entry name="Bulky" value="Encombrant"/>
 	<entry name="BulkyDescription" value="Nécessite un emplacement de chargement supplémentaire dans un transport."/>
 	<entry name="BulkyFlavor" value="Cette créature est si massive qu'elle occupe un espace démesuré dans tout véhicule ou bâtiment où elle pénètre."/>
	<entry name="CamoNetting" value="Filet de camouflage"/>
	<entry name="CamoNettingDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="CamoNettingFlavor" value="Qu'ils s'agissent de filets réalisés avec la rarissime caméléoline ou des sangles grossières tissées avec la flore locale, les filets de camouflages aident à cacher un véhicule des regards indiscrets."/>
	<entry name="Capturable" value="Capturable"/>
	<entry name="CapturableDescription" value="L'unité peut être capturée par une autre unité adjacente."/>
	<entry name="CeramitePlating" value="Blindage en céramite"/>
	<entry name="CeramitePlatingDescription" value="Augmente l'armure."/>
	<entry name="CeramitePlatingFlavor" value="Ces coques sont triplement bénies par les Techmarines du chapitre et sont ointes avec les sept onguents sacrés anti-thermiques pour protéger contre les conditions extrêmement de la rentrée en orbite. De telles précautions servent aussi à contrecarrer la furie de certaines armes, absorbant et dispersant même les températures et émissions micro-ondes les plus extrêmes.."/>
	<entry name="Chaff" value="Paillettes"/>
	<entry name="ChaffDescription" value="Augmente la réduction de dégâts à distance"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculum" value="Occulum ésotérique"/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumDescription" value="Bienfait du Chaos qui augmente la précision."/>
	<entry name="ChaosSpaceMarines/ArcaneOcculumFlavor" value="Un œil injecté de sang se fraye un chemin à travers la chair."/>
	<entry name="ChaosSpaceMarines/AuraOfDarkGlory" value="Aura de gloire sombre"/>
 	<entry name="ChaosSpaceMarines/AuraOfDarkGloryDescription" value="Augmente la réduction des dégâts de l'invulnérabilité."/>
 	<entry name="ChaosSpaceMarines/AuraOfDarkGloryFlavor" value="Certains champions du Chaos sont tellement bénis par leur divinité protectrice qu'ils bénéficient d'une protection surnaturelle contre tout danger. Ils peuvent être entourés d'une puissante bulle d'énergie psychique crépitante, ou bien les balles qui leur sont destinées peuvent être étrangement déviées juste avant de les atteindre. Dans tous les cas, il est clair que les Dieux Noirs veillent sur eux."/>
	<entry name="ChaosSpaceMarines/BlastDamage" value="Enveloppes démoniaques"/>
	<entry name="ChaosSpaceMarines/BlastDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="ChaosSpaceMarines/BlastDamageFlavor" value="Les Techmanciens n'ont peut-être piégé que des horreurs mineurs dans les enveloppes de ces obus, mais leur rage au point d'impact n'est pas mineur—pendant qu'elles se brisent et que les démons retournent dans le warp, ils se déchaînent et emportent tout ce qui est à portée."/>
	<entry name="ChaosSpaceMarines/Bloated" value="Boursouflé"/>
	<entry name="ChaosSpaceMarines/BloatedDescription" value="Restaure des points de vie."/>
	<entry name="ChaosSpaceMarines/BloatedFlavor" value="Bien que les maladies maudites de Nurgle rongent les humains aussi bien physiquement que spirituellement, elles accordent aussi à leurs victimes une chair pourrissante à la résistance surhumaine. Pour ces formes gonflées, répandre leurs germes peut souvent leur apporter des bénédictions mineurs du Dieu de la peste."/>
	<entry name="ChaosSpaceMarines/BloodForTheBloodGod" value="Du sang pour le Dieu du sang"/>
	<entry name="ChaosSpaceMarines/BloodForTheBloodGodDescription" value="Augmente les attaques"/>
	<entry name="ChaosSpaceMarines/BloodRage" value="Rage sanguinaire"/>
	<entry name="ChaosSpaceMarines/BloodRageDescription" value="Augmente les attaques au corps à corps et le mouvement."/>
	<entry name="ChaosSpaceMarines/BloodRageFlavor" value="Le Métabrutus se jette sur les ennemis proches de lui, lançant sa prison maudite en forme de Dreadnought au-delà des anciennes limites de sécurité, agissant de manière complètement démoniaque."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/BoltDamage" value="Bolts à propulsion Warp"/>
	<entry name="ChaosSpaceMarines/BoltDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="ChaosSpaceMarines/BoltDamageFlavor" value="Réunissant les bolts Inferno au prométhéum de l'Imperium et les bolts Inferno psychiques de la Légion traîtresse des Thousand Sons, les bolts à propulsion Warp passent à travers le Warp pour déchaîner un feu chimique surchauffé sur leurs cibles."/>
	<entry name="ChaosSpaceMarines/CityTier2" value="L'appel des Dieux Sombres"/>
	<entry name="ChaosSpaceMarines/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="ChaosSpaceMarines/CityTier2Flavor" value="Les peuples de l'Imperium écouteront tous les mensonges, croiront tous les contes, qui leur disent qu'ils ne sont pas condamnés. Cette voie, malheureusement, mène à la perdition et les portes du Chaos sont grandes ouvertes pour les égarés et les damnés—du moins pour entrer."/>
	<entry name="ChaosSpaceMarines/CityTier3" value="Nexus du destin"/>
	<entry name="ChaosSpaceMarines/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="ChaosSpaceMarines/CityTier3Flavor" value="Les adorateurs du Chaos ont arrêté de faire semblant. Là où ils attiraient les âmes en utilisant leurs faiblesses ou la séduction démoniaque, ils le font désormais par les fouets et les chaînes. Des groupes d'esclavagistes et des bandes de guerre chassent dans les zones non-cartographiées de la ville pour des âmes à sacrifier. Seuls les plus forts peuvent échapper à leur atteinte; les faibles sont du grain pour le moulin du Chaos."/>
	<entry name="ChaosSpaceMarines/Crazed" value="Fou furieux"/>
	<entry name="ChaosSpaceMarines/CrazedDescription" value="Si l'unité a été endommagée lors du tour précédent, elle gagne l'un des traits suivant aléatoirement pour un tour : Frénésie de tirs, Furie élevante, Rage sanguinaire."/>
	<entry name="ChaosSpaceMarines/CrazedFlavor" value="Tous les Metabrutus sont des monstruosités psychotiques et dangereuses—des vétérans de la Longue Guerre rendus fous par leur enfermement combiné à des tortures. Attaquer un Metabrutus peut donc sembler rationnel, mais si vous échouez à le détruire immédiatement, vous ne ferez que le rendre plus fou et en colère."/> <!-- Hellbrute. -->
	<entry name="ChaosSpaceMarines/ChampionOfChaos" value="Champion du Chaos"/>
	<entry name="ChaosSpaceMarines/ChampionOfChaosDescription" value="Tuer une unité ennemi peut accorder à cette unité un Bienfait du chaos débloqué. Les non-héros peuvent aussi être transformés en Enfant du Chaos ou en Prince Démon."/>
	<entry name="ChaosSpaceMarines/ChampionOfChaosFlavor" value="Ce n'est pas inhabituel pour les puissances du Chaos d'accorder d'étranges bienfaits et mutations à ceux qui tuent en leurs noms. Tous ces bienfaits ne sont pas bénéfiques—les puissances sombres sont aussi changeantes qu'inscrutables, et même leurs plus grands dévôts ne sont qu'à peine plus que des pions dans un jeu céleste."/>
	<entry name="ChaosSpaceMarines/CrystallineBody" value="Corps crystallin"/>
	<entry name="ChaosSpaceMarines/CrystallineBodyDescription" value="Bienfait du Chaos qui augmente les points de vie."/>
	<entry name="ChaosSpaceMarines/CrystallineBodyFlavor" value="La chair du champion se transforme en diamant."/>
	<entry name="ChaosSpaceMarines/CultistSacrifice" value="<string name='Actions/ChaosSpaceMarines/CultistSacrifice'/>"/>
	<entry name="ChaosSpaceMarines/CultistSacrificeDescription" value="Augmente la vitesse de croissance de la population."/>
	<entry name="ChaosSpaceMarines/CultistSacrificeFlavor" value="<string name='Actions/ChaosSpaceMarines/CultistSacrificeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Daemonforge" value="<string name='Actions/ChaosSpaceMarines/Daemonforge'/>"/>
	<entry name="ChaosSpaceMarines/DaemonforgeDescription" value="Augmente les dégâts et la pénétration d'armure."/>
	<entry name="ChaosSpaceMarines/DaemonforgeFlavor" value="<string name='Actions/ChaosSpaceMarines/DaemonforgeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DarkGlory" value="<string name='Actions/ChaosSpaceMarines/DarkGlory'/>"/>
	<entry name="ChaosSpaceMarines/DarkGloryDescription" value="<string name='Actions/ChaosSpaceMarines/DarkGloryDescription'/>"/>
	<entry name="ChaosSpaceMarines/DarkGloryFlavor" value="<string name='Actions/ChaosSpaceMarines/DarkGloryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/DevourerOfSouls" value="Dévoreur d'âmes"/>
	<entry name="ChaosSpaceMarines/DevourerOfSoulsDescription" value="Restaure des points vie tous les tours et à chaque fois que l'unité tue une unité ennemie."/>
	<entry name="ChaosSpaceMarines/DevourerOfSoulsFlavor" value="Les Venomcrawlers sont peut-être les œuvres les plus sophistiquées des Techmanciens, ces Machines-Démons ressemblant à des araignées qui désirent l'énergie empyréenne et consumeront d'autres entités démoniaques pour l'obtenir—ou même des mortels."/> <!-- Venomcrawler -->
	<entry name="ChaosSpaceMarines/DirgeCaster" value="<string name='Actions/ChaosSpaceMarines/DirgeCaster'/>"/>
	<entry name="ChaosSpaceMarines/DirgeCasterDescription" value="Empêche l'unité d'exécuter des tirs en état d'alerte."/>
	<entry name="ChaosSpaceMarines/DirgeCasterFlavor" value="<string name='Actions/ChaosSpaceMarines/DirgeCasterFlavor'/>"/>
	<entry name="ChaosSpaceMarines/FireFrenzy" value="Frénésie de tirs"/>
	<entry name="ChaosSpaceMarines/FireFrenzyDescription" value="Réduit le mouvement et augmente les attaques à distance."/>
	<entry name="ChaosSpaceMarines/FireFrenzyFlavor" value="En réponse aux tirs pitoyables des mortels, le Metabrutus décharge ses armes impitoyablement, sourd à tout sauf le hurlement assourdissant de ses munitions démoniaques."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/DeferredAbsolution" value="<string name='Actions/ChaosSpaceMarines/DeferredAbsolution'/>"/>
 	<entry name="ChaosSpaceMarines/DeferredAbsolutionDescription" value="Augmente la réduction de dégâts invulnérable."/>
 	<entry name="ChaosSpaceMarines/DeferredAbsolutionFlavor" value="<string name='Actions/ChaosSpaceMarines/DeferredAbsolutionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/GiftOfMutation" value="Don de mutation"/>
	<entry name="ChaosSpaceMarines/GiftOfMutationDescription" value="Au début du prochain tour, l'unité gagne un nouveau Bienfait du Chaos aléatoire déjà recherché, et ce trait disparaît."/>
	<entry name="ChaosSpaceMarines/GiftOfMutationFlavor" value="Les Dieux Sombres ont accordé à leur champion une récompense maléfique, qui peut aussi bien être un appendice redoutablement acéré qu’une langue située à un endroit insolite."/>
	<entry name="ChaosSpaceMarines/GraspingPseudopods" value="Pseudopodes aggripants"/>
	<entry name="ChaosSpaceMarines/GraspingPseudopodsDescription" value="Augmente les attaques au corps-à-corps."/>
	<entry name="ChaosSpaceMarines/GraspingPseudopodsFlavor" value="Des tentacules charnus émergent de la forme gigotante de l'enfant, s'agitant et baragouinant à ses opposants proches. Approchez-vous trop près et ils s'enrouleront autour de vous par réflexe, vous amenant vers la gueule et les dents que l'enfant a actuellement."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/IchorBlood" value="<string name='Actions/ChaosSpaceMarines/IchorBlood'/>"/>
	<entry name="ChaosSpaceMarines/IchorBloodDescription" value="<string name='Actions/ChaosSpaceMarines/IchorBloodDescription'/>"/>
	<entry name="ChaosSpaceMarines/IchorBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/IchorBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespair" value="<string name='Actions/ChaosSpaceMarines/IconOfDespair'/>"/>
	<entry name="ChaosSpaceMarines/IconOfDespairDescription" value="Augmente la réduction de dégâts au corps-à-corps quand vous êtes attaqués par des ennemis sensibles à la peur."/>
	<entry name="ChaosSpaceMarines/IconOfDespairFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfDespairFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcess" value="<string name='Actions/ChaosSpaceMarines/IconOfExcess'/>"/>
	<entry name="ChaosSpaceMarines/IconOfExcessDescription" value="Augmente la réduction de dégâts."/>
	<entry name="ChaosSpaceMarines/IconOfExcessFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfExcessFlavor'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrath" value="<string name='Actions/ChaosSpaceMarines/IconOfWrath'/>"/>
	<entry name="ChaosSpaceMarines/IconOfWrathDescription" value="Augmente les dégâts"/>
	<entry name="ChaosSpaceMarines/IconOfWrathFlavor" value="<string name='Actions/ChaosSpaceMarines/IconOfWrathFlavor'/>"/>
	<entry name="ChaosSpaceMarines/InfernalFusillade" value="<string name='Actions/ChaosSpaceMarines/InfernalFusillade'/>"/>
 	<entry name="ChaosSpaceMarines/InfernalFusilladeDescription" value="Augmente les attaques avec les armes d'éclairs."/>
 	<entry name="ChaosSpaceMarines/InfernalFusilladeFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalFusillade'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustry" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustry'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustryDescription" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustryDescription'/>"/>
	<entry name="ChaosSpaceMarines/InfernalIndustryFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalIndustryFlavor'/>"/>
	<entry name="ChaosSpaceMarines/InfernalPower" value="<string name='Actions/ChaosSpaceMarines/InfernalPower'/>"/>
	<entry name="ChaosSpaceMarines/InfernalPowerDescription" value="Augmente la précision et les dégâts."/>
	<entry name="ChaosSpaceMarines/InfernalPowerFlavor" value="<string name='Actions/ChaosSpaceMarines/InfernalPowerFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LasDamage" value="Énergies interdites"/>
	<entry name="ChaosSpaceMarines/LasDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="ChaosSpaceMarines/LasDamageFlavor" value="Aller au-delà des technologie interdites par l'Imperium a permis au Mechanicum Noir de découvrir des armes aux capacités plus létales et plus horribles."/>
	<entry name="ChaosSpaceMarines/LasherTendrils" value="<string name='Actions/ChaosSpaceMarines/LasherTendrils'/>"/>
	<entry name="ChaosSpaceMarines/LasherTendrilsDescription" value="Réduit les attaques au corps-à-corps."/>
	<entry name="ChaosSpaceMarines/LasherTendrilsFlavor" value="<string name='Actions/ChaosSpaceMarines/LasherTendrilsFlavor'/>"/>
	<entry name="ChaosSpaceMarines/LoathsomeAura" value="<string name='Actions/ChaosSpaceMarines/LoathsomeAura'/>"/>
	<entry name="ChaosSpaceMarines/LoathsomeAuraDescription" value="Augmente la réduction de dégâts invulnérable."/>
	<entry name="ChaosSpaceMarines/LoathsomeAuraFlavor" value="<string name='Actions/ChaosSpaceMarines/LoathsomeAuraFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MalevolentLocus" value="<string name='Actions/ChaosSpaceMarines/MalevolentLocus'/>"/>
	<entry name="ChaosSpaceMarines/MalevolentLocusDescription" value="Inflige des dégâts à chaque tour."/>
	<entry name="ChaosSpaceMarines/MalevolentLocusFlavor" value="<string name='Actions/ChaosSpaceMarines/MalevolentLocusFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MaliciousVolleys" value="Salve malveillante"/>
	<entry name="ChaosSpaceMarines/MaliciousVolleysDescription" value="Si l'unité n'a pas bougé, le bonus de Tir Rapide s'applique à n'importe quelle distance."/>
	<entry name="ChaosSpaceMarines/MaliciousVolleysFlavor" value="Pour un Astartes hérétique, un bolter est plus qu'une arme, c'est un instrument de sa colère qui apporte la mort à ses ennemis."/>
	<entry name="ChaosSpaceMarines/MarkOfKhorne" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorne'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneDescription" value="Augmente les attaques."/>
	<entry name="ChaosSpaceMarines/MarkOfKhorneFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfKhorneFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgle" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgle'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleDescription" value="Augmente les points de vie."/>
	<entry name="ChaosSpaceMarines/MarkOfNurgleFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfNurgleFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaanesh" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaanesh'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshDescription" value="Augmente le mouvement."/>
	<entry name="ChaosSpaceMarines/MarkOfSlaaneshFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfSlaaneshFlavor'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentch" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentch'/>"/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchDescription" value="Augmente la réduction de dégâts."/>
	<entry name="ChaosSpaceMarines/MarkOfTzeentchFlavor" value="<string name='Actions/ChaosSpaceMarines/MarkOfTzeentchFlavor'/>"/>
	<entry name="ChaosSpaceMarines/Mechanoid" value="Mechanoid"/>
	<entry name="ChaosSpaceMarines/MechanoidDescription" value="Bienfait du Chaos qui augmente l'armure."/>
	<entry name="ChaosSpaceMarines/MechanoidFlavor" value="La chair du champion fusionne avec son armure."/>
	<entry name="ChaosSpaceMarines/MeleeDamage" value="Armes fusionnelles"/>
	<entry name="ChaosSpaceMarines/MeleeDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="ChaosSpaceMarines/MeleeDamageFlavor" value="C'est normal pour les soldats d'avoir une arme de confiance dont ils se sentent plus proches—mais les marines des Légions traîtresses sont allés plus loin. Au cours des siècles les troupes de l'Adeptus Astartes ont littéralement fusionnés avec leurs armes, ne faisant plus qu'un avec leurs lames, augmentant leur efficacité exponentiellement."/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReason" value="Muté au-delà de toute raison"/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReasonDescription" value="Au début de chaque tour, l'unité gagne l'une des mutations aléatoires suivantes pour un tour : Pseudopodes aggripants, Armure sous-cutanée, Hémorragie toxique."/>
	<entry name="ChaosSpaceMarines/MutatedBeyondReasonFlavor" value="Quand ils ne sont plus dans les grâces de leur dieu, les adorateurs du Chaos sont bénis et maudits à part égale, se transformant en vils Enfants du Chaos—des masses grouillantes de mutation, développant de nouvelles caractéristiques à l'infini selon les volontés de leur dieu."/>
	<entry name="ChaosSpaceMarines/MultiLeggedTerror" value="Terreur multi-pattes"/>
 	<entry name="ChaosSpaceMarines/MultiLeggedTerrorDescription" value="Augmente les attaques de type piétinement."/>
 	<entry name="ChaosSpaceMarines/MultiLeggedTerrorFlavor" value="Le poids titanesque du fer et du laiton qui composent les parties non empyréennes du Scorpion de laiton repose sur six membres mortellement acérés. Lorsqu'il s'élance vers l'avant à une vitesse inattendue et fracassante, il peut facilement piétiner n'importe quel adversaire de petite taille."/>
	<entry name="ChaosSpaceMarines/Possession" value="<string name='Actions/ChaosSpaceMarines/Possession'/>"/>
	<entry name="ChaosSpaceMarines/PossessionDescription" value="<string name='Actions/ChaosSpaceMarines/PossessionDescription'/>"/>
	<entry name="ChaosSpaceMarines/PossessionFlavor" value="<string name='Actions/ChaosSpaceMarines/PossessionFlavor'/>"/>
	<entry name="ChaosSpaceMarines/PrinceOfChaos" value="<string name='Actions/ChaosSpaceMarines/PrinceOfChaos'/>"/>
	<entry name="ChaosSpaceMarines/PrinceOfChaosDescription" value="Augmente la précision."/>
	<entry name="ChaosSpaceMarines/PrinceOfChaosFlavor" value="<string name='Actions/ChaosSpaceMarines/PrinceOfChaosFlavor'/>"/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergy" value="<string name='Actions/ChaosSpaceMarines/ReservoirOfDaemonicEnergy'/>"/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergyDescription" value="Réduit le temps de rechargement d'Incursion."/>
	<entry name="ChaosSpaceMarines/ReservoirOfDaemonicEnergyFlavor" value="<string name='Actions/ChaosSpaceMarines/ReservoirOfDaemonicEnergyFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBlood" value="<string name='Actions/ChaosSpaceMarines/RiteOfBlood'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodDescription" value="Augmente la production fournie"/>
	<entry name="ChaosSpaceMarines/RiteOfBloodFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfBloodFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChange" value="<string name='Actions/ChaosSpaceMarines/RiteOfChange'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfChangeDescription" value="Augmente la production fournie et la production de recherche."/>
	<entry name="ChaosSpaceMarines/RiteOfChangeFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfChangeFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilence" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilence'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceDescription" value="Augmente la production de nourriture et le rythme de croissance de la population."/>
	<entry name="ChaosSpaceMarines/RiteOfPestilenceFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfPestilenceFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasure" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasure'/>"/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureDescription" value="Augmente la production d'influence et de loyauté."/>
	<entry name="ChaosSpaceMarines/RiteOfUnholyPleasureFlavor" value="<string name='Actions/ChaosSpaceMarines/RiteOfUnholyPleasureFlavor'/>"/>
	<entry name="ChaosSpaceMarines/RisingFury" value="Furie élevante"/>
	<entry name="ChaosSpaceMarines/RisingFuryDescription" value="Augmente les attaques au corps à corps"/>
	<entry name="ChaosSpaceMarines/RisingFuryFlavor" value="La rage du Métabrutus prend le dessus, lui faisant infliger une frénésie de coups contre l'entité la plus proche."/> <!-- Hellbrute Crazed result. -->
	<entry name="ChaosSpaceMarines/RunesOfTheBloodGod" value="Runes du dieu du sang"/>
 	<entry name="ChaosSpaceMarines/RunesOfTheBloodGodDescription" value="Renvoie les dégâts des attaques de feu de sorcière."/>
 	<entry name="ChaosSpaceMarines/RunesOfTheBloodGodFlavor" value="Les adeptes du Dark Mechanicum ont inscrit sur le Scorpion de laiton des runes chatoyantes qui dansent comme des flammes et se tortillent à la surface. Les psykers assez fous pour tenter un assaut verront leur esprit transpercé par la rage éternelle de Khorne."/>
	<entry name="ChaosSpaceMarines/ShatterDefences" value="<string name='Actions/ChaosSpaceMarines/ShatterDefences'/>"/>
	<entry name="ChaosSpaceMarines/ShatterDefencesDescription" value="Réduit la réduction de dégâts à distance."/>
	<entry name="ChaosSpaceMarines/ShatterDefencesFlavor" value="<string name='Actions/ChaosSpaceMarines/ShatterDefencesFlavor'/>"/>
	<entry name="ChaosSpaceMarines/SiegeCrawler" value="Bête de Siège"/>
	<entry name="ChaosSpaceMarines/SiegeCrawlerDescription" value="Augmente la pénétration d'armures contre les fortifications."/>
	<entry name="ChaosSpaceMarines/SiegeCrawlerFlavor" value="Les Ferrocerberus n'ont aucune arme à distance, ce qui est inhabituel pour un véhicule de cette taille. Cependant leur agilité et leurs différentes armes de découpe signifient qu'ils sont une gigantesque menace contre les fortifications statiques, capables de les explorer, de trouver un point faible et de l'exploiter."/> <!-- Maulerfiend. -->
	<entry name="ChaosSpaceMarines/SubcutaneousArmour" value="Armure sous-cutanée"/>
	<entry name="ChaosSpaceMarines/SubcutaneousArmourDescription" value="Augmente l'armure."/>
	<entry name="ChaosSpaceMarines/SubcutaneousArmourFlavor" value="L'armure des Space Marines, même aussi vieille que celle des Légions traîtresses, s'intègrent avec la Carapace Noire unique aux Adeptus Astartes, mais au cours des millénaires de nombreux Space Marines du Chaos n'ont plus fait qu'un avec leur armure, avec la Carapace Noire s'étendant dans leur chair et leurs os."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/TemporalDistortion" value="Distorsion Temporelle"/>
	<entry name="ChaosSpaceMarines/TemporalDistortionDescription" value="Bienfait du Chaos qui augmente le mouvement."/>
	<entry name="ChaosSpaceMarines/TemporalDistortionFlavor" value="Le temps est altéré autour du champion."/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhage" value="Hémorragie toxique"/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhageDescription" value="Augmente les dégâts contre les unités d'infanterie et les créatures monstrueuses."/>
	<entry name="ChaosSpaceMarines/ToxicHaemorrhageFlavor" value="Éjectant de viles vapeurs et des fluides horribles, les organes internes maudits de l'Enfant du Chaos pourrait signifier la mort d'une créature moins chanceuse."/> <!-- MutatedBeyondReason result. -->
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWar" value="Vétérans de la Longue Guerre"/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarDescription" value="Réduit la perte de moral, et augmente la précision en mêlée contre les unités de la faction Space Marines."/>
	<entry name="ChaosSpaceMarines/VeteransOfTheLongWarFlavor" value="De nombreuses factions de Space Marines du Chaos ont été bloquées dans une guerre constante et écrasante contre l'Imperium de l'Humanité pendant des siècles, voire des millénaires. La haine brûlante qu'ils vouent à leurs frères loyalistes a eu le temps de mûrir, surpassant toute autre émotion. Ceci s'applique par-dessus tout aux neuf Légions traîtresses originales qui ont rejoint la bannière d'Horus il y a dix mille ans, et qui continuent leur guerre contre leurs frères loyaux jusqu'à ce jour."/>
	<entry name="ChaosSpaceMarines/WarpFrenzy" value="Frénésie du Warp"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyDescription" value="Bienfait du Chaos qui augmente les attaques"/>
	<entry name="ChaosSpaceMarines/WarpFrenzyFlavor" value="Le champion est consumé par la colère."/>
	<entry name="ChaosSpaceMarines/WorthyOffering" value="<string name='Actions/ChaosSpaceMarines/WorthyOffering'/>"/>
	<entry name="ChaosSpaceMarines/WorthyOfferingDescription" value="<string name='Actions/ChaosSpaceMarines/WorthyOfferingDescription'/>"/>
	<entry name="ChaosSpaceMarines/WorthyOfferingFlavor" value="<string name='Actions/ChaosSpaceMarines/WorthyOfferingFlavor'/>"/>
	<entry name="ChapterUnity" value="Unité du chapitre"/>
	<entry name="ChapterUnityDescription" value="Augmente la production de loyauté"/>
	<entry name="ChapterUnityFlavor" value="Les reliques abritées dans le Grand Hall rappelle aux Adeptus Astartes les héros du passés, et qu'un jour leur armure, armes et os pourraient aussi être révérés ici."/>
	<entry name="City" value="Ville"/>
	<entry name="CityDescription" value="Augmente la réduction de dégâts et le facteur de soin pour les unités alliées. Réduit le coût de mouvement des unités. Augmente la réduction de dégât à distance des unités d'infanterie."/>
	<entry name="ClusterMines" value="<string name='Weapons/ClusterMines'/>"/>
	<entry name="ClusterMinesDescription" value="Inflige des dégâts lors de l'arrivée sur la case."/>
	<entry name="ClusterMinesFlavor" value="<string name='Weapons/ClusterMinesFlavor'/>"/>
	<entry name="CombatShield" value="Bouclier de combat"/>
	<entry name="CombatShieldDescription" value="Augmente la réduction de dégâts"/>
	<entry name="CombatShieldFlavor" value="Un bouclier de combat est une version plus légère du bouclier tempête accroché au bras du porteur, laissant leur main libre pour utiliser une autre arme tout en fournissant une protection contre les attaques."/>
	<entry name="CompendiumFlavor" value="Si les guerriers du 41° millénaire étaient identiques, les guerres seraient une simple question de nombres—et l'humanité aurait gagné depuis longtemps. A la place, chaque race, chaque troupe et chaque soldat a ses propres traits ce qui les rend parfaits pour certaines situations—et les laisse horriblement exposés dans d'autres."/>
	<entry name="Concussion" value="Commotion"/>
	<entry name="ConcussionDescription" value="Réduit la précision."/>
	<entry name="Concussive" value="Commotion"/>
	<entry name="ConcussiveDescription" value="Réduit temporairement la précision de l'unité d'infanterie ou la créature monstrueuse ciblée."/>
	<entry name="ConcussiveFlavor" value="Certaines armes sont conçues pour laisser désorienté et facile à tuer un ennemi qui réussirait à survivre leur attaque."/>
	<entry name="ConvergentTargeting" value="Triangulation"/>
	<entry name="ConvergentTargetingDescription" value="Augmente la précision lorsqu'adjacent à un Canon Thunderfire allié."/>
	<entry name="CultAmbush" value="Embuscade du culte"/>
	<entry name="CultAmbushDescription" value="Augmente la précision des tirs en état d'alerte."/>
	<entry name="CurseOfTheWalkingPox" value="La malédiction de la peste marchante"/>
 	<entry name="CurseOfTheWalkingPoxDescription" value="Convertit les dégâts en soins pour cette unité."/>
 	<entry name="CurseOfTheWalkingPoxFlavor" value="Le rictus qui orne chaque marcheur de peste dissimule le tourment dans lequel se trouvent leurs âmes, piégées dans leurs corps mutants et morts qui se battent pour Nurgle, infectant de plus en plus d'innocents avec le fléau de la marche."/>
	<entry name="CultAmbushFlavor" value="Les cultes Genestealers préparent méticuleusement chaque attaque, leurs stratégies affinées pour leur donner tous les avantages lorsqu'ils se soulèvent."/>
	<entry name="Daemon" value="Démon"/>
	<entry name="DaemonDescription" value="Augmente la réduction de dégâts."/>
	<entry name="DaemonFlavor" value="Les créatures du warps sont viles et nombreuses, avec une variété infinie, mais il y a quelques caractéristiques qu'elles partagent toutes."/>
	<entry name="Damaged" value="Endommagé"/>
	<entry name="DamagedDescription" value="Réduit la précision"/>
 	<entry name="Deathshriek" value="Hurlement d'agonie"/>
 	<entry name="DeathshriekDescription" value="Inflige des dégâts à l'attaquant lorsque l'unité meurt."/>
 	<entry name="DeathshriekFlavor" value="Lorsqu'un Umbra meurt, ceux alentours reçoivent une vision—de la créature qui fût mise en morceaux il y a si longtemps et jeté dans le Warp, tandis qu'une voix, horrifiante et attirante, murmure une malédiction… “ERRE…”"/>
	<entry name="DeedsOfGlory" value="Actes glorieux"/>
	<entry name="DeedsOfGloryDescription" value="<string name='Actions/DeedsOfGloryDescription'/>"/>
	<entry name="DestroyerWeapon" value="Arme destructrice"/>
	<entry name="DestroyerWeaponDescription" value="Augmente les dégâts."/>
	<entry name="DevastatorDoctrine" value="Doctrine Devastator"/>
	<entry name="DevastatorDoctrineDescription" value="Augmente la précision."/>
	<entry name="DevastatorDoctrineFlavor" value="<string name='Actions/DevastatorDoctrineFlavor'/>"/>
	<entry name="Discipline" value="Discipline"/>
	<entry name="DisciplineDescription" value="Augmente la précision"/>
	<entry name="DisciplineFlavor" value="<string name='Actions/AuraOfDisciplineFlavor'/>"/>
 	<entry name="DistortScythe" value="Faux à distorsion"/>
 	<entry name="DistortScytheDescription" value="Augmente les dégâts."/>
 	<entry name="DistortScytheFlavor" value="<string name='Weapons/DistortionFlavor'/>"/>
	<entry name="DogmaAstrates" value="Dogme Astartes"/>
	<entry name="DogmaAstratesDescription" value="<string name='Actions/DogmaAstratesDescription'/>"/>
	<entry name="DogmaAstratesFlavor" value="<string name='Actions/DogmaAstratesFlavor'/>"/>
	<entry name="DozerBlade" value="Lame de bulldozer"/>
	<entry name="DozerBladeDescription" value="Réduit la pénalité de mouvement dans les forêts et les ruines impériales."/>
	<entry name="DozerBladeFlavor" value="Les lames de bulldozer sont de lourdes lames, béliers, charrues et pelles utilisés pour dégager les obstacles devant le véhicule."/>
	<entry name="EavyArmour" value="Armur' lourde"/>
	<entry name="EavyArmourDescription" value="Augmente l'armure."/>
	<entry name="EavyArmourFlavor" value="L’armure lourde Ork est façonnée par un martelage de ferraille, de tôles hétéroclites et de pièces d’armures récupérées sur des ennemis défaits. Bien qu’elle ne corresponde que très approximativement aux mensurations de son propriétaire, l’armure lourde lui fournit une protection appréciable."/>
 	<entry name="Drukhari/AncientEvil" value="<string name='Actions/Drukhari/AncientEvil'/>"/>
    <entry name="Drukhari/AncientEvilDescription" value="Les unités attaquées perdent leur moral."/>
    <entry name="Drukhari/AncientEvilFlavor" value="<string name='Actions/Drukhari/AncientEvilFlavor'/>"/>
    <entry name="Drukhari/AssaultWeaponBonus" value="Concepts déviants"/>
    <entry name="Drukhari/AssaultWeaponBonusDescription" value="Augmente la pénétration de l'armure."/>
    <entry name="Drukhari/AssaultWeaponBonusFlavor" value="Les maîtres d'armes des fonderies kabalites ont accès à des variantes d'armes normalement considérées comme impossibles ou totalement contraires à l'éthique. Bien entendu, il s'agit simplement de savoir combien un Archon particulier est prêt à payer pour les obtenir."/>
    <entry name="Drukhari/BetrayalCulture" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgrade'/>"/>
 	<entry name="Drukhari/BetrayalCultureDescription" value="Les villes drukhari ont une loyauté plus faible par défaut. Elles obtiennent une loyauté supplémentaire au fur et à mesure qu'elles accumulent de l'Influence."/>
 	<entry name="Drukhari/BetrayalCultureFlavor" value="Pour les Drukhari dépravés, il semble normal qu'un enfant trahisse ses parents et les plonge dans une éternité de torture pour le moindre avantage. Il n'y a pas de confiance dans une cité drukhari, seulement du pouvoir. Plus vous êtes puissant, plus vous serez nombreux à vous ranger de votre côté."/>
    <entry name="Drukhari/BetrayalCultureUpgrade" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgrade'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgradeDescription" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgradeDescription'/>"/>
	<entry name="Drukhari/BetrayalCultureUpgradeFlavor" value="<string name='Upgrades/Drukhari/BetrayalCultureUpgradeDescription'/>"/>
	<entry name="Drukhari/BladeArtists" value="Artistes de la lame"/>
    <entry name="Drukhari/BladeArtistsDescription" value="Augmente la pénétration de l'armure en mêlée."/>
    <entry name="Drukhari/BladeArtistsFlavor" value="Tous les habitants de Commorragh apprennent dès leur plus jeune âge la valeur des lames, et tous sont experts dans leur utilisation, qu'elles soient maniées par leurs mains cruelles ou qu'elles fassent partie de leur armure aux bords tranchants."/>
    <entry name="Drukhari/BladeWhip" value="Fouet de lames"/> 
    <entry name="Drukhari/BladeWhipDescription" value="Augmente la précision."/>
    <entry name="Drukhari/BladeWhipFlavor" value="Seuls les Lacerai et les Wych utilisent régulièrement la lame segmentée appelée Razorflail, mais leur habileté est légendaire. Les attaques qui semblaient vouées à être manquées se courbent pour se connecter, passant du fouet à la lame à volonté."/>
    <entry name="Drukhari/BloodDancer" value="<string name='Actions/Drukhari/BloodDancer'/>"/>
    <entry name="Drukhari/BloodDancerDescription" value="<string name='Actions/Drukhari/BloodDancerDescription'/>"/>
    <entry name="Drukhari/BloodDancerFlavor" value="<string name='Actions/Drukhari/BloodDancerFlavor'/>"/>
    <entry name="Drukhari/BonusResources" value="<string name='Actions/Drukhari/BonusResources'/>"/>
    <entry name="Drukhari/BonusResourcesDescription" value="Augmente le rendement des bâtiments."/>
    <entry name="Drukhari/BonusResourcesFlavor" value="<string name='Actions/Drukhari/BonusResourcesFlavor'/>"/>
    <entry name="Drukhari/BridesOfDeath" value="<string name='Actions/Drukhari/BridesOfDeath'/>"/>
    <entry name="Drukhari/BridesOfDeathDescription" value="Augmente les dégâts de mêlée."/>
    <entry name="Drukhari/BridesOfDeathFlavor" value="<string name='Actions/Drukhari/BridesOfDeathFlavor'/>"/>
    <entry name="Drukhari/CityTier2" value="Gonflé par le péché"/>
    <entry name="Drukhari/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
    <entry name="Drukhari/CityTier2Flavor" value="Au fur et à mesure que les raiders drukhari s'habituent à une planète, leur base de raid s'étend plus largement, attirant davantage de résidents parmi les plus vils de la galaxie, et pouvant supporter une plus grande population de vils héritiers des Aeldari. Les bidonvilles et les jardins de torture de la ville s'étendent de plus en plus vers l'extérieur et vers le bas…"/>
    <entry name="Drukhari/CityTier3" value="Domaine du désespoir"/>
    <entry name="Drukhari/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
    <entry name="Drukhari/CityTier3Flavor" value="Les Archons et les Dracons de ce royaume pirate sont devenus si riches qu'ils ont établi des palais pour eux-mêmes ici, sachant parfaitement qu'ils ne peuvent pas rester, de peur que celle qui a soif ne consume complètement leurs âmes. Bien entendu, tant qu'ils sont ici, ils ont besoin d'un approvisionnement constant en captifs pour assouvir leurs désirs inhumains et empêcher le pourrissement de leurs âmes."/>
    <entry name="Drukhari/CombatDrugs" value="Drogues de combat"/>
    <entry name="Drukhari/CombatDrugsDescription" value="Augmente diverses capacités de combat."/>
    <entry name="Drukhari/CombatDrugsFlavor" value="Bien qu'ils réduisent considérablement l'espérance de vie de l'utilisateur, les stimulants chimiques sont largement utilisés pour améliorer les performances au combat."/>
    <entry name="Drukhari/CorsairOutposts" value="Avant-poste Corsaire"/>
 	<entry name="Drukhari/CorsairOutpostsDescription" value="Les villes drukhari ont une croissance plus faible par défaut. Les avant-postes contrôlés accordent une croissance supplémentaire."/>
	<entry name="Drukhari/CorsairOutpostsFlavor" value="Bien que ces ghettos ressemblent à des villes pour les races du troupeau, les Drukhari n'y resteront pas longtemps s'ils ne savent pas qu'il y a des richesses à trouver dans les environs. Mais les bases de raids les plus prospères attireront davantage de drukhari, désireux de prendre leur part de meurtres et de souffrances."/>
    <entry name="Drukhari/CorsairOutpostsUpgrade" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgrade'/>"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeDescription" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgradeDescription'/>"/>
	<entry name="Drukhari/CorsairOutpostsUpgradeFlavor" value="<string name='Upgrades/Drukhari/CorsairOutpostsUpgradeDescription'/>"/>
	<entry name="Drukhari/Dodge" value="Esquiver"/>
    <entry name="Drukhari/DodgeDescription" value="Augmente la réduction des dégâts en mêlée."/>
    <entry name="Drukhari/DodgeFlavor" value="Les divers guerriers—connus sous le nom d'Hekatarii—des Cultes du Wych manient un éventail extrêmement varié d'outils exotiques pour mutiler, piéger, taillader et embrocher. Ils portent peu d'armures ; leur défense repose sur leur vitesse et leur agilité, affichant leur arrogance lorsqu'ils esquivent les coups maladroits de leurs adversaires."/>
    <entry name="Drukhari/EnergyBuildingBonus" value="Collecteurs d'Ilmaea relâché"/>
    <entry name="Drukhari/EnergyBuildingBonusDescription" value="Augmente la production d'énergie."/>
    <entry name="Drukhari/EnergyBuildingBonusFlavor" value="Cette centrale n'a accès qu'à une infime partie de la puissance de l'un des Ilmaea des Drukhari, les soleils noirs capturés qui encerclent Commorragh. Le fait de relâcher un tant soit peu les collecteurs de sécurité augmente la puissance de façon proportionnelle, tout en augmentant considérablement le risque que l'Ilmaea se trompe et consomme Gladius Prime d'un coup. C'est un risque que les Drukhari, presque immortels, sont heureux de prendre."/>
    <entry name="Drukhari/EnhancedAethersails" value="<string name='Actions/Drukhari/EnhancedAethersails'/>"/>
    <entry name="Drukhari/EnhancedAethersailsDescription" value="<string name='Actions/Drukhari/EnhancedAethersailsDescription'/>"/>
    <entry name="Drukhari/EnhancedAethersailsFlavor" value="<string name='Actions/Drukhari/EnhancedAethersailsFlavor'/>"/>
    <entry name="Drukhari/Flickerfield" value="Champ de scintillement"/>
    <entry name="Drukhari/FlickerfieldDescription" value="Augmente la réduction des dégâts d'invulnérabilité."/>
    <entry name="Drukhari/FlickerfieldFlavor" value="Les champs de scintillement sont des boucliers optiques très avancés qui donnent l'impression que le véhicule sur lequel ils sont montés vacille dans la réalité."/>
    <entry name="Drukhari/GhostplateArmour" value="Armure de plaques fantômes"/>
    <entry name="Drukhari/GhostplateArmourDescription" value="Augmente la réduction des dégâts d'invulnérabilité."/>
    <entry name="Drukhari/GhostplateArmourFlavor" value="Les Drukhari qui souhaitent bénéficier d'une protection importante tout en conservant une grande mobilité portent des armures faites de résines durcies et traversées par des poches de gaz plus léger que l'air. Les armures de plaques fantômes intègrent également une technologie de champ de force mineure afin de mieux protéger leur porteur."/>
    <entry name="Drukhari/GrislyTrophies" value="<string name='Actions/Drukhari/GrislyTrophies'/>"/>
    <entry name="Drukhari/GrislyTrophiesDescription" value="Réduit la perte de moral."/>
    <entry name="Drukhari/GrislyTrophiesFlavor" value="<string name='Actions/Drukhari/GrislyTrophiesFlavor'/>"/>
    <entry name="Drukhari/HeavyWeaponBonus" value="Ordonnance Equipoise"/>
    <entry name="Drukhari/HeavyWeaponBonusDescription" value="Augmente la pénétration de l'armure."/>
    <entry name="Drukhari/HeavyWeaponBonusFlavor" value="“N'espérez pas un jour nouveau, petits mortels. Les Drukhari vous tiennent maintenant et notre emprise est inébranlable. Je vous assure que vous ne connaîtrez plus que la nuit éternelle et le tourment éternel.”—Gyrthineus Roche, Archon de la Dernière Lame."/>
    <entry name="Drukhari/MasterOfPain" value="<string name='Actions/Drukhari/MasterOfPain'/>"/>
    <entry name="Drukhari/MasterOfPainDescription" value="Confère une réduction supplémentaire des dégâts de Insensible à la souffrance."/>
    <entry name="Drukhari/MasterOfPainFlavor" value="<string name='Actions/Drukhari/MasterOfPainFlavor'/>"/>
    <entry name="Drukhari/MeleeWeaponBonus" value="L'esthétique des lames, perfectionnée"/>
    <entry name="Drukhari/MeleeWeaponBonusDescription" value="Augmente la pénétration de l'armure."/>
    <entry name="Drukhari/MeleeWeaponBonusFlavor" value="“La douleur est la seule constante universelle. La douleur est tout. Elle est la clé de la création et de la destruction. C'est ainsi que celui qui maîtrise la douleur devient un dieu”.—Urien Rakarth, maître des Haemonculi"/>
    <entry name="Drukhari/NightShields" value="Boucliers nocturnes"/>
    <entry name="Drukhari/NightShieldsDescription" value="Augmente la réduction des dégâts à distance."/>
    <entry name="Drukhari/NightShieldsFlavor" value="Le véhicule est recouvert d'un champ de déplacement à large spectre, qui l'enveloppe d'un froid et d'une obscurité d'encre. Les ennemis ont du mal à cibler le véhicule, caché qu'il est dans un manteau d'ombres tourbillonnantes."/>
    <entry name="Drukhari/NoEscape" value="<string name='Actions/Drukhari/NoEscape'/>"/>
    <entry name="Drukhari/NoEscapeDescription" value="Réduit le mouvement."/>
    <entry name="Drukhari/NoEscapeFlavor" value="<string name='Actions/Drukhari/NoEscapeFlavor'/>"/>
    <entry name="Drukhari/Overlord" value="<string name='Actions/Drukhari/Overlord'/>"/>
    <entry name="Drukhari/OverlordDescription" value="Augmente la précision."/>
    <entry name="Drukhari/OverlordFlavor" value="<string name='Actions/Drukhari/OverlordFlavor'/>"/>
    <entry name="Drukhari/PowerFromPain" value="Le pouvoir de la souffrance"/>
    <entry name="Drukhari/PowerFromPainDescription" value="Octroie des bonus de combat au fur et à mesure que l'unité monte en niveau. Augmente la réduction des dégâts à partir du niveau 3, augmente les dégâts de mêlée à partir du niveau 6 et réduit la perte de moral au niveau 10."/>
    <entry name="Drukhari/PowerFromPainFlavor" value="En se nourrissant des âmes de leurs ennemis, les Drukhari s'imprègnent d'une force surnaturelle et finissent par se transformer en machines à tuer."/>
    <entry name="Drukhari/RaidersTactics" value="Tactique des raiders"/>
    <entry name="Drukhari/RaidersTacticsDescription" value="Confère des traits bonus lorsque cette unité débarque d'un transport."/>
    <entry name="Drukhari/RaidersTacticsFlavor" value="“Pourquoi montons-nous sur ces élégantes embarcations ? Pour mieux entendre les cris de nos proies lorsque nous les descendons, pour savourer la peur gravée sur leur visage, pour goûter l'odeur alléchante de leur sang dans l'air, comme un amuse-gueule avant le festin. Mais surtout, nous les chevauchons pour que le massacre commence le plus tôt possible”.—Dariaq Bladetongue de l'œil percé"/>
    <entry name="Drukhari/RaidersTacticsDamage" value="Assaut des raiders"/>
    <entry name="Drukhari/RaidersTacticsDamageDescription" value="Augmente les dégâts."/>
    <entry name="Drukhari/RaidersTacticsDamageFlavor" value="Des millénaires de déprédations sur le plan mortel font des Drukhari des prédateurs d'opportunité inhumains sans équivalent dans l'histoire. Cela est dû en partie à des siècles d'expérience, en partie à l'évolution et en partie aux adaptations des Haemonculi. Lorsqu'ils sortent de leurs aéronefs, ils tombent sur leurs ennemis comme des aigles qui descendent."/>
    <entry name="Drukhari/RaidersTacticsDamageReduction" value="Évasion des raiders"/>
    <entry name="Drukhari/RaidersTacticsDamageReductionDescription" value="Augmente la réduction des dégâts d'invulnérabilité."/>
    <entry name="Drukhari/RaidersTacticsDamageReductionFlavor" value="Les Drukhari vivent pour infliger de la douleur, et certains moments cruciaux de leur existence en sont l'illustration. La chute du glaive d'un incube, le tourment d'une Wych dans l'arène, la fraction de seconde de surprise mortelle lorsque les raiders drukhari sautent de leur aéronef…"/>
    <entry name="Drukhari/RaidersTacticsHealingRate" value="Préparation des raiders"/>
    <entry name="Drukhari/RaidersTacticsHealingRateDescription" value="Augmente le taux de guérison lorsque cette unité est embarquée dans un véhicule."/>
    <entry name="Drukhari/RaidersTacticsHealingRateFlavor" value="“Mes amis—je vous appelle ainsi, même si je sais que vous me donneriez un coup de couteau dans le dos au moindre coup d'œil de Vect—mes amis, tenez-vous prêts. Le moment est presque arrivé, le troupeau ne sait même pas que nous arrivons. Dans quelques minutes, nous descendrons, écorcherons, déchirerons, tourmenterons, assassinerons et boirons leurs âmes… avec style, bien sûr. Nous ne sommes pas des sauvages.”—Gyrthineus Roche, Archon de la Dernière Lame."/>
    <entry name="Drukhari/Shadowfield" value="<string name='Actions/Drukhari/Shadowfield'/>"/>
    <entry name="Drukhari/ShadowfieldDescription" value="Augmente la réduction des dégâts d'invulnérabilité."/>
    <entry name="Drukhari/ShadowfieldFlavor" value="<string name='Actions/Drukhari/ShadowfieldFlavor'/>"/>
    <entry name="Drukhari/SacrificeToKhaine" value="Sacrifice de Khaine"/>
    <entry name="Drukhari/SacrificeToKhaineDescription" value="Augmente les dégâts."/>
    <entry name="Drukhari/SacrificeToKhaineFlavor" value="Les Drukhari ne vénèrent pas les anciens dieux Aeldari ou les Anciens, à l'exception d'un seul, Kaela Mensha Khaine, dieu du meurtre et de la guerre. Seule une poignée de Drukhari voue un culte à Khaine et la plupart d'entre eux sont des Incubes, les miroirs sombres des guerriers d'aspect Aeldari. Chaque fois qu'ils tuent avec leurs glaives rituels, c'est pour louer Khaine."/>
    <entry name="Drukhari/ShroudGate" value="Porte du linceul"/>
    <entry name="Drukhari/ShroudGateDescription" value="Augmente la réduction des dégâts à distance."/>
    <entry name="Drukhari/ShroudGateFlavor" value="“C'était une bonne idée de tendre une embuscade au portail de la toile, brave petit humain. Chut, maintenant. Mais tu ne savais pas où et quand nous allions surgir, et c'était ton erreur. Pour nous, Drukhari, les portails sont une commodité inutile… mais ta souffrance, je le crains, est une nécessité.”—Gyrthineus Roche, Archon de la Dernière Lame."/>
    <entry name="Drukhari/SoulHunger" value="Faim d'âme"/>
    <entry name="Drukhari/SoulHungerDescription" value="Octroie de l'influence en tuant un ennemi."/>
    <entry name="Drukhari/SoulHungerFlavor" value="La seule forme de subsistance qui rafraîchit un Drukhari—la seule chose qui rend leur vie immortelle acceptable et supportable, alors que Slaanesh sape leurs âmes—est la souffrance des autres. Et les Archons qui peuvent assurer un approvisionnement régulier en victimes et en douleur reçoivent la loyauté—ou plutôt, ce qui s'en rapproche le plus pour un Drukhari…"/>
    <entry name="Drukhari/SoulHungerLoyalty" value="Pain d'âme et cirque"/>
    <entry name="Drukhari/SoulHungerLoyaltyDescription" value="Augmente le taux de fidélité."/>
    <entry name="Drukhari/SoulHungerLoyaltyFlavor" value="Dans un monde si dépourvu de loyauté, il est payant de garder les masses de son côté—qui sait quand vous aurez besoin de conduire une foule de lyncheurs après votre rival, par exemple ? Lorsqu'un Archon paie pour qu'un culte Wych se produise dans l'arène ou distribue de la nourriture, des armes ou des victimes aux désespérés des bas-fonds de Commorragh, chaque Drukhari comprend qu'il ne le fait pas par compassion, mais par calcul."/>
    <entry name="Drukhari/SoulHungerOutposts" value="Dîme de l'âme"/>
    <entry name="Drukhari/SoulHungerOutpostsDescription" value="Augmente la production de ressources."/>
    <entry name="Drukhari/SoulHungerOutpostsFlavor" value="Bien sûr, tous les Drukhari Kabalites sont redevables à leur Archon et ne feraient rien pour perdre ses faveurs… Pourtant, d'une manière ou d'une autre, il existe un marché noir florissant pour les ressources volées, en particulier loin du siège du pouvoir. Ainsi, lorsque l'ordre d'envoyer la dîme arrive, il est étonnamment facile d'en rassembler suffisamment."/>
    <entry name="Drukhari/SoulHarvest" value="<string name='Actions/Drukhari/SoulHarvest'/>"/>
    <entry name="Drukhari/SoulHarvestDescription" value="Augmente les dégâts et accorde de l'influence en tuant un ennemi."/>
    <entry name="Drukhari/SoulHarvestFlavor" value="<string name='Actions/Drukhari/SoulHarvestFlavor'/>"/>
    <entry name="Drukhari/FeastOfTorment" value="Festin de tourment"/>
    <entry name="Drukhari/FeastOfTormentDescription" value="Restaure les points de vie à chaque tour."/>
    <entry name="Drukhari/FeastOfTormentFlavor" value="Il n'est pas nécessaire d'encourager les Drukhari à commettre des actes de sadisme, car cela leur vient naturellement. Cependant, ils craignent toujours la colère de leur Archon, et il est donc parfois nécessaire de leur donner la permission explicite de torturer les innocents de Gladius Prime plutôt que de les renvoyer à Commorragh. Par ailleurs, le sang innocent a un effet merveilleusement rajeunissant."/>
    <entry name="Drukhari/SpiritProbe" value="<string name='Actions/Drukhari/SpiritProbe'/>"/>
    <entry name="Drukhari/SpiritProbeDescription" value="Augmente la réduction des dégâts."/>
    <entry name="Drukhari/SpiritProbeFlavor" value="<string name='Actions/Drukhari/SpiritProbeFlavor'/>"/>
    <entry name="Drukhari/ToweringArrogance" value="<string name='Actions/Drukhari/ToweringArrogance'/>"/>
    <entry name="Drukhari/ToweringArroganceDescription" value="Diminue la perte de moral."/>
    <entry name="Drukhari/ToweringArroganceFlavor" value="<string name='Actions/Drukhari/ToweringArroganceFlavor'/>"/>
    <entry name="Drukhari/WealthPlunder" value="Études sur l'agonie"/>
    <entry name="Drukhari/WealthPlunderDescription" value="Permet d'effectuer des recherches en tuant un ennemi."/>
    <entry name="Drukhari/WealthPlunderFlavor" value="“Près de la mort, je les tiens. Les secrets qu'ils chuchotent. Les supplications. Tactiques, inventions, connaissances de l'autre côté, révélations… tout a été déversé. Pour arrêter la douleur. Mais pourquoi devrait-elle s'arrêter ? Quand elle est si… productive.”—Arkanic, Haemonculus Kabalite de la dernière lame."/>
    <entry name="Drukhari/WeaponRacks" value="Râtelier d'armes"/>
    <entry name="Drukhari/WeaponRacksDescription" value="Accorde un lien double pendant un tour aux unités qui débarquent de ce véhicule."/>
    <entry name="Drukhari/WeaponRacksFlavor" value="Certains véhicules Drukhari transportent des râteliers supplémentaires d'armes antipersonnel sur leur pont. Cela permet aux passagers de vider littéralement les chargeurs de leurs armes dans de grandes fusillades, avant de se débarrasser de leurs armes usagées en faveur d'armes de remplacement entièrement chargées au moment du débarquement."/>
    <entry name="Drukhari/WhirlingDeath" value="<string name='Actions/Drukhari/WhirlingDeath'/>"/>
    <entry name="Drukhari/WhirlingDeathDescription" value="Sélectionne tous les membres du groupe de l'unité cible."/>
    <entry name="Drukhari/WhirlingDeathFlavor" value="<string name='Actions/Drukhari/WhirlingDeathFlavor'/>"/>
	<entry name="Eldar/AerobaticGrace" value="Grâce aérienne"/>
 	<entry name="Eldar/AerobaticGraceDescription" value="Accorde une réduction de dégâts à distance si l'unité a bougé ce tour-ci."/>
 	<entry name="Eldar/AerobaticGraceFlavor" value="Si les Arlequins sont les véritables maîtres de la danse parmi les Aeldari, les Lances étincelantes s'en approchent grâce à leur maîtrise des moto-jets, semblant pirouetter entre les tempêtes de tirs de bolter alors qu'ils chargent leurs adversaires."/>
 	<entry name="Eldar/AircraftBuildingBonus" value="Invocations de Kurnous"/>
 	<entry name="Eldar/AircraftBuildingBonusDescription" value="Augmente la production fournie."/>
 	<entry name="Eldar/AircraftBuildingBonusFlavor" value="Les Chasseurs écarlates jurent allégeance à Kurnous, le Dieu de la Chasse Aeldari. Bien qu'il soit mort depuis 60 millions d'année, lorsque son nom est invoqué, ils redoublent d'effort, à la fois en terme de recrutement et de construction."/>
 	<entry name="Eldar/AncientDoom" value="Fléau ancestral"/>
 	<entry name="Eldar/AncientDoomDescription" value="Augmente la précision et les dégâts subis contre les unités du Chaos."/>
 	<entry name="Eldar/AncientDoomFlavor" value="Les Aeldaris haïssent et craignent l'Assoiffée par-dessus tout, car ils voient en Slaanesh leur destin incarné."/>
 	<entry name="Eldar/AssaultWeaponBonus" value="Munition sub-moléculaire"/>
 	<entry name="Eldar/AssaultWeaponBonusDescription" value="Augmente la pénétration d'armure."/>
 	<entry name="Eldar/AssaultWeaponBonusFlavor" value="Les munitions-shurikens unique des Aeldaris consistent en des lames monomoléculaires tournantes, tirées à grande vitesse. Mais les molécules sont si limitantes lorsqu'il y a des structures plus petites et mortelles à utiliser."/>
 	<entry name="Eldar/AssuredDestruction" value="Destruction certaine"/>
 	<entry name="Eldar/AssuredDestructionDescription" value="Augmente les dégâts contre les véhicules."/>
 	<entry name="Eldar/AssuredDestructionFlavor" value="“Si un capitaine est assez stupide pour permettre à cinq Aeldaris avec des fuseurs et plusieurs millénaires d'experience martiale s'approcher à moins de 20 mètres de son char… Et bien honnêtement, il mérite d'être une flaque de métal fondu par terre.”<br/>  — Commissaire Gruber"/>
 	<entry name="Eldar/AsuryaniArrivals" value="<string name='Actions/Eldar/AsuryaniArrivals'/>"/>
 	<entry name="Eldar/AsuryaniArrivalsDescription" value="<string name='Actions/Eldar/AsuryaniArrivalsDescription'/>"/>
 	<entry name="Eldar/AsuryaniArrivalsFlavor" value="<string name='Actions/Eldar/AsuryaniArrivalsFlavor'/>"/>
 	<entry name="Eldar/AutarchsAssault" value="<string name='Actions/Eldar/AutarchsAssault'/>"/>
 	<entry name="Eldar/AutarchsAssaultDescription" value="Augmente les dégâts"/>
 	<entry name="Eldar/AutarchsAssaultFlavor" value="<string name='Actions/Eldar/AutarchsAssaultFlavor'/>"/>
 	<entry name="Eldar/AutarchsAssaultPassive" value="<string name='Actions/Eldar/AutarchsAssault'/>"/>
 	<entry name="Eldar/BansheeMask" value="Masque de Banshee"/>
 	<entry name="Eldar/BansheeMaskDescription" value="<string name='Traits/InfiltrateDescription'/>"/>
 	<entry name="Eldar/BansheeMaskFlavor" value="Ces masques amplifient le cri de guerre des Aeldaris, infligeant une paralysie temporaire."/>
 	<entry name="Eldar/BattleFocus" value="Transe guerrière"/>
 	<entry name="Eldar/BattleFocusDescription" value="Les actions ne consomment pas le mouvement."/>
 	<entry name="Eldar/BattleFocusFlavor" value="Quand les Asuryanis enfilent leurs masques de guerre, ils entrent dans une transe guerrière si concentrée qu'ils fondent sur le champ de bataille comme du vif-argent, tuant leurs ennemis sans rompre leur rythme."/>
 	<entry name="Eldar/CityTier2" value="Tête-de-pont Exodite"/>
 	<entry name="Eldar/CityTier2Description" value="Augmente le rayon d'acquisition des cases"/>
 	<entry name="Eldar/CityTier2Flavor" value="Malgré les millénaires qu'ils ont passé à dériver parmi les étoiles, certains Rangers des Aeldaris des Vaisseaux-mondes ont passé du temps sur des Mondes Exodites et des Mondes Vierges, et ont appris comment vivre sur une planète, faire pousser de la nourriture et construire leurs bâtiments de moelle spectrale selon les anciennes pratiques."/>
 	<entry name="Eldar/CityTier3" value="Infrastructure Exodite"/>
 	<entry name="Eldar/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
 	<entry name="Eldar/CityTier3Flavor" value="Ici, des concepts de construction Exodite avancés sont utilisés par les Chanteurs de moelle des Vaisseaux-mondes. Ayant toujours travaillé sur les anciens Vaisseaux-mondes, l'idée de construire une ville pour la première fois est quelque chose de complètement nouveau pour leurs esprits."/>
 	<entry name="Eldar/Command" value="<string name='Actions/Eldar/Command'/>"/>
 	<entry name="Eldar/CommandDescription" value="Augmente la précision."/>
 	<entry name="Eldar/CommandFlavor" value="<string name='Actions/Eldar/CommandFlavor'/>"/>
 	<entry name="Eldar/ConstructionBuildingBonus" value="Convocation des Chanteurs de moelle"/>
 	<entry name="Eldar/ConstructionBuildingBonusDescription" value="Augmente la production fournie."/>
 	<entry name="Eldar/ConstructionBuildingBonusFlavor" value="Les Chanteurs de moelle sont rares et précieux, même parmi les Aeldaris éparpillés—mais ils sont venus en nombre pour répondre aux demandes du Grand prophète, pour capturer cette planète unique pour leur race mourante."/>
 	<entry name="Eldar/CrackShot" value="Tir assuré"/>
 	<entry name="Eldar/CrackShotDescription" value="Augmente la précision et la pénétration d'armure."/>
 	<entry name="Eldar/CrackShotFlavor" value="Les Dragons flamboyants sont craints à juste raison en tant que chasseur de chars, et aucun n'est plus craint que leur Exarque, qui mène son escouade au front, l'inspirant à plus de destructions avec ses propres prouesses dévastatrices."/>
 	<entry name="Eldar/CrystalTargetingMatrix" value="<string name='Actions/Eldar/CrystalTargetingMatrix'/>"/>
 	<entry name="Eldar/CrystalTargetingMatrixDescription" value="<string name='Actions/Eldar/CrystalTargetingMatrixDescription'/>"/>
 	<entry name="Eldar/CrystalTargetingMatrixFlavor" value="<string name='Actions/Eldar/CrystalTargetingMatrixFlavor'/>"/>
 	<entry name="Eldar/Doom" value="<string name='Actions/Eldar/Doom'/>"/>
 	<entry name="Eldar/DoomDescription" value="Augmente les dégâts subis."/>
 	<entry name="Eldar/DoomFlavor" value="<string name='Actions/Eldar/DoomFlavor'/>"/>
 	<entry name="Eldar/ExpertHunter" value="Chasseur expert"/>
 	<entry name="Eldar/ExpertHunterDescription" value="Augmente les dégâts contre les créatures monstrueuses, les véhicules et les fortifications."/>
 	<entry name="Eldar/ExpertHunterFlavor" value="L'armement des Lances étincelantes a peut-être une portée courte, mais leurs lances stellaires et lances laser sont incroyablement puissantes, alors qu'elles tournent autour de cibles plus grandes qu'elles, cherchant leurs points faibles."/>
 	<entry name="Eldar/FoodBuildingBonus" value="Cultivation exquise"/>
 	<entry name="Eldar/FoodBuildingBonusDescription" value="Augmente la production de nourriture."/>
 	<entry name="Eldar/FoodBuildingBonusFlavor" value="Les Aeldaris ressentent tout si intensément que même leurs aliments de base sont des œuvres d'arts, cultivés par des artisans ayant passé des millénaires à perfectionner leur cuisine. Avec un éventail plus large d'ingrédient et d'assaisonnements, ils peuvent créer des miracles avec ce qui semble être des aliments de base."/>
 	<entry name="Eldar/Forceshield" value="Bouclier de force"/>
 	<entry name="Eldar/ForceshieldDescription" value="Augmente la réduction de dégâts."/>
 	<entry name="Eldar/ForceshieldFlavor" value="Cet appareil contient un puissant projecteur de bouclier déflecteur."/>
 	<entry name="Eldar/HeavyWeaponBonus" value="Machine de Vaul"/>
 	<entry name="Eldar/HeavyWeaponBonusDescription" value="Augmente la pénétration d'armure."/>
 	<entry name="Eldar/HeavyWeaponBonusFlavor" value="Alors que l'armement plasma de l'humanité est souvent fatal à ses utilisateurs, les équivalents Aeldaris ne présentent pas cette menace. Et maintenant, une découverte chanceuse sur la surface de Gladius Prime a permis aux Chanteurs de moelle et Exarques d'améliorer encore leur précision."/>
 	<entry name="Eldar/HoloFields" value="Holo-champs"/>
 	<entry name="Eldar/HoloFieldsDescription" value="Augmente la réduction de dégâts si l'unité a bougé ce tour-ci."/>
 	<entry name="Eldar/HoloFieldsFlavor" value="Utilisant l'énergie cinétique pour faire scintiller et déformer la silhouette du véhicule, les holo-champs empêchent l'ennemi de viser avec précision l'engin alors qu'il avance sur le champ de bataille."/>
 	<entry name="Eldar/InescapableAccuracy" value="Une précision inéluctable"/>
	<entry name="Eldar/InescapableAccuracyDescription" value="Augmente la précision contre les motos, les motojets, les avions volants et les véhicules qui se sont déplacés pendant ce tour."/>
	<entry name="Eldar/InescapableAccuracyFlavor" value="Les guerriers de l'Aspect Faucheur sombre du Vaisseau-monde Aeldari ont hérité d'une expérience de combat de plusieurs lustres contre leurs parents, les Drukhari, qui leur a appris à suivre même leurs propres moto-jets, protégés par une holocaméra. Atteindre de telles cibles avec leurs missiles Faucheur mortels est simple pour eux, et fait partie de leur émulation avec Khaine dans son rôle de Destructeur."/>
	<entry name="Eldar/InfantryBuildingBonus" value="Invocations d'Asuryan"/>
 	<entry name="Eldar/InfantryBuildingBonusDescription" value="Augmente la production fournie."/>
 	<entry name="Eldar/InfantryBuildingBonusFlavor" value="Asurmen est le fondateur de la Voie du Guerrier et le premier des Seigneurs Phénix, les créateurs des Temples Aspects. Invoquer son nom apportera sûrement plus de guerriers sur Gladius Prime, pour saisir cette planète au nom des Aeldaris."/>
 	<entry name="Eldar/InfluenceBuildingBonus" value="Oracle de Lileath"/>
 	<entry name="Eldar/InfluenceBuildingBonusDescription" value="Augmente la production d'influence."/>
 	<entry name="Eldar/InfluenceBuildingBonusFlavor" value="Peu de mortels ont jamais eu la prescience d'un Grand prophète — un pouvoir peut-être dérivé de la déesse Aeldari perdue Lileath, qui avait prédit la mort des dieux aux mains des Aeldaris mortels. Un oracle qui invoque son nom attirer les yeux de tous les Aeldaris sur vous…"/>
 	<entry name="Eldar/Jinx" value="Spoliation"/>
 	<entry name="Eldar/JinxDescription" value="Réduit l'armure."/>
 	<entry name="Eldar/JinxFlavor" value="<string name='Actions/Eldar/ProtectJinxFlavor'/>"/>
 	<entry name="Eldar/KhaineAwakened" value="<string name='Actions/Eldar/KhaineAwakened'/>"/>
 	<entry name="Eldar/KhaineAwakenedDescription" value="Augmente les attaques au corps à corps, les dégâts au corps à corps, réduit les pertes de moral et immunise contre la peur et le pilonnage."/>
 	<entry name="Eldar/KhaineAwakenedFlavor" value="<string name='Actions/Eldar/KhaineAwakenedFlavor'/>"/>
 	<entry name="Eldar/KhainesMight" value="<string name='Actions/Eldar/KhainesMight'/>"/>
 	<entry name="Eldar/KhainesMightDescription" value="Augmente les attaques au corps à corps"/>
 	<entry name="Eldar/KhainesMightFlavor" value="<string name='Actions/Eldar/KhainesMightFlavor'/>"/>
 	<entry name="Eldar/LinkedFire" value="<string name='Actions/Eldar/LinkedFire'/>"/>
 	<entry name="Eldar/LinkedFireDescription" value="Augmente les dégâts et la pénétration d'armure du canon à Prisme."/>
 	<entry name="Eldar/LinkedFireFlavor" value="<string name='Actions/Eldar/LinkedFireFlavor'/>"/>
 	<entry name="Eldar/MarksmansEye" value="Oeil du chasseur"/>
 	<entry name="Eldar/MarksmansEyeDescription" value="Augmente la précision."/>
 	<entry name="Eldar/MarksmansEyeFlavor" value="Seul un Chasseur écarlate pourrait considérer le pilotage de l'aéronef le plus rapide et manœuvrable des Aeldaris comme si facile, qu'il est aussi capable de viser et de tirer avec trois armes différentes avec des cadences de tir différentes en même temps."/>
 	<entry name="Eldar/MeleeWeaponBonus" value="Plan sub-moléculaire"/>
 	<entry name="Eldar/MeleeWeaponBonusDescription" value="Augmente la pénétration d'armure"/>
 	<entry name="Eldar/MeleeWeaponBonusFlavor" value="Les filaments monomoléculaires et les lames des Aeldaris sont déjà assez létales par elles-mêmes, mais avec la moindre addition de technologie de phase (comme dans le Baiser d'Arlequin), elles transpercent même l'armure la plus épaisse."/>
 	<entry name="Eldar/MindWar" value="<string name='Actions/Eldar/MindWar'/>"/>
 	<entry name="Eldar/MindWarDescription" value="Diminue la précision."/>
 	<entry name="Eldar/MindWarFlavor" value="<string name='Actions/Eldar/MindWarFlavor'/>"/>
 	<entry name="Eldar/MoltenBody" value="Corps en fusion"/>
 	<entry name="Eldar/MoltenBodyDescription" value="Immunisé aux lance-flammes et fuseurs."/>
 	<entry name="Eldar/MoltenBodyFlavor" value="L'Avatar de Khaine n'est pas juste le dernier fragment d'un dieu mourant—de manière plus pratique, c'est une construction en fer surchauffé dont le centre est littéralement de la lave, animée par la puissance psychique des Aeldaris et de la colère immortelle du dieu. Le faire chauffer ne fait que le rendre plus en colère."/>
 	<entry name="Eldar/OreBuildingBonus" value="Extraction efficace"/>
 	<entry name="Eldar/OreBuildingBonusDescription" value="Augmente la production de minerai."/>
 	<entry name="Eldar/OreBuildingBonusFlavor" value="Les Aeldaris ont moins besoin de métaux que les autres races, étant donné que la plupart de leurs structures et véhicules sont de l'énergie psychique rendues solides, sous la forme de moelle spectrale. Mais pour la faible quantité dont ils ont besoin, il y a toujours des améliorations qui peuvent être faites."/>
 	<entry name="Eldar/PowerField" value="Champ énergétique"/>
 	<entry name="Eldar/PowerFieldDescription" value="Augmente la réduction de dégâts."/>
 	<entry name="Eldar/PowerFieldFlavor" value="Ce champ redirige une portion des réserves d’énergie du véhicule pour émettre un bouclier scintillant capable de dévier les tirs."/>
 	<entry name="Eldar/Protect" value="Protection"/>
 	<entry name="Eldar/ProtectDescription" value="Augmente l'armure."/>
 	<entry name="Eldar/ProtectFlavor" value="<string name='Actions/Eldar/ProtectJinxFlavor'/>"/>
 	<entry name="Eldar/RemnantsOfTheFall" value="Restes de la Chute"/>
 	<entry name="Eldar/RemnantsOfTheFallDescription" value="Diminue le rythme de croissance de la population."/>
 	<entry name="Eldar/RemnantsOfTheFallFlavor" value="Depuis la naissance de Slaanesh, les Aeldaris restants ont lutté pour survivre dans une galaxie hostile. Leur longévité extraordinaire signifie qu'ils ont toujours eu une faible fertilité et une gestation lente, ce qui rend difficile le remplacement de ceux qui tombent au combat. Sur de nombreux Vaisseaux-mondes, les morts sont largement en surnombre par rapport aux vivants."/>
 	<entry name="Eldar/ResearchBuildingBonus" value="Archivistes de la Bibliothèque Interdite"/>
 	<entry name="Eldar/ResearchBuildingBonusDescription" value="Augmente la production de recherche."/>
 	<entry name="Eldar/ResearchBuildingBonusFlavor" value="Les Grands prophète de la Bibliothèque Interdite nous ont envoyé dans cette quête et maintenant, ils nous offrent leur soutien. En ayant accès à leurs archives, nous devrions être capable de redécouvrir des technologies perdues, et améliorer plus rapidement notre tête-de-pont sur cette planète."/>
 	<entry name="Eldar/ReturnOfTheAeldari" value="<string name='Actions/Eldar/ReturnOfTheAeldari'/>"/>
 	<entry name="Eldar/ReturnOfTheAeldariDescription" value="<string name='Actions/Eldar/ReturnOfTheAeldariDescription'/>"/>
 	<entry name="Eldar/ReturnOfTheAeldariFlavor" value="<string name='Actions/Eldar/ReturnOfTheAeldariFlavor'/>"/>
 	<entry name="Eldar/ReaperRangefinder" value="Télémètre Faucheur"/>
    <entry name="Eldar/ReaperRangefinderDescription" value="Ignore la réduction des dégâts à distance accordée par l'esquive et la compétence d'esquive."/>
    <entry name="Eldar/ReaperRangefinderFlavor" value="Les ailettes du casque des faucheurs sombres sont équipées d'un système de ciblage très perfectionné qui calcule les télémétries en un clin d'œil."/>
	<entry name="Eldar/RuneArmour" value="Armure runique"/>
 	<entry name="Eldar/RuneArmourDescription" value="Augmente la réduction de dégâts."/>
 	<entry name="Eldar/RuneArmourFlavor" value="Les psykers Aeldaris conçoivent d'élégantes armures décorées avec des runes en moelle spectrale pour eux. On dit que l'armure runique bat en unisson avec le cœur de son porteur, et qu'elle offre une protection contre les attaques physiques et spirituelles."/>
 	<entry name="Eldar/Scattershield" value="Bouclier rayonnant"/>
 	<entry name="Eldar/ScattershieldDescription" value="Augmente la réduction des dégâts et aveugle les attaquants de mêlée de l'infanterie et des créatures monstrueuses."/>
 	<entry name="Eldar/ScattershieldFlavor" value="Utilisé pour protéger les plus précieuses créations mécaniques des Aeldaris, le bouclier rayonnant est un générateur en forme d’éventail qui convertit l'énergie des tirs adverses en champ de force multicolore aveuglant."/>
 	<entry name="Eldar/SerpentShield" value="Bouclier-serpent"/>
 	<entry name="Eldar/SerpentShieldDescription" value="Augmente l'armure tant que l'arme Bouclier-serpent n'est pas en recharge."/>
 	<entry name="Eldar/SerpentShieldFlavor" value="<string name='Weapons/SerpentShieldFlavor'/>"/>
 	<entry name="Eldar/Skyhunter" value="Nemrod des cieux"/>
 	<entry name="Eldar/SkyhunterDescription" value="Augmente la pénétration d'armure contre les unités volantes."/>
 	<entry name="Eldar/SkyhunterFlavor" value="L'esprit d'une Exarque perdue de Biel-Tan est entrée dans nos Chasseurs écarlates, révélant des secrets de millénaires de combat contre les ennemis des Aeldaris. Sa sagesse redécouverte va nous permettre de dominer les cieux comme jamais avant."/>
	<entry name="Eldar/SpiritMark" value="<string name='Actions/Eldar/SpiritMark'/>"/>
 	<entry name="Eldar/SpiritMarkDescription" value="Augmente la précision"/>
 	<entry name="Eldar/SpiritMarkFlavor" value="<string name='Actions/Eldar/SpiritMarkFlavor'/>"/>
 	<entry name="Eldar/SpiritPreservation" value="Préservation de l'esprit"/>
 	<entry name="Eldar/SpiritPreservationDescription" value="Donne de l'énergie à la mort."/>
 	<entry name="Eldar/SpiritPreservationFlavor" value="Nous sommes peu nombreux, et l'Assoiffée attend dans le Warp, pour dévorer nos âmes. Cependant, nous portons des Pierres-esprits pour capturer notre essence au moment de notre mort; bien que tous les Aeldaris tremblent de dégoût à cette idée, lorsqu'ils sont récupérés, ces esprits peuvent être utilisés pour guider nos machines de guerre."/>
 	<entry name="Eldar/SpiritStones" value="Pierres-esprits"/>
 	<entry name="Eldar/SpiritStonesDescription" value="Réduit les pertes de moral."/>
 	<entry name="Eldar/SpiritStonesFlavor" value="La coque de certains véhicules Aeldaris est sertie de grandes pierres-esprits pugnaces qui peuvent prendre le contrôle en cas de défaillance."/>
 	<entry name="Eldar/StarEngines" value="Moteurs stellaires"/>
 	<entry name="Eldar/StarEnginesDescription" value="Augmente le mouvement."/>
 	<entry name="Eldar/StarEnginesFlavor" value="Tous les véhicules Aeldaris sont agiles, mais ceux qui sont équipés de moteurs stellaires atteignent des vitesses inimaginables.Les races inférieures ne peuvent que s'émerveiller devant cette vitesse et manœuvrabilité phénoménales."/>
 	<entry name="Eldar/TitanHoloFields" value="Holo-champs de titans"/>
 	<entry name="Eldar/TitanHoloFieldsDescription" value="<string name='Traits/Eldar/HoloFieldsDescription'/>"/>
 	<entry name="Eldar/TitanHoloFieldsFlavor" value="“Je regardais le meilleur-meilleur des spectacles dans la Ruche, avec plein de 'tites étincelles et de boum-boums et de trucs brillants et siiii psychédélique… Puis je me suis tourné ébahi, et les Irréguliers étaient morts-morts et le char extra-terrestre a juste roulé devant moi comme si j'étais de la plasti-pâte.”<br/>  — Trooper Grande, seul survivant du quatrième régiment d'Irréguliers de Necromunda"/>
 	<entry name="Eldar/TranscendentBliss" value="<string name='Actions/Eldar/TranscendentBliss'/>"/>
 	<entry name="Eldar/TranscendentBlissDescription" value="<string name='Actions/Eldar/TranscendentBlissDescription'/>"/>
 	<entry name="Eldar/TranscendentBlissFlavor" value="<string name='Actions/Eldar/TranscendentBlissFlavor'/>"/>
 	<entry name="Eldar/VectorDancer" value="<string name='Actions/Eldar/VectorDancer'/>"/>
 	<entry name="Eldar/VectorDancerDescription" value="<string name='Actions/Eldar/VectorDancerDescription'/>"/>
 	<entry name="Eldar/VectorDancerFlavor" value="<string name='Actions/Eldar/VectorDancerFlavor'/>"/>
 	<entry name="Eldar/VectoredEngines" value="<string name='Actions/Eldar/VectoredEngines'/>"/>
 	<entry name="Eldar/VectoredEnginesDescription" value="<string name='Actions/Eldar/VectoredEnginesDescription'/>"/>
 	<entry name="Eldar/VectoredEnginesFlavor" value="<string name='Actions/Eldar/VectoredEnginesFlavor'/>"/>
 	<entry name="Eldar/VehicleBuildingBonus" value="Invocations de Vaul"/>
 	<entry name="Eldar/VehicleBuildingBonusDescription" value="Augmente la production fournie."/>
 	<entry name="Eldar/VehicleBuildingBonusFlavor" value="Une pierre-esprit récupérée sur Gladius s'est trouvée être celle d'un ancien Chanteur de moelle, perdu ici il y a des millénaires. Grâce à ses connaissances sur la structure et les ressources de la planète, nous sommes capables d'augmenter de manière significative notre production."/>
	<entry name="Enslaved" value="Asservi"/>
	<entry name="EnslavedDescription" value="Tuer l'Asservisseur pour libérer l'unité"/>
	<entry name="EreWeGo" value="Cé parti!"/>
	<entry name="EreWeGoDescription" value="Augmente le mouvement."/>
	<entry name="EreWeGoFlavor" value="<string name='Actions/EreWeGoFlavor'/>"/>
	<entry name="ExtraInfantryArmour" value="Armure supplémentaire d'infanterie"/>
	<entry name="ExtraInfantryArmourDescription" value="Augmente l'armure."/>
	<entry name="ExtraMonstrousCreatureArmour" value="Armure supplémentaire de créature monstrueuse"/>
	<entry name="ExtraMonstrousCreatureArmourDescription" value="Augmente l'armure"/>
	<entry name="ExtraVehicleArmour" value="Blindage supplémentaire de véhicule"/>
	<entry name="ExtraVehicleArmourDescription" value="Augmente l'armure"/>
	<entry name="Fear" value="Peur"/>
	<entry name="FearDescription" value="Réduit le moral tous les tours."/>
	<entry name="FearFlavor" value="<string name='Actions/AuraOfFearFlavor'/>"/>
	<entry name="Fearless" value="Intrépide"/>
	<entry name="FearlessDescription" value="Réduit les pertes de moral et confère l'immunité à la peur et au pilonnage."/>
	<entry name="FearlessFlavor" value="Les soldats intrépides n'abandonnent jamais et n'utilisent que rarement le couvert—même quand il serait plus sage de le faire."/>
	<entry name="FeelNoPain" value="Insensible à la douleur"/>
	<entry name="FeelNoPainDescription" value="Augmente la réduction de dégâts"/>
	<entry name="FeelNoPainFlavor" value="Que ce soit par la force de sa volonté, avec des augmentations bioniques ou une vile sorcellerie, ce guerrier peut combattre malgré des blessures terrifiantes."/>
	<entry name="Flail" value="Fléau"/>
	<entry name="FlailDescription" value="Augmente la réduction de dégâts au corps-à-corps."/>
	<entry name="FlailFlavor" value="Armé d'un fléau, ces guerriers gênent ses agresseurs lorsqu'ils s'approchent."/>
 	<entry name="Flame" value="Lance-flammes"/>
 	<entry name="FlameDescription" value="Classification."/>
	<entry name="Fleet" value="Course"/>
	<entry name="FleetDescription" value="Augmente le mouvement"/>
	<entry name="FleetFlavor" value="Ces guerriers à l'agilité surnaturelle peuvent couvrir plus de terrain que leurs ennemis moins mobiles."/>
	<entry name="Fleshbane" value="Fléau de la chair"/>
	<entry name="FleshbaneDescription" value="Augmente les dégâts contre l'infanterie et les créatures monstrueuses."/>
	<entry name="Flyer" value="Volant"/>
	<entry name="FlyerDescription" value="Peut voler au-dessus des falaises, de l'eau et des unités ennemies. Ne peut pas capturer les artefacts et les avant-postes. Ignore les zones de contrôle des unités ennemis au sol. Ne peut pas être touché par une arme de corps-à-corps d'une unité au sol et est difficile à toucher avec des armes à distance sans 'antiaérien'. Ne subit pas de pénalité pour utiliser les armes lourdes et l'artillerie."/>
	<entry name="FlyerFlavor" value="L'espace aérien au-dessus des affrontements est plein d'activité. Des chasseurs et des bombardiers se lancent dans les cieux, s'affrontant les uns les autres tout en fournissant des tirs de soutien aux troupes au sol."/>
	<entry name="Forest" value="Forêt"/>
	<entry name="ForestDescription" value="Augmente la réduction de dégâts à distance pour l'infanterie."/>
	<entry name="ForestFlavor" value="<string name='Features/ForestFlavor'/>"/>
	<entry name="ForestStealth" value="Camouflage forestier"/>
	<entry name="ForestStealthDescription" value="Augmente la réduction de dégâts à distance en forêt."/>
	<entry name="Fortification" value="Fortification"/>
	<entry name="FortificationDescription" value="Emplacement stationnaire et lourdement fortifié qui contrôle les avant-postes adjacents s'il est armé."/>
	<entry name="FullThrottle" value="“Plein gaz !”"/>
	<entry name="FullThrottleDescription" value="Augmente le mouvement"/>
	<entry name="FuelledByRage" value="Alimenté par la colère"/>
	<entry name="FuelledByRageDescription" value="Augmente les attaques lorsque les points de vie de l'unité diminuent."/>
	<entry name="FuriousCharge" value="Charge furieuse"/>
	<entry name="FuriousChargeDescription" value="Augmente les dégâts au corps-à-corps."/>
	<entry name="FuriousChargeFlavor" value="Certains guerriers utilisent l'élan de leur charge pour alimenter leur propre colère."/>
	<entry name="Gargantuan" value="Gargantuesque"/>
	<entry name="GargantuanDescription" value="Classification."/>
	<entry name="GargantuanFlavor" value="Les créatures gargantuesques sont d'une taille tellement immense qu'elles peuvent affronter des armées entières à elles seules. Elles dominent le champ de bataille, faisant trembler le sol en avançant vers leurs ennemis, écrasant les créatures plus petites sous leur pieds alors qu'elles avancent pesamment."/>
	<entry name="Gauss" value="Arme à fission"/>
	<entry name="GaussDescription" value="Augmente les dégâts minimums en fonction des points de vie la cible."/>
	<entry name="GaussFlavor" value="<string name='Weapons/GaussFlavor'/>"/>
	<entry name="GaussDamage" value="Écorcheurs atomiques"/>
	<entry name="GaussDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="GaussDamageFlavor" value="Dans une galaxie remplie d'armements cruels, monstrueux ou fous, la technologie de fission est particulièrement crainte. Un projectile de bolter ne fera qu'exploser en vous, mais une arme à fission vous écorchera vivant, atome par atome."/>
	<entry name="GetsHot" value="Surchauffe"/>
	<entry name="GetsHotDescription" value="L'unité perd des points de vie à chaque fois qu'elle utilise une arme à distance."/>
	<entry name="GetsHotFlavor" value="Certaines armes sont alimentées par une source d'énergie instable et risquent de surchauffer à chaque tir—souvent au détriment de l'utilisateur."/>
	<entry name="Graviton" value="Graviton"/>
	<entry name="GravitonDescription" value="Modifie les dégâts en fonction de l'armure de la cible."/>
	<entry name="GravitonFlavor" value="Certaines armes écrasent les ennemis avec leur propre armure."/>
	<entry name="Grenade" value="Grenade"/>
	<entry name="GrenadeDescription" value="Classification."/>
	<entry name="GrotRiggers" value="Grots bidouilleurs"/>
	<entry name="GrotRiggersDescription" value="Restaure des points de vie tous les tours."/>
	<entry name="GrotRiggersFlavor" value="Que ce soit en re-attachant des bidules avec des riveteuses, ou juste en descendant pour pousser, un équipage de grots bidouilleurs peut aider à garder un véhicule Ork en état de combattre plus longtemps qu'il ne l'aurait dû."/>
	<entry name="GroundAttack" value="Attaque au sol"/>
	<entry name="GroundAttackDescription" value="Ne peut attaquer que des unités terrestres."/>
	<entry name="GunnersKillOnSight" value="“Tireurs, tuez à vue !”"/>
	<entry name="GunnersKillOnSightDescription" value="Augmente les attaques à distance."/>
	<entry name="HammerOfWrath" value="<string name='Actions/HammerOfWrath'/>"/>
	<entry name="HammerOfWrathDescription" value="<string name='Actions/HammerOfWrathDescription'/>"/>
	<entry name="HammerOfWrathFlavor" value="<string name='Actions/HammerOfWrathFlavor'/>"/>
	<entry name="Hammerhand" value="<string name='Actions/Hammerhand'/>"/>
 	<entry name="HammerhandDescription" value="<string name='Actions/HammerhandDescription'/>"/>
 	<entry name="HammerhandFlavor" value="<string name='Actions/HammerhandFlavor'/>"/>
 	<entry name="Hallucination" value="<string name='Actions/Hallucination'/>"/>
 	<entry name="HallucinationDescription" value="Diminue les attaques."/>
 	<entry name="HallucinationFlavor" value="<string name='Actions/HallucinationFlavor'/>"/>
	<entry name="HarvestResourceFeatures" value="Récolte"/>
	<entry name="HarvestResourceFeaturesDescription" value="Récolte les ressources provenant des avant-postes."/>
	<entry name="Haywire" value="Disruption"/>
	<entry name="HaywireDescription" value="Augmente les dégâts et ignore l'armure contre les véhicules et les fortifications."/>
	<entry name="HaywireFlavor" value="Les armes disruptives utilisent de puissantes pulsions électromagnétiques."/>
	<entry name="Headquarters" value="Quartier général"/>
	<entry name="HeadquartersDescription" value="Détruire le quartier-général détruit toute la ville."/>
	<entry name="HeavyWeapon" value="Arme lourde"/>
	<entry name="HeavyWeaponDescription" value="Réduit la précision après avoir bougé."/>
	<entry name="HellstormTemplate" value="Fournaise"/>
	<entry name="HellstormTemplateDescription" value="<string name='Traits/BeamDescription'/>"/>
	<entry name="Hero" value="Héros"/>
	<entry name="HeroDescription" value="Augmente la réduction de dégâts si le héro est une unité d'infanterie."/>
	<entry name="HeroFlavor" value="Qu'est-ce que c'est d'être un héros? Beaucoup définiraient ça comme un représentant particulièrement brave, intelligent ou fort d'une espèce. Dans un univers de guerre infinie, ça a malheureusement tendance à désigner quelqu'un de vraiment très bon à tuer ses ennemis et à survivre d'une manière ou d'un autre."/>
	<entry name="HighPower" value="Pleine puissance"/>
	<entry name="HighPowerDescription" value="Actif avant de bouger."/>
	<entry name="HitAndRun" value="Désengagement"/>
	<entry name="HitAndRunDescription" value="Ignore les zones de contrôle de l'ennemi."/>
	<entry name="HitAndRunFlavor" value="Certaines unités emploient une méthode de combat flexible, engageant un ennemi au corps-à-corps un moment, avant de se retirer pour frapper avec une vigueur renouvelée l'instant d'après."/>
	<entry name="Homing" value="Autoguidé"/>
	<entry name="HomingDescription" value="Ne nécessite pas de ligne de vue"/>
	<entry name="IgnoresCover" value="Ignores le couvert"/>
	<entry name="IgnoresCoverDescription" value="Ignore la réduction de dégâts à distance de l'unité."/>
	<entry name="IgnoresCoverFlavor" value="Cette arme tire des munitions qui privent l'ennemi de son abri."/>
	<entry name="Illuminated" value="Illuminé"/>
	<entry name="IlluminatedDescription" value="Réduit la réduction des dégâts à distance."/>
	<entry name="Immobilized" value="Immobilisé"/>
	<entry name="ImmobilizedDescription" value="L'unité ne peut pas bouger."/>
	<entry name="ImperialRuin" value="<string name='Features/ImperialRuin'/>"/>
	<entry name="ImperialRuinDescription" value="Augmente la réduction de dégâts à distance pour les unités d'infanterie."/>
	<entry name="ImperialRuinFlavor" value="<string name='Features/ImperialRuinFlavor'/>"/>
	<entry name="ImperialSplendour" value="Splendeur impériale"/>
	<entry name="ImperialSplendourDescription" value="Augmente l'influence"/>
	<entry name="ImperialSplendourFlavor" value="Un gouverneur planétaire couronné de succès montrera la richesse de son domaine avec des monuments à l'Imperium—Des basiliques titanesques en plasto-béton et des statues de Space Marines, des Primarques et de l'Empereur."/>
	<entry name="Infiltrate" value="Infiltration"/>
	<entry name="InfiltrateDescription" value="L'unité ne peut pas subir de tirs en état d'alerte."/>
	<entry name="InfiltrateFlavor" value="De nombreuses armées utilisent des unités de reconnaissance qui restent cachées pendant des jours, attendant le bon moment pour frapper."/>
	<entry name="InstantDeath" value="Mort instantanée"/>
 	<entry name="InstantDeathDescription" value="Augmente considérablement les dégâts contre les unités d'infanterie et de créatures monstrueuses."/>
 	<entry name="InstantDeathFlavor" value="Certains coups peuvent tuer purement et simplement un ennemi, quelle que soit sa résistance."/>
	<entry name="Invulnerable" value="Invulnérable"/>
	<entry name="InvulnerableDescription" value="Cette unité est invulnérable."/>
	<entry name="IronHalo" value="Halo de fer"/>
	<entry name="IronHaloDescription" value="Augmente la réduction de dégâts."/>
	<entry name="IronHaloFlavor" value="Le halo de fer est un honneur accordé aux commandants Space Marines et un symbole de leur courage et sagesse exceptionnels. Habituellement montés sur le paquetage dorsal ou dans un gorgerin, ces engins irradient un champ énergétique qui protège contre même les armes ennemies les plus puissantes."/>
	<entry name="IronWill" value="Volonté de fer"/>
	<entry name="IronWillDescription" value="<string name='Actions/IronWillDescription'/>"/>
	<entry name="IronWillFlavor" value="<string name='Actions/IronWillFlavor'/>"/>
	<entry name="ItWillNotDie" value="Il est invincible!"/>
	<entry name="ItWillNotDieDescription" value="Restaure des points de vie tous les tours."/>
	<entry name="ItWillNotDieFlavor" value="Dans les coins sombres de la galaxie, il y a des créatures qui se soignent à une vitesse terrifiante."/>
	<entry name="JetPack" value="Réacteurs dorsaux"/>
	<entry name="JetPackDescription" value="Cette unité peut se déplacer sur l'eau. Elle ignore les pénalités des rivières et des herbes barbelées."/>
	<entry name="JetPackFlavor" value="Les réacteurs dorsaux sont conçus pour fournir des plateformes de tir stables plutôt qu'un moyen d'aller au corps-à-corps."/>
	<entry name="Jetbike" value="Motojet"/>
	<entry name="JetbikeDescription" value="Cette unité peut se déplacer sur l'eau. Elle ignore les pénalités des rivières et des herbes barbelées."/>
	<entry name="JetbikeFlavor" value="La technologie pour fabriquer de petits engins anti-grav fiables et rapides est plus difficile qu'il n'y paraît, et pendant de nombreuses années, seuls les Aeldaris avaient le savoir-faire technique. Plus récemment, les Orks ont développé leur propre version pesante, les Kopters, et les Nécrons émergeants ont apporté avec eux les imprédictibles Mécanoptères."/>
 	<entry name="Jink" value="<string name='Actions/Jink'/>"/>
 	<entry name="JinkDescription" value="Augment la réduction de dégâts à distance mais réduit la précision."/>
	<entry name="JinkFlavor" value="<string name='Actions/JinkFlavor'/>"/>
	<entry name="Killshot" value="Tir fatal"/>
	<entry name="KillshotDescription" value="Augmente les dégâts contre les créatures monstrueuses, les véhicules et les fortifications lorsqu'adjacent à un Predator allié."/>
	<entry name="KillshotFlavor" value="Il y a peu de choses qui peuvent résister à la puissance combiné d'un Escadron Assassins—un trio de Predators—leurs tireurs Space Marines crééent des zones de tir imbriquées pour abattre même les créatures Xenos les plus massives ou des grandes machines de guerre dans des tirs de barrage concentrés."/>
	<entry name="Lance" value="Arme à rayon"/>
	<entry name="LanceDescription" value="Limite l'armure de la cible."/>
	<entry name="LanceFlavor" value="La terreur des chefs de char, une arme à rayon tirant un faisceau concentré d'énergie qui peut traverser n'importe quelle armure, indépendamment de son épaisseur."/>
	<entry name="LargeBlast" value="Grande explosion"/>
	<entry name="LargeBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="LargeBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="LastStand" value="Dernier carré"/>
	<entry name="LastStandDescription" value="Augmente le moral."/>
	<entry name="LastStandFlavor" value="“Ici nous nous tenons, et ici nous mourrons, fiers et insoumis. Bien que la main de la mort elle-même vienne pour nous, nous cracherons notre défi jusqu'à la fin!”<br/>  — Chapelain Armand Titus des Griffons hurlants"/>
	<entry name="LifeDrain" value="Drain de vie"/>
	<entry name="LifeDrainDescription" value="Augmente les dégâts contre l'infanterie et les créatures monstrueuses."/>
	<entry name="LifeSteal" value="Vol de vie"/>
	<entry name="LifeStealDescription" value="Convertit les dégâts en soin pour cette unité et les unités d'infanterie et les créatures monstrueuses à proximité."/>
	<entry name="LinebreakerBombardment" value="Bombardement briseur de lignes"/>
	<entry name="LinebreakerBombardmentDescription" value="Ignore la réduction de dégâts à distance de la cible lorsqu'adjacent à un Vindicator allié."/>
	<entry name="Luminagen" value="Luminagène"/>
	<entry name="LuminagenDescription" value="Réduit temporairement la réduction de dégâts à distance de la cible."/>
	<entry name="LocatorBeacon" value="Balise de localisation"/>
	<entry name="LocatorBeaconDescription" value="Le déploiement orbital ne consomme pas les points d'actions si utilisé sur une case adjacente à cette unité."/>
	<entry name="LocatorBeaconFlavor" value="Les balises de localisation sont souvent portés par des motards scouts ou montés sur des modules d'atterrissage et fournissent un ensemble de signaux, des communications à large spectre et des géopositionnements. Quand elles sont activées, les balises envoient ces informations de position détaillées au relais tactique des Space Marines afin que ceux-ci expédient leurs réserves avec précision."/>
	<entry name="LowPower" value="Puissance réduite"/>
	<entry name="LowPowerDescription" value="Actif après avoir bougé."/>
	<entry name="MachineEmpathy" value="Empathie avec machine"/>
	<entry name="MachineEmpathyDescription" value="Restaure des points de vie."/>
	<entry name="MachineEmpathyFlavor" value="<string name='Actions/MachineEmpathyFlavor'/>"/>
	<entry name="MannedWeapon" value="Arme d'équipage"/>
	<entry name="MannedWeaponDescription" value="Requiert du cargo pour tirer."/>
	<entry name="MassiveBlast" value="Explosion massive"/>
	<entry name="MassiveBlastDescription" value="<string name='Traits/BlastDescription'/>"/>
	<entry name="MassiveBlastFlavor" value="<string name='Weapons/BlastFlavor'/>"/>
	<entry name="MasterCrafted" value="Artisanat de Maître"/>
	<entry name="MasterCraftedDescription" value="Augmente la précision."/>
	<entry name="MasterCraftedFlavor" value="Certaines armes sont des artefacts entretenus avec amour, fabriqués avec un savoir-faire aujourd'hui perdu. Bien que la forme exacte de l'artisanat de maître varie, elle est toujours considérée comme le sommet de l'art de l'armurier."/>	
	<entry name="Melee" value="Corps-à-corps"/>
	<entry name="MeleeDescription" value="Ne peut pas effectuer de tirs en état d'alerte."/>
	<entry name="MeleeFlavor" value="<string name='Weapons/MeleeFlavor'/>"/>
	<entry name="Melta" value="Fuseur"/>
	<entry name="MeltaDescription" value="Augmente la pénétration d'armure à mi-distance."/>
	<entry name="MeltaFlavor" value="<string name='Weapons/MeltaFlavor'/>"/>
	<entry name="MindControl" value="Contrôle mental"/>
	<entry name="MindControlDescription" value="Réduit le moral tous les tours."/>
	<entry name="Misfortune" value="<string name='Actions/Misfortune'/>"/>
	<entry name="MisfortuneDescription" value="Augmente les dégâts subis."/>
	<entry name="MisfortuneFlavor" value="<string name='Actions/MisfortuneFlavor'/>"/>
	<entry name="Missing" value="Manquant"/>
	<entry name="MobRule" value="Mouvement de foule"/>
	<entry name="MobRuleDescription" value="Réduite la perte de moral selon le nombre d'unités alliées aux alentours."/>
	<entry name="MobRuleFlavor" value="Les Orks sont des créatures simplistes et brutales qui aiment se battre et tirent leur confiance dans la force du nombre."/>
	<entry name="MobileCommand" value="Commandement mobile"/>
	<entry name="MobileCommandDescription" value="Les capacités passives fonctionnent pour le cargo embarqué dans ce transport."/>
	<entry name="MobileCommandFlavor" value="Un véhicule de transport est habituellement scellé, rendant les troupes inefficaces (mais protégées) jusqu'à ce qu'elles atteignent leur destination. Contrairement aux meurtrières, l'équipement de communication ne compromet pas la protection comme le prouve la variante de commandent de la Chimère Impériale, permettant aux commandants de diriger leurs forces."/>
 	<entry name="Monofilament" value="Monofilament"/>
 	<entry name="MonofilamentDescription" value="Inverse la précision en fonction du mouvement maximal de l'unité cible. Augmente la pénétration de l'armure."/>
 	<entry name="MonofilamentFlavor" value="<string name='Weapons/MonofilamentFlavor'/>"/>
	<entry name="MonstrousCreature" value="Créature Monstrueuse"/>
	<entry name="MonstrousCreatureDescription" value="Classification."/>
	<entry name="MonstrousCreatureFlavor" value="Ce sont des géants imposants capables de broyer un char – comme le Carnifex Tyranide, une créature ayant évolué par bio-ingénierie pour devenir un bélier vivant."/>
	<entry name="MoraleSoak" value="Domination psychique"/>
	<entry name="MoraleSoakDescription" value="Réduit les dégâts en utilisant le moral de la cible."/>
	<entry name="MoveThroughCover" value="Déplacement à couvert"/>
	<entry name="MoveThroughCoverDescription" value="Ignore la pénalité de mouvement dans les forêts et les ruines impériales."/>
	<entry name="MoveThroughCoverFlavor" value="Certains guerriers sont doués pour se déplacer sur du terrain instable."/>
	<entry name="Necrons/AircraftBuildingBonus" value="Austérité orphique"/>
	<entry name="Necrons/AircraftBuildingBonusDescription" value="Augmente la production fournie."/>
	<entry name="Necrons/AircraftBuildingBonusFlavor" value="Cette étendue inhumaine de pierre semble désormais se plier de manière impossible, remplissant davantage d'espace qu'il n'y en a. Les véhicules volants Nécrons s'étirent et vacillent entre existence et inexistence lorsqu'ils la traversent."/>
	<entry name="Necrons/AttackCityBonus" value="Ciblage statique"/>
	<entry name="Necrons/AttackCityBonusDescription" value="Augmente la précision contre les unités dans les villes."/>
	<entry name="Necrons/AttackCityBonusFlavor" value="La technologie des Nécrons a beau avoir été récupéré d'anciennes tombes poussiéreuses, mais peu de races dans les 60 derniers millions d'années ont atteint leur prouesse ne manipulation de variables dimensionnelles. Aucun mur, bunker ou grille laser n'est une défense contre de telles armes."/>
	<entry name="Necrons/BlastDamage" value="Ciblage dispersé"/>
	<entry name="Necrons/BlastDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Necrons/BlastDamageFlavor" value="Les systèmes de visée humains ont tendance à se concentrer sur la précision, rétrécissant la dispersion des tirs de leurs armes. De manière contre-intuitive, les systèmes de visée Nécrons les plus avancés diffusent le tir de l'arme et réussissent tout de même à augmenter la probabilité que chaque tir touche."/>
	<entry name="Necrons/BloodyCrusade" value="<string name='Actions/Necrons/BloodyCrusade'/>"/>
	<entry name="Necrons/BloodyCrusadeDescription" value="Augmente les dégâts."/>
	<entry name="Necrons/CityTier2" value="Excavation préliminaire"/>
	<entry name="Necrons/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="Necrons/CityTier2Flavor" value="Davantage de l'ancienne ville a été découverte par les esclaves, qui ont désormais arrêté de creuser et s'attèlent à la restauration des bâtiments enfouis longtemps."/>
	<entry name="Necrons/CityTier3" value="Ville déterrée"/>
	<entry name="Necrons/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="Necrons/CityTier3Flavor" value="L'intégralité de la nécropole enterrée est désormais révélée, resplendissante dans son horreur hallucinante."/>
	<entry name="Necrons/ConstructionBuildingBonus" value="Amélioration des esclaves"/>
	<entry name="Necrons/ConstructionBuildingBonusDescription" value="Augmente la production fournie."/>
	<entry name="Necrons/ConstructionBuildingBonusFlavor" value="Des modifications mineures sur les corps et les esprits de vos esclaves leur permettent de dormir moins et de travailler plus, aussi longtemps que leurs corps fragiles durent."/>
	<entry name="Necrons/CtanNecrodermis" value="Nécroderme C'tan"/>
	<entry name="Necrons/CtanNecrodermisDescription" value="Augmente la réduction de dégât"/>
	<entry name="Necrons/CtanNecrodermisBlast" value="Explosion de nécroderme C'tan"/>
	<entry name="Necrons/CtanNecrodermisBlastDescription" value="Inflige des dégâts aux unités proches lors de la mort."/>
	<entry name="Necrons/DefensiveProtocols" value="<string name='Actions/Necrons/DefensiveProtocols'/>"/>
	<entry name="Necrons/DefensiveProtocolsDescription" value="Augmente l'armure."/>
	<entry name="Necrons/DestructionProtocols" value="<string name='Actions/Necrons/DestructionProtocols'/>"/>
	<entry name="Necrons/DestructionProtocolsDescription" value="Augmente les dégâts subis."/>
	<entry name="Necrons/Dynasty" value="<string name='Actions/Necrons/Dynasty'/>"/>
	<entry name="Necrons/DynastyDescription" value="<string name='Actions/Necrons/DynastyDescription'/>"/>
	<entry name="Necrons/EnergyBuildingBonus" value="Accumulateurs empyréens"/>
	<entry name="Necrons/EnergyBuildingBonusDescription" value="Augmente la production d'énergie."/>
	<entry name="Necrons/EnergyBuildingBonusFlavor" value="Peu de choses ont changé à l'extérieur de ce sarcophage baroque—pourtant dans son intérieur étrange, de sombres appareils Cryptek stabilisent un appareil nettement amélioré."/>
	<entry name="Necrons/EntropicStrike" value="Frappe entropique"/>
	<entry name="Necrons/EntropicStrikeDescription" value="Augmente les dégâts minimums en fonction des points de vie de la cible."/>
	<entry name="Necrons/EternityGate" value="Porte d'éternité"/>
	<entry name="Necrons/EternityGateDescription" value="Les unités Nécrons peuvent se téléporter sur les cases adjacentes."/>
	<entry name="Necrons/EternityGateFlavor" value="La porte d'éternité d'un Monolithe est un couloir dimensionnel entre le champ de bataille et un monde tombe, permettant à des légions de guerriers nécron de traverser de vastes distances et d'entrer dans la mêlée en un seul pas."/>
	<entry name="Necrons/GloomPrism" value="<string name='Actions/Necrons/GloomPrism'/>"/>
	<entry name="Necrons/GloomPrismDescription" value="Augmente la réduction de dégâts de décharge psy."/>
	<entry name="Necrons/GloomPrismFlavor" value="<string name='Actions/Necrons/GloomPrismFlavor'/>"/>
	<entry name="Necrons/GravityPulse" value="<string name='Actions/Necrons/GravityPulse'/>"/>
	<entry name="Necrons/GravityPulseDescription" value="Réduit le mouvement de et inflige des dégâts aux unités volantes, motojets et antigrav à portée."/>
	<entry name="Necrons/GrowthBonus" value="Rituels de réanimation"/>
	<entry name="Necrons/GrowthBonusDescription" value="Augmente le rythme de croissance de la population."/>
	<entry name="Necrons/GrowthBonusFlavor" value="Établir des protocoles plus efficaces pour récupérer les Nécrons enterrés depuis longtemps dans leurs tombes réduit les pertes des excavateurs esclaves maladroits."/>
	<entry name="Necrons/HardwiredForDestruction" value="Cablé pour détruire"/>
 	<entry name="Necrons/HardwiredForDestructionDescription" value="Augmente la précision."/>
 	<entry name="Necrons/HardwiredForDestructionFlavor" value="Les Nécrons du Culte du Destructeur sont des moissonneurs de vivants animés par la haine et obsédés par l'éradication de tous les êtres sensibles. Ils chargent les Crypteks de modifier leurs corps pour se transformer en machines à tuer optimisées. Sur Gladius Prime, les Destructeurs Skorpekh, récemment ressuscités, en font la parfaite démonstration en assénant des coups d'une précision brutale à l'aide de leurs grandes lames."/>
	<entry name="Necrons/HuntersFromHyperspace" value="<string name='Actions/Necrons/HuntersFromHyperspace'/>"/>
    <entry name="Necrons/HuntersFromHyperspaceDescription" value="<string name='Actions/Necrons/HuntersFromHyperspaceDescription'/>"/>
    <entry name="Necrons/HuntersFromHyperspaceFlavor" value="<string name='Actions/Necrons/HuntersFromHyperspaceFlavor'/>"/>
	<entry name="Necrons/HousingBuildingBonus" value="Compression des abris"/>
	<entry name="Necrons/HousingBuildingBonusDescription" value="Augmente la limite de population"/>
	<entry name="Necrons/HousingBuildingBonusFlavor" value="En altérant les contraintes dimensionnelles de chaque abri, bien plus de Nécrons peuvent être accueillis pour être réparés ou subir une maintenance."/>
	<entry name="Necrons/ImmuneToNaturalLaw" value="Immunisés aux lois de la nature"/>
	<entry name="Necrons/ImmuneToNaturalLawDescription" value="Cette unité peut bouger sur l'eau, à travers les unités ennemies et ignore les pénalités des rivières et de l'herbe barbelée."/>
	<entry name="Necrons/InfantryBuildingBonus" value="Raffinement des noyaux"/>
	<entry name="Necrons/InfantryBuildingBonusDescription" value="Augmente la production fournie."/>
	<entry name="Necrons/InfantryBuildingBonusFlavor" value="Avec de meilleurs outils et moins de contraintes, les Cryptek et Mécarachnides sont capables de récupérer une plus grande proportion des Nécrons enfouis, et plus vite."/>
	<entry name="Necrons/InfluenceBuildingBonus" value="Inscriptions hantées"/>
	<entry name="Necrons/InfluenceBuildingBonusDescription" value="Augmente la production d'influence."/>
	<entry name="Necrons/InfluenceBuildingBonusFlavor" value="Malheur au mortel qui poserait l’œil sur ces inscriptions—leurs angles nets semblent onduler lorsqu'ils sont observés du coin de l’œil, induisant la peur dans les cœurs humains."/>
	<entry name="Necrons/InvasionBeams" value="Rayons d'invasion"/>
	<entry name="Necrons/InvasionBeamsDescription" value="Débarquer ne consomme pas le mouvement."/>
	<entry name="Necrons/JetCharge" value="<string name='Actions/Necrons/JetCharge'/>"/>
	<entry name="Necrons/JetChargeDescription" value="Augmente la réduction de dégâts."/>
	<entry name="Necrons/LivingMetal" value="Métal vivant"/>
	<entry name="Necrons/LivingMetalDescription" value="Restaure des points de vie tous les tours."/>
	<entry name="Necrons/LivingMetalFlavor" value="C'est compréhensible que les Nécrons aient construit leurs véhicules avec le même métal vivant que leurs propres corps, étant donné sa résistance et sa capacité à réparer les dégâts qu'il subit au combat."/>
	<entry name="Necrons/LoyaltyBuildingBonus" value="L'accord baroque"/>
	<entry name="Necrons/LoyaltyBuildingBonusDescription" value="Augmente la production de loyauté."/>
	<entry name="Necrons/LoyaltyBuildingBonusFlavor" value="Davantage du conte caché de l'Autel est désormais révélé, bien que l'histoire s'arrête toujours avant que le Roi Silencieux puisse se venger des Dieux Stellaires voraces qui ont enlevé leurs âmes à son peuple."/>
	<entry name="Necrons/MeleeDamage" value="Lames de nécroderme"/>
	<entry name="Necrons/MeleeDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Necrons/MeleeDamageFlavor" value="Faites de tranches impossiblement fines du métal vivant omniprésent des Nécrons, ces lames traversent la peau, les os et les armures avec une facilité déconcertante."/>
	<entry name="Necrons/NanoscarabReanimationBeam" value="Rayon de réanimation Nanoscarabée"/>
 	<entry name="Necrons/NanoscarabReanimationBeamDescription" value="Restaure les points de vie."/>
 	<entry name="Necrons/NanoscarabReanimationBeamFlavor" value="<string name='Actions/Necrons/NanoscarabReanimationBeamFlavor'/>"/>
	<entry name="Necrons/Nebuloscope" value="Nébuloscope"/>
	<entry name="Necrons/NebuloscopeDescription" value="Ignore la réduction de dégâts à distance des ennemis."/>
	<entry name="Necrons/NebuloscopeFlavor" value="Cet appareil ésotérique permet au pilote d'un Mécanoptère de traquer sa proie à travers différentes dimensions, ne lui laissant aucune place où se cacher."/>
	<entry name="Necrons/OreBuildingBonus" value="Esclaves de confiance"/>
	<entry name="Necrons/OreBuildingBonusDescription" value="Augmente la production de minerai."/>
	<entry name="Necrons/OreBuildingBonusFlavor" value="Faire un peu confiance dans les jeunes races asservies crées par les Anciens et améliorer leur sort peut sembler stupide, mais cela augmente la productivité."/>
	<entry name="Necrons/Chronometron" value="<string name='Actions/Necrons/Chronometron'/>"/>
	<entry name="Necrons/ChronometronDescription" value="Augmente la réduction de dégâts."/>
	<entry name="Necrons/ChronometronFlavor" value="<string name='Actions/Necrons/ChronometronFlavor'/>"/>
	<entry name="Necrons/PhaseShiftGenerator" value="<string name='Actions/Necrons/PhaseShiftGenerator'/>"/>
	<entry name="Necrons/PhaseShiftGeneratorDescription" value="Augmente la réduction de dégâts."/>
	<entry name="Necrons/PhaseShiftGeneratorFlavor" value="<string name='Actions/Necrons/PhaseShiftGeneratorFlavor'/>"/>
	<entry name="Necrons/QuantumShielding" value="<string name='Actions/Necrons/QuantumShielding'/>"/>
	<entry name="Necrons/QuantumShieldingDescription" value="Augmente la réduction de dégâts invulnérable."/>
	<entry name="Necrons/QuantumShieldingFlavor" value="<string name='Actions/Necrons/QuantumShieldingFlavor'/>"/>
	<entry name="Necrons/ReanimationProtocols" value="Protocoles de Réanimation"/>
	<entry name="Necrons/ReanimationProtocolsDescription" value="Restaure des points de vie tous les tours."/>
	<entry name="Necrons/ReanimationProtocolsFlavor" value="Les Nécrons ont des systèmes d'auto-réparation sophistiqués qui renvoie même les guerriers les plus endommagés au combat."/>
	<entry name="Necrons/Reaper" value="<string name='Actions/Necrons/Reaper'/>"/>
	<entry name="Necrons/ReaperFlavor" value="<string name='Actions/Necrons/ReaperFlavor'/>"/>
	<entry name="Necrons/ResearchBuildingBonus" value="Stylet à données Cryptek"/>
	<entry name="Necrons/ResearchBuildingBonusDescription" value="Augmente la production de recherche."/>
	<entry name="Necrons/ResearchBuildingBonusFlavor" value="Inventer de nouvelles technologies est du domaine des Crypteks—et avec des stylets à données, ils peuvent accéder aux informations partagées sans les risques présentés par une connexion directe d'esprit à esprit."/>
	<entry name="Necrons/ShieldVane" value="Aileron renforcé"/>
	<entry name="Necrons/ShieldVaneDescription" value="Augmente l'armure."/>
	<entry name="Necrons/ShieldVaneFlavor" value="Les Mécanoptères qui sont déployés directement au milieu des défenses d'un monde sont souvent équipés avec des plaques d'armure supplémentaires."/>
	<entry name="Necrons/SleepingSentry" value="<string name='Actions/Necrons/SleepingSentry'/>"/>
	<entry name="Necrons/SleepingSentryDescription" value="Augmente la réduction de dégâts mais empêche l'unité de se déplacer ou d'agir."/>
	<entry name="Necrons/TargetRelayed" value="Cible relayée"/>
	<entry name="Necrons/TargetRelayedDescription" value="Augmente la précision à distance des Nécrons contre cette unité."/>
	<entry name="Necrons/TargetRelayedFlavor" value="<string name='Traits/Necrons/TargetingRelayFlavor'/>"/>
	<entry name="Necrons/TargetingRelay" value="Relai de ciblage"/>
	<entry name="Necrons/TargetingRelayDescription" value="Augmente temporairement la précision à distance des Nécrons contre l'unité ciblée."/>
	<entry name="Necrons/TargetingRelayFlavor" value="Les Rôdeurs du Triarcat peuvent sembler fragile, mais leur blindage quantique les garde en un seul morceau, pour que leur relai de ciblage puisse amplifier la puissance de feu des Nécrons alentours."/>
	<entry name="Necrons/Technomancer" value="Technomancien"/>
	<entry name="Necrons/TechnomancerDescription" value="Augmente la réduction de dégâts"/>
	<entry name="Necrons/VehiclesBuildingBonus" value="Travailleurs Canoptek"/>
	<entry name="Necrons/VehiclesBuildingBonusDescription" value="Augmente la production fournie."/>
	<entry name="Necrons/VehiclesBuildingBonusFlavor" value="Assigner des constructeurs Canopteks pour maintenir et réparer les machines de guerre variées en hibernation accélère grandement leur remise en état."/>
	<entry name="Necrons/VengeanceOfTheEnchained" value="Vengeance de l'enchaîné"/>
	<entry name="Necrons/VengeanceOfTheEnchainedDescription" value="L'explosion en mourant cause des dégâts supplémentaires."/>
	<entry name="Necrons/WraithForm" value="Enveloppe spectrale"/>
 	<entry name="Necrons/WraithFormDescription" value="Augmente la réduction de dégâts invulnérable."/>
 	<entry name="Necrons/WraithFormFlavor" value="En tant qu'engin Canoptek, le Spectre devait s'occuper de la maintenance des Nécrons pendant les millénaires où ils dormaient, et était capable de se déstabiliser dimensionnellement pour réparer des composants internes qui ne fonctionneraient plus. Maintenant que ceux dont ils s'occupaient sont réveillés, il utilise la même technologie pour voltiger au-dessus du champ de bataille, les munitions ennemies passant au travers de lui sans l'endommager."/>
 	<entry name="Necrons/Wraithflight" value="Vol spectral"/>
 	<entry name="Necrons/WraithflightDescription" value="<string name='Traits/Necrons/ImmuneToNaturalLawDescription'/>"/>
 	<entry name="Necrons/WraithflightFlavor" value="Leur matrice de déstabilisation dimensionnelle ne fait pas que rendre les Spectres presque impossibles à tuer, elle leur permet aussi de passer à travers n'importe quel obstacle, que ce soit des rivières, de l'herbe barbelée ou même des guerriers ennemis."/>
	<entry name="Necrons/WrithingWorldscape" value="<string name='Actions/Necrons/WrithingWorldscape'/>"/>
    <entry name="Necrons/WrithingWorldscape" value="<string name='Actions/Necrons/WrithingWorldscape'/>"/>
	<entry name="NoEscape" value="Pas d'échappatoire"/>
	<entry name="NoEscapeDescription" value="Augmente les attaques contre les véhicules découverts."/>
	<entry name="OpenTopped" value="Découvert"/>
	<entry name="OpenToppedDescription" value="Classification."/>
	<entry name="OpenToppedFlavor" value="Certains véhicules ont peu de blindage les rendant plus vulnérables à cause de leur structure légère. Cependant ces véhicules font d'excellents transports de troupes d'assaut car leur passagers peuvent débarquer avec plus de facilité."/>
	<entry name="OrationOfRestoration" value="Oraison de restauration"/>
	<entry name="OrationOfRestorationDescription" value="Restaure des points de vie."/>
	<entry name="Ordnance" value="Artillerie"/>
	<entry name="OrdnanceDescription" value="Augmente la pénétration d'armure contre les véhicules et les fortifications. Ne peut pas être utilisé par de l'infanterie après s'être déplacé."/>
	<entry name="OrkoidFungus" value="Champignon orkoïde"/>
	<entry name="OrkoidFungusDescription" value="Restaure des points de vie tous les tours aux unités terrestres Orks."/>
	<entry name="OrkoidFungusFlavor" value="Les Orks ont une relation étrangement symbiotique avec le champignon qui se répand sur les planètes—il est génétiquement identique à eux, aux squigs et aux grots, et toutes ces espèces ont l'habitude d'émerger d'eux plutôt que de naître. Les unités Orks diminuées par le combat feraient bien de se replier sur ces champs pour acquérir des troupes (littéralement) fraîches."/>
	<entry name="OrkoidFungusBonusHealingRate" value="Forêts fongiques"/>
	<entry name="OrkoidFungusBonusHealingRateDescription" value="Restaure des points de vie tous les tours aux unités terrestres Orks."/>
	<entry name="OrkoidFungusBonusHealingRateFlavor" value="Une fois que les spores fongiques des Orks sont bien établies sur une planète, elles peuvent ressembler à des forêts—bien qu'elles soient formées de champignons immenses crachant des Orks sauvages, des Grots et des Squigs."/>
	<entry name="OrkoidFungusFood" value="Floraison de champiling"/>
	<entry name="OrkoidFungusFoodDescription" value="Augmente la production de nourriture"/>
	<entry name="OrkoidFungusFoodFlavor" value="Les minuscules Snotlings, comme tous les orkoïdes, sont dérivés de la même structure génétique de champignon. Contrairement aux autres orkoïdes cependant, les Snotlings particulièrement paresseux peuvent s'allonger dans un champs de champignons et redevenir des créatures géantes mi-champignons, une friandise particulière pour leurs frères Orks plus grands."/>
	<entry name="Orks/BeastSnagga" value="Bête Snagga"/>
 	<entry name="Orks/BeastSnaggaDescription" value="Augmente la précision contre les véhicules et les créatures monstrueuses et augmente la réduction des dégâts d'invulnérabilité."/> 	
	<entry name="Orks/BeastSnaggaFlavor" value="Repérant les menaces les plus importantes ou les plus dangereuses sur le champ de bataille—outre les Orks eux-mêmes—les Beast Snaggas les pourchassent avec un enthousiasme jubilatoire."/>
	<entry name="Orks/BigBoss" value="<string name='Actions/Orks/BigBoss'/>"/>
	<entry name="Orks/BigBossDescription" value="Augmente les points de vie."/>
	<entry name="Orks/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="Orks/BlastDamageDescription" value="<string name='Traits/AstraMilitarum/BlastDamageDescription'/>"/>
	<entry name="Orks/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="Orks/BoltDamage" value="Bolts plus gro'"/>
	<entry name="Orks/BoltDamageDescription" value="<string name='Traits/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="Orks/BoltDamageFlavor" value="Comme les bolts Kraken des Space Marines, cette modification du canon par un Mek permet au fling' de contenir des munitions de plus grand calibre, permettant à l'utilisateur chanceux de se vanter en utilisant un pistolet encore plus grand qui fait encore plus mal."/>
	<entry name="Orks/BonusBeastsProduction" value="Fouets plus grands"/>
	<entry name="Orks/BonusBeastsProductionDescription" value="Augmente la production fournie par les terrains des fouettards."/>
	<entry name="Orks/BonusBeastsProductionFlavor" value="“On va avoir b'soin d'un plus gros fouet”<br/>  — Fouettard Gark Snotsmeer, juste avant qu'un Squiggoth l'écrase"/>
	<entry name="Orks/BonusColonizersProduction" value="Marteaux à ferraille"/>
	<entry name="Orks/BonusColonizersProductionDescription" value="Augmente la production fournie par les chantiers à trucs des Meks"/>
	<entry name="Orks/BonusColonizersProductionFlavor" value="Quand il n'y a pas assez de ferraille de petite taille à portée de main pour leurs créations disgracieuses, les Meks en obtiennent davantage en créant d'étranges mais formidables machines qui ravagent les ruines et les épaves des mondes conquis."/>
	<entry name="Orks/BonusInfantryProduction" value="Plus de dakka"/>
	<entry name="Orks/BonusInfantryProductionDescription" value="Augmente la production fournie par les piles de dakka"/>
	<entry name="Orks/BonusInfantryProductionFlavor" value="“Lé méyeur fling' ke j'ai jamé fait, ça. Plein d'kannons pour k'ça soit tire à fon'. Sauf s'truk passke c'est un krameur, cé brûlant. Ouais, tout bon tout bien. Et lé balles sont s'plozives… elles font boum dans lé truk sur qui vous tirez. Et s'bouton-là, cé l'meilleur. Keske sa fé, vous voyez ça… ça… oh zut. Cé rien boss. Nan vous avez pas b'soin d'voir c'que le bouton fé… Appuyé pa!”<br/>  — Dernières paroles, Nazdakka Boomsnik, Mek"/>
	<entry name="Orks/BonusVehiclesProduction" value="Travail de brikolo"/>
	<entry name="Orks/BonusVehiclesProductionDescription" value="Augmente la production fournie par les Kultes d'la vitesse."/>
	<entry name="Orks/BonusVehiclesProductionFlavor" value="La majeure partie de la technologie Ork ne devrait pas fonctionner—des composants vitaux sont souvent manquants ou l'engin est simplement impossible. La croyance simpliste des Orks—realisées sous la forme de l'énergie Waaagh!—compense les manques. C'est pourquoi accélérer la production de véhicules et d'armes Ork n'a pratiquement aucun effet sur leur qualité."/>
	<entry name="Orks/BustHeads" value="<string name='Actions/Orks/BustHeads'/>"/>
	<entry name="Orks/BustHeadsDescription" value="Réduit les pertes de moral."/>
	<entry name="Orks/BustHeadsFlavor" value="<string name='Actions/Orks/BustHeadsFlavor'/>"/>
	<entry name="Orks/ChannelMentalEmissions" value="<string name='Actions/Orks/ChannelMentalEmissions'/>"/>
	<entry name="Orks/ChannelMentalEmissionsDescription" value="Augmente la production de recherche."/>
 	<entry name="Orks/CityEnergy" value="Truk teknologiks krépitans"/>
 	<entry name="Orks/CityEnergyDescription" value="Augmente la production d'énergie"/>
 	<entry name="Orks/CityEnergyFlavor" value="Des arcs électriques verts et rouge entre les fourches complexes de ces appareils d'amplification de l'énergie, faisant frire tout Peau-verte qui s'approcherait trop près. C'est un sacré sport de spectacle, mais la technologie manque d'éléments clés, et ne pourrait donc pas soutenir les prétentions des Méks selon lesquelles elle augmenterait la production d'énergie—si ce n'était pour la croyance capable d'altérer la réalité des Orks qui fait qu'elle l'augmente."/>
	<entry name="Orks/CityGrowth" value="Haut-parleurs du boss"/>
	<entry name="Orks/CityGrowthDescription" value="Augmente la production fournie par les Hangars à Ferraille."/>
	<entry name="Orks/CityGrowthFlavor" value="“Nan, j'veux pas de bordel de tweeterz. J'veux des gorkawoofers… Des qui vont jusqu'à onze!!”<br/>  — Oreilles ki saignent Merfnik, Seigneur de guerre et rocker Goff"/>
	<entry name="Orks/CityInfluence" value="Truk tré briyant"/>
	<entry name="Orks/CityInfluenceDescription" value="Augmente la production d'influence."/>
	<entry name="Orks/CityInfluenceFlavor" value="'Je ne sais pas ce que c'est, mais je les veux.'<br/>  — Le chef de guerre Flashgrub Nitwiz, contemplant les portes en or martelé de la Basilica Imperialis sur Catiline VII"/>	
	<entry name="Orks/CityLoyalty" value="Perches à trophées"/>
	<entry name="Orks/CityLoyaltyDescription" value="Augmente la production de loyauté."/>
	<entry name="Orks/CityLoyaltyFlavor" value="Il n'y a rien qui agite davantage un Ork que la vue de ses ennemis vaincus (ou les bouts qu'il en reste) pendus en haut d'un mât."/>
	<entry name="Orks/CityPopulationLimit" value="Kreuz' profond"/>
	<entry name="Orks/CityPopulationLimitDescription" value="Augmente la limite de population."/>
	<entry name="Orks/CityPopulationLimitFlavor" value="En utilisant les machines les plus récentes des Meks, les cabanes et forges Orks s'étendent désormais loin sous la surface de la ville. Bien sûr l'effondrement occasionnel emporte des Orks hurlant dans les profondeurs, mais ça vaut le coup pour l'espace supplémentaire."/>
	<entry name="Orks/CityResearch" value="Salles de Réflektion"/>
	<entry name="Orks/CityResearchDescription" value="Augmente la production de recherche."/>
	<entry name="Orks/CityResearchFlavor" value="Etant donné que la technologie est implantée dans les gènes des Orks normaux, un Big Boss intelligent découvrira que dédier de l'espace, de la main d'oeuvre et du temps pour bloquer ses Meks dans une petite pièce pleine de ferraille en leur ordonnant de construire de la bonne Dakka payera toujours."/>
	<entry name="Orks/CityTier2" value="Ghetto"/>
	<entry name="Orks/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="Orks/CityTier2Flavor" value="A l'extérieur de chacun des murs originels de la forteresse, un gheto de gretchins, de squigs de snotlings et d'Orks de bas rang est apparu, alors que le champignon vert insidieux s'est répandu partout refaisant ce monde à l'image génétique des Orks."/>
	<entry name="Orks/CityTier3" value="Bidonville"/>
	<entry name="Orks/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="Orks/CityTier3Flavor" value="La réputation du Big Boss s'est répandu sur toute la planète, avec les énergies Waaagh! invisibles suivant son chemin. Où que les Orks apparaissent, ils ressentent une attraction irrésistible vers les villes du Big Boss, qui sont maintenant éparpillées dans tout le paysage avoisinant telles une infinité de bidonvilles et de ghettos."/>
	<entry name="Orks/CreateOrkoidFungusOnDeath" value="Décomposition rapide"/>
	<entry name="Orks/CreateOrkoidFungusOnDeathDescription" value="L'unité créée des champignons orkoïdes lorsqu'elle meurt."/>
	<entry name="Orks/CreateOrkoidFungusOnDeathFlavor" value="“Exposition 27: Une unique 'spore' de l'espèce xenos #0451 'Ork'. Acolytes, vous noterez la texture fongineuse et les structures microhomonculaires. Notre théorie est que ces 'spores' génèrent de nouveaux 'Orks' dans n'importe quel environnement suffisamment fertile. Quand un Ork atteint sa maturité (typiquement 30 minutes après son émergence) il les émet constamment—mais elles sont émises en masse au moment de la mort. Ne la faites pas tomber, s'il vous plaît.”<br/>  — Transcriptions d'une leçon, Grigomen “Fan des orks” Delr, Libre-Marchand et xénologue amateur"/>
	<entry name="Orks/CyborkImplants" value="<string name='Actions/Orks/CyborkImplants'/>"/>
	<entry name="Orks/CyborkImplantsDescription" value="Augmente les dégâts et la réduction de dégâts."/>
	<entry name="Orks/ExtraBitz" value="<string name='Actions/Orks/ExtraBitz'/>"/>
	<entry name="Orks/ExtraBitzDescription" value="Réduit les coûts d'entretien en nourriture, minerai et énergie."/>
	<entry name="Orks/ExperimentalProcedure" value="<string name='Actions/Orks/ExperimentalProcedure'/>"/>
	<entry name="Orks/ExperimentalProcedureDescription" value="Augmente les dégâts et la réduction de dégâts."/>
	<entry name="Orks/Flyboss" value="As dé zas"/>
	<entry name="Orks/FlybossDescription" value="Augmente la précision à distance contre les unités volantes, les motojets et les antigravs."/>
	<entry name="Orks/FlybossFlavor" value="Les as dé zas sont des pilotes émérites ayant survécu à plus de combats aériens qu'ils ne peuvent conter (même avec leurs doigts de pied)."/>
	<entry name="Orks/Gitfinda" value="Vizeur"/>
	<entry name="Orks/GitfindaDescription" value="Augmente la précision à distance si l'unité reste stationnaire."/>
	<entry name="Orks/GitfindaFlavor" value="Qu’il prenne la forme d’une visière à monoculaire d’un implant bionique sophistiqué, d’une gros télescope ou de Mork sait quoi, le Vizeur dote son utilisateur d’une précision presque moyenne."/>
	<entry name="Orks/GreenTide" value="Marée verte"/>
	<entry name="Orks/GreenTideFlavor" value="Les Orks sont vraiment très difficiles à tuer. Des Orks apparemment tués lors d'une bataille reviennent souvent lors de la prochaine, soit remis sur pied par un Dok Maboul soit simplement soigné miraculeusement—et plus résistant et fort suite à l'expérience."/>
	<entry name="Orks/GreenTideGrowth" value="<string name='Traits/Orks/GreenTide'/>"/>
	<entry name="Orks/GreenTideGrowthDescription" value="Augmente le rythme de croissance de la population."/>
	<entry name="Orks/GreenTideGrowthFlavor" value="<string name='Traits/Orks/GreenTideFlavor'/>"/>
	<entry name="Orks/GreenTideHealing" value="<string name='Traits/Orks/GreenTide'/>"/>
	<entry name="Orks/GreenTideHealingDescription" value="Augmente le rythme de soin."/>
	<entry name="Orks/GreenTideHealingFlavor" value="<string name='Traits/Orks/GreenTideFlavor'/>"/>
	<entry name="Orks/GrotGunner" value="Tireur grot"/>
	<entry name="Orks/GrotGunnerDescription" value="Augmente la précision des gros fling' et des gros fling' jumelés."/>
	<entry name="Orks/KustomForceField" value="<string name='Actions/Orks/KustomForceField'/>"/>
	<entry name="Orks/KustomForceFieldDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="Orks/KustomForceFieldFlavor" value="<string name='Actions/Orks/KustomForceFieldFlavor'/>"/>
	<entry name="Orks/MeleeDamage" value="Gros kikoup'"/>
	<entry name="Orks/MeleeDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Orks/MeleeDamageFlavor" value="Quand les Orks réfléchissent à des améliorations, ils ne réfléchissent pas longtemps. Méga-armure, méga fling'… et gros kikoup. Si vous pouvez le porter, vous pouvez l'agiter, et si vous pouvez l'agiter, vous pouvez frapper quelqu'un avec."/>
	<entry name="Orks/MightMakesRight" value="La loi du plus fort"/>
	<entry name="Orks/MightMakesRightDescription" value="Donne de l'influence lorsque cette unité attaque."/>
	<entry name="Orks/MightMakesRightFlavor" value="Les orks ne se sentent réellement vivant que lorsqu'ils sont dans une Waaagh! et une Waaagh! ne prospère qu'à travers des combats constants. Tant que les Orks se battent, la Waaagh! devrait grandir."/>
	<entry name="Orks/ProphetOfTheWaaagh" value="<string name='Actions/Orks/ProphetOfTheWaaagh'/>"/>
	<entry name="Orks/ProphetOfTheWaaaghDescription" value="Donne de l'influence basé sur le coût d'entretien lorsque cette unité attaque."/>
	<entry name="Orks/Scavenger" value="Ramasseur"/>
	<entry name="Orks/ScavengerDescription" value="Donne du minerai lorsque cette unité tue un ennemi."/>
	<entry name="Orks/ScavengerFlavor" value="Les Orks ne sont pas connus pour leur amour du travail, préférant paresser entre deux batailles, s'enlevant les morceaux de squigs coincés dans leurs dents et se battant entre eux. Plutôt que de miner du minerai dans le sol, ils ont tendance à le récupérer de, eh bien, n'importe où. Ou plutôt leurs sous-fifres Grots le font."/>
	<entry name="Orks/WarbikeTurboBoost" value="<string name='Actions/Orks/WarbikeTurboBoost'/>"/>
	<entry name="Orks/WarbikeTurboBoostDescription" value="<string name='Actions/Orks/WarbikeTurboBoostDescription'/>"/>
	<entry name="Orks/WarbikeTurboBoostFlavor" value="<string name='Actions/Orks/WarbikeTurboBoostFlavor'/>"/>
	<entry name="Orks/Warpath" value="<string name='Actions/Orks/Warpath'/>"/>
	<entry name="Orks/WarpathDescription" value="<string name='Actions/Orks/WarpathDescription'/>"/>
	<entry name="Orks/WarpathFlavor" value="<string name='Actions/Orks/WarpathFlavor'/>"/>
	<entry name="Orks/WingMissiles" value="<string name='Weapons/WingMissiles'/>"/>
	<entry name="Orks/WingMissilesDescription" value="Augmente la précision contre les véhicules."/>
	<entry name="Outpost" value="Avant-poste"/>
	<entry name="OutpostDescription" value="Augmente la réduction de dégâts et le rythme de soin des alliés. Augmente la réduction de dégât à distance des unités d'infanterie."/>
	<entry name="Outflank" value="Débordement"/>
	<entry name="OutflankDescription" value="Augmente la précisions lorsqu'adjacent à un allié."/>
	<entry name="OutflankFlavor" value="La meilleur façon de surprendre un ennemi est de l'attaque d'un endroit inattendu."/>
	<entry name="Pinned" value="Pilonné"/>
	<entry name="PinnedDescription" value="Réduit le mouvement et la précision à distance, augmente la réduction de dégâts à distance et empêche l'unité d'effectuer des tirs en état d'alerte."/>
	<entry name="PinnedFlavor" value="Subir des tirs sans savoir d'où ils viennent, ou avoir des obus d'artillerie pleuvoir sur vous, peut faire vaciller la détermination même celle des des guerriers les plus braves, les faisant plonger au sol et se cacher derrière le premier couvert qu'ils trouvent."/>
	<entry name="Pinning" value="Pilonnage"/>
	<entry name="PinningDescription" value="Réduit temporairement le mouvement de l'infanterie ciblée et l'empêche d'effectuer des tirs en état d'alerte."/>
	<entry name="PinningFlavor" value="<string name='Traits/PinnedFlavor'/>"/>
	<entry name="Poisoned" value="Empoisonné"/>
	<entry name="PoisonedDescription" value="Augmente les dégâts contre les unités d'infanterie et les créatures monstrueuses."/>
	<entry name="PoisonedFlavor" value="Il y a de nombreux poisons virulents et létaux dans le sombre futur. C'est la simplicité même d'adapter ces toxines pour l'utilisation au combat. Ca n'importe pas s'ils enduisent des lames, des balles ou qu'ils sont secrétés par des monstruosités aliens – tous sont létaux."/>
	<entry name="PowerOfTheMachineSpirit" value="<string name='Actions/PowerOfTheMachineSpirit'/>"/>
	<entry name="PowerOfTheMachineSpiritDescription" value="<string name='Actions/PowerOfTheMachineSpiritDescription'/>"/>
	<entry name="PowerOfTheMachineSpiritFlavor" value="<string name='Actions/PowerOfTheMachineSpiritFlavor'/>"/>
	<entry name="PrecisionShots" value="Tirs de précision"/>
	<entry name="PrecisionShotsDescription" value="Augmente la précision."/>
	<entry name="PreferredEnemy" value="Ennemi juré"/>
	<entry name="PreferredEnemyDescription" value="Augmente la précision."/>
	<entry name="PreferredEnemyFlavor" value="De nombreux guerriers de la galaxie s'entraînent dur pour vaincre un ennemi particulier, permettant de prédire les actions de cet ennemi et donc de le toucher et le blesser plus facilement."/>
	<entry name="PrimaryWeapon" value="Arme principale"/>
	<entry name="PrimaryWeaponDescription" value="Augmente la pénétration d'armure."/>
	<entry name="PsychicBlock" value="Blocage psychique"/>
	<entry name="PsychicBlockDescription" value="Annule les dégâts et les effets du prochain pouvoir psychique ennemi."/>
	<entry name="PsychicHood" value="Coiffe psychique"/>
	<entry name="PsychicHoodDescription" value="Augmente la réduction des dégâts de décharge psy."/>
	<entry name="PsychicLash" value="Fouet psychique"/>
	<entry name="PsychicLashDescription" value="Les attaques pénètrent toutes les armures."/>
	<entry name="PsychicPower" value="Pouvoir psychique"/>
	<entry name="PsychicPowerDescription" value="Classification."/>
	<entry name="PsychneueinInfest" value="Larves psychneuien"/>
	<entry name="PsychneueinInfestDescription" value="Lorsque l'unité ciblé est tué, elle donne naissance à une unité de psychneuien."/>
	<entry name="PsychneueinInfestation" value="Infestation psychneuien"/>
	<entry name="PsychneueinInfestationDescription" value="Donne naissance à une unité de psychneuien lorsque l'unité meurt."/>
	<entry name="Psyker" value="Psyker"/>
	<entry name="PsykerDescription" value="Classification."/>
	<entry name="PsykerFlavor" value="Les psykers sont des mystiques de combat qui utilisent l'énergie du Warp."/>
	<entry name="Rage" value="Rage"/>
	<entry name="RageDescription" value="Augmente les attaques."/>
	<entry name="RageFlavor" value="La soif de sang est une arme puissante au combat, incitant un guerrier à tailler ses ennemis en pièces dans une tornade de carnage sans réflexion (mais éminemment satisfaisante)."/>
	<entry name="Rampage" value="Folie furieuse"/>
	<entry name="RampageDescription" value="Augmente les attaques quand il y a plus d'ennemis que d'alliés sur les cases adjacentes."/>
	<entry name="RampageFlavor" value="Pour certains guerriers, être en infériorité numérique n'est pas quelque chose de désespérant, mais un appel pour éliminer leurs ennemis avec une contre-attaque furieuse."/>
	<entry name="RapidFire" value="Tir rapide"/>
	<entry name="RapidFireDescription" value="Double les attaques à mi-distance."/>
	<entry name="RecoveryGear" value="Équipement de dépannage"/>
	<entry name="RecoveryGearDescription" value="Augmente la vitesse de soin."/>
	<entry name="RecoveryGearFlavor" value="De nombreux équipages chargent leurs véhicules avec des collections d'outils, de câbles de remorquages et d'autres gadgets qui peuvent faire la différence entre dégager un véhicule immobilisé ou devoir l'abandonner à son sort."/>
	<entry name="RedPaintJob" value="Peintur' rouj"/>
	<entry name="RedPaintJobDescription" value="Augmente les dégâts"/>
	<entry name="RedPaintJobFlavor" value="Les Orks croient dur comme fer qu’un véhicule va plus vite s’il est peint en rouge. Contre toute vraisemblance, ils n’ont pas tort."/>
	<entry name="RefractorField" value="Champ réfracteur"/>
	<entry name="RefractorFieldDescription" value="Augmente la réduction des dégâts"/>
	<entry name="RefractorFieldFlavor" value="Souvent portés par les officiers de haut-rang et les héros impériaux, les champs réfracteurs scintillent en réfléchissant l'énergie approchant leurs porteurs, repoussant des explosions ou des coups de lame qui les abattrait sinon."/>
	<entry name="Regeneration" value="Régénération"/>
	<entry name="RegenerationDescription" value="Restaure des points de vie tous les tours."/>
	<entry name="RegenerationFlavor" value="Certaines unités ont la capacité de récupérer d'horribles blessures qui auraient dû être fatales."/>
	<entry name="Relentless" value="Implacable"/>
	<entry name="RelentlessDescription" value="Annule la pénalité pour les armes lourdes, d'artillerie et à salve."/>
	<entry name="RelentlessFlavor" value="Les guerriers implacables sont forts—rien ne peut arrêter leur avance impitoyable."/>
	<entry name="RelicPlating" value="Reliquaire"/>
	<entry name="RelicPlatingDescription" value="Augmente la réduction des dégâts de décharge psy."/>
	<entry name="RelicPlatingFlavor" value="Parfois un équipage établira une relation empathique avec l'esprit de la machine de leur char. Quand un tel équipage périt, leurs restes sont inhumés dans leur véhicule, leurs esprits restant pour protéger le véhicule des énergies maléfiques du warp."/>
	<entry name="Rending" value="Perforant"/>
	<entry name="RendingDescription" value="Augmente les dégâts et la pénétration d'armure."/>
	<entry name="RendingFlavor" value="Certaines armes peuvent infliger des coups critiques contre lesquels aucune armure ne peut protéger."/>
	<entry name="RepulsorGrid" value="Grille de déflexion"/>
	<entry name="RepulsorGridDescription" value="Augmente la réduction des dégâts et renvoie les dégâts à distance provenant d'armes qui ne sont pas d'explosion, de souffle ou de décharge psy à l'attaquant."/>
	<entry name="RitesOfWar" value="Rituels de guerre"/>
	<entry name="RitesOfWarDescription" value="Augmente les dégâts."/>
	<entry name="RitesOfWarFlavor" value="<string name='Actions/RitesOfWarFlavor'/>"/>
	<entry name="Rosarius" value="Rosarius"/>
	<entry name="RosariusDescription" value="Augmente la réduction des dégâts."/>
	<entry name="RosariusFlavor" value="Un rosarius est porté par les chapelains space marines pour les protéger et comme symbole d'office. Il émet un champ d'énergie protecteur autour du porteur capable de dévier des coups et des tirs qui exploseraient un bunker en ferrobéton. Il est dit que plus la croyance du porteur en l'Empereur est grande, plus le champ de force du rosarius est puissant lui aussi."/>
	<entry name="RuinsStealth" value="Discrétion dans les ruines"/>
	<entry name="RuinsStealthDescription" value="Augmente la réduction des dégâts à distance dans les ruines impériales."/>
	<entry name="Salvo" value="Salve"/>
	<entry name="SalvoDescription" value="Divise par deux les attaques et la portée si l'unité s'est déplacée."/>
	<entry name="SavantInterlocution" value="Interlocution savante"/>
	<entry name="SavantInterlocutionDescription" value="Augmente la précision à distance contre les volants et les antigravs lorsqu'adjacent à un Hunter allié."/>
	<entry name="SavantLock" value="Verrouillage savant"/>
	<entry name="SavantLockDescription" value="Augmente la précision contre les volants, les motojets et les antigravs."/>
	<entry name="SeekerMissile" value="<string name='Weapons/SeekerMissile'/>"/>
	<entry name="SeekerMissileDescription" value="Portée réduite contre les cibles qui ne sont pas sous désignation laser. Ne nécessite pas de ligne de vue sur les cibles sous désignation laser."/>
	<entry name="SeekerMissileFlavor" value="<string name='Weapons/SeekerMissileFlavor'/>"/>
	<entry name="Seeking" value="Tête chercheuse"/>
	<entry name="SeekingDescription" value="Augmente la précision à distance contre les volants."/>
	<entry name="Shaken" value="Secoué"/>
	<entry name="ShakenDescription" value="Réduit la précision et augmente les dégâts reçus."/>
	<entry name="Shielded" value="Protégé"/>
	<entry name="ShieldedDescription" value="Augmente la réduction des dégâts."/>
	<entry name="ShieldedFlavor" value="Certains guerriers sont protégés par davantage qu'une simple armure physique. Ils peuvent être protégés par des champs de force, enveloppés par des énergies mystiques ou avoir une constitution qui se tir de tirs qui troueraient un char."/>
	<entry name="Shop" value="Commerce"/>
	<entry name="ShopDescription" value="Permet aux héros d'acheter et de vendre des objets."/>
	<entry name="Shred" value="Lacération"/>
	<entry name="ShredDescription" value="Augmente les dégâts."/>
	<entry name="ShredFlavor" value="Certaines armes et certains guerriers frappent avec une tornade de coups, déchirant la chair dans une série de frappes brutales."/>
	<entry name="Shrouded" value="Dissimulation"/>
	<entry name="ShroudedDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="ShroudedFlavor" value="La source de l'obscurité entourant ces guerriers n'importe pas—seul un tir chanceux peut percer le linceul d'obscurité qui les cache."/>
	<entry name="SiegeMasters" value="Maîtres de siège"/>
	<entry name="SiegeMastersDescription" value="Augmente les dégâts contre les unités ennemies dans les villes et les fortifications."/>
	<entry name="SiegeShield" value="Bouclier de siège"/>
	<entry name="SiegeShieldDescription" value="Augmente l'armure et réduit la pénalité de mouvement dans les forêts et les ruines impériales."/>
	<entry name="SiegeShieldFlavor" value="Beaucoup de Vindicator sont équipés d’une énorme lame de bulldozer qui leur permet de dégager les décombres du champ de bataille."/>
	<entry name="Signum" value="Signum"/>
	<entry name="SignumDescription" value="Augmente la précision à distance."/>
	<entry name="SignumFlavor" value="<string name='Actions/SignumFlavor'/>"/>
	<entry name="SistersOfBattle/ActOfFaith" value="Acte de foi"/>
	<entry name="SistersOfBattle/ActOfFaithDescription" value="Peut réaliser des Actes de Foi si l'unité n'est pas brisée ou secouée."/>
	<entry name="SistersOfBattle/ActOfFaithFlavor" value="Les Adepta Sororitas peuvent puiser à la source de leur foi et faire appel à l'Empereur pour guider leurs actions. De même, la croyance absolue dans le Credo impérial permet aux Sœurs de Bataille de réaliser l'apparemment impossible sur le champ de bataille. Pourtant, les miracles ne doivent pas être considérés comme une évidence. Au cœur du Credo impérial se trouve la conviction que le divin Empereur compte sur ses fidèles pour créer leur propre salut, mais aussi que si la situation est suffisamment sombre, il interviendra pour délivrer ses véritables serviteurs."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagus" value="Sarcophage de l'Anchorite"/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusDescription" value="Augmente l'armure."/>
	<entry name="SistersOfBattle/AnchoriteSarcophagusFlavor" value="Pour les Repentia qui ont non seulement fui, mais aussi trahi leurs sœurs au combat, un sort encore pire les attend. Après avoir été placés dans le creuset de leur Mortificatrice, ils sont enterrés derrière une épaisse enveloppe d'adamantine. Ce sarcophage protège leurs corps torturés des tirs et des lames désespérément balancées, les privant ainsi de la mort."/>
	<entry name="SistersOfBattle/AngelicVisage" value="Visage angélique"/>
	<entry name="SistersOfBattle/AngelicVisageDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/AngelicVisageFlavor" value="Avec un sourire béatifiant, les Zéphyrines esquivent les coups de leurs ennemis avec une grâce fluide avant de porter un coup mortel, généralement un coup de tête à bout portant avec un pistolet à balles ou un coup d'épée de puissance."/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemed" value="L'angoisse de l'irrécupérable"/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemedDescription" value="Endommage l'attaquant en mêlée lors de sa mort."/>
	<entry name="SistersOfBattle/AnguishOfTheUnredeemedFlavor" value="Les mortifères tracent un chemin de destruction avec leurs flammes avant de foncer tête baissée sur l'ennemi, la culpabilité et la douleur les poussant à avancer, sans se soucier du danger. Même si elles meurent, elles frappent leur ennemi, cherchant à se racheter à chaque instant."/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherine" value="Armure de Sainte Katherine"/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherineDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/ArmourOfSaintKatherineFlavor" value="Depuis que cette armure vénérée a été ointe d'une fiole de sang de la martyre Sainte Katherine, on lui attribue des pouvoirs sacrés de protection."/>
	<entry name="SistersOfBattle/AssaultWeaponBonus" value="Fusillade furieuse"/>
	<entry name="SistersOfBattle/AssaultWeaponBonusDescription" value="Augmente la pénétration de l'armure."/>
	<entry name="SistersOfBattle/AssaultWeaponBonusFlavor" value="La bonne attaque, judicieusement placée, peut vaincre le plus écrasant des ennemis. Cependant, si nous ne connaissons pas la bonne attaque ? Alors un assaut écrasant et confus atteindra le bon endroit, éventuellement…”<br/>—Chanoinesse Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/AvengingZeal" value="Zèle vengeur"/>
	<entry name="SistersOfBattle/AvengingZealDescription" value="Réduit la perte de moral."/>
	<entry name="SistersOfBattle/AvengingZealFlavor" value="<string name='Traits/SistersOfBattle/MartyrSpiritFlavor'/>"/>
	<entry name="SistersOfBattle/BerserkKillingMachine" value="Machine à tuer Berserker"/>
	<entry name="SistersOfBattle/BerserkKillingMachineDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/BerserkKillingMachineFlavor" value="Tourmentée par des réseaux de neuro-agrégateurs qui amplifient sa haine de soi et séparée des prières de ses sœurs par une cloison autour de sa tête, la souffrance spirituelle de la malheureuse pilote alimente la Mortificatrice. Ils avancent en trombe comme des troupes de choc terrifiantes, avant de s'écraser sur leur ennemi."/>
	<entry name="SistersOfBattle/BloodyResolution" value="Résolution sanglante"/>
	<entry name="SistersOfBattle/BloodyResolutionDescription" value="Réduit la perte de moral."/>
	<entry name="SistersOfBattle/BloodyResolutionFlavor" value="<string name='Traits/SistersOfBattle/MartyrdomFlavor'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnance" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnance'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnanceDescription" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnanceDescription'/>"/>
	<entry name="SistersOfBattle/CatechismOfRepugnanceFlavor" value="<string name='Actions/SistersOfBattle/CatechismOfRepugnanceFlavor'/>"/>
	<entry name="SistersOfBattle/CityGrowth" value="Recrutement des noviciats"/>
	<entry name="SistersOfBattle/CityGrowthDescription" value="Augmente le taux de croissance."/>
	<entry name="SistersOfBattle/CityGrowthFlavor" value="L'invasion Orks et la guerre qui a suivi sur Gladius Prime ont laissé beaucoup d'orphelins. L'envoi de missionnaires et de prédicateurs dans les régions désertiques pour recruter dans les armées de l'Imperium soutiendra la croissance de notre ville."/>
	<entry name="SistersOfBattle/CityTier2" value="Annexes du préceptorat"/>
	<entry name="SistersOfBattle/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="SistersOfBattle/CityTier2Flavor" value="Étant donné le nombre de réfugiés qui affluent au Préceptorat depuis les ruches en ruines et les terrains vagues de Gladius Prime, la chanoinesse supérieure a ordonné que nous sanctifiions de nouvelles terres à utiliser, en faisant venir tous les indigènes dans le giron, qu'ils le veuillent ou non."/>
	<entry name="SistersOfBattle/CityTier3" value="Forteresse du Sanctuaire"/>
	<entry name="SistersOfBattle/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="SistersOfBattle/CityTier3Flavor" value="Le Préceptorat est plus une ville maintenant, ressemblant aux cités ruches qui parsemaient autrefois la surface de la planète. Sous sa masse se trouve une ville souterraine, au-dessus de laquelle s'élèvent des flèches et des chants. Seuls les monastères-forteresses de l'Adeptus Astartes rivalisent avec lui en termes de taille et d'ornements sacrés."/>
	<entry name="SistersOfBattle/DivineDeliverance" value="<string name='Actions/SistersOfBattle/DivineDeliverance'/>"/>
	<entry name="SistersOfBattle/DivineDeliveranceDescription" value="<string name='Actions/SistersOfBattle/DivineDeliveranceDescription'/>"/>
	<entry name="SistersOfBattle/DivineDeliveranceFlavor" value="<string name='Actions/SistersOfBattle/DivineDeliveranceFlavor'/>"/>
	<entry name="SistersOfBattle/EternalCrusade" value="Éternelle Croisade"/>
	<entry name="SistersOfBattle/EternalCrusadeDescription" value="Augmente le rendement de la production."/>
	<entry name="SistersOfBattle/EternalCrusadeFlavor" value="“Et nous ne nous reposerons pas, car l'œil de l'Empereur-Dieu est sur nous. Il voit chaque âme sauvée et sait que le nombre d'âmes en danger l'emporte de loin. Nous ne pouvons pas nous arrêter, nous ne pouvons pas ralentir—notre croisade est éternelle”—Chanoinesse Vandire, In Memoriam De Virtute"/>
	<entry name="SistersOfBattle/ExpertFighters" value="Combattantes expérimentées"/>
	<entry name="SistersOfBattle/ExpertFightersDescription" value="Augmente la précision."/>
	<entry name="SistersOfBattle/ExpertFightersFlavor" value="Dans la plupart des escadres d'attaque, une poignée d'avions sont pilotés par des pilotes chevronnés. Ces as de la chasse sont des ennemis vraiment dangereux, capables de prévoir la réaction de leur proie avec une précision étonnante. Peu d'entre eux survivent au-delà d'une ou deux campagnes car ils attirent les missions les plus dangereuses où seuls les plus expérimentés s'imposent."/>
	<entry name="SistersOfBattle/FlankSpeed" value="Vitesse de flanquage"/>
	<entry name="SistersOfBattle/FlankSpeedDescription" value="Augmente le mouvement, mais empêche l'utilisation des armes à distance."/>
	<entry name="SistersOfBattle/FlankSpeedFlavor" value="'On ne devrait pas mettre en doute la noblesse du Questor Imperialis. Mais pour la foi, aucun ne peut égaler le Chevalier-Lancier, qui part au combat armé seulement d'une lance et de sa propre vitesse prodigieuse, en priant pour arriver jusqu'aux lignes ennemies. C'est un adversaire de choix pour nos forces.'<br/>  — Chanoinesse Vandire, In Memoriam De Virtute"/>
	<entry name="SistersOfBattle/HeavyWeaponBonus" value="Amélioration de l'armement"/>
	<entry name="SistersOfBattle/HeavyWeaponBonusDescription" value="Augmente la pénétration de l'armure."/>
	<entry name="SistersOfBattle/HeavyWeaponBonusFlavor" value="'Sur les obus du Castigator, nous saupoudrons la terre de la sainte Terra, élevons la voix en prière et faisons une génuflexion devant le sanctuaire de Sainte Catherine. Puis nous ajoutons 25 % d'explosif à chaque obus et les coiffons d'adamantium.'<br/>  — Chanoinesse Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/IonGauntletShield" value="Bouclier de gants ioniques"/>
    <entry name="SistersOfBattle/IonGauntletShieldDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/IonGauntletShieldFlavor" value="Le générateur de bouclier ionique monté dans le gantelet droit du Cerastus est plus concentré que le bouclier ionique directionnel monté sur le Paladin chevalier, mais il n'en a pas la souplesse tactique."/>
	<entry name="SistersOfBattle/KeepersOfTheFaith" value="Gardiennes de la foi"/>
	<entry name="SistersOfBattle/KeepersOfTheFaithDescription" value="Empêche la surveillance, mais augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/KeepersOfTheFaithFlavor" value="'La raison dit, tirez quand vous voyez l'ennemi. La raison dit, mets-toi à couvert quand l'ennemi te tire dessus. La raison dit, meurs quand on te tire dessus. La raison ne comprend pas notre foi et comment nous la gardons.'<br/>  — Chanoinesse Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/KnightParry" value="Parade du chevalier"/>
	<entry name="SistersOfBattle/KnightParryDescription" value="Réduit la précision de la mêlée."/>
	<entry name="SistersOfBattle/KnightParryFlavor" value="Le générateur de bouclier ionique monté dans le gantelet droit du Cerastus peut être utilisé pour dévier les coups les plus puissants en combat rapproché."/>
	<entry name="SistersOfBattle/LaudHailer" value="Laudaphone"/>
	<entry name="SistersOfBattle/LaudHailerDescription" value="Cette unité peut encore accomplir des actes de foi si elle est secouée."/>
	<entry name="SistersOfBattle/LaudHailerFlavor" value="<string name='Actions/SistersOfBattle/LaudHailerFlavor'/>"/>
	<entry name="SistersOfBattle/LeadTheRighteous" value="<string name='Actions/SistersOfBattle/LeadTheRighteous'/>"/>
	<entry name="SistersOfBattle/LeadTheRighteousDescription" value="Augmente la précision."/>
	<entry name="SistersOfBattle/LeadTheRighteousFlavor" value="<string name='Actions/SistersOfBattle/LeadTheRighteousFlavor'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperor" value="<string name='Actions/SistersOfBattle/LightOfTheEmperor'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperorDescription" value="<string name='Actions/SistersOfBattle/LightOfTheEmperorDescription'/>"/>
	<entry name="SistersOfBattle/LightOfTheEmperorFlavor" value="<string name='Actions/SistersOfBattle/LightOfTheEmperorFlavor'/>"/>
	<entry name="SistersOfBattle/Martyrdom" value="Le martyr"/>
	<entry name="SistersOfBattle/MartyrdomDescription" value="À la mort, réduit la perte de moral de toutes les unités alliées de l'Adepta Sororitas."/>
	<entry name="SistersOfBattle/MartyrdomFlavor" value="Les sœurs de l'Adepta Sororitas ne cèdent pas au désespoir lorsque leurs chefs sont tués. Au contraire, le sang de ces héros martyrs ne fait que renforcer leur détermination, le sacrifice les incitant à de grands actes d'héroïsme."/>
	<entry name="SistersOfBattle/MartyrSpirit" value="Esprit du martyr"/>
	<entry name="SistersOfBattle/MartyrSpiritDescription" value="À la mort, réduit la perte de moral des unités alliées adjacentes avec Bouclier de la foi."/>
	<entry name="SistersOfBattle/MartyrSpiritFlavor" value="'La perte de nos sœurs et auxiliaires nous a attristés—mais nous a encouragés à redoubler d'efforts.'<br/>  — Souvenir inconnu, Évangile de la Toile"/>
	<entry name="SistersOfBattle/MedicusMinistorum" value="<string name='Actions/SistersOfBattle/MedicusMinistorum'/>"/>
	<entry name="SistersOfBattle/MedicusMinistorumDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/MedicusMinistorumFlavor" value="<string name='Actions/SistersOfBattle/MedicusMinistorumFlavor'/>"/>
	<entry name="SistersOfBattle/MeleeWeaponBonus" value="Lames bénies"/>
	<entry name="SistersOfBattle/MeleeWeaponBonusDescription" value="Augmente la pénétration de l'armure."/>
	<entry name="SistersOfBattle/MeleeWeaponBonusFlavor" value="Les armes des Adepta Sororitas diffèrent peu de celles réquisitionnées par l'Astra Militarum, sauf sur deux points : la qualité de leur entretien, et les bénédictions et supplications sacrées qui leur sont adressées. Seules ces dernières peuvent expliquer les miracles qui se produisent en combat, où ces lames bénies trouvent la moindre faiblesse, encore et encore."/>
	<entry name="SistersOfBattle/MiraculousIntervention" value="<string name='Actions/SistersOfBattle/MiraculousIntervention'/>"/>
	<entry name="SistersOfBattle/MiraculousInterventionDescription" value="<string name='Actions/SistersOfBattle/MiraculousInterventionDescription'/>"/>
	<entry name="SistersOfBattle/MiraculousInterventionFlavor" value="<string name='Actions/SistersOfBattle/MiraculousInterventionFlavor'/>"/>
	<entry name="SistersOfBattle/NonMilitantMobilisation" value="Mobilisation du ministorum"/>
	<entry name="SistersOfBattle/NonMilitantMobilisationDescription" value="Augmente le rendement des réquisitions."/>
	<entry name="SistersOfBattle/NonMilitantMobilisationFlavor" value="Les rites de l'Ecclésiarchie ne sont pas seulement militaristes : des prières et des liturgies existent pour toutes les fonctions de l'Imperium. Être le centre d'intérêt d'un tel rite, qu'il s'agisse du Famulus, du Pronatus ou même du plus simple travailleur, les pousse à de plus grands efforts."/>
	<entry name="SistersOfBattle/OathOfFaith" value="Serment de foi"/>
	<entry name="SistersOfBattle/OathOfFaithDescription" value="Réduit la précision et empêche l'utilisation du Bouclier de la Foi."/>
	<entry name="SistersOfBattle/OathOfFaithFlavor" value="'Nous avons toutes prêtées serment, de défendre l'Imperium, de purger les hérétiques et les aliens. Cela nous a soutenu, nous a poussé à traverser la douleur et la peur. Mais quand nos sœurs ont finalement craqué et se sont enfuies, notre serment les a hantées… Si elles survivaient à leur fuite, elles reviendraient en tant que Sœurs Repentia…'<br/>  — Souvenirs inconnus, Évangile de la Voie de la toile"/>
	<entry name="SistersOfBattle/ParagonWarsuit" value="Exo-harnais Parangon"/>
	<entry name="SistersOfBattle/ParagonWarsuitDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/ParagonWarsuitFlavor" value="Parmi l'arsenal sacré d'un Ordre Militant, les anciens Exo-harnais Parangon sont considérés comme des vêtements sacrés dotés d'une noble volonté. Seuls les Célestes les plus valeureuses peuvent maîtriser son esprit pour l'utiliser au combat."/>
	<entry name="SistersOfBattle/Protected" value="Protégé"/>
	<entry name="SistersOfBattle/ProtectedDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/ProtectedFlavor" value="<string name='Actions/SistersOfBattle/BodyguardFlavor'/>"/>
	<entry name="SistersOfBattle/PsyShock" value="Choc psy"/>
	<entry name="SistersOfBattle/PsyShockDescription" value="Étourdit les psykers."/>
	<entry name="SistersOfBattle/PsyShockFlavor" value="<string name='Weapons/CondemnorBoltgunSilverStake'/>"/>
	<entry name="SistersOfBattle/PurifyingRecitations" value="Récitations purificatrices"/>
	<entry name="SistersOfBattle/PurifyingRecitationsDescription" value="Augmente les dégâts."/>
	<entry name="SistersOfBattle/PurifyingRecitationsFlavor" value="'Nos ennemis nous appellent froides, inhumaines, insensibles. Mais nous ressentons, nous pleurons, nous rageons. Laissez-les sentir notre chaleur dans nos flammes, nos fuseurs.'<br/>  — Chanoinesse Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/RagingFervour" value="Ferveur déchaînée"/>
	<entry name="SistersOfBattle/RagingFervourDescription" value="Augmente les dégâts."/>
	<entry name="SistersOfBattle/RagingFervourFlavor" value="'Les armes dans nos mains sont les instruments de notre foi. Chaque projectile est une prière envoyée au cœur de ceux qui n'ont pas la foi, répandant sa parole dans leur être même.'<br/>  — Chanoinesse Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/RighteousJudgement" value="<string name='Actions/SistersOfBattle/RighteousJudgement'/>"/>
	<entry name="SistersOfBattle/RighteousJudgementDescription" value="<string name='Actions/SistersOfBattle/RighteousJudgementDescription'/>"/>
	<entry name="SistersOfBattle/RighteousJudgementFlavor" value="<string name='Actions/SistersOfBattle/RighteousJudgementFlavor'/>"/>
	<entry name="SistersOfBattle/SacresantShield" value="Sacré-saint Bouclier"/>
	<entry name="SistersOfBattle/SacresantShieldDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/SacresantShieldFlavor" value="Les fidèles Célestes sacro-saintes tiennent bon contre les pires horreurs de la galaxie. Des hordes de mutants et d'hérétiques s'écrasent futilement contre les murs de leur bouclier avant d'être terrassées par la fureur de la justice."/>
	<entry name="SistersOfBattle/SaintlyBlessings" value="Bénédictions de la sainteté"/>
	<entry name="SistersOfBattle/SaintlyBlessingsDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/SaintlyBlessingsFlavor" value="<string name='Actions/SistersOfBattle/SaintlyBlessingsFlavor'/>"/>
	<entry name="SistersOfBattle/ShieldOfFaith" value="Bouclier de la foi"/>
	<entry name="SistersOfBattle/ShieldOfFaithDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SistersOfBattle/ShieldOfFaithFlavor" value="On enseigne aux membres de l'Adepta Sororitas que la foi est un bouclier plus solide que toute armure. La puissance de leur croyance que l'Empereur les protégera est telle que les Adepta Sororitas peuvent se débarrasser des blessures les plus graves et résister à la sorcellerie des sorciers ennemis."/>
	<entry name="SistersOfBattle/SimulacrumImperialis" value="Simulacrum Imperialis"/>
	<entry name="SistersOfBattle/SimulacrumImperialisDescription" value="Réduit le temps de recharge des Actes de Foi."/>
	<entry name="SistersOfBattle/SimulacrumImperialisFlavor" value="Ces symboles sacrés de l'Ecclésiarchie ont été portés par l'un des nombreux saints de l'Imperium, ou ont même été façonnés à partir de leurs os. Ce sont des sources d'inspiration et de foi et c'est un grand honneur de porter une relique aussi irremplaçable au combat."/>
	<entry name="SistersOfBattle/SisterSuperior" value="Sœur supérieure"/>
	<entry name="SistersOfBattle/SisterSuperiorDescription" value="Augmente le moral des unités d'infanterie et des unités Parangon."/>
	<entry name="SistersOfBattle/SisterSuperiorFlavor" value="Ce sont les Sœurs Supérieures qui constituent les véritables chevilles ouvrières de chaque escouade de Sœurs de bataille. S'exprimant avec l'autorité que leur confèrent des années d'expérience du combat et une foi suprême dans le Dieu-Empereur, ces officiers remarquables veillent à ce que chaque Sœur sous leur commandement se batte au maximum de ses capacités et de son endurance, maximisant ainsi l'impact stratégique de l'escouade dans son ensemble."/>
	<entry name="SistersOfBattle/SolaceInAnguish" value="<string name='Actions/SistersOfBattle/SolaceInAnguish'/>"/>
	<entry name="SistersOfBattle/SolaceInAnguishDescription" value="<string name='Actions/SistersOfBattle/SolaceInAnguishDescription'/>"/>
	<entry name="SistersOfBattle/SolaceInAnguishFlavor" value="<string name='Actions/SistersOfBattle/SolaceInAnguishFlavor'/>"/>
	<entry name="SistersOfBattle/StirringRhetoric" value="<string name='Actions/SistersOfBattle/StirringRhetoric'/>"/>
	<entry name="SistersOfBattle/StirringRhetoricDescription" value="Augmente l'armure."/>
	<entry name="SistersOfBattle/StirringRhetoricFlavor" value="<string name='Actions/SistersOfBattle/StirringRhetoricFlavor'/>"/>
	<entry name="SistersOfBattle/ThePassion" value="<string name='Actions/SistersOfBattle/ThePassion'/>"/>
	<entry name="SistersOfBattle/ThePassionDescription" value="Augmente la précision en mêlée."/>
	<entry name="SistersOfBattle/ThePassionFlavor" value="<string name='Actions/SistersOfBattle/ThePassionFlavor'/>"/>
	<entry name="SistersOfBattle/UsedActOfFaith" value="Acte de foi utilisé"/>
	<entry name="SistersOfBattle/UsedActOfFaithDescription" value="Cette unité a utilisé sa capacité d'acte de foi ce tour-ci."/>
	<entry name="SistersOfBattle/UsedActOfFaithFlavor" value="<string name='Traits/SistersOfBattle/UsedActOfFaithDescription'/>"/>
	<entry name="SistersOfBattle/UsedSacredRite" value="Utilisé 1 Rite Sacré"/>
 	<entry name="SistersOfBattle/UsedSacredRite2" value="Utilisé 2 Rites Sacrés"/>
	<entry name="SistersOfBattle/VengefulSpirit" value="Esprit vengeur"/>
	<entry name="SistersOfBattle/VengefulSpiritDescription" value="Renvoie les dégâts à l'attaquant."/>
	<entry name="SistersOfBattle/VengefulSpiritFlavor" value="'Et dans la mort, nous avons une dernière chance—une libération des soucis. Une fois mort, plus rien ne retient notre fureur, plus besoin de se protéger. Nous pouvons utiliser tout ce que nous sommes, prendre tous les coups qui se présentent à nous, pour une dernière chance de justice.'<br/>  — Chanoinesse Vandire, In Nomine Strategi"/>
	<entry name="SistersOfBattle/WarHymn" value="<string name='Actions/SistersOfBattle/WarHymn'/>"/>
	<entry name="SistersOfBattle/WarHymnDescription" value="Augmente les attaques."/>
	<entry name="SistersOfBattle/WarHymnFlavor" value="<string name='Actions/SistersOfBattle/WarHymnFlavor'/>"/>
	<entry name="SistersOfBattle/WarmachinesWrath" value="Colère mécanisée"/>
	<entry name="SistersOfBattle/WarmachinesWrathDescription" value="Augmente le rendement de la production."/>
	<entry name="SistersOfBattle/WarmachinesWrathFlavor" value="'En ces derniers jours, la ferveur s'est répandue. Le Rite de Suppléance a été accompli, pour exiger le maximum des esprits-machines qui dirigeaient la Manufactora et les hangars où les anciennes machines de guerre étaient ointes. Ils n'ont pas déçu.'<br/>  — Souvenir inconnu, Évangile de la Toile"/>	
 	<entry name="SistersOfBattle/TaleOfTheFaithful" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithful'/>"/>
 	<entry name="SistersOfBattle/TaleOfTheFaithfulDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithfulDescription'/>"/>
 	<entry name="SistersOfBattle/TaleOfTheFaithfulFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheFaithfulFlavor'/>"/>
 	<entry name="SistersOfBattle/TaleOfTheStoic" value="<string name='Actions/SistersOfBattle/TaleOfTheStoic'/>"/>
 	<entry name="SistersOfBattle/TaleOfTheStoicDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheStoicDescription'/>"/>
	<entry name="SistersOfBattle/TaleOfTheStoicFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheStoicFlavor'/>"/>
 	<entry name="SistersOfBattle/TaleOfTheWarrior" value="<string name='Actions/SistersOfBattle/TaleOfTheWarrior'/>"/>
 	<entry name="SistersOfBattle/TaleOfTheWarriorDescription" value="<string name='Actions/SistersOfBattle/TaleOfTheWarriorDescription'/>"/>
 	<entry name="SistersOfBattle/TaleOfTheWarriorFlavor" value="<string name='Actions/SistersOfBattle/TaleOfTheWarriorFlavor'/>"/>
	<entry name="SkilledJink" value="<string name='Actions/SkilledJink'/>"/>
 	<entry name="SkilledJinkDescription" value="<string name='Traits/JinkDescription'/>"/>
 	<entry name="SkilledJinkFlavor" value="<string name='Actions/SkilledJinkFlavor'/>"/>
	<entry name="Skimmer" value="Antigrav"/>
	<entry name="SkimmerDescription" value="Cette unité peut se déplacer sur l'eau. Ignore les pénalités des rivières et de l'herbe barbelée. Réduit la pénalité de mouvement dans les forêts et les ruines impériales."/>
	<entry name="SkimmerFlavor" value="Certains véhicules hautement avancés sont équipés de moteurs anti-gravité qui leur permet de se déplacer rapidement au-dessus d'un terrain difficile et des troupes, les rendant parfaits pour des attaques surprises de flanc."/>
	<entry name="SkullAltar" value="<string name='Features/SkullAltar'/>"/>
	<entry name="SkullAltarDescription" value="Accorde une récompense aux unités qui arrivent sur la case."/>
	<entry name="SkullAltarFlavor" value="<string name='Features/SkullAltarFlavor'/>"/>
	<entry name="SkullsForTheSkullThrone" value="Des crânes pour le Trône de Crâne"/>
	<entry name="SkullsForTheSkullThroneDescription" value="Augmente la réduction des dégâts."/>
	<entry name="SkullsForTheSkullThroneFlavor" value="Khorne ne respecte que les forts et ceux qui tuent sans limite."/>	
	<entry name="Skyfire" value="Antiaérien"/>
	<entry name="SkyfireDescription" value="Augmente la précision contre les volants. Réduit la précision contre les unités terrestres qui ne sont pas antigravs, motojets ou avec des réacteurs dorsaux."/>
	<entry name="SkyfireFlavor" value="Les armes antiaériennes excellent pour abattre les aéronefs et antigravs ennemis."/>
	<entry name="SlowAndPurposeful" value="Lent et méthodique"/>
	<entry name="SlowAndPurposefulDescription" value="Annule la pénalité pour les armes lourdes et d'artillerie."/>
	<entry name="SlowAndPurposefulFlavor" value="Beaucoup de guerriers sont stables et assurés, lents mais pas moins meurtriers pour autant."/>
	<entry name="Slowed" value="Ralenti"/>
	<entry name="SlowedDescription" value="Réduit le mouvement."/>
	<entry name="Smash" value="Concassage"/>
	<entry name="SmashDescription" value="Augmente la pénétration d'armure au corps à corps."/>
	<entry name="SmashFlavor" value="Pour les créatures les plus terrifiants, un seul coup suffit pour pénétrer le blindage d'un tank ou pour transformer une créature vivante en pulpe sanguinolente."/>
	<entry name="SmokeScreen" value="Fumigènes"/>
	<entry name="SmokeScreenDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="SmokeScreenFlavor" value="<string name='Actions/CreateSmokeScreenFlavor'/>"/>
 	<entry name="Sniper" value="Sniper"/>
 	<entry name="SniperDescription" value="Augmente les dégâts et la pénétration d'armure contre l'infanterie et les créatures monstrueuses."/>
 	<entry name="SniperFlavor" value="Les armes de sniper sont des instruments de précisions, utilisés pour frapper les points faibles de la cible."/>
	<entry name="SonicBoom" value="Bang supersonique"/>
	<entry name="SonicBoomDescription" value="Augmente les dégâts contre les volants."/>
	<entry name="SoulBlaze" value="Feu de l'âme"/>
	<entry name="SoulBlazeDescription" value="Inflige des dégâts tous les tours."/>
	<entry name="SoulBlazed" value="Feu de l'âme"/>
	<entry name="SoulBlazedDescription" value="<string name='Traits/SoulBlazeDescription'/>"/>
	<entry name="SpaceMarines/BlastDamage" value="<string name='Traits/AstraMilitarum/BlastDamage'/>"/>
	<entry name="SpaceMarines/BlastDamageDescription" value="<string name='Traits/AstraMilitarum/BlastDamageDescription'/>"/>
	<entry name="SpaceMarines/BlastDamageFlavor" value="<string name='Traits/AstraMilitarum/BlastDamageFlavor'/>"/>
	<entry name="SpaceMarines/BoltDamage" value="<string name='Traits/AstraMilitarum/BoltDamage'/>"/>
	<entry name="SpaceMarines/BoltDamageDescription" value="<string name='Traits/AstraMilitarum/BoltDamageDescription'/>"/>
	<entry name="SpaceMarines/BoltDamageFlavor" value="<string name='Traits/AstraMilitarum/BoltDamageFlavor'/>"/>
	<entry name="SpaceMarines/BolterDiscipline" value="Discipline du bolter"/>
	<entry name="SpaceMarines/BolterDisciplineDescription" value="<string name='Traits/ChaosSpaceMarines/MaliciousVolleysDescription'/>"/>
	<entry name="SpaceMarines/BolterDisciplineFlavor" value="Pour un Space Marine, le bolter est plus qu'une arme, c'est l'instrument de la divinité de l'humanité qui apporte la mort à ses ennemis."/>
	<entry name="SpaceMarines/CloseQuartersFirepower" value="Puissance de feu rapprochée"/>
 	<entry name="SpaceMarines/CloseQuartersFirepowerDescription" value="Augmente la pénétration de l'armure à distance."/>
 	<entry name="SpaceMarines/CloseQuartersFirepowerFlavor" value="À plus courte portée, les armes conçues par Belisarius Cawl pour ses marines Primaris sont mortellement efficaces, ce qui encourage les meilleurs éléments de l'Empereur à réduire la distance avec leurs ennemis."/>
 	<entry name="SpaceMarines/DutyEternal" value="Devoir éternel"/>
 	<entry name="SpaceMarines/DutyEternalDescription" value="Augmente la réduction des dégâts de l'invulnérabilité."/>
 	<entry name="SpaceMarines/DutyEternalFlavor" value="Tous les Adeptus Astartes possèdent une volonté indomptable. Cependant, les quelques Primaris qui ont le privilège d'être enfermés dans un Redemptor Dreadnought après leur mort effective ont une responsabilité plus profonde, celle d'être à la hauteur de cet honneur unique. Ils continueront à se battre, quelle que soit la gravité de leur blessure ou des dommages subis."/>
	<entry name="SpaceMarines/CityTier2" value="Expansion de la forteresse"/>
	<entry name="SpaceMarines/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="SpaceMarines/CityTier2Flavor" value="La seconde étape de la construction de la forteresse a commencé. Les infrastructures principales étant finies, les techmarines sont autorisés à étendre leurs opérations et à crééer une seconde ligne défensive."/>
	<entry name="SpaceMarines/CityTier3" value="Redoutes avancées"/>
	<entry name="SpaceMarines/CityTier3Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier3Flavor" value="La troisième étape de la construction de la forteresse implique la création de garnisons et de contreforts indépendants accompagnés d'une ligne supplémentaires de murs pour la ville. Les serviteurs et les serfs passent leur vie dans l'ombre de ces barrières cylopéennes, se hâtant d'une tâche à l'autre."/>
	<entry name="SpaceMarines/CityTier4" value="Forteresse Suprême"/>
	<entry name="SpaceMarines/CityTier4Description" value="<string name='Traits/SpaceMarines/CityTier2Description'/>"/>
	<entry name="SpaceMarines/CityTier4Flavor" value="Avec cette expansion, le Maître Artificier déclare la forteresse complète. Maintenant elle rivalise en taille avec une ville ruche. Dans ses murs imprenables, vous vous croiriez n'importe où dans l'Imperium. En-dehors, les tribus locales traversent d'énormes distances pour payer leur tribut, alors que leurs meilleurs guerriers espèrent faire partie des compétitions meurtrières qui pourront mener à leur transformation en Space Marine."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReduction" value="Protection de la forteresse"/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionDescription" value="Augmente la réduction de dégâts invulnérable."/>
	<entry name="SpaceMarines/FortressOfRedemptionDamageReductionFlavor" value="Gladius Prime s'est déjà montrée bien plus hostile que les pires prévisions de l'Adeptus Administratum. Les défenses physiques de la forteresse subissant des assauts constants, les techmarines ont approuvé l'installation d'un bouclier void. Une fois construit, ce dôme d'énergie scintillant pourra absorber la puissance de feu de régiments entiers (ou même d'un Chevalier impérial rebelle) avant de s'effondrer."/>
	<entry name="SpaceMarines/LasDamage" value="<string name='Traits/AstraMilitarum/LasDamage'/>"/>
	<entry name="SpaceMarines/LasDamageDescription" value="<string name='Traits/AstraMilitarum/LasDamageDescription'/>"/>
	<entry name="SpaceMarines/LasDamageFlavor" value="<string name='Traits/AstraMilitarum/LasDamageFlavor'/>"/>
	<entry name="SpaceMarines/MeleeDamage" value="Maîtrise du corps-à-corps"/>
	<entry name="SpaceMarines/MeleeDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="SpaceMarines/MeleeDamageFlavor" value="Certains chapitres de l'Adeptus Astartes — les Space Wolves, les Blood Drinkers, les Minotaures — sont réputés pour leur maîtrise du combat au corps-à-corps. Être en prise avec même le Scout de plus bas étage d'un tel chapitre revient à combattre des générations d'entraînement et de connaissance."/>
	<entry name="SpaceMarines/Omniscope" value="<string name='Actions/SpaceMarines/Omniscope'/>"/>
 	<entry name="SpaceMarines/OmniscopeDescription" value="<string name='Actions/SpaceMarines/OmniscopeDescription'/>"/>
 	<entry name="SpaceMarines/OmniscopeFlavor" value="<string name='Actions/SpaceMarines/OmniscopeFlavor'/>"/>
	<entry name="SpaceMarines/RepulsorField" value="<string name='Actions/SpaceMarines/RepulsorField'/>"/>
 	<entry name="SpaceMarines/RepulsorFieldDescription" value="Réduit le mouvement."/>
 	<entry name="SpaceMarines/RepulsorFieldFlavor" value="<string name='Actions/SpaceMarines/RepulsorFieldFlavor'/>"/>
	<entry name="SpaceMarines/StormShield" value="Bouclier tempête"/>
 	<entry name="SpaceMarines/StormShieldDescription" value="Augmente la réduction des dégâts d'invulnérabilité."/>
 	<entry name="SpaceMarines/StormShieldFlavor" value="Un bouclier tempête est un grand bouclier solide dans lequel est intégré un générateur de champ d'énergie. Bien que le bouclier offre une protection physique, c'est le champ d'énergie qui est le plus impressionnant, car il est capable de dévier presque toutes les attaques. Même les coups qui auraient normalement traversé l'armure du Terminator sont facilement repoussés par les énergies protectrices du bouclier."/>
	<entry name="SpaceMarines/SuppressiveBombardment" value="Bombardement suppressif"/>
 	<entry name="SpaceMarines/SuppressiveBombardmentDescription" value="Augmente les dégâts et les attaques pour immobiliser l'infanterie ennemie lorsqu'il est adjacent à un autre Whirlwind."/>
 	<entry name="SpaceMarines/SuppressiveBombardmentFlavor" value="Selon le Codex Astartes, les Whirlwinds opèrent de préférence en groupes plus importants et déclenchent un barrage roulant qu'il est impossible d'éviter, mais qu'il faut au contraire endurer. Même à l'abri, les troupes et les véhicules à l'armure plus légère sont complètement dévastés par la pluie incessante d'ogives."/>
	<entry name="SpaceMarines/Thunderstrike" value="Thunderstrike"/>
 	<entry name="SpaceMarines/ThunderstrikeDescription" value="Augmente les dégâts à distance des unités de Space Marines contre les cibles attaquées par cette unité."/>
 	<entry name="SpaceMarines/ThunderstrikeFlavor" value="Les Storm Speeders lourdement armés conçus par Belisarius Cawl sont spécialisés dans la lutte contre certaines cibles, le Thunderstrike se concentrant sur l'élimination des véhicules ennemis et des cibles clés. En effet, un ennemi paralysé par ses armements devient encore plus vulnérable aux attaques de suivi de ses frères de bataille."/>
 	<entry name="SpaceMarines/ThunderstrikeTarget" value="<string name='Traits/SpaceMarines/Thunderstrike'/>"/>
 	<entry name="SpaceMarines/ThunderstrikeTargetDescription" value="Augmente les dégâts à distance des unités de Space Marines contre cette unité."/>
 	<entry name="SpaceMarines/ThunderstrikeTargetFlavor" value="<string name='Traits/SpaceMarines/ThunderstrikeFlavor'/>"/>
	<entry name="SpaceSlip" value="<string name='Actions/SpaceSlip'/>"/>
	<entry name="SpaceSlipDescription" value="Augmente la réduction des dégâts."/>
	<entry name="Stealth" value="Discrétion"/>
	<entry name="StealthDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="StealthFlavor" value="Certains guerriers sont maîtres dans l'art du déguisement et de la dissimulation, les rendant capable de disparaître dans le chaos d'un champ de bataille jusqu'à ce qu'ils soient prêts à frapper."/>
	<entry name="StrafingRun" value="Mitraillage"/>
	<entry name="StrafingRunDescription" value="Augmente la précision à distance contre les unités terrestres."/>
	<entry name="StrafingRunFlavor" value="Ce véhicule est conçu pour attaquer les unités à terre, l'écart et la distance de convergence de ses armes sont réfléchis pour maximiser le carnage sur les ennemis en-dessous de lui."/>
	<entry name="Strikedown" value="Renversement"/>
	<entry name="StrikedownDescription" value="Réduit temporairement le mouvement de l'unité d'infanterie ciblée."/>
	<entry name="StrikedownFlavor" value="Un coup suffisamment puissant peut renverser même le plus puissant des guerriers."/>
	<entry name="Stubborn" value="Obstiné"/>
	<entry name="StubbornDescription" value="Réduit les pertes de moral."/>
	<entry name="StubbornFlavor" value="De nombreux guerriers vivent et meurent selon le principe de 'la mort avant le déshonneur'. Ces guerriers reculent rarement, même d'un pas, face au danger."/>
	<entry name="Stunned" value="Incapacité"/>
	<entry name="StunnedDescription" value="Empêche l'unité de se déplacer ou d'agir."/>
	<entry name="Suicider" value="Suicidaire"/>
	<entry name="SuiciderDescription" value="Les ennemis ne gagnent pas d'expérience lorsque l'unité se tue elle-même."/>
	<entry name="Summon" value="Invocation"/>
	<entry name="SummonDescription" value="Classification."/>
	<entry name="SuperHeavy" value="Super-lourd"/>
	<entry name="SuperHeavyDescription" value="Classification."/>
	<entry name="SuperHeavyFlavor" value="Ces constructions immenses bardées de blindage ont toutes assez de puissance de feu pour vaporiser, écraser ou incinérer une armée entière."/>
	<entry name="Supersonic" value="Supersonique"/>
	<entry name="SupersonicDescription" value="Augmente le mouvement."/>
	<entry name="SupersonicFlavor" value="Les véhicules supersoniques sont suprêmement rapides, même selon les standards de l'aviation, les rendant exceptionnellement mobiles au combat."/>
	<entry name="Swarms" value="Nuées"/>
	<entry name="SwarmsDescription" value="Les dégâts subis sont répartis équitablement dans toute l'unité, mais elle prend davantage d'attaques contre les attaques à explosion et à souffle."/>
	<entry name="SwarmsFlavor" value="Ces créatures sont si nombreuses qu'elles ne peuvent pas être éliminées individuellement mais doivent être affrontées en groupe."/>
	<entry name="Swiftstrike" value="Frappe rapide"/>
	<entry name="SwiftstrikeDescription" value="Augmente les attaques"/>
	<entry name="TacticalDoctrine" value="Doctrine tactique"/>
	<entry name="TacticalDoctrineDescription" value="Augmente la précision."/>
	<entry name="TacticalDoctrineFlavor" value="<string name='Actions/TacticalDoctrineFlavor'/>"/>
	<entry name="Tank" value="Tank"/>
	<entry name="TankDescription" value="Classification."/>
	<entry name="TankFlavor" value="Les tanks peuvent utiliser leur poids comme une arme, se jetant droit dans et à travers des formations denses d'ennemis. Cela sème souvent la confusion dans les lignes ennemies, car avoir un béhémoth de métal foncer sur vous est stressant pour n'importe qui."/>
	<entry name="TankHunters" value="Tueurs de chars"/>
	<entry name="TankHuntersDescription" value="Augmente la pénétration d'armure contre les véhicules ennemis."/>
	<entry name="TankHuntersFlavor" value="Ces vétérans de la guerre contre les blindés sont capables d'identifier les points faibles des véhicules ennemis et de viser en conséquence."/>
	<entry name="Tau/AdvancedTargetingSystem" value="Système de ciblage avancé"/>
	<entry name="Tau/AdvancedTargetingSystemDescription" value="Augmente la précision à distance."/>
	<entry name="Tau/AdvancedTargetingSystemFlavor" value="Un système de ciblage avancé permet au tireur d'un véhicule d'identifier les cibles particulièrement intéressantes ou dangereuses et de déterminer des solutions de tirs pour les contrer."/>
	<entry name="Tau/AutomatedRepairSystem" value="Système de réparation automatisé"/>
	<entry name="Tau/AutomatedRepairSystemDescription" value="Restaure des points de vie tous les tours."/>
	<entry name="Tau/AutomatedRepairSystemFlavor" value="De minuscules drones de maintenance s'agglutinent sur les systèmes endommagés pour les réparer en plein combat."/>
	<entry name="Tau/BlacksunFilter" value="Photo-régulateur"/>
	<entry name="Tau/BlacksunFilterDescription" value="Augmente la vision."/>
	<entry name="Tau/BlacksunFilterFlavor" value="Cet assemblage de filtres optiques permet aux senseurs des véhicules de viser les ennemis à pleine précision et portée, même durant les opérations nocturnes."/>
	<entry name="Tau/BlastDamage" value="Phasage d'explosion"/>
	<entry name="Tau/BlastDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Tau/BlastDamageFlavor" value="Les armes à zone d'effet ont toujours été mieux utilisées en attaquant de grandes formations d'infanterie plutôt que des cibles blindées comme les véhicules—mais les ingénieurs de la Caste de la Terre ont trouvé un moyen pour contourner ça. En changeant la phase des composants énergétiques des armes explosives et incendiaires, ils peuvent assurer qu'au moins une partie de leur effet va ‘couler’ à travers n'importe quelle protection."/>
	<entry name="Tau/BoltDamage" value="Manufacture Brachyurienne"/>
	<entry name="Tau/BoltDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Tau/BoltDamageFlavor" value="Même le meilleur technicien de la Caste de la Terre ne peut pas assembler les composants de leurs armes rail à la main. Mais en confiant ce travail à la minuscule race brachyurienne et en leur fournissant des outils miniaturisés, ils peuvent diminuer la taille et réduire la déviation fondamentale à toute opération délicate, permettant la création d'armes encore plus finement réglées."/>
	<entry name="Tau/BreakComposure" value="<string name='Actions/Tau/BreakComposure'/>"/>
	<entry name="Tau/BreakComposureDescription" value="Les unités attaquées perdent du moral."/>
	<entry name="Tau/BreakComposureFlavor" value="<string name='Actions/Tau/BreakComposureFlavor'/>"/>
	<entry name="Tau/CityTier2" value="Dissémination"/>
	<entry name="Tau/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="Tau/CityTier2Flavor" value="Une fois qu'une ville atteint une certaine taille, la Caste de l'Eau commence une offensive diplomatique pour amener davantage de civiles dans la Voie—les bombardant de propagande adaptée de toutes formes, depuis les jeter de brochures aux émissions vidéos, radios et aux crieurs de rue."/>
	<entry name="Tau/CityTier3" value="Fondations Anthrazods"/>
	<entry name="Tau/CityTier3Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="Tau/CityTier3Flavor" value="Les Anthrazods à la peau épaisse sont une race lente d'esprit habituellement employée par les T'au pour les activités dangereuses demandant beaucoup de force, comme miner les astéroïdes—mais dirigée par les Demiurges et la Caste de la Terre, elle peut produire les infrastructures d'une ville et des tunnels de transport comme personne d'autre, reliant des secteurs distants avec facilité."/>
	<entry name="Tau/ClusterFire" value="Incendie en grappe"/>
 	<entry name="Tau/ClusterFireDescription" value="Augmente les attaques et les dégâts contre les unités qui sont des motos, des jetbikes ou qui sont très encombrantes. Augmente fortement les attaques et les dégâts contre les unités qui sont des créatures monstrueuses, des véhicules ou des fortifications."/>
 	<entry name="Tau/ClusterFireFlavor" value="Les canons à sous-munitions à impulsion des R'Varna, uniques en leur genre, fonctionnent à des distances extrêmes, saturant la zone ciblée de micro-éclats de plasma. Plus la cible est grande, plus le nombre d'éclats de plasma est élevé, ce qui permet de la réduire facilement en petits morceaux."/>
	<entry name="Tau/CounterfireDefenceSystem" value="Système de défense Contre-feu"/>
	<entry name="Tau/CounterfireDefenceSystemDescription" value="Augmente la précision des tirs en état d'alerte."/>
	<entry name="Tau/CounterfireDefenceSystemFlavor" value="<string name='Actions/Tau/CounterfireDefenceSystemFlavor'/>"/>
	<entry name="Tau/DisruptionPod" value="Nacelle de brouillage"/>
	<entry name="Tau/DisruptionPodDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="Tau/DisruptionPodFlavor" value="Une nacelle de brouillage projette des images déformées à la fois dans le spectre visuel et magnétique, rendant plus difficile de cibler le véhicule à distance."/>
	<entry name="Tau/DroneController" value="Contrôleur de drones"/>
	<entry name="Tau/DroneControllerDescription" value="Augmente la précision des drones adjacents."/>
	<entry name="Tau/DroneControllerFlavor" value="<string name='Actions/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/DroneControllerInRange" value="Contrôleur de drones à portée"/>
	<entry name="Tau/DroneControllerInRangeDescription" value="Augmente la précision"/>
	<entry name="Tau/DroneControllerInRangeFlavor" value="<string name='Actions/Tau/DroneControllerFlavor'/>"/>
	<entry name="Tau/FieldAmplifierRelay" value="Relais de l'amplificateur de champ"/>
 	<entry name="Tau/FieldAmplifierRelayDescription" value="Augmente la réduction des dégâts d'invulnérabilité lorsque l'unité est protégée par un bouclier."/>
 	<entry name="Tau/FieldAmplifierRelayFlavor" value="Prenant la forme d'un sac à dos léger, le relais amplificateur de champ capte le champ de force protecteur du drone bouclier, le répartit en un parapluie énergisé sur son porteur et transmet le signal à d'autres relais à portée."/>
	<entry name="Tau/FireTeam" value="Equipe de tir"/>
	<entry name="Tau/FireTeamDescription" value="Augmente la précision à distance lorsqu'adjacent à un véhicule allié ou une créature monstrueuse alliée."/>
	<entry name="Tau/FireTeamFlavor" value="Les systèmes sensoriels de certaines exo-armures et chars peuvent être reliés pour fournir une efficacité augmentée lorsqu'ils se battent en équipe de tir."/>
	<entry name="Tau/FlechetteDischarger" value="Cribleur"/>
	<entry name="Tau/FlechetteDischargerDescription" value="Inflige des dégâts aux attaquants aux corps à corps."/>
	<entry name="Tau/FlechetteDischargerFlavor" value="De puissantes grappes de charges réactives sont attachées à la coque de nombreux véhicules T'au. Si l'ennemi s'approche, les grappes relâchent des nuages vicieux de fléchettes à grand vitesse."/>
	<entry name="Tau/GhostkeelElectrowarfareSuite" value="Brouillage actif Ghostkeel"/>
	<entry name="Tau/GhostkeelElectrowarfareSuiteDescription" value="Augmente la réduction de dégâts à distance contre les attaques provenant de 2 de distance ou plus."/>
	<entry name="Tau/GhostkeelElectrowarfareSuiteFlavor" value="L'IA de brouillage de la Ghostkeel scanne agressivement les spectres de ciblage de l'ennemi et envahit les senseurs de l'ennemi, leur fournissant des fausses informations et des données-poubelles rendant pratiquement impossible de tirer effectivement sur l'exo-armure à distance."/>
	<entry name="Tau/HolophotonCountermeasures" value="<string name='Actions/Tau/HolophotonCountermeasures'/>"/>
	<entry name="Tau/HolophotonCountermeasuresDescription" value="Réduit la précision à distance."/>
	<entry name="Tau/HolophotonCountermeasuresFlavor" value="<string name='Actions/Tau/HolophotonCountermeasuresFlavor'/>"/>
	<entry name="Tau/IntegratedShieldGenerator" value="Générateur de bouclier intégré"/>
	<entry name="Tau/IntegratedShieldGeneratorDescription" value="Augmente la réduction des dégâts d'invulnérabilité et accorde une réduction améliorée des dégâts d'invulnérabilité à distance."/>
	<entry name="Tau/IntegratedShieldGeneratorFlavor" value="Contrairement au Riptide, la combinaison de combat R'varna est destinée à l'appui-feu à longue distance et non à la mobilité, ce qui lui permet de porter une armure plus lourde. De même, son générateur de bouclier reste efficace contre les assauts rapprochés, mais fonctionne de manière optimale lorsque l'ennemi attaque à distance."/>
	<entry name="Tau/Kauyon" value="<string name='Actions/Tau/Kauyon'/>"/>
	<entry name="Tau/KauyonDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="Tau/KauyonFlavor" value="<string name='Actions/Tau/KauyonFlavor'/>"/>
	<entry name="Tau/LasDamage" value="Accélérateurs en Mor'tonium."/>
	<entry name="Tau/LasDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Tau/LasDamageFlavor" value="Comme l'humanité a appris à ses dépends, de manière répétée, l'utilisation des matériaux radioactifs dans les armes est dangereux. Il est raisonnable de penser que les T'au le savent, mais ont quand même choisi d'utiliser le Mor'tonium récemment découvert dans leur technologie militaire malgré tout. Lorsqu'il est exposé à l'air, il se désintègre en une explosion massive d'énergie ionique que les T'au utilisent."/>
	<entry name="Tau/MobileDefencePlatform" value="Plateforme de défense mobile"/>
	<entry name="Tau/MobileDefencePlatformDescription" value="Augmente le mouvement lorsque l'unité transporte du cargo."/>
	<entry name="Tau/Montka" value="<string name='Actions/Tau/Montka'/>"/>
	<entry name="Tau/MontkaDescription" value="Augmente les dégâts contre les unités en-dessous de 50% de vie."/>
	<entry name="Tau/MontkaFlavor" value="<string name='Actions/Tau/MontkaFlavor'/>"/>
	<entry name="Tau/NetworkedMarkerlight" value="Désignateur laser multinode"/>
	<entry name="Tau/NetworkedMarkerlightDescription" value="Augmente la précision à distance et ignore la réduction de dégâts à distance. Les attaques de cette unité ne bénéficient ni ne consument Cible acquise."/>
	<entry name="Tau/NetworkedMarkerlightFlavor" value="Ce type de Désignateur Laser est relié directement aux systèmes d’arme, améliorant grandement l’acuité de leurs tirs."/>
	<entry name="Tau/NovaBoost" value="<string name='Actions/Tau/NovaBoost'/>"/>
	<entry name="Tau/NovaBoostDescription" value="Augmente le mouvement."/>
	<entry name="Tau/NovaBoostFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaFire" value="<string name='Actions/Tau/NovaFire'/>"/>
	<entry name="Tau/NovaFireDescription" value="Augmente les attaques"/>
	<entry name="Tau/NovaFireFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaShield" value="<string name='Actions/Tau/NovaShield'/>"/>
	<entry name="Tau/NovaShieldDescription" value="Augmente la réduction de dégâts."/>
	<entry name="Tau/NovaShieldFlavor" value="<string name='Actions/Tau/NovaBoostFlavor'/>"/>
	<entry name="Tau/NovaElectromagneticShockwave" value="<string name='Actions/Tau/NovaElectromagneticShockwave'/>"/>
 	<entry name="Tau/NovaElectromagneticShockwaveDescription" value="Atteint tous les membres du groupe de l'unité cible."/>
 	<entry name="Tau/NovaElectromagneticShockwaveFlavor" value="<string name='Actions/Tau/NovaElectromagneticShockwaveFlavor'/>"/>
	<entry name="Tau/PointDefenceTargetingRelay" value="Relais de défense ponctuelle"/>
	<entry name="Tau/PointDefenceTargetingRelayDescription" value="Augmente les dégâts de tirs en état d'alerte contre les unités ennemies adjacentes à une autre unité alliée."/>
	<entry name="Tau/PointDefenceTargetingRelayFlavor" value="Conçu pour fournir une tir de couverture supérieur pour les guerriers de la Caste du Feu proches, un relais de défense ponctuelle vise et engage automatiquement les ennemis qui tentent d'attaquer."/>
	<entry name="Tau/PulseAccelerator" value="Accélérateur d'impulsions"/>
	<entry name="Tau/PulseAcceleratorDescription" value="Augmente la portée des armes à impulsion."/>
	<entry name="Tau/PulseAcceleratorFlavor" value="<string name='Actions/Tau/PulseAcceleratorFlavor'/>"/>
	<entry name="Tau/PulseBlaster" value="Blaster à impulsion"/>
 	<entry name="Tau/PulseBlasterDescription" value="Augmente les dégâts et la pénétration d'armure des attaques de surveillance."/>
 	<entry name="Tau/PulseBlasterFlavor" value="Bien que les T'au redoutent à juste titre les combats à courte distance, la nécessité de combattre les carcasses spatiales ou les mondes-ruches impériaux labyrinthiques a conduit à la mise au point du blaster à impulsion, plus communément appelé fusil de chasse à impulsion. Le blaster a la particularité de peindre sa cible avec des particules chargées négativement quelques instants avant de tirer, afin d'augmenter l'effet de la charge de plasma."/>
	<entry name="Tau/Rinyon" value="<string name='Actions/Tau/Rinyon'/>"/>
	<entry name="Tau/RinyonDescription" value="Augmente la précision contre les unités adjacentes à d'autres unités alliées."/>
	<entry name="Tau/RinyonFlavor" value="<string name='Actions/Tau/RinyonFlavor'/>"/>
	<entry name="Tau/RiptideShieldGenerator" value="Générateur de bouclier Riptide"/>
	<entry name="Tau/RiptideShieldGeneratorDescription" value="Augmente la réduction des dégâts."/>
	<entry name="Tau/RiptideShieldGeneratorFlavor" value="A l'intérieur du bouclier ablatif de l'exo-armure Riptide se trouve un petit générateur de champ d'énergie dont la puissance peut être augmenté en détournant de l'énergie du réacteur Nova de la XV104."/>
	<entry name="Tau/Ripyka" value="<string name='Actions/Tau/Ripyka'/>"/>
	<entry name="Tau/RipykaDescription" value="<string name='Actions/Tau/RipykaDescription'/>"/>
	<entry name="Tau/RipykaFlavor" value="<string name='Actions/Tau/RipykaFlavor'/>"/>
	<entry name="Tau/RipykaVa" value="Ripyka'va"/>
	<entry name="Tau/RipykaVaDescription" value="<string name='Actions/Tau/RipykaDescription'/>"/>
	<entry name="Tau/RipykaVaFlavor" value="La commandeur Coldflame était réputée pour sa subtilité, sa capacité à agencer de multiples stratégies en simultanés inspirant ses troupes à s'efforcer d'arriver au même niveau de complexité stratégique. Certains murmuraient, cependant, qu'elle s'inquiétait trop du Bien Suprême pour une simple Guerrière du Feu…"/>
	<entry name="Tau/SenseOfStone" value="<string name='Actions/Tau/SenseOfStone'/>"/>
	<entry name="Tau/SenseOfStoneDescription" value="Augmente la réduction des dégâts."/>
	<entry name="Tau/SenseOfStoneFlavor" value="<string name='Actions/Tau/SenseOfStoneFlavor'/>"/>
	<entry name="Tau/ShieldGenerator" value="Générateur de bouclier"/>
	<entry name="Tau/ShieldGeneratorDescription" value="Augmente la réduction de dégâts."/>
	<entry name="Tau/ShieldGeneratorFlavor" value="<string name='Actions/Tau/ShieldGeneratorFlavor'/>"/>
	<entry name="Tau/StabilisingAnchors" value="Ancrage au sol"/>
	<entry name="Tau/StabilisingAnchorsDescription" value="Augmente les attaques à distance si l'unité ne s'est pas déplacée ce tour-ci."/>
	<entry name="Tau/StabilisingAnchorsFlavor" value="Les combinaisons balistiques KV128 Stormsurge sont trop grandes pour être équipées d'un jetpack comme leurs homologues combinaisons de bataille. Le scientifique Bork'an, de la caste terrienne, les a conçues pour qu'elles se verrouillent une fois déployées, ce qui leur permet de consacrer toute l'énergie de leurs réacteurs à leur immense armement."/>
	<entry name="Tau/StimulantInjector" value="Injecteur de stimulants"/>
	<entry name="Tau/StimulantInjectorDescription" value="Augmente la réduction des dégâts."/>
	<entry name="Tau/StimulantInjectorFlavor" value="<string name='Actions/Tau/StimulantInjectorFlavor'/>"/>
	<entry name="Tau/StormOfFire" value="<string name='Actions/Tau/StormOfFire'/>"/>
	<entry name="Tau/StormOfFireDescription" value="Augmente les attaques."/>
	<entry name="Tau/StormOfFireFlavor" value="<string name='Actions/Tau/StormOfFireFlavor'/>"/>
	<entry name="Tau/SubvertCity" value="<string name='Actions/Tau/SubvertCity'/>"/>
	<entry name="Tau/SubvertCityDescription" value="Réduit la loyauté."/>
	<entry name="Tau/SubvertCityFlavor" value="<string name='Actions/Tau/SubvertCityFlavor'/>"/>
	<entry name="Tau/SupportSystems" value="Systèmes de soutien"/>
	<entry name="Tau/SupportSystemsDescription" value="Permet l'installation de systèmes de soutien."/>
	<entry name="Tau/SupportSystemsFlavor" value="Les Exo-armures T'au sont conçues pour être modulaires, pour que la Caste de la Terre puisse les personnaliser facilement pour les adapter à différentes situations de combat. Chaque armure n'a qu'un nombre limité de points de fixation cependant, et réduire la puissance de feu d'une armure est toujours un choix difficile."/>
	<entry name="Tau/SupportingFire" value="Tir de soutien"/>
	<entry name="Tau/SupportingFireDescription" value="Augmente les dégâts des tirs en état d'alerte contre les ennemis adjacents à d'autres unités alliées."/>
	<entry name="Tau/SupportingFireFlavor" value="La doctrine de la Caste du Feu, comme écrite dans le Code du Feu, ordonne à tout guerrier de protéger ses camarades. En utilisant des zones de tir se chevauchant, les équipes se fournissent un soutien mutuel sur le champ de bataille."/>
	<entry name="Tau/TargetAcquired" value="Cible acquise"/>
	<entry name="Tau/TargetAcquiredDescription" value="Augmente la précision à distance des T'au contre l'unité et réduit la réduction de dégâts à distance. Disparaît après avoir été attaqué par un T'au."/>
	<entry name="Tau/TargetAcquiredFlavor" value="Aucune race ne se dédie autant au travail de groupe que les T'au, et le désignateur laser est un excellent exemple. C'est un laser de visée portable qui est relié au système d'information des T'au, permettant des tirs précis par les autres forces T'au."/>
	<entry name="Tau/TidewallShieldline" value="Réseau défensif Tidewall"/>
	<entry name="Tau/TidewallShieldlineDescription" value="Renvoie les dégâts à distance qui ne sont pas d'explosion, de souffle ou de décharge psy à l'attaquant."/>
	<entry name="Tau/TidewallShieldlineFlavor" value="La fortification la plus souvent observée chez les forces de l'Empire T'au est le réseau défensif Tidewall, un mur d'énergie derrière lequel l'infanterie peut rester à couvert. Alors que les tirs ennemies crépitent et se brisent, inoffensifs, sur le champ réfracteur du réseau, les guerriers du feu qu'il protège déchaînent une pluie de tirs à impulsion en retour. Pire encore pour l'agresseur qui tenterait de faire sortir les T'au du couvert, the mur de force peut rediriger l'énergie kinétique, renvoyant des tirs de laser et des obus anti-blindages vers les rangs ennemis."/>
	<entry name="Tau/TidewallShieldlineCity" value="<string name='Traits/Tau/TidewallShieldline'/>"/>
	<entry name="Tau/TidewallShieldlineCityDescription" value="<string name='Traits/Tau/TidewallShieldlineDescription'/>"/>
	<entry name="Tau/TidewallShieldlineCityFlavor" value="<string name='Traits/Tau/TidewallShieldlineFlavor'/>"/>
	<entry name="Tau/TidewallShieldlineOutpost" value="<string name='Traits/Tau/TidewallShieldline'/>"/>
	<entry name="Tau/TidewallShieldlineOutpostDescription" value="<string name='Traits/Tau/TidewallShieldlineDescription'/>"/>
	<entry name="Tau/TidewallShieldlineOutpostFlavor" value="<string name='Traits/Tau/TidewallShieldlineFlavor'/>"/>
	<entry name="Tau/Unity" value="<string name='Actions/Tau/Unity'/>"/>
	<entry name="Tau/UnityDescription" value="<string name='Actions/Tau/UnityDescription'/>"/>
	<entry name="Tau/UnityFlavor" value="<string name='Actions/Tau/UnityFlavor'/>"/>
	<entry name="Tau/UtopiaBonus" value="Dans la diversité, l'unité"/>
	<entry name="Tau/UtopiaBonusDescription" value="Augmente le bonus de loyauté pour bâtir de nouveaux bâtiments."/>
	<entry name="Tau/UtopiaBonusFlavor" value="La ville T'au idéale est un exemple pour toutes les autres, affichant une balance parfaites des différentes castes des T'au et leurs races auxiliaires et clientes, toutes vivants en harmonie."/>
	<entry name="Tau/VectoredRetroThrusters" value="Rétrofusée vectorielle"/>
	<entry name="Tau/VectoredRetroThrustersDescription" value="Augmente le mouvement et ignore la zone de contrôle ennemie."/>
	<entry name="Tau/VectoredRetroThrustersFlavor" value="<string name='Actions/Tau/VectoredRetroThrustersFlavor'/>"/>
	<entry name="Tau/VelocityTracker" value="Traqueur de vélocité"/>
	<entry name="Tau/VelocityTrackerDescription" value="Augmente la précision contre les volants."/>
	<entry name="Tau/VelocityTrackerFlavor" value="<string name='Actions/Tau/VelocityTrackerFlavor'/>"/>
	<entry name="Tau/VolleyFire" value="<string name='Actions/Tau/VolleyFire'/>"/>
	<entry name="Tau/VolleyFireDescription" value="Augmente les attaques à distance si l'unité n'a pas bougé."/>
	<entry name="Tau/VolleyFireFlavor" value="<string name='Actions/Tau/VolleyFireFlavor'/>"/>
	<entry name="Tau/ZephyrsGrace" value="<string name='Actions/Tau/ZephyrsGrace'/>"/>
	<entry name="Tau/ZephyrsGraceDescription" value="Augmente les points d'action."/>
	<entry name="Tau/ZephyrsGraceFlavor" value="<string name='Actions/Tau/ZephyrsGraceFlavor'/>"/>
	<entry name="TelekineDome" value="Dôme télékinétique"/>
	<entry name="TelekineDomeDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="TelekineDomeFlavor" value="<string name='Actions/TelekineDomeFlavor'/>"/>
	<entry name="TeleportHomer" value="Balise de téléportation"/>
	<entry name="TeleportHomerDescription" value="Le déploiement orbital ne consomme pas de points d'action lors du déploiement de Chapelains, des Terminators d'assaut et des Terminators adjacents à cette unité."/>
	<entry name="TeleportHomerFlavor" value="Les balises de téléportation émettent un puissant signal permettant aux croiseurs d'attaque de se verrouiller dessus avec leur équipement de téléportation. En faisant correspondre les coordonnées exactes de ce signal, le risque de manquer la cible voulue est grandement réduit."/>
	<entry name="Template" value="Souffle"/>
	<entry name="TemplateDescription" value="<string name='Traits/BeamDescription'/>"/>
	<entry name="TerminatorArmour" value="Armure Terminator"/>
	<entry name="TerminatorArmourDescription" value="Augmente la réduction des dégâts."/>
	<entry name="TerminatorArmourFlavor" value="L'armure Terminator est la meilleure protection que peut avoir un Space Marine. Il est même dit qu'une armure Terminator peut supporter les énergies titanesques au cœur d'un générateur à plasma et que c'était, en fait, le but originel de l'armure."/>
	<entry name="Tesla" value="Tesla"/>
	<entry name="TeslaDescription" value="Augmente les attaques."/>
	<entry name="TeslaFlavor" value="<string name='Weapons/TeslaFlavor'/>"/>
	<entry name="TeslaDamage" value="Décharge viridienne"/>
	<entry name="TeslaDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="TeslaDamageFlavor" value="Les éclairs vert lumineux déchargés par ces exotiques et anciennes armes nécrons crépitent de vie, se tordant et semblant griffer la cible qu'ils touchent, bougeant comme s'ils avaient une conscience."/>
	<entry name="TheFleshIsWeak" value="La chair est faible"/>
	<entry name="TheFleshIsWeakDescription" value="Augmente la réduction des dégâts."/>
	<entry name="TheFleshIsWeakFlavor" value="<string name='Actions/TheFleshIsWeakFlavor'/>"/>
	<entry name="TrainedSentinelPilots" value="Pilotes de sentinelles entraînés"/>
	<entry name="TrainedSentinelPilotsDescription" value="Augmente les dégâts."/>
	<entry name="TrainedSentinelPilotsFlavor" value="Les sentinelles sont utilisées dans une large variété de rôles à travers l'Imperium de l'humanité—de chariot élévateur utilisé pour charger les armes des vaisseaux spatiaux jusqu'à des scouts à toit ouvert. Certaines sentinelles sont équipés avec un blindage amélioré et des stabilisateurs pour porter un armement plus lourd. Cela fait passer le rôle de la sentinelle de soutien et reconnaissance à celui de plate-forme de tir lourde."/>
	<entry name="Traktor" value="Trakteur"/>
	<entry name="TraktorDescription" value="Immobilise les volants ennemis."/>
	<entry name="Transport" value="Transport"/>
	<entry name="TransportDescription" value="Permet de transporter des unités d'infanterie et des créatures monstrueuses."/>
 	<entry name="TransportFlavor" value="Certains peuvent transporter d'autres personnes sur le champ de bataille, leur apportant vitesse et protection. Bien entendu, si le transport est détruit, les passagers seront brûlés vifs dans l'explosion."/>
	<entry name="TurboBoost" value="Turbo-Boost"/>
	<entry name="TurboBoostDescription" value="Augmente le mouvement."/>
	<entry name="Tusked" value="Défenses"/>
	<entry name="TuskedDescription" value="Augmente l'attaque des armes de corps à corps."/>
	<entry name="Tyranids/AcidBlood" value="Sang acide"/>
	<entry name="Tyranids/AcidBloodDescription" value="Les ennemis subissent des dégâts lorsqu'ils infligent des dégâts au corps à corps."/>
	<entry name="Tyranids/AcidBloodFlavor" value="Le sang étrange versé par certains Tyranides est si corrosif qu'il peut dévorer une armure en céramite et dissoudre la chair en quelques instants."/>
	<entry name="Tyranids/AdaptiveBiology" value="<string name='Actions/Tyranids/AdaptiveBiology'/>"/>
	<entry name="Tyranids/AdaptiveBiologyDescription" value="Augmente la réduction des dégâts."/>
	<entry name="Tyranids/AdaptiveBiologyFlavor" value="<string name='Actions/Tyranids/AdaptiveBiologyFlavor'/>"/>
	<entry name="Tyranids/AlphaWarrior" value="<string name='Actions/Tyranids/AlphaWarrior'/>"/>
	<entry name="Tyranids/AlphaWarriorDescription" value="Augmente la précision."/>
	<entry name="Tyranids/AlphaWarriorFlavor" value="<string name='Actions/Tyranids/AlphaWarriorFlavor'/>"/>
	<entry name="Tyranids/Biomorph" value="Biomorphe"/>
	<entry name="Tyranids/BiomorphDescription" value="Classification."/>
	<entry name="Tyranids/BiomorphDamage" value="Adaptation des biomorphes"/>
	<entry name="Tyranids/BiomorphDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Tyranids/BiomorphDamageFlavor" value="Loin d'être né parfaitement prêt pour n'importe quelle situation, les Tyranides ont un quantité variable d'adaptabilité dans leur ADN. Augmenter cette capacité est coûteux en termes de ressources mais permet de changer leurs comportement et capacités physiques au combat."/>
	<entry name="Tyranids/BoundingLeap" value="Bondissant"/>
	<entry name="Tyranids/BoundingLeapDescription" value="Annule la pénalité de mouvement des rivières."/>
	<entry name="Tyranids/BroodProgenitor" value="<string name='Actions/Tyranids/BroodProgenitor'/>"/>
	<entry name="Tyranids/BroodProgenitorDescription" value="Augmente les attaques."/>
	<entry name="Tyranids/BroodProgenitorFlavor" value="<string name='Actions/Tyranids/BroodProgenitorFlavor'/>"/>
	<entry name="Tyranids/ChameleonicSkin" value="Peau caméléon"/>
	<entry name="Tyranids/ChameleonicSkinDescription" value="Permet les attaques en état d'alerte avec les armes de corps à corps."/>
	<entry name="Tyranids/CityDamage" value="Le salon de l'araignée"/>
	<entry name="Tyranids/CityDamageDescription" value="Inflige des dégâts tous les tours."/>
	<entry name="Tyranids/CityDamageFlavor" value="Entrer dans une ville Tyranide mature revient à marcher dans des géants—des géants hostiles et fabriqués biologiquement, déterminés à vous consommer. Des villosités couvertes de toxines fouettent l'infanterie, des dents poussent dans des vallées qui se ferment comme une mâchoire, et des sphincters de la taille d'un centre-ville s'ouvrent soudainement, faisant tomber des armées inattentives entières dans ses bassins gastriques acides."/>
	<entry name="Tyranids/CityGrowth" value="Expansion agressive"/>
	<entry name="Tyranids/CityGrowthDescription" value="Augmente le rythme de croissance de la population."/>
	<entry name="Tyranids/CityGrowthFlavor" value="Les villes Tyranides ne valorisent pas la taille mais la productivité. Tant que la ville produit efficacement des troupes, peu importe qu'elle occupe de l'espace. Mais quand une ville doit s'étendre, elle peut le faire extrêmement vite — des tendons crochus sortent des sphincters, traînant des graines à l'endroit voulu où elles éructent telle des étrangleurs. En quelques instants, le terrain est occupé, donnant rapidement naissance à une nouvelle structure."/>
	<entry name="Tyranids/CityLoyalty" value="Dispersion de matière grise"/>
	<entry name="Tyranids/CityLoyaltyDescription" value="Diminue la pénalité de loyauté due à la quantité de ville."/>
	<entry name="Tyranids/CityLoyaltyFlavor" value="À travers la ruche, depuis les grands organismes producteurs jusqu'aux petites créatures de maintenance, jusqu'au substrat lui-même, de petits paquets de matière grise sont dispersés, augmentant la facilité avec laquelle les créatures de la ruche peuvent être contrôlées."/>
	<entry name="Tyranids/CityPopulationLimit" value="Bio-génèse d'organites"/>
	<entry name="Tyranids/CityPopulationLimitDescription" value="Augmente la limite de population."/>
	<entry name="Tyranids/CityPopulationLimitFlavor" value="Rien ne “vit” vraiment dans une ville-ruche Tyranide. Des créatures 'organites' sans intelligence naissent et exécutent des fonctions cruciales avant d'être ré-assimilées. Des altérations sur la structure de la ville peuvent être faites, permettant à ces organismes d'être créés et réabsorbés bien plus vite, permettant à leur population totale d'augmenter."/>
	<entry name="Tyranids/CityProduction" value="Parturition propulsive"/>
	<entry name="Tyranids/CityProductionDescription" value="Augmente la production fournie."/>
	<entry name="Tyranids/CityProductionFlavor" value="Attendre que les nouveaux-nés Tyranides quittent leurs bâtiments-mères respectives est relativement inefficace. Les Genetors ont vu des bâtiments adaptés, cependant, qui éjectent la créature nouvelle-née de son sac de naissance avec une impulsion électrique convulsive, lui permettant d'être ré-éutilisé plus rapidement."/>
	<entry name="Tyranids/CityTier2" value="Dispersion des voraces"/>
	<entry name="Tyranids/CityTier2Description" value="Augmente le rayon d'acquisition des cases."/>
	<entry name="Tyranids/CityTier2Flavor" value="Lorsque la ruche arrive à maturité, les voraces n'ont plus besoin de rester ensemble en nombre important pour leur sécurité. Les disperser permet à l'Esprit de la Ruche de coordonner leurs comportement de récupération sur une plus grande zone."/>
	<entry name="Tyranids/DiffusionField" value="<string name='Actions/Tyranids/DiffusionField'/>"/>
 	<entry name="Tyranids/DiffusionFieldDescription" value="Augmente la réduction des dégâts à distance."/>
 	<entry name="Tyranids/DiffusionFieldFlavor" value="<string name='Actions/Tyranids/DiffusionFieldFlavor'/>"/>
	<entry name="Tyranids/Dominion" value="<string name='Actions/Tyranids/Dominion'/>"/>
	<entry name="Tyranids/DominionDescription" value="<string name='Actions/Tyranids/DominionDescription'/>"/>
	<entry name="Tyranids/DominionFlavor" value="<string name='Actions/Tyranids/DominionFlavor'/>"/>
	<entry name="Tyranids/ExploitWeaknesses" value="<string name='Actions/Tyranids/ExploitWeaknesses'/>"/>
	<entry name="Tyranids/ExploitWeaknessesDescription" value="Réduit l'armure."/>
	<entry name="Tyranids/ExploitWeaknessesFlavor" value="<string name='Actions/Tyranids/ExploitWeaknessesFlavor'/>"/>
	<entry name="Tyranids/FeederBeast" value="Faim dévorante"/>
	<entry name="Tyranids/FeederBeastDescription" value="Les dégâts causés par cette unité sont convertis en soin."/>
	<entry name="Tyranids/InstinctiveFire" value="Le feu instinctif"/>
 	<entry name="Tyranids/InstinctiveFireDescription" value="Ne peut cibler que l'unité ennemie la plus proche."/>
 	<entry name="Tyranids/InstinctiveFireFlavor" value="Le Tyrannocyte est cultivé par la Flotte Ruche comme un mécanisme de livraison, pour débarquer ses troupes clés depuis l'espace et les rapprocher de la biomasse de l'ennemi. Cependant, une fois son objectif initial atteint, le travail de cette créature quasi insignifiante n'est pas terminé. En flottant, elle continue de faire pleuvoir un enfer organique et acide et de projeter ses tentacules sur tout ce qui se trouve à proximité."/>
	<entry name="Tyranids/GraspingTail" value="<string name='Actions/Tyranids/GraspingTail'/>"/>
	<entry name="Tyranids/GraspingTailDescription" value="Réduit les attaques."/>
	<entry name="Tyranids/GraspingTailFlavor" value="<string name='Actions/Tyranids/GraspingTailFlavor'/>"/>
	<entry name="Tyranids/HiveCommander" value="<string name='Actions/Tyranids/HiveCommander'/>"/>
	<entry name="Tyranids/HiveCommanderDescription" value="Augment les dégâts et la réduction des dégâts."/>
	<entry name="Tyranids/HiveCommanderFlavor" value="<string name='Actions/Tyranids/HiveCommanderFlavor'/>"/>
	<entry name="Tyranids/IndescribableHorror" value="<string name='Actions/Tyranids/IndescribableHorror'/>"/>
	<entry name="Tyranids/IndescribableHorrorDescription" value="Réduit le morale tous les tours."/>
	<entry name="Tyranids/IndescribableHorrorFlavor" value="<string name='Actions/Tyranids/IndescribableHorrorFlavor'/>"/>
	<entry name="Tyranids/InfantryUpkeep" value="Instincts des gaunts"/>
	<entry name="Tyranids/InfantryUpkeepDescription" value="Réduit le coût d'entretien en biomasse."/>
	<entry name="Tyranids/InfantryUpkeepFlavor" value="L'Esprit de la Ruche maintient habituellement un contrôle ferme sur son essaim, dirigeant tout à part les actions inconscientes—mais en relâchant ce contrôle, les plus petits organismes peuvent avoir suffisamment d'autonomie pour trouver de quoi se nourrir eux-mêmes."/>
	<entry name="Tyranids/InstinctiveBehaviour" value="Comportement instinctif"/>
	<entry name="Tyranids/InstinctiveBehaviourDescription" value="<string name='Traits/Tyranids/InstinctiveBehaviourLurkDescription'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourLurkFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourFeed" value="Comportement instinctif (Nutrition)"/>
	<entry name="Tyranids/InstinctiveBehaviourFeedDescription" value="L'unité perd des points de vie tous les tours quand elle n'est pas à portée d'une créature synaptique."/>
	<entry name="Tyranids/InstinctiveBehaviourFeedFlavor" value="S'ils ne sont pas contrôlés ou coordonnées par la puissante volonté de l'Esprit de la Ruche, de nombreux organismes Tyranides reviendront à leurs instincts de base."/>
	<entry name="Tyranids/InstinctiveBehaviourHunt" value="Comportement instinctif (Prédation)"/>
	<entry name="Tyranids/InstinctiveBehaviourHuntDescription" value="L'unité perd du mouvement tous les tours quand elle n'est pas à portée d'une créature synaptique."/>
	<entry name="Tyranids/InstinctiveBehaviourHuntFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourLurk" value="Comportement instinctif (Préservation)"/>
	<entry name="Tyranids/InstinctiveBehaviourLurkDescription" value="L'unité perd du moral tous les tours quand elle n'est pas à portée d'une créature synaptique."/>
	<entry name="Tyranids/InstinctiveBehaviourLurkFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/InstinctiveBehaviourOverride" value="Ignorer le comportement instinctif"/>
	<entry name="Tyranids/InstinctiveBehaviourOverrideDescription" value="Le comportement instinctif de l'unité est ignoré."/>
	<entry name="Tyranids/InstinctiveBehaviourOverrideFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/LivingBatteringRam" value="<string name='Actions/Tyranids/LivingBatteringRam'/>"/>
	<entry name="Tyranids/LivingBatteringRamDescription" value="Augmente les dégâts."/>
	<entry name="Tyranids/LivingBatteringRamFlavor" value="<string name='Actions/Tyranids/LivingBatteringRamFlavor'/>"/>
	<entry name="Tyranids/LongRangedDamage" value="Prolifération oculaire."/>
	<entry name="Tyranids/LongRangedDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Tyranids/LongRangedDamageFlavor" value="Viser des ennemis à distance peut être difficile avec une simple vision binoculaire. Une adaptation sensée est d'augmenter la variété et le nombre des senseurs oculaires disponibles pour l'organisme pour améliorer son ciblage de tous les types d'ennemis."/>
	<entry name="Tyranids/MassIncubation" value="<string name='Actions/Tyranids/MassIncubation'/>"/>
	<entry name="Tyranids/MassIncubationDescription" value="<string name='Actions/Tyranids/MassIncubationDescription'/>"/>
	<entry name="Tyranids/MassIncubationFlavor" value="<string name='Actions/Tyranids/MassIncubationFlavor'/>"/>
	<entry name="Tyranids/MeleeDamage" value="Symbiose avec les bio-armes"/>
	<entry name="Tyranids/MeleeDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Tyranids/MeleeDamageFlavor" value="Quand les Tyranides furent rencontrés pour la première fois, particulièrement leurs précurseurs les Zoats, leur armement était typiquement des organismes séparés. Avec le temps, les Tyranides et leurs armes ont fusionné."/>
	<entry name="Tyranids/Onslaught" value="<string name='Actions/Tyranids/Onslaught'/>"/>
	<entry name="Tyranids/OnslaughtDescription" value="Augmente le mouvement."/>
	<entry name="Tyranids/OnslaughtFlavor" value="<string name='Actions/Tyranids/OnslaughtFlavor'/>"/>
	<entry name="Tyranids/Paroxysm" value="<string name='Actions/Tyranids/Paroxysm'/>"/>
	<entry name="Tyranids/ParoxysmDescription" value="Réduit la précision."/>
	<entry name="Tyranids/ParoxysmFlavor" value="<string name='Actions/Tyranids/ParoxysmFlavor'/>"/>
	<entry name="Tyranids/PreyAdaptation" value="Adaptation à la proie"/>
	<entry name="Tyranids/PreyAdaptationDescription" value="L'Esprit de la ruche gagne de la recherche quand le Malanthrope gagne de l'expérience au combat."/>
	<entry name="Tyranids/PreyAdaptationFlavor" value="Le malanthrope ne fait pas que rassembler du nouveau matériel génétique des cadavres—il est aussi tout à fait capable d'observer les comportements acquis par l'enseignement d'une créature à un autre, que ce soit un parent ou un professeur, et de les retenir pour les encoder dans les créatures de l'Esprit de la Ruche."/>
	<entry name="Tyranids/ProductionBuildingUpkeep" value="Canaux de naissance"/>
	<entry name="Tyranids/ProductionBuildingUpkeepDescription" value="Réduit le coût d'entretien en influence."/>
	<entry name="Tyranids/ProductionBuildingUpkeepFlavor" value="Une simple changement dans la structure des grands organises qui composent la ruche Tyranide, les canaux de naissance sont un peu comme des lines de production biologiques, permettant à de nombreuses créatures d'être produites en parallèle sans la surveillance directe de l'Esprit de la Ruche."/>
	<entry name="Tyranids/PsychicBarrier" value="Barrière psychique"/>
 	<entry name="Tyranids/PsychicBarrierDescription" value="Augmente la réduction des dégâts d'invulnérabilité."/>
 	<entry name="Tyranids/PsychicBarrierFlavor" value="Les horreurs tyranides de la taille du Maleceptor ne sont pas dépourvues de défenses inhérentes. Au-delà de sa masse et de son armure résistante, le Malcepteur projette également une formidable barrière psychique, capable de vaporiser ou de dévier les balles et les éclairs d'énergie qui lui sont adressés avant qu'ils ne l'atteignent."/>
 	<entry name="Tyranids/PsychicOverload" value="<string name='Actions/Tyranids/PsychicOverload'/>"/>
 	<entry name="Tyranids/PsychicOverloadDescription" value="Frappe avec une plus grande précision."/>
 	<entry name="Tyranids/PsychicOverloadFlavor" value="Les pseudopodes éthérés projetés par le tissu encéphalique d'un Maleceptor sont des manifestations de l'Ombre dans le Warp lui-même, la présence psychique annulatrice de l'Esprit de la Ruche. Si ces pseudopodes venaient à effleurer la conscience d'un adversaire affaibli, celui-ci ferait l'expérience directe de l'immensité terrifiante de cette immanence et mourrait rapidement, horriblement et de manière explosive."/>
	<entry name="Tyranids/RakingStrike" value="Frappe éventreuse"/>
	<entry name="Tyranids/RakingStrikeDescription" value="Augmente les attaques au corps à corps du Virago des ruches. Augmente encore plus les attaques au corps à corps contre les volants."/>
	<entry name="Tyranids/RakingStrikeFlavor" value="Allonger et renforcer les éperons aiguisés sous le ventre du Virago lui permet d'exécuter des attaques en survol meurtrières. De plus, les bouts des ailes aiguisés permettent au Virago de viser précisément les pilotes ennemis."/>
	<entry name="Tyranids/RapaciousHunger" value="<string name='Actions/Tyranids/RapaciousHunger'/>"/>
	<entry name="Tyranids/RapaciousHungerDescription" value="Augmente les attaques."/>
	<entry name="Tyranids/RapaciousHungerFlavor" value="<string name='Actions/Tyranids/RapaciousHungerFlavor'/>"/>
	<entry name="Tyranids/Reclamation2" value="Archevores synaptiques"/>
	<entry name="Tyranids/Reclamation2Description" value="Réduit le coût en influence."/>
	<entry name="Tyranids/Reclamation2Flavor" value="Les fosses gastriques peuvent sembler le plus simple des organismes Tyranides—des trous remplis de matière digestive bouillonante pour décomposer la biomasse—mais étant donnée leur fonction cruciale au cycle de vie des Tyranids, leur conception a sans nul doute été raffinée pendant des générations. Cette adaptation particulière place des synapses de contrôle à chaque coin de la fosse pour gérer la digestion avec moins de surveillance de la part de l'Esprit de la Ruche."/>
	<entry name="Tyranids/Reclamation3" value="Accélérateurs à assimilation"/>
	<entry name="Tyranids/Reclamation3Description" value="Supprime le temps de rechargement."/>
	<entry name="Tyranids/Reclamation3Flavor" value="La récupération doit être un procédé efficace pour servir au mieux l'Esprit de la Ruche—la moindre biomasse qui doit être abandonnée pourrait signifier la défaite. Cette adaptation fait de la récupération un procédé continu, ce qui permet un haut pourcentage de récupération de la biomasse."/>
	<entry name="Tyranids/Regeneration" value="Régénération"/>
	<entry name="Tyranids/RegenerationDescription" value="Restaure des points de vie tous les tours."/>
	<entry name="Tyranids/RegenerationFlavor" value="Certains Tyranides ont la capacité de récupérer de blessures horribles qui auraient dû être fatales."/>
	<entry name="Tyranids/ResourceBuildingUpkeep" value="Charognards"/>
	<entry name="Tyranids/ResourceBuildingUpkeepDescription" value="Réduit le coût d'entretien en influence."/>
	<entry name="Tyranids/ResourceBuildingUpkeepFlavor" value="L'Esprit de la Ruche maintient habituellement un contrôle ferme sur sa nuée, dirigeant tout à part les actions inconscientes—mais en relâchant ce contrôle, les plus petits organismes peuvent avoir suffisamment d'autonomie pour trouver de quoi se nourrir eux-mêmes."/>
	<entry name="Tyranids/ScourgeOfTheBrood" value="<string name='Actions/Tyranids/ScourgeOfTheBrood'/>"/>
	<entry name="Tyranids/ScourgeOfTheBroodDescription" value="Augmente les dégâts."/>
	<entry name="Tyranids/ScourgeOfTheBroodFlavor" value="<string name='Actions/Tyranids/ScourgeOfTheBroodFlavor'/>"/>
	<entry name="Tyranids/ShadowInTheWarp" value="<string name='Actions/Tyranids/ShadowInTheWarp'/>"/>
	<entry name="Tyranids/ShadowInTheWarpDescription" value="Réduit le moral tous les tours."/>
	<entry name="Tyranids/ShadowInTheWarpFlavor" value="<string name='Actions/Tyranids/ShadowInTheWarpFlavor'/>"/>
	<entry name="Tyranids/ShortRangedDamage" value="Ruches de gigacorcheurs"/>
	<entry name="Tyranids/ShortRangedDamageDescription" value="Augmente la pénétration d'armure."/>
	<entry name="Tyranids/ShortRangedDamageFlavor" value="L'écorcheur est composé d'un nid de coléoptères, des insectoïdes acides qui entrent en frénésie et sont projetés de l'arme lorsque son porteur fait feu. Les ruches de giga-écorcheurs contiennent moins de coléoptères mais des plus gros, permettant simplement d'infliger plus de dégâts."/>
	<entry name="Tyranids/SingularPurpose" value="<string name='Actions/Tyranids/SingularPurpose'/>"/>
 	<entry name="Tyranids/SingularPurposeDescription" value="Les émissaires norns ont une précision et des dégâts accrus contre cette unité."/>
	<entry name="Tyranids/SingularPurposeFlavor" value="<string name='Actions/Tyranids/SingularPurposeFlavor'/>"/>
 	<entry name="Tyranids/SporeCloud" value="<string name='Actions/Tyranids/SporeCloud'/>"/>
	<entry name="Tyranids/SporeCloudDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="Tyranids/SporeCloudFlavor" value="<string name='Actions/Tyranids/SporeCloudFlavor'/>"/>
	<entry name="Tyranids/SymbioticTargeting" value="Ciblage symbiotique"/>
	<entry name="Tyranids/SymbioticTargetingDescription" value="Augmente la précision à distance si l'unité n'a pas bougé."/>
	<entry name="Tyranids/SynapseLink" value="Lien synaptique"/>
	<entry name="Tyranids/SynapseLinkDescription" value="L'unité ne peut ni être secouée ni brisée, elle est immunisée à la peur et n'est pas sujette au comportement instinctif."/>
	<entry name="Tyranids/SynapseLinkFlavor" value="<string name='Traits/Tyranids/InstinctiveBehaviourFeedFlavor'/>"/>
	<entry name="Tyranids/SynapticBacklash" value="Contrecoup synaptique"/>
	<entry name="Tyranids/SynapticBacklashDescription" value="Les Termagants aux environs subissent des dégâts lorsque le Tervigon meurt."/>
	<entry name="Tyranids/SynapticBacklashFlavor" value="Bien que les Tyranides n'aient aucune affection les uns pour les autres—ils observeront calmement leurs camarades de nichée mourir ou se diriger vers les fosses gastriques—le trauma psychique que le couvain d'un Tervigon souffre au décès de celui-ci est étrange, particulièrement étant donné l'apparente incapacité de l'Esprit de la Ruche à l'éliminer. Peut-être que c'est un héritage de la race décédée depuis longtemps dont le matériel génétique a été utilisé pour concevoir le Tervigon—et un symbole que l'Esprit de la Ruche innove rarement mais plagie plutôt."/>
	<entry name="Tyranids/ToxinSacs" value="Sacs à toxines"/>
	<entry name="Tyranids/ToxinSacsDescription" value="Augmente les dégâts des armes de corps à corps contre l'infanterie et les créatures monstrueuses."/>
	<entry name="Tyranids/ToxinSacsFlavor" value="Ces glandes parasitiques secrètent de vils fluides, couvrant les griffes, dents et serres des Tyranides avec une variété de poisons extra-terrestres létaux."/>
	<entry name="Tyranids/Tunnel2" value="Murs échiuriens"/>
	<entry name="Tyranids/Tunnel2Description" value="Augmente les points de vie."/>
	<entry name="Tyranids/Tunnel2Flavor" value="Les murs de la Ruche souterraine grandissent rapidement, ce qui signifie qu'ils ont tendance à être relativement fragiles, pour des Tyranides. Les Génétors ont observé des variantes qui croissent plus lentement mais qui accumulent de la masse musculaire, et qui sont généralement plus résistantes."/>
	<entry name="Tyranids/UnnaturalResilience" value="Résilience surnaturelle"/>
 	<entry name="Tyranids/UnnaturalResilienceDescription" value="Augmente la réduction des dégâts d'invulnérabilité et accorde une réduction supplémentaire des dégâts d'insensibilité à la douleur."/>
 	<entry name="Tyranids/UnnaturalResilienceFlavor" value="Imprégné de la volonté de la reine Norn, l'émissaire s'adapte instantanément aux blessures qui paralyseraient toute autre créature, défiant la mort pour atteindre son but."/>
	<entry name="Tyranids/VehiclesUpkeep" value="Instincts de Megaunts"/>
	<entry name="Tyranids/VehiclesUpkeepDescription" value="Réduit le coût d'entretien en biomasse."/>
	<entry name="Tyranids/VehiclesUpkeepFlavor" value="L'Esprit de la Ruche maintient habituellement un contrôle ferme sur son essaim, dirigeant tout à part les actions inconscientes, à part pour ses créatures alphas—mais en relâchant ce contrôle, les grands organismes peuvent diriger ou dévorer les plus petits organismes, leur permettant de survivre sans contrôle direct."/>
	<entry name="Tyranids/WarpField" value="Champ Warp"/>
	<entry name="Tyranids/WarpFieldDescription" value="Augmente la réduction des dégâts."/>
	<entry name="Tyranids/WarpFieldFlavor" value="Les Zoanthropes sont des éléments vitaux pour exploiter la puissance psychique de l'Esprit de la Ruche et sont créés avec un fort sens de l'auto-préservation. Du coup, ils projettent instinctivement un puissant champ Warp pour se protéger au combat – un bouclier mental qui est invisible à part un léger scintillement lorsque des tirs armes de petits comme de gros calibres crépitent dessus sans blesser le Zoanthrope."/>
	<entry name="TwinLinked" value="Jumelé"/>
	<entry name="TwinLinkedDescription" value="Augmente la précision."/>
	<entry name="TwinLinkedFlavor" value="Ces armes sont greffées sur le même système de visée pour une plus grande précision."/>
	<entry name="Uncommon" value="Peu commun"/>
	<entry name="UncommonDescription" value="Classification."/>
 	<entry name="Unique" value="Unique"/>
 	<entry name="UniqueDescription" value="Limite la quantité d'unités de ce type."/>
	<entry name="UnendingHorde" value="Horde sans fin"/>
	<entry name="UnendingHordeDescription" value="Augmente la réduction des dégâts."/>
 	<entry name="UnendingHordeFlavor" value="Comme les zombies des anciens romans terriens, les marcheurs de peste ne s'arrêtent pas tant qu'ils ne sont pas complètement détruits. Leur chair morte est insensible, de sorte que les attaques qui mettraient à terre un humain normal ne sont que de simples désagréments tandis qu'ils continuent de ricaner et de gémir…"/>
	<entry name="Unwieldy" value="Encombrant"/>
	<entry name="UnwieldyDescription" value="Réduit la précision."/>
	<entry name="UnwieldyFlavor" value="Cette arme est très grande et plus que maladroite, rendant des coups rapides impossible à exécuter."/>
	<entry name="VectoredAfterburners" value="Post-combustion vectorisée"/>
	<entry name="VectoredAfterburnersDescription" value="Augmente le mouvement et la réductions des dégâts à distance"/>
	<entry name="Vehicle" value="Véhicule"/>
	<entry name="VehicleDescription" value="Ignore la pénalité des armes lourdes et d'artillerie. Augmente la pénalité de mouvement en forêt et dans les ruines impériales."/>
	<entry name="VehicleFlavor" value="La guerre n'est pas une activité réservé aux soldats vivants, mais aussi aux puissants engins de guerres et chars."/>
	<entry name="VeryBulky" value="Très volumineux"/>
 	<entry name="VeryBulkyDescription" value="Nécessite deux emplacements de chargement supplémentaires dans un transport."/>	
 	<entry name="VeryBulkyFlavor" value="<string name='Traits/BulkyFlavor'/>"/>	
	<entry name="VoidShield" value="Bouclier Void"/>
	<entry name="VoidShieldDescription" value="Augmente la réduction de dégâts à distance."/>
	<entry name="VoidShieldFlavor" value="<string name='Actions/AstraMilitarum/ProjectedVoidShieldFlavor'/>"/>
	<entry name="VoxCaster" value="Émetteur vox"/>
	<entry name="VoxCasterDescription" value="Réduit les pertes de moral."/>
	<entry name="VoxCasterFlavor" value="Un émetteur vox est un moyen de communication fiable connecté au commandement tactique par des émetteurs à rayons courts."/>
	<entry name="Waaagh" value="Waaagh!"/>
	<entry name="WaaaghDescription" value="Augmente les attaques."/>
	<entry name="WaaaghFlavor" value="La Waaagh! est joie de vivre, croisade, immense pouvoir psychique, une aura de croyance tangible et peut-être les dieux Orks eux-mêmes, le tout mélangé dans un seul joyeux rugissement faisant claquer des dents et tourner les estomacs, faisant avancer les Orks dans la galaxie et au combat. C'est le coeur même de l'existence d'un Ork."/>
	<entry name="Walker" value="Marcheur"/>
	<entry name="WalkerDescription" value="<string name='Traits/DozerBladeDescription'/>"/>
	<entry name="Weakened" value="Affaibli"/>
	<entry name="WeakenedDescription" value="Réduit le mouvement et les dégâts."/>
	<entry name="WireWeed" value="<string name='Features/WireWeed'/>"/>
	<entry name="WireWeedDescription" value="Inflige des dégâts tous les tours."/>
	<entry name="WireWeedFlavor" value="<string name='Features/WireWeedFlavor'/>"/>
	<entry name="Witchfire" value="Décharge psy"/>
	<entry name="WitchfireDescription" value="Classification."/>
	<entry name="Zzap" value="Zzap"/>
	<entry name="ZzapFlavor" value="Les armes zzap tire des éclairs imprévisibles. Bien qu’elles puissent perforer la coque du plus lourd véhicule, elles tendent malheureusement à électrocuter leur opérateur en cas de surcharge."/>
	<entry name="Zealot" value="Zélot"/>
	<entry name="ZealotDescription" value="Réduit la perte de moral, augmente les dégâts au corps à corps et accorde l'immunité à la peur et au pilonnage."/>
	<entry name="ZealotFlavor" value="Les zélots se battent quelque soient les pertes ou les horreurs de la guerre; ils sont poussés par leur conviction."/>
</language>
